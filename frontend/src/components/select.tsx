import styled from "@emotion/styled";
import {Select, SelectProps} from "antd"
import React from "react";

interface SelectEmbeddeTitleProp extends SelectProps {
    _title: string;
    containerClassName?: string;
    left?: number; // 左偏移量: 用于对齐
}

export const SelectEmbeddeTitle = (props: SelectEmbeddeTitleProp) => {

    const titleRef = React.useRef<HTMLSpanElement>(null)

    const divRef = React.useRef<HTMLDivElement>(null)

    const id = Math.random().toString(36)

    return (
        <SelectedContainer className={props?.disabled ? "full-width-disabled" : props.containerClassName} style={props.style} ref={divRef} id={id}>
            <TitleContainer ref={titleRef}>{props._title}</TitleContainer>
            <StyledSelect
                {...props}
                titleWidth={titleRef.current?.offsetWidth}
                bordered={false}
                style={{ flex: 1, overflow: "hidden" }}
                dropdownMatchSelectWidth={divRef.current?.offsetWidth}
                getPopupContainer={() => document.getElementById(id) as HTMLElement}
            />
        </SelectedContainer>
    )
}

const SelectedContainer = styled.div`
    display: flex;
    align-items: center;
    height: 32px;
    border: 1px solid #E3E4E6;
    border-radius: 2px;
    cursor: default;
    .ant-select .ant-select-borderless > .ant-select-selector,.ant-select-selection-search{
        padding-left: 0px;
        left: 0px !important;
    }
    :hover {
        border: 1px solid #5f8bfa;
    }
    :focus-within {
        border: 1px solid #5f8bfa;
        box-shadow: 0 0 0 2px rgb(53 98 236 / 20%);
    }
`

const TitleContainer = styled.span`
    padding: 0 8px;
    color: #4E5969;
    white-space: nowrap;
`


interface CustomSelectProps extends SelectProps {
    titleWidth?: number;
    left?: number;
}

class IntermediateSelect<T extends CustomSelectProps> extends React.Component<T, {}> { }

class CustomSelect extends IntermediateSelect<CustomSelectProps> { 
    render() {
        return <Select {...this.props} />
    }
 }

const StyledSelect = styled(CustomSelect)`
    & + div .ant-select-dropdown {
        left: ${props => (props.left || props.left === 0) ? `${props.left}px` : `2px`} !important;
    }
`
