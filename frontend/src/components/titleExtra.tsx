import styled from "@emotion/styled";

interface TitleProp {
    name: string;
    icon?: React.ReactNode;
    extra?: React.ReactNode; 
}

export const TitleExtra = (props: TitleProp) => {
    return (<TitleContainer>
        {(!props.icon) && <TitleIcon/>}
        <TitleContent>{props.name}</TitleContent>
        <div style={{marginLeft:"4px"}}>
            {props.extra}
        </div>
    </TitleContainer>)
}

const TitleContainer = styled.div`
    display: flex;
    align-items: center;
`

export const TitleIcon = styled.div`
    background: #165DFF;
    height: 14px;
    width: 2px;
    margin-right: 8px;
    border-radius: 0px;
`

const TitleContent = styled.div`
    color: #1D2129;
    font-weight: 600;
    font-size: 14px;
`