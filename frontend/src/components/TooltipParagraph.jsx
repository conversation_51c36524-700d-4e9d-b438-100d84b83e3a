import styled from "@emotion/styled";
import {Tooltip, Typography} from "antd";
import {useState} from "react";

const { Paragraph } = Typography;

export const TooltipParagraph = ({
  tooltip = "",
  children,
  ellipsis,
  outStyle = {},
  ...props
}) => {
  const [truncated, setTruncated] = useState(false);

  return (
    <Tooltip title={truncated ? (tooltip || children) : undefined}>
      <CustomParagraph
        style={{ width: "inherit", ...outStyle }}
        {...props}
        ellipsis={{ ...ellipsis, onEllipsis: setTruncated }}
      >
        {children}
      </CustomParagraph>
    </Tooltip>
  );
};

const CustomParagraph = styled(Paragraph)`
  margin-bottom: 0 !important;
`;
