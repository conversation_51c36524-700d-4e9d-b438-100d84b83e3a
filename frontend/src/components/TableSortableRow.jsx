import {useSortable} from "@dnd-kit/sortable";
import {CSS} from "@dnd-kit/utilities";

export const TableSortableRow = ({ disabled, style, ...restProps }) => {
  const {
    attributes,
    listeners,
    isDragging,
    setNodeRef,
    transform,
    transition,
  } = useSortable({
    disabled,
    id: restProps["data-row-key"],
  });

  if (
    restProps.className === "ant-table-placeholder" ||
    restProps.className.includes("ant-table-expanded-row")
  ) {
    return <tr style={style} {...restProps} />;
  }

  style = {
    background: isDragging ? "#FAFAFA" : undefined,
    cursor: disabled ? undefined : "move",
    touchAction: "none",
    transform: CSS.Transform.toString(transform),
    transition,
    ...style,
  };

  return (
    <tr
      ref={setNodeRef}
      style={style}
      {...restProps}
      {...attributes}
      {...listeners}
    />
  );
};
