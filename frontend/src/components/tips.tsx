import {InfoCircleFilled} from "@ant-design/icons"
import styled from "@emotion/styled"
import {ReactNode} from "react"

interface InfoTipsProp {
    content: ReactNode;
    style?: React.CSSProperties;
}
export const InfoTips = (props: InfoTipsProp) => {
    return (
        <TipsContainer style={props.style}>
            <TipsBody>
                <InfoCircleFilled style={{ color: "#155BD4", marginRight: 8 }} />
                {props.content}
            </TipsBody>
        </TipsContainer>
    )
}

const TipsContainer = styled.div`
    background: rgba(22, 93, 255, 0.1);
    border: 0.5px solid #165DFF;
    border-radius: 2px;
`
const TipsBody = styled.div`
    padding: 6px 8px;
    word-break: break-all;
    line-height: 20px;
`