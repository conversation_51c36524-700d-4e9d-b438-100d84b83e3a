
// table空数据的列填充默认值-
// 只针对字符串类型和数组类型的字段，其余字段不管
export const fillTableCellEmptyPlaceholder = (data: any[]) => {
    const recursionFill = (obj: any) => {
        if (typeof obj !== "object") {
            throw ("target is not a object");
        }
        for (const k in obj) {
            // string 类型
            if (typeof obj[k] === "string" && obj[k] === "") {
                obj[k] = "-"
            }
            // object 类型
            if (typeof obj[k] === "object") {
                obj[k] = recursionFill(obj[k])
            }
        }
        return obj
    };

    if (data != null){
        data.forEach(item => {
            recursionFill(item)
        })
    }
    return data
}

