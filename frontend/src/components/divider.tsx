import {Divider} from "antd";
import {ReactNode} from "react";

export const InsertDivider = (btns: ReactNode[]) => {
    const split = (<Divider type="vertical" style={{border: "0.5px solid #f0f0f0"}}/>)
    const results: any[] = [];
    btns.forEach((it: any, index) => {
        results.push(it);
        if (btns.length > 1 && index !== btns.length - 1) {
            results.push(split);
        }
    })
    if (btns.length === 0) {
        return <>-</>
    }
    return (<>{results.map((it, index) => (<span key={index}>{it}</span>))}</>)
}