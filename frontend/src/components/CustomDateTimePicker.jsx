/** @jsxImportSource @emotion/react */
import { CalendarOutlined } from "@ant-design/icons";
import { Button, DatePicker, Row, Select, TimePicker } from "antd";
import { useEffect, useRef, useState } from "react";
import moment from "moment";
import { useTranslation } from "../pages/common/multilingual/component";

export const CustomDateTimePicker = ({ value, onChange, disabledDate, disabledTime, ph }) => {
  const intl = useTranslation();
  const { formatMessage } = intl;
  const [open, setOpen] = useState(false);
  const [date, setDate] = useState(moment());
  const [time, setTime] = useState(undefined);
  const [placeholder, setPlaceholder] = useState("placeholder.select.common");
  const selectKey = useRef(0); // 用于强制重新渲染 Select 组件
  const [inputValue, setInputValue] = useState("");

  useEffect(() => {
    let [date, time] = !!value ? value.split(" ") : "";
    setTime(!!time ? moment(value) : undefined);
    setDate(!!date ? moment(date) : moment());
    setPlaceholder(ph ? ph : "placeholder.select.common");
  }, [value]);

  const handleClear = () => {
    setInputValue("");
    onChange(undefined);
    setOpen(false);
  };

  const range = (start, end) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };


  const disabledDateTime = () => {
    const currentDate = moment(date).startOf("day");
    // 获取今天的日期
    const today = moment().startOf("day");
    const todayHour = moment().hour();
    const todayMinute = moment().minutes();
    if (disabledTime === "disBefore") { //禁用当前时间之前
      if (currentDate && currentDate.isSame(today)) {
        return {
          disabledHours: () => range(0, todayHour),
          disabledMinutes: (selectedHour) => {
            if (selectedHour === todayHour) {
              return range(0, todayMinute);
            }
            return [];
          }
        };
      }
      return {};
    } else { //禁用未来时间
      return {
        disabledHours: () => {
          if (currentDate.isSame(today)) {
            return range(0, 24).splice(todayHour + 1, 20);
          }
        },
        disabledMinutes: (selectedHour) => {
          if (currentDate.isSame(today) && selectedHour === todayHour) {
            return range(todayMinute + 1, 60);
          }
        }
      };
    }
  };

  const handleSearch = (inputValueStr) => {
    if (inputValueStr === "") {
      setDate(moment());
      setTime(undefined);
      return;
    }
    const [inputDate, inputTime] = inputValueStr.split(' ');
    if (inputDate) {
      const dateMoment = moment(inputDate, 'YYYY-MM-DD', true);
      if (dateMoment.isValid()) {
        setDate(dateMoment);
        setInputValue(inputValueStr);
      } else {
        setInputValue("");
      }
    }
    if (inputTime) {
      const timeMoment = moment(inputTime, 'HH:mm:ss', true);
      if (timeMoment.isValid()) {
        setTime(timeMoment);
        setInputValue(inputValueStr);
      } else {
        setInputValue("");
      }
    }
  };

  const handleBlur = () => {
    if (inputValue) {
      onChange(
        !!time
          ? `${date.format("YYYY-MM-DD")} ${time.format("HH:mm:ss")}`
          : date.format("YYYY-MM-DD")
      );
    }
    setOpen(false);
    selectKey.current += 1;
  };

  return (
    <div style={{ position: "relative", overflow: "hidden" }}>
      <Select
        key={selectKey.current} // 使用 key 属性强制重新渲染
        allowClear
        showSearch
        open={false}
        placeholder={formatMessage({ id: placeholder })}
        suffixIcon={<CalendarOutlined />}
        style={{ flex: 1 }}
        value={value}
        onBlur={handleBlur}
        onClear={handleClear}
        onDropdownVisibleChange={setOpen}
        onSearch={handleSearch}
      />
      <DatePicker
        format="YYYY-MM-DD"
        open={open}
        value={date}
        onChange={setDate}
        showToday={false}
        disabledDate={disabledDate}
        style={{ position: "absolute", left: 0, zIndex: -1 }}
        // placement="top"
        panelRender={(panel) => (
          <div>
            {panel}
            <Row
              align="middle"
              style={{
                height: 41,
                borderBottom: "1px solid #f0f0f0",
                paddingLeft: 8,
              }}
            >
              <Button
                type="link"
                onClick={() => {
                  setDate(moment());
                  setTime(undefined);
                }}
              >
                {formatMessage({ id: "common.reset" })}
              </Button>
            </Row>
          </div>
        )}
        popupClassName="custom-date-picker"
      />
      <TimePicker
        open={open}
        value={time}
        onSelect={setTime}
        style={{ position: "absolute", left: 280, zIndex: -1 }}
        // placement="top"
        disabledTime={disabledTime !== null && disabledDateTime}
        panelRender={(panel) => (
          <div>
            <Row
              align="middle"
              justify="center"
              style={{
                height: 39,
                borderBottom: "1px solid #f0f0f0",
                borderLeft: "1px solid #f0f0f0",
              }}
            >
              {!!time
                ? `${date.format("YYYY-MM-DD")} ${time.format("HH:mm:ss")}`
                : date.format("YYYY-MM-DD")}
            </Row>
            {panel}
          </div>
        )}
        renderExtraFooter={() => (
          <Row justify="end" align="middle">
            <Button
              size="small"
              type="primary"
              onClick={() => {
                onChange(
                  !!time
                    ? `${date.format("YYYY-MM-DD")} ${time.format("HH:mm:ss")}`
                    : date.format("YYYY-MM-DD")
                );
                setOpen(false);
              }}
            >
              {formatMessage({ id: "common.ok" })}
            </Button>
          </Row>
        )}
        popupClassName="custom-time-picker"
      />
    </div>
  );
};
