/** @jsxImportSource @emotion/react */
import {
  CalendarOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { Select, Button, TimePicker } from "antd";
import { useState } from "react";
import { useIntl } from "react-intl";
import styled from "@emotion/styled";

export const CustomTimePicker = ({
  format = "HH:mm:ss",
  value,
  onChange,
  disabledDate,
}) => {
  const [open, setOpen] = useState(false);
  const intl = useIntl();
  const { formatMessage } = intl;
  let selectValue = !!value
    ? typeof value === "string"
      ? value === "currentTime"
        ? formatMessage({ id: "common.current.time" })
        : value
      : value.format(format)
    : undefined;

  const props = {
    format,
    open,
    value: typeof value === "string" ? null : value,
    onChange: (value) => {
      onChange(value);

      setOpen(false);
    },
    disabledDate,
    onOk: () => setOpen(false),
  };

  let child = (
    <TimePicker
      showToday={false}
      {...props}
      renderExtraFooter={() => (
        <div style={{ textAlign: "center" }}>
          <Button
            type="link"
            block={false}
            onClick={() => {
              onChange(formatMessage({ id: "common.current.time" }));
              setOpen(false);
            }}
          >
            {formatMessage({ id: "common.current.time" })}
          </Button>
        </div>
      )}
    />
  );

  return (
    <div style={{ position: "relative" }}>
      {child}
      <CustomSelect
        allowClear
        open={false}
        placeholder={formatMessage({ id: "placeholder.select.common" })}
        suffixIcon={
          format.includes("Y") ? (
            <CalendarOutlined
              style={{ fontSize: "14px" }}
              onClick={() => setOpen(true)}
            />
          ) : (
            <ClockCircleOutlined
              style={{ fontSize: "14px" }}
              onClick={() => setOpen(true)}
            />
          )
        }
        style={{ position: "absolute", left: 0, width: "100%" }}
        value={selectValue}
        onBlur={() => setOpen(false)}
        onClear={() => {
          onChange(undefined);
          setOpen(false);
        }}
        onDropdownVisibleChange={setOpen}
      />
    </div>
  );
};

const CustomSelect = styled(Select)`
  .ant-select-clear {
    font-size: 14px;
    height: 14px;
    width: 14px;
    margin-top: -7px;
  }
`;
