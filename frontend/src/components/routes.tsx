import {Outlet, Route, Routes, useLocation, useNavigate,} from "react-router-dom";
import {Login} from "pages/login";
import {NotFound} from "pages/not-found";
import {Forbidden} from "pages/forbidden";
import {RequireAuth, RequireProject, useAuth} from "context/auth";
import {verify} from "api/user";
import {useMount} from "ahooks";
import {message} from "antd";
import {Fetching} from "./fetching";
import {Main} from "pages/main";
import {Home} from "pages/main/home";
import {Index as Role} from "pages/main/settings/role";
import {Projects} from "pages/main/projects";
import {Index as Depots} from "pages/main/settings/depots";
import {Index as User} from "pages/main/settings/user";
import {Index as Project} from "pages/project/layout";
import {Home as ProjectHome} from "pages/project/home";
import {Index as Notice} from "pages/project/settings/notice";
import {Index as ProjectUser} from "pages/project/settings/user";
import {Index as Plan} from "pages/project/build/plan";
import {Index as Drug} from "pages/project/build/drug";
import {Index as Randomization} from "pages/project/build/randomization";
import {Index as Attribute} from "pages/project/build/attribute";
import {ProjectSite} from "pages/project/build/site";
import {Index as Subject} from "pages/project/sub/subject";
import {VisitCycle as ProjectVisitCycle} from "pages/project/sub/visit_cycle";
import {Index as Check} from "pages/project/check";
import {useFetch} from "hooks/request";
import {Shipment} from "pages/project/supply/shipment";
import {Storehouse} from "../pages/project/supply/storehouse_statistics";
import {SupplySite} from "../pages/project/supply/site_pharmacy";
import {DrugFreeze} from "../pages/project/supply/drug_freeze";
import {DrugRecovery} from "../pages/project/supply/drug_recovery";
import {Index} from "../pages/project/build/code_rule";
import {HistoryList} from "../pages/project/build/history_info";
import {PushList} from "../pages/project/build/push";
import {SimulateRandom} from "../pages/project/build/simulate_random";
import {StorehouseManage} from "../pages/project/build/storehouse";
import {DrugDTP} from "../pages/project/supply/drug_dtp";
import {RandomConfigure} from "../pages/project/settings/export/random-configure";
import {Index as Report} from "../pages/main/report";
import {MessageCenter} from "../pages/common/message-center";
import {MessageSingle} from "../pages/common/message-single";
import {WS} from "../pages/ws";
import {WSIrt} from "../pages/ws-irt";
import {Index as MultiLanguage} from "../pages/main/multiLanguage";
import {LocalesManage} from "../pages/common/multilingual/manage";
import {useEffect} from "react";

// https://stackblitz.com/github/remix-run/react-router/tree/main/examples/auth?file=src/App.tsx

export const AppRoutes = () => {
    const auth = useAuth();
    const location = useLocation();
    const navigate = useNavigate();

    const { run: run_verify, loading: fetching } = useFetch(verify, {
        manual: true,
        onSuccess: (result: any) => {
            if (result && result.code === 0) {
                if (sessionStorage.getItem("current_project")) {
                    auth.setProject(
                        JSON.parse(
                            sessionStorage.getItem("current_project") as string
                        )
                    );
                }
                if (sessionStorage.getItem("customerId")) {
                    auth.setCustomerId(
                        sessionStorage.getItem("customerId") as string
                    );
                }
                if (sessionStorage.getItem("cohort")) {
                    auth.setCohort(
                        JSON.parse(sessionStorage.getItem("cohort") as string)
                    );
                }
                if (sessionStorage.getItem("env")) {
                    auth.setEnv(
                        JSON.parse(sessionStorage.getItem("env") as string)
                    );
                }
                if (sessionStorage.getItem("cloud")) {
                    auth.setCloudUrl(sessionStorage.getItem("cloud"));
                }
                sessionStorage.setItem("token", result.data.token);
                auth.setUser(result.data.user);
            } else {
                message.error("Not logged in or login timeout!");
                sessionStorage.removeItem("token");
                navigate("/login", {
                    state: { from: location.pathname, search: location.search },
                });
            }
        },
        onError: (result: any) => {
            message.error("Not logged in or login timeout!");
            sessionStorage.removeItem("token");
            navigate("/login", {
                state: { from: location.pathname, search: location.search },
            });
        },
    });

    useMount(() => {
        const token = sessionStorage.getItem("token");
        if (token && !auth.user) {
            run_verify({ token, type: 2 });
        }
    });

    useEffect(() => {
        sessionStorage.removeItem('project_preview')
    }, [location])

    return (
        <>
            <Routes>
                {/*<Route path="/locales-manage" element={<LocalesManage />}/>*/}
                <Route element={fetching ? <Fetching /> : <Outlet />}>
                    <Route
                        path="/"
                        element={<RequireAuth children={<Main />} />}
                    >
                        <Route path="/" element={<Home />} />
                        <Route path="/projects" element={<Projects />} />
                        <Route path="/settings">
                            <Route path="/settings/roles" element={<Role />} />
                            <Route path="/settings/users" element={<User />} />
                            <Route
                                path="/settings/depots"
                                element={<Depots />}
                            />
                        </Route>
                        <Route path="/report" element={<Report />} />
                        <Route path="/multiLanguage" element={<MultiLanguage />} />
                        <Route
                            path="/message-center"
                            element={<MessageCenter />}
                        />
                        <Route
                            path="/message-single/:id"
                            element={<MessageSingle />}
                        />
                    </Route>
                    <Route
                        path="/check"
                        element={<RequireAuth children={<Check />} />}
                    />
                    <Route
                        path="/project"
                        element={
                            <RequireAuth
                                children={
                                    <RequireProject children={<Project />} />
                                }
                            />
                        }
                    >
                        <Route path="/project/home" element={<ProjectHome />} />
                        <Route path="/project/sub">
                            <Route path="/project/sub/subject" element={<Subject />} />
                            <Route path="/project/sub/visit-cycle" element={<ProjectVisitCycle />} />
                        </Route>
                        <Route path="/project/supply">
                            <Route
                                path="/project/supply/shipment"
                                element={<Shipment />}
                            />
                            <Route
                                path="/project/supply/storehouse"
                                element={<Storehouse />}
                            />
                            <Route
                                path="/project/supply/site"
                                element={<SupplySite />}
                            />
                            <Route
                                path="/project/supply/drug"
                                element={<DrugDTP />}
                            />
                            <Route
                                path="/project/supply/drug-freeze"
                                element={<DrugFreeze />}
                            />
                            <Route
                                path="/project/supply/drug-recovery"
                                element={<DrugRecovery />}
                            />
                        </Route>
                        <Route path="/project/build">
                            <Route
                                path="/project/build/code-rule"
                                element={<Index />}
                            />
                            <Route
                                path="/project/build/supply-plan"
                                element={<Plan />}
                            />
                            <Route
                                path="/project/build/drug"
                                element={<Drug />}
                            />
                            <Route
                                path="/project/build/storehouse"
                                element={<StorehouseManage />}
                            />
                            <Route
                                path="/project/build/randomization"
                                element={<Randomization />}
                            />
                            <Route
                                path="/project/build/attribute"
                                element={<Attribute />}
                            />
                            <Route
                                path="/project/build/site"
                                element={<ProjectSite />}
                            />
                            <Route
                                path="/project/build/history"
                                element={<HistoryList />}
                            />
                            <Route
                                path="/project/build/push-statistics"
                                element={<PushList />}
                            />
                            <Route
                                path="/project/build/simulate-random"
                                element={<SimulateRandom />}
                            />
                        </Route>
                        <Route path="/project/settings">
                            <Route
                                path="/project/settings/notice"
                                element={<Notice />}
                            />
                            <Route
                                path="/project/settings/user"
                                element={<ProjectUser />}
                            />
                            <Route
                                path="/project/settings/export"
                                element={<RandomConfigure />}
                            />
                        </Route>
                    </Route>
                </Route>
                <Route path="/login" element={<Login />} />
                <Route path="/forbidden" element={<Forbidden />} />
                <Route
                    path="*"
                    element={fetching ? <Fetching /> : <NotFound />}
                />
            </Routes>
            {!!sessionStorage.getItem("token") &&
                auth.cloudUrl &&
                auth.user?.id ? (
                    <WS />
                ) : null
            }
            {sessionStorage.getItem("token") && ((auth.customerId && auth.project?.id && auth.env?.id) || location.pathname==="/" || location.pathname==="/report") && auth.user?.id ? <WSIrt /> : null}
        </>
    );
};
