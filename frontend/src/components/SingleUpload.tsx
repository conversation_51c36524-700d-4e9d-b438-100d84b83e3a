import {CloseCircleFilled, UploadOutlined} from '@ant-design/icons'
import styled from '@emotion/styled';
import {useSafeState} from 'ahooks';
import {Button, message, Upload, UploadProps} from 'antd'
import {useImperativeHandle} from 'react';
import {useIntl} from 'react-intl';

interface SingleUploadProps extends UploadProps {
    bind: any
    tips: string | React.ReactNode
    width?: number
    height?: number
    clickStyle?: React.CSSProperties
    buttonStyle?: React.CSSProperties
    fileSize?: number
}

export const SingleUpload = (props: SingleUploadProps) => {

    const {formatMessage} = useIntl()

    const [fileList, setFileList] = useSafeState<any>([])

    useImperativeHandle(props.bind, () => ({
        fileList,
        originFile: fileList.length > 0 ? fileList[0].originFileObj : null,
        reset: () => {
            setFileList([])
        }
    }))

    const beforeUpload = (file: any) => {
        if (props.fileSize && props.fileSize > 0) {
            const isLimit = file.size / 1024 / 1024 < (props.fileSize || 50)
            if (!isLimit) {
                message.error(formatMessage({id:'common.upload.fileSize'}))
            }
        }
        return false
    }

    const onChange = (info: any) => {
        if (info.fileList && info.fileList.length > 0) {
            setFileList([info.fileList[0]])
        } else {
            setFileList([])
        }
    }

    return (
        <NoTopUpload {...props}
            fileList={fileList}
            beforeUpload={beforeUpload}
            onChange={onChange}
            showUploadList={{ removeIcon:<CloseCircleFilled />}}
        >
            {
                fileList.length === 0 && 
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            border: '1px solid #E3E4E6',
                            borderRadius: '2px',
                            width: props.width || 410,
                            height: props.height || 93,
                            ...props.clickStyle
                        }}
                    >
                        <div style={{marginBottom: 8, color: '#ADB2BA'}}>
                            <Button type='dashed'  icon={<UploadOutlined />}>
                                {formatMessage({id: 'common.upload.add'})}</Button>
                        </div>
                        {
                            typeof props.tips === 'string' ?
                                <div style={{fontSize: 12}} onClick={e => e.stopPropagation()}>
                                    {props.tips}
                                </div>
                                :
                                props.tips
                        }
                    </div>
            }
        </NoTopUpload>
    )
}

const NoTopUpload = styled(Upload)`
    .ant-upload-list-item {
        margin-top: 0;
    }
`
