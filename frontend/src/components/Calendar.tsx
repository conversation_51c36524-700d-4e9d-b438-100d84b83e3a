import {Dayjs} from 'dayjs';
import dayjsGenerateConfig from 'rc-picker/es/generate/dayjs';
import generateCalendar, { CalendarMode, HeaderRender } from 'antd/es/calendar/generateCalendar';
import "dayjs/locale/en";
import "dayjs/locale/zh-cn";
import { ReactNode } from 'react';
import 'antd/es/calendar/style';

const Calendar = generateCalendar<Dayjs>(dayjsGenerateConfig);

interface customCalendarProp {
    value?: Dayjs | undefined;
    onPanelChange: ((date: Dayjs, mode: CalendarMode) => void) | undefined;
    onSelect: ((date: Dayjs) => void) | undefined;
    headerRender?: HeaderRender<any>;
    dateCellRender?: ((date: Dayjs) => ReactNode) | undefined;
    dateFullCellRender?: ((date: Dayjs) => ReactNode) | undefined;
    monthCellRender?: ((date: Dayjs) => ReactNode) | undefined;
}

function customCalendar(props: customCalendarProp) {

  return (
    <div>
      <Calendar
        value={props.value}
        onPanelChange={props.onPanelChange}
        onSelect={props.onSelect} 
        headerRender={props.headerRender}
        dateCellRender={props.dateCellRender}
        dateFullCellRender={props.dateFullCellRender}
        monthCellRender={props.monthCellRender}
      />
    </div>
  );
}

export default customCalendar;