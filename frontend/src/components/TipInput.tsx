import {Input, InputNumber, InputNumberProps, InputProps, Tooltip} from "antd";
import {TooltipPropsWithTitle} from "antd/lib/tooltip";
import React from "react";

// @ts-ignore
interface TipInputProps extends TooltipPropsWithTitle, InputProps {
    tipClassName?: string;
    tipStyle?: React.CSSProperties;
    inputClassName?: string;
    inputStyle?: React.CSSProperties;
    ellipsis?: boolean;
}

export const TipInput = (props: TipInputProps) => {

    const [inputValue, setInputValue] = React.useState(props.value)

    return (
        <Tooltip
            title={props.title === true || !props.title || (typeof props.title === 'string' && !props.title.trim()) ? (inputValue || props.placeholder) : props.title}
            // title={(props.title || !props.title || (typeof props.title === 'string' && !props.title.trim())) ? (inputValue || props.placeholder) : props.title}
            align={props.align}
            arrowPointAtCenter={props.arrowPointAtCenter}
            autoAdjustOverflow={props.autoAdjustOverflow}
            color={props.color}
            defaultVisible={props.defaultVisible}
            destroyTooltipOnHide={props.destroyTooltipOnHide}
            getPopupContainer={props.getPopupContainer}
            mouseEnterDelay={props.mouseEnterDelay}
            mouseLeaveDelay={props.mouseLeaveDelay}
            overlayClassName={props.overlayClassName}
            overlayInnerStyle={props.overlayInnerStyle}
            overlayStyle={props.overlayStyle}
            placement={props.placement}
            trigger={props.trigger}
            zIndex={props.zIndex}
            visible={props.visible}
            onVisibleChange={props.onVisibleChange}
            overlay={props.overlay}
            className={props.tipClassName}
            style={props.tipStyle}
        >
            <Input
                placeholder={props.placeholder}
                addonAfter={props.addonAfter}
                addonBefore={props.addonBefore}
                allowClear={props.allowClear}
                bordered={props.bordered}
                defaultValue={props.defaultValue}
                disabled={props.disabled}
                id={props.id}
                maxLength={props.maxLength}
                showCount={props.showCount}
                status={props.status}
                prefix={props.prefix}
                size={props.size}
                suffix={props.suffix}
                type={props.type}
                value={props.value}
                onChange={(e) => {
                    setInputValue(e.target.value)
                    props.onChange && props.onChange(e)
                }}
                onPressEnter={props.onPressEnter}
                className={props.inputClassName}
                style={props.inputStyle ?
                    props.ellipsis ? {...props.inputStyle, textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap'} : props.inputStyle
                    : props.ellipsis ? {textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap'} : undefined
                }
            />
        </Tooltip>
    )
}

// @ts-ignore
interface TipInputNumberProps extends TooltipPropsWithTitle, InputNumberProps {
    tipClassName?: string
    tipStyle?: React.CSSProperties
    inputClassName?: string
    inputStyle?: React.CSSProperties
}

export const TipInputNumber = (props: TipInputNumberProps) => {

    const [inputValue, setInputValue] = React.useState(props.value)

    return (
        <Tooltip 
            title={props.title === true || !props.title || (typeof props.title === 'string' && !props.title.trim()) ? (inputValue || props.placeholder) : props.title}
            align={props.align}
            arrowPointAtCenter={props.arrowPointAtCenter}
            autoAdjustOverflow={props.autoAdjustOverflow}
            color={props.color}
            defaultVisible={props.defaultVisible}
            destroyTooltipOnHide={props.destroyTooltipOnHide}
            getPopupContainer={props.getPopupContainer}
            mouseEnterDelay={props.mouseEnterDelay}
            mouseLeaveDelay={props.mouseLeaveDelay}
            overlayClassName={props.overlayClassName}
            overlayInnerStyle={props.overlayInnerStyle}
            overlayStyle={props.overlayStyle}
            placement={props.placement}
            trigger={props.trigger}
            zIndex={props.zIndex}
            visible={props.visible}
            onVisibleChange={props.onVisibleChange}
            overlay={props.overlay}
            className={props.tipClassName}
            style={props.tipStyle}
        >
            <InputNumber
                placeholder={props.placeholder}
                addonAfter={props.addonAfter}
                addonBefore={props.addonBefore}
                bordered={props.bordered}
                defaultValue={props.defaultValue}
                disabled={props.disabled}
                id={props.id}
                min={props.min}
                precision={props.precision}
                maxLength={props.maxLength}
                status={props.status}
                prefix={props.prefix}
                size={props.size}
                type={props.type}
                value={props.value}
                onChange={(v) => {
                    setInputValue(v)
                    props.onChange && props.onChange(v)
                }}
                onPressEnter={props.onPressEnter}
                className={props.inputClassName}
                style={props.inputStyle}
            />
        </Tooltip>
    )
}
