import {Popover} from "antd"

interface TableCellPopoverProp {
    title: React.ReactNode;
    items: string[];
    // 超过多少条数据时才显示卡片气泡, 默认显示两条
    minTriggerNumber?: number;
    contentLineHeight?: string;
}

export const TableCellPopover = (props: TableCellPopoverProp) => {
    if (!props.items||props.items.length===0) {
        return <>-</>
    }
    const minTriggerNumber = props.minTriggerNumber ? props.minTriggerNumber : 3
    // 最多只显示三行
    const displayItems = props.items.slice(0, 3)
    if (props.items.length > minTriggerNumber) {
        displayItems.push("...")
    }
    return <Popover
        // https://github.com/ant-design/ant-design/issues/27102; 防止 popover 出现抖动效果
        transitionName=''
        trigger={["hover", "click"]}
        title={props.title}
        placement="topLeft"
        visible={props.items.length <= minTriggerNumber ? false : undefined}
        content={
            <div>
                {props.items.map((item, index) => (
                    // 最后一个元素的marginBottom不加
                    <div key={index} style={{ color: "#000000", marginBottom: index === props.items.length - 1 ? 0 : 16 }}>
                        {index + 1}.{item}
                    </div>
                ))}
            </div>
        }
    >
        <div className={props.items.length < minTriggerNumber ? undefined : "mouse"}>
            {displayItems.map((item, index) => (
                <div key={index} style={{lineHeight: props.contentLineHeight || "20px"}}>
                    {item}
                </div>
            ))}
        </div>
    </Popover>
}



/**
 * 带图标标题组件的TableCellPopover
 * @param title 标题
 * @param data 数据
 * @param iconlink iconfont链接
 * @param iconsrc 其他图片路径
 * @param contentLineHeight 底层内容的行高
 */
 interface TitledPopoverProps {
    title: string;
    data: any[];
    iconlink?: string;
    iconsrc?: string;
    contentLineHeight?: string;
}

export const TitledPopover = (props: TitledPopoverProps) => {
    const { title, data, iconlink, iconsrc, contentLineHeight } = props
    if (!data || data.length === 0) return <>-</>;
    const _title = (
        <span style={{ marginLeft: -2, display: "flex", alignItems: "center" }}>
            {iconlink ? <svg className="iconfont" width={16} height={16}> <use xlinkHref={iconlink} /></svg> : iconsrc ? <img alt='icon' src={iconsrc} width={16} height={16} /> : null}
            <span className="title-text" style={(iconlink || iconsrc) ? { marginLeft: 8 } : {}}>{title}</span>
        </span>
    )
    return <TableCellPopover title={_title} items={data} contentLineHeight={contentLineHeight} />
}
