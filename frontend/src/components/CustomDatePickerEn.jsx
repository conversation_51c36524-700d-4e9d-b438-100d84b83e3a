/** @jsxImportSource @emotion/react */
import { CalendarOutlined, ClockCircleOutlined, } from "@ant-design/icons";
import { Select } from "antd";
import { useState } from "react";
import { useIntl } from "react-intl";
import DatePicker from "./DatePicker";
import styled from "@emotion/styled";

export const CustomDatePickerEn = ({
    format = "YYYY-MM-DD",
    value,
    onChange,
    picker,
    disabledDate,
    disabled
}) => {
    const [open, setOpen] = useState(false);
    const intl = useIntl();
    const { formatMessage } = intl;
    let selectValue = !!value
        ? typeof value === "string"
            ? value === "currentTime"
                ? formatMessage({ id: "common.current.time" })
                : value
            : value.format(format)
        : undefined;

    const props = {
        format,
        open,
        value: typeof value === "string" ? null : value,
        onChange: (value) => {
            onChange(value);

            setOpen(false);
        },
        onOk: () => setOpen(false),
        picker,
        disabledDate,
        disabled
    };

    let child = (
        <DatePicker
            disabled={disabled}
            style={{ width: "100%" }}
            showTime={format.includes("HH")}
            showToday={false}
            picker={picker}
            {...props}
        />
    );

    return (
        <div style={{ position: "relative" }}>
            {child}
            <CustomSelect
                disabled={disabled}
                allowClear
                open={false}
                placeholder={formatMessage({ id: "placeholder.select.common" })}
                suffixIcon={
                    format.includes("Y") ? (
                        <CalendarOutlined
                            style={{ fontSize: "14px" }}
                            onClick={() => setOpen(true)}
                        />
                    ) : (
                        <ClockCircleOutlined
                            style={{ fontSize: "14px" }}
                            onClick={() => setOpen(true)}
                        />
                    )
                }
                style={{ position: "absolute", left: 0, width: "100%" }}
                value={selectValue}
                onBlur={() => setOpen(false)}
                onClear={() => {
                    onChange(undefined);
                    setOpen(false);
                }}
                onDropdownVisibleChange={setOpen}
            />
        </div>
    );
};

const CustomSelect = styled(Select)`
    .ant-select-clear {
        font-size: 14px;
        height: 14px;
        width: 14px;
    }
`;
