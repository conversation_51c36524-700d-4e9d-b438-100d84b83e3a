import {useSafeState, useUpdateEffect} from "ahooks";
import {usePage} from "../context/page";
import {useEffect} from "react";

interface useCacheTableProp {
    dataSource: any[];
}
export const useCacheTableBatch = (props: useCacheTableProp) => {
    const pageCtx = usePage();
    // 当前table渲染的数据
    const [tableData, setTableData] = useSafeState<any[]>([]);
    const [dateLength, setDataLength] = useSafeState<number>(0);
    useEffect(() => {
        var totol = 0
        for (let index = 0; index < props.dataSource?.length; index++) {
            const data = props.dataSource[index];
            if (data.packageNumberRowSpan !== undefined && data.packageNumberRowSpan === 0) {
            }else{
                totol++
            }
        }
        pageCtx.setTotal(totol);
        if (props.dataSource?.length !== dateLength) {
            setDataLength(props.dataSource?.length);
            pageCtx.setCurrentPage(1);
            pageCtx.setPageSize(10);
            //setTableData(props.dataSource.slice(0, 10));
            //判断是否有合并单元格的数据
            var countEnd = 0
            var rowCount = 0 
            for (let index = 0; index < props.dataSource?.length; index++) {
                const data = props.dataSource[index];
                if (data.packageNumberRowSpan !== undefined && data.packageNumberRowSpan === 0) {
                    rowCount++
                }else{
                    countEnd++
                }
                if (countEnd ===+ 11){
                    countEnd = countEnd - 1
                    break
                }
            }
            var end = countEnd + rowCount
            setTableData(props.dataSource.slice(0, end));
        }
    }, [props.dataSource])

    useUpdateEffect(() => {
        // 页码更新，从缓存中更新当前的表格
        const start = (pageCtx.currentPage - 1) * pageCtx.pageSize;
        const end = pageCtx.currentPage * pageCtx.pageSize;
        var lstart = start
        var lend = end
        //判断是否有合并单元格的数据
        var countEnd = 0 //当前第几条
        var rowCount = 0 
        for (let index = 0; index < props.dataSource?.length; index++) {
            const data = props.dataSource[index];
            if (data.packageNumberRowSpan !== undefined && data.packageNumberRowSpan === 0) {
                rowCount++
            }else{
                countEnd++
            }
            if (countEnd === start+1){
                countEnd = countEnd -1
                break
            }
        }
        lstart = countEnd + rowCount
        var endCountEnd = 0
        var endRowCount = 0 
        for (let index = lstart; index < props.dataSource?.length; index++) {
            const data = props.dataSource[index];
            if (data.packageNumberRowSpan !== undefined && data.packageNumberRowSpan === 0) {
                endRowCount++
            }else{
                endCountEnd++
            }
            if (endCountEnd === pageCtx.pageSize+1){
                endCountEnd = endCountEnd - 1
                break
            }
        }
        lend = endCountEnd + endRowCount
        setTableData(props.dataSource.slice(lstart, lstart+lend));
    }, [props.dataSource, pageCtx]);

    // console.log("td==" + tableData.length)
    return tableData;
}