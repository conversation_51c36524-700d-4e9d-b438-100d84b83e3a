import { useSafeState } from "ahooks";
import { Checkbox } from "antd";
import { useCallback, useEffect } from "react";
import _ from "lodash";

interface rowSelectionProp {
    dataSource: any[];
    allData: any[];
    key: string;
    renderCell: any;
    packageIsOpen: boolean;
    allSelected: boolean;
    onChange: any,
    //changeKey: any,
    operation: any,
    mixPackage: any;
}

export const useRowSelection = (props: rowSelectionProp) => {
    const [selectKeysSet] = useSafeState<Set<string>>(new Set());
    const [selectedIds, setSelectedIds] = useSafeState<any[]>([]);
    let [currentSelectList, setCurrentSelectList] = useSafeState<any>([]);

    const resetSelectedIds = useCallback(() => {
        selectKeysSet.clear();
        setSelectedIds([]);
        // props.changeKey([])
        setCurrentSelectList([]);
    }, []);
    const headerCheckbox = () => {
        const actualList = props.packageIsOpen
            ? props.dataSource.filter(
                (item: any) =>
                    (item.package_numberRowSpan !== undefined &&
                        item.package_numberRowSpan !== 0) ||
                    item.package_numberRowSpan === undefined
            )
            : props.dataSource;

        const selectedList = _.intersection(
            currentSelectList,
            actualList.map((item: any) => item._id)
        );

        return (
            <Checkbox
                checked={
                    !!selectedList.length && selectedList.length === actualList.length
                }
                indeterminate={
                    !!selectedList.length && selectedList.length < actualList.length
                }
                onChange={(e) => {
                    if (e.target.checked) {
                        actualList.forEach((item: any) => {
                            selectKeysSet.add(item._id);
                            currentSelectList.push(item._id);
                        });
                    } else {
                        var del: any = []
                        actualList.forEach((item: any) => {
                            selectKeysSet.delete(item._id);
                            del.push(item._id);
                            // currentSelectList = [];
                        });
                        _.pullAll(currentSelectList, del)
                    }
                    const keys: string[] = [];
                    if (props.packageIsOpen) {
                        selectKeysSet.forEach((v) => {
                            props.allData.forEach((it: any) => {
                                if (v === it._id && it.package_numberRowSpan !== 0) {
                                    keys.push(v);
                                }
                            });
                        });
                        //手动勾选选中全部数据
                        const allPackagekeys: string[] = [];
                        const allkeys: string[] = [];
                        props.allData.forEach((it: any) => {
                            if (
                                !props.packageIsOpen ||
                                (props.packageIsOpen && it.package_numberRowSpan !== 0)
                            ) {
                                allPackagekeys.push(it[props.key]);
                            }
                            allkeys.push(it[props.key]);
                        });
                        if (
                            keys.length === allPackagekeys.length &&
                            allPackagekeys.filter((t) => !keys.includes(t))
                        ) {
                            setSelectedIds(allkeys);
                        } else {
                            setSelectedIds(keys);
                        }
                    } else {
                        selectKeysSet.forEach((v) => {
                            keys.push(v);
                        });
                        setSelectedIds(keys);
                    }
                    setCurrentSelectList([...currentSelectList]);
                }}
            //onClick={onSelectAll}
            ></Checkbox>
        );
    };
    const rowSelection = {
        columnTitle: headerCheckbox,
        type: "checkbox" as "checkbox",
        selectedRowKeys: selectedIds,
        onSelect: (record: any, selected: boolean) => {
            if (selected) {
                selectKeysSet.add(record[props.key]);
                currentSelectList.push(record[props.key]);
            } else {
                selectKeysSet.delete(record[props.key]);
                const index = _.indexOf(currentSelectList, record[props.key])
                currentSelectList.splice(index, 1);
            }
            setCurrentSelectList([...currentSelectList]);
            const keys: string[] = [];
            if (props.packageIsOpen) {
                selectKeysSet.forEach((v) => {
                    props.dataSource.forEach((it: any) => {
                        if (v === it._id && it.package_numberRowSpan !== 0) {
                            keys.push(v);
                        }
                    });
                });
                if (props.operation === "add") {
                    let packageNames: any = props.mixPackage[record.name]
                    if (packageNames !== undefined && packageNames.length > 1) {
                        props.dataSource.forEach((it: any) => {
                            if (packageNames.includes(it.name) && record._id !== it._id && record.batchNumber === it.batchNumber && record.expirationData === it.expirationData && record.packageMethod === it.packageMethod) {
                                if (selected) {
                                    keys.push(it._id);
                                    selectKeysSet.add(it._id);
                                    currentSelectList.push(it._id);
                                } else {
                                    const index: any = keys.indexOf(it._id)
                                    if (index !== -1) {
                                        keys.splice(index, 1);
                                        selectKeysSet.delete(it._id);
                                    }
                                }
                            }
                        });
                    }
                }
                //手动勾选选中全部数据
                const allPackagekeys: string[] = [];
                const allkeys: string[] = [];
                props.dataSource.forEach((it: any) => {
                    if (
                        !props.packageIsOpen ||
                        (props.packageIsOpen && it.package_numberRowSpan !== 0)
                    ) {
                        allPackagekeys.push(it[props.key]);
                    }
                    allkeys.push(it[props.key]);
                });

                if (
                    keys.length === allPackagekeys.length &&
                    allPackagekeys.filter((t) => !keys.includes(t))
                ) {
                    setSelectedIds(allkeys);
                } else {
                    setSelectedIds(keys);
                }
            } else {
                selectKeysSet.forEach((v) => {
                    keys.push(v);
                });
                setSelectedIds(keys);
            }
            // if (props.changeKey !== undefined && props.changeKey !== null) {
            //     props?.changeKey(keys)
            // }
        },
        renderCell: props.renderCell,
        onChange: props.onChange,
    };
    useEffect(() => {
        // 数据为空时，清空状态
        if (props.dataSource.length === 0) {
            // selectKeysSet.clear();
            // setSelectedIds([]);
            //currentSelectList = [];
        } else {
            if (props.allSelected) {
                currentSelectList = [];
                const keys: string[] = [];
                props.allData.forEach((it: any) => {
                    selectKeysSet.add(it[props.key]);
                    keys.push(it[props.key]);
                    currentSelectList.push(it[props.key]);
                });
                setSelectedIds(keys);
                // props.changeKey(keys)
                setCurrentSelectList([...currentSelectList]);
            }
        }
    }, [props.dataSource]);
    return {
        selectedIds: selectedIds,
        resetSelectedIds: resetSelectedIds,
        setSelectedIds: setSelectedIds,
        rowSelection: rowSelection,
    };
};
