import { useMemo, useCallback, useRef } from 'react';

/**
 * 优化的 rowSelection Hook
 * 专门解决全选性能问题
 */
export const useOptimizedRowSelection = ({
    dataSource = [],
    selectableStatusCheck,
    onSelectionChange,
    selectedIds = [],
    selectedRows = [],
}) => {
    const selectionTimeoutRef = useRef(null);
    const LARGE_DATA_THRESHOLD = 1000;

    // 预计算可选择的行数据
    const selectableData = useMemo(() => {
        const selectableRows = dataSource.filter(row => 
            selectableStatusCheck ? selectableStatusCheck(row) : true
        );
        const selectableRowIds = selectableRows.map(row => row.id);
        
        return {
            rows: selectableRows,
            ids: selectableRowIds,
            count: selectableRows.length
        };
    }, [dataSource, selectableStatusCheck]);

    // 优化的选择变更处理
    const handleSelectionChange = useCallback((selectedRowKeys, selectedRowsData) => {
        // 清除之前的定时器
        if (selectionTimeoutRef.current) {
            clearTimeout(selectionTimeoutRef.current);
        }

        // 立即更新选中的 keys（用于 UI 反馈）
        if (onSelectionChange) {
            onSelectionChange(selectedRowKeys, selectedRowsData, 'change');
        }

        // 防抖更新选中的行数据（用于业务逻辑）
        selectionTimeoutRef.current = setTimeout(() => {
            if (onSelectionChange) {
                onSelectionChange(selectedRowKeys, selectedRowsData, 'debounced');
            }
        }, 100);
    }, [onSelectionChange]);

    // 优化的全选处理
    const handleSelectAll = useCallback((selected, selectedRowsData, changeRows) => {
        // 清除之前的定时器
        if (selectionTimeoutRef.current) {
            clearTimeout(selectionTimeoutRef.current);
        }

        const isLargeDataset = dataSource.length > LARGE_DATA_THRESHOLD;

        if (selected) {
            // 全选：使用预计算的可选择行数据
            if (onSelectionChange) {
                onSelectionChange(selectableData.ids, selectableData.rows, 'selectAll');
            }

            if (isLargeDataset) {
                // 大数据量：分批处理，避免阻塞 UI
                const batchSize = 200;
                let currentIndex = 0;
                
                const processBatch = () => {
                    const endIndex = Math.min(currentIndex + batchSize, selectableData.rows.length);
                    
                    if (endIndex >= selectableData.rows.length) {
                        // 最后一批，完成处理
                        if (onSelectionChange) {
                            onSelectionChange(selectableData.ids, selectableData.rows, 'selectAllComplete');
                        }
                    } else {
                        // 继续下一批
                        currentIndex = endIndex;
                        setTimeout(processBatch, 0); // 让出控制权
                    }
                };
                
                processBatch();
            }
        } else {
            // 反选：直接清空
            if (onSelectionChange) {
                onSelectionChange([], [], 'deselectAll');
            }
        }
    }, [selectableData, dataSource.length, LARGE_DATA_THRESHOLD, onSelectionChange]);

    // 缓存的 getCheckboxProps 函数
    const getCheckboxProps = useCallback((record) => ({
        disabled: selectableStatusCheck ? !selectableStatusCheck(record) : false
    }), [selectableStatusCheck]);

    // 构建优化的 rowSelection 对象
    const rowSelection = useMemo(() => ({
        type: "checkbox",
        onChange: handleSelectionChange,
        onSelectAll: handleSelectAll,
        selectedRowKeys: selectedIds,
        getCheckboxProps: getCheckboxProps,
        preserveSelectedRowKeys: true,
        // 性能优化配置
        columnWidth: 32,
        checkStrictly: false,
        // 自定义选择菜单
        selections: [
            {
                key: 'all-selectable',
                text: `选择可用项 (${selectableData.count})`,
                onSelect: () => {
                    handleSelectAll(true, selectableData.rows, selectableData.rows);
                },
            },
            {
                key: 'clear-all',
                text: '清空选择',
                onSelect: () => {
                    handleSelectAll(false, [], []);
                },
            },
            {
                key: 'invert-selectable',
                text: '反选可用项',
                onSelect: () => {
                    const currentSelectedSet = new Set(selectedIds);
                    const invertedIds = selectableData.ids.filter(id => !currentSelectedSet.has(id));
                    const invertedRows = selectableData.rows.filter(row => !currentSelectedSet.has(row.id));
                    handleSelectionChange(invertedIds, invertedRows);
                },
            },
        ],
    }), [
        handleSelectionChange,
        handleSelectAll,
        selectedIds,
        getCheckboxProps,
        selectableData
    ]);

    // 清理函数
    const cleanup = useCallback(() => {
        if (selectionTimeoutRef.current) {
            clearTimeout(selectionTimeoutRef.current);
        }
    }, []);

    return {
        rowSelection,
        selectableData,
        cleanup,
        // 辅助方法
        selectAll: () => handleSelectAll(true, selectableData.rows, selectableData.rows),
        deselectAll: () => handleSelectAll(false, [], []),
        isLargeDataset: dataSource.length > LARGE_DATA_THRESHOLD,
    };
};

/**
 * 使用示例：
 * 
 * const {
 *   rowSelection,
 *   selectableData,
 *   cleanup,
 *   selectAll,
 *   deselectAll
 * } = useOptimizedRowSelection({
 *   dataSource: summaryList,
 *   selectableStatusCheck: (record) => [1, 6, 14, 7].includes(record.status),
 *   onSelectionChange: (selectedKeys, selectedRows, type) => {
 *     console.log('Selection changed:', type, selectedKeys.length);
 *     if (type === 'change' || type === 'selectAll' || type === 'deselectAll') {
 *       setSelectedIds(selectedKeys);
 *     }
 *     if (type === 'debounced' || type === 'selectAllComplete') {
 *       setSelecteds(selectedRows);
 *     }
 *   },
 *   selectedIds,
 *   selectedRows,
 * });
 * 
 * // 在组件卸载时清理
 * useEffect(() => {
 *   return cleanup;
 * }, [cleanup]);
 */

export default useOptimizedRowSelection;
