export function combineRow(tableData, key, turnKey, indexFlag) {
    var index = 0
    for (var i = 0; i < tableData.length; i++) {
        index++
        const item = tableData[i]
        let count = 1
        // if (item[key] !== undefined  && item[key] !== ""  && packageDrugNames[item.name] !== undefined && packageDrugNames[item.name] != null){
        if (item[key] !== undefined && item[key] !== "" && item.packageDrug !== undefined && item.packageDrug !== "" && item.packageDrug === true) {
            for (let j = i + 1; j < tableData.length; j++) {
                // 如果是同一个值，往后递增
                if (item[key] === tableData[j][key]) {
                    count++
                    // 往后相同的值都设为空单元格
                    tableData[j][`${turnKey}RowSpan`] = 0
                    // 只有同值第一个才设置合并的单元格数
                    item[`${turnKey}RowSpan`] = count
                    // 所有都是为同一个值的情况
                    // 如果到了尾部，则循环结束
                    if (j === tableData.length - 1) {
                        item["index"] = index
                        return tableData
                    }
                } else {
                    // 指针跳转到下一个，从下一排开始
                    i = j - 1
                    count = 1
                    if (tableData[j][key] !== undefined && tableData[j][key] !== "") {
                        tableData[j][`${turnKey}RowSpan`] = 1
                        item["index"] = index
                    }
                    break
                }
            }
        }
        if (indexFlag) {
            item["index"] = index
        }

    }

    return tableData
}


