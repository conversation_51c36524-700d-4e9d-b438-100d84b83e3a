export const commonMockData = [
    {
        "method": "GET",
        "path": "/users/learn",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "courses": 3,
            }
        }
    },
    {
        "method": "GET",
        "path": "/envs/cloud",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": "https://cloud-dev.clinflash.com"
        }
    },
    {
        "method": "GET",
        "path": "/projects/time-zone",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": "UTC+08:00"
        }
    },
    {
        "method": "GET",
        "path": "/sites/config",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {}
        }
    },
    {
        "method": "GET",
        "path": "/roles/is-bind-role",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": false
        }
    },
    {
        "method": "GET",
        "path": "/check/auth-user",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "auth": false
            }
        }
    },
    {
        "method": "GET",
        "path": "/roles/roleIsBind",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": false
        }
    },
    {
        "method": "POST",
        "path": "/multiLanguage/translate/map",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {}
        }
    },
    {
        "method": "GET",
        "path": "/multiLanguage/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "682a9b3180c8209fcecf826e",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "projectId": "67480dd6bbbcf65e2286164a",
                    "code": "zh",
                    "language": "简体中文",
                    "status": true,
                    "sharedSystemLibrary": true,
                    "translationQuantity": 0,
                },
                {
                    "id": "682a9b3180c8209fcecf826f",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "projectId": "67480dd6bbbcf65e2286164a",
                    "code": "en",
                    "language": "English",
                    "status": true,
                    "sharedSystemLibrary": true,
                    "translationQuantity": 0,
                },
            ]
        }
    },
    {
        "method": "GET",
        "path": "/countries",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "cn": "中国",
                    "en": "China",
                    "ko": "중국",
                    "code": "CHN",
                    "state": [
                        {
                            "cn": "北京",
                            "en": "Beijing",
                            "ko": "Beijing",
                            "code": "11",
                            "city": [
                                {
                                    "cn": "昌平",
                                    "en": "Changping",
                                    "ko": "창핑",
                                    "code": "21"
                                },
                                {
                                    "cn": "朝阳",
                                    "en": "Chaoyang",
                                    "ko": "차오양",
                                    "code": "5"
                                },
                                {
                                    "cn": "大兴",
                                    "en": "Daxing",
                                    "ko": "다싱",
                                    "code": "24"
                                },
                                {
                                    "cn": "东城",
                                    "en": "Dongcheng",
                                    "ko": "둥청",
                                    "code": "1"
                                },
                                {
                                    "cn": "房山",
                                    "en": "Fangshan",
                                    "ko": "팡산",
                                    "code": "11"
                                },
                                {
                                    "cn": "丰台",
                                    "en": "Fengtai",
                                    "ko": "펭타이",
                                    "code": "6"
                                },
                                {
                                    "cn": "海淀",
                                    "en": "Haidian",
                                    "ko": "하이뎬",
                                    "code": "8"
                                },
                                {
                                    "cn": "怀柔",
                                    "en": "Huairou",
                                    "ko": "화이러우",
                                    "code": "27"
                                },
                                {
                                    "cn": "门头沟",
                                    "en": "Mentougou",
                                    "ko": "먼터우거우",
                                    "code": "9"
                                },
                                {
                                    "cn": "密云",
                                    "en": "Miyun",
                                    "ko": "미윈",
                                    "code": "28"
                                },
                                {
                                    "cn": "平穀",
                                    "en": "Pinggu",
                                    "ko": "핑구",
                                    "code": "26"
                                },
                                {
                                    "cn": "石景山",
                                    "en": "Shijingshan",
                                    "ko": "스징산",
                                    "code": "7"
                                },
                                {
                                    "cn": "顺义",
                                    "en": "Shunyi",
                                    "ko": "순이",
                                    "code": "13"
                                },
                                {
                                    "cn": "通州",
                                    "en": "Tongzhou",
                                    "ko": "퉁저우",
                                    "code": "12"
                                },
                                {
                                    "cn": "西城",
                                    "en": "Xicheng",
                                    "ko": "시청",
                                    "code": "2"
                                },
                                {
                                    "cn": "延庆",
                                    "en": "Yanqing",
                                    "ko": "옌칭",
                                    "code": "29"
                                }
                            ]
                        },
                    ]
                },
            ]
        }
    },
    {
        "method": "GET",
        "path": "/operation",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "id": "67593d1098853085045c2fd3",
                        "user_email": "<EMAIL>",
                        "user_name": "预览用户(100000)",
                        "unicode": 0,
                        "operation_type": "编辑",
                        "time": 1733901584,
                        "time_str": "2024-12-11 15:19:44(UTC+8)",
                        "fields": [
                            "项目构建-属性配置-第二阶段",
                            "盲法 : 开放 -> 盲态"
                        ]
                    },
                ],
                "total": 1
            }
        }
    },
    {
        "method": "GET",
        "path": "/projects-roles",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "67480dd6bbbcf65e2286164c",
                    "roleId": "61837481a51e045e685fd805",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "projectId": "67480dd6bbbcf65e2286164a",
                    "name": "Biostatistician [blinded]",
                    "scope": "study",
                    "description": "111",
                    "template": 1,
                    "status": 1,
                    "permissions": [
                        "operation.subject.view-list",
                        "operation.subject.random",
                        "operation.subject.replace",
                        "operation.subject.trail",
                        "operation.subject.unblinding-pv",
                        "operation.subject.medicine.view-dispensing",
                        "operation.subject.medicine.trail",
                        "operation.subject.medicine.dispensing",
                        "operation.subject.medicine.replace",
                        "operation.subject.unblinding",
                        "operation.subject.download",
                        "operation.subject.medicine.export",
                        "operation.subject.registered",
                        "operation.subject.update",
                        "operation.subject.delete",
                        "operation.subject.medicine.room",
                        "operation.subject.medicine.retrieval",
                        "operation.subject.medicine.out-visit-dispensing",
                        "operation.subject.medicine.invalid",
                        "operation.subject.medicine.room-download",
                        "operation.subject.medicine.register",
                        "operation.subject.secede",
                        "operation.projects.home.view",
                        "operation.build.history.view",
                        "operation.build.history.print",
                        "operation.supply.shipment.create",
                        "operation.supply.shipment.cancel",
                        "operation.supply.shipment.send",
                        "operation.supply.shipment.lose",
                        "operation.supply.shipment.list",
                        "operation.supply.shipment.receive",
                        "operation.supply.shipment.download",
                        "operation.supply.shipment.alarm",
                        "operation.supply.shipment.confirm",
                        "operation.supply.shipment.reason",
                        "operation.supply.recovery.list",
                        "operation.supply.recovery.add",
                        "operation.supply.recovery.receive",
                        "operation.supply.recovery.confirm",
                        "operation.supply.recovery.cancel",
                        "operation.supply.recovery.lose",
                        "operation.supply.recovery.download",
                        "operation.supply.recovery.reason",
                        "operation.supply.freeze.list",
                        "operation.supply.freeze.release",
                        "operation.supply.freeze.delete",
                        "operation.subject.medicine.trail.print",
                        "operation.subject.print",
                        "operation.supply.shipment.print",
                        "operation.supply.recovery.print",
                        "operation.supply.freeze.print",
                        "operation.supply.shipment.history",
                        "operation.supply.recovery.history",
                        "operation.supply.freeze.history",
                        "operation.supply.storehouse.medicine.summary",
                        "operation.supply.storehouse.medicine.singe",
                        "operation.supply.storehouse.medicine.use",
                        "operation.supply.storehouse.medicine.freeze",
                        "operation.supply.storehouse.medicine.history",
                        "operation.supply.storehouse.medicine.print",
                        "operation.supply.storehouse.no_number.view",
                        "operation.supply.storehouse.no_number.freeze",
                        "operation.supply.storehouse.no_number.history",
                        "operation.supply.storehouse.no_number.print",
                        "operation.supply.site.medicine.summary",
                        "operation.supply.site.medicine.singe",
                        "operation.supply.site.medicine.use",
                        "operation.supply.site.medicine.freeze",
                        "operation.supply.site.medicine.download",
                        "operation.supply.site.medicine.history",
                        "operation.supply.site.medicine.print",
                        "operation.supply.site.no_number.view",
                        "operation.supply.site.no_number.freeze",
                        "operation.supply.site.no_number.history",
                        "operation.supply.site.no_number.print",
                        "operation.project.status.view",
                        "operation.project.task.view",
                        "operation.project.dynamics.view",
                        "operation.subject.download-random",
                        "operation.subject.download-random.template",
                        "operation.supply.storehouse.medicine.download",
                        "operation.supply.storehouse.medicine.download.template",
                        "operation.build.medicine.upload.downdata.template",
                        "operation.build.randomization.info.export",
                        "operation.report.auditTrailExport.download",
                        "operation.build.settings.user.download",
                        "operation.report.userLoginHistory.download",
                        "operation.report.userRoleAssignHistory.download",
                        "operation.report.randomizationStatisticsExport.download",
                        "operation.report.subjectStatisticsExport.download",
                        "operation.projects.project.info.view",
                        "operation.supply.shipment.cancel-dtp",
                        "operation.supply.shipment.confirm-dtp"
                    ]
                },
                {
                    "id": "67480dd6bbbcf65e2286164f",
                    "roleId": "627d044aa9968d53ae6b221c",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "projectId": "67480dd6bbbcf65e2286164a",
                    "name": "CRC [blinded, can see room number temporarily]",
                    "scope": "site",
                    "description": "-",
                    "template": 1,
                    "status": 1,
                    "permissions": [
                        "operation.subject.medicine.export-room",
                        "operation.supply.storehouse.medicine.summary",
                        "operation.supply.storehouse.medicine.singe",
                        "operation.supply.storehouse.medicine.use",
                        "operation.supply.storehouse.medicine.freeze",
                        "operation.supply.storehouse.medicine.download",
                        "operation.supply.site.medicine.summary",
                        "operation.supply.site.medicine.singe",
                        "operation.supply.site.medicine.use",
                        "operation.supply.site.medicine.freeze",
                        "operation.supply.site.medicine.download",
                        "operation.supply.site.no_number.view",
                        "operation.supply.site.no_number.freeze",
                        "operation.build.storehouse.add",
                        "operation.build.storehouse.delete",
                        "operation.build.storehouse.edit",
                        "operation.build.storehouse.view",
                        "operation.build.attribute.view",
                        "operation.build.attribute.history",
                        "operation.build.code-rule.view",
                        "operation.build.code-rule.edit",
                        "operation.build.randomization.type.view",
                        "operation.build.randomization.type.edit",
                        "operation.build.randomization.group.add",
                        "operation.build.randomization.group.view",
                        "operation.build.randomization.factor.add",
                        "operation.build.randomization.factor.view",
                        "operation.build.randomization.factor.delete",
                        "operation.build.randomization.factor.edit",
                        "operation.build.randomization.factor.set-toplimit",
                        "operation.build.randomization.list.view-summary",
                        "operation.build.randomization.list.upload",
                        "operation.build.randomization.list.active",
                        "operation.build.randomization.list.invalid",
                        "operation.build.randomization.list.segmentation",
                        "operation.build.randomization.factor-in.view",
                        "operation.build.randomization.factor-in.set-people",
                        "operation.build.randomization.form.add",
                        "operation.build.randomization.form.delete",
                        "operation.build.randomization.form.list",
                        "operation.build.medicine.visit.add",
                        "operation.build.medicine.visit.delete",
                        "operation.build.medicine.visit.edit",
                        "operation.build.medicine.visit.list",
                        "operation.build.medicine.configuration.list",
                        "operation.build.medicine.upload.list",
                        "operation.build.medicine.upload.downdata",
                        "operation.build.medicine.packlist.upload",
                        "operation.build.medicine.otherm.list",
                        "operation.build.medicine.batch.list",
                        "operation.build.medicine.batch.edit",
                        "operation.build.medicine.barcode.view",
                        "operation.build.medicine.barcode.scan",
                        "operation.build.supply-plan.medicine.view",
                        "operation.build.supply-plan.medicine.history",
                        "operation.build.history.view",
                        "operation.build.history.print",
                        "operation.build.settings.notice.view",
                        "operation.build.settings.notice.edit",
                        "operation.build.settings.user.view",
                        "operation.build.settings.user.edit",
                        "operation.build.settings.user.download",
                        "operation.build.randomization.info.view",
                        "operation.build.randomization.info.export",
                        "operation.monitor.view",
                        "operation.monitor.edit",
                        "operation.supply.shipment.cancel",
                        "operation.supply.shipment.send",
                        "operation.supply.shipment.lose",
                        "operation.supply.shipment.list",
                        "operation.supply.shipment.receive",
                        "operation.supply.shipment.download",
                        "operation.supply.shipment.alarm",
                        "operation.supply.shipment.confirm",
                        "operation.supply.shipment.reason",
                        "operation.supply.shipment.create",
                        "operation.subject.view-list",
                        "operation.subject.random",
                        "operation.subject.replace",
                        "operation.subject.trail",
                        "operation.subject.unblinding-pv",
                        "operation.subject.medicine.view-dispensing",
                        "operation.subject.medicine.trail",
                        "operation.subject.medicine.dispensing",
                        "operation.subject.medicine.replace",
                        "operation.subject.unblinding",
                        "operation.subject.download",
                        "operation.subject.medicine.export",
                        "operation.subject.download-random",
                        "operation.subject.registered",
                        "operation.subject.update",
                        "operation.subject.delete",
                        "operation.subject.medicine.room",
                        "operation.subject.medicine.retrieval",
                        "operation.subject.medicine.out-visit-dispensing",
                        "operation.subject.medicine.invalid",
                        "operation.subject.medicine.room-download",
                        "operation.subject.medicine.register",
                        "operation.subject.secede",
                        "operation.subject.medicine.reissue",
                        "operation.build.site.view",
                        "operation.build.site.add",
                        "operation.build.site.dispensing",
                        "operation.supply.recovery.list",
                        "operation.supply.recovery.receive",
                        "operation.supply.recovery.confirm",
                        "operation.supply.recovery.cancel",
                        "operation.supply.recovery.lose",
                        "operation.supply.recovery.download",
                        "operation.supply.recovery.reason",
                        "operation.supply.recovery.add",
                        "operation.supply.freeze.list",
                        "operation.supply.freeze.release",
                        "operation.supply.freeze.delete",
                        "operation.build.site.supply-plan.edit",
                        "operation.build.site.supply-plan.view",
                        "operation.build.site.edit",
                        "operation.subject.medicine.trail.print",
                        "operation.subject.print",
                        "operation.build.randomization.form.edit",
                        "operation.build.randomization.form.preview",
                        "operation.supply.storehouse.medicine.print",
                        "operation.supply.site.medicine.print",
                        "operation.supply.shipment.print",
                        "operation.supply.recovery.print",
                        "operation.supply.freeze.print",
                        "operation.build.medicine.upload.print",
                        "operation.supply.storehouse.medicine.history",
                        "operation.supply.site.medicine.history",
                        "operation.supply.shipment.history",
                        "operation.supply.recovery.history",
                        "operation.supply.freeze.history",
                        "operation.build.medicine.upload.uploadHistory",
                        "operation.project.status.view",
                        "operation.project.task.view",
                        "operation.project.dynamics.view",
                        "operation.build.settings.user.add",
                        "operation.build.settings.user.role",
                        "operation.build.settings.user.site",
                        "operation.build.settings.user.depot",
                        "operation.build.settings.user.app",
                        "operation.build.settings.user.reauthorization",
                        "operation.build.settings.user.add",
                        "operation.build.settings.user.role",
                        "operation.build.settings.user.site",
                        "operation.build.settings.user.depot",
                        "operation.build.settings.user.app",
                        "operation.build.settings.user.reauthorization",
                        "operation.build.randomization.factor-in.delete",
                        "operation.build.randomization.factor-in.add",
                        "operation.supply.shipment.cancel-dtp",
                        "operation.supply.shipment.confirm-dtp"
                    ]
                },
                {
                    "id": "67480dd6bbbcf65e22861650",
                    "roleId": "630c575979a1fdb5e11383b5",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "projectId": "67480dd6bbbcf65e2286164a",
                    "name": "IP Officer",
                    "scope": "study",
                    "description": "全权限",
                    "template": 1,
                    "status": 1,
                    "permissions": [
                        "operation.projects.home.view",
                        "operation.subject.view-list",
                        "operation.subject.random",
                        "operation.subject.replace",
                        "operation.subject.trail",
                        "operation.subject.unblinding-pv",
                        "operation.subject.medicine.view-dispensing",
                        "operation.subject.medicine.trail",
                        "operation.subject.medicine.dispensing",
                        "operation.subject.medicine.reissue",
                        "operation.subject.medicine.replace",
                        "operation.subject.registered",
                        "operation.subject.update",
                        "operation.subject.delete",
                        "operation.subject.medicine.room",
                        "operation.subject.medicine.retrieval",
                        "operation.subject.medicine.out-visit-dispensing",
                        "operation.subject.medicine.invalid",
                        "operation.subject.medicine.room-download",
                        "operation.subject.medicine.register",
                        "operation.subject.secede",
                        "operation.supply.storehouse.medicine.summary",
                        "operation.supply.storehouse.medicine.singe",
                        "operation.supply.storehouse.medicine.use",
                        "operation.supply.storehouse.medicine.freeze",
                        "operation.supply.site.medicine.singe",
                        "operation.supply.site.medicine.use",
                        "operation.supply.site.medicine.freeze",
                        "operation.supply.site.no_number.view",
                        "operation.supply.site.no_number.freeze",
                        "operation.build.storehouse.add",
                        "operation.build.storehouse.delete",
                        "operation.build.storehouse.edit",
                        "operation.build.storehouse.notice",
                        "operation.build.storehouse.view",
                        "operation.build.storehouse.alarm",
                        "operation.build.site.view",
                        "operation.build.site.add",
                        "operation.build.site.dispensing",
                        "operation.build.attribute.view",
                        "operation.build.attribute.edit",
                        "operation.build.attribute.history",
                        "operation.build.code-rule.view",
                        "operation.build.code-rule.edit",
                        "operation.build.simulate-random.view",
                        "operation.build.simulate-random.edit",
                        "operation.build.simulate-random.add",
                        "operation.build.simulate-random.run",
                        "operation.build.simulate-random.site",
                        "operation.build.simulate-random.factor",
                        "operation.build.randomization.type.view",
                        "operation.build.randomization.type.edit",
                        "operation.build.randomization.list.view-summary",
                        "operation.build.randomization.list.upload",
                        "operation.build.randomization.list.generate",
                        "operation.build.randomization.list.active",
                        "operation.build.randomization.list.export",
                        "operation.build.randomization.list.invalid",
                        "operation.build.randomization.list.attribute",
                        "operation.build.randomization.factor-in.view",
                        "operation.build.randomization.factor-in.set-people",
                        "operation.build.randomization.form.add",
                        "operation.build.randomization.form.delete",
                        "operation.build.randomization.form.list",
                        "operation.build.medicine.configuration.add",
                        "operation.build.medicine.configuration.delete",
                        "operation.build.medicine.configuration.edit",
                        "operation.build.medicine.configuration.list",
                        "operation.build.medicine.packlist.upload",
                        "operation.build.medicine.otherm.add",
                        "operation.build.medicine.otherm.delete",
                        "operation.build.medicine.otherm.edit",
                        "operation.build.medicine.otherm.list",
                        "operation.build.medicine.batch.list",
                        "operation.build.medicine.batch.edit",
                        "operation.build.supply-plan.medicine.add",
                        "operation.build.supply-plan.medicine.delete",
                        "operation.build.supply-plan.medicine.edit",
                        "operation.build.supply-plan.medicine.view",
                        "operation.build.supply-plan.medicine.history",
                        "operation.build.supply-plan.add",
                        "operation.build.supply-plan.delete",
                        "operation.build.supply-plan.edit",
                        "operation.build.supply-plan.view",
                        "operation.build.supply-plan.history",
                        "operation.build.history.view",
                        "operation.build.history.print",
                        "operation.build.settings.notice.view",
                        "operation.build.settings.notice.edit",
                        "operation.build.settings.user.download",
                        "operation.build.randomization.info.view",
                        "operation.build.randomization.info.export",
                        "operation.build.site.supply-plan.edit",
                        "operation.build.site.supply-plan.view",
                        "operation.build.site.edit",
                        "operation.supply.shipment.approval.view",
                        "operation.supply.shipment.approval.print",
                        "operation.supply.shipment.logistics.view",
                        "operation.supply.shipment.logistics.edit",
                        "operation.monitor.view",
                        "operation.monitor.edit",
                        "operation.supply.freeze.list",
                        "operation.supply.freeze.release",
                        "operation.supply.freeze.delete",
                        "operation.supply.freeze.approval",
                        "operation.supply.shipment.create",
                        "operation.supply.shipment.cancel",
                        "operation.supply.shipment.send",
                        "operation.supply.shipment.lose",
                        "operation.supply.shipment.list",
                        "operation.supply.shipment.receive",
                        "operation.supply.shipment.alarm",
                        "operation.supply.shipment.confirm",
                        "operation.supply.shipment.approval",
                        "operation.supply.shipment.close",
                        "operation.supply.shipment.terminated",
                        "operation.supply.site.medicine.summary",
                        "operation.supply.recovery.list",
                        "operation.supply.recovery.add",
                        "operation.supply.recovery.receive",
                        "operation.supply.recovery.confirm",
                        "operation.supply.recovery.cancel",
                        "operation.supply.recovery.lose",
                        "operation.supply.recovery.reason",
                        "operation.supply.recovery.determine",
                        "operation.supply.recovery.close",
                        "operation.supply.recovery.end",
                        "operation.subject.medicine.trail.print",
                        "operation.subject.print",
                        "operation.build.randomization.form.edit",
                        "operation.build.randomization.form.preview",
                        "operation.supply.storehouse.medicine.print",
                        "operation.supply.site.medicine.print",
                        "operation.supply.shipment.print",
                        "operation.supply.recovery.print",
                        "operation.supply.freeze.print",
                        "operation.build.randomization.list.print",
                        "operation.build.medicine.upload.print",
                        "operation.supply.storehouse.medicine.history",
                        "operation.supply.site.medicine.history",
                        "operation.supply.site.no_number.history",
                        "operation.supply.site.no_number.print",
                        "operation.supply.shipment.history",
                        "operation.supply.recovery.history",
                        "operation.supply.freeze.history",
                        "operation.build.randomization.list.history",
                        "operation.build.medicine.upload.uploadHistory",
                        "operation.build.push.view",
                        "operation.build.push.all.send",
                        "operation.build.push.batch.send",
                        "operation.build.push.send",
                        "operation.build.push.details",
                        "operation.build.randomization.group.add",
                        "operation.build.randomization.group.delete",
                        "operation.build.randomization.group.edit",
                        "operation.build.randomization.group.view",
                        "operation.build.randomization.edc.mapping",
                        "operation.subject.medicine.export",
                        "operation.subject.medicine.export.template",
                        "operation.build.simulate-random.download",
                        "operation.build.simulate-random.pdf.download",
                        "operation.subject.download",
                        "operation.subject.download.template",
                        "operation.supply.shipment.download",
                        "operation.supply.shipment.download.template",
                        "operation.supply.recovery.download",
                        "operation.supply.recovery.download.template",
                        "operation.supply.site.medicine.download",
                        "operation.supply.site.medicine.download.template",
                        "operation.supply.storehouse.medicine.download",
                        "operation.supply.storehouse.medicine.download.template",
                        "operation.build.medicine.upload.downdata",
                        "operation.build.medicine.upload.downdata.template",
                        "operation.report.auditTrailExport.download",
                        "operation.report.auditTrailExport.build",
                        "operation.report.auditTrailExport.settings",
                        "operation.report.auditTrailExport.release-record",
                        "operation.report.auditTrailExport.order",
                        "operation.report.auditTrailExport.drug_recovery",
                        "operation.report.auditTrailExport.subject",
                        "operation.report.auditTrailExport.dispensing",
                        "operation.report.auditTrailExport.ip",
                        "operation.report.userLoginHistory.download",
                        "operation.report.userRoleAssignHistory.download",
                        "operation.report.siteIPStatisticsExport.download",
                        "operation.report.depotIPStatisticsExport.download",
                        "operation.report.randomizationStatisticsExport.download",
                        "operation.report.subjectStatisticsExport.download",
                        "operation.project.status.view",
                        "operation.project.task.view",
                        "operation.project.random.view",
                        "operation.project.random.download",
                        "operation.project.subject.view",
                        "operation.project.subject.download",
                        "operation.project.depot.IPStatistics.view",
                        "operation.project.depot.IPStatistics.download",
                        "operation.project.site.IPStatistics.view",
                        "operation.project.site.IPStatistics.download",
                        "operation.project.analysis.view",
                        "operation.project.dynamics.view",
                        "operation.supply.storehouse.no_number.history",
                        "operation.supply.storehouse.no_number.print",
                        "operation.supply.shipment.reason",
                        "operation.supply.shipment.contacts",
                        "operation.build.randomization.factor.add",
                        "operation.build.randomization.factor.view",
                        "operation.build.randomization.factor.delete",
                        "operation.build.randomization.factor.edit",
                        "operation.build.randomization.factor.set-toplimit",
                        "operation.build.randomization.list.sync",
                        "operation.build.randomization.list.segmentation.view",
                        "operation.build.randomization.list.segmentation.clear",
                        "operation.build.randomization.list.segmentation.site",
                        "operation.build.randomization.list.segmentation.factor",
                        "operation.build.randomization.list.segmentation.region",
                        "operation.build.randomization.list.segmentation.country",
                        "operation.build.medicine.visit.add",
                        "operation.build.medicine.visit.delete",
                        "operation.build.medicine.visit.edit",
                        "operation.build.medicine.visit.list",
                        "operation.build.medicine.visit.update",
                        "operation.build.medicine.visit.drag",
                        "operation.build.medicine.visit.copy",
                        "operation.build.medicine.visit.push",
                        "operation.build.medicine.upload.list",
                        "operation.build.medicine.upload.upload",
                        "operation.build.medicine.upload.delete",
                        "operation.build.medicine.package.setting",
                        "operation.build.settings.user.edit",
                        "operation.supply.drug_recovery.logistics.view",
                        "operation.supply.drug_recovery.logistics.edit",
                        "operation.build.medicine.barcode.view",
                        "operation.build.medicine.barcode.add",
                        "operation.build.medicine.barcode.scan",
                        "operation.build.medicine.barcode.export",
                        "operation.subject.unblinding-pv-view",
                        "operation.subject.unblinding-pv-application",
                        "operation.subject.unblinding-pv-approval",
                        "operation.subject.unblinding-pv-log",
                        "operation.subject.unblinding-pv-sms",
                        "operation.subject.unblinding-log",
                        "operation.subject.unblinding-sms",
                        "operation.subject.unblinding",
                        "operation.subject.unblinding-application",
                        "operation.subject.unblinding-approval",
                        "operation.build.settings.user.view",
                        "operation.build.settings.user.unbind",
                        "operation.build.settings.user.unbind.batch",
                        "operation.build.settings.users.invite-again",
                        "operation.build.settings.user.history",
                        "operation.build.settings.user.print",
                        "operation.build.settings.user.add",
                        "operation.build.settings.user.role",
                        "operation.build.settings.user.site",
                        "operation.build.settings.user.depot",
                        "operation.build.settings.user.app",
                        "operation.source.ip.upload.history.downdata",
                        "operation.subject.download-random",
                        "operation.subject.download-random.template",
                        "operation.projects.project.info.view",
                        "operation.projects.project.basic.information.view",
                        "operation.projects.project.basic.environment.view",
                        "operation.projects.project.business.functions.view",
                        "operation.projects.project.external.docking.view",
                        "operation.projects.project.custom.process.view",
                        "operation.projects.project.permissions.view",
                        "operation.build.settings.user.reauthorization",
                        "operation.build.randomization.factor-in.delete",
                        "operation.build.randomization.factor-in.add",
                        "operation.build.medicine.visit.setting.edit",
                        "operation.build.medicine.visit.setting.list",
                        "operation.supply.shipment.cancel-dtp",
                        "operation.supply.shipment.terminated-dtp",
                        "operation.supply.shipment.close-dtp",
                        "operation.supply.shipment.confirm-dtp",
                        "operation.build.push.history",
                        "operation.subject.invalid-list"
                    ]
                },
                {
                    "id": "67480dd6bbbcf65e22861651",
                    "roleId": "630c5af2ab0f27e20ac84767",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "projectId": "67480dd6bbbcf65e2286164a",
                    "name": "PI [blinded,can unblind]",
                    "scope": "site",
                    "description": "111",
                    "template": 1,
                    "status": 1,
                    "permissions": [
                        "operation.project.status.view",
                        "operation.project.task.view",
                        "operation.project.dynamics.view",
                        "operation.subject.medicine.export",
                        "operation.subject.medicine.export.template",
                        "operation.supply.site.medicine.summary",
                        "operation.supply.site.medicine.singe",
                        "operation.supply.site.medicine.use",
                        "operation.supply.site.medicine.freeze",
                        "operation.supply.site.medicine.lost",
                        "operation.supply.site.no_number.view",
                        "operation.supply.site.no_number.freeze",
                        "operation.supply.site.no_number.lost",
                        "operation.supply.site.medicine.history",
                        "operation.supply.site.medicine.print",
                        "operation.supply.site.no_number.history",
                        "operation.supply.site.no_number.print",
                        "operation.supply.shipment.approval.view",
                        "operation.supply.shipment.approval.print",
                        "operation.supply.shipment.logistics.view",
                        "operation.supply.shipment.logistics.edit",
                        "operation.supply.shipment.contacts",
                        "operation.supply.shipment.reason",
                        "operation.supply.shipment.history",
                        "operation.supply.shipment.print",
                        "operation.supply.recovery.list",
                        "operation.supply.recovery.add",
                        "operation.supply.recovery.receive",
                        "operation.supply.recovery.confirm",
                        "operation.supply.recovery.cancel",
                        "operation.supply.recovery.lose",
                        "operation.supply.recovery.reason",
                        "operation.supply.recovery.determine",
                        "operation.supply.recovery.close",
                        "operation.supply.recovery.end",
                        "operation.supply.recovery.history",
                        "operation.supply.recovery.print",
                        "operation.build.site.supply-plan.view",
                        "operation.build.site.supply-plan.edit",
                        "operation.build.site.view",
                        "operation.build.site.edit",
                        "operation.build.site.add",
                        "operation.build.site.dispensing",
                        "operation.build.attribute.view",
                        "operation.build.code-rule.view",
                        "operation.build.code-rule.edit",
                        "operation.build.medicine.visit.push.record",
                        "operation.build.medicine.visit.list",
                        "operation.build.medicine.configuration.list",
                        "operation.build.settings.user.view",
                        "operation.build.settings.user.edit",
                        "operation.build.settings.user.unbind",
                        "operation.build.settings.user.unbind.batch",
                        "operation.build.settings.user.history",
                        "operation.build.settings.user.print",
                        "operation.build.settings.users.invite-again",
                        "operation.build.randomization.info.view",
                        "operation.subject.view-list",
                        "operation.subject.random",
                        "operation.subject.replace",
                        "operation.subject.unblinding-pv",
                        "operation.subject.registered",
                        "operation.subject.update",
                        "operation.subject.delete",
                        "operation.subject.secede",
                        "operation.subject.secede-registered",
                        "operation.subject.cohort.status",
                        "operation.subject.unblinding-log",
                        "operation.subject.unblinding-sms",
                        "operation.subject.unblinding-pv-view",
                        "operation.subject.unblinding-pv-application",
                        "operation.subject.unblinding-pv-approval",
                        "operation.subject.unblinding-pv-log",
                        "operation.subject.unblinding-pv-sms",
                        "operation.subject.unblinding",
                        "operation.subject.unblinding-application",
                        "operation.subject.unblinding-approval",
                        "operation.build.settings.user.add",
                        "operation.build.settings.user.role",
                        "operation.build.settings.user.site",
                        "operation.build.settings.user.depot",
                        "operation.build.settings.user.app",
                        "operation.build.settings.user.reauthorization",
                        "operation.build.settings.user.add",
                        "operation.build.settings.user.role",
                        "operation.build.settings.user.site",
                        "operation.build.settings.user.depot",
                        "operation.build.settings.user.app",
                        "operation.build.settings.user.reauthorization"
                    ]
                },
                {
                    "id": "67480dd6bbbcf65e22861653",
                    "roleId": "630c5cecab0f27e20ac84777",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "projectId": "67480dd6bbbcf65e2286164a",
                    "name": "Project Manager [blinded]",
                    "scope": "study",
                    "description": "",
                    "template": 1,
                    "status": 1,
                    "permissions": [
                        "operation.project.status.view",
                        "operation.project.task.view",
                        "operation.project.dynamics.view"
                    ]
                },
                {
                    "id": "67480dd6bbbcf65e2286164e",
                    "roleId": "6273689d01fcea2d8dedb69a",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "projectId": "67480dd6bbbcf65e2286164a",
                    "name": "Project-Admin",
                    "scope": "study",
                    "description": "-",
                    "template": 1,
                    "status": 1,
                    "permissions": [
                        "operation.settings.storehouse.view",
                        "operation.settings.storehouse.edit",
                        "operation.settings.storehouse.delete",
                        "operation.settings.storehouse.add",
                        "operation.projects.main.setting.docking.edit",
                        "operation.projects.main.setting.docking.view",
                        "operation.projects.main.setting.view",
                        "operation.projects.main.setting.custom.view",
                        "operation.projects.main.setting.custom.edit",
                        "operation.projects.main.setting.project.view",
                        "operation.projects.main.setting.project.edit",
                        "operation.build.settings.user.edit",
                        "operation.projects.main.setting.permission.add",
                        "operation.projects.main.setting.permission.edit",
                        "operation.projects.main.setting.permission.setting",
                        "operation.projects.main.setting.permission.export",
                        "operation.projects.main.setting.permission.view",
                        "menu.projects.project.settings.user",
                        "operation.build.settings.user.download",
                        "operation.project.status.view",
                        "operation.project.task.view",
                        "operation.project.dynamics.view",
                        "operation.projects.main.view",
                        "operation.settings.users.view",
                        "operation.settings.users.add",
                        "operation.settings.users.edit",
                        "operation.build.settings.user.view",
                        "operation.build.settings.user.add",
                        "operation.build.settings.user.role",
                        "operation.build.settings.user.site",
                        "operation.build.settings.user.depot",
                        "operation.build.settings.user.app",
                        "operation.build.settings.user.unbind",
                        "operation.build.settings.user.unbind.batch",
                        "operation.build.settings.users.invite-again",
                        "operation.build.settings.user.reauthorization",
                        "operation.build.settings.user.history",
                        "operation.build.settings.user.print",
                        "operation.build.settings.notice.view",
                        "operation.build.settings.notice.edit",
                        "operation.build.randomization.info.view",
                        "operation.projects.main.setting.notice.view",
                        "operation.projects.main.setting.notice.add",
                        "operation.projects.main.setting.notice.delete",
                        "operation.projects.main.setting.notice.edit",
                        "operation.projects.main.setting.base.view",
                        "operation.projects.main.setting.base.edit",
                        "operation.projects.main.setting.base.modify",
                        "operation.projects.main.config.view",
                        "operation.projects.main.config.create",
                        "operation.projects.main.config.copy",
                        "operation.projects.main.config.unlock",
                        "operation.projects.main.config.lock",
                        "operation.projects.main.config.add",
                        "operation.projects.main.config.edit",
                        "operation.projects.main.config.delete",
                        "operation.projects.main.config.copy_cohort",
                        "operation.projects.main.config.edit_env",
                        "operation.projects.main.setting.function.view",
                        "operation.projects.main.setting.function.edit",
                        "operation.projects.main.setting.function.admin",
                        "operation.projects.project.multiLanguage.view",
                        "operation.projects.project.multiLanguage.add",
                        "operation.projects.project.multiLanguage.edit",
                        "operation.projects.project.multiLanguage.delete",
                        "operation.projects.project.multiLanguage.trail",
                        "operation.projects.project.multiLanguage.details.view",
                        "operation.projects.project.multiLanguage.details.edit",
                        "operation.projects.project.multiLanguage.details.preview",
                        "operation.projects.project.multiLanguage.details.downloadTemplate",
                        "operation.projects.project.multiLanguage.details.batchExport"
                    ]
                },
                {
                    "id": "67480dd6bbbcf65e22861652",
                    "roleId": "630c5babab0f27e20ac84774",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "projectId": "67480dd6bbbcf65e2286164a",
                    "name": "Sponsor[blinded]",
                    "scope": "study",
                    "description": "",
                    "template": 1,
                    "status": 1,
                    "permissions": [
                        "operation.project.status.view",
                        "operation.project.task.view",
                        "operation.project.dynamics.view"
                    ]
                },
                {
                    "id": "67480dd6bbbcf65e2286164b",
                    "roleId": "6184f02911c3966e5754f567",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "projectId": "67480dd6bbbcf65e2286164a",
                    "name": "Sub-I [unblinded]",
                    "scope": "site",
                    "description": "",
                    "template": 1,
                    "status": 1,
                    "permissions": [
                        "operation.projects.main.setting.base.view",
                        "operation.projects.main.setting.base.edit",
                        "operation.subject.view-list",
                        "operation.subject.random",
                        "operation.subject.replace",
                        "operation.subject.trail",
                        "operation.subject.unblinding-pv",
                        "operation.subject.medicine.view-dispensing",
                        "operation.subject.medicine.trail",
                        "operation.subject.medicine.dispensing",
                        "operation.subject.medicine.reissue",
                        "operation.subject.medicine.replace",
                        "operation.subject.download",
                        "operation.subject.medicine.export",
                        "operation.subject.download-random",
                        "operation.subject.registered",
                        "operation.subject.update",
                        "operation.subject.delete",
                        "operation.subject.medicine.room",
                        "operation.subject.medicine.retrieval",
                        "operation.subject.medicine.out-visit-dispensing",
                        "operation.subject.medicine.invalid",
                        "operation.subject.medicine.room-download",
                        "operation.subject.medicine.register",
                        "operation.subject.secede",
                        "operation.projects.home.view",
                        "operation.build.randomization.type.view",
                        "operation.build.randomization.group.delete",
                        "operation.build.randomization.group.edit",
                        "operation.build.randomization.group.view",
                        "operation.build.randomization.factor.add",
                        "operation.build.randomization.factor.view",
                        "operation.build.randomization.factor.delete",
                        "operation.build.randomization.factor.edit",
                        "operation.build.randomization.factor.set-toplimit",
                        "operation.build.randomization.list.view-summary",
                        "operation.build.randomization.list.upload",
                        "operation.build.randomization.list.generate",
                        "operation.build.randomization.list.active",
                        "operation.build.randomization.list.export",
                        "operation.build.randomization.list.invalid",
                        "operation.build.randomization.list.attribute",
                        "operation.build.randomization.list.segmentation.view",
                        "operation.build.randomization.list.segmentation.clear",
                        "operation.build.randomization.list.segmentation.site",
                        "operation.build.randomization.list.segmentation.factor",
                        "operation.build.randomization.factor-in.view",
                        "operation.build.randomization.factor-in.set-people",
                        "operation.subject.unblinding",
                        "operation.subject.unblinding-application",
                        "operation.subject.medicine.trail.print",
                        "operation.subject.print",
                        "operation.build.randomization.list.print",
                        "operation.build.randomization.list.history",
                        "operation.project.status.view",
                        "operation.project.task.view",
                        "operation.project.dynamics.view",
                        "operation.build.randomization.factor-in.delete",
                        "operation.build.randomization.factor-in.add"
                    ]
                },
                {
                    "id": "67480dd6bbbcf65e22861654",
                    "roleId": "665011a340e96a704e77d8e9",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "projectId": "67480dd6bbbcf65e2286164a",
                    "name": "Supply Manager [blinded]",
                    "scope": "study",
                    "description": "",
                    "template": 1,
                    "status": 1,
                    "permissions": []
                }
            ]
        }
    },

]