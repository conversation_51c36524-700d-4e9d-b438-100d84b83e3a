import dayjs from "dayjs";

export const projectMockData = [
    {
        "method": "GET",
        "path": "randomization/attributes",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_id_001",
                    "customerId": "mock_customer_001",
                    "projectId": "mock_project_001",
                    "envId": "mock_env_001",
                    "cohortId": "000000000000000000000000",
                    "info": {
                        "random": true,
                        "randomControl": false,
                        "randomControlRule": 0,
                        "randomControlGroup": 0,
                        "isRandomNumber": true,
                        "isRandomSequenceNumber": true,
                        "randomSequenceNumberPrefix": "xxx",
                        "randomSequenceNumberDigit": 1,
                        "randomSequenceNumberStart": 2,
                        "dispensing": true,
                        "dtpRule": 1,
                        "blind": true,
                        "countryLayered": false,
                        "instituteLayered": true,
                        "regionLayered": false,
                        "prefix": true,
                        "subjectNumberRule": 1,
                        "prefixExpression": "",
                        "subjectReplaceText": "受试者编号",
                        "subjectReplaceTextEn": "Subject Number",
                        "digit": 7,
                        "accuracy": 1,
                        "field": {
                            "id": "mock_field_001",
                            "cohortId": "000000000000000000000000",
                            "cohortName": "",
                            "isCalc": false,
                            "calcType": null,
                            "name": "shortname",
                            "label": "受试者编号",
                            "labelNation": null,
                            "labelEn": "Subject Number",
                            "customFormulas": "",
                            "precision": null,
                            "round": 0,
                            "type": "input",
                            "status": null,
                            "applicationType": null,
                            "variable": "",
                            "used": false,
                            "options": null,
                            "list": false,
                            "modifiable": true,
                            "required": false,
                            "stratification": false,
                            "digit": 7,
                            "accuracy": 1,
                            "dateFormat": "",
                            "formatType": "",
                            "timeFormat": "",
                            "length": null,
                            "range": null,
                            "dateRange": null,
                            "isCustomFormula": false
                        },
                        "prefixSymbol": "",
                        "isFreeze": false,
                        "isRandom": false,
                        "isCountryRandom": false,
                        "isRegionRandom": false,
                        "edcDrugConfigLabel": "",
                        "segment": true,
                        "segmentType": 0,
                        "unblindingReasonConfig": [
                            {
                                "reason": "SAE",
                                "allowRemark": false
                            },
                            {
                                "reason": "妊娠",
                                "allowRemark": false
                            },
                            {
                                "reason": "政策要求",
                                "allowRemark": false
                            }
                        ],
                        "freezeReasonConfig": null,
                        "blindingRestrictions": true,
                        "pvUnBlindingRestrictions": true,
                        "replaceRule": 0,
                        "replaceRuleNumber": 0,
                        "isScreen": false,
                        "connectAli": false,
                        "aliProjectNo": "",
                        "allowReplace": true,
                        "allowRegisterGroup": true,
                        "minimizeCalc": 0,
                        "IPInheritance": true,
                        "remainingVisit": 111,
                        "codeRule": 0,
                        "codeConfigInit": false
                    }
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/projects",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "id": "mock_project_001",
                "customerId": "mock_customer_001",
                "info": {
                    "type": 1,
                    "number": "DEMO001",
                    "name": "示例项目",
                    "sponsor": "示例申办方",
                    "phone": "",
                    "tz": "",
                    "timeZoneStr": "8",
                    "timeZone": 8,
                    "connectEdc": 2,
                    "edcSupplier": 1,
                    "pushMode": 1,
                    "synchronizationMode": 1,
                    "edcUrl": "",
                    "pushTypeEdc": "",
                    "pushRules": 0,
                    "pushScenario": {
                        "registerPush": false,
                        "updateRandomFrontPush": false,
                        "updateRandomAfterPush": false,
                        "randomPush": false,
                        "randomBlockPush": false,
                        "formRandomBlockPush": false,
                        "cohortRandomBlockPush": false,
                        "dispensingPush": false,
                        "screenPush": false
                    },
                    "visitRandomization": {
                        "visits": null,
                        "randomizations": null
                    },
                    "connectAli": 0,
                    "bound": 0,
                    "aliProjectId": "",
                    "description": "",
                    "systemCourses": 1,
                    "needSystemCourses": 0,
                    "connectLearning": 1,
                    "needLearning": 2,
                    "needLearningEnv": [
                        "PROD"
                    ],
                    "orderCheck": 1,
                    "orderCheckDay": [
                        1,
                        2,
                        3
                    ],
                    "orderCheckTime": "08:00",
                    "orderConfirmation": 1,
                    "deIsolationApproval": 1,
                    "researchAttribute": 0,
                    "unblindingControl": 1,
                    "unblindingType": 1,
                    "unblindingSms": 0,
                    "unblindingProcess": 1,
                    "unblindingCode": 0,
                    "pvUnblindingType": 1,
                    "pvUnblindingSms": 0,
                    "pvUnblindingProcess": 1,
                    "ipUnblindingType": 1,
                    "ipUnblindingSms": 0,
                    "ipUnblindingProcess": 1,
                    "orderApprovalControl": 1,
                    "orderApprovalSms": 0,
                    "orderApprovalProcess": 1,
                    "room": true,
                    "statusReason": ""
                },
                "envs": [
                    {
                        "id": "mock_001",
                        "name": "DEV",
                        "lockConfig": false,
                        "capacity": null,
                        "reminderThresholds": null,
                        "alertThresholds": null,
                        "status": 2,
                        "cohorts": [],
                        "isCopy": false
                    },
                    {
                        "id": "mock_002",
                        "name": "05081",
                        "lockConfig": false,
                        "capacity": null,
                        "reminderThresholds": null,
                        "alertThresholds": null,
                        "status": 1,
                        "cohorts": [],
                        "isCopy": false
                    }
                ],
                "administrators": [
                    "mock_003",
                    "mock_004",
                    "mock_005"
                ],
                "status": 0,
                "meta": {
                    "createdAt": 0,
                    "createdBy": "000000000000000000000000",
                    "updatedAt": 0,
                    "updatedBy": "000000000000000000000000",
                    "deletedAt": 0,
                    "deletedBy": "000000000000000000000000"
                }
            }
        }
    },
    {
        "method": "GET",
        "path": "/projects-sites/user-sites",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_006",
                    "name": "示例医院A",
                    "nameEn": "示例医院A",
                    "number": "001",
                    "time_zone": "UTC+8"
                },
                {
                    "id": "mock_007",
                    "name": "示例医院B",
                    "nameEn": "示例医院B",
                    "number": "002",
                    "time_zone": "UTC+8"
                }
            ]
        }
    },
    {
        "method": "POST",
        "path": "/subject",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "blind": true,
                "fields": [
                    {
                        "id": "000000000000000000000000",
                        "label": "受试者编号",
                        "labelEn": "受试者编号",
                        "name": "shortname",
                        "status": null,
                        "type": "",
                        "options": null,
                        "invalidDisplay": "",
                        "isCalc": false,
                        "calcType": null,
                        "dateFormat": "",
                        "timeFormat": ""
                    },
                ],
                "haveJustDispensing": false,
                "haveJustRandom": true,
                "isRandomNumber": true,
                "isRandomSequenceNumber": false,
                "items": [1, 3, 4, 6, 7, 8, 9, 10, 11].map((status, index) => ({
                    "id": `mock_00${index}`,
                    "customerId": "mock_009",
                    "projectId": "mock_010",
                    "envId": "mock_001",
                    "cohortId": "000000000000000000000000",
                    "projectSiteID": "mock_006",
                    "randomListId": "mock_011",
                    "registerRandomListId": "000000000000000000000000",
                    "randomNumber": `A${(index + 1).toString().padStart(4, "0")}`,
                    "lastGroup": "",
                    "group": "实验组",
                    "parGroupName": "实验组",
                    "subGroupName": "",
                    "subNameBlind": false,
                    "info": [
                        {
                            "name": "shortname",
                            "value": (index + 1).toString().padStart(3, "0"),
                            "label": ""
                        },
                        {
                            "name": "field2",
                            "value": 1,
                            "label": "1"
                        }
                    ],
                    "status": status,
                    "pvUnblindingStatus": 0,
                    "urgentUnblindingStatus": 0,
                    "sign": true,
                    "dispensingSign": true,
                    "replaceSign": false,
                    "roomNumber": "",
                    "meta": {},
                    "urgentUnblindingApprovals": [],
                    "pvUrgentUnblindingApprovals": [],
                    "hasDispensing": true,
                    "isDispensing": true,
                    "randomTime": 1694318296,
                    "joinTime": "2023-09-10 11:58:16(UTC+08:00)",
                    "joinTimeForTime": 0,
                    "reRandomJoinTime": null,
                    "siteName": "示例医院A",
                    "siteNumber": "001",
                    "cohortName": "",
                    "cohort": {},
                    "timeZone": 8,
                    "tz": "Asia/Shanghai",
                    "attribute": {
                        "id": "mock_012",
                        "customerId": "mock_009",
                        "projectId": "mock_010",
                        "envId": "mock_001",
                        "cohortId": "000000000000000000000000",
                        "info": {
                            "random": true,
                            "randomControl": false,
                            "randomControlRule": 0,
                            "randomControlGroup": 0,
                            "isRandomNumber": true,
                            "isRandomSequenceNumber": false,
                            "randomSequenceNumberPrefix": "",
                            "randomSequenceNumberDigit": 0,
                            "randomSequenceNumberStart": 0,
                            "dispensing": true,
                            "dtpRule": 1,
                            "blind": true,
                            "countryLayered": false,
                            "instituteLayered": true,
                            "regionLayered": false,
                            "prefix": false,
                            "subjectNumberRule": 1,
                            "prefixExpression": "",
                            "subjectReplaceText": "受试者编号",
                            "subjectReplaceTextEn": "",
                            "digit": 7,
                            "accuracy": 1,
                            "field": {
                                "id": "mock_013",
                                "cohortId": "000000000000000000000000",
                                "cohortName": "",
                                "isCalc": false,
                                "calcType": null,
                                "name": "shortname",
                                "label": "受试者编号",
                                "labelNation": null,
                                "labelEn": "",
                                "customFormulas": "",
                                "precision": null,
                                "round": 0,
                                "type": "input",
                                "status": null,
                                "applicationType": null,
                                "variable": "",
                                "used": false,
                                "options": null,
                                "list": false,
                                "modifiable": true,
                                "required": false,
                                "stratification": false,
                                "digit": 7,
                                "accuracy": 1,
                                "dateFormat": "",
                                "formatType": "",
                                "timeFormat": "",
                                "length": null,
                                "range": null,
                                "dateRange": null,
                                "isCustomFormula": false
                            },
                            "prefixSymbol": "",
                            "isFreeze": false,
                            "isRandom": false,
                            "isCountryRandom": false,
                            "isRegionRandom": false,
                            "edcDrugConfigLabel": "",
                            "segment": true,
                            "segmentType": 0,
                            "unblindingReasonConfig": [],
                            "freezeReasonConfig": null,
                            "blindingRestrictions": true,
                            "pvUnBlindingRestrictions": true,
                            "replaceRule": 0,
                            "replaceRuleNumber": 0,
                            "isScreen": false,
                            "connectAli": false,
                            "aliProjectNo": "",
                            "allowReplace": true,
                            "allowRegisterGroup": true,
                            "minimizeCalc": 0,
                            "IPInheritance": true,
                            "remainingVisit": 111,
                            "codeRule": 0,
                            "codeConfigInit": false
                        }
                    },
                    "factorSign": false,
                    "form": {
                        "id": "mock_014",
                        "customerId": "mock_009",
                        "projectId": "mock_010",
                        "envId": "mock_001",
                        "cohortId": "000000000000000000000000",
                        "fields": [
                            {
                                "id": "mock_013",
                                "cohortId": "000000000000000000000000",
                                "cohortName": "",
                                "isCalc": false,
                                "calcType": null,
                                "name": "shortname",
                                "label": "受试者编号",
                                "labelNation": null,
                                "labelEn": "",
                                "customFormulas": "",
                                "precision": null,
                                "round": 0,
                                "type": "input",
                                "status": null,
                                "applicationType": null,
                                "variable": "",
                                "used": false,
                                "options": null,
                                "list": false,
                                "modifiable": true,
                                "required": false,
                                "stratification": false,
                                "digit": 7,
                                "accuracy": 1,
                                "dateFormat": "",
                                "formatType": "",
                                "timeFormat": "",
                                "length": null,
                                "range": null,
                                "dateRange": null,
                                "isCustomFormula": false
                            },
                            {
                                "id": "mock_015",
                                "cohortId": "000000000000000000000000",
                                "cohortName": "",
                                "isCalc": false,
                                "calcType": null,
                                "name": "field2",
                                "label": "小数测试",
                                "labelNation": null,
                                "labelEn": "",
                                "customFormulas": "",
                                "precision": null,
                                "round": 0,
                                "type": "inputNumber",
                                "status": 1,
                                "applicationType": 1,
                                "variable": "小数测试",
                                "used": false,
                                "options": null,
                                "list": false,
                                "modifiable": true,
                                "required": false,
                                "stratification": false,
                                "digit": 0,
                                "accuracy": 0,
                                "dateFormat": "",
                                "formatType": "decimalLength",
                                "timeFormat": "",
                                "length": 0,
                                "range": null,
                                "dateRange": null,
                                "isCustomFormula": false
                            }
                        ]
                    },
                    "unblindingApprovalUser": [],
                    "pvUnblindingApprovalUser": [],
                    "atRandom": {
                        "setButton": false,
                        "currentStage": "",
                        "nextStage": ""
                    },
                    "finishTime": 0,
                    "cohortStatus": 0,
                    "randomSequenceNumber": "",
                    "shortname": "001",
                    "受试者受试者受试者受试者受试者受试者受试者受试者受试者受试者": "001"
                })),
                "reRandomForm": [],
                "total": 9
            }
        }
    },
    {
        "method": "GET",
        "path": "randomization/attribute",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "id": "mock_012",
                "customerId": "mock_009",
                "projectId": "mock_010",
                "envId": "mock_001",
                "cohortId": "000000000000000000000000",
                "info": {
                    "random": true,
                    "randomControl": true,
                    "randomControlRule": 3,
                    "randomControlGroup": 0,
                    "isRandomNumber": true,
                    "isRandomSequenceNumber": true,
                    "randomSequenceNumberPrefix": "xxx",
                    "randomSequenceNumberDigit": 1,
                    "randomSequenceNumberStart": 2,
                    "dispensing": true,
                    "dtpRule": 1,
                    "blind": true,
                    "countryLayered": false,
                    "instituteLayered": true,
                    "regionLayered": false,
                    "prefix": true,
                    "subjectNumberRule": 1,
                    "prefixExpression": "{siteNO}",
                    "subjectReplaceText": "受试者编号",
                    "subjectReplaceTextEn": "siteNO",
                    "digit": 7,
                    "accuracy": 1,
                    "field": {
                        "id": "mock_013",
                        "cohortId": "000000000000000000000000",
                        "cohortName": "",
                        "isCalc": false,
                        "calcType": null,
                        "name": "shortname",
                        "label": "受试者编号",
                        "labelNation": null,
                        "labelEn": "",
                        "customFormulas": "",
                        "precision": null,
                        "round": 0,
                        "type": "input",
                        "status": null,
                        "applicationType": null,
                        "variable": "",
                        "used": false,
                        "options": null,
                        "list": false,
                        "modifiable": true,
                        "required": false,
                        "stratification": false,
                        "digit": 7,
                        "accuracy": 1,
                        "dateFormat": "",
                        "formatType": "",
                        "timeFormat": "",
                        "length": null,
                        "range": null,
                        "dateRange": null,
                        "isCustomFormula": false
                    },
                    "prefixSymbol": "",
                    "isFreeze": true,
                    "isRandom": false,
                    "isCountryRandom": false,
                    "isRegionRandom": false,
                    "edcDrugConfigLabel": "",
                    "segment": true,
                    "segmentType": 0,
                    "unblindingReasonConfig": [
                        {
                            "reason": "SAE",
                            "allowRemark": false
                        },
                        {
                            "reason": "妊娠",
                            "allowRemark": false
                        },
                        {
                            "reason": "政策要求",
                            "allowRemark": false
                        }
                    ],
                    "freezeReasonConfig": null,
                    "blindingRestrictions": true,
                    "pvUnBlindingRestrictions": true,
                    "replaceRule": 1,
                    "replaceRuleNumber": 0,
                    "isScreen": false,
                    "connectAli": true,
                    "aliProjectNo": "",
                    "allowReplace": true,
                    "allowRegisterGroup": true,
                    "minimizeCalc": 1,
                    "IPInheritance": true,
                    "remainingVisit": 10,
                    "codeRule": 0,
                    "codeConfigInit": false,
                }
            }
        }
    },
    {
        "method": "GET",
        "path": "randomization/cohort/random",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_021",
                    "lastId": "000000000000000000000000",
                    "type": 0,
                    "name": "第一阶段",
                    "capacity": 0,
                    "reminderThresholds": null,
                    "alertThresholds": [],
                    "factor": "",
                    "status": 2,
                    "copyFrom": "000000000000000000000000"
                },
                {
                    "id": "mock_022",
                    "lastId": "mock_021",
                    "type": 0,
                    "name": "第二阶段",
                    "capacity": 0,
                    "reminderThresholds": null,
                    "alertThresholds": null,
                    "factor": "",
                    "status": 1,
                    "copyFrom": "000000000000000000000000"
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "randomization/random-statistics/bar",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "current": [
                    {
                        "name": "2023.09",
                        "value": 1
                    },
                    {
                        "name": "2023.12",
                        "value": 2
                    },
                    {
                        "name": "2024.01",
                        "value": 7
                    }
                ],
                "cumulative": [
                    {
                        "name": "2023.09",
                        "value": 1
                    },
                    {
                        "name": "2023.12",
                        "value": 3
                    },
                    {
                        "name": "2024.01",
                        "value": 10
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "randomization/attribute/connect",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "id": "mock_012",
                "customerId": "mock_009",
                "projectId": "mock_010",
                "envId": "mock_001",
                "cohortId": "000000000000000000000000",
                "info": {
                    "random": true,
                    "randomControl": false,
                    "randomControlRule": 0,
                    "randomControlGroup": 0,
                    "isRandomNumber": true,
                    "isRandomSequenceNumber": false,
                    "randomSequenceNumberPrefix": "",
                    "randomSequenceNumberDigit": 0,
                    "randomSequenceNumberStart": 0,
                    "dispensing": true,
                    "dtpRule": 1,
                    "blind": true,
                    "countryLayered": false,
                    "instituteLayered": true,
                    "regionLayered": false,
                    "prefix": true,
                    "subjectNumberRule": 1,
                    "prefixExpression": "",
                    "subjectReplaceText": "受试者编号",
                    "subjectReplaceTextEn": "",
                    "digit": 7,
                    "accuracy": 1,
                    "field": {
                        "id": "mock_013",
                        "cohortId": "000000000000000000000000",
                        "cohortName": "",
                        "isCalc": false,
                        "calcType": null,
                        "name": "shortname",
                        "label": "受试者编号",
                        "labelNation": null,
                        "labelEn": "",
                        "customFormulas": "",
                        "precision": null,
                        "round": 0,
                        "type": "input",
                        "status": null,
                        "applicationType": null,
                        "variable": "",
                        "used": false,
                        "options": null,
                        "list": false,
                        "modifiable": true,
                        "required": false,
                        "stratification": false,
                        "digit": 7,
                        "accuracy": 1,
                        "dateFormat": "",
                        "formatType": "",
                        "timeFormat": "",
                        "length": null,
                        "range": null,
                        "dateRange": null,
                        "isCustomFormula": false
                    },
                    "prefixSymbol": "",
                    "isFreeze": false,
                    "isRandom": false,
                    "isCountryRandom": false,
                    "isRegionRandom": false,
                    "edcDrugConfigLabel": "",
                    "segment": true,
                    "segmentType": 0,
                    "unblindingReasonConfig": [
                        {
                            "reason": "SAE",
                            "allowRemark": false
                        },
                        {
                            "reason": "妊娠",
                            "allowRemark": false
                        },
                        {
                            "reason": "政策要求",
                            "allowRemark": false
                        }
                    ],
                    "freezeReasonConfig": null,
                    "blindingRestrictions": true,
                    "pvUnBlindingRestrictions": true,
                    "replaceRule": 0,
                    "replaceRuleNumber": 0,
                    "isScreen": false,
                    "connectAli": false,
                    "aliProjectNo": "",
                    "allowReplace": true,
                    "allowRegisterGroup": true,
                    "minimizeCalc": 0,
                    "IPInheritance": true,
                    "remainingVisit": 111,
                    "codeRule": 0,
                    "codeConfigInit": false
                }
            }
        }
    },
    {
        "method": "GET",
        "path": "/projects-storehouses/user-storehouses",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_023",
                    "name": "仓库1",
                    "storehouseId": "mock_024"
                },
                {
                    "id": "mock_025",
                    "name": "仓库2",
                    "storehouseId": "mock_026"
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/projects/overview",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "projectStatus": 0,
                "siteCount": 2,
                "cohorts_cases": [
                    {
                        "actualCases": 35,
                        "is_random": false
                    }
                ],
                "projectTimeZone": 8,
                "startTime": dayjs().unix() - 86400 * 404,
                "endTime": dayjs().unix() + 86400 * 404,
            }
        }
    },
    {
        "method": "GET",
        "path": "/projects-sites/sites-and-storehouses",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "site": [
                    {
                        "deleted": 2,
                        "label": "001-示例医院A",
                        "value": "mock_006"
                    },
                    {
                        "deleted": 2,
                        "label": "002-示例医院B",
                        "value": "mock_007"
                    }
                ],
                "storehouse": [
                    {
                        "deleted": 2,
                        "label": "仓库1",
                        "value": "mock_023"
                    },
                    {
                        "deleted": 2,
                        "label": "仓库2",
                        "value": "mock_025"
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/project-dynamics",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "total": 2,
                "data": [
                    {
                        "id": "6837c90d61f10094dff6ccd6",
                        "typeTran": "project_dynamics_type_role_assignment",
                        "time": 1748486413,
                        "content": "【角色分配】<a><EMAIL></a>已分配Project-Admin、IP Officer角色",
                        "highlight": "<EMAIL>",
                        "tooltip": null
                    },
                    {
                        "id": "67edee6a9ed7efd701ef8648",
                        "typeTran": "project_dynamics_type_enter_site",
                        "time": 1743646314,
                        "content": "【示例医院A】<a><EMAIL></a>已进入中心",
                        "highlight": "<EMAIL>",
                        "tooltip": null
                    },
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/project-dynamics/analysis",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "orderCount": 244,
                "orderTimeOutCount": 10,
                "workCount": 28,
                "workTimeOutCount": 6,
                "todoTaskCount": 6
            }
        }
    },
    {
        "method": "GET",
        "path": "/medicines/all-medicine-site-stat",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "count": 12,
                        "destroy_count": 0,
                        "expired_count": 0,
                        "frozen_count": 0,
                        "id": {
                            "name": "A",
                            "spec": "mg"
                        },
                        "in_transit_count": 2,
                        "locked_count": 0,
                        "lost_count": 2,
                        "name": "A",
                        "quarantined_count": 0,
                        "receive_count": 0,
                        "return_count": 0,
                        "spec": "mg",
                        "to_be_confirm_count": 0,
                        "to_be_send_count": 0,
                        "used_count": 39
                    },
                    {
                        "count": 33,
                        "destroy_count": 0,
                        "expired_count": 0,
                        "frozen_count": 1,
                        "id": {
                            "name": "B",
                            "spec": "mg"
                        },
                        "in_transit_count": 2,
                        "locked_count": 0,
                        "lost_count": 2,
                        "name": "B",
                        "quarantined_count": 0,
                        "receive_count": 0,
                        "return_count": 0,
                        "spec": "mg",
                        "to_be_confirm_count": 0,
                        "to_be_send_count": 0,
                        "used_count": 17
                    },
                    {
                        "count": 94,
                        "destroy_count": 0,
                        "expired_count": 0,
                        "frozen_count": 22,
                        "id": {
                            "name": "体重药",
                            "spec": "盒"
                        },
                        "in_transit_count": 0,
                        "locked_count": 0,
                        "lost_count": 1,
                        "name": "体重药",
                        "quarantined_count": 5,
                        "receive_count": 0,
                        "return_count": 0,
                        "spec": "盒",
                        "to_be_confirm_count": 0,
                        "to_be_send_count": 0,
                        "used_count": 93
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/medicines/all-medicine-storehouse-stat",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "count": 0,
                        "expired_count": 71,
                        "frozen_count": 9,
                        "in_transit_count": 0,
                        "locked_count": 0,
                        "lost_count": 0,
                        "name": "体重药",
                        "quarantined_count": 0,
                        "spec": "盒",
                        "to_be_confirm_count": 5,
                        "to_be_send_count": 0,
                        "used_count": 0
                    },
                    {
                        "count": 249,
                        "expired_count": 0,
                        "frozen_count": 11,
                        "in_transit_count": 0,
                        "locked_count": 0,
                        "lost_count": 3,
                        "name": "实验药",
                        "quarantined_count": 0,
                        "spec": "mg",
                        "to_be_confirm_count": 3,
                        "to_be_send_count": 0,
                        "used_count": 0
                    },
                    {
                        "count": 119,
                        "expired_count": 0,
                        "frozen_count": 2,
                        "in_transit_count": 0,
                        "locked_count": 0,
                        "lost_count": 0,
                        "name": "对照药",
                        "quarantined_count": 3,
                        "spec": "盒",
                        "to_be_confirm_count": 0,
                        "to_be_send_count": 0,
                        "used_count": 0
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "randomization/random-statistics/pie",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "total": 35,
                "array": []
            }
        }
    },
    {
        "method": "GET",
        "path": "randomization/random-statistics/pie",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "total": 35,
                "array": []
            }
        }
    },
    {
        "method": "GET",
        "path": "/approvalProcess/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "currentTotal": 2,
                "items": [
                    {
                        "id": "mock_027",
                        "number": "202505200001",
                        "name": "project.task.pv-unblinding.title",
                        "type": 3,
                        "status": 0,
                        "estimatedCompletionTime": 1747980204,
                        "applicationTime": 1747721004,
                    },
                    {
                        "id": "mock_028",
                        "number": "202505200002",
                        "name": "project.task.urgent-unblinding.title",
                        "type": 3,
                        "status": 0,
                        "estimatedCompletionTime": 1747980204,
                        "applicationTime": 1747721004,
                    }
                ],
                "packageIsOpen": true,
                "total": 2
            }
        }
    },
    {
        "method": "GET",
        "path": "/project-dynamics/subject-site-statistics",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "000000000000000000000000",
                    "number": "001",
                    "name": "示例医院A",
                    "subjectStatusCount": [
                        {
                            "status": 1,
                            "count": 33
                        },
                        {
                            "status": 3,
                            "count": 30
                        },
                        {
                            "status": 5,
                            "count": 1
                        },
                        {
                            "status": 6,
                            "count": 0
                        },
                        {
                            "status": 7,
                            "count": 30
                        },
                        {
                            "status": 8,
                            "count": 0
                        },
                        {
                            "status": 9,
                            "count": 0
                        },
                        {
                            "status": 11,
                            "count": 0
                        },
                        {
                            "status": 12,
                            "count": 0
                        }
                    ]
                },
                {
                    "id": "000000000000000000000000",
                    "number": "002",
                    "name": "示例医院B",
                    "subjectStatusCount": [
                        {
                            "status": 1,
                            "count": 5
                        },
                        {
                            "status": 3,
                            "count": 5
                        },
                        {
                            "status": 5,
                            "count": 0
                        },
                        {
                            "status": 6,
                            "count": 0
                        },
                        {
                            "status": 7,
                            "count": 5
                        },
                        {
                            "status": 8,
                            "count": 0
                        },
                        {
                            "status": 9,
                            "count": 0
                        },
                        {
                            "status": 11,
                            "count": 0
                        },
                        {
                            "status": 12,
                            "count": 0
                        }
                    ]
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/project-dynamics/subject-statistics",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "register": 38,
                "random": 35,
                "exit": 1,
                "unBlind": 0,
                "screenSuccess": 35,
                "screenFail": 0,
                "finish": 0,
                "toBeRandom": 0,
                "join": 0,
                "pvUnBlind": 0
            }
        }
    },
    {
        "method": "GET",
        "path": "/medicines/all-medicine-site-stat",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "count": 12,
                        "destroy_count": 0,
                        "expired_count": 0,
                        "frozen_count": 0,
                        "id": {
                            "name": "A",
                            "spec": "mg"
                        },
                        "in_transit_count": 0,
                        "locked_count": 0,
                        "lost_count": 2,
                        "name": "A",
                        "quarantined_count": 0,
                        "receive_count": 0,
                        "return_count": 0,
                        "spec": "mg",
                        "to_be_confirm_count": 0,
                        "to_be_send_count": 0,
                        "used_count": 39
                    },
                    {
                        "count": 33,
                        "destroy_count": 0,
                        "expired_count": 0,
                        "frozen_count": 1,
                        "id": {
                            "name": "B",
                            "spec": "mg"
                        },
                        "in_transit_count": 0,
                        "locked_count": 0,
                        "lost_count": 2,
                        "name": "B",
                        "quarantined_count": 0,
                        "receive_count": 0,
                        "return_count": 0,
                        "spec": "mg",
                        "to_be_confirm_count": 0,
                        "to_be_send_count": 0,
                        "used_count": 17
                    },
                    {
                        "count": 61,
                        "destroy_count": 0,
                        "expired_count": 0,
                        "frozen_count": 22,
                        "id": {
                            "name": "体重药",
                            "spec": "盒"
                        },
                        "in_transit_count": 0,
                        "locked_count": 0,
                        "lost_count": 0,
                        "name": "体重药",
                        "quarantined_count": 5,
                        "receive_count": 0,
                        "return_count": 0,
                        "spec": "盒",
                        "to_be_confirm_count": 0,
                        "to_be_send_count": 0,
                        "used_count": 87
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/visit",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "id": "mock_029",
                "customerId": "mock_009",
                "projectId": "mock_010",
                "envId": "mock_001",
                "cohortId": "000000000000000000000000",
                "setInfo": {
                    "isOpen": true,
                    "id": "mock_030",
                    "nameZh": "计划外自定义",
                    "nameEn": "outSizeVisit"
                },
                "visit_type": 0,
                "baseCohort": "000000000000000000000000",
                "version": "0416",
                "infos": [
                    {
                        "id": "mock_031",
                        "number": "V1",
                        "name": "V1",
                        "random": true,
                        "dispensing": true,
                        "dtp": false,
                        "replace": false,
                        "doseAdjustment": false,
                        "DTPType": null,
                        "group": ["**********",],
                        "interval": 30,
                        "unit": "w",
                        "PeriodMin": -2,
                        "periodMax": 1,
                        "startDays": 0,
                        "endDays": 0,
                        "isUsed": false,
                        "isCopyEditDelete": false
                    },
                    {
                        "id": "mock_032",
                        "number": "V2",
                        "name": "V2",
                        "random": false,
                        "dispensing": true,
                        "dtp": true,
                        "replace": false,
                        "doseAdjustment": true,
                        "DTPType": [2, 0],
                        "group": ["**********",],
                        "interval": 30,
                        "unit": "d",
                        "PeriodMin": -2,
                        "periodMax": 1,
                        "startDays": 0,
                        "endDays": 0,
                        "isUsed": false,
                        "isCopyEditDelete": false
                    },
                    {
                        "id": "mock_033",
                        "number": "V3",
                        "name": "V3",
                        "random": false,
                        "dispensing": true,
                        "dtp": true,
                        "replace": false,
                        "doseAdjustment": true,
                        "DTPType": [2, 1, 0],
                        "group": ["**********"],
                        "interval": 30,
                        "unit": "h",
                        "PeriodMin": -2,
                        "periodMax": 1,
                        "startDays": 0,
                        "endDays": 0,
                        "isUsed": false,
                        "isCopyEditDelete": false
                    },
                    {
                        "id": "mock_034",
                        "number": "V4",
                        "name": "V4",
                        "random": false,
                        "dispensing": true,
                        "dtp": true,
                        "replace": false,
                        "doseAdjustment": true,
                        "DTPType": [2, 1, 0],
                        "group": ["**********"],
                        "interval": 30,
                        "unit": "m",
                        "PeriodMin": -2,
                        "periodMax": 1,
                        "startDays": 0,
                        "endDays": 0,
                        "isUsed": false,
                        "isCopyEditDelete": false
                    }
                ],
                "update_infos": {
                    "visit_type": 0,
                    "baseCohort": "000000000000000000000000",
                    "version": "",
                    "infos": [
                        {
                            "id": "mock_031",
                            "number": "V1",
                            "name": "V1",
                            "random": true,
                            "dispensing": true,
                            "dtp": false,
                            "replace": false,
                            "doseAdjustment": false,
                            "DTPType": null,
                            "group": ["**********",],
                            "interval": 30,
                            "unit": "w",
                            "PeriodMin": -2,
                            "periodMax": 1,
                            "startDays": 0,
                            "endDays": 0,
                            "isUsed": false,
                            "isCopyEditDelete": false
                        },
                        {
                            "id": "mock_032",
                            "number": "V2",
                            "name": "V2",
                            "random": false,
                            "dispensing": true,
                            "dtp": true,
                            "replace": false,
                            "doseAdjustment": true,
                            "DTPType": [2, 0],
                            "group": ["**********",],
                            "interval": 30,
                            "unit": "d",
                            "PeriodMin": -2,
                            "periodMax": 1,
                            "startDays": 0,
                            "endDays": 0,
                            "isUsed": false,
                            "isCopyEditDelete": false
                        },
                        {
                            "id": "mock_033",
                            "number": "V3",
                            "name": "V3",
                            "random": false,
                            "dispensing": true,
                            "dtp": true,
                            "replace": false,
                            "doseAdjustment": true,
                            "DTPType": [2, 1, 0],
                            "group": ["**********"],
                            "interval": 30,
                            "unit": "h",
                            "PeriodMin": -2,
                            "periodMax": 1,
                            "startDays": 0,
                            "endDays": 0,
                            "isUsed": false,
                            "isCopyEditDelete": false
                        },
                        {
                            "id": "mock_034",
                            "number": "V4",
                            "name": "V4",
                            "random": false,
                            "dispensing": true,
                            "dtp": true,
                            "replace": false,
                            "doseAdjustment": true,
                            "DTPType": [2, 1, 0],
                            "group": ["**********"],
                            "interval": 30,
                            "unit": "m",
                            "PeriodMin": -2,
                            "periodMax": 1,
                            "startDays": 0,
                            "endDays": 0,
                            "isUsed": false,
                            "isCopyEditDelete": false
                        }
                    ],
                    "createdAt": 1692151215,
                    "createdBy": "000000000000000000000000",
                    "updatedAt": 1744770717,
                    "updatedBy": "000000000000000000000000",
                    "deletedAt": 0,
                    "deletedBy": "000000000000000000000000"
                },
                "history_info": [
                    {
                        "visit_type": 0,
                        "baseCohort": "000000000000000000000000",
                        "version": "V1",
                        "infos": null,
                        "createdAt": 0,
                        "createdBy": "000000000000000000000000",
                        "updatedAt": 0,
                        "updatedBy": "000000000000000000000000",
                        "deletedAt": 0,
                        "deletedBy": "000000000000000000000000"
                    },
                    {
                        "visit_type": 0,
                        "baseCohort": "000000000000000000000000",
                        "version": "V2",
                        "infos": [
                            {
                                "id": "mock_031",
                                "number": "V1",
                                "name": "V1",
                                "random": false,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": [
                                    "**********"
                                ],
                                "interval": 1,
                                "unit": "d",
                                "PeriodMin": null,
                                "periodMax": null,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_032",
                                "number": "V2",
                                "name": "V2",
                                "random": false,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": [
                                    "**********"
                                ],
                                "interval": 1,
                                "unit": "d",
                                "PeriodMin": null,
                                "periodMax": null,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            }
                        ],
                        "createdAt": 1692152046,
                        "createdBy": "000000000000000000000000",
                        "updatedAt": 1694318103,
                        "updatedBy": "000000000000000000000000",
                        "deletedAt": 0,
                        "deletedBy": "000000000000000000000000"
                    },
                    {
                        "visit_type": 0,
                        "baseCohort": "000000000000000000000000",
                        "version": "V3",
                        "infos": [
                            {
                                "id": "mock_031",
                                "number": "V1",
                                "name": "V1",
                                "random": false,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": [
                                    "**********"
                                ],
                                "interval": 1,
                                "unit": "d",
                                "PeriodMin": null,
                                "periodMax": null,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_032",
                                "number": "V2",
                                "name": "V2",
                                "random": false,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": [
                                    "**********"
                                ],
                                "interval": 1,
                                "unit": "d",
                                "PeriodMin": null,
                                "periodMax": null,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_034",
                                "number": "V3",
                                "name": "V3",
                                "random": true,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": [
                                    "**********"
                                ],
                                "interval": null,
                                "unit": "d",
                                "PeriodMin": null,
                                "periodMax": null,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            }
                        ],
                        "createdAt": 1693618369,
                        "createdBy": "000000000000000000000000",
                        "updatedAt": 1694318247,
                        "updatedBy": "000000000000000000000000",
                        "deletedAt": 0,
                        "deletedBy": "000000000000000000000000"
                    }
                ],
                "createdAt": 1744770717,
                "createdBy": "000000000000000000000000",
                "updatedAt": 1744770729,
                "updatedBy": "000000000000000000000000",
                "deletedAt": 0,
                "deletedBy": "000000000000000000000000"
            }
        }
    },
    {
        "method": "GET",
        "path": "/dispensing/subject/status-room",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "id": "mock_008",
                "customerId": "mock_009",
                "projectId": "mock_010",
                "envId": "mock_001",
                "cohortId": "000000000000000000000000",
                "projectSiteID": "mock_006",
                "randomListId": "mock_011",
                "registerRandomListId": "000000000000000000000000",
                "randomNumber": "A0001",
                "lastGroup": "",
                "group": "实验组",
                "parGroupName": "实验组",
                "subGroupName": "",
                "info": [
                    {
                        "name": "shortname",
                        "value": "001",
                        "label": ""
                    }
                ],
                "actualInfo": null,
                "status": 3,
                "randomTime": 1694318296,
                "randomPeople": "mock_005",
                "pvUnblindingStatus": 0,
                "pvUnblindingTime": 1747720992,
                "pvUnblindingPeople": "000000000000000000000000",
                "pvUnblindingReasonStr": "政策要求",
                "pvUnblindingReason": "",
                "urgentUnblindingStatus": 0,
                "urgentUnblindingTime": 0,
                "urgentUnblindingPeople": "000000000000000000000000",
                "urgentUnblindingReasonStr": "",
                "urgentUnblindingReason": "",
                "isSponsor": false,
                "remark": "",
                "signOutReason": "",
                "signOutTime": 0,
                "signOutRealTime": "",
                "signOutPeople": "000000000000000000000000",
                "finishRemark": "",
                "finishPeople": "000000000000000000000000",
                "finishTime": 0,
                "roomNumber": "",
                "replaceSubjectId": "000000000000000000000000",
                "replaceSubject": "",
                "replaceNumber": "",
                "meta": {
                    "createdAt": 1692152170,
                    "createdBy": "mock_005",
                    "updatedAt": 0,
                    "updatedBy": "000000000000000000000000",
                    "deletedAt": 0,
                    "deletedBy": "000000000000000000000000"
                },
                "urgentUnblindingApprovals": [],
                "pvUrgentUnblindingApprovals": [
                    {
                        "number": "20250520001",
                        "status": 2,
                        "approvalType": 1,
                        "applicationTime": 1747708999,
                        "applicationBy": "mock_005",
                        "approvalTime": 1747720992,
                        "approvalBy": "mock_005",
                        "applicationByEmail": "<EMAIL>",
                        "approvalByEmail": "<EMAIL>",
                        "reason": 0,
                        "reasonStr": "妊娠",
                        "remark": "",
                        "rejectReason": "111",
                        "msg": "",
                        "smsUsers": null,
                        "lang": "zh"
                    },
                    {
                        "number": "20250520002",
                        "status": 0,
                        "approvalType": 1,
                        "applicationTime": 1747721004,
                        "applicationBy": "mock_005",
                        "approvalTime": 0,
                        "approvalBy": "000000000000000000000000",
                        "applicationByEmail": "<EMAIL>",
                        "approvalByEmail": "",
                        "reason": 0,
                        "reasonStr": "政策要求",
                        "remark": "",
                        "rejectReason": "",
                        "msg": "",
                        "smsUsers": null,
                        "lang": "zh"
                    }
                ],
                "isScreen": null,
                "screenTime": "",
                "icfTime": "",
                "joinTime": "",
                "joinTimeForTime": 0,
                "registerGroup": "",
                "TransferSiteInfos": null,
                "deleted": false,
                "randomSequenceNumber": ""
            }
        }
    },
    {
        "method": "GET",
        "path": "/dispensing/subject/role",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "visitOpen": "",
                "canOutVisit": true,
                "canReissue": false,
                "visitCycleInfoId": "mock_032",
                "info": [
                    {
                        "id": "mock_035",
                        "customerId": "mock_009",
                        "projectId": "mock_010",
                        "envId": "mock_001",
                        "cohortId": "000000000000000000000000",
                        "subjectId": "mock_008",
                        "visitInfo": {
                            "visitCycleInfoId": "mock_031",
                            "number": "V1",
                            "instanceRepeatNo": "0",
                            "blockRepeatNo": "0",
                            "name": "V1",
                            "random": true,
                            "dispensing": true
                        },
                        "serialNumber": 100,
                        "visitSign": false,
                        "dispensingMedicines": [
                            {
                                "medicineId": "mock_036",
                                "name": "实验药",
                                "number": "SYY002",
                                "shortCode": "",
                                "expirationDate": "2026-09-25",
                                "batchNumber": "SYY",
                                "packageNumber": "",
                                "time": 1695872994,
                                "type": 1,
                                "realMedicineId": "000000000000000000000000",
                                "label": "",
                                "useFormulas": null,
                                "doseInfo": null,
                                "openSetting": 0,
                                "unBlind": 1,
                                "dtps": [
                                    0
                                ]
                            },
                            {
                                "medicineId": "mock_036",
                                "name": "实验药",
                                "number": "SYY003",
                                "shortCode": "",
                                "expirationDate": "2026-09-26",
                                "batchNumber": "SYY",
                                "packageNumber": "",
                                "time": 1695872994,
                                "type": 1,
                                "realMedicineId": "000000000000000000000000",
                                "label": "",
                                "useFormulas": null,
                                "doseInfo": null,
                                "openSetting": 0,
                                "unBlind": 2,
                                "dtps": [
                                    0
                                ]
                            }
                        ],
                        "otherDispensingMedicines": [],
                        "othersDispensingMedicines": null,
                        "realDispensingMedicines": null,
                        "status": 2,
                        "reissue": 0,
                        "dispensingTime": 1695872994,
                        "reasons": [],
                        "replaceMedicines": [],
                        "print": 0,
                        "label": "",
                        "labels": null,
                        "remark": "",
                        "order": "",
                        "canRetrieval": [
                            {
                                "medicineId": "mock_036",
                                "name": "实验药",
                                "number": "SYY002",
                                "shortCode": "",
                                "expirationDate": "2026-09-25",
                                "batchNumber": "SYY",
                                "packageNumber": "",
                                "time": 1695872994,
                                "type": 1,
                                "realMedicineId": "000000000000000000000000",
                                "label": "",
                                "useFormulas": null,
                                "doseInfo": null,
                                "openSetting": 0,
                                "dtps": [
                                    0
                                ]
                            }
                        ],
                        "otherCanRetrieval": null,
                        "medicineOrder": [],
                        "realOtherDispensingMedicines": null,
                        "cancelMedicinesHistory": [
                            {
                                "medicineId": "mock_037",
                                "name": "体重药",
                                "number": "TZY001",
                                "shortCode": "",
                                "expirationDate": "2024-09-07",
                                "batchNumber": "20230902",
                                "packageNumber": "",
                                "time": 1693635658,
                                "type": 6,
                                "realMedicineId": "000000000000000000000000",
                                "label": "",
                                "useFormulas": null,
                                "doseInfo": null,
                                "openSetting": 0
                            },
                            {
                                "medicineId": "mock_038",
                                "name": "实验药",
                                "number": "SYY001",
                                "shortCode": "",
                                "expirationDate": "2026-09-25",
                                "batchNumber": "SYY",
                                "packageNumber": "",
                                "time": 1694318368,
                                "type": 1,
                                "realMedicineId": "000000000000000000000000",
                                "label": "",
                                "useFormulas": null,
                                "doseInfo": null,
                                "openSetting": 0
                            }
                        ],
                        "otherMedicinesHistory": null,
                        "replaceOtherMedicines": null,
                        "workTaskId": "000000000000000000000000",
                        "doseInfo": null,
                        "period": {
                            "outSize": false,
                            "outSizeWindow": false,
                            "minPeriod": "",
                            "maxPeriod": "",
                            "lineTime": "",
                            "maximumTime": 0
                        },
                        "dtp": false,
                        "cohortName": "",
                        "subjectStatus": 0,
                        "tz": "Asia/Shanghai",
                        "startVisit": 0,
                        "dispensingTrailCount": 15,
                        "canDispensing": false,
                        "canRegister": true,
                        "canReplace": false,
                        "retrieval": false,
                        "canResume": false,
                        "canReissue": false,
                        "outVisitStr": "计划外自定义",
                        "random": true,
                        "otherDispensingMedicinesCount": [],
                        "realOtherDispensingMedicinesCount": []
                    },
                    {
                        "id": "mock_039",
                        "customerId": "mock_009",
                        "projectId": "mock_010",
                        "envId": "mock_001",
                        "cohortId": "000000000000000000000000",
                        "subjectId": "mock_008",
                        "visitInfo": {
                            "visitCycleInfoId": "mock_031",
                            "number": "V1",
                            "instanceRepeatNo": "0",
                            "blockRepeatNo": "1",
                            "name": "V1",
                            "random": true,
                            "dispensing": true
                        },
                        "serialNumber": 101,
                        "visitSign": true,
                        "dispensingMedicines": [],
                        "otherDispensingMedicines": [],
                        "othersDispensingMedicines": null,
                        "realDispensingMedicines": null,
                        "status": 3,
                        "reissue": 1,
                        "dispensingTime": 1696989668,
                        "reasons": [],
                        "replaceMedicines": [],
                        "print": 0,
                        "label": "",
                        "labels": null,
                        "remark": "",
                        "order": "",
                        "canRetrieval": null,
                        "otherCanRetrieval": null,
                        "medicineOrder": [],
                        "realOtherDispensingMedicines": null,
                        "cancelMedicinesHistory": [
                            {
                                "medicineId": "mock_040",
                                "name": "实验药",
                                "number": "SYY005",
                                "shortCode": "",
                                "expirationDate": "2026-09-25",
                                "batchNumber": "SYY",
                                "packageNumber": "",
                                "time": 1694332618,
                                "type": 6,
                                "realMedicineId": "000000000000000000000000",
                                "label": "",
                                "useFormulas": null,
                                "doseInfo": null,
                                "openSetting": 0
                            },
                            {
                                "medicineId": "mock_040",
                                "name": "实验药",
                                "number": "SYY005",
                                "shortCode": "",
                                "expirationDate": "2026-09-25",
                                "batchNumber": "SYY",
                                "packageNumber": "",
                                "time": 1694332964,
                                "type": 6,
                                "realMedicineId": "000000000000000000000000",
                                "label": "",
                                "useFormulas": null,
                                "doseInfo": null,
                                "openSetting": 0
                            }
                        ],
                        "otherMedicinesHistory": [],
                        "replaceOtherMedicines": null,
                        "workTaskId": "000000000000000000000000",
                        "doseInfo": null,
                        "period": {
                            "outSize": false,
                            "outSizeWindow": false,
                            "minPeriod": "",
                            "maxPeriod": "",
                            "lineTime": "",
                            "maximumTime": 0
                        },
                        "dtp": false,
                        "cohortName": "",
                        "subjectStatus": 0,
                        "tz": "Asia/Shanghai",
                        "startVisit": 0,
                        "dispensingTrailCount": 5,
                        "canDispensing": false,
                        "canRegister": false,
                        "canReplace": false,
                        "retrieval": false,
                        "canResume": false,
                        "canReissue": false,
                        "outVisitStr": "计划外自定义",
                        "random": true,
                        "otherDispensingMedicinesCount": [],
                        "realOtherDispensingMedicinesCount": []
                    },
                    {
                        "id": "mock_041",
                        "customerId": "mock_009",
                        "projectId": "mock_010",
                        "envId": "mock_001",
                        "cohortId": "000000000000000000000000",
                        "subjectId": "mock_008",
                        "visitInfo": {
                            "visitCycleInfoId": "mock_042",
                            "number": "V3",
                            "instanceRepeatNo": "0",
                            "blockRepeatNo": "0",
                            "name": "V3",
                            "random": false,
                            "dispensing": true
                        },
                        "serialNumber": 300,
                        "visitSign": false,
                        "dispensingMedicines": [],
                        "otherDispensingMedicines": [],
                        "othersDispensingMedicines": null,
                        "realDispensingMedicines": null,
                        "status": 1,
                        "reissue": 0,
                        "dispensingTime": 0,
                        "reasons": [],
                        "replaceMedicines": null,
                        "print": 0,
                        "label": "",
                        "labels": null,
                        "remark": "",
                        "order": "",
                        "canRetrieval": null,
                        "otherCanRetrieval": null,
                        "medicineOrder": [],
                        "realOtherDispensingMedicines": null,
                        "cancelMedicinesHistory": null,
                        "otherMedicinesHistory": null,
                        "replaceOtherMedicines": null,
                        "workTaskId": "000000000000000000000000",
                        "doseInfo": null,
                        "period": {
                            "outSize": true,
                            "outSizeWindow": false,
                            "minPeriod": "",
                            "maxPeriod": "",
                            "lineTime": "",
                            "maximumTime": 0
                        },
                        "dtp": true,
                        "cohortName": "",
                        "subjectStatus": 0,
                        "tz": "Asia/Shanghai",
                        "startVisit": 0,
                        "dispensingTrailCount": 3,
                        "canDispensing": true,
                        "canRegister": false,
                        "canReplace": false,
                        "retrieval": false,
                        "canResume": false,
                        "canReissue": false,
                        "outVisitStr": "计划外自定义",
                        "random": true,
                        "otherDispensingMedicinesCount": [],
                        "realOtherDispensingMedicinesCount": []
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/history",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "oid": "mock_041",
                        "content": "【恢复发放】受试者编号：001",
                        "tableInfo": {
                            "title": null,
                            "value": null
                        },
                        "user": "demo.user(100012)",
                        "time": 1738735432
                    },
                    {
                        "oid": "mock_041",
                        "content": "【不参加访视】受试者编号：001，备注：1。",
                        "tableInfo": {
                            "title": null,
                            "value": null
                        },
                        "user": "demo.user(100012)",
                        "time": 1738735114
                    },
                    {
                        "oid": "mock_041",
                        "content": "【不参加访视】受试者编号：001，备注：<no value>。",
                        "tableInfo": {
                            "title": null,
                            "value": null
                        },
                        "user": "demo.user(100012)",
                        "time": 1705631688
                    }
                ],
                "total": 3
            }
        }
    },
    {
        "method": "GET",
        "path": "/randomization/formula/form",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_043",
                    "cohortId": "000000000000000000000000",
                    "cohortName": "",
                    "isCalc": false,
                    "calcType": null,
                    "name": "field0",
                    "label": "身高表单",
                    "labelNation": null,
                    "labelEn": "",
                    "customFormulas": "",
                    "precision": null,
                    "round": 0,
                    "type": "inputNumber",
                    "status": 1,
                    "applicationType": 2,
                    "variable": "height",
                    "used": false,
                    "options": null,
                    "list": false,
                    "modifiable": false,
                    "required": false,
                    "stratification": false,
                    "digit": 0,
                    "accuracy": 0,
                    "dateFormat": "",
                    "formatType": "numberLength",
                    "timeFormat": "",
                    "length": 20,
                    "range": null,
                    "dateRange": null,
                    "isCustomFormula": false
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/dispensing/subject/visit/web",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "visitId": "mock_032",
                "visitName": "V2",
                "label": [
                    {
                        "id": "mock_044",
                        "index": 0,
                        "openSetting": 1,
                        "name": "2112",
                        "dtp": [
                            0
                        ],
                        "defaultShow": false
                    }
                ],
                "medicine": []
            }
        }
    },
    {
        "method": "GET",
        "path": "/dispensing/dose",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "field": {
                    "id": "mock_045",
                    "cohortId": "000000000000000000000000",
                    "cohortName": "",
                    "isCalc": false,
                    "calcType": null,
                    "name": "field4",
                    "label": "剂量判断",
                    "labelNation": null,
                    "labelEn": "",
                    "customFormulas": "",
                    "precision": null,
                    "round": 0,
                    "type": "radio",
                    "status": 1,
                    "applicationType": 3,
                    "variable": "doseTwo",
                    "used": false,
                    "options": [
                        {
                            "label": "否",
                            "value": "option2",
                            "formula": null,
                            "disable": false,
                            "isCopyData": false
                        }
                    ],
                    "list": false,
                    "modifiable": false,
                    "required": false,
                    "stratification": false,
                    "digit": 0,
                    "accuracy": 0,
                    "dateFormat": "",
                    "formatType": "",
                    "timeFormat": "",
                    "length": null,
                    "range": null,
                    "dateRange": null,
                    "isCustomFormula": false
                },
                "noFrequency": false,
                "inheritValue": null,
                "label": null,
                "name": null,
                "isPage": true
            }
        }
    },
    {
        "method": "GET",
        "path": "/dispensing/formula",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "weight": false,
                "weight_value": null,
                "height": false,
                "height_value": null,
                "age": false,
                "age_value": null,
                "medicine_name": null
            }
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/drug-names",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "blindDrugNames": [
                    "xxxddd",
                    "A",
                    "对照药"
                ],
                "drugNames": [
                    "xxxddd",
                    "未编号",
                    "未标号"
                ],
                "drugSpecs": {
                    "xxxddd": [
                        "mg"
                    ],
                    "未标号": [
                        "mg"
                    ],
                    "未编号": [
                        "mg"
                    ]
                },
                "openDrugNames": null,
                "otherDrugNames": [
                    "xxxddd",
                    "未编号",
                    "未标号"
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/dispensing/subject/status-room",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "id": "mock_046",
                "customerId": "mock_009",
                "projectId": "mock_010",
                "envId": "mock_001",
                "cohortId": "000000000000000000000000",
                "projectSiteID": "mock_006",
                "randomListId": "mock_011",
                "registerRandomListId": "000000000000000000000000",
                "randomNumber": "A0002",
                "lastGroup": "",
                "group": "实验组",
                "parGroupName": "实验组",
                "subGroupName": "",
                "info": [
                    {
                        "name": "shortname",
                        "value": "002",
                        "label": ""
                    },
                    {
                        "name": "field0",
                        "value": null,
                        "label": ""
                    }
                ],
                "actualInfo": null,
                "status": 3,
                "randomTime": 1703754555,
                "randomPeople": "mock_005",
                "pvUnblindingStatus": 0,
                "pvUnblindingTime": 0,
                "pvUnblindingPeople": "000000000000000000000000",
                "pvUnblindingReasonStr": "",
                "pvUnblindingReason": "",
                "urgentUnblindingStatus": 0,
                "urgentUnblindingTime": 0,
                "urgentUnblindingPeople": "000000000000000000000000",
                "urgentUnblindingReasonStr": "",
                "urgentUnblindingReason": "",
                "isSponsor": false,
                "remark": "",
                "signOutReason": "",
                "signOutTime": 0,
                "signOutRealTime": "",
                "signOutPeople": "000000000000000000000000",
                "finishRemark": "",
                "finishPeople": "000000000000000000000000",
                "finishTime": 0,
                "roomNumber": "",
                "replaceSubjectId": "000000000000000000000000",
                "replaceSubject": "",
                "replaceNumber": "",
                "meta": {
                    "createdAt": 1703754552,
                    "createdBy": "mock_005",
                    "updatedAt": 0,
                    "updatedBy": "000000000000000000000000",
                    "deletedAt": 0,
                    "deletedBy": "000000000000000000000000"
                },
                "urgentUnblindingApprovals": [],
                "pvUrgentUnblindingApprovals": null,
                "isScreen": null,
                "screenTime": "",
                "icfTime": "",
                "joinTime": "",
                "joinTimeForTime": 0,
                "registerGroup": "",
                "TransferSiteInfos": null,
                "deleted": false,
                "randomSequenceNumber": ""
            }
        }
    },
    {
        "method": "GET",
        "path": "/dispensing/subject/role",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "visitOpen": "计划外自定义",
                "canOutVisit": false,
                "canReissue": false,
                "visitCycleInfoId": "000000000000000000000000",
                "info": [
                    {
                        "id": "mock_047",
                        "customerId": "mock_009",
                        "projectId": "mock_010",
                        "envId": "mock_001",
                        "cohortId": "000000000000000000000000",
                        "subjectId": "mock_046",
                        "visitInfo": {
                            "visitCycleInfoId": "mock_031",
                            "number": "V1",
                            "instanceRepeatNo": "0",
                            "blockRepeatNo": "0",
                            "name": "V1",
                            "random": true,
                            "dispensing": true
                        },
                        "serialNumber": 100,
                        "visitSign": false,
                        "dispensingMedicines": [],
                        "otherDispensingMedicines": [],
                        "othersDispensingMedicines": null,
                        "realDispensingMedicines": null,
                        "status": 1,
                        "reissue": 0,
                        "dispensingTime": 0,
                        "reasons": null,
                        "replaceMedicines": null,
                        "print": 0,
                        "label": "",
                        "labels": null,
                        "remark": "",
                        "order": "",
                        "canRetrieval": null,
                        "otherCanRetrieval": null,
                        "medicineOrder": [],
                        "realOtherDispensingMedicines": null,
                        "cancelMedicinesHistory": null,
                        "otherMedicinesHistory": null,
                        "replaceOtherMedicines": null,
                        "workTaskId": "000000000000000000000000",
                        "doseInfo": null,
                        "period": {
                            "outSize": false,
                            "outSizeWindow": false,
                            "minPeriod": "",
                            "maxPeriod": "",
                            "lineTime": "",
                            "maximumTime": 0
                        },
                        "dtp": false,
                        "cohortName": "",
                        "subjectStatus": 0,
                        "tz": "Asia/Shanghai",
                        "startVisit": 0,
                        "dispensingTrailCount": 0,
                        "canDispensing": true,
                        "canRegister": false,
                        "canReplace": false,
                        "retrieval": false,
                        "canResume": false,
                        "canReissue": false,
                        "outVisitStr": "计划外自定义",
                        "random": true,
                        "otherDispensingMedicinesCount": [],
                        "realOtherDispensingMedicinesCount": []
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/randomization/formula/form",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_043",
                    "cohortId": "000000000000000000000000",
                    "cohortName": "",
                    "isCalc": false,
                    "calcType": null,
                    "name": "field0",
                    "label": "身高表单",
                    "labelNation": null,
                    "labelEn": "",
                    "customFormulas": "",
                    "precision": null,
                    "round": 0,
                    "type": "inputNumber",
                    "status": 1,
                    "applicationType": 2,
                    "variable": "height",
                    "used": false,
                    "options": null,
                    "list": false,
                    "modifiable": false,
                    "required": false,
                    "stratification": false,
                    "digit": 0,
                    "accuracy": 0,
                    "dateFormat": "",
                    "formatType": "numberLength",
                    "timeFormat": "",
                    "length": 20,
                    "range": null,
                    "dateRange": null,
                    "isCustomFormula": false
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/dispensing/dose",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": null
        }
    },
    {
        "method": "GET",
        "path": "/dispensing/subject/visit/web",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "visitId": "mock_031",
                "visitName": "V1",
                "label": [
                    {
                        "id": "mock_048",
                        "index": 0,
                        "openSetting": 1,
                        "customerCalculation": "{height}*2",
                        "name": "实验药",
                        "dtp": [
                            0
                        ],
                        "values": {
                            "spec": "mg",
                            "custom_dispensing_number": [
                                1,
                                2,
                                3
                            ],
                            "automaticRecode": true,
                            "label": "实验药"
                        },
                        "defaultShow": false
                    },
                    {
                        "id": "mock_049",
                        "index": 1,
                        "openSetting": 1,
                        "name": "1",
                        "dtp": [
                            0
                        ],
                        "values": {
                            "spec": "mg",
                            "custom_dispensing_number": [
                                1,
                                2,
                                3
                            ],
                            "automaticRecode": false,
                            "label": "1"
                        },
                        "defaultShow": false
                    },
                    {
                        "id": "mock_050",
                        "index": 2,
                        "name": "A12",
                        "dtp": [
                            0
                        ],
                        "values": {
                            "spec": "mg",
                            "custom_dispensing_number": [
                                12,
                                13,
                                14
                            ],
                            "automaticRecode": false,
                            "label": ""
                        },
                        "defaultShow": false
                    }
                ],
                "medicine": [
                    {
                        "id": "mock_051",
                        "key": 0,
                        "name": "未编号",
                        "customerCalculation": "",
                        "dtp": [
                            0
                        ],
                        "max": 10,
                        "defaultShow": false,
                        "spec": "mg",
                        "custom_dispensing_number": [
                            1,
                            2,
                            3
                        ],
                        "is_other": true,
                        "salt": "KwCa0W",
                        "saltName": "2cd89afb26802784fbe02ebbfed3ce8e",
                        "automaticRecode": false
                    },
                    {
                        "id": "mock_051",
                        "key": 1,
                        "name": "未编号2",
                        "customerCalculation": "",
                        "dtp": [
                            0
                        ],
                        "max": 2,
                        "defaultShow": false,
                        "spec": "hg",
                        "custom_dispensing_number": [
                            2
                        ],
                        "salt": "zyg3Uc",
                        "saltName": "452ba1704f48e8e3819f34facde9df7a296addb96600542e1530d8571dfd5cd7",
                        "automaticRecode": false
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/dispensing/formula",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "weight": false,
                "weight_value": null,
                "height": false,
                "height_value": null,
                "age": false,
                "age_value": null,
                "medicine_name": null
            }
        }
    },
    {
        "method": "GET",
        "path": "/subject/list-invalid",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "blind": true,
                "fields": [],
                "isRandomNumber": true,
                "items": [],
                "reRandomForm": [],
                "total": 0
            }
        }
    },
    {
        "method": "GET",
        "path": "/subject-visit",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "dateInfo": {
                    [dayjs().format('YYYY-MM-DD')]: [
                        {
                            "name": "002-07",
                            "period": {
                                "outSize": true,
                                "outSizeWindow": true,
                                "minPeriod": "2025-05-09",
                                "maxPeriod": "2025-05-23",
                                "lineTime": "2025-05-18",
                                "maximumTime": 1747998849
                            },
                            "status": 3,
                            "visitNumber": "V4",
                            "site": "001-示例医院A",
                            "actualDate": "",
                            "dispensingId": "67b2a97d0d8b5d041d7cfe2f"
                        }
                    ],
                },
                "subjectVisitSummary": {
                    "outSizeNotCompleted": 128,
                    "inProgress": 1,
                    "outSizeCompleted": 28,
                    "completedOnSchedule": 13,
                    "prepare": 0
                },
                "outSizeNotCompleted": 128,
                "inProgress": 1,
                "outSizeCompleted": 28,
                "completedOnSchedule": 13,
                "prepare": 0
            }
        }
    },
    {
        "method": "GET",
        "path": "/subject-visit/history",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": []
        }
    },
    {
        "method": "GET",
        "path": "/projects/role/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_055",
                    "roleId": "mock_056",
                    "customerId": "mock_009",
                    "projectId": "mock_010",
                    "name": "Sub-I [unblinded]",
                    "scope": "study",
                    "description": "",
                    "template": 1,
                    "status": 1,
                    "permissions": [
                        "operation.projects.main.setting.base.view",
                        "operation.projects.main.setting.base.edit",
                        "operation.subject.view-list"
                    ]
                },
                {
                    "id": "mock_057",
                    "roleId": "mock_058",
                    "customerId": "mock_009",
                    "projectId": "mock_010",
                    "name": "CRA [blinded]",
                    "scope": "study",
                    "description": "-",
                    "template": 1,
                    "status": 2,
                    "permissions": [
                        "operation.projects.home.view",
                        "operation.subject.view-list",
                        "operation.subject.random"
                    ]
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/projects/role/user/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_059",
                    "userId": "mock_005",
                    "cloudId": "mock_016",
                    "unbind": false,
                    "name": "demo.user",
                    "email": "<EMAIL>",
                    "phone": ""
                },
                {
                    "id": "mock_060",
                    "userId": "mock_018",
                    "cloudId": "mock_018",
                    "unbind": false,
                    "name": "",
                    "email": "<EMAIL>",
                    "phone": ""
                }
            ]
        }
    },
    {
        "method": "POST",
        "path": "/medicines/medicine-summary-storehouse",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "_id": "mock_023",
                        "customer_id": "mock_009",
                        "env_id": "mock_001",
                        "medicine": [
                            {
                                "_id": {
                                    "batch_number": "AAA",
                                    "name": "A",
                                    "spec": "mg",
                                    "status": 1,
                                    "storehouse_id": "mock_023"
                                },
                                "count": 18
                            },
                        ],
                        "medicine_others": [],
                        "project_id": "mock_010",
                        "storehouse": {
                            "_id": "mock_024",
                            "deleted": 0,
                            "meta": {
                                "created_at": 1620356362,
                                "created_by": "mock_061",
                                "updated_at": 0,
                                "updated_by": "000000000000000000000000"
                            },
                            "name": "仓库1",
                            "number": "S01"
                        },
                        "storehouse_id": "mock_024"
                    },
                ],
                "total": 1
            }
        }
    },
    {
        "method": "GET",
        "path": "/barcodes/codeRule",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "codeRule0": true,
                "codeRule1": true,
                "cohortIds": []
            }
        }
    },
    {
        "method": "POST",
        "path": "/medicines/storehouse-sku",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "isOpenPackage": true,
                "items": [
                    {
                        "batchNumber": "A",
                        "expirationDate": "2025-07-17",
                        "id": "mock_062",
                        "name": "B",
                        "number": "A068",
                        "orderNumber": null,
                        "packageNumber": "-",
                        "place": "-",
                        "shortCode": "-",
                        "status": 1,
                        "storehouseName": "仓库1"
                    }
                ],
                "total": 1
            }
        }
    },
    {
        "method": "POST",
        "path": "/medicines/storehouse-other-sku",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "isOpenPackage": true,
                "items": [
                    {
                        "batchNumber": "A",
                        "expirationDate": "2025-07-17",
                        "id": "mock_062",
                        "name": "B",
                        "number": "A068",
                        "orderNumber": null,
                        "packageNumber": "-",
                        "place": "-",
                        "shortCode": "-",
                        "status": 1,
                        "storehouseName": "仓库1",
                        "group": []
                    }
                ],
                "total": 1
            }
        }
    },
    {
        "method": "GET",
        "path": "/projects/cohort",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": ""
        }
    },
    {
        "method": "GET",
        "path": "/history",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "oid": "mock_062",
                        "content": "【研究产品上传】状态：待入仓",
                        "tableInfo": {
                            "title": null,
                            "value": null
                        },
                        "user": "demo.user(100012)",
                        "time": 1720518206
                    }
                ],
                "total": 1
            }
        }
    },
    {
        "method": "POST",
        "path": "/medicines/medicine-summary-site",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "_id": "mock_006",
                        "medicine": [
                            {
                                "_id": {
                                    "batch_number": "A",
                                    "name": "A",
                                    "site_id": "mock_006",
                                    "spec": "mg",
                                    "status": 6
                                },
                                "count": 2
                            },
                        ],
                        "medicine_others": [],
                        "name": "示例医院A",
                        "number": "001"
                    },
                ],
                "total": 1
            }
        }
    },
    {
        "method": "GET",
        "path": "/medicines/site-forecast",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_006",
                    "number": "001",
                    "name": "示例医院A",
                    "name_en": "示例医院A",
                    "date": "-"
                },
                {
                    "id": "mock_007",
                    "number": "002",
                    "name": "示例医院B",
                    "name_en": "示例医院B",
                    "date": "-"
                }
            ]
        }
    },
    {
        "method": "POST",
        "path": "/medicines/site-sku",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "isOpenPackage": true,
                "items": [
                    {
                        "batchNumber": "A",
                        "expirationDate": "2025-07-11",
                        "id": "mock_063",
                        "name": "A",
                        "number": "A001",
                        "orderNumber": [
                            "202407090001"
                        ],
                        "packageNumber": "-",
                        "place": "-",
                        "shortCode": "-",
                        "siteName": "001-示例医院A",
                        "status": 5,
                        "timeZone": "UTC+8",
                        "tz": "Asia/Shanghai"
                    },
                    {
                        "batchNumber": "A",
                        "expirationDate": "2025-07-17",
                        "id": "mock_064",
                        "name": "B",
                        "number": "A002",
                        "orderNumber": [
                            "202407090001"
                        ],
                        "packageNumber": "-",
                        "place": "-",
                        "shortCode": "-",
                        "siteName": "001-示例医院A",
                        "status": 6,
                        "timeZone": "UTC+8",
                        "tz": "Asia/Shanghai"
                    },
                    {
                        "batchNumber": "A",
                        "expirationDate": "2025-07-17",
                        "id": "mock_065",
                        "name": "B",
                        "number": "A010",
                        "orderNumber": [
                            "202407090001"
                        ],
                        "packageNumber": "-",
                        "place": "-",
                        "shortCode": "-",
                        "siteName": "001-示例医院A",
                        "status": 14,
                        "timeZone": "UTC+8",
                        "tz": "Asia/Shanghai"
                    }
                ],
                "total": 3
            }
        }
    },
    {
        "method": "POST",
        "path": "/medicines/site-other-sku",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "batchNumber": "A",
                        "expirationDate": "2025-07-19",
                        "group": [
                            {
                                "packageCount": 0,
                                "status": 5,
                                "statusCount": 16
                            },
                            {
                                "packageCount": 0,
                                "status": 1,
                                "statusCount": 372
                            },
                            {
                                "packageCount": 0,
                                "status": 6,
                                "statusCount": 12
                            }
                        ],
                        "groupInfo": [
                            {
                                "packageNumbers": [
                                    "-",
                                    "-",
                                    "-"
                                ],
                                "status": 5,
                                "statusCount": 16
                            },
                            {
                                "packageNumbers": [
                                    "-",
                                    "-",
                                    "-"
                                ],
                                "status": 1,
                                "statusCount": 372
                            },
                            {
                                "packageNumbers": [
                                    "-",
                                    "-",
                                    "-"
                                ],
                                "status": 6,
                                "statusCount": 12
                            }
                        ],
                        "id": "mock_066",
                        "name": "未编号",
                        "site": "mock_006",
                        "siteName": "001-示例医院A",
                        "count": 372,
                        "to_be_send_count": 0,
                        "in_transit_count": 0,
                        "quarantined_count": 0,
                        "used_count": 16,
                        "lost_count": 12,
                        "expired_count": 0,
                        "to_be_confirm_count": 0,
                        "locked_count": 0,
                        "frozen_count": 0
                    },
                ],
                "total": 1
            }
        }
    },
    {
        "method": "GET",
        "path": "/order/subject",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "label": "009",
                    "value": "mock_069"
                },
                {
                    "label": "027",
                    "value": "mock_070"
                },
                {
                    "label": "002-04",
                    "value": "mock_071"
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/drug-names-by-role",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "drugNames": [
                    {
                        "drugName": "对照药2",
                        "isBlindedDrug": true,
                        "salt": "",
                        "saltDrugName": ""
                    },
                    {
                        "drugName": "B",
                        "isBlindedDrug": true,
                        "salt": "",
                        "saltDrugName": ""
                    },
                    {
                        "drugName": "12",
                        "isBlindedDrug": true,
                        "salt": "",
                        "saltDrugName": ""
                    }
                ],
                "haveDrug": true,
                "haveOtherDrug": true
            }
        }
    },
    {
        "method": "GET",
        "path": "/order/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "LOGOrderNo": "",
                        "_id": "mock_075",
                        "address": "",
                        "app": 0,
                        "cancellerUser": [],
                        "carrier": "",
                        "confirm_at": 0,
                        "contacts": "",
                        "count": 0,
                        "customer_id": "mock_009",
                        "dispensing": null,
                        "dispensing_id": "000000000000000000000000",
                        "dtp_cancel": true,
                        "email": "",
                        "env_id": "mock_001",
                        "expected_arrival_time": "",
                        "last_reminder_date": 0,
                        "logistics_info": {
                            "logistics": "",
                            "number": "",
                            "other": ""
                        },
                        "medicines": null,
                        "medicines_history": null,
                        "medicines_package": [],
                        "meta": {
                            "createdUser": [],
                            "created_at": 1726629664,
                            "created_by": "000000000000000000000000",
                            "updated_at": 1726629698,
                            "updated_by": "mock_005"
                        },
                        "mode": 1,
                        "order_number": "202409180007",
                        "other_medicines": [
                            {
                                "batch": "CCC",
                                "expire_date": "2025-09-20",
                                "id": "mock_076",
                                "name": "未标号",
                                "receive_count": 12,
                                "salt": "",
                                "saltname": "",
                                "use_count": 12
                            }
                        ],
                        "other_medicines_new": null,
                        "phone": "",
                        "project_id": "mock_010",
                        "reason": "",
                        "receiveUser": [
                            {
                                "_id": "mock_005",
                                "admin": true,
                                "app_language": "zh-CN",
                                "cloud_id": "mock_016",
                                "deleted": false,
                                "group": 1,
                                "info": {
                                    "company": "EDC",
                                    "description": "",
                                    "email": "<EMAIL>",
                                    "name": "demo.user",
                                    "phone": "",
                                    "status": 1,
                                    "unicode": 100012,
                                    "version": 19
                                },
                                "projectadmin": true,
                                "registration_id": "1104a8979399595f069",
                                "roles": [
                                    "mock_077",
                                    "mock_078",
                                    "mock_079"
                                ],
                                "unicode": 100012
                            }
                        ],
                        "receive_at": 1726629698,
                        "receive_by": "mock_005",
                        "receive_id": "mock_007",
                        "send_at": 1726629685,
                        "send_by": "mock_005",
                        "send_id": "mock_023",
                        "shipment_info": {
                            "logistics": 0,
                            "number": "",
                            "other": ""
                        },
                        "status": 3,
                        "subject_id": "000000000000000000000000",
                        "task_id": "000000000000000000000000",
                        "type": 1
                    },
                ],
                "mixPackage": {
                    "A": ["A", "B"],
                    "B": ["A", "B"],
                    "实验药": ["实验药"]
                },
                "packageIsOpen": true,
                "total": 1
            }
        }
    },
    {
        "method": "GET",
        "path": "/order/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "_id": "mock_089",
                        "actual_receipt_time": "",
                        "address": "",
                        "app": 0,
                        "cancellerUser": [],
                        "confirm_at": 0,
                        "contacts": "",
                        "count": 146,
                        "customer_id": "mock_009",
                        "dispensing": null,
                        "dispensing_id": "000000000000000000000000",
                        "dtp_cancel": true,
                        "email": "",
                        "env_id": "mock_001",
                        "last_reminder_date": 0,
                        "logistics_info": {
                            "logistics": "",
                            "number": "",
                            "other": ""
                        },
                        "medicines": [
                            "mock_090",
                            "mock_091",
                            "mock_092"
                        ],
                        "medicines_history": null,
                        "medicines_package": null,
                        "meta": {
                            "createdUser": [
                                {
                                    "_id": "mock_005",
                                    "admin": true,
                                    "app_language": "zh-CN",
                                    "cloud_id": "mock_016",
                                    "deleted": false,
                                    "group": 1,
                                    "info": {
                                        "company": "EDC",
                                        "description": "",
                                        "email": "<EMAIL>",
                                        "name": "demo.user",
                                        "phone": "",
                                        "status": 1,
                                        "unicode": 100012,
                                        "version": 19
                                    },
                                    "projectadmin": true,
                                    "registration_id": "1104a8979399595f069",
                                    "roles": [
                                        "mock_077",
                                        "mock_078",
                                        "mock_079"
                                    ],
                                    "unicode": 100012
                                }
                            ],
                            "created_at": 1694318451,
                            "created_by": "mock_005",
                            "updated_at": 1694318463,
                            "updated_by": "mock_005"
                        },
                        "mode": 0,
                        "order_number": "202309100001",
                        "other_medicines": null,
                        "other_medicines_new": null,
                        "phone": "",
                        "project_id": "mock_010",
                        "receiveUser": [
                            {
                                "_id": "mock_005",
                                "admin": true,
                                "app_language": "zh-CN",
                                "cloud_id": "mock_016",
                                "deleted": false,
                                "group": 1,
                                "info": {
                                    "company": "EDC",
                                    "description": "",
                                    "email": "<EMAIL>",
                                    "name": "demo.user",
                                    "phone": "",
                                    "status": 1,
                                    "unicode": 100012,
                                    "version": 19
                                },
                                "projectadmin": true,
                                "registration_id": "1104a8979399595f069",
                                "roles": [
                                    "mock_077",
                                    "mock_078",
                                    "mock_079"
                                ],
                                "unicode": 100012
                            }
                        ],
                        "receive_at": 1694318463,
                        "receive_by": "mock_005",
                        "receive_id": "mock_023",
                        "send_id": "mock_006",
                        "shipment_info": {
                            "logistics": 0,
                            "number": "",
                            "other": ""
                        },
                        "sort_index": 20,
                        "status": 3,
                        "subject_id": "000000000000000000000000",
                        "task_id": "000000000000000000000000",
                        "type": 4
                    }
                ],
                "mixPackage": {
                    "A": [
                        "A",
                        "B"
                    ],
                    "B": [
                        "A",
                        "B"
                    ],
                    "实验药": [
                        "实验药"
                    ]
                },
                "packageIsOpen": true,
                "total": 1
            }
        }
    },
    {
        "method": "GET",
        "path": "/medicines/medicine-freeze",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "_id": "mock_093",
                        "bl": true,
                        "close_date": 0,
                        "count": 1,
                        "customer_id": "mock_009",
                        "env_id": "mock_001",
                        "frozen_medicine_ids": null,
                        "history": [
                            "mock_074"
                        ],
                        "institute_id": "mock_023",
                        "institute_type": 2,
                        "isolationCount": 0,
                        "medicines": [
                            "mock_074"
                        ],
                        "medicines_package": [],
                        "meta": {
                            "createdUser": [
                                {
                                    "_id": "mock_005",
                                    "admin": true,
                                    "app_language": "zh-CN",
                                    "cloud_id": "mock_016",
                                    "deleted": false,
                                    "group": 1,
                                    "info": {
                                        "company": "EDC",
                                        "description": "",
                                        "email": "<EMAIL>",
                                        "name": "demo.user",
                                        "phone": "",
                                        "status": 1,
                                        "unicode": 100012,
                                        "version": 19
                                    },
                                    "projectadmin": true,
                                    "registration_id": "1104a8979399595f069",
                                    "roles": [
                                        "mock_077",
                                        "mock_078",
                                        "mock_079"
                                    ],
                                    "unicode": 100012
                                }
                            ],
                            "created_at": 1730443011,
                            "created_by": "mock_005",
                            "updated_at": 0,
                            "updated_by": "000000000000000000000000"
                        },
                        "number": "202105270492",
                        "other_history": null,
                        "other_medicines": null,
                        "project_id": "mock_010",
                        "reason": "1",
                        "show": 1,
                        "untie_reason": ""
                    },
                ],
                "total": 4
            }
        }
    },
    {
        "method": "GET",
        "path": "/storehouses/all",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "id": "mock_097",
                        "number": "11",
                        "name": "22",
                        "deleted": 0,
                        "meta": {
                            "createdAt": 1687340051,
                            "createdBy": "000000000000000000000000",
                            "updatedAt": 0,
                            "updatedBy": "000000000000000000000000",
                            "deletedAt": 0,
                            "deletedBy": "000000000000000000000000"
                        }
                    },
                    {
                        "id": "mock_098",
                        "number": "22",
                        "name": "22",
                        "deleted": 0,
                        "meta": {
                            "createdAt": 1688435142,
                            "createdBy": "000000000000000000000000",
                            "updatedAt": 0,
                            "updatedBy": "000000000000000000000000",
                            "deletedAt": 0,
                            "deletedBy": "000000000000000000000000"
                        }
                    },
                    {
                        "id": "mock_099",
                        "number": "1",
                        "name": "222",
                        "deleted": 0,
                        "meta": {
                            "createdAt": 1687339085,
                            "createdBy": "000000000000000000000000",
                            "updatedAt": 0,
                            "updatedBy": "000000000000000000000000",
                            "deletedAt": 0,
                            "deletedBy": "000000000000000000000000"
                        }
                    }
                ],
                "total": 15
            }
        }
    },
    {
        "method": "GET",
        "path": "/projects-storehouses/list-medicine",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_023",
                    "name": "仓库1",
                    "medicineName": "",
                    "validityReminder": 0,
                    "alert": 0,
                    "connected": false,
                    "storehouseId": "mock_024",
                    "contacts": "XXX",
                    "isUsed": false,
                    "phone": "",
                    "email": "",
                    "address": "",
                    "supplier": "",
                    "notIncluded": false,
                    "country": null,
                    "onlyId": "NTZBRjJDNDk1NDY2QTI0REYxNjE3OUJG"
                },
                {
                    "id": "mock_025",
                    "name": "仓库2",
                    "medicineName": "",
                    "validityReminder": 0,
                    "alert": 0,
                    "connected": false,
                    "storehouseId": "mock_026",
                    "contacts": "12",
                    "isUsed": false,
                    "phone": "",
                    "email": "",
                    "address": "",
                    "supplier": "",
                    "notIncluded": false,
                    "country": null,
                    "onlyId": "MjZDQkMxNzI2QTczODQwNzNBNUMzOEMz"
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/projects-storehouses/query/one",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "id": "mock_023",
                "customerId": "mock_009",
                "projectId": "mock_010",
                "envId": "mock_001",
                "storehouseId": "mock_024",
                "alert": null,
                "medicineInfos": [],
                "contacts": "XXX",
                "phone": "",
                "email": "",
                "address": "",
                "deleted": 2,
                "connected": false,
                "supplier": "",
                "notIncluded": false,
                "country": null,
                "isUsed": false,
                "meta": {
                    "createdAt": 1693620933,
                    "createdBy": "mock_005",
                    "updatedAt": 0,
                    "updatedBy": "000000000000000000000000",
                    "deletedAt": 0,
                    "deletedBy": "000000000000000000000000"
                }
            }
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/drug-names",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "blindDrugNames": [
                    "B",
                    "对照药",
                    "对照药2"
                ],
                "drugNames": [
                    "xxxddd",
                    "体重药",
                    "A"
                ],
                "drugSpecs": {
                    "1": [
                        "2"
                    ],
                    "12": [
                        "mg"
                    ],
                    "122": [
                        "mg"
                    ],
                    "A": [
                        "mg"
                    ],
                    "B": [
                        "mg"
                    ],
                    "C": [
                        "mg"
                    ],
                    "xxxddd": [
                        "mg"
                    ],
                    "体重药": [
                        "盒"
                    ],
                    "对照药": [
                        "盒",
                        "mg"
                    ],
                    "对照药2": [
                        "mg"
                    ],
                    "年龄药23": [
                        "mg"
                    ],
                    "批次混合药": [
                        "mg"
                    ],
                    "未标号": [
                        "mg"
                    ],
                    "未编号": [
                        "mg"
                    ],
                    "未编号2": [
                        "hg"
                    ]
                },
                "openDrugNames": [
                    "体重药",
                    "A",
                    "B"
                ],
                "otherDrugNames": [
                    "xxxddd",
                    "未编号",
                    "未标号"
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/projects-sites/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "list": [
                    {
                        "active": 2,
                        "address": "-",
                        "batchGroupAlarm": [
                            {
                                "batch": "20230902",
                                "info": [
                                    {
                                        "estimate": 11,
                                        "group": "实验组",
                                        "warn": 12
                                    }
                                ]
                            },
                            {
                                "batch": "DZY1214",
                                "info": [
                                    {
                                        "estimate": 11,
                                        "group": "实验组",
                                        "warn": 12
                                    }
                                ]
                            }
                        ],
                        "batchGroupAlarmOpen": true,
                        "cn": "示例医院A",
                        "contactGroup": [],
                        "contacts": "-",
                        "country": [
                            "CHN",
                            "50"
                        ],
                        "deleted": 2,
                        "dmp_id": "000000000000000000000000",
                        "email": "-",
                        "en": "示例医院A",
                        "id": "mock_006",
                        "location": "UTC+08:00",
                        "name": "示例医院A",
                        "number": "001",
                        "orderApplicationConfig": null,
                        "phone": "-",
                        "plan": [
                            "PLAN"
                        ],
                        "regionId": "000000000000000000000000",
                        "sendOrder": 48,
                        "shortName": "-",
                        "storehouseId": [
                            "67bc2c123c83c5a3704837a6"
                        ],
                        "storehouseName": [
                            "仓库1"
                        ],
                        "supplyPlanId": "mock_100",
                        "supplyRatio": false,
                        "time_zone": "UTC+8",
                        "tz": "Asia/Shanghai"
                    },
                    {
                        "active": 1,
                        "address": "-",
                        "aliSiteId": "-",
                        "batchGroupAlarm": null,
                        "batchGroupAlarmOpen": false,
                        "cn": "示例医院B",
                        "contactGroup": [],
                        "contacts": "-",
                        "country": [
                            "CHN",
                            "23",
                            "6"
                        ],
                        "deleted": 1,
                        "dmp_id": "000000000000000000000000",
                        "email": "-",
                        "en": "示例医院B",
                        "id": "mock_007",
                        "location": "UTC+08:00",
                        "name": "示例医院B",
                        "number": "002",
                        "orderApplicationConfig": null,
                        "phone": "-",
                        "plan": [
                            "PLAN"
                        ],
                        "regionId": "000000000000000000000000",
                        "sendOrder": 32,
                        "shortName": "-",
                        "storehouseId": [
                            "64f29ac5fb97161fd42a6645"
                        ],
                        "storehouseName": [
                            "仓库2"
                        ],
                        "supplyPlanId": "mock_100",
                        "supplyRatio": false,
                        "time_zone": "UTC+8",
                        "tz": "Asia/Shanghai"
                    }
                ],
                "total": 2
            }
        }
    },
    {
        "method": "GET",
        "path": "/supply-plan/medicine",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_101",
                    "customerId": "mock_009",
                    "projectId": "mock_010",
                    "envId": "mock_001",
                    "supplyPlanId": "mock_100",
                    "info": {
                        "medicineName": "对照药",
                        "isBlind": true,
                        "warning": 20,
                        "buffer": 49,
                        "forecastMin": 0,
                        "forecastMax": 12,
                        "secondSupply": 2,
                        "initSupply": 0,
                        "unDistributionDate": 0,
                        "notCountedDate": 0,
                        "unProvideDate": 0,
                        "validityReminder": 0,
                        "autoSupply": false,
                        "autoSupplySize": [
                            2
                        ],
                        "supplyMode": 4,
                        "dispensingAlarm": 12
                    }
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/supply-plan/site",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "bindSupplyPlan": {
                    "id": "mock_100",
                    "name": "PLAN"
                },
                "optionalSupplyPlan": [
                    {
                        "id": "mock_100",
                        "name": "PLAN"
                    },
                    {
                        "id": "mock_102",
                        "name": "PLan2"
                    },
                    {
                        "id": "mock_103",
                        "name": "Plan3"
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/barcodes/config",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "id": "mock_104",
                "customerId": "mock_009",
                "projectId": "mock_010",
                "envId": "mock_001",
                "cohortId": "000000000000000000000000",
                "codeRule": 0,
                "codeConfigInit": true
            }
        }
    },
    {
        "method": "GET",
        "path": "randomization/attribute/randomization/type",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": 1
        }
    },
    {
        "method": "GET",
        "path": "randomization/configure",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "type": 1,
                "groups": [
                    {
                        "id": "mock_105",
                        "code": "实验组",
                        "name": "实验组",
                        "ratio": 0,
                        "description": "",
                        "segmentLength": 2,
                        "subGroup": null,
                        "status": 1,
                        "used": true,
                        "isCopyData": false
                    },
                    {
                        "id": "mock_106",
                        "code": "对照组",
                        "name": "对照组",
                        "ratio": 0,
                        "description": "",
                        "segmentLength": 2,
                        "subGroup": null,
                        "status": 1,
                        "used": true,
                        "isCopyData": false
                    },
                ],
                "factors": [
                    {
                        "id": "mock_108",
                        "number": "TEST-0225",
                        "name": "factor0",
                        "label": "研究产品",
                        "customFormulas": "",
                        "type": "select",
                        "dateFormat": null,
                        "precision": null,
                        "round": 0,
                        "isCalc": false,
                        "calcType": null,
                        "options": [
                            {
                                "label": "1",
                                "value": "1",
                                "formula": null,
                                "disable": false,
                                "isCopyData": false
                            }
                        ],
                        "modifiable": false,
                        "status": 1,
                        "ratio": 0,
                        "isCopyData": false
                    }
                ],
                "ratio": 0,
                "combination": null,
                "blockNumber": 21
            }
        }
    },
    {
        "method": "GET",
        "path": "/simulate-random/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_109",
                    "cohortId": "000000000000000000000000",
                    "cohort": {
                        "id": "000000000000000000000000",
                        "lastId": "000000000000000000000000",
                        "type": 0,
                        "name": "",
                        "capacity": 0,
                        "reminderThresholds": null,
                        "alertThresholds": null,
                        "factor": "",
                        "status": 0,
                        "copyFrom": "000000000000000000000000"
                    },
                    "name": "A",
                    "randomListIds": [
                        "mock_011"
                    ],
                    "simulateRandomList": [
                        "A"
                    ],
                    "siteQuantity": 3,
                    "countryQuantity": 1,
                    "regionQuantity": 2,
                    "runQuantity": 4,
                    "subjectQuantity": 4,
                    "groupCounts": null,
                    "simulationRandomResult": {
                        "avgSDOverview": {
                            "projectOverView": {
                                "name": "0816",
                                "avgSDs": [
                                    {
                                        "group": "实验组",
                                        "avg": "2.00",
                                        "sd": "0.00"
                                    },
                                    {
                                        "group": "对照组",
                                        "avg": "2.00",
                                        "sd": "0.00"
                                    },
                                    {
                                        "group": "2",
                                        "avg": "0.00",
                                        "sd": "0.00"
                                    }
                                ]
                            },
                            "siteOverView": [
                                {
                                    "name": "site_1",
                                    "avgSDs": [
                                        {
                                            "group": "实验组",
                                            "avg": "1.00",
                                            "sd": "1.00"
                                        },
                                        {
                                            "group": "对照组",
                                            "avg": "0.75",
                                            "sd": "0.83"
                                        },
                                        {
                                            "group": "2",
                                            "avg": "0.00",
                                            "sd": "0.00"
                                        }
                                    ]
                                },
                                {
                                    "name": "site_2",
                                    "avgSDs": [
                                        {
                                            "group": "实验组",
                                            "avg": "1.00",
                                            "sd": "1.00"
                                        },
                                        {
                                            "group": "对照组",
                                            "avg": "0.50",
                                            "sd": "0.87"
                                        },
                                        {
                                            "group": "2",
                                            "avg": "0.00",
                                            "sd": "0.00"
                                        }
                                    ]
                                },
                                {
                                    "name": "site_3",
                                    "avgSDs": [
                                        {
                                            "group": "实验组",
                                            "avg": "0.00",
                                            "sd": "0.00"
                                        },
                                        {
                                            "group": "对照组",
                                            "avg": "0.75",
                                            "sd": "0.83"
                                        },
                                        {
                                            "group": "2",
                                            "avg": "0.00",
                                            "sd": "0.00"
                                        }
                                    ]
                                }
                            ],
                            "regionOverView": [
                                {
                                    "name": "region_1",
                                    "avgSDs": [
                                        {
                                            "group": "实验组",
                                            "avg": "2.00",
                                            "sd": "0.00"
                                        },
                                        {
                                            "group": "对照组",
                                            "avg": "1.25",
                                            "sd": "0.83"
                                        },
                                        {
                                            "group": "2",
                                            "avg": "0.00",
                                            "sd": "0.00"
                                        }
                                    ]
                                },
                                {
                                    "name": "region_2",
                                    "avgSDs": [
                                        {
                                            "group": "实验组",
                                            "avg": "0.00",
                                            "sd": "0.00"
                                        },
                                        {
                                            "group": "对照组",
                                            "avg": "0.75",
                                            "sd": "0.83"
                                        },
                                        {
                                            "group": "2",
                                            "avg": "0.00",
                                            "sd": "0.00"
                                        }
                                    ]
                                }
                            ],
                            "countryOverView": [
                                {
                                    "name": "country_1",
                                    "avgSDs": [
                                        {
                                            "group": "实验组",
                                            "avg": "2.00",
                                            "sd": "0.00"
                                        },
                                        {
                                            "group": "对照组",
                                            "avg": "2.00",
                                            "sd": "0.00"
                                        },
                                        {
                                            "group": "2",
                                            "avg": "0.00",
                                            "sd": "0.00"
                                        }
                                    ]
                                }
                            ],
                            "factorOverView": [
                                {
                                    "name": "研究产品(1)",
                                    "avgSDs": [
                                        {
                                            "group": "实验组",
                                            "avg": "0.00",
                                            "sd": "0.00"
                                        },
                                        {
                                            "group": "对照组",
                                            "avg": "0.00",
                                            "sd": "0.00"
                                        },
                                        {
                                            "group": "2",
                                            "avg": "0.00",
                                            "sd": "0.00"
                                        }
                                    ]
                                }
                            ],
                            "combinationFactorOverView": []
                        },
                        "minOverview": {
                            "projectOverView": {
                                "name": "0816",
                                "mins": [
                                    {
                                        "group": "实验组",
                                        "min": 2
                                    },
                                    {
                                        "group": "对照组",
                                        "min": 2
                                    },
                                    {
                                        "group": "2",
                                        "min": 0
                                    }
                                ]
                            },
                            "siteOverView": [
                                {
                                    "name": "site_1",
                                    "mins": [
                                        {
                                            "group": "实验组",
                                            "min": 0
                                        },
                                        {
                                            "group": "对照组",
                                            "min": 0
                                        },
                                        {
                                            "group": "2",
                                            "min": 0
                                        }
                                    ]
                                },
                                {
                                    "name": "site_2",
                                    "mins": [
                                        {
                                            "group": "实验组",
                                            "min": 0
                                        },
                                        {
                                            "group": "对照组",
                                            "min": 0
                                        },
                                        {
                                            "group": "2",
                                            "min": 0
                                        }
                                    ]
                                },
                                {
                                    "name": "site_3",
                                    "mins": [
                                        {
                                            "group": "实验组",
                                            "min": 0
                                        },
                                        {
                                            "group": "对照组",
                                            "min": 0
                                        },
                                        {
                                            "group": "2",
                                            "min": 0
                                        }
                                    ]
                                }
                            ],
                            "regionOverView": [
                                {
                                    "name": "region_1",
                                    "mins": [
                                        {
                                            "group": "实验组",
                                            "min": 2
                                        },
                                        {
                                            "group": "对照组",
                                            "min": 0
                                        },
                                        {
                                            "group": "2",
                                            "min": 0
                                        }
                                    ]
                                },
                                {
                                    "name": "region_2",
                                    "mins": [
                                        {
                                            "group": "实验组",
                                            "min": 0
                                        },
                                        {
                                            "group": "对照组",
                                            "min": 0
                                        },
                                        {
                                            "group": "2",
                                            "min": 0
                                        }
                                    ]
                                }
                            ],
                            "countryOverView": [
                                {
                                    "name": "country_1",
                                    "mins": [
                                        {
                                            "group": "实验组",
                                            "min": 2
                                        },
                                        {
                                            "group": "对照组",
                                            "min": 2
                                        },
                                        {
                                            "group": "2",
                                            "min": 0
                                        }
                                    ]
                                }
                            ],
                            "factorOverView": [
                                {
                                    "name": "研究产品(1)",
                                    "mins": [
                                        {
                                            "group": "实验组",
                                            "min": 0
                                        },
                                        {
                                            "group": "对照组",
                                            "min": 0
                                        },
                                        {
                                            "group": "2",
                                            "min": 0
                                        }
                                    ]
                                }
                            ],
                            "combinationFactorOverView": []
                        },
                        "unbalancedRunCountOverview": {
                            "projectOverView": {
                                "name": "0816",
                                "count": 4
                            },
                            "siteOverView": [
                                {
                                    "name": "site_1",
                                    "count": 0
                                },
                                {
                                    "name": "site_2",
                                    "count": 0
                                },
                                {
                                    "name": "site_3",
                                    "count": 0
                                }
                            ],
                            "regionOverView": [
                                {
                                    "name": "region_1",
                                    "count": 0
                                },
                                {
                                    "name": "region_2",
                                    "count": 0
                                }
                            ],
                            "countryOverView": [
                                {
                                    "name": "country_1",
                                    "count": 0
                                }
                            ],
                            "factorOverView": [
                                {
                                    "name": "研究产品(1)",
                                    "count": 0
                                }
                            ],
                            "combinationFactorOverView": []
                        },
                        "unbalancedDetails": [
                            {
                                "run": 1,
                                "groupCounts": [
                                    {
                                        "group": "实验组",
                                        "run": 1,
                                        "count": 2
                                    },
                                    {
                                        "group": "对照组",
                                        "run": 1,
                                        "count": 2
                                    },
                                    {
                                        "group": "2",
                                        "run": 1,
                                        "count": 0
                                    }
                                ],
                                "groups": [
                                    "实验组",
                                    "对照组",
                                    "2"
                                ],
                                "projectDetail": {
                                    "name": "0816",
                                    "unbalanceds": [
                                        {
                                            "group": "实验组",
                                            "count": 2,
                                            "unbalanced": "2.0"
                                        },
                                        {
                                            "group": "对照组",
                                            "count": 2,
                                            "unbalanced": "2.0"
                                        },
                                        {
                                            "group": "2",
                                            "count": 0,
                                            "unbalanced": "4.0"
                                        }
                                    ],
                                    "total": 4
                                },
                                "siteDetail": [
                                    {
                                        "name": "site_1",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 0
                                    },
                                    {
                                        "name": "site_2",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 4
                                    },
                                    {
                                        "name": "site_3",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 0
                                    }
                                ],
                                "regionDetail": [
                                    {
                                        "name": "region_1",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 4
                                    },
                                    {
                                        "name": "region_2",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 0
                                    }
                                ],
                                "countryDetail": [
                                    {
                                        "name": "country_1",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 4
                                    }
                                ],
                                "factorDetail": [
                                    {
                                        "name": "研究产品(1)",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 0
                                    }
                                ],
                                "combinationFactorDetail": []
                            },
                            {
                                "run": 2,
                                "groupCounts": [
                                    {
                                        "group": "实验组",
                                        "run": 2,
                                        "count": 2
                                    },
                                    {
                                        "group": "对照组",
                                        "run": 2,
                                        "count": 2
                                    },
                                    {
                                        "group": "2",
                                        "run": 2,
                                        "count": 0
                                    }
                                ],
                                "groups": [
                                    "实验组",
                                    "对照组",
                                    "2"
                                ],
                                "projectDetail": {
                                    "name": "0816",
                                    "unbalanceds": [
                                        {
                                            "group": "实验组",
                                            "count": 2,
                                            "unbalanced": "2.0"
                                        },
                                        {
                                            "group": "对照组",
                                            "count": 2,
                                            "unbalanced": "2.0"
                                        },
                                        {
                                            "group": "2",
                                            "count": 0,
                                            "unbalanced": "4.0"
                                        }
                                    ],
                                    "total": 4
                                },
                                "siteDetail": [
                                    {
                                        "name": "site_1",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 2,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 2
                                    },
                                    {
                                        "name": "site_2",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 2,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 2
                                    },
                                    {
                                        "name": "site_3",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 0
                                    }
                                ],
                                "regionDetail": [
                                    {
                                        "name": "region_1",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 4
                                    },
                                    {
                                        "name": "region_2",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 0
                                    }
                                ],
                                "countryDetail": [
                                    {
                                        "name": "country_1",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 4
                                    }
                                ],
                                "factorDetail": [
                                    {
                                        "name": "研究产品(1)",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 0
                                    }
                                ],
                                "combinationFactorDetail": []
                            },
                            {
                                "run": 3,
                                "groupCounts": [
                                    {
                                        "group": "实验组",
                                        "run": 3,
                                        "count": 2
                                    },
                                    {
                                        "group": "对照组",
                                        "run": 3,
                                        "count": 2
                                    },
                                    {
                                        "group": "2",
                                        "run": 3,
                                        "count": 0
                                    }
                                ],
                                "groups": [
                                    "实验组",
                                    "对照组",
                                    "2"
                                ],
                                "projectDetail": {
                                    "name": "0816",
                                    "unbalanceds": [
                                        {
                                            "group": "实验组",
                                            "count": 2,
                                            "unbalanced": "2.0"
                                        },
                                        {
                                            "group": "对照组",
                                            "count": 2,
                                            "unbalanced": "2.0"
                                        },
                                        {
                                            "group": "2",
                                            "count": 0,
                                            "unbalanced": "4.0"
                                        }
                                    ],
                                    "total": 4
                                },
                                "siteDetail": [
                                    {
                                        "name": "site_1",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 2,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 2
                                    },
                                    {
                                        "name": "site_2",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 0
                                    },
                                    {
                                        "name": "site_3",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 2,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 2
                                    }
                                ],
                                "regionDetail": [
                                    {
                                        "name": "region_1",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 2,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 2
                                    },
                                    {
                                        "name": "region_2",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 2,
                                                "unbalanced": "1.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 2
                                    }
                                ],
                                "countryDetail": [
                                    {
                                        "name": "country_1",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 2,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 4
                                    }
                                ],
                                "factorDetail": [
                                    {
                                        "name": "研究产品(1)",
                                        "unbalanceds": [
                                            {
                                                "group": "实验组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "对照组",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            },
                                            {
                                                "group": "2",
                                                "count": 0,
                                                "unbalanced": "0.0"
                                            }
                                        ],
                                        "total": 0
                                    }
                                ],
                                "combinationFactorDetail": []
                            }
                        ],
                        "groups": [
                            "实验组",
                            "对照组",
                            "2"
                        ]
                    },
                    "onlyId": "RjYyQ0U4NzUwOThEMjkzOUMzRTMwMjNG",
                    "factorRatio": null
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "randomization/random/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "_id": "mock_011",
                    "cohort_id": "000000000000000000000000",
                    "config": {
                        "block_size": 0,
                        "block_sizes": null,
                        "blocks": [
                            {
                                "blocklength": 4,
                                "blocknumber": 20
                            }
                        ],
                        "blocks_rule": 1,
                        "check_other_factor": false,
                        "check_site": false,
                        "end_value": 0,
                        "initial_value": 1,
                        "number_length": 5,
                        "prefix": "A",
                        "probability": 0,
                        "rule": 0,
                        "seed": 4,
                        "site_allocation": false,
                        "total": 0
                    },
                    "count": 80,
                    "customer_id": "mock_009",
                    "design": {
                        "block_number": 0,
                        "combination": null,
                        "factors": [],
                        "groups": [
                            {
                                "blind": false,
                                "code": "实验组",
                                "description": "",
                                "id": "mock_105",
                                "name": "实验组",
                                "par_name": "实验组",
                                "ratio": 1,
                                "segment_length": 0,
                                "sub_name": ""
                            },
                            {
                                "blind": false,
                                "code": "对照组",
                                "description": "",
                                "id": "mock_106",
                                "name": "对照组",
                                "par_name": "对照组",
                                "ratio": 1,
                                "segment_length": 0,
                                "sub_name": ""
                            }
                        ],
                        "ratio": 0,
                        "type": 1
                    },
                    "env_id": "mock_001",
                    "last_group": "",
                    "meta": {
                        "createdUser": [
                            {
                                "_id": "mock_005",
                                "admin": true,
                                "app_language": "zh-CN",
                                "cloud_id": "mock_016",
                                "deleted": false,
                                "group": 1,
                                "info": {
                                    "company": "EDC",
                                    "description": "",
                                    "email": "<EMAIL>",
                                    "name": "demo.user",
                                    "phone": "",
                                    "status": 1,
                                    "unicode": 100012,
                                    "version": 19
                                },
                                "projectadmin": true,
                                "registration_id": "1104a8979399595f069",
                                "roles": [
                                    "mock_077",
                                    "mock_078",
                                    "mock_079"
                                ],
                                "unicode": 100012
                            }
                        ],
                        "created_at": 1694318191,
                        "created_by": "mock_005",
                        "updated_at": 0,
                        "updated_by": "000000000000000000000000"
                    },
                    "name": "A",
                    "projectSiteData": null,
                    "project_id": "mock_010",
                    "site_ids": null,
                    "status": 1,
                    "use": 35,
                    "version": 0
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "randomization/region",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": []
        }
    },
    {
        "method": "GET",
        "path": "/randomization/form",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "id": "mock_014",
                "customerId": "mock_009",
                "projectId": "mock_010",
                "envId": "mock_001",
                "cohortId": "000000000000000000000000",
                "fields": [
                    {
                        "id": "mock_043",
                        "cohortId": "000000000000000000000000",
                        "cohortName": "",
                        "isCalc": false,
                        "calcType": null,
                        "name": "field0",
                        "label": "身高表单",
                        "labelNation": null,
                        "labelEn": "",
                        "customFormulas": "",
                        "precision": null,
                        "round": 0,
                        "type": "inputNumber",
                        "status": 1,
                        "applicationType": 2,
                        "variable": "height",
                        "used": true,
                        "options": null,
                        "list": false,
                        "modifiable": false,
                        "required": false,
                        "stratification": false,
                        "digit": 0,
                        "accuracy": 0,
                        "dateFormat": "",
                        "formatType": "numberLength",
                        "timeFormat": "",
                        "length": 20,
                        "range": null,
                        "dateRange": null,
                        "isCustomFormula": false
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "randomization/attribute/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_012",
                    "customerId": "mock_009",
                    "projectId": "mock_010",
                    "envId": "mock_001",
                    "cohortId": "000000000000000000000000",
                    "info": {
                        "random": true,
                        "randomControl": false,
                        "randomControlRule": 0,
                        "randomControlGroup": 0,
                        "isRandomNumber": true,
                        "isRandomSequenceNumber": false,
                        "randomSequenceNumberPrefix": "",
                        "randomSequenceNumberDigit": 0,
                        "randomSequenceNumberStart": 0,
                        "dispensing": true,
                        "dtpRule": 1,
                        "blind": true,
                        "countryLayered": false,
                        "instituteLayered": true,
                        "regionLayered": false,
                        "prefix": true,
                        "subjectNumberRule": 1,
                        "prefixExpression": "",
                        "subjectReplaceText": "受试者编号",
                        "subjectReplaceTextEn": "",
                        "digit": 7,
                        "accuracy": 1,
                        "field": {
                            "id": "mock_013",
                            "cohortId": "000000000000000000000000",
                            "cohortName": "",
                            "isCalc": false,
                            "calcType": null,
                            "name": "shortname",
                            "label": "受试者编号",
                            "labelNation": null,
                            "labelEn": "",
                            "customFormulas": "",
                            "precision": null,
                            "round": 0,
                            "type": "input",
                            "status": null,
                            "applicationType": null,
                            "variable": "",
                            "used": false,
                            "options": null,
                            "list": false,
                            "modifiable": true,
                            "required": false,
                            "stratification": false,
                            "digit": 7,
                            "accuracy": 1,
                            "dateFormat": "",
                            "formatType": "",
                            "timeFormat": "",
                            "length": null,
                            "range": null,
                            "dateRange": null,
                            "isCustomFormula": false
                        },
                        "prefixSymbol": "",
                        "isFreeze": false,
                        "isRandom": false,
                        "isCountryRandom": false,
                        "isRegionRandom": false,
                        "edcDrugConfigLabel": "",
                        "segment": true,
                        "segmentType": 0,
                        "unblindingReasonConfig": [
                            {
                                "reason": "SAE",
                                "allowRemark": false
                            },
                            {
                                "reason": "妊娠",
                                "allowRemark": false
                            },
                            {
                                "reason": "政策要求",
                                "allowRemark": false
                            }
                        ],
                        "freezeReasonConfig": null,
                        "blindingRestrictions": true,
                        "pvUnBlindingRestrictions": true,
                        "replaceRule": 0,
                        "replaceRuleNumber": 0,
                        "isScreen": false,
                        "connectAli": false,
                        "aliProjectNo": "",
                        "allowReplace": true,
                        "allowRegisterGroup": true,
                        "minimizeCalc": 0,
                        "IPInheritance": true,
                        "remainingVisit": 111,
                        "codeRule": 0,
                        "codeConfigInit": false
                    }
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/group",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "key": 0,
                    "salt": null,
                    "saltName": null,
                    "group": "N/A",
                    "subSalt": null,
                    "subSaltName": null,
                    "subGroup": "",
                    "all_salt_name": "Ti9B"
                },
                {
                    "key": 1,
                    "salt": null,
                    "saltName": null,
                    "group": "实验组",
                    "subSalt": null,
                    "subSaltName": null,
                    "subGroup": "",
                    "all_salt_name": "5a6e6aqM57uE"
                },
                {
                    "key": 2,
                    "salt": null,
                    "saltName": null,
                    "group": "对照组",
                    "subSalt": null,
                    "subSaltName": null,
                    "subGroup": "",
                    "all_salt_name": "5a+554Wn57uE"
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/judge/group",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": ""
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/visit",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "id": "mock_029",
                "customerId": "mock_009",
                "projectId": "mock_010",
                "envId": "mock_001",
                "cohortId": "000000000000000000000000",
                "setInfo": {
                    "isOpen": true,
                    "id": "mock_030",
                    "nameZh": "计划外自定义",
                    "nameEn": "outSizeVisit"
                },
                "visit_type": 0,
                "baseCohort": "000000000000000000000000",
                "version": "0416",
                "infos": [
                    {
                        "id": "mock_031",
                        "number": "V1",
                        "name": "V1",
                        "random": true,
                        "dispensing": true,
                        "dtp": false,
                        "replace": false,
                        "doseAdjustment": false,
                        "DTPType": null,
                        "group": [
                            "N/A",
                            "实验组",
                            "对照组"
                        ],
                        "interval": null,
                        "unit": "d",
                        "PeriodMin": null,
                        "periodMax": null,
                        "startDays": 0,
                        "endDays": 0,
                        "isUsed": false,
                        "isCopyEditDelete": false
                    },
                    {
                        "id": "mock_032",
                        "number": "V2",
                        "name": "V2",
                        "random": false,
                        "dispensing": true,
                        "dtp": true,
                        "replace": false,
                        "doseAdjustment": true,
                        "DTPType": [
                            2,
                            0
                        ],
                        "group": [
                            "N/A",
                            "实验组",
                            "对照组"
                        ],
                        "interval": 30,
                        "unit": "d",
                        "PeriodMin": -2,
                        "periodMax": 1,
                        "startDays": 0,
                        "endDays": 0,
                        "isUsed": false,
                        "isCopyEditDelete": false
                    },
                    {
                        "id": "mock_033",
                        "number": "V3",
                        "name": "V33",
                        "random": false,
                        "dispensing": true,
                        "dtp": true,
                        "replace": false,
                        "doseAdjustment": true,
                        "DTPType": [
                            2,
                            1,
                            0
                        ],
                        "group": [
                            "N/A",
                            "实验组",
                            "对照组"
                        ],
                        "interval": null,
                        "unit": "d",
                        "PeriodMin": null,
                        "periodMax": null,
                        "startDays": 0,
                        "endDays": 0,
                        "isUsed": false,
                        "isCopyEditDelete": false
                    }
                ],
                "update_infos": {
                    "visit_type": 0,
                    "baseCohort": "000000000000000000000000",
                    "version": "",
                    "infos": [
                        {
                            "id": "mock_031",
                            "number": "V1",
                            "name": "V1",
                            "random": true,
                            "dispensing": true,
                            "dtp": false,
                            "replace": false,
                            "doseAdjustment": false,
                            "DTPType": null,
                            "group": [
                                "N/A",
                                "实验组",
                                "对照组"
                            ],
                            "interval": null,
                            "unit": "d",
                            "PeriodMin": null,
                            "periodMax": null,
                            "startDays": 0,
                            "endDays": 0,
                            "isUsed": false,
                            "isCopyEditDelete": false
                        },
                        {
                            "id": "mock_032",
                            "number": "V2",
                            "name": "V2",
                            "random": false,
                            "dispensing": true,
                            "dtp": true,
                            "replace": false,
                            "doseAdjustment": true,
                            "DTPType": [
                                2,
                                0
                            ],
                            "group": [
                                "N/A",
                                "实验组",
                                "对照组"
                            ],
                            "interval": 30,
                            "unit": "d",
                            "PeriodMin": -2,
                            "periodMax": 1,
                            "startDays": 0,
                            "endDays": 0,
                            "isUsed": false,
                            "isCopyEditDelete": false
                        },
                        {
                            "id": "mock_033",
                            "number": "V3",
                            "name": "V33",
                            "random": false,
                            "dispensing": true,
                            "dtp": true,
                            "replace": false,
                            "doseAdjustment": true,
                            "DTPType": [
                                2,
                                1,
                                0
                            ],
                            "group": [
                                "N/A",
                                "实验组",
                                "对照组"
                            ],
                            "interval": null,
                            "unit": "d",
                            "PeriodMin": null,
                            "periodMax": null,
                            "startDays": 0,
                            "endDays": 0,
                            "isUsed": false,
                            "isCopyEditDelete": false
                        }
                    ],
                    "createdAt": 1692151215,
                    "createdBy": "000000000000000000000000",
                    "updatedAt": 1744770717,
                    "updatedBy": "000000000000000000000000",
                    "deletedAt": 0,
                    "deletedBy": "000000000000000000000000"
                },
                "history_info": [
                    {
                        "visit_type": 0,
                        "baseCohort": "000000000000000000000000",
                        "version": "V1",
                        "infos": null,
                        "createdAt": 0,
                        "createdBy": "000000000000000000000000",
                        "updatedAt": 0,
                        "updatedBy": "000000000000000000000000",
                        "deletedAt": 0,
                        "deletedBy": "000000000000000000000000"
                    },
                    {
                        "visit_type": 0,
                        "baseCohort": "000000000000000000000000",
                        "version": "V2",
                        "infos": [
                            {
                                "id": "mock_031",
                                "number": "V1",
                                "name": "V1",
                                "random": false,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": [
                                    "N/A"
                                ],
                                "interval": 1,
                                "unit": "d",
                                "PeriodMin": null,
                                "periodMax": null,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_032",
                                "number": "V2",
                                "name": "V2",
                                "random": false,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": [
                                    "N/A"
                                ],
                                "interval": 1,
                                "unit": "d",
                                "PeriodMin": null,
                                "periodMax": null,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            }
                        ],
                        "createdAt": 1692152046,
                        "createdBy": "000000000000000000000000",
                        "updatedAt": 1694318103,
                        "updatedBy": "000000000000000000000000",
                        "deletedAt": 0,
                        "deletedBy": "000000000000000000000000"
                    },
                    {
                        "visit_type": 0,
                        "baseCohort": "000000000000000000000000",
                        "version": "V3",
                        "infos": [
                            {
                                "id": "mock_031",
                                "number": "V1",
                                "name": "V1",
                                "random": false,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": [
                                    "N/A"
                                ],
                                "interval": 1,
                                "unit": "d",
                                "PeriodMin": null,
                                "periodMax": null,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_032",
                                "number": "V2",
                                "name": "V2",
                                "random": false,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": [
                                    "N/A"
                                ],
                                "interval": 1,
                                "unit": "d",
                                "PeriodMin": null,
                                "periodMax": null,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_034",
                                "number": "V3",
                                "name": "V3",
                                "random": true,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": [
                                    "N/A"
                                ],
                                "interval": null,
                                "unit": "d",
                                "PeriodMin": null,
                                "periodMax": null,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            }
                        ],
                        "createdAt": 1693618369,
                        "createdBy": "000000000000000000000000",
                        "updatedAt": 1694318247,
                        "updatedBy": "000000000000000000000000",
                        "deletedAt": 0,
                        "deletedBy": "000000000000000000000000"
                    }
                ],
                "createdAt": 1744770717,
                "createdBy": "000000000000000000000000",
                "updatedAt": 1744770729,
                "updatedBy": "000000000000000000000000",
                "deletedAt": 0,
                "deletedBy": "000000000000000000000000"
            }
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/judge/group",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": ""
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/visit/settings",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "isOpen": true,
                "id": "mock_030",
                "nameZh": "计划外自定义",
                "nameEn": "outSizeVisit"
            }
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/settings/dtpRule",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "type": 1,
                "drugNameList": [
                    "xxxddd",
                    "体重药",
                    "A"
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "id": "mock_110",
                "customerId": "mock_009",
                "projectId": "mock_010",
                "envId": "mock_001",
                "cohortId": "000000000000000000000000",
                "configures": [
                    {
                        "id": "mock_048",
                        "group": "实验组",
                        "parName": "实验组",
                        "subName": "",
                        "values": [
                            {
                                "drugName": "xxxddd",
                                "dispensingNumber": 10,
                                "drugSpec": "mg",
                                "pkgSpec": "",
                                "customDispensingNumber": "1~10",
                                "isOther": true,
                                "isCount": true,
                                "isOpen": false,
                                "calculationInfo": {
                                    "comparisonSwitch": false,
                                    "comparisonType": null,
                                    "comparisonRatio": null,
                                    "currentComparisonType": null,
                                    "keepDecimal": false,
                                    "precision": null,
                                    "unitCalculationStandard": 60,
                                    "specifications": {
                                        "value": 37.66,
                                        "unit": "mg"
                                    },
                                    "formulas": [
                                        {
                                            "expression": "X<100",
                                            "value": 1
                                        }
                                    ],
                                    "comparisonSymbols": 0
                                },
                                "label": "",
                                "automaticRecode": true,
                                "automaticRecodeSpec": 2,
                                "isCopyEditDelete": false
                            }
                        ],
                        "visitCycles": [
                            "mock_031",
                            "mock_032",
                            "mock_111"
                        ],
                        "label": "实验药",
                        "roomNumbers": null,
                        "openSetting": 1,
                        "calculationType": 0,
                        "customerCalculation": "{height}*2",
                        "isFormula": true,
                        "keepDecimal": 0,
                        "customerCalculationSpec": "盒",
                        "onlyId": "NDZDQkY1NTNEOUZCRDBCRUIxNkFDRjY5",
                        "isCopyEditDelete": false,
                        "routineVisitMappingList": [],
                        "showAll": false
                    },
                    {
                        "id": "mock_112",
                        "group": "对照组",
                        "parName": "对照组",
                        "subName": "",
                        "values": [
                            {
                                "drugName": "体重药",
                                "dispensingNumber": 200,
                                "drugSpec": "盒",
                                "pkgSpec": "",
                                "customDispensingNumber": "2~200",
                                "isOther": false,
                                "isCount": true,
                                "isOpen": true,
                                "calculationInfo": {
                                    "comparisonSwitch": false,
                                    "comparisonType": null,
                                    "comparisonRatio": null,
                                    "currentComparisonType": null,
                                    "keepDecimal": false,
                                    "precision": null,
                                    "unitCalculationStandard": 30,
                                    "specifications": {
                                        "value": 30,
                                        "unit": "mg"
                                    },
                                    "formulas": [],
                                    "comparisonSymbols": 0
                                },
                                "label": "",
                                "automaticRecode": false,
                                "automaticRecodeSpec": null,
                                "isCopyEditDelete": false
                            }
                        ],
                        "visitCycles": [
                            "mock_031",
                            "mock_032",
                            "mock_033"
                        ],
                        "label": "",
                        "roomNumbers": null,
                        "openSetting": 2,
                        "calculationType": 0,
                        "customerCalculation": "",
                        "isFormula": false,
                        "keepDecimal": null,
                        "customerCalculationSpec": "",
                        "onlyId": "MjYzMzlENjkzOTQ0M0JCNDA3MTcwNDQ5",
                        "isCopyEditDelete": false,
                        "routineVisitMappingList": [],
                        "showAll": false
                    },
                    {
                        "id": "mock_113",
                        "group": "对照组",
                        "parName": "对照组",
                        "subName": "",
                        "values": [
                            {
                                "drugName": "A",
                                "dispensingNumber": 100,
                                "drugSpec": "mg",
                                "pkgSpec": "",
                                "customDispensingNumber": "1~100",
                                "isOther": false,
                                "isCount": true,
                                "isOpen": false,
                                "calculationInfo": {
                                    "comparisonSwitch": false,
                                    "comparisonType": null,
                                    "comparisonRatio": null,
                                    "currentComparisonType": null,
                                    "keepDecimal": false,
                                    "precision": null,
                                    "unitCalculationStandard": null,
                                    "specifications": {
                                        "value": null,
                                        "unit": "mg"
                                    },
                                    "formulas": [],
                                    "comparisonSymbols": 0
                                },
                                "label": "A",
                                "automaticRecode": false,
                                "automaticRecodeSpec": null,
                                "isCopyEditDelete": false
                            },
                            {
                                "drugName": "B",
                                "dispensingNumber": 100,
                                "drugSpec": "mg",
                                "pkgSpec": "",
                                "customDispensingNumber": "1~100",
                                "isOther": false,
                                "isCount": true,
                                "isOpen": false,
                                "calculationInfo": {
                                    "comparisonSwitch": false,
                                    "comparisonType": null,
                                    "comparisonRatio": null,
                                    "currentComparisonType": null,
                                    "keepDecimal": false,
                                    "precision": null,
                                    "unitCalculationStandard": null,
                                    "specifications": {
                                        "value": null,
                                        "unit": "mg"
                                    },
                                    "formulas": [],
                                    "comparisonSymbols": 0
                                },
                                "label": "B",
                                "automaticRecode": false,
                                "automaticRecodeSpec": null,
                                "isCopyEditDelete": false
                            },
                            {
                                "drugName": "C",
                                "dispensingNumber": 100,
                                "drugSpec": "mg",
                                "pkgSpec": "",
                                "customDispensingNumber": "1~100",
                                "isOther": false,
                                "isCount": true,
                                "isOpen": false,
                                "calculationInfo": {
                                    "comparisonSwitch": false,
                                    "comparisonType": null,
                                    "comparisonRatio": null,
                                    "currentComparisonType": null,
                                    "keepDecimal": false,
                                    "precision": null,
                                    "unitCalculationStandard": null,
                                    "specifications": {
                                        "value": null,
                                        "unit": "mg"
                                    },
                                    "formulas": [],
                                    "comparisonSymbols": 0
                                },
                                "label": "C",
                                "automaticRecode": false,
                                "automaticRecodeSpec": null,
                                "isCopyEditDelete": false
                            }
                        ],
                        "visitCycles": [
                            "mock_031",
                            "mock_032",
                            "mock_111"
                        ],
                        "label": "",
                        "roomNumbers": null,
                        "openSetting": 1,
                        "calculationType": 0,
                        "customerCalculation": "",
                        "isFormula": false,
                        "keepDecimal": null,
                        "customerCalculationSpec": "",
                        "onlyId": "MzY1OEQ3NjBDOUU3NjhEQTkxNDM1MDA2",
                        "isCopyEditDelete": false,
                        "routineVisitMappingList": [],
                        "showAll": true
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/judge/label",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": ""
        }
    },
    {
        "method": "GET",
        "path": "/medicines",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "basicMovement": false,
                "items": [
                    {
                        "id": "mock_063",
                        "customerId": "mock_009",
                        "projectId": "mock_010",
                        "envId": "mock_001",
                        "name": "A",
                        "number": "A001",
                        "storehouseId": "000000000000000000000000",
                        "siteId": "mock_006",
                        "expirationDate": "2025-07-11",
                        "batchNumber": "A",
                        "spce": "mg",
                        "orderId": "mock_114",
                        "status": 5,
                        "isolationApprovalSign": 0,
                        "ttxSkuId": "",
                        "packageNumber": "",
                        "packageSerialNumber": "",
                        "packlist": null,
                        "subjectId": "000000000000000000000000",
                        "entryTime": 0,
                        "scanStatus": 0,
                        "serialNumber": "A001",
                        "shortCode": "",
                        "transferData": {
                            "encData": {
                                "fromLCname": "",
                                "fromLCnameID": "",
                                "toIWRSname": "",
                                "toIWRSID": "",
                                "operDate": "",
                                "drugAndCode": null
                            },
                            "signValue": "",
                            "trialProjectId": "000000000000000000000000"
                        }
                    },
                    {
                        "id": "mock_064",
                        "customerId": "mock_009",
                        "projectId": "mock_010",
                        "envId": "mock_001",
                        "name": "B",
                        "number": "A002",
                        "storehouseId": "000000000000000000000000",
                        "siteId": "mock_006",
                        "expirationDate": "2025-07-17",
                        "batchNumber": "A",
                        "spce": "mg",
                        "orderId": "mock_114",
                        "status": 6,
                        "isolationApprovalSign": 0,
                        "ttxSkuId": "",
                        "packageNumber": "",
                        "packageSerialNumber": "",
                        "packlist": null,
                        "subjectId": "mock_115",
                        "entryTime": 0,
                        "scanStatus": 0,
                        "serialNumber": "A002",
                        "shortCode": "",
                        "transferData": {
                            "encData": {
                                "fromLCname": "",
                                "fromLCnameID": "",
                                "toIWRSname": "",
                                "toIWRSID": "",
                                "operDate": "",
                                "drugAndCode": null
                            },
                            "signValue": "",
                            "trialProjectId": "000000000000000000000000"
                        }
                    },
                    {
                        "id": "mock_065",
                        "customerId": "mock_009",
                        "projectId": "mock_010",
                        "envId": "mock_001",
                        "name": "B",
                        "number": "A010",
                        "storehouseId": "000000000000000000000000",
                        "siteId": "mock_006",
                        "expirationDate": "2025-07-17",
                        "batchNumber": "A",
                        "spce": "mg",
                        "orderId": "mock_114",
                        "status": 14,
                        "isolationApprovalSign": 0,
                        "ttxSkuId": "",
                        "packageNumber": "",
                        "packageSerialNumber": "",
                        "packlist": null,
                        "subjectId": "mock_116",
                        "entryTime": 0,
                        "scanStatus": 0,
                        "serialNumber": "A010",
                        "shortCode": "",
                        "transferData": {
                            "encData": {
                                "fromLCname": "",
                                "fromLCnameID": "",
                                "toIWRSname": "",
                                "toIWRSID": "",
                                "operDate": "",
                                "drugAndCode": null
                            },
                            "signValue": "",
                            "trialProjectId": "000000000000000000000000"
                        }
                    }
                ],
                "packageIsOpen": true,
                "total": 3
            }
        }
    },
    {
        "method": "GET",
        "path": "/projects-storehouses/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "mock_023",
                    "name": "仓库1"
                },
                {
                    "id": "mock_025",
                    "name": "仓库2"
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/drug/configure/medicine-other",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "isOpenPackage": true,
                "items": [
                    {
                        "batchNumber": "AAA",
                        "count": 20,
                        "expirationDate": "2025-09-20",
                        "name": "未标号",
                        "orderIdsCount": 0,
                        "packageCount": 0,
                        "storehouseId": "mock_023"
                    },
                    {
                        "batchNumber": "B",
                        "count": 20,
                        "expirationDate": "2026-02-21",
                        "name": "未标号",
                        "orderIdsCount": 0,
                        "packageCount": 0,
                        "storehouseId": "mock_025"
                    },
                    {
                        "batchNumber": "CCC",
                        "count": 144,
                        "expirationDate": "2025-09-20",
                        "name": "未标号",
                        "orderIdsCount": 0,
                        "packageCount": 0,
                        "storehouseId": "mock_023"
                    }
                ],
                "total": 5
            }
        }
    },
    {
        "method": "POST",
        "path": "/medicines/batch/select",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": []
        }
    },
    {
        "method": "GET",
        "path": "/medicines/batch",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "expirationDate": "2024-09-07",
                        "batchNumber": "20230901",
                        "name": "-",
                        "position": "202409290003",
                        "positionId": "mock_117",
                        "count": 5,
                        "type": 2,
                        "orderType": 0,
                        "status": 6,
                        "isOther": false,
                        "isPackage": false,
                        "packageNumber": 0
                    },
                    {
                        "expirationDate": "2024-09-07",
                        "batchNumber": "20230902",
                        "name": "-",
                        "position": "仓库1",
                        "positionId": "mock_023",
                        "count": 80,
                        "type": 1,
                        "orderType": 0,
                        "status": 0,
                        "isOther": false,
                        "isPackage": false,
                        "packageNumber": 0
                    },
                    {
                        "expirationDate": "2025-12-19",
                        "batchNumber": "20230903",
                        "name": "-",
                        "position": "202409260017",
                        "positionId": "mock_118",
                        "count": 2,
                        "type": 3,
                        "orderType": 0,
                        "status": 2,
                        "isOther": false,
                        "isPackage": false,
                        "packageNumber": 0
                    }
                ],
                "total": 3
            }
        }
    },
    {
        "method": "PATCH",
        "path": "/drug/configure/visit/history",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "data": [
                    {
                        "visit_type": 0,
                        "baseCohort": "000000000000000000000000",
                        "version": "0416",
                        "infos": [
                            {
                                "id": "mock_031",
                                "number": "V1",
                                "name": "V1",
                                "random": true,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": ["Ti9B"],
                                "interval": 30,
                                "unit": "w",
                                "PeriodMin": -2,
                                "periodMax": 1,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_032",
                                "number": "V2",
                                "name": "V2",
                                "random": false,
                                "dispensing": true,
                                "dtp": true,
                                "replace": false,
                                "doseAdjustment": true,
                                "DTPType": [
                                    2,
                                    0
                                ],
                                "group": ["Ti9B",],
                                "interval": 30,
                                "unit": "d",
                                "PeriodMin": -2,
                                "periodMax": 1,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_033",
                                "number": "V3",
                                "name": "V3",
                                "random": false,
                                "dispensing": true,
                                "dtp": true,
                                "replace": false,
                                "doseAdjustment": true,
                                "DTPType": [2, 1, 0],
                                "group": ["Ti9B",],
                                "interval": 30,
                                "unit": "h",
                                "PeriodMin": -2,
                                "periodMax": 1,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_034",
                                "number": "V4",
                                "name": "V4",
                                "random": false,
                                "dispensing": true,
                                "dtp": true,
                                "replace": false,
                                "doseAdjustment": true,
                                "DTPType": [2, 1, 0],
                                "group": ["Ti9B",],
                                "interval": 30,
                                "unit": "m",
                                "PeriodMin": -2,
                                "periodMax": 1,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            }
                        ],
                        "createdAt": 1692151215,
                        "createdBy": "000000000000000000000000",
                        "updatedAt": 1744770729,
                        "updatedBy": "000000000000000000000000",
                        "deletedAt": 0,
                        "deletedBy": "000000000000000000000000"
                    },
                    {
                        "visit_type": 0,
                        "baseCohort": "000000000000000000000000",
                        "version": "0319",
                        "infos": [
                            {
                                "id": "mock_031",
                                "number": "V1",
                                "name": "V1",
                                "random": true,
                                "dispensing": true,
                                "dtp": false,
                                "replace": false,
                                "doseAdjustment": false,
                                "DTPType": null,
                                "group": ["Ti9B"],
                                "interval": 30,
                                "unit": "w",
                                "PeriodMin": -2,
                                "periodMax": 1,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_032",
                                "number": "V2",
                                "name": "V2",
                                "random": false,
                                "dispensing": true,
                                "dtp": true,
                                "replace": false,
                                "doseAdjustment": true,
                                "DTPType": [
                                    2,
                                    0
                                ],
                                "group": ["Ti9B",],
                                "interval": 30,
                                "unit": "d",
                                "PeriodMin": -2,
                                "periodMax": 1,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_033",
                                "number": "V3",
                                "name": "V3",
                                "random": false,
                                "dispensing": true,
                                "dtp": true,
                                "replace": false,
                                "doseAdjustment": true,
                                "DTPType": [2, 1, 0],
                                "group": ["Ti9B",],
                                "interval": 30,
                                "unit": "h",
                                "PeriodMin": -2,
                                "periodMax": 1,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            },
                            {
                                "id": "mock_034",
                                "number": "V4",
                                "name": "V4",
                                "random": false,
                                "dispensing": true,
                                "dtp": true,
                                "replace": false,
                                "doseAdjustment": true,
                                "DTPType": [2, 1, 0],
                                "group": ["Ti9B",],
                                "interval": 30,
                                "unit": "m",
                                "PeriodMin": -2,
                                "periodMax": 1,
                                "startDays": 0,
                                "endDays": 0,
                                "isUsed": false,
                                "isCopyEditDelete": false
                            }
                        ],
                        "createdAt": 1692151215,
                        "createdBy": "000000000000000000000000",
                        "updatedAt": 1742364855,
                        "updatedBy": "000000000000000000000000",
                        "deletedAt": 0,
                        "deletedBy": "000000000000000000000000"
                    },
                ],
                "total": 3
            }
        }
    },
    {
        "method": "GET",
        "path": "/supply-plan/",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "items": [
                    {
                        "allSite": true,
                        "autoSupply": null,
                        "description": "-",
                        "id": "mock_100",
                        "name": "Plan1",
                        "planControl": false,
                        "siteIds": [],
                        "siteWarning": null,
                        "status": 1,
                        "storehouse": "000000000000000000000000"
                    },
                    {
                        "allSite": true,
                        "autoSupply": [],
                        "description": "-",
                        "id": "mock_102",
                        "name": "PLan2",
                        "planControl": true,
                        "siteIds": [],
                        "siteWarning": [1],
                        "status": 0,
                        "storehouse": "000000000000000000000000"
                    },
                ],
                "total": 3
            }
        }
    },
    {
        "method": "GET",
        "path": "/supply-plan/medicine-configures",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "isBlind": false,
                    "name": "未编号"
                },
                {
                    "isBlind": true,
                    "name": "批次混合药"
                },
                {
                    "isBlind": false,
                    "name": "未编号2"
                }
            ]
        }
    },
    {
        "method": "GET",
        "path": "/notices/configs",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": []
        }
    },
    {
        "method": "GET",
        "path": "/projects/envs/users/list",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "list": [
                    {
                        "_id": "mock_059",
                        "app": true,
                        "closeCustomer": [],
                        "cloudId": "mock_016",
                        "createAt": 1692151141,
                        "depots": [],
                        "id": "mock_005",
                        "info": {
                            "company": "EDC",
                            "description": "",
                            "email": "<EMAIL>",
                            "name": "demo.user",
                            "phone": "",
                            "status": 1,
                            "unicode": 100012,
                            "version": 19
                        },
                        "roles": [{"_id": "mock_055", "name": "Sub-I [unblinded]", "scope": "study", "status": 1}],
                        "sites": [{"site_id": "mock_006"}],
                        "settings": {"timezone": "8", "tz": "Asia/Shanghai"},
                        "unblindingCode": {}
                    },
                    {
                        "_id": "mock_123",
                        "app": false,
                        "closeCustomer": [],
                        "cloudId": "mock_017",
                        "createAt": 1695275021,
                        "depots": [],
                        "id": "mock_017",
                        "info": {
                            "company": "",
                            "description": "",
                            "email": "<EMAIL>",
                            "name": "demo.user2",
                            "phone": "",
                            "status": 1,
                            "unicode": 100180,
                            "version": 355
                        },
                        "roles": [
                            {"_id": "mock_124", "name": "Biostatistician [blinded]", "scope": "study", "status": 2},
                            {"_id": "mock_125", "name": "CRC [blinded, can see room number temporarily]", "scope": "site", "status": 1},
                            {"_id": "mock_028", "name": "IP Officer", "scope": "study", "status": 1}
                        ],
                        "sites": [{"site_id": "mock_006"}],
                        "settings": {"timezone": "8", "tz": "Asia/Shanghai"},
                        "unbind": true,
                        "unblindingCode": {}
                    },
                    {
                        "_id": "mock_126",
                        "app": false,
                        "closeCustomer": [],
                        "cloudId": "mock_004",
                        "createAt": 1711612716,
                        "depots": [],
                        "id": "mock_004",
                        "info": {
                            "company": "",
                            "description": "",
                            "email": "<EMAIL>",
                            "name": "demo.user3",
                            "phone": "17688392073",
                            "status": 0,
                            "unicode": 100020,
                            "version": 7
                        },
                        "roles": [
                            {"_id": "mock_127", "name": "Project-Admin", "scope": "study", "status": 1},
                            {"_id": "mock_125", "name": "CRC [blinded, can see room number temporarily]", "scope": "site", "status": 1}
                        ],
                        "settings": {"timezone": "8", "tz": "Asia/Shanghai"},
                        "sites": [],
                        "unblindingCode": {}
                    }
                ],
                "total": 5
            }
        }
    },
    {
        "method": "GET",
        "path": "/push",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "afootCount": 0,
                "failCount": 0,
                "handleCount": 0,
                "items": Array.from({length:15}).map((it, index) => ({
                    "subjectNo": `${(index + 1).toString().padStart(4, "0")}`,
                    "sourceType": index,
                    "pushMode": (index % 2 === 0) ? 1 : 2,
                    "status": index % 5,
                    "sendTime": dayjs().unix(),
                    "count": 1
                })),
                "loseCount": 0,
                "successCount": 0,
                "total": 15
            }
        }
    },
    {
        "method": "GET",
        "path": "/barcodes/correlationIDs",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "datas":null
            }
        }
    },
    {
        "method": "GET",
        "path": "/barcodes/correlationID",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "datas": []
            }
        }
    },
    {
        "method": "GET",
        "path": "/barcodes",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "codeRule": 0,
                "datas": [{
                    correlationId: "0001",
                    storehouseName: "仓库1",
                    creatTime: dayjs().unix()
                }],
                "packageIsOpen": true,
                "total": 0
            }
        }
    },
    {
        "method": "GET",
        "path": "/barcode-label",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "data": [
                    {
                        "id": "6855123d75ed412442db8dcb",
                        "labelNumber": "L000001",
                        "correlationName": "",
                        "codeMethod": 1,
                        "productCount": 0,
                        "status": 1,
                    },
                    {
                        "id": "6855123d75ed412442db8dcv",
                        "labelNumber": "L000002",
                        "correlationName": "",
                        "codeMethod": 2,
                        "productCount": 0,
                        "status": 2,
                    }
                ],
                "total": 2,
                "allLabel": [
                    {
                        "_id": "6855123d75ed412442db8dcb",
                        "correlation_name": "",
                        "label_number": "L000001"
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/projects-roles/pool",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "001",
                    "type": 1,
                    "name": "IP Officer",
                    "scope": "study",
                    "description": "",
                    "template": 1,
                    "status": 1,
                },
                {
                    "id": "002",
                    "type": 2,
                    "name": "PI [blinded,can unblind]",
                    "scope": "site",
                    "description": "",
                    "template": 1,
                    "status": 1,
                },
                {
                    "id": "003",
                    "type": 3,
                    "name": "CRC [blinded, can see room number temporarily]",
                    "scope": "site",
                    "description": "",
                    "template": 2,
                    "status": 1,
                }

            ]
        }
    },
    {
        "method": "GET",
        "path": "/projects-roles",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "001",
                    "type": 1,
                    "name": "IP Officer",
                    "scope": "study",
                    "description": "",
                    "template": 1,
                    "status": 1,
                },
                {
                    "id": "002",
                    "type": 2,
                    "name": "PI [blinded,can unblind]",
                    "scope": "site",
                    "description": "",
                    "template": 1,
                    "status": 1,
                },
                {
                    "id": "003",
                    "type": 3,
                    "name": "CRC [blinded, can see room number temporarily]",
                    "scope": "site",
                    "description": "",
                    "template": 2,
                    "status": 1,
                }

            ]
        }
    },
    {
        "method": "GET",
        "path": "/menus-permissions",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": []
        }
    },

    {
        "method": "GET",
        "path": "/projects/card",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "administrators": [
                    {
                        "id": "6090e4318d9d12afad9d4ebd",
                        "group": 1,
                        "admin": true,
                        "project_admin": true,
                        "info": {
                            "emailLanguage": null,
                            "email": "<EMAIL>",
                            "name": "Preview User",
                            "phone": "+8613800000000",
                            "company": "EDC",
                            "description": "",
                            "version": 19,
                            "status": 1,
                            "unicode": 100012
                        },
                        "roles": ["6090e57d8d9d12afad9d4ec7",],
                        "deleted": false,
                        "cloudId": "6090e430f0d300456f8f517d",
                        "settings": {},
                        "closeCustomer": null,
                        "registrationId": "1104a8979399595f069",
                        "appLanguage": "zh-CN"
                    },
                ],
                "customer": {
                    "id": "000000000000000000000000",
                    "name": "Customer",
                    "email": "",
                    "phone": "",
                    "description": "",
                    "meta": {}
                },
                "project": {
                    "id": "64dc2d36de9209a4e22d3cb9",
                    "customerId": "6090e4318d9d12afad9d4ebc",
                    "info": {
                        "type": 1,
                        "number": "XXX",
                        "name": "XXX",
                        "sponsor": "xxx",
                        "phone": "",
                        "tz": "Asia/Shanghai",
                        "timeZoneStr": "8",
                        "timeZone": 8,
                        "connectEdc": 1,
                        "edcSupplier": 1,
                        "pushMode": 1,
                        "synchronizationMode": 1,
                        "edcUrl": "",
                        "pushTypeEdc": "",
                        "pushRules": 0,
                        "pushScenario": {
                            "registerPush": true,
                            "updateRandomFrontPush": true,
                            "updateRandomAfterPush": true,
                            "randomPush": true,
                            "randomBlockPush": true,
                            "formRandomBlockPush": true,
                            "cohortRandomBlockPush": true,
                            "dispensingPush": true,
                            "screenPush": true
                        },
                        "visitRandomization": {
                            "visits": null,
                            "randomizations": null
                        },
                        "connectAli": 0,
                        "bound": 0,
                        "aliProjectId": "",
                        "description": "",
                        "systemCourses": 1,
                        "needSystemCourses": 0,
                        "connectLearning": 1,
                        "needLearning": 2,
                        "needLearningEnv": [
                            "PROD"
                        ],
                        "orderCheck": 1,
                        "orderCheckDay": [1, 3, 4],
                        "orderCheckTime": "08:00",
                        "orderConfirmation": 1,
                        "deIsolationApproval": 1,
                        "researchAttribute": 1,
                        "unblindingControl": 1,
                        "unblindingType": 1,
                        "unblindingSms": 1,
                        "unblindingProcess": 1,
                        "unblindingCode": 1,
                        "pvUnblindingType": 1,
                        "pvUnblindingSms": 1,
                        "pvUnblindingProcess": 1,
                        "ipUnblindingType": 1,
                        "ipUnblindingSms": 1,
                        "ipUnblindingProcess": 1,
                        "orderApprovalControl": 1,
                        "orderApprovalSms": 1,
                        "orderApprovalProcess": 1,
                        "room": true,
                        "statusReason": ""
                    },
                    "envs": [
                        {
                            "id": "64dc2d65de9209a4e22d3ceb",
                            "name": "DEV",
                            "lockConfig": false,
                            "capacity": null,
                            "reminderThresholds": null,
                            "alertThresholds": null,
                            "status": 2,
                            "cohorts": [],
                            "isCopy": false
                        },
                    ],
                    "administrators": [],
                    "status": 0,
                    "meta": {}
                }
            }
        }
    },
    {
        "method": "GET",
        "path": "/projects/notice",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": {
                "customerId": "6090e4318d9d12afad9d4ebc",
                "projectId": "64dc2d36de9209a4e22d3cb9",
                "envId": "64dc2d65de9209a4e22d3ceb",
                "open": true,
                "noticeRule": [
                    {
                        "id": "66e2ad6c6a0663a24d0c11b1",
                        "date": 9,
                        "time": "22:49:29",
                        "template": "a"
                    }
                ],
                "userIds": [
                    {
                        "roleId": "64dc2d36de9209a4e22d3cba",
                        "users": [
                            "6090e4318d9d12afad9d4ebd"
                        ]
                    }
                ]
            }
        }
    },
    {
        "method": "GET",
        "path": "/projects/user-role-project-env",
        "data": {
            "code": 0,
            "msg": "操作成功",
            "data": [
                {
                    "id": "64dc2d36de9209a4e22d3cbf",
                    "roleName": "IP Officer",
                    "user": [
                        {
                            "id": "6090e4318d9d12afad9d4ebd",
                            "info": {
                                "emailLanguage": null,
                                "email": "<EMAIL>",
                                "name": "",
                                "phone": "",
                                "company": "",
                                "description": "",
                                "version": 0,
                                "status": 0,
                                "unicode": 0
                            }
                        },
                    ]
                }
            ]
        },
    },
];