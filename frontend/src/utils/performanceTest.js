/**
 * 性能测试工具
 * 用于测试 rowSelection 全选优化效果
 */

// 生成测试数据
export const generateTestData = (size) => {
    const statuses = [1, 6, 14, 7, 2, 3, 4, 5]; // 包含可选择和不可选择的状态
    
    return Array.from({ length: size }, (_, index) => ({
        id: `item-${index}`,
        siteName: `Site ${index % 10}`,
        number: `MED${String(index).padStart(6, '0')}`,
        name: `Medicine ${index}`,
        batchNumber: `BATCH${index % 100}`,
        expirationDate: '2024-12-31',
        orderNumber: `ORDER${index % 50}`,
        status: statuses[index % statuses.length],
        packageNumber: index % 3 === 0 ? `PKG${index}` : '-',
        shortCode: index % 4 === 0 ? '-' : `SC${index}`,
    }));
};

// 性能测试函数
export const performanceTest = {
    // 测试全选性能
    testSelectAll: (dataSize, selectAllFunction) => {
        console.group(`🚀 Performance Test: Select All ${dataSize} items`);
        
        const start = performance.now();
        selectAllFunction();
        const end = performance.now();
        
        const duration = end - start;
        console.log(`⏱️  Duration: ${duration.toFixed(2)}ms`);
        
        // 性能评级
        let rating = '';
        if (duration < 50) {
            rating = '🟢 Excellent';
        } else if (duration < 200) {
            rating = '🟡 Good';
        } else if (duration < 500) {
            rating = '🟠 Fair';
        } else {
            rating = '🔴 Poor';
        }
        
        console.log(`📊 Performance: ${rating}`);
        console.groupEnd();
        
        return { duration, rating };
    },

    // 测试反选性能
    testDeselectAll: (dataSize, deselectAllFunction) => {
        console.group(`🚀 Performance Test: Deselect All ${dataSize} items`);
        
        const start = performance.now();
        deselectAllFunction();
        const end = performance.now();
        
        const duration = end - start;
        console.log(`⏱️  Duration: ${duration.toFixed(2)}ms`);
        console.groupEnd();
        
        return { duration };
    },

    // 批量测试不同数据量
    batchTest: (selectAllFunction, deselectAllFunction) => {
        const testSizes = [100, 500, 1000, 2000, 5000];
        const results = [];
        
        console.group('📈 Batch Performance Test');
        
        testSizes.forEach(size => {
            const selectResult = performanceTest.testSelectAll(size, selectAllFunction);
            const deselectResult = performanceTest.testDeselectAll(size, deselectAllFunction);
            
            results.push({
                size,
                selectDuration: selectResult.duration,
                selectRating: selectResult.rating,
                deselectDuration: deselectResult.duration,
            });
        });
        
        // 输出汇总表格
        console.table(results);
        console.groupEnd();
        
        return results;
    },

    // 内存使用测试
    testMemoryUsage: (testFunction, description) => {
        if (!performance.memory) {
            console.warn('Memory API not available in this browser');
            return null;
        }
        
        const beforeMemory = performance.memory.usedJSHeapSize;
        
        testFunction();
        
        // 强制垃圾回收（如果可用）
        if (window.gc) {
            window.gc();
        }
        
        setTimeout(() => {
            const afterMemory = performance.memory.usedJSHeapSize;
            const memoryDiff = afterMemory - beforeMemory;
            
            console.log(`💾 Memory Usage (${description}):`, {
                before: `${(beforeMemory / 1024 / 1024).toFixed(2)} MB`,
                after: `${(afterMemory / 1024 / 1024).toFixed(2)} MB`,
                diff: `${(memoryDiff / 1024 / 1024).toFixed(2)} MB`,
            });
        }, 100);
    },

    // 渲染性能测试
    testRenderPerformance: (renderFunction, description) => {
        console.group(`🎨 Render Performance Test: ${description}`);
        
        const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry) => {
                if (entry.entryType === 'measure') {
                    console.log(`📏 ${entry.name}: ${entry.duration.toFixed(2)}ms`);
                }
            });
        });
        
        observer.observe({ entryTypes: ['measure'] });
        
        performance.mark('render-start');
        renderFunction();
        
        setTimeout(() => {
            performance.mark('render-end');
            performance.measure('render-duration', 'render-start', 'render-end');
            observer.disconnect();
            console.groupEnd();
        }, 0);
    },
};

// React 组件性能监控 Hook
export const usePerformanceMonitor = (componentName) => {
    const renderCount = React.useRef(0);
    const lastRenderTime = React.useRef(performance.now());
    
    React.useEffect(() => {
        renderCount.current += 1;
        const currentTime = performance.now();
        const timeSinceLastRender = currentTime - lastRenderTime.current;
        
        if (process.env.NODE_ENV === 'development') {
            console.log(`🔄 ${componentName} render #${renderCount.current}, time since last: ${timeSinceLastRender.toFixed(2)}ms`);
        }
        
        lastRenderTime.current = currentTime;
    });
    
    return {
        renderCount: renderCount.current,
        logPerformance: (operation, duration) => {
            if (process.env.NODE_ENV === 'development') {
                console.log(`⚡ ${componentName} ${operation}: ${duration.toFixed(2)}ms`);
            }
        }
    };
};

// 使用示例
export const exampleUsage = `
// 在组件中使用性能测试
import { performanceTest, generateTestData, usePerformanceMonitor } from './performanceTest';

const MyComponent = () => {
    const { logPerformance } = usePerformanceMonitor('MyComponent');
    
    const handleSelectAll = () => {
        const start = performance.now();
        // 执行全选逻辑
        const end = performance.now();
        logPerformance('selectAll', end - start);
    };
    
    // 测试不同数据量的性能
    const runPerformanceTest = () => {
        performanceTest.batchTest(handleSelectAll, handleDeselectAll);
    };
    
    return (
        <div>
            <button onClick={runPerformanceTest}>Run Performance Test</button>
        </div>
    );
};
`;

export default performanceTest;
