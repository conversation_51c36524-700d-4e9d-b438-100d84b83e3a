export function handlerRowSpan (datasource,type,index) {
    const objResult = {}
    const arr = []
    const arr1 = []
    const arr2 = [0]
    let len = 0
    let typeResult
    datasource.map((val) =>{
        for (const i in val){
            if (i === type){
                typeResult = val[i]
            }
        }
        const objArray = objResult[typeResult] || []
        objArray.push(val)
        objResult[typeResult] = objArray
    })
    for (const i in objResult){
        arr.push(objResult[i])
    }
    arr.map((a)=>{
        arr1.push(a.length)
        len += a.length
        arr2.push(len)
    })
    let obj = {}
    for (let i = 0;i<arr1.length;i++){
        if (index === arr2[i]){
            obj.rowSpan = arr1[i]
        }
        if (index > arr2[i] && index < arr2[i+1]){
            obj.rowSpan = 0
        }
    }
    return obj
}