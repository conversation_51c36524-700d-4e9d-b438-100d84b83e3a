import {get} from "utils/http";

export const all = (params: object = {}) => get(`/project-dynamics`, { params });
export const analysis = (params: object = {}) => get(`/project-dynamics/analysis`, { params });
export const subjectSiteStatistics = (params: object = {}) => get(`/project-dynamics/subject-site-statistics`, { params });
export const subjectStatistics = (params: object = {}) => get(`/project-dynamics/subject-statistics`, { params });
