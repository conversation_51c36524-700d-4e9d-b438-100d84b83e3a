import {del, get, patch, post} from "utils/http";

export const getName = () => get("/multiLanguage/name", {});
export const getList = ({ customerId, projectId }: any) => get("/multiLanguage/list", { params: { customerId, projectId } });
export const addLanguage = (params: object = {},data: object = {}) => post('/multiLanguage/add', { params, data });
export const updateLanguage = (params: object = {},data: object = {}) => post('/multiLanguage/update', { params, data });
export const deleteLanguage = ({ id }: any) => del("/multiLanguage/delete", { params: { id } });
export const getTranslateList = (params: object = {},data: object = {}) => post('/multiLanguage/translate/list', { params, data });
export const getTranslateMap = (params: object = {},data: object = {}) => post('/multiLanguage/translate/map', { params, data });
export const updateTranslate = (params: object = {},data: object = {}) => post('/multiLanguage/translate/update', { params, data });
export const getHistoryList = (params: object = {}) => get(`/multiLanguage/history`, { params });