import {get, patch, post} from "../utils/http";

export const list = (params: object = {}) => get('/simulate-random/list', { params});
export const update = (params: object = {},data: object = {}) => patch('/simulate-random', { params, data });
export const add = (params: object = {},data: object = {}) => post('/simulate-random', { params, data });
export const run = (params: object = {},data: object = {}) => post('/simulate-random/run', { params, data });
export const downloadList = (params: object = {}) => get('/simulate-random/download', { params});
export const factor = (params: object = {}) => get('/simulate-random/factor', { params});
