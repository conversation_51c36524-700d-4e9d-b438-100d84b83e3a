import {del, get, post, put} from "../utils/http"

const prefix = '/barcode-label'
export const getBarcodeLabelList = (params: object = {}) => get(`${prefix}`, { params })
export const getBarcodeLabelById = (id: string, params: object = {}) => get(`${prefix}/${id}`, { params })
export const getPreviewBarcodeLabel = (id: string, params: object = {}) => get(`${prefix}/preview/${id}`, { params })
export const addBarcodeLabel = (data: object = {}) => post(`${prefix}`, { data })
export const updateBarcodeLabel = (id: string, data: object = {}) => put(`${prefix}/${id}`, { data })
export const sendBarcodeLabel = (id: string, data: object = {}) => put(`${prefix}/send/${id}`, { data })
export const deleteBarcodeLabel = (id: string, data: object = {}) => del(`${prefix}/${id}`, { data })

export const getBarcodeLabelNumber = (params: object = {}) => get(`${prefix}/numbers`, { params })
