import {get, patch, post} from "utils/http";


export const list = (params: object = {}) => get(`/projects-roles`, { params })
export const listPool = (params: object = {}) => get(`/projects-roles/pool`, { params })
export const hasBindUser = (params: object = {}) => get(`/projects-roles/bind-user`, { params })
export const add = (data: object = {}) => post(`/projects-roles`, { data })
export const update = (params: object = {},data: object = {}) => patch(`/projects-roles`, { params,data })
export const getPermission = (params: object = {}) => get(`/projects-roles/permission`, { params })