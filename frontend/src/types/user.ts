export enum CustomerModeEnum {List, View}

export interface User {
    id: string;
    group: number;
    admin: boolean;
    project_admin: boolean;
    info: UserInfo;
    settings: UserSettings;
    apps?: string[];
}

export interface UserInfo {
    email: string;
    name?: string;
    mobile?: string;
    company?: string;
    description?: string;
}

export interface UserSettings {
    timezone: string;
    dateformat: string;
}

