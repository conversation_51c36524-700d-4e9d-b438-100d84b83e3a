import React, {ReactNode, useEffect, useState} from "react";
import {useSafeState} from "ahooks";
import _ from "lodash";
import {defaultLanguageOptions, getDefaultLocale} from "../data/data"

export const GlobalContext = React.createContext<
    {
        lang: string;
        setLanguage: (lang: string) => void;
        languageOptions: any[];
        setLanguageOptions: (options: any[]) => void;
        initLanguageOptions: () => void;
        setProjectLanguageOptions: (options: any[]) => void;
        quantity: number;
        setQuantity: (quantity: number) => void;
        hover: boolean;
        setHover: (quantity: boolean) => void;
        data: any[];
        setData: (data: any[]) => void;
        currentPage: number;
        setCurrentPage: (v: number) => void;
        pageSize: number;
        setPageSize: (v: number) => void;
        total: number;
        setTotal: (v: number) => void;
    }
    |
    null
>(null);

export const GlobalContextProvider = ({ children }: { children: ReactNode }) => {

    const [lang, setLang] = useSafeState(sessionStorage.getItem('lang') || 'en');
    const [quantity, setQuantity] = useSafeState(0);
    const [hover, setHover] = useState(false);

    const [data, setData] = useSafeState<any[]>([]);
    const [currentPage, setCurrentPage] = useSafeState(1);
    const [pageSize, setPageSize] = useSafeState(20);
    const [total, setTotal] = useSafeState(0);

    const [languageOptions, setLanguageOptions] = useSafeState(defaultLanguageOptions)
    // 临时保存的语言
    const [tempLang, setTempLang] = useSafeState(sessionStorage.getItem('lang') || 'en')

    // 切换语言
    const setLanguage = (lang: string) => {
        updateLanguage(lang)
        setLang(lang)
    }

    // 进入项目时设置
    const setProjectLanguageOptions = (options: any[])=> {
        setLanguageOptions(_.uniqBy([...defaultLanguageOptions, ...options], 'value'))
    }

    // 退出项目时重置
    const initLanguageOptions = () => {
        const initLang = getDefaultLocale(tempLang)
        updateLanguage(initLang)
        setLang(initLang)
        setLanguageOptions(defaultLanguageOptions)
    }

    const updateLanguage = (lang: string) => {
        // 是否是系统默认语言
        const isDefaultLanguage = !!defaultLanguageOptions.find(it => it.value === lang)
        // 切换的语言为系统默认语言时，暂存系统原语言，退出项目时重置为原语言
        if (isDefaultLanguage) setTempLang(lang)
        // 语言选项
        const languageOption = languageOptions.find(it => it.value === lang) as any
        // languageId
        const languageId = isDefaultLanguage ? '' : (languageOption?.languageId || '')
        sessionStorage.setItem('languageId', languageId)
        // language，暂仅支持默认语言
        const language = isDefaultLanguage ? lang : (languageOption?.baseCode || lang)
        sessionStorage.setItem('lang', getDefaultLocale(language))
    }

    return (
        <GlobalContext.Provider
            value={
                {
                    lang,
                    setLanguage,
                    languageOptions,
                    setLanguageOptions,
                    initLanguageOptions,
                    setProjectLanguageOptions,
                    quantity,
                    setQuantity,
                    hover,
                    setHover,
                    data,
                    setData,
                    currentPage,
                    setCurrentPage,
                    pageSize,
                    setPageSize,
                    total,
                    setTotal,
                }
            }
        >
            {children}
        </GlobalContext.Provider>
    )
};

export const useGlobal = () => {
    const context = React.useContext(GlobalContext);
    if (!context) {
        throw new Error("useGlobal must be used in GlobalContextProvider");
    }
    return context;
};
