import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const PageContext = React.createContext<
    {
        data: any[];
        setData: (data: any[]) => void;
        currentPage: number;
        setCurrentPage: (v: number) => void;
        pageSize: number;
        setPageSize: (v: number) => void;
        total: number;
        setTotal: (v: number) => void;
    }
    |
    null
>(null);

export const PageContextProvider = ({ children }: { children: ReactNode }) => {

    const [data, setData] = useSafeState<any[]>([]);
    const [currentPage, setCurrentPage] = useSafeState(1);
    const [pageSize, setPageSize] = useSafeState(20);
    const [total, setTotal] = useSafeState(0);

    return (
        <PageContext.Provider
            value={
                {
                    data,
                    setData,
                    currentPage,
                    setCurrentPage,
                    pageSize,
                    setPageSize,
                    total,
                    setTotal
                }
            }
        >
            {children}
        </PageContext.Provider>
    )
};

export const usePage = () => {
    const context = React.useContext(PageContext);
    if (!context) {
        throw new Error("usePage must be used in HomeContextProvider");
    }
    return context;
};

