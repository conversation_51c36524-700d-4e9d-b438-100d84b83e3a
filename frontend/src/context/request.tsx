import {message} from "antd";
import React, {ReactNode} from "react";
import {useIntl} from "react-intl";
// import { Service, Options, Plugin, Result } from "ahooks/lib/useRequest/src/types"

const UseRequestContext = React.createContext<
    {
        // request: (service: Service<unknown, any[]>, options?: Options<unknown, any[]> | undefined, plugins?: Plugin<unknown, any[]>[] | undefined) => Result<unknown, any[]>;
        onError: (e: Error, params: any[]) => void;
    }
    |
    null
>(null);

export const UseRequestProvider = ({ children }: { children: ReactNode }) => {

    const intl = useIntl();

    // const request = (service: Service<unknown, any[]>, options?: Options<unknown, any[]> | undefined, plugins?: Plugin<unknown, any[]>[] | undefined) => {
    //     return useRequest(service, {
    //         ...options,
    //         onError: onError
    //     });
    // };
    
    const onError = (e: any, params: any[]) => {
        switch (e.status) {
            case 500:
                const contentType = e.headers.get("content-type");
                if (contentType && contentType.indexOf("application/json") > -1) {
                    e.json().then((data: any) => message.error(data.msg));
                }
                break;
            case 400:
                message.error(intl.formatMessage({ id: "error.invalid-request" }));
                break;
            case 401:
                sessionStorage.removeItem("token");
                message.error(intl.formatMessage({ id: "error.not-logged-in" }));
                // window.location.reload();
                break;
            case 403:
                message.error(intl.formatMessage({ id: "error.unauthorized" }));
                break;
            case 404:
                message.error(intl.formatMessage({ id: "error.resource-not-found" }));
                break;
            default:
                message.error(intl.formatMessage({ id: "error.unknown" }));
                break;
        }
    };

    return (
        <UseRequestContext.Provider value={{ onError }}>
            {children}
        </UseRequestContext.Provider>
    );
};

export const useRequestx = () => {
    const context = React.useContext(UseRequestContext);
    if (!context) {
        throw new Error("useRequestx must be used in UseRequestContext");
    }
    return context;
};