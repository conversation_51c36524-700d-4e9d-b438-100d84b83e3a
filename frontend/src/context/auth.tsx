import React, {ReactNode} from "react";
import {Navigate, useLocation} from "react-router-dom";
import {useIdleTimer} from 'react-idle-timer';
import {getSearchVariable} from "utils/url";
import {useSafeState} from "ahooks";
import {SelectCustomer} from "types/customer";
import {NotFound} from "../pages/not-found";

export const AuthContext = React.createContext<{
        idle: boolean;
        setIdle: (v: boolean) => void;
        user: any | undefined;
        setUser: (v: any | undefined) => void;
        customers: SelectCustomer[] | undefined;   // 关联客户
        setCustomers: (data: SelectCustomer[] | undefined) => void;
        customerId: string | undefined; // 当前选择的关联客户ID
        setCustomerId: (v: string | undefined) => void;
        adminCustomers: SelectCustomer[] | undefined;   // 作为客户管理员或项目管理员的客户
        setAdminCustomers: (data: SelectCustomer[] | undefined) => void;
        adminCustomerId: string | undefined;    // 当前选择的作为（客户或项目）管理员的客户ID
        setAdminCustomerId: (v: string | undefined) => void;
        project: any | undefined;
        setProject: React.Dispatch<any>;
        env: any | undefined;
        setEnv: (v: any | undefined) => void;
        cohort: any | undefined;
        setCohort: (v: any | undefined) => void;
        codeRule: number | undefined;
        setCodeRule: (v: any | undefined) => void;
        attribute: any;
        setAttribute: (v: any) => void;
        permissions: any;
        setPermissions: (v: any) => void;
        projectPermissions: any;
        setProjectPermissions: (v: any) => void;
        systemCheck: any;
        setSystemCheck: (v: any) => void;
        cloudUrl: any;
        setCloudUrl: (v: any) => void;
        isRandomDispensing: any;
        setIsRandomDispensing: (v: any) => void;
        attributeUpdate: any;
        setAttributeUpdate: (v: any) => void;
        reportProjectId: any;
        setReportProjectId: (v: any) => void;
        multiLanguageProjectId: any;
        setMultiLanguageProjectId: (v: any) => void;
    }
    |
    null>(null);

export const AuthProvider = ({children}: { children: ReactNode }) => {

    const [idle, setIdle] = useSafeState(false);
    const [user, setUser] = useSafeState<any | undefined>(undefined);
    const [customers, setCustomers] = useSafeState<SelectCustomer[] | undefined>(undefined);
    const [customerId, setCustomerId] = useSafeState<string | undefined>(undefined);
    const [adminCustomers, setAdminCustomers] = useSafeState<SelectCustomer[] | undefined>(undefined);
    const [adminCustomerId, setAdminCustomerId] = useSafeState<string | undefined>(undefined);
    const [project, setProject] = useSafeState<any | undefined>(undefined);
    const [env, setEnv] = useSafeState<any | undefined>(undefined);
    const [cohort, setCohort] = useSafeState<any | undefined>(undefined);
    const [codeRule, setCodeRule] = useSafeState<number | undefined>(0);
    const [attribute, setAttribute] = useSafeState<any | undefined>(undefined);
    const [permissions, setPermissions] = useSafeState<any>([]);
    const [projectPermissions, setProjectPermissions] = useSafeState<any>([]);
    const [systemCheck, setSystemCheck] = useSafeState<any>(false);
    const [cloudUrl, setCloudUrl] = useSafeState<any>("");
    const [isRandomDispensing, setIsRandomDispensing] = useSafeState<any>({});
    const [attributeUpdate, setAttributeUpdate] = useSafeState<any>(0);
    const [reportProjectId, setReportProjectId] = useSafeState<any>(null);
    const [multiLanguageProjectId, setMultiLanguageProjectId] = useSafeState<any>(null);

    useIdleTimer(
        {
            timeout: 1000 * 60 * 30,
            onAction: () => {
            },
            onActive: () => setIdle(false),
            onIdle: () => {
                if (user) {
                    setIdle(true);
                    sessionStorage.setItem("lock", "");
                }
            }
        }
    );

    return (
        <AuthContext.Provider
            value={
                {
                    idle,setIdle,
                    user, setUser,
                    customers, setCustomers,
                    customerId, setCustomerId,
                    adminCustomers, setAdminCustomers,
                    adminCustomerId, setAdminCustomerId,
                    project, setProject,
                    env, setEnv,
                    cohort, setCohort,
                    codeRule, setCodeRule,
                    attribute, setAttribute,
                    permissions, setPermissions,
                    projectPermissions, setProjectPermissions,
                    systemCheck, setSystemCheck,
                    cloudUrl, setCloudUrl,
                    isRandomDispensing, setIsRandomDispensing,
                    attributeUpdate, setAttributeUpdate,
                    reportProjectId, setReportProjectId,
                    multiLanguageProjectId, setMultiLanguageProjectId,
                }
            }
        >
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = React.useContext(AuthContext);
    if (!context) {
        throw new Error("useAuth must be used in AuthProvider");
    }
    return context;
};

export const RequireAuth = ({children}: { children: JSX.Element}) => {
    let auth = useAuth();
    let location = useLocation();
    if (!auth.user) {
        if (!sessionStorage.getItem("token")) {
            return <Navigate to="/login"
                             state={{from: location.pathname, token: getSearchVariable(location.search, "t"),lang: getSearchVariable(location.search, "lang")}}/>;
        }
        return null;
    }
    return (
        children
    );
};


export const RequireProject = ({children}: { children: JSX.Element}) => {
    let auth = useAuth();
    if (!auth.project){
        return <NotFound></NotFound>
    }
    return (
        children
    );
};
