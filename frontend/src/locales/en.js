export const message = {
    "error.unknown": "Unknown Error",
    "error.invalid-request": "Invalid Request",
    "error.not-logged-in": "Not Logged in",
    "error.unauthorized": "Unauthorized",
    "error.resource-not-found": "Resource Not Found",
    "common.options": "Option",
    "common.all-options": "All Options",
    "common.collapse": "collapse",
    "common.expand": "expand",
    "common.detail": "Detail",
    "common.details": "Details",
    "common.pagination.tip.total": "total of {total} items,",
    "common.pagination.tip.pagesize-prefix": " ",
    "common.pagination.tip.pagesize-suffix": "per page",
    "common.all": "All",
    "common.customer": "Customer",
    "common.selectUser": "Select Customer",
    "common.add": "Add",
    "common.copy": "Copy",
    "common.copy.add": "Copy & Add",
    "common.push": "Release",
    "common.view": "View",
    "common.sprint": "Printed ",
    "common.delete": "Delete",
    "common.unbinding": "Unbind",
    "common.unbinding.batch": "Batch Unbinding",
    "common.recover": "Recover",
    "common.save": "Save",
    "common.edit": "Edit",
    "common.edit.batch": "Batch Edit",
    "common.modify": "Modify",
    "common.submit": "Submit",
    "common.search": "Search",
    "common.reset": "Reset",
    "common.select": "Select",
    "common.select.list.tips": "Please select the list data first",
    "common.ok": "Confirm",
    "common.ok.sync": "Confirm and synchronize",
    "common.cancel": "Cancel",
    "common.next": "Next",
    "common.open": "Open",
    "common.close": "Close",
    "common.close.batch": "Batch Closing",
    "common.operation": "Operation",
    "common.enable": "Enable",
    "common.active": "Active",
    "common.more": "More",
    "common.status": "Status",
    "common.timezone": "TimeZone",
    "common.yes": "Yes",
    "common.no": "No",
    "common.update": "Update",
    "common.update.count": "Update count",
    "common.page": "Page",
    "common.type": "Type",
    "common.setting": "Setting",
    "common.settings": "Settings",
    "common.setting.batch": "Batch Setting",
    "common.print": "Print",
    "common.between": "Range",
    "common.confirm.tip": "Please confirm the following information.",
    "common.created.at": "Created Time",
    "common.created.by": "Created by",
    "common.updated.at": "Updated Time",
    "common.updated.by": "Updated by",
    "common.operator": "Operator",
    "common.operation.time": "Operation Time",
    "common.operation.content": "Operation Content",
    "common.operation.type": "Operation Method",
    "common.operation.error": "Operation failed!",
    "common.history": "Trail",
    "common.statistics": "statistics",

    "common.register": "Register",
    "common.login": "Log in",
    "common.logout": "Logout",
    "common.username": "Account",
    "common.password": "Password",
    "common.remember": "Remember Me",
    "common.forget": "Forgot password?",
    "common.user.info": "Profile",
    "common.tips": "Tips",
    "common.occasions": "Times",
    "common.tips.expireDate_batch": "The expiration date/lot number is empty and can be updated in Batch Management or DTP shipments.",
    "common.tips.expireDate_batch.know": "I Know",
    "common.tips.save": "The current data has not been saved, is it confirmed to leave? ",
    "common.random.tips": "Is it confirmed to randomize?",
    "common.task.automatic": "Automated Tasks",
    "common.task.manual": "Manual Tasks",
    "common.email.language": "Email Language",
    "common.email.language.zh": "Chinese",
    "common.email.language.en": "English",
    "common.email.language.zh_en": "Chinese And English",
    "common.email": "Email",
    "common.phone": "TEL",
    "common.number": "Number",
    "common.name": "Name",
    "common.name.current": "Current Name",
    "common.name.new": "New Name",
    "common.country": "Country/Region",
    "common.full_name": "Full Name",
    "common.company": "Company",
    "common.language": "Language",
    "common.contacts": "Contacts",
    "common.address": "Address",
    "common.description": "Description",
    "common.confirm.save": "Is it confirmed to save?",
    "common.confirm.delete": "Is it confirmed to delete?",
    "common.confirm.cancel.edit": "Is it confirmed to cancel edit?",
    "common.confirm.is_delete": "ls it confirmed to delete?",
    "common.confirm.delete.unable.recover": "Subject data cannot be recovered after deletion.",
    "common.confirm.copy": "Are you sure to copy?",
    "common.confirm.push": "Is it confirmed to release?",
    "common.confirm.unbinding": "Is it confirmed to unbind?",
    "common.confirm.close": "Is it confirmed to close?",
    "common.confirm.inactivate.group": "Is it confirmed to inactivate group?",
    "common.administrator": "Administrator",
    "common.administrator.project": "Project Administrator",
    "common.select.role": "Select Role",
    "common.all.role": "All Roles",
    "common.role": "Role",
    "common.permission": "Permission",
    "common.role.assigned":
        "System detects that user has been added to the current customer group, but role and permission have not been assigned. Please contact administrator for assignment.",

    "common.upload.fileSize": "Size of uploaded file must not exceed 50MB!",
    "common.upload.fileSize.100": "Size of uploaded file must not exceed 100MB!",
    "common.upload": "Upload",
    "common.download": "Download",
    "common.download.data": "Download Data",
    "common.download.template": "Download Template",
    "common.generate": "Generate",
    "common.sync": "Synchronize",
    "common.inactivating": "Inactive",
    "common.site": "Site",
    "common.subject": "Subject",
    "common.depot": "Depot",
    "common.export": "Export",
    "common.export.list": "Export List",
    "common.to.set": "Go to Set",
    "common.site.country.null": "Corresponding country information of the site cannot be found.",
    "common.download.fail": "Download Failed",
    "common.not-logged-in": "Not Logged in or Login Timeout",
    "common.classification": "Classification",
    "common.effective": "Valid",
    "common.invalid": "Invalid",
    "common.menu": "Menu/Module",
    "common.role.setting": "Permission Settings",
    "common.serial": "No.",
    "common.common": "Common",
    "common.template": "Template",
    "common.addTo": "Add",
    "common.authorization.success": "Authorization succeeded",
    "common.success": "Operation Succeeded",
    "common.fail": "Operation failed",
    "common.confirm": "Confirm",
    "common.previous": "Previous",
    "common.open.switch": "Open",
    "common.close.switch": "Close",
    "common.have": "Have",
    "common.nothing": "Not Found",
    "common.time": "Time",
    "common.all.select": "All",
    "common.time.start": "Start Time",
    "common.time.end": "End Time",
    "common.pagination.seleted": "Selected",
    "common.pagination.record": " ",
    "common.pagination.all": "Total",
    "common.pagination.empty": "Empty",
    "common.lock": "Lock",
    "common.unlock": "Unlock",
    "common.required.prefix": "Please enter ",
    "common.required.prefix.capping": "Please enter capping",
    "common.required.prefix.factor": "Please enter factor",
    "common.required.prefix.search": "Please enter search",
    "common.required.prefix.name": "Please enter name",
    "common.required.prefix.new.name": "Please enter new name",
    "common.required.prefix.keyword": "Please enter a keyword",
    "common.required.positiveInteger": "Please enter a positive integer",
    "common.required.positiveInteger.zero": "The number of decimal places to retain must be a non-negative integer. Please confirm.",
    "common.upload.add": "Add file",
    "common.upload.excel.tip": "Only XLSX formats are supported. Please refer to the template for detailed formats.",
    "common.upload.excel.tip2": "Only XLSX formats are supported.",
    "common.upload.excel.tip3": "Tips：",
    "common.upload.excel.tip4": "·The first 10 column headers will be read;",
    "common.upload.excel.tip5": "·Fields in the system-provided template are mandatory;",
    "common.upload.excel.tip6": "·No empty columns are allowed before the last column.",
    "common.confirm-change": "Confirm Change",
    "common.delete.confirm": "Is it confirmed to delete?",
    "common.delete.ok": "Deletion Succeeded",
    "common.copy.ok": "Copy succeeded",
    "common.create": "Create",
    "common.reason": "Reason",
    "common.condition": "Condition",
    "common.rest.assured": "CodeTrust Platform",
    "common.please.confirm": "Please confirm.",
    "common.colon": ":",
    "common.update.fail": "Modification failed",
    "common.stage.name": "Stage Name",
    "common.stage": "Stage",
    "common.remark": "Remark",
    "common.current.time": "Current Time",
    "common.approval.confirm": "Approval Confirmation",
    "common.toBeApproved": "To be approved",
    "common.ratio": "Case numbers",
    "common.ip.info": "IP information",
    "role.setting.operation": "Operation Permission",
    "role.operation.template": "Permission Template",
    "role.name.enter": "Please enter role name",
    "role.name": "Role Name",

    "project.task": "Task",
    "project.task.status": "Task Status",
    "project.task.status.all": "All",
    "project.task.status.notStarted": "Not Started",
    "project.task.status.completed": "Completed",
    "project.task.approvalStatus.notStarted": "Submit application",
    "project.task.approvalStatus.passed": "Approved",
    "project.task.approvalStatus.rejected": "Denied",
    "project.task.approvalStatus.pass": "Approved",
    "project.task.approvalStatus.reject": "Deny",
    "project.task.estimatedCompletionTime": "Estimated completion",
    "project.task.approvalTime": "Actual completion",
    "project.task.addOrder.title": "Site Shipment Application",
    "project.task.urgent-unblinding.title": "Emergency Unblinding Application",
    "project.task.pv-unblinding.title": "PV Unblinding Application",
    "project.task.ip-unblinding.title": "IP Unblinding Application",
    "project.task.approval.deadline": "Deadline",

    "project.statistics": "Project Statistics",

    "project.depot.ip.statistics": "Depot Item Statistics",
    "project.depot.ip.statistics.info": "All IP status statistics of depot",
    "project.site.ip.statistics": "Site Item Statistics",
    "project.site.ip.statistics.info": "All IP status statistics of site",
    "project.overview": "Project Overview",
    "project.overview.timeZone": "IS project time zone: ",
    "project.overview.timeZone.home": "Dashboard page, time zone calculation standard:",
    "project.overview.progress.template": "The project has been in progress for {startYear} years, {startMonth} months and {startDay} days, and there are still {endYear} years, {endMonth} months and {endDay} days before the end.",
    "project.random.statistics": "Randomization Statistics",
    "project.random.statistics.info": "Actual randomization statistics of the project by stratification factor or time",
    "project.visit.cycle.calendar": "Visit cycle calendar",
    "project.visit.cycle.calendar.summary": "Overview",
    "project.subject.statistics": "Subject Statistics",
    "project.subject.statistics.info": "Project/Site Subject Status Statictics:",
    "project.subject.statistics.info.register": "Registered: Registered subject number;",
    "project.subject.statistics.info.screenSuccess": "Screening successful: Successfully screened subject number;",
    "project.subject.statistics.info.screenFail": "Screening failed: Screening failed subject number;",
    "project.subject.statistics.info.random": "Randomized/Enrollment: Randomized subjects number;",
    "project.subject.statistics.info.exit": "Deactivated: number of who ceased to participate in the study;",
    "project.subject.statistics.info.unblinding": "Emergency unblinded: emergency unblinded subject statistics;",
    "project.subject.statistics.info.pvUnblinding": "PV unblinded: PV unblinded subject statistics;",
    "project.subject.statistics.info.finish": "Complete study: Subject number who completed the study.",
    "project.subject.statistics.info.status.register": "Registered",
    "project.subject.statistics.info.status.screenSuccess": "Screening successful",
    "project.subject.statistics.info.status.screenFail": "Screening failed",
    "project.subject.statistics.info.status.random": "Randomized",
    "project.subject.statistics.info.status.exit": "Deactivated",
    "project.subject.statistics.info.status.unblinding": "Emergency unblinded",
    "project.subject.statistics.info.status.pv": "PV unblinded",
    "project.subject.statistics.info.status.finish": "Complete study",
    "project.analysis": "Anomaly Analysis",
    "project.analysis.info": "Abnormal order and task statistics",
    "project.analysis.order.timeout": "Timeout Shipments",
    "project.analysis.order.all": "Total Shipments",
    "project.analysis.task.timeout": "Timeout Tasks",
    "project.analysis.task.all": "Total Tasks",
    "project.analysis.task.prepare": "To-do Tasks",
    "project.analysis.subject.visit": "Overdue",
    "project.analysis.subject.visit.outSizeNotCompleted": "Undone",
    "project.analysis.subject.visit.outSizeCompleted": "Completed",
    "project.attribute.subjectNumberPrex.err": "受试者前缀必须已{siteNO}开始",
    "project.attribute.randomSubject.err": "Please select the randomization supply check rule",
    "project.attribute.subject_number_rule.info2": "The subject screening process must remain consistent, and other group configurations have been synchronized.",
    "project.attribute.subject_number_rule.info3": "The subject screening process must remain consistent, and other stage configurations have been synchronized.",
    "project.attribute.subject_number_rule.info4": "subject ID rules must remain consistent, and other group configurations have been synchronized.",
    "project.attribute.subject_number_rule.info5": "subject ID rules must remain consistent, and other stage configurations have been synchronized.",
    "project.attribute.subject_number_rule.info6": "The subject screening process/subject ID rules must remain consistent, and other group configurations have been synchronized.",
    "project.attribute.subject_number_rule.info7": "The subject screening process/subject ID rules must remain consistent, and other stage configurations have been synchronized.",
    "project.attribute.freeze_reason.info1": "The Quarantine Reason must remain consistent, and other group configurations have been synchronized",
    "project.attribute.freeze_reason.info2": "The Quarantine Reason must remain consistent, and other stage configurations have been synchronized.",
    // "project.attribute.subject_number_rule.info4": "The subject number rules need to remain consistent, and other groups have been synchronized with them.",
    // "project.attribute.subject_number_rule.info5": "The subject number rules need to remain consistent, and other stages have been synchronized with them.",
    "project.noPermission": "Permissions within the project have not been assigned",
    "projects.all": "All Projects",
    "projects.all.admin": "All administrators",
    "projects.add": "Add Project",
    "projects.edit": "Edit Project",
    "projects.number": "Project Number",
    "projects.name": "Project Name",
    "projects.name.enter": "Please enter project name",
    "projects.timezone": "TimeZone",
    "projects.description": "Project Description",
    "projects.sponsor": "Sponsor",
    "projects.sponsor.enter": "Please enter the Sponsor",
    "projects.connectEdc": "Sync EDC",
    "projects.connectEdc.supplier": "EDC Supplier",
    "projects.connectEdc.mapping.configuration": "EDC Mapping Rules",
    "common.select.supplier": "Please Select Supplier",
    "projects.learning.system.courses": "System Course",
    "projects.learning.courses": "Project Course",
    "projects.learning.compulsory": "Must to Learn",
    "projects.no.system.learning": "Enter failed, there are unfinished system courses currently.",
    "projects.no.learning": "Enter failed, there are unfinished project courses currently.",
    "projects.learning": "Go to learn",
    "projects.connectELearning": "Sync eLearning",
    "projects.learning.need": "Required to complete the courses",
    "projects.traceability.code": "Traceability code",
    "projects.env": "Environment",
    "projects.orderCheck": "Run Shipping Algorithm",
    "projects.timing": "Fixed Time",
    "projects.realTime": "Real Time",
    "projects.manualRun": "(including manual run)",
    "projects.notApplicable": "Not Applicable",
    "projects.customTime": "Custom Time",
    "projects.synchronization.mode": "Synchronization Mode",
    "projects.orderCheckWeek.noEmpty": "Run shipping algorithm time cannot be empty",

    "projects.push.rules": "Push Rules",
    "projects.subject.uid": "Subject UID",
    "projects.push.scene": "Push Scenarios",
    "projects.push.scene.select": "Please select push scenario",
    "projects.push.scene.block.select": "Please select the block randomization option",
    "projects.subject.update": "Subject modify",
    "projects.subject.update.front": "(Before Random)",
    "projects.subject.update.after": "(After Random)",
    "projects.subject.random.block": "Stratification checks inconsistency, and randomization blocking is performed",
    "projects.subject.stratification": "Stratification",
    "projects.subject.form": "Form",
    "projects.subject.cohortName": "Cohort Name",
    "projects.subject.stageName": "Stage Name",
    "projects.subject.block.str-before": "When the parity",
    "projects.subject.block.str-after": "When the verification is inconsistent, random blocking is carried out",
    "projects.subject.no.tip": "Subject ID: Associated by subject ID as a unique identifier. When the subject ID is modified, the data will be overwritten or added with the unique judgment of the subject ID.",
    "projects.subject.uid.tip": "Subject UID: Associated by the unique ID of the subject in the database. When the subject ID is modified, the data will be overwritten or added with the prioritized binding relationship judgment.",

    "projects.recycling.confirmation": "Return Shipments Confirmation",
    "projects.recycling.confirmation.tip":
        "Once enabled, the return shipment is only allowed to be continued to deliver, receive and other processes after approved.",
    "projects.step.by.synchronization": "Data synchronization when subject screening",
    "projects.one.time.full.synchronization": "Data synchronization when subjects randomizing",
    "projects.connectAli": "Sync aliHealth",
    "projects.connectAliID": "Sync aliHealth ID",
    "projects.type": "Project Type",
    "projects.status": "Project Status",
    "projects.status.progress": "In Progress",
    "projects.status.finish": "Completed",
    "projects.status.close": "Closed",
    "projects.status.pause": "Paused",
    "projects.status.terminate": "Terminated",
    "projects.edit.status.progress.tips": "Is it confirmed to revise the status to ‘In Progress’?",
    "projects.edit.status.progress.content":
        "After the project is restored to in progress, the project data can be operated.",
    "projects.edit.status.progress.reason": "Please explain the reason for project recovery... (Required)",
    "projects.edit.status.finish.tips": "Is it confirmed to revise the status to ‘Completed’?",
    "projects.edit.status.finish.content":
        "Please ensure that all execution work and tasks of the project have been completed. After the project is finished, the project data will be inoperable.",
    "projects.edit.status.close.tips": "Is it confirmed to revise the status to ‘Closed’?",
    "projects.edit.status.close.content":
        "After closure, the project data will be frozen and only can be viewed.",
    "projects.edit.status.pause.tips": "Is it confirmed to revise the status to ‘Paused’?",
    "projects.edit.status.pause.content":
        "After the project is paused, the project data will be temporarily restricted to operate.",
    "projects.edit.status.pause.reason": "Please describe the reason for project paused... (required)",
    "projects.edit.status.terminate.tips": "Is it confirmed to revise the status to ‘Terminated’?",
    "projects.edit.status.terminate.content":
        "Please make sure the current work of the project has been completed. After the project is terminated, all project data will be frozen and cannot be processed anymore.",
    "projects.edit.status.terminate.reason": "Please describe the reason for project termination... (required)",
    "projects.types.first": "Basic Study",
    "projects.types.second": "Cohort Study",
    "projects.types.third": "Re-randomization Study",
    "projects.administrators": "Project Administrator",
    "projects.envs": "Project Environment",
    "projects.envs.all": "All Environments",
    "projects.envs.create": "Create Environment",
    "projects.envs.add": "Add Environment",
    "projects.envs.edit": "Edit Environment",
    "projects.envs.delete": "Delete Environment",
    "projects.envs.empty": "Environment name cannot be empty",
    "projects.envs.duplicate.name": "Duplicated Environment Name",
    "projects.envs.cohorts.capacity": "Capping",
    "projects.envs.cohorts.capacity.value": "Capping Value",
    "projects.envs.cohorts.reminder.thresholds": "Alert Threshold",
    "projects.envs.cohorts.reminder.thresholds.toplimit": "Threshold Capping",
    "projects.envs.cohorts.factor": "Factor",
    "projects.envs.cohorts.stage": "Previous Stage",
    "projects.envs.cohorts.status1": "Draft",
    "projects.envs.cohorts.status2": "Enrolling",
    "projects.envs.cohorts.status3": "Completed",
    "projects.envs.cohorts.status4": "Stopped",
    "projects.envs.cohorts.status5": "Enrollment Full",
    "projects.plannedCases": "Planned Enrollment Number",
    "projects.cycle": "Project Cycle",
    "projects.plannedCases.enter": "Please enter planned enrollment number",
    "projects.plannedCases.enter.min": "The number of subjects planned to enroll must be greater than 0.",
    "projects.second": "Cohort Name",
    "projects.third": "Re-randomization",
    "projects.third.name": "Re-randomization Name",
    "projects.current.stage": "Current Stage",
    "projects.startDate": "Start Date",
    "projects.endDate": "End Date",
    "projects.startDateTooltip": "Start date cannot be later than end date",
    "projects.endDateTooltip": "End date must greater than start date",

    "projects.envs.copy": "Copy Environment",
    "projects.envs.the.copy": "Copy Environment",
    "projects.envs.oper.lock.title": "Is it confirmed to lock configuration?",
    "projects.envs.oper.unlock.title": "Is it confirmed to unlock configuration?",
    "projects.envs.oper.lock.message": "Data of some pages cannot be edited after locking configuration.",
    "projects.envs.oper.unlock.message": "Data can be edited after unlocking configuration.",
    "projects.envs.lock": "Lock Configuration",
    "projects.envs.unlock": "Unlock Configuration",
    "projects.envs.name": "Environment Name",
    "projects.envs.cohort.type": "Cohort Type",
    "projects.envs.cohort.type.cohort": "Ordinary Cohort",
    "projects.envs.cohort.type.reRandom": "Re-randomization Cohort ",
    "projects.envs.current": "Current Environment",
    "projects.envs.new": "New Environment",

    "projects.users": "Project User",
    "projects.users.all": "All User",
    "projects.users.add": "Add User",
    "projects.users.edit": "Edit User",
    "projects.storehouse.bind": "Allocate Depot",
    "projects.storehouse.connected": "Sync or Not",

    "customers.add": "Add Customer",
    "customers.edit": "Edit Customer",
    "customers.name": "Customer Name",

    "roles.add": "Add Role",
    "roles.edit": "Edit Role",
    "roles.name": "Role Name",
    "roles.scope": "Classification",

    "user.all": "All",
    "user.add": "Add User",
    "user.edit": "Edit User",
    "user.name": "Name",
    "user.allocationRole": "Assign Role",
    "user.allocationRole.info": "Please select the role to assign first",
    "user.allocationProject": "Assign Project",
    "user.list": "User List",
    "user.no.exist": "User does not exist",
    "user.status.notActive": "Inactived",
    "user.status.active": "Activated",
    "user.status.open": "Enabled",
    "user.status.close": "Closed",
    "user.status.unauthorized": "Unauthorized",
    "user.resend.invite.email": "Resend",
    "user.resend.invite.email.info": "Is it confirmed to resend the activation email?",
    "user.customer.admin.add": "Is it confirmed to assign Admin permission to this account?",
    "user.customer.admin.remove": "Is it confirmed to remove Admin permission of this account?",
    "user.customer.learn": "Start Training",
    "user.customer.learnTrain": "Training",
    "user.customer.unLearn": "Training course is not completed yet.",
    "user.customer.isLearn": "Has the training course been completed?",
    "user.customer.learnTip": "Training Reminder",
    "user.customer.learnContext":
        "There are uncompleted training courses in the project environment. Please confirm whether to attend training.",
    "user.customer.cancel": "Return to login page and complete training courses later.",
    "user.customer.welcome": "Welcome to Login",
    "user.customer.invite": "The administrator invites you to complete system training courses.",
    "user.app.account": "APP Account",
    "user.settings.invite-again": "Invite Again",
    "user.settings.reauthorization": "Reauthorization",
    "user.settings.batch-close.tip": "Only activated/enabled users are supported",
    "form.onlyID": "Form ID",
    "form.setting": "Form Configuration",
    "form.preview": "Preview",
    "form.field.name": "Variable Name",
    "form.field.label": "Field Name",
    "form.modify": "Editable ",
    "form.application.type": "Application Type",
    "form.application.type.register": "Subject Registration",
    "form.application.type.formula": "Custom formula",
    "form.application.type.doseAdjustment": "Dose Adjustment",
    "form.application.type.variable": "Variable ID",
    "form.list.modify": "Editable or Not",
    "form.required": "Required",
    "form.control.type": "Control Type",
    "form.control.type.input": "Input Box",
    "form.control.type.inputNumber": "Numeric Input Box",
    "form.control.type.textArea": "Multiline Text Box",
    "form.control.type.checkbox": "Checkbox",
    "form.control.type.format.limit": "The character length is limited to {length},please re-enter.",
    "form.control.type.radio": "Radio Box",
    "form.control.type.switch": "Switch",
    "form.control.type.date": "Date Selection Box",
    "form.control.type.dateTime": "TimePickerDialog",
    "form.control.type.select": "Drop-down Box",
    "form.control.type.maximum": "Subject ID Digit",
    "form.control.type.exact": "Exact value for Subject ID",
    "form.control.type.le": "Less than or Equal to",
    "form.control.type.eq": "Equal to",
    "form.control.type.options": "Option",
    "form.control.type.label": "Name",
    "form.control.type.value": "Value",
    "form.control.type.format": "Format type",
    "form.control.type.format.characterLength": "Character Length",
    "form.control.type.format.numberLength": "Number Length",
    "form.control.type.format.decimalLength": "Decimal Length",
    "form.control.type.format.checkbox": "Checkbox",
    "form.control.type.variableFormat": "Variable Format",
    "form.control.type.variableFormat.tip1": "Character Length: The value specifies the maximum input length for text. Example: \"200\" allows a string with a maximum of 200 characters.",
    "form.control.type.variableFormat.tip2": "Number Length: The value indicates the permitted number of digits for an integer. Example: \"3\" restricts input to a maximum of 3-digit integers.",
    "form.control.type.variableFormat.tip3": "Decimal Length: The format specifies the maximum number length (including decimal) and decimal precision. Example: \"8.3\" allows a number with 8 total digits where the decimal part has 3 digits.",
    "form.control.type.variableRange": "Variable Range",
    "form.control.type.variableRange.min": "Please enter min value",
    "form.control.type.variableRange.max": "Please enter max value",
    "form.control.type.variableRange.validate":
        "The maximum value must be greater than or equal to the minimum value. please re-enter.",
    "form.control.type.variableRange.validate.range": "Input error, out of variable format range.",
    "form.control.type.options.lack": "Lack of options",
    "form.validate.info": "Stratification factor field cannot be editable",
    "form.picker.max.tip": "The system automatically calculates the constraint and the maximum value is selected as the current time.",
    "projects.attributes.random": "Randomize or Not",
    "projects.attributes.isRandom": "Randomize",
    "projects.attributes.randomYes": "Randomize",
    "projects.attributes.randomNo": "non-randomized",
    "projects.attributes.random.registerGroup": "Actually used IP group",
    "projects.attributes.random.registerGroup.info": "Once enabled, allows subsequent IP to be dispensed based on the actual IP group when the actual IP  group first registered after randomization does not match the randomization group.Only applies to process with consistent visit flow.",
    "projects.attributes.overdueVisit.approval": "超窗访视发放审批",
    "projects.attributes.overdueVisit.approval.info": "开启后，超窗访视需要审批通过后，才能完成研究产品的发放。",
    "projects.attributes.overdueVisit.state.mainVisit": "Main Visit",
    "projects.attributes.overdueVisit.state.Re-Dispensation": "Re-Dispense",
    "projects.attributes.overdueVisit.state.UnscheduledDispensation": "Unscheduled Dispense/独立计划外",
    "projects.attributes.random.control": "Randomization Supply Check",
    "projects.attributes.random.control.info":
        "After opening, when the inventory in the site / depot is insufficient or the IP is not configured, it is not allowed to continue random.",
    "projects.attributes.random.control.rule1": "All groups, can randomize after with sufficient inventory",
    "projects.attributes.random.control.rule1Info": "After selection, the system will force an inventory check before randomizing. Randomization should only be permitted if there is sufficient inventory within the study site to issue subjects potentially randomized to the group.",
    "projects.attributes.random.control.rule2": "Allocated groups, can randomize after with sufficient inventory",
    "projects.attributes.random.control.rule2Info": "After selection, it is necessary to ensure that the subject's group has sufficient inventory to allow randomization.",
    "projects.attributes.random.control.rule3": "Force random to available inventory group",
    "projects.attributes.random.control.rule3Info": "Subjects can be forced to be randomly assigned to a group with stocks in the site. Note that frequent execution may result in unbalanced enrollment.",
    "projects.attributes.random.control.rule3Input1": "At least",
    "projects.attributes.random.control.rule3Input2": "groups need to be supplied",
    "projects.attributes.subject.number.rules": "Subject number input rule",
    "projects.attributes.subject.number.rule1": "Customize",
    "projects.attributes.subject.number.rule2": "Auto-incrementing and unique within the project",
    "projects.attributes.subject.number.rule3": "Auto-incrementing and unique within the site",
    "projects.attributes.is.random.showNumber": "Display Randomization ID",
    "projects.attributes.is.random.showSequenceNumber": "Random Sequence Number Display",
    "projects.attributes.is.random.sequenceNumberPrefix": "Random Sequence Number Prefix",
    "projects.attributes.is.random.sequenceNumberDigit": "Random Sequence Number Digits",
    "projects.attributes.is.random.sequenceNumberStartNumber": "Random Sequence Number Start Number",
    "projects.attributes.is.random.sequenceNumber.tip": "The total number of digits for the random sequence number includes the prefix. For example, if the prefix is \"A\" and the digit count is 3, the generated sequence numbers will be A01, A02, A03.",
    "projects.attributes.is.random.number.show": "Display",
    "projects.attributes.is.random.number.notShow": "Do not display",
    "projects.attributes.dispensingDesign": "Treatment Design",
    "projects.attributes.dispensing.yes": "Dispense",
    "projects.attributes.dispensing.no": "With no dispensation operation",
    "projects.attributes.dtp.rule": "DTP Rules",
    "projects.attributes.dtp.rule.ip": "IP",
    "projects.attributes.dtp.rule.ip.tooltip": "In the project dimension, add IP shipped using the confirmed DTP method, and can be applied on subject dispensation scenario.",
    "projects.attributes.dtp.rule.visitFlow": "Visit Flow",
    "projects.attributes.dtp.rule.notApplicable": "Not Applicable",
    "projects.attributes.dispensing.noInfo":
        "After saving, the system will automatically hide the menus and functions related to dispensation, and it will take effect after logging in again.",
    "projects.attributes.isBlind": "Blind Design",
    "projects.attributes.isBlind.blind": "Blind",
    "projects.attributes.isBlind.open": "Open",
    "projects.attributes.isScreen": "Subject Screening Process",
    "projects.attributes.random.minimize.calc": "Minimized Randomization Calculation",
    "projects.attributes.visit.inheritance": "IP Inheritance",
    "projects.attributes.visit.inheritance.tip": "Upon enabling, subject visits are allowed to inherit the use of IP from the first-issued batch with valid expiration dates.",
    "projects.attributes.visit.inheritance.enough": "Remaining Visit Cycles",
    "projects.attributes.random.minimize.calc.factor": "Randomization Stratification",
    "projects.attributes.random.minimize.calc.actual.factor": "Actual Stratification",
    "projects.attributes.random.minimize.calc.notApplicable": "Not Applicable",
    "projects.attributes.random.minimize.calc.tip": "Priority is given to the use of actual stratification or, if actual stratification is not registered, randomization stratification calculations are used.",
    "projects.attributes.isFreeze.info":
        "After enabled, when running shipping algorithm, the quarantined items will be counted as available inventory.",
    "projects.attributes.saveInfo":
        'Confirmation succeeded. If "no dispensing" is selected for the project, the menu and permission about dispensing in the project will be hidden automatically, and it will take effect after re login.',
    "projects.attributes.isFreeze": "Quarantined-IP Counting Rule",
    "projects.attributes.prefix.bool": "Subject Prefix",
    "projects.attributes.prefix": "Subject ID Prefix",
    "projects.attributes.prefix.tooltip1": "Site number described as {siteNO}, connectors can be —，~，/.",
    "projects.attributes.subject.allow.replace.tip": "Dispensation projects, please configure whether replacement is allowed for corresponding visits at the same time.",
    "projects.attributes.subject.replace.text.en": "Replacement text for Subject ID (English)",
    "projects.attributes.prefix.tooltip2": "E.g. {siteNO}-A as the Rule, then Subject ID will be 09-A001,09 is the site number, 001 is the subject sequence number.",
    "projects.attributes.subject.replace.text": "Replacement text for Subject ID ",
    "projects.attributes.is.random.number": "Display Randomization ID or Not",
    "projects.attributes.dispensing": "Dispense or Not",
    "projects.attributes.blind": "Blind or Not",
    "projects.attributes.instituteLayered": "Site",
    "projects.attributes.regionLayered": "Region",
    "projects.attributes.instituteLayer": "Set site as stratification factor ",
    "projects.attributes.Layered": "Set region as stratification factor",
    "projects.attributes.countryLayered": "Set country as stratification factor",
    "projects.attributes.country": "Country",
    "projects.attributes.edc.label": "EDC Docking Treatment Design Label",
    "projects.attributes.systemRules": "System Configuration",
    "projects.attributes.subject.numberRules": "Subject ID Rules",
    "projects.attributes.other.numberRules": "Other Rules",
    "projects.attributes.blind.rules": "Blinding Restrictions",
    "projects.attributes.blind.rules.stop": "Stop Unblinded Subjects",
    "projects.attributes.blind.rules.stop.open": "After enabled, dispensation will not be allowed for emergency unblinded subjects.",
    "projects.attributes.rest.assured.stop.open": "After active, the project supports docking with Ali Health's traceability code.",
    "projects.attributes.blind.rules.stop.pv": "Include PV unblinded subjects",
    "projects.attributes.blind.rules.stop.pv.notApplicable": "Not Applicable",
    "projects.attributes.subject.type.site.numberPrefix": "Use Site Number as Prefix or Not",
    "projects.attributes.subject.prefixConnector": "Prefix Connector",
    "projects.attributes.subject.other.prefix": "Other Prefixes of Subject ID",
    "projects.attributes.subject.prefix.text": "Prefix Text",
    "projects.attributes.subject.isRandom": "Site cannot enroll subjects without assigned randomization IDs.",
    "projects.attributes.subject.isRandom.title": "Enrollment Capping",
    "projects.attributes.subject.isRandom.tips": "Site cannot enroll subjects if no randomization ID is assigned to country/site/region on project level,",
    "projects.attributes.code.config.all": "All Coding Configuration",
    "projects.attributes.code.config": "Coding Configuration",
    "projects.attributes.code.config.method": "Coding Method",
    "projects.attributes.code.rule": "Coding Rules",
    "projects.attributes.code.rule.manual": "Manual Coding Upload",
    "projects.attributes.code.rule.auto": "Automatic Coding",
    "projects.attributes.code.rule.software.informed": "Software informed",
    "projects.attributes.code.rule.information":
        "When using IRT APP, users have certain rights to know about its related functions and information collection. Common descriptions include user privacy agreement and other contents related to user accounts. After it is enabled, it will support users to sign the informed process when scanning barcodes using IRT APP. After it is enabled, it does not support modification.",
    "projects.attributes.code.rule.informed.agreement":
        "Please enter the content of the code scanning informed agreement confirmed by the company's legal affairs. The content can only be configured once and saved after confirmation.",
    "projects.attributes.code.rule.confirm.title": "Is it confirmed to save?",
    "projects.attributes.code.rule.confirm.msg": "After confirmation, the configuration cannot be changed.",
    "projects.attributes.segment": "Segmented Dispensation",
    "projects.attributes.segment.type": "Calculation Rules",
    "projects.attributes.segment.length": "Segment Length",
    "projects.attributes.groupsInfo": "Group not found, please complete group setting in Randomization Design first",
    "projects.attributes.unblindingReason": "Unblinding Reason (Radio)",
    "projects.attributes.freezeReason": "Quarantine Reason (Radio)",
    "projects.attributes.freezeReason.info": "Quarantine Reason",
    "projects.attributes.allowed.remark": "Allowed to remark",
    "projects.attributes.subject.replace": "Replacing randomization ID",
    "projects.attributes.subject.replace.customize": "Customize",
    "projects.attributes.subject.replace.auto": "Automatically generated by the system",
    "projects.attributes.subject.replace.tip": "Please confirm that the calculated replacement randomization ID is not within the range of the current randomization list, otherwise the replacement will fail.",
    "projects.attributes.subject.replace.original": "Original randomization ID",
    "projects.randomization.configure": "Randomization Design",
    "projects.randomization.list": "Randomization List",
    "projects.randomization.factor": "Stratification Factor",
    "projects.randomization.separate": "Randomization Segment",
    "projects.randomization.type": "Randomization Type",
    "projects.randomization.blockRandom": "Block Randomization",
    "projects.randomization.minimize": "Minimized Randomization",
    "projects.randomization.source": "Source of Randomization Number",
    "projects.randomization.group": "Treatment Group",
    "projects.randomization.groupRatio": "Group Ratio",
    "projects.randomization.groupMapping": "Group Mapping",
    "projects.randomization.factorRatio": "Stratification Factor Weight Ratio",
    "projects.randomization.probability": "Bias Probability",
    "projects.randomization.blockLength": "Block Length",
    "projects.randomization.total": "Total Cases",
    "projects.randomization.seed": "Randomization Seed",
    "projects.randomization.prefix": "Randomization ID Prefix",
    "projects.randomization.upload": "Upload",
    "projects.randomization.generate": "Generate",
    "projects.randomization.count": "Randomization ID Quantity",
    "projects.randomization.useCount": "Randomization ID.(used/total)",
    "projects.randomization.defaultNumber": "Initial Number",
    "projects.randomization.numberLength": "Number Length",
    "projects.randomization.numberPrefix": "Number Prefix",
    "projects.randomization.blockConfig": "Block Configuration",
    "projects.randomization.groupConfig": "Group Configuration",
    "projects.randomization.blockRule": "Block Rules",
    "projects.randomization.blockRule.order": "Order",
    "projects.randomization.blockRule.reverse": "Disorder",
    "projects.randomization.randomNumberRule": "Randomization ID Rule",
    "projects.randomization.randomNumberRule.order": "Order",
    "projects.randomization.randomNumberRule.reverse": "Disorder",
    "projects.randomization.endNumber": "Termination Number",
    "projects.randomization.weight": "Weight",
    "projects.randomization.confirmUpload": "Please upload files",
    "projects.randomization.lockFactor": "Stratification Not Found",
    "projects.randomization.selectBlock": "Please select block",
    "projects.randomization.nullFactor": "Please select stratification factor",
    "projects.randomization.nullSite": "Error, site assignment is empty, please confirm again.",
    "projects.randomization.nullCountry": "Error, country assignment is empty, please confirm again.",
    "projects.randomization.nullRegion": "Error, Region assignment is empty, please confirm again.",
    "projects.randomization.distribution": "Assign",
    "projects.randomization.distributions": "Block Assign",
    "projects.randomization.distributionCenter": "Assign Block to Site",
    "projects.randomization.distributionRegion": "Assign Block to Region",
    "projects.randomization.distributionCountry": "Assign Block to Country",
    "projects.randomization.distributionFactor": "Assign Block to Stratification Factor",
    "projects.randomization.distributionConfirm": "Is it confirmed to assign?",
    "projects.randomization.distributionContent": "Current {target} will be assigned {currentCount} random numbers and cumulatively {totalCount} random numbers have been assigned.",
    "projects.randomization.sourceSite": "Source Site",
    "projects.randomization.targetSite": "Target Site",
    "projects.randomization.design": "Randomization Design",
    "projects.randomization.form": "Form Configuration",
    "projects.randomization.selectFactor": "Select Stratification Factor",
    "projects.randomization.enterSourceSite": "Please select the site to assign.",
    "projects.randomization.enterTargetSite": "Please select destination",
    "projects.randomization.enterFactor": "Please select stratification factor",
    "projects.randomization.factorRules": "Stratification Factor Rules",
    "projects.randomization.otherFactor": "Other Stratification Factor",
    "projects.randomization.allocation.factor": "Clear Other Stratification factors",
    "projects.randomization.allocation.factorConfirm": "Is it confirmed to clear other layers?",
    "projects.randomization.allocation.block.activate.tip": "Confirm activation of this block?",
    "projects.randomization.allocation.block.activate.content": "After activation, inactive Randomization IDs within the block will be reactivated and eligible for rerandomization.",
    "projects.randomization.allocation.block.Inactivate.tip": " Confirm deactivation of this block?",
    "projects.randomization.allocation.block.Inactivate.content": "After deactivation, active Randomization IDs within the block will be marked as inactive and ineligible for randomization.",
    "projects.randomization.allocation.randomNumber.activate.tip": "Confirm activation of this Randomization ID?",
    "projects.randomization.allocation.randomNumber.activate.content": "After activation, this Randomization ID will be reactivated and eligible for randomization.",
    "projects.randomization.allocation.randomNumber.Inactivate.tip": "Confirm deactivation of this Randomization ID?",
    "projects.randomization.allocation.randomNumber.Inactivate.content": "After deactivation, this Randomization ID will be marked as inactive and ineligible for randomization.",
    "projects.site.list": "Allocate Site",
    "projects.site.no.supplyPlan": "Please assign supply plan",
    "projects.site.no.supplyPlan.store": "Please configure supply plans and depots.",
    "projects.site.no.storehouse": "Shipment addition failed, the site’s supply plan is not bound to the depot, please re-confirm.",
    "projects.site.supplyPlan.0init_supply": "Initial shipment quantity setting as 0, cannot create initial shipment.",
    "projects.site.send.medicine": "Confirm Shipment",
    "projects.site.not.enough.medicine": "Insufficient IP Inventory",
    "project.site.add_order_error": "Site has been activated. Initial shipment cannot be created again.",
    "projects.site.medicine": "Site Inventory",
    "projects.storehouse.list": "Depot List",
    "projects.storehouse.name": "Depot Name",
    "projects.storehouse.inStorehouse": "Is it confirmed that IP in this depot is delivered to the logistics warehouse?",
    "projects.site.name": "Site Name",
    "projects.site.name.standard": "Site Standard Name",
    "projects.site.short.name": "Site abbreviation",
    "projects.site.number": "Site Number",
    "projects.site.supply": "Automatic Re-Supply",
    "projects.site.open": "Open",
    "projects.site.close": "Close",
    "projects.site.isDefault.contact": "As default",
    "projects.drug.alert": "Inventory Alert",
    "projects.drug.alert.check": "Please select IP name or alert value",

    "visit.cycle.push": "Release",
    "visit.cycle.push.visit": "Release Visit",
    "visit.cycle.management": "Visit Management",
    "visit.cycle.name": "Visit Name",
    "visit.cycle.type": "Visit Offset Type",
    "visit.cycle.type.tip": "Visit Offset Type，",
    "visit.cycle.version.number": "Cycle Version No.",
    "visit.cycle.version": "Cycle Version",
    "visit.cycle.push.date": "Release Date",
    "visit.cycle.push.record": "Release Record",
    "visit.cycle.push.record.view": "View",
    "visit.cycle.type.tip.baseline": "Baseline: Calculation of the planned date of the follow-up visit based on the random date;",
    "visit.cycle.type.tip.lastdate": "Lastdate: Calculates the planned date of the follow-up visit based on the actual time of dispensation of IP.",
    "visit.cycle.day.tip": "The length of the interval between negative offsets, the system treats this event as a pre-randomization visit. The pre-randomization visit is calculated from the actual dispensation time of the first pre-randomization visit and is calculated until the randomization visit.",
    "visit.cycle.push.tip": "After published, some visit configurations will no longer be allowed to be revised.",
    "visit.cycle.window": "Visit Window",
    "visit.cycle.group": "Group",
    "visit.cycle.period": "Window",
    "visit.cycle.interval": "Interval Duration",
    "visit.cycle.setting.unscheduled_visit": "Unscheduled Visit",
    "visit.cycle.setting.nameZh": "Chinese Name",
    "visit.cycle.setting.nameEn": "English Name",
    "visit.cycle.visitNumber": "Visit Number",
    "visit.cycle.visitName": "Visit Name",
    "visit.cycle.startDays": "Start Day",
    "visit.cycle.endDays": "End Day",
    "visit.cycle.dispensing": "Allowed To Dispense",
    "visit.cycle.random": "Allowed To Randomize",
    "visit.cycle.replace": "Allowed To Subject Replace",
    "visit.cycle.dtp": "Allowed To DTP",
    "visit.cycle.doseAdjustment": "Dose Adjustment",

    "drug.configure.drugNumber.limit": "Dispensation Quantity Limit",
    "drug.configure.open.tip.one": "Open Configuration:After checked, you can directly select IP name and quantity for dispensing，also can customize dispense quantity.",
    "drug.configure.open.tip.two": "By label: Supports selection of IP label or customized quantity dispensation;",
    "drug.configure.open.tip.three": "According to the calculation formula: It can be dispensed according to the suggested value calculated by the formula.",
    "drug.configure.open.tip.four": "Customized quantity dispensation, use English commas to separate different numbers, and use \"~\" to connect the number range when config dispensation quantity.",
    "drug.configure.open.tip.six": "Dispensation Quantity: 3,7~9, indicating that IP with specifications of 3,7,8, and 9 spec can be dispensed.",
    "drug.configure.open.tip.seven": "Dispensation Quantity:2, indicating that IP only 2 spec can be dispensed.",
    "drug.configure.other.tip": "After checked,means it is an Unnumbered IP",
    "drug.configure.open.config": "Open Configuration",
    "drug.batch.management.update.info": "Batch updates only support IP operations with the same batch expiration date.",
    "drug.configure.formula.config": "According to the calculation formula",
    "drug.configure.label.config": "By label",
    "drug.configure.label.config.error": "Configuration conflict, combined labels are only applicable for fixed quantities of IP dispensation.",
    "drug.configure.label.config.duplicate.error": "Submission failed, dispensation label is empty, please confirm again.",
    "drug.configure.formula": "Formula",
    "drug.configure.formula.tip": "Calculated as follows :",
    "drug.configure.formula.tip.one": "Age: Calculated according to 365.25 days/year;",
    "drug.configure.formula.tip.two": "Simple body surface area BSA = [weight (kg) x height (cm)/3600]1/2;",
    "drug.configure.formula.tip.three": "Other body surface area BSA supports custom input formula calculation.",
    "drug.configure.formula.weight": "Weight",
    "drug.configure.formula.weight.last": "The calculated weight at the last visit",
    "drug.configure.formula.weight.actual": "The actual weight at the last visit",
    "drug.configure.formula.weight.random": "The weight at the random visit",
    "drug.configure.formula.standard": "Unit Calculation Standard",
    "drug.configure.formula.unit": "Calculation Unit",
    "drug.configure.formula.unit.capacity": "Unit capacity",
    "drug.configure.formula.weight.range": "Weight Range",
    "drug.configure.formula.height": "Height",
    "drug.configure.formula.age": "Age",
    "drug.configure.formula.age.name": "Age",
    "drug.configure.formula.age.range": "Age Range",
    "drug.configure.formula.date": "Date Of Birth",
    "drug.configure.formula.bsa": "Simple body surface area BSA",
    "drug.configure.formula.other": "Other body surface area BSA",
    "drug.configure.formula.customer": "Custom formula",
    "drug.list.serialNumber": "Sequence Number",
    "drug.configure": "Treatment Design",
    "drug.configure.group": "Group",
    "drug.configure.subGroup": "Sub Group",
    "drug.configure.drugName": "IP Name",
    "drug.isolation.quantity": "Quarantined Quantity",
    "drug.isolation.single.quantity": "Quarantine quantity(item)",
    "drug.isolation.package.quantity": "Quarantine quantity(package)",
    "drug.configure.visitName": "Visit Name",
    "drug.configure.roomNumber": "Room Number",
    "drug.configure.drugNumber": "Dispensation Quantity",
    "drug.configure.drugLabel": "Combined Dispensation Label",
    "drug.configure.showAll": "Distribute and display all",
    "drug.configure.routine.visit.mapping": "Common Visit Mapping",
    "drug.configure.drugLabel.config": "(Combined) Dispensation label",
    "drug.configure.drugSpecifications": "IP Specification",
    "drug.configure.PkgSpecifications": "Package Specification",
    "drug.configure.spec": "Specification",
    "drug.configure.delete":
        "Deleting treatment design information will have impact on packlist. Is it confirmed to delete it?",
    "drug.configure.sub-labels.tip": "Allow the multiple selection to be dispensed all at once under sub-labels/IP configuration in one visit.",
    "drug.configure.labels.tip": "Allow the single selection to be dispensed under multiple-labels in one visit.",
    "drug.configure.ip.tip": "Allow the multiple selection to be dispensed all at once under sub-labels/IP configuration in one visit.",
    "drug.configure.visitCycles.name.err": "The unscheduled visit rule does not apply by according to the calculation formula, please re-select.",
    "drug.configure.visitCycles.config.error": "Configuration conflict.",
    "drug.configure.package.setting.error": "Blind IP and open IP are not allowed in the same package, please reconfirm.",
    "drug.configure.package.setting.error1": "Save failed, mixed packages need to be configured for multiple IP.",
    "drug.configure.package.setting.error2": "Unnumbered IP cannot be configured in mixed packs. Please verify.",
    "drug.configure.package.setting.error3": "has been configured as item and generated inventory, please reconfirm the data before operation.",
    "drug.list": "IP list",
    "drug.list.packlist": "Packlist",
    "drug.list.name": "IP Name",
    "drug.list.batchMag": "Batch Management",
    "batchMag.type.depot": "Depot",
    "batchMag.type.site": "Site",
    "batchMag.type.order": "Order",
    "drug.list.isolation": "Quarantine",
    "drug.list.lost": "Lost/Wasted",
    "drug.list.setUse": "Make Available",
    "drug.list.release": "Lifting Quarantine",
    "drug.list.drugNumber": "IP NO.",
    "drug.list.expireDate": "Expiration Date",
    "drug.list.entryTime": "Warehousing Time",
    "drug.list.delete": "Mass Delete",
    "drug.list.batch": "Batch NO.",
    "drug.list.verificationCode": "IP Verification Code",
    "drug.list.status": "Status",
    "drug.list.current.status": "Current Status",
    "drug.list.delete.info": "Please tick the IP to be deleted",
    "drug.list.availableCount": "Available Inventory",
    "drug.list.number.placeholder": "Please enter IP number",
    "drug.batch.management.stock": "Batch Number",
    "drug.batch.management.status": "IP Status",
    "drug.batch.management.update": "Update Batch Number",
    "drug.batch.management.selectStatus": "Please select IP status",
    "drug.batch.management.updateExpireDate": "Update Expiry Date",
    "drug.batch.management.updateError": "Input error, the quantity of items does not match the ratio of the package, please re-input.",
    "drug.batch.management.storeGroup.add": "Add Depot Group",
    "drug.batch.management.group.add": "Add Batch Number Group",
    "drug.batch.management.group.del": "Delete Batch Number Group",
    "drug.upload.uploadDrug": "Upload Packlist",
    "medicine.but.examine": "Approval",
    "medicine.but.update": "Modify",
    "medicine.but.release": "Release",
    "medicine.status.dsm": "To be scanned",
    "medicine.status.dsh": "To be approved",
    "medicine.status.shsb": "Approval failed",
    "medicine.status.examine.tip": "Please select the IP to be reviewed.",
    "medicine.status.update.tip": "Please select the IP that need to be modified. After modification, the IP status will be updated to 'to be approved'.",
    "medicine.status.release.tip": "Please check the IP that need to be released. After release, the IP will be assigned to the corresponding depots.",
    "medicine.status.examine.confirm": "Approval Confirmation",
    "medicine.status.toBeWarehoused": "To be Warehoused",
    "medicine.status.available": "Available",
    "medicine.status.sending": "In Delivery",
    "medicine.status.quarantine": "Quarantined",
    "medicine.status.used": "Dispensed",
    "medicine.status.transit": "In Delivery",
    "medicine.status.delivered": "Confirmed",
    "medicine.status.lose": "Lost/Wasted",
    "medicine.status.expired": "Expired",
    "medicine.status.receive": "Received",
    "medicine.status.return": "Returned",
    "medicine.status.destroy": "Destroyed",
    "medicine.status.InOrder": "To be Confirmed",
    "medicine.status.stockPending": "To be warehoused",
    "medicine.status.inStorage": "In the warehouse process",
    "medicine.status.apply": "Have Applied",
    "medicine.status.frozen": "Frozen",
    "medicine.status.fzn": "Frozen",
    "medicine.status.toBeApproved": "To be approved",
    "medicine.status.under.approval": "Lifting Quarantine under approval",
    "medicine.status.locked": "Locked",
    "drug.other": "Unnumbered IP",
    "drug.medicine": "Numbered IP",
    "drug.other.name": "IP Name",
    "drug.other.count": "Quantity",
    "drug.other.singleCount": "Item Quantity",
    "drug.other.packageCount": "Packages Quantity",
    "drug.other.add.error": "The quantity of items does not match the packaging ratio, please reconfirm.",
    "drug.medicine.upload": "Upload IP",
    "drug.medicine.packlist": "Package NO.",
    "drug.medicine.packlist.settings": "Settings",
    "drug.medicine.package.serial_number": "The package sequence number",
    "drug.medicine.packlist.upload": "Upload Packlist",
    "drug.medicine.packlist.downtemplate": "Download the packlist list template",
    "drug.medicine.packlist.select.file": "Please select the packlist file to upload.",
    "drug.medicine.order.supply.ratio": "Supply Ratio",
    "drug.medicine.package.setting.isOpen": "Shipped by Package",
    "drug.medicine.package.mixed": "Mixed packaging",
    "drug.medicine.package.setting.info": "Once enabled, shipments will be created by package.",
    "drug.medicine.package.setting": "Package Configuration",
    "drug.medicine.package.reupload": "Please re-upload IP list containing package numbers.",
    "drug.medicine.package.upload": "Please upload IP list containing package numbers.",
    "drug.medicine.package.message": "The packaging number function in the uploaded IP list will be unavailable, and will create new shipments by calculating individual items.",
    "drug.medicine.packlist.setting.unProvideDate": "Do Not Dispense Setting",
    "drug.medicine.setting.unProvideDate": "Do Not Dispense Setting",
    "drug.medicine.setting.application.err1": "Adding only a single blinded IP application may result in breaking the blind.",
    "drug.medicine.setting.application.err2": "Saved Failed, only open-confirguration IP does not apply to supply ratio configurations.",
    "drug.medicine.setting.application.err3": "The IP shipped by package must be consistent with the package ratio.",
    "drug.medicine.setting.application.err4": "Saving failed, it is not allow the addition of only a single blinded IP control.",
    "drug.medicine.setting.application.err5": "Saving failed, Please set a valid default contact.",
    "validator.message.phone": "Invalid Phone Number",
    "validator.message.replace.medicine": "Please Select IP to be Replaced",
    "validator.message.site": "Please enter 1-10 digits of number/letter",

    "randomization.config.type": "Randomization Type",
    "randomization.config.group": "Treatment Group",
    "randomization.config.subGroup": "Sub Group",
    "randomization.config.factorName": "Stratification Name",
    "randomization.config.factor": "Stratification Factor",
    "randomization.config.factors": "Stratification",
    "randomization.config.region": "Region Factors",
    "randomization.config.region.duplicate": "Duplicated Region Factors",
    "randomization.config.randomList": "Randomization List",
    "randomization.config.groupAdd": "Add Treatment Group",
    "randomization.config.groupEdit": "Edit-Treatment Group",
    "randomization.config.factorAdd": "Add Stratification Factor",
    "randomization.config.factor.addError": "Save failed, unable to select different option values for the same stratification factor simultaneously.",
    "randomization.config.factorEdit": "Edit-Add Stratification Factor",
    "randomization.config.desc": "Description",
    "randomization.config.groupSize": "Block Size",
    "randomization.config.groupSize.placeholder": "Please enter. Use ‘,’ to separate multiple blocks.",
    "randomization.config.number": "Field Code",
    "randomization.config.factor.calc": "Stratification Calculation",
    "randomization.config.factor.calc.type.age": "Age",
    "randomization.config.factor.calc.type.bmi": "BMI",
    "randomization.config.factor.calc.formula": "Formula Type",
    "randomization.config.factor.calc.formula.customer": "Custom formula",
    "randomization.config.factor.calc.formula.round.up": "Ceiling rounding",
    "randomization.config.factor.calc.formula.round.down": "Floor rounding",
    "randomization.config.factor.calc.formula.keep.decimal.places": "Keep Decimal Places",
    "randomization.config.factor.calc.formula.title1": "The variable ID for the current time is {CurrentTime}.",
    "randomization.config.factor.calc.formula.title2": "The input formula only supports the following symbols:",
    "randomization.config.factor.calc.formula.footer1": "Custom formula example:",
    "randomization.config.factor.calc.formula.footer2": "({CurrentTime}-{Time variable ID})/365",
    "randomization.config.factor.calc.formula.tip": "Supports referencing fields in the form configuration to set up custom formulas for calculations. For example:",
    "randomization.config.factor.calc.formula.tip.age": "Age: Calculated according to 365.25 days/year;",
    "randomization.config.factor.calc.formula.tip.bmi": "BMI:weight/(height)²,units in kg and m.",
    "randomization.config.input.label": "Enter Field Name",
    "randomization.config.input.label.weight": "Enter Weight Field Name",
    "randomization.config.input.label.weight.placeholder": "Please enter weight field name,unit:kg",
    "randomization.config.input.label.height": "Enter Height Field Name",
    "randomization.config.input.label.height.placeholder": "Please enter height field name,unit:kg",
    "randomization.config.factor.label": "Stratification Factor Name",
    "randomization.config.factor.label.option.mapping": "Stratification Option Mapping",
    "randomization.config.factor.label.option.value.range": "Stratification Value Range",
    "randomization.config.factor.label.option.value": "Stratification Option",
    "randomization.config.code": "Group Code",
    "randomization.config.subGroup.duplicate": "Duplicate Sub Group Names",
    "randomization.config.subGroup.duplicate.all": "All Sub Group",
    "randomization.config.group.duplicate.all": "All Group",
    "randomization.random.list.invalid": "Invalidate",
    "randomization.random.number.invalid": "Invalid",
    "randomization.random.number.used": "Used",
    "randomization.random.number.unused": "Available",
    "randomization.random.number.not.available": "Unavailable",
    "randomization.random.number.block.all.usable": "Fully Available",
    "randomization.random.number.block.partially.usable": "Partially Available",
    "randomization.random.number.block.used": "Used",
    "randomization.random.number.block.invalid": "Invalid",
    "randomization.random.number.block.unavailable": "Unavailable",
    "randomization.random.number.block.status.tip.1": "Block Status Description:",
    "randomization.random.number.block.status.tip.2": "Fully Available: All Randomization IDs in the block are in an available state.",
    "randomization.random.number.block.status.tip.3": "Partially Available: There are remaining available Randomization IDs within the block.",
    "randomization.random.number.block.status.tip.4": "Used: All Randomization IDs in the block have been utilized.",
    "randomization.random.number.block.status.tip.5": "Invalid: There are invalid Randomization IDs in the block.",
    "randomization.random.number.block.status.tip.6": "Unavailable: There are unavailable Randomization IDs in the block.",
    "randomization.random.number.block.status.tip.7": "Randomization ID Status Description:",
    "randomization.random.number.block.status.tip.8": "Available: The Randomization ID can be used for randomization.",
    "randomization.random.number.block.status.tip.9": "Unavailable: The Randomization ID cannot be used for randomization.",
    "randomization.random.number.block.status.tip.10": "Invalid: The Randomization ID is invalid due to expired Group validity or an invalidated Randomization list.",
    "randomization.random.number.block.status.tip.11": "Used: The Randomization ID has been already used.",
    // "randomization.random.number.block.status": "Block Status",
    "randomization.random.list.invalid.doubt.title": "Is it confirmed to lost/wasted it?",
    "randomization.random.list.invalid.doubt":
        "Is it confirmed to invalidate the list? Randomization list cannot be recovered after the operation",
    "randomization.random.list.invalid.doubt.first":
        "Randomization / dispensation operation of some subjects have already happened. It cannot be restored once invalidated.",
    "randomization.random.list.invalid.doubt.info": "System detects",
    "randomization.random.list.invalid.doubt.second": "it cannot be recovered after invalidate the list.",
    "projects.randomization.randomizationSubTab": "Randomization List Property",
    "projects.randomization.randomNumberTab": "Randomization ID Block",
    "projects.randomization.block": "Block",
    "projects.randomization.randomNumber": "Randomization ID",
    "projects.randomization.randomSequenceNumber": "Random Sequence Number",
    "projects.randomization.groupList": "Group",
    "projects.randomization.planNumber": "Planned Randomization Number",
    "projects.randomization.randomCount": "Randomization ID Quantity",
    "projects.randomization.actualPeople": "Actual Quantity",
    "projects.randomization.planPeople": "Estimated NO.",
    "projects.randomization.alertPeople": "Subject Alert NO.",
    "projects.randomization.upperLimitPeople": "Subject limit NO.",
    "projects.randomization.alertSetFactor": "Randomization list is not assigned to a stratification factor",
    "projects.randomization.settingPeople": "Set Number",
    "projects.randomization.settingRandom":
        "This is a non-randomized project, please complete project property setting first",
    "projects.randomization.settingFactor": "Randomization list is not assigned to a stratification factor yet",
    // "projects.randomization.selectBlock":"Please select zone group",
    // "projects.randomization.sameSite":"The distributor and the receiver can not be the same",
    // "projects.randomization.distribution":"distribution",
    // "projects.randomization.sourceSite":"Distribution agencies",
    // "projects.randomization.targetSite":"Receiving institution",

    "projects.randomization.generateVerificationTips1": "Block length must be the multiple of the sum of the ratios",
    "projects.randomization.generateVerificationTips2":
        "Length of each block must be the multiple of the sum of the ratios",
    "projects.randomization.endValueTips": "The available randomization ID range cannot meet the configuration for current block, please reconfigure.",
    "projects.randomization.generateGroupWeightRatio": "Group Configuration (weight ratio)",
    "projects.randomization.generateWeightRatio": "Weight Ratio",
    "projects.randomization.generateBlockNumber": "Block Quantity",
    "projects.randomization.generateLayeredWeightRatio": "Stratification Configuration\n(weight ratio)",
    "projects.randomization.last.group": "Previous Stage Group",

    "storehouse.name": "Depot",
    "storehouse.all": "All Depots",
    "projects.statistics.site": "Site",
    "projects.statistics.selectField": "Please Select Search Field",
    "projects.storehouse.statistics.summary": "Overview",
    "projects.storehouse.statistics.sku": "Item Management",
    "projects.storehouse.statistics.other.sku": "Unnumbered Item Management",
    "projects.storehouse.statistics.material.forecast": "Material Forecast",
    "projects.material.forecast.name": "Material Name",
    "projects.material.forecast.need.transport": "Need Transport",
    "projects.material.forecast.current.stock": "Current Stock",
    "projects.material.forecast.predicted.consumption": "Predicted Consumption",
    "material.forecast.settings.project.enrollment.plan": "Project Enrollment Plan",
    "material.forecast.settings.site.enrollment.plan": "Site Enrollment Plan",
    "material.forecast.settings.enrollment.period": "Enrollment Period",
    "material.forecast.settings.target.count": "Target Count",
    "material.forecast.settings.monthly.enrollment.plan": "Monthly Enrollment Plan",
    "material.forecast.settings.weekly.enrollment.plan": "Weekly Enrollment Plan",
    "material.forecast.settings.search.site": "Search Site",
    "material.forecast.settings.planned.enrollment": "Planned Enrollment",
    "material.forecast.settings.actual.enrollment": "Actual Enrollment",
    "material.forecast.settings.period": "Period",
    "projects.statistics.sku.expirationDate": "Expiration Date",
    "projects.statistics.sku.place": "Location",
    "projects.statistics.sku.status": "Status",
    "projects.storehouse.statistics.send.unit": "Origin",
    "projects.storehouse.no.storehouse": "Please allocate depot",
    "projects.other.package.info": "In the inventory quantity display, the number of packages is shown in parentheses ().",
    "projects.other.sku.freeze.package": "Quarantined Packages",
    "projects.other.sku.freeze.single": "Quarantined Items",
    "projects.other.sku.freeze.count": "Quarantined",
    "projects.other.sku.lost.count": "Lost/Wasted",
    "projects.other.sku.lost.package": "Lost/Wasted Packages",
    "projects.other.sku.lost.single": "Lost/Wasted Items",
    "projects.other.sku.freeze.info": "Please select data with an available / Frozen quantity greater than 0 for isolation operation",
    "projects.sitePharmacy.forecast": "Latest available time for current inventory",
    "projects.other.sku.lost.info": "Please select data with an available / expired quantity greater than 0 for Lost/Wasted operation",
    "projects.sitePharmacy.medicineReceive": "Receive",
    "projects.sitePharmacy.storehouseMedicine": "Depot IP",
    "projects.sitePharmacy.availableMedicine": "Available IP",
    "projects.sitePharmacy.usedMedicine": "Used IP",
    "projects.sitePharmacy.isolateMedicine": "Quarantined IP",
    "projects.sitePharmacy.wasteMedicine": "Lost/wasted IP",
    "projects.sitePharmacy.orderHistory": "History Shipment",
    "projects.sitePharmacy.order.status": "Shipment Status",
    "projects.sitePharmacy.order.medicineQuantity": "IP Quantity",
    "projects.sitePharmacy.order.receiver": "Receiver",
    "projects.sitePharmacy.order.receiveDate": "Receiving Date",
    "projects.sitePharmacy.no.site": "Please assign site",

    "drug.freeze.release": "Lift Quarantine",
    "drug.freeze.reason": "Reason",
    "drug.freeze.form-item.reason": "Quarantined Reason",
    "drug.freeze.selectData": "Please tick the IP to be quarantined",
    "drug.freeze.number": "Quarantine Number",
    "drug.freeze.startDate": "Quarantine Date",
    "drug.freeze.operator": "Operator",
    "drug.freeze.endDate": "Closed Date",
    "drug.freeze.count": "Quantity",
    "drug.freeze.receive.count": "Received Quantity",
    "drug.freeze.confirm.count": "Confirm Quantity",
    "drug.freeze.receiveFreeze.count": "Quarantined Quantity",
    "drug.freeze.institute": "Quarantine Location",
    "drug.freeze.ipNumber": "Quarantine IP Number",
    "drug.freeze.all": "All Quarantine Management",
    "shipment.number.interval": "The Serial number range",
    "shipment.orderNumber": "Shipment NO.",
    "shipment.send": "Origin",
    "shipment.receive": "Destination",
    "shipment.status": "Status",
    "shipment.medicine": "IP",
    "shipment.supplement.mode": "Supply Mode",
    "shipment.other.drug": "Unnumbered IP",
    "shipment.drug": "IP",
    "shipment.basic.information": "Basic Information",
    "shipment.approval.confirm.title": "Approval confirmation",
    "shipment.approval.confirm": "Approval",
    "shipment.supply.count": "Sum of Supply",
    "shipment.expectedArrivalTime": "Expected Arrival Time",
    "shipment.actualReceiptTime": "Actual Receipt Time",
    "shipment.blindCount": "Blinded IP Number",
    "shipment.supply": "Current supply plan",
    "shipment.supply.info": "Choose non-default supply plan, it will creat orders automatically after approved.",
    "shipment.mode.set": "Quantity",
    "shipment.mode.reSupply": "Re-Supply",
    "shipment.mode.max": "Max Buffer",
    "shipment.mode.forecast": "Minimum Forecast",
    "shipment.mode.supplyRatio": "Supply Ratio",
    "shipment.mode.serialNo": "Serial Number",
    "shipment.cancal.order": "Cancel Shipment",
    "shipment.transit.order": "Is it confirmed to deliver this shipment?",
    "shipment.transit.work.task.title": "Is it confirmed to send delivery shipment confirmation task?",
    "shipment.transit.work.task.msg": "After confirmation, please login to the APP to complete the task.",
    "shipment.transit.confirm": "Confirm Delivery",
    "shipment.transit.skip": "Skip, manually confirmed",
    "shipment.transit.artificial.confirm": "The shipment has been manually checked and confirmed correct.",
    "shipment.lose.order": "Lost Shipment",
    "shipment.end.order": "Terminate Shipment",
    "shipment.close.order": "Close Shipment",
    "shipment.transit": "Deliver ",
    "shipment.end": "Terminate",
    "shipment.lose": "Lost",
    "shipment.received": "Receive",
    "shipment.approvalTask": "Approval Record",
    "shipment.order.contacts.detail": "Detail",
    "shipment.order.all": "All",
    "shipment.order.all-no": "All",
    "shipment.order.fail.title": "Submit failed",
    "shipment.order.page.fail": "无法创建订单，“{name}”为包装运输，同包装研究产品“{otherName}”所属群组状态为”已完成/草稿/已终止“，请重新确认。",
    "shipment.order.success.title": "The shipment will be created automatically after it is approved successfully.",
    "shipment.order.received": "Receive Shipment",
    "shipment.order.create.modeMax.info": "The destination available inventory has exceeded the maximum buffer, and IP shipment is not created.",
    "shipment.order.received.info": "Please pick out available IP. Unselected IP will be quarantined automatically, and a quarantine record will be generated",
    "single.freeze.info": "IP shipped by package should be quarantined by package.",
    "single.lost.info": "IP shipped by package should be lost/void by package.",
    "single.freeze.lost.info": "Lost/Wasted is limited to the use of 'Available'、'Expired' IP,total of {total} items",
    "single.freeze.freeze.info": "Isolation is limited to the use of 'Available'、'Frozen' IP,total of {total} items",
    "single.freeze.setUse.info": "Make Available is limited to the use of 'lost/void' IP,total of {total} items",
    "single.freeze.lost.info.hint": "For lost/voided operations, please check the “available, expired” status of the IP.",
    "shipment.order.medicines": "IP quantity details",
    "shipment.order.mediciens.changeRecords": "IP Replacement",
    "shipment.order.medicines.change": "Replace",
    "shipment.order.medicines.changeCount": "Replace Count",
    "shipment.order.medicines.change.records": "Replacement Records",
    "shipment.order.old.medicine": "Original IP",
    "shipment.order.new.medicine": "Post-replacement IP",
    "shipment.order.change.operTime": "Replacement time",
    "logistics.info.details": "details",
    "shipment.order.sendAndReceive.info": "Please select a unique origin and destination",
    "shipment.order.availableCount": "Inventory",
    "shipment.order.package.method": "Deliver Mode",
    "shipment.order.packageMethod.package": "Package",
    "shipment.order.packageMethod.single": "Item",
    "shipment.order.create.info": "Please select IP name",
    "shipment.order.supplyUnset.info": "The destination hasn't been assigned with a supply plan, please contact project admin to complete configuration.",
    "shipment.order.medicine.change.info": "Please confirm the list items, the system will reassign the IP after replacement.",
    "shipment.order.medicine.batch.info": "Once confirmed, the expiration date and batch of the corresponding IP will be automatically updated.",
    "shipment.order.medicine.change.success.info": "The IP replacement was successful and the system reassigned the IP as follows:",
    "shipment.order.change.success": "Replace Successful",
    "shipment.order.change.records": "Replacement Records",
    "shipment.order.change.count.info": "Please fill in quantity",
    "shipment.order.create.count.info": "Please fill in quantity",
    "shipment.receive.work.task.title": "Is it confirmed to send IP receiving task?",
    "shipment.receive.reason": "Not Received IP",
    "shipment.receive.user": "Receiver",
    "shipment.receive.time": "Receiving Time",
    "shipment.canceller": "Canceller",
    "shipment.cancel.time": "Cancellation Time",

    "shipment.status.requested": "Confirmed",
    "shipment.status.transit": "In Delivery",
    "shipment.status.received": "Received",
    "shipment.status.lose": "Lost",
    "shipment.status.cancelled": "Cancelled",
    "shipment.status.toBeConfirm": "To be Confirmed",
    "shipment.status.apply": "Have Applied",
    "shipment.status.end": "Terminated",
    "shipment.status.timeout": "Timeout",
    "shipment.list.role": "No Permission to View",
    "shipment.store.alarm": "Shipping Algorithm",
    "shipment.confirm.order": "Confirm Shipment",
    "shipment.oper.confirm.order": "Confirm",
    "shipment.receive.confirm.order": "To be received",
    "shipment.isolation.confirm.order": "To be quarantined",
    "shipment.cancel.order": "Cancel Shipment",
    "shipment.confirm.select": "Failed to submit. Please confirm the quantity of IP before submitting.",
    "shipment.medicine.availableCount": "Inventory",

    "projects.supplyPlan.secondSupply.tip": "Re-Supply：When available inventory in site is below the alert value, system creates shipment automatically according to re-supply value;",
    "projects.supplyPlan.forecast.tip": "Minimum Forecast: The system will predict  IP dispensation quantity according to the minimum window period. When the available inventory of the site does not meet the expected quantity, it will create new shipments according to IP dispensation quantity of the maximum window period;",
    "projects.supplyPlan.buffer.tip": "Max Buffer：When the site inventory reaches the warning value, the site order will be automatically triggered according to the max buffer value and site inventory.",
    "projects.supplyPlan.na.tip": "NA:The system will have no condition to match and to trigger the site automatic order.",
    "projects.supplyPlan.na.alert": "System will have no condition to match and create the automatic shipment for site.",
    "projects.supplyPlan.max.alert": "The max forecast window must be larger than or equal to the min forecast window",

    "projects.supplyPlan.warning.tip": "Site Inventory Alert Value, when the corresponding IP in the site falls below the alert value, it will trigger an alert email notification to the relevant role users.",
    "projects.supplyPlan.warning.dispensing.tip": "Subject Dispensation Alert Value, dispensation will be restricted when the IP inventory falls below the corresponding subject dispensation alert value.",


    "projects.supplyPlan.order.fail": "Automatic order will be created fail.",
    "projects.supplyPlan.order.no.auto": "System will have no condition to match and create the automatic shipment for site.",
    "projects.supplyPlan.unDistributionDate.err": "The number of Do-Not-Deliver days must be greater than or equal to the sum of Do-Not-Count days and Do-Not-Dispense days.",
    "projects.supplyPlan.unDistributionDate.tip": "Do Not Deliver, refers to the number of days until the IP expires, and IP will not be shipped from the depot.",
    "projects.supplyPlan.notCountedDate.tip": "Do not count, refers to the number of days which a typical shipment will take to be delivered to the site.",
    "projects.supplyPlan.unProvideDate.tip": "Do Not Dispense Setting: The number of days from expiry date that IP will not be dispensed to a subject.",
    "projects.supplyPlan.all": "All Supply Plan",
    "projects.supplyPlan.warning": "Alert Value",
    "projects.supplyPlan.warning.site": "Site Inventory Alert Value",
    "projects.supplyPlan.warning.dispensing": "Subject Dispensation Alert Value",
    "projects.supplyPlan.buffer": "Max Buffer",
    "projects.supplyPlan.forecast": "Forecast Window",
    "projects.supplyPlan.secondSupply": "Re-Supply",
    "projects.supplyPlan.initSupply": "Quantity of Initial Shipment",
    "projects.supplyPlan.unDistributionDate": "Do Not Deliver",
    "projects.supplyPlan.notCountedDate": "Do Not Count",
    "projects.supplyPlan.unProvideDate": "Do Not Dispense Setting",
    "projects.supplyPlan.validityReminder": "Expiration Reminder",
    "projects.supplyPlan.autoSupply": "Automatic Re-Supply",
    "projects.supplyPlan.autoSupplySize": "Auto-Ration Mode",
    "projects.supplyPlan.supplyMode": "Supply Mode",
    "projects.supplyPlan.name": "Supply Plan Name",
    "projects.supplyPlan.site": "Site",
    "projects.supplyPlan.storehouse": "Depot",
    "projects.supplyPlan.description": "Plan Description",
    "projects.supplyPlan.allSupply": "Full-IP Supplement",
    "projects.supplyPlan.singleSupply": "Single-product Supplement",
    "projects.supplyPlan.allSupplyAndMedicine": "Full-IP Supplement Plus One Random IP/Package",
    "projects.supplyPlan.singleSupplyAndMedicine": "Single-product Supplement Plus One Random IP/Package",
    "projects.supplyPlan.control": "Supply Plan Control",
    "projects.supplyPlan.control.tips": "Supply Plan Control: After activation, research products with different attributes can be independently configured and different central inventory verification methods can be applied; \n" +
        "Central Inventory Alert: Based on the \"Central Inventory Alert Value\" configured in the Plan, if the conditions are met, a central inventory alert email notification will be triggered; \n" +
        "Automatic Supply: The center will activate automatic supply, verify inventory based on the triggering conditions of automatic order verification, and create an automatic order when inventory is insufficient; \n" +
        "Blind Research Product Minimum Prediction Automatic Supply Deactivation: Due to the requirement of consistent automatic allocation methods for blind research products, this option is selected when blind product automatic supply is not needed, and the corresponding window period is configured as 0-0.",
    "projects.supplyPlan.siteWarning": "Site Inventory Alert",
    "projects.supplyPlan.auto": "Automatic Supply",
    "projects.supplyPlan.drugBlind": "Blind IP",
    "projects.supplyPlan.drugOpen": "Open IP",
    "projects.supplyPlan.drugBlindAuto": "Blind IP minimum predicted automatic supply shutdown",

    "projects.subject.selectSite": "No Site Selected or No Permission to View",
    "subject.registration.success": " Registration Successful",
    "subject.status.registered": "Registered",
    "subject.status.to.be.random": "To be randomized",
    "subject.status.filtered": "Screened",
    "subject.status.random": "Randomized",
    "subject.status.blinded.urgent": "Emergency Unblinded",
    "subject.status.blinded.pv": "PV Unblinded",
    "subject.status.exited": "Deactivated",
    "subject.status.replace": "Replaced",
    "subject.status.screen.success": "Screening successful",
    "subject.status.screen.fail": "Screening failed",
    "subject.status.finish": "Complete Study",
    "export.random.toBeRandom": "To be randomized",
    "subject.status.invalid": "Invalid",
    "subject.status.join": "Enrollment",
    "subject.confirm.random": "The relevant information that affects subject random results cannot be changed once the randomization is successful.",
    "subject.check.random.form": "All forms must be filled out",
    "subject.confirm.exited": "Is it confirmed to stop this subject?",
    "subject.confirm.finish": "Is it confirmed to let the study complete?",
    "subject.confirm.transport": "Is it confirmed to transfer to other site?",
    "subject.confirm.transport.content": "Subjet data only viewed in new site after transfered.",
    "subject.confirm.switch.cohort": "Is it confirm to switch cohort?",
    "subject.confirm.switch.cohort.content": "After switching, subject data will be transferred to the new cohort.",
    "subject.random": "Randomize",
    "subject.update": "Modify",
    "subject.exited": "Stop",
    "subject.transport": "Transfer",
    "subject.switch.cohort": "Switch Cohort",
    "subject.screen": "Screening",
    "subject.finish": "Complete Study",
    "subject.screen.title": "Subject Screening",
    "subject.finish.title": "Subject Complete Study",
    "subject.screen.field": "Screening successful or not",
    "subject.screen.time.field": "Screening Date",
    "subject.screen.ICF.field": "ICF Signed Date",
    "subject.current.site": "Current Site",
    "subject.new.site": "Site",
    "subject.current.cohort": "Current Cohort",
    "subject.new.cohort": "New Cohort",
    "subject.stop.time": "Actual Stopped Date",
    "subject.unblinding.urgent": "Emergency Unblinding",
    "subject.unblinding.pv": "PV Unblinding",
    "subject.unblinding.ip": "IP Unblinding",
    "subject.unblinding.sponsor": "Whether the Sponsor has been notified or not.",
    "subject.trail": "Trail",
    "subject.register": "Register",
    "subject.register.add": "Subject Registration",
    "subject.register.update": "Subject Modify",
    "subject.register.edit": "Subject Edit",
    "subject.reason": "Reason for Stop",
    "subject.finish.remark": "Remark",
    "subject.unblinding.remark": "Remark",
    "subject.unblinding.reason": "Unblinding Reason",
    "subject.unblinding.time": "Unblinding Time",
    "subject.unblinding.real.title":
        "Please confirm the IP dispensed by system is consistent with the one actually dispensed!",
    "subject.unblinding.real.true": "Consistent or Registered,",
    "subject.unblinding.real.false": "If inconsistent, please register actually used IP for tracking purpose,",
    "subject.unblinding.real.continue": "Continue to Unblind>>",
    "subject.unblinding.real.register": "Go to Register>>",
    "subject.sign.out": "Stop",
    "subject.replace": "Replace",
    "subject.register.replace": "Subject Replacement",
    "subject.number": "Subject ID",
    "subject.number.replace": "Replace {label}",
    "subject.replace.button": "Confirm Randomization",
    "subject.replace.site.tip.title": "Is it confirmed to replace?",
    "subject.replace.site.tip.content": "Replacement subject is not under the same site as current subjects, and there may be a risk of imbalanced proportions under the site.",
    "subject.replace.factor.tip.content": "Replacement subject is not under the same stratification as current subjects, and there may be a risk of imbalanced proportions under the site.",
    "subject.number.random.replace": "Replacing randomization ID",
    "subject.number.no.empty": "Required",
    "subject.already.unblinding": "Unblinded",
    "subject.Unblinded": "Blinded",
    "subject.brokenBlinded": "Unblind",
    "subject.number.digit.no": "Entered length is not equal to pre-set value",
    "subject.unblinding.download": "Download Unblinding Report",
    "subject.dispensing.download": "Download Dispensation Report",
    "dispensing.approval.name.main-visit": "IP Dispense Application",
    "dispensing.approval.name.out-visit": "IP Dispense application Unscheduled Visit",
    "dispensing.approval.name.reissue": "Re-dispensation Application",
    "subject.random.download": "Download Randomization Report",
    "subject.unblinding.confirm": "Is it confirmed to unblind the following subject?",
    "subject.unblinding.confirmTip": "Unblinding Confirmation",
    "subject.invalid.list": "Invalid List",

    "subject.dispensing.subject.detail": "View Subject Information Details",
    "subject.dispensing.subjectInfo": "Subject Dispensation Information",
    "subject.dispensing.info": "Dispensation Information",
    "subject.replace.info.old": "Original subject information",
    "subject.dispensing.apply.subjectInfo.apply": "Subject Dispensation Application Details",
    "subject.dispensing.apply.time": "Application Time",
    "subject.dispensing.apply.order.tip":
        "Shipment has been applied successfully, please inform depot to provide IP in time.",
    "subject.dispensing.apply.order.tip.apply": "Shipment status is ‘Applied’, please check it on ‘Shipments’ page.",
    "subject.dispensing.apply.order.tip.to_send": "Shipment status is ‘To be Confirmed’, please confirm it on ‘Shipments’ page.",
    "subject.dispensing.subject": "Subject Information",
    "subject.dispensing.record": "Dispensation Record",
    "subject.dispensing.time": "Operation Time",
    "subject.dispensing.drugNumber": "IP Number",
    "subject.dispensing.drugNumber.system": "IP Number in system",
    "subject.dispensing.registration.success": "Registration Successful!",
    "subject.dispensing.realDispensing.res": "The system found that the registered IP group did not match the randomization group, and the subsequent dispensation group will be the actual registered IP group at this time.",
    "subject.dispensing.realDispensing": "Actually Used IP Number",
    "subject.dispensing.realDispensingTip": "Is it confirmed to register actually dispensed IP?",
    "subject.dispensing.realDispensingConfirm": "After registration, IP status will be updated to ‘Dispensed’.",
    "subject.dispensing.realDispensingConfirmDTP": "After registration, this DTP IP will trigger a new shipment with a confirmed status.",
    "subject.dispensing.selectDrugNumber": "Please select IP number",
    "subject.dispensing.selectDrugNumber.label": "Please select IP label",
    "subject.dispensing.selectedRepeatedly": "IP cannot be selected repeatedly",
    "subject.dispensing.label.selectedRepeatedly": "IP label cannot be selected repeatedly",
    "subject.dispensing.NotFilledDrug": "Name or quantity of IP is incomplete",
    "subject.dispensing.label.typeSelectedRepeatedly": "Multiple dispensation is not allowed when combination labeling.",
    "subject.dispensing.formula.dispensing": "Formula Dispense",
    "subject.dispensing.label.dispensing": "Label Dispense",
    "subject.dispensing.drugName.dispensing": "IP Dispense",
    "subject.dispensing.drugName": "IP Name",
    "subject.dispensing.drugLabel": "IP Label",
    "subject.dispensing.dose.tip": "Last dispensation level:{last}, current dispensation level:{current}.",

    "subject.dispensing.dispensing": "Dispense",
    "subject.dispensing.reissue": "Re-Dispense",
    "subject.dispensing.replace": "Replace IP",
    "subject.dispensing.replace.reason": "Reason for Replacement",
    "subject.dispensing.cancel": "Withdraw",
    "subject.dispensing.visitSign": "Unscheduled",
    "subject.dispensing.visitSignDispensing": "Unscheduled Dispense",
    "subject.dispensing.visitSignDispensingReason": "Unscheduled Dispensation Reason",
    "subject.dispensing.reissue.reason": "Re-dispensation reason",
    "subject.dispensing.room": "Room Number",
    "subject.dispensing.retrieval": "Retrieve",
    "subject.dispensing.invalid": "Do Not Attend the Visit",
    "subject.dispensing.resumeTip": "Is it confirmed to restore dispensation?",
    "subject.dispensing.invalidTip": "Is it confirmed not to attend this visit?",
    "subject.dispensing.confirmInvalid": "If it is confirmed, you will skip the IP dispensation part of this visit.",
    "subject.dispensing.confirmRetrieval": "Is it confirmed to retrieve?",
    "subject.dispensing.retrieve.freeze":
        "After retrieval, IP will be available or be frozen.",
    "subject.dispensing.number.register": "Registration of Actually Used IP",
    "subject.dispensing.form.number": "Dispensed IP",
    "subject.dispensing.form.realNumber": "Actually Dispensed IP",
    "subject.dispensing.medicine.available.frozen": "Available/Frozen",
    "subject.dispensing.medicine.validator": "The upper limit is {data}, please re-enter.",
    "subject.dispensing.outsize": "Overdue",
    "subject.dispensing.no.join": "Not Attend",
    "subject.dispensing.plan.time": "Scheduled Visit",
    "subject.dispensing.actual.time": "Actual Dispensation",
    "subject.dispensing.outsize.reason": "The dispensation is out of the visit window, please comment reason.",
    "subject.dispensing.open.visit": "Open follow-up visit stage",
    "subject.dispensing.open.visit.tip1": "Once enabled, it will close {atThisStage} dispensation function, and allow the dispensation of {nextStage}.",
    "subject.dispensing.open.visit.tip2": "Once enabled, it will close {atThisStage} dispensation function.",

    "subject.dispensing.retrievalMessage":
        "After retrieval, IP will be available or be frozen.",
    "subject.dispensing.placeholder.input.count": "Please enter the quantity",
    "subject.dispensing.placeholder.input.expiration": "Please enter Expiration date",
    "subject.dispensing.placeholder.input.batch": "Please enter batch",
    "subject.dispensing.placeholder.input.dispensing.count": "Please enter dispensation quantity",


    "subject.dispensing.apply": "Dispensation Application",
    "subject.apply": "Apply",
    "subject.dispensing.visitSignDispensing.apply": "Unscheduled Dispensation Application",
    "subject.dispensing.reissue.apply": "Re-dispensation Application",
    "subject.dispensing.visitSignDispensingReason.apply": "Unscheduled Dispensation Application Reason",
    "subject.dispensing.order.view": "View Shipments",
    "subject.dispensing.detail": "Return to Details",
    "subject.dispensing.apply.success": "Application succeeded",
    "subject.dispensing.dtp.success": "DTP Order created successfully",
    "subject.random.confirm": "Randomization Confirmation",
    "subject.random.select.confirm":
        "System will replace IP according to the submitted information, please confirm the following information.",
    "subject.dispensing.confirm":
        "System will dispense IP according to the submitted information, please confirm the following information.",
    "subject.dispensing.confirm.replace": "System will perform IP replacement according to submitted information. Please confirm below information.",
    "subject.dispensing.replace.info": "Replace IP",
    "subject.dispensing.replace.select": "Select IP to be Replaced",
    "subject.dispensing.replace.confirm": "IP Replacement confirmation",
    "subject.dispensing.available.frozen": "Available/Frozen",
    "subject.random.info": "Randomization Information",
    "subject.dispensing.drug.limit": "The maximum limit is exceeded, please re-enter",


    "subject.dispensing.drug.formula.tip": "The system suggest {number} {spec} (unit IP: {specifications}{unit}/{spec}), and the actual dosage is {count}{unit} .",
    "subject.dispensing.drug.formula.tip.start": "The system suggest ",
    "subject.dispensing.drug.formula.tip.end": `{spec}  (unit IP: {specifications}{unit}/{spec})and the actual dosage is `,
    "subject.dispensing.drug.formula.weight.tip": "The system suggest {number} {spec}.",
    "subject.dispensing.drug.formula.weight.tip.start": "The system suggest ",
    "subject.dispensing.drug.formula.two.tip": "Compared with {comparisonType}, the change is >{radio}%, and this time the calculated body weight is the {currentComparisonType}.",
    "subject.dispensing.drug.formula.two.tip.start": "Compared with {comparisonType}, the change is {comparisonSymbols} ",
    "subject.dispensing.drug.formula.two.tip.end": ", and this time the calculated body weight is the {currentComparisonType}.",
    "subject.dispensing.drug.customer.formula.tip": "{unit}  (unit IP:{spec}). ",
    "subject.dispensing.drug.input.error": "The input quantity is not within the set range, please re-enter.",
    "subject.dispensing.drug.input.error.age": "Unable to match age/weight calculation range, please confirm again.",
    "subject.dispensing.drug.formula.error": "Calculation goes incorrect, the calculated value does not match the amount to be dispensation.",
    "subject.dispensing.form.factor.title": "Form/Stratification Factor Name",
    "subject.dispensing.factor.title": "Value(randomization stratification/ registration form)",
    "subject.dispensing.actual.factor.title": "Value(actual stratification)",
    "subject.random.success": "Randomization Succeeded",
    "subject.random.fail": "Randomization failed",
    "subject.random.all": "All subjects",
    "subject.confirm.random.button": "Confirm Randomization",
    "subject.confirm.dispensing.button": "Confirm Dispensation",
    "subject.random.section1": "Is it confirmed ",
    "subject.random.section2": "to complete the first stage of randomization, and proceed with the second stage of randomization ",
    "subject.random.section3": " ",

    "notice.type": "Notification Type",
    "notice.basic.settings": "Basic Settings",
    "notice.subject.add": "Subject Register",
    "notice.subject.random": "Subject Randomization",
    "notice.subject.signOut": "Subject Stop",
    "notice.subject.replace": "Subject Replacement",
    "notice.subject.screen": "Subject Screening",
    "notice.subject.update": "Subject Modify",
    "notice.subject.dispensing": "Subject Dispensation",
    "notice.subject.alarm": "Subject Alert",
    "notice.subject.unblinding.type": "Unblinding Type",
    "notice.subject.unblinding": "Emergency Unblinding",
    "notice.subject.pv.unblinding": "PV Unblinding",
    "notice.subject.ip.unblinding": "IP Unblinding",
    "notice.medicine.isolation": "IP Quarantine",
    "notice.order.timeout": "Late Shipment Alert",
    "notice.subject.medicine.alarm": "Subject Depots/Site Alert Reminder",
    "notice.subject.medicine.capping": "Subject Depots/Site Upper Limit Reminder",
    "notice.medicine.un_provide_date": "Do Not Dispense days",
    "notice.subject.alert.threshold": "Subject limit setting reminder",
    "notice.email.content": "Email Content",
    "notice.email.bodyContent": "Body Configuration",
    "notice.medicine.order": "Supply Shipment",
    "notice.medicine.retrieve": "IP Return",
    "notice.medicine.reminder": "IP Expiration Reminder",
    "notice.medicine.alarm": "Site Inventory Alert",
    "notice.storehouse.alarm": "Depot Inventory Alert",
    "order.timeout.days": "Number of days from shipment confirmation",
    "order.send.days": "Number of days from shipment in delivery",
    "notice.config.content": "Content Configuration",
    "notice.config.content.group": "Unblinded roles will see the information, while blinded roles will see ***.",
    "notice.config.state": "Scene",
    "notice.content.group": "Group",
    "notice.content.number": "Randomization ID",
    "notice.state.dispensing": "Dispensation",
    "notice.state.unscheduled": "Unscheduled Dispensation",
    "notice.state.re.dispensing": "Re-Dispensation",
    "notice.state.replace": "Replace IP",
    "notice.state.retrieval": "Retrieve IP",
    "notice.state.register": "Actually Dispensed IP",
    "notice.state.not-attend": "Do Not Attend the Visit",
    "notice.state.form_factor": "Form/Factor Information",
    "notice.state.screen": "Screen Information",
    "notice.state.stop": "Stop Information",
    "notice.state.finish": "Complete study Information",
    "notice.order.scene.apply": "Apply",
    "notice.order.scene.applyFail": "Application approval failed",
    "notice.order.scene.create": "Create (manual shipment)",
    "notice.order.scene.confirm": "Confirm",
    "notice.order.scene.cancel": "Cancel",
    "notice.order.scene.close": "Close",
    "notice.order.scene.transport": "Deliver",
    "notice.order.scene.receive": "Receive",
    "notice.order.scene.stop": "Termination",
    "notice.order.scene.lost": "Lost",
    "notice.order.scene.autoCreate": "Create (automatic shipment)",
    "notice.order.scene.autoCreateFail": "Creation failed (automatic shipment)",
    "notice.order.scene.autoAlarm": "Automatic Shipment Alerts",
    "notice.order.scene.change": "IP Shipment Update",
    "notice.order.scene.batch": "Batch Number And Expiration Date Update",
    "notice.order.tip": "Only for created IP shipment to site",
    "notice.order.scene.isolation": "Quarantine",
    "notice.order.scene.release": "Lifting Quarantine",
    "notice.alarm.scene.stock": "IP Inventory",
    "notice.alarm.scene.forecast": "Inventory used time prediction",
    "notice.alarm.scene.forecast.time": "Prediction reminder lead time",

    "tips.processing": "Processing......",
    "tips.search.permission": "Retrieving User Permissions…",

    "history.randomization": "Randomization Design Trail",
    "history.dispensing": "Dispensation Trail",
    "history.order": "Shipment Trail",
    "history.subject": "Subject Trail",
    "history.medicine": "IP Trail",
    "history.users": "User Management Trail",
    "history.medicine.drugFreeze": "Trail",
    "history.supply-plan": "Supply Plan Trail",
    "history.supply-plan-medicine": "Supply Plan Design Trail",
    "history.sign": "Investigator Signature",
    "history.date": "Signature Date",
    "history.user.search": "Please enter operator",
    "history.all": "All Trail",
    "barcode.isPackage.tip": "Once enabled, the packaging barcode feature will include both IP barcodes and packaging barcodes in the generated barcode list, applicable to scenarios where investigational products are transported in packaging.",
    "barcode.rule": "IP Barcode Rules",
    "barcode.package": "Packaging Barcode",
    "barcode.package.rule": "Packaging Barcode Rules",
    "barcode.count": "Barcode Quantity",
    "package.count": "Package Quantity",
    "barcode.add": "Generate Barcode",
    "barcode.available-count": "Available Quantity",
    "barcode.list": "Barcode List",
    "barcode.scan": "Scan for Warehousing",
    "barcode.scan.confirm": "Is it confirmed to send ‘Scan for Warehousing’ task?",
    "barcode.scan.confirm.msg": "After confirmation, please login to the APP to complete the task.",
    "barcode": "Barcode",
    "barcode.correlationID": "Task ID",
    "barcode.taskIDs": "Associated Barcode Task",
    "packageBarcode": "Package Barcode",
    "shortCode.prefix": "Short Code prefix",
    "workTask.add.success": "Task sent successfully",
    "workTask.system.add.success":
        "System has sent the dispensation confirmation task, please login the APP to complete in time.",
    "projects.storehouse.logistics.supplier": "Logistics Vendor",
    "projects.storehouse.logistics.shengsheng": "SHENGSHENG LOGISTICS",
    "projects.storehouse.logistics.catalent": "catalent",
    "projects.storehouse.logistics.baicheng": "Bioquick",
    "projects.storehouse.logistics.eDRUG": "eDRUG ",
    "projects.storehouse.not.included": "Shipment does not include\n quarantined IP",
    "storehouse.country.region": "Country and Region",
    "simulate_random.name": "Name",
    "simulate_random.all": "All Randomization Simulation",
    "simulate_random.site.count": "Site Quantity",
    "simulate_random.country.count": "Country Quantity",
    "simulate_random.region.count": "Region Quantity",
    "simulate_random.run.count": "Number of Runs",
    "simulate_random.subject.count": "Subject Quantity",
    "simulate_random.factor.ratio": "Stratification Case Numbers",
    "simulate_random.detail": "Details",
    "simulate_random.detail.unbalanced": "Detail(Disequilibrium Number)",
    "simulate_random.site.details": "Site Details",
    "simulate_random.site.disequilibrium-number": "Site[Disequilibrium Number]",
    "simulate_random.site.detail.total": "Total",
    "simulate_random.site.detail.people.count": "Number of People",
    "simulate_random.site.detail.unbalanced": "Imbalance",
    "simulate_random.run": "Run",
    "simulate_random.overview": "Overview",
    "simulate_random.overview.avgsd": "Overview(Average ± Standard Deviation)",
    "simulate_random.overview.min": "Overview(Min)",
    "simulate_random.overview.unbalanced.run.count": "Overview (number of running imbalances)",
    "simulate_random.unbalanced.run.count": "Number of Running Imbalances",
    "simulate_random.project": "Project",
    "simulate_random.project.overview": "Project Overview",
    "simulate_random.site.overview": "Site Overview",
    "simulate_random.layered.overview": "Stratification Overview",
    "simulate_random.country.overview": "Country Overview",
    "simulate_random.region.overview": "Region Overview",
    "simulate_random.combination.factor.overview": "Portfolio Stratification Overview",
    "simulate_random.combination.factor": "Portfolio Stratification",
    "simulate_random.detail.meanStandard": "Average ± Standard Deviation",
    "simulate_random.subject.count.min": "Minimum number of subjects",
    "simulate_random.average.value": "Average",
    "simulate_random.standard.deviation": "Standard Deviation",
    "page_notice.system_update": "System Update",
    "page_notice.system_update.current": "Current version",
    "page_notice.email_error": "Mail Exception",
    "page_notice.show_detail": "View Details",
    "page_notice.fail_reason": "Failure Reason",
    "page_notice.mail_detail": "Mail Details",
    "page_notice.mail_resend": "Resend",
    "page_notice.mail_resend_email": "Resend Mailbox",
    "page_notice.message_center_all": "All",
    "page_notice.message_center_read": "Unread",
    "page_notice.message_center_all_read": "All Read",

    "check.select.project": "Select Project",
    "check.select.site": "Select Site",
    "check.select.time": "Enrollment Time",
    "check.select.unlimited": "Unlimited",
    "dynamic.monitoring.total.random": "Total Randomization Quantity",
    "dynamic.monitoring.random.error": "Randomization Error",
    "check.affiliated.site": "Site",
    "check.random.group": "Group(randomized by system)",
    "check.blind.random.group": "Group(randomization list)",
    "check.blind.random.number": "Randomization ID(randomization list)",
    "check.random.time": "Randomization Time",
    "check.search": "Search",
    "check.dispensing.medicine": "IP Number/Name(dispensed by system)",
    "check.dispensing.medicine.blind": "Packlist",
    "check.dispensing.group": "IP Group",
    "check.dispensing.time": "Dispensation Time",
    "check.dispensing.error": "Dispensation Error",
    "check.subject.search": "Please enter subject ID",
    "check.system": "System Monitoring",
    "check.cohort": "Cohort/Stage",
    "group.cohort": "Stage/Group",
    "common.sites": "All Sites",

    "subject.room.download": "Export Room Number",
    "subject.with.room": "Include Room Number or Not",
    "subject.dispensing.room.download": "Download Room Number Viewing Record",


    "tips.locked": "Screen is locked, please enter your password",
    'tips.user.password-changed': "The password has been changed. please enter new password.",
    'tips.user.disabled': "The account has been disabled. Please contact the administrator.",
    'tips.user.deleted': "The account has been deleted, Please login in again.",
    "user.status.not.active": "Inactived",
    "user.status.enable": "Activated",
    "user.status.disable": "Deactivated",

    "menu.back": "Back to Homepage",
    "menu.projects.project.subject.screening": "Screening",
    "menu.projects.project.subject.register": "Register",
    "menu.projects.project.subject.randomization": "Randomize",
    "menu.projects.project.subject.unblinding": "Unblind",
    "menu.projects.project.subject.dispensing": "Dispense",
    "menu.projects.project.supply.drug_upload": "IP Upload",
    "menu.projects.project.supply.drug_freeze": "IP Quarantine",
    "menu.projects.project.build.careDesign": "Treatment Design",
    //---------------------------------菜单部分----------------------------------------
    "menu.projects.project.supply.storehouse.no_number": "Unnumbered Item Management",
    "menu.projects.project.build.push": "Push Statistics",
    "menu.home": "Workbench",
    "menu.settings": "Settings",
    "menu.settings.storehouse": "Depots",
    "menu.settings.roles": "Permissions",
    "menu.settings.users": "Users",
    "menu.projects": "Projects",
    "menu.projects.main": "Projects",
    "menu.projects.main.env": "Project Environments",
    "menu.projects.main.setting": "Project Settings",
    "menu.projects.main.setting.base": "Basic Information",
    "menu.projects.main.setting.project": "Project Information",
    "menu.projects.main.setting.function": "Business Functions",
    "menu.projects.main.setting.docking": "External Docking",
    "menu.projects.main.setting.permission": "Project Permissions",
    "menu.projects.main.setting.notice": "Project Notification",
    "menu.projects.main.setting.custom": "Custom Process",
    "menu.projects.project": "Project Details",
    "menu.projects.project.home": "Home",
    "menu.projects.project.overview": "Overview",
    "menu.projects.project.status": "Status",
    "menu.projects.project.task": "Task",
    "menu.projects.project.random.statistics": "Randomization Statistics",
    "menu.projects.project.subject.statistics": "Subject Statistics",
    "menu.projects.project.depot.ip.statistics": "Depot Item Statistics",
    "menu.projects.project.site.ip.statistics": "Site Item Statistics",
    "menu.projects.project.analysis": "Anomaly Analysis",
    "menu.projects.project.dynamics": "Notification",
    "menu.projects.project.sub": "Subject",
    "menu.projects.project.subject": "Subjects",
    "menu.projects.project.subject.urgent-unblinding": "Emergency Unblinding",
    "menu.projects.project.subject.urgent-unblinding.unblinding": "Emergency Unblinding",
    "menu.projects.project.subject.urgent-unblinding.approval-log": "Approval record",
    "menu.projects.project.subject.urgent-unblinding-pv": "PV Unblinding",
    "menu.projects.project.subject.urgent-unblinding.unblinding-pv": "PV Unblinding",
    "menu.projects.project.subject.urgent-unblinding.approval-log-pv": "Approval record",
    "menu.projects.project.subject.urgent-unblinding-ip": "IP Unblinding",
    "menu.projects.project.subject.urgent-unblinding.unblinding-ip": "IP Unblinding",
    "menu.projects.project.subject.urgent-unblinding.approval-log-ip": "Approval record",
    "menu.projects.project.subject.visit.cycle": "Visit Management",
    "menu.projects.project.supply": "Inventory",
    "menu.projects.project.supply.storehouse": "Depot Inventory",
    "menu.projects.project.supply.storehouse.summary": "Overview",
    "menu.projects.project.supply.storehouse.single": "Item Management",
    "menu.projects.project.supply.site": "Site Inventory",
    "menu.projects.project.supply.site.summary": "Overview",
    "menu.projects.project.supply.site.single": "Item Management",
    "menu.projects.project.supply.site.no_number": "Unnumbered Item Management",
    "menu.projects.project.supply.shipment": "Shipments",
    "menu.projects.project.supply.shipment.approval": "Approval Record",
    "menu.projects.project.supply.shipment.logistics": "Logistics",
    "menu.projects.project.supply.drug_recovery": "Return Shipments",
    "menu.projects.project.supply.drug_recovery.logistics": "Logistics",
    "menu.projects.project.supply.release-record": "Quarantine Management",
    "menu.projects.project.supply.drug": "IP",
    "menu.projects.project.supply.drug.order": "Shipments",
    "menu.projects.project.supply.drug.single": "Item Management",
    "menu.projects.project.supply.drug.no_number": "Unnumbered Item Management",
    "menu.projects.project.build": "Project Design",
    "menu.projects.project.build.storehouse": "Depot Management",
    "menu.projects.project.build.site": "Site Management",
    "menu.projects.project.build.site.supply-plan": "Supply Plan",
    "menu.projects.project.build.attributes": "Properties",
    "menu.projects.project.build.code_rule": "Coding Configuration",
    "menu.projects.project.build.simulate_random": "Randomization Simulation",
    "menu.projects.project.build.randomization": "Randomization Design",
    "menu.projects.project.build.randomization.design": "Randomization Design",
    "menu.projects.project.build.randomization.design.type": "Randomization Type",
    "menu.projects.project.build.randomization.design.group": "Treatment Group",
    "menu.projects.project.build.randomization.design.factor": "Stratification Factor",
    "menu.projects.project.build.randomization.design.list": "Randomization List",
    "menu.projects.project.build.randomization.design.attribute": "Randomization List Property",
    "menu.projects.project.build.randomization.design.block": "Randomization ID Block",
    "menu.projects.project.build.randomization.design.factor-in": "Stratification Factor",
    "menu.projects.project.build.randomization.form": "Form Configuration",
    "menu.projects.project.build.randomization.tooltip": "Please select the list data first",
    "menu.projects.project.build.drug": "Treatment Design",
    "menu.projects.project.build.drug.visit": "Visit Management",
    "menu.projects.project.build.drug.visit.setting": "Setting",
    "menu.projects.project.build.drug.config": "Treatment Design",
    "menu.projects.project.build.drug.config.setting": "Setting",
    "menu.projects.project.build.drug.list": "IP list",
    "menu.projects.project.build.drug.no_number": "Unnumbered IP",
    "menu.projects.project.build.drug.batch": "Batch Management",
    "menu.projects.project.build.drug.barcode": "Barcode List",
    "menu.projects.project.build.drug.barcode_label": "Label Management",
    "menu.projects.project.build.plan": "Supply Plan",
    "menu.projects.project.build.plan.config": "Supply Plan Management",
    "menu.projects.project.build.history": "Project Log",
    "menu.projects.project.settings": "Other Settings",
    "menu.projects.project.settings.notice": "Notification Settings",
    "menu.projects.project.settings.user": "User Management",
    "menu.projects.project.settings.export": "Configuration Export",
    "menu.projects.project.settings.role": "Permissions",
    "menu.projects.project.settings.config": "Project Configuration",
    "menu.projects.project.monitor": "Dynamic Monitoring",

    "menu.projects.project.info": "Project Information",
    "operation.projects.project.info.view": "View",
    "menu.projects.project.basic.information": "Basic Information",
    "operation.projects.project.basic.information.view": "View",
    "menu.projects.project.basic.environment": "Project Environment",
    "operation.projects.project.basic.environment.view": "View",
    "menu.projects.project.business.functions": "Business Functions",
    "operation.projects.project.business.functions.view": "View",
    "menu.projects.project.external.docking": "External Docking",
    "operation.projects.project.external.docking.view": "View",
    "menu.projects.project.custom.process": "Custom Process",
    "operation.projects.project.custom.process.view": "View",
    "menu.projects.project.permissions": "Project Permissions",
    "operation.projects.project.permissions.view": "View",
    "menu.projects.notice.permissions": "Project Notification",
    "operation.projects.notice.permissions.view": "View",


    "menu.projects.project.subject.medicine.trail": "Trail",
    "menu.projects.project.subject.trail": "Trail",
    "menu.projects.project.storehouse.sku.trail": "Trail",
    "menu.projects.project.drug.sku.trail": "Trail",
    "menu.projects.project.site.sku.trail": "Trail",
    "menu.projects.project.site.no_number.trail": "Trail",
    "menu.projects.project.storehouse.no_number.trail": "Trail",
    "menu.projects.project.supply.shipment.trail": "Trail",
    "menu.projects.project.recovery.trail": "Trail",
    "menu.projects.project.freeze.trail": "Trail",
    "menu.projects.project.randomization.list.trail": "Trail",
    "menu.projects.project.medicine.upload.trail": "Trail",
    "menu.projects.project.supply.order.trail": "Trail",
    "menu.report": "Report",
    "menu.report.randomizationStatisticsExport": "Randomization Statistics Report",
    "menu.report.subjectStatisticsExport": "Subject Statistics Report",
    "menu.report.siteIPStatisticsExport": "Site IP Statistics Report",
    "menu.report.depotIPStatisticsExport": "Depot IP Statistics Report",
    "menu.report.userLoginHistory": "User Login History",
    "menu.report.userRoleAssignHistory": "User Role Assign History",
    "menu.report.userRoleStatus": "User Role Status Report",
    "menu.report.auditTrailExport": "Audit Trail",
    "menu.report.auditTrailExport.build": "Project Design",
    "menu.report.auditTrailExport.settings": "Project Settings",
    "menu.report.auditTrailExport.release-record": "Quarantine Management",
    "menu.report.auditTrailExport.order": "Shipments",
    "menu.report.auditTrailExport.drug_recovery": "Return Shipments",
    "menu.report.auditTrailExport.subject": "Subject",
    "menu.report.auditTrailExport.dispensing": "Subject Dispensation",
    "menu.report.auditTrailExport.ip": "IP",
    "menu.report.auditTrailExport.userLoginHistory": "User Login History",
    "menu.report.auditTrailExport.userRoleAssignHistory": "User Role Assign History",
    "menu.report.projectPermissionConfigurationExport": "Project Permission Configuration Report",
    "menu.report.ProjectNotificationsConfigurationReportExport": "Project Notifications Configuration Report",
    "menu.report.configureReport": "Configuration Report",
    "menu.report.sourceIPExport": "Source IP List",
    "menu.report.sourceRandomizationListExport": "Source Randomization List",
    "menu.report.randomizationSimulationReport": "Randomization Simulation Report",
    "menu.report.randomizeReport": "Subject Detail Report",
    "menu.report.dispenseReport": "Dispensation Report",
    "menu.report.unblindingReport": "Unblinding Report",
    "menu.report.IPUnblindingReport": "IP Unblinding Report",
    "menu.report.roomNumberViewHistory": "Room Number View History",
    "menu.report.depotItemReport": "Depot Item Report",
    "menu.report.siteItemReport": "Site Item Report",
    "menu.report.sourceIpUploadHistory": "Source IP Upload History",
    "menu.report.shipmentOrdersReport": "Shipments Report",
    "menu.report.returnOrdersReport": "Return Shipments Report",
    "menu.report.randomizationSimulationResult": "Randomization Simulation Result",
    "menu.report.RandomizationSimulationPDFExport": "Randomization Simulation Report",
    "menu.report.forecastingPrediction": "Forecasting Prediction Report",
    "menu.report.visitForecast": "Visit Statistics Report",


    //多语言
    "menu.projects.project.multiLanguage": "Multilingual",
    "menu.projects.project.multiLanguage.details": "Language Details",

    //---------------------------------菜单部分----------------------------------------
    //---------------------------------权限部分----------------------------------------
    "operation.settings.roles.view": "View",
    "operation.settings.roles.add": "Add",
    "operation.settings.roles.edit": "Edit",
    "operation.settings.roles.config": "Set Permissions",
    "operation.settings.roles.export": "Export",
    "operation.settings.users.view": "View",
    "operation.settings.users.add": "Add",
    "operation.settings.users.setting": "Admin",
    "operation.settings.users.edit": "Edit",
    "operation.settings.users.close": "Close",
    "operation.settings.users.close.batch": "Batch Closing",
    "operation.settings.users.invite-again": "Invite Again",
    "operation.settings.users.cancel": "Cancel",
    "operation.settings.users.export": "Export",
    "operation.settings.storehouse.view": "View",
    "operation.settings.storehouse.add": "Add",
    "operation.settings.storehouse.edit": "Edit",
    "operation.settings.storehouse.delete": "Delete",
    "operation.projects.main.view": "View",
    "operation.projects.main.create": "Create Project",
    "operation.projects.main.config.view": "View",
    "operation.projects.main.config.create": "Add Environment",
    "operation.projects.main.config.copy": "Copy Environment",
    "operation.projects.main.config.edit_env": "Edit Environment",
    "operation.projects.main.config.unlock": "Unlock",
    "operation.projects.main.config.lock": "Lock",
    "operation.projects.main.config.add": "Add",
    "operation.projects.main.config.edit": "Edit",
    "operation.projects.main.config.delete": "Delete",
    "operation.projects.main.config.copy_cohort": "Copy",
    // "operation.projects.main.config.edit": "Manage",
    "operation.projects.main.setting.view": "View",
    "operation.projects.main.setting.base.view": "View",
    "operation.projects.main.setting.base.edit": "Edit",
    "operation.projects.main.setting.base.modify": "Modify",
    "operation.projects.main.setting.function.view": "View",
    "operation.projects.main.setting.function.edit": "Edit",
    "operation.projects.main.setting.function.admin": "Admin Enable/Disable",
    "operation.projects.main.setting.docking.view": "View",
    "operation.projects.main.setting.docking.edit": "Edit",
    "operation.projects.main.setting.custom.view": "View",
    "operation.projects.main.setting.custom.edit": "Edit",
    "operation.projects.main.setting.permission.view": "View",
    "operation.projects.main.setting.permission.add": "Add",
    "operation.projects.main.setting.permission.edit": "Edit",
    "operation.projects.main.setting.permission.setting": "Permission Setting",
    // "operation.projects.main.setting.permission.export": "Export",
    //项目 项目 项目设置 项目通知
    "operation.projects.main.setting.notice.view": "View",
    "operation.projects.main.setting.notice.add": "Add",
    "operation.projects.main.setting.notice.delete": "Delete",
    "operation.projects.main.setting.notice.edit": "Edit",
    "operation.projects.home.view": "View",
    "operation.project.status.view": "View",
    "operation.project.task.view": "View",
    "operation.project.analysis.view": "View",
    "operation.project.dynamics.view": "View",
    "operation.project.site.IPStatistics.view": "View",
    "operation.project.site.IPStatistics.download": "Export",
    "operation.project.depot.IPStatistics.view": "View",
    "operation.project.depot.IPStatistics.download": "Export",
    "operation.project.subject.view": "View",
    "operation.project.subject.download": "Export",
    "operation.project.random.view": "View",
    "operation.project.random.download": "Export",
    "operation.project.depot.download": "Export",
    "operation.subject.view-list": "View",
    "operation.subject.random": "Randomize",
    "operation.subject.replace": "Replace Subject",
    "operation.subject.trail": "View",
    "operation.subject.unblinding-pv": "PV Unblinding",
    "operation.subject.medicine.view-dispensing": "View Subject Information",
    "operation.subject.medicine.transport": "Transport",
    "operation.subject.medicine.trail": "View",
    "operation.subject.medicine.dispensing": "Dispense",
    "operation.subject.medicine.reissue": "Re-Dispense",
    "operation.subject.medicine.replace": "Replace IP",
    "operation.subject.medicine.resume": "Recover Dispensation",
    "operation.subject.medicine.formula.update": "Modify",
    "operation.subject.medicine.joinTime": "Edit",
    // "operation.subject.unblinding": "Unblinding (Urgent)",
    "operation.subject.unblinding": "View",
    "operation.subject.unblinding-application": "Emergency Unblinding application",
    "operation.subject.unblinding-approval": "Emergency Unblinding approval",
    "operation.subject.unblinding-log": "View",
    "operation.subject.unblinding-sms": "Send SMS",
    "operation.subject.unblinding-print": "Print",
    "operation.subject.unblinding-pv-view": "View",
    "operation.subject.unblinding-pv-application": "Unblinding（PV）Application",
    "operation.subject.unblinding-pv-approval": "Unblinding（PV）Approval",
    "operation.subject.unblinding-pv-log": "View",
    "operation.subject.unblinding-pv-sms": "Send SMS",
    "operation.subject.unblinding-pv-print": "Print",
    "operation.subject.unblinding-ip-view": "View",
    "operation.subject.unblinding-ip-application": "Unblinding（IP）Application",
    "operation.subject.unblinding-ip-approval": "Unblinding（IP）Approval",
    "operation.subject.unblinding-ip-log": "View",
    "operation.subject.unblinding-ip-sms": "Send SMS",
    "operation.subject.unblinding-ip-print": "Print",
    // "operation.subject.download": "Download Unblinding Report",
    // "operation.subject.medicine.export": "Download Dispensing Report",
    // "operation.subject.download-random": "Download Randomization Report",
    "operation.subject.registered": "Register Subject",
    "operation.subject.invalid-list": "Invalid List",
    "operation.subject.switch.cohort": "Switch Cohort",
    "operation.subject.update": "Edit Subject",
    "operation.subject.delete": "Delete Subject",
    "operation.subject.medicine.room": "View Room Number",
    "operation.subject.medicine.retrieval": "Retrieve",
    "operation.subject.medicine.out-visit-dispensing": "Unscheduled Dispense",
    "operation.subject.medicine.invalid": "Do Not Attend the Visit",
    "operation.subject.medicine.room-download": "Download Room Number Viewing Record",
    "operation.subject.medicine.register": "Registration of Actually Used IP",
    "operation.subject.medicine.setUp": "Setting",
    "operation.subject.medicine.dispensing-approval": "Out-of-Window Visit Dispensation Approval",
    "operation.subject.medicine.approval-log": "Approval Records - View",
    "operation.subject.medicine.approval-sms": "Send SMS",
    "operation.subject.medicine.approval-print": "Print",
    "operation.subject.secede": "Stop (Randomize)",
    "operation.subject.secede-registered": "Stop (Register/Screening successful)",
    "operation.subject.print": "Print",
    "operation.subject.screen": "Screening",
    "operation.subject.finish": "Complete Study",
    "operation.subject.medicine.trail.print": "Print",
    "operation.subject.cohort.status": "Status Modify",
    "operation.subject.cohort.status.no.permission": "No permission, please contact the administrator to assign operation permission.",
    "operation.subject-dtp.view-list": "View",
    "operation.subject-dtp.random": "Randomize Subject",
    "operation.subject-dtp.replace": "Replace Subject",
    "operation.subject-dtp.trail": "Subject Trail",
    "operation.subject-dtp.unblinding-pv": "PV Unblinding",
    "operation.subject-dtp.medicine.view-dispensing": "Dispensation Application Details",
    "operation.subject-dtp.medicine.trail": "Dispensation Trail",
    "operation.subject-dtp.medicine.dispensing": "Apply",
    "operation.subject-dtp.medicine.transport": "Transport",
    "operation.subject-dtp.medicine.reissue": "Re-dispensation Application",
    "operation.subject-dtp.medicine.replace": "Replace IP",
    // "operation.subject-dtp.unblinding": "Unblinding (emergency)",
    "operation.subject-dtp.unblinding": "View",
    "operation.subject-dtp.unblinding-application": "Emergency Unblinding application",
    "operation.subject-dtp.unblinding-approval": "Emergency Unblinding approval",
    "operation.subject-dtp.unblinding-log": "View",
    "operation.subject-dtp.unblinding-sms": "Send SMS",
    "operation.subject-dtp.download": "Download Unblinding Report",
    "operation.subject-dtp.medicine.export": "Download Dispensation Report",
    "operation.subject-dtp.download-random": "Download Randomization Report",
    "operation.subject-dtp.registered": "Register",
    "operation.subject-dtp.update": "Edit Subject",
    "operation.subject-dtp.delete": "Delete Subject",
    "operation.subject-dtp.medicine.room": "View Room Number",
    "operation.subject-dtp.medicine.out-visit-dispensing": "Unscheduled Dispensation Application",
    "operation.subject-dtp.medicine.invalid": "Do Not Attend the Visit",
    "operation.subject-dtp.medicine.export-room": "Download Dispensation Report(including room number)",
    "operation.subject-dtp.medicine.room-download": "Download Room Number Viewing Record",
    "operation.subject-dtp.medicine.register": "Register Actually Dispensed IP",
    "operation.subject-dtp.secede": "Stop (Randomize)",
    "operation.subject-dtp.secede-registered": "Stop (Register/Screening successful)",
    "operation.subject-dtp.print": "Print Subject Trail",
    "operation.subject-dtp.medicine.print": "Print Dispensation Trail",
    "operation.subject-dtp.screen": "Screening failed",
    "operation.subject-dtp.finish": "Complete Study",
    "operation.project.subject.visit.cycle.view": "View",
    "operation.project.subject.visit.cycle.notice.view": "Notification-View",
    "operation.project.subject.visit.cycle.send.notice": "Send Notification",
    "operation.supply.storehouse.medicine.summary": "View",
    "operation.supply.storehouse.medicine.singe": "View",
    "operation.supply.storehouse.medicine.use": "Make Available",
    "operation.supply.storehouse.medicine.freeze": "Quarantine",
    "operation.supply.storehouse.medicine.lost": "Lost/Wasted",
    "operation.supply.storehouse.medicine.history": "View",
    "operation.supply.storehouse.medicine.print": "Print",
    // "operation.supply.storehouse.medicine.download": "Download Data",
    "operation.supply.storehouse.no_number.view": "View",
    "operation.supply.storehouse.no_number.freeze": "Quarantine",
    "operation.supply.storehouse.no_number.lost": "Lost/Wasted",
    "operation.supply.storehouse.no_number.history": "View",
    "operation.supply.storehouse.no_number.print": "Print",
    "operation.supply.site.medicine.summary": "View",
    "operation.supply.site.medicine.summary.formula": "Inventory used time prediction",
    "operation.supply.site.medicine.singe": "View",
    "operation.supply.site.medicine.use": "Make Available",
    "operation.supply.site.medicine.freeze": "Quarantine",
    "operation.supply.site.medicine.lost": "Lost/Wasted",
    "operation.supply.site.medicine.history": "View",
    "operation.supply.site.medicine.print": "Print",
    // "operation.supply.site.medicine.download": "Download Data",
    "operation.supply.site.no_number.view": "View",
    "operation.supply.site.no_number.freeze": "Quarantine",
    "operation.supply.site.no_number.lost": "Lost/Wasted",
    "operation.supply.site.no_number.history": "View",
    "operation.supply.site.no_number.print": "Print",
    "operation.supply.drug.order.list": "View",
    "operation.supply.drug.order.send": "Deliver",
    "operation.supply.drug.order.receive": "Receive",
    "operation.supply.drug.order.end": "Terminate",
    "operation.supply.drug.order.cancel": "Cancel",
    "operation.supply.drug.order.reason": "Reason",
    "operation.supply.drug.order.confirm": "Confirm",
    "operation.supply.drug.order.close": "Close",
    "operation.supply.drug.order.download": "Download",
    "operation.supply.drug.order.history": "View",
    "operation.supply.drug.order.print": "Print",
    "operation.supply.drug.single.sku": "View",
    "operation.supply.drug.single.history": "View",
    "operation.supply.drug.single.print": "Print",
    "operation.supply.drug.single.download": "Download",
    "operation.supply.drug.single.delete": "Lost/Wasted",
    "operation.supply.drug.single.use": "Make Available",
    "operation.supply.drug.no_number.view": "View",
    "operation.supply.shipment.create": "Add",
    "operation.supply.shipment.cancel": "Common-Cancel",
    "operation.supply.shipment.send": "Deliver",
    "operation.supply.shipment.lose": "Lost",
    "operation.supply.shipment.list": "View",
    "operation.supply.shipment.receive": "Receive",
    "operation.supply.shipment.alarm": "Shipping Algorithm",
    "operation.supply.shipment.history": "View",
    "operation.supply.shipment.print": "Print",
    "operation.supply.shipment.confirm": "Common-Confirm",
    "operation.supply.shipment.reason": "Reason",
    "operation.supply.shipment.approval": "Site Shipment Application Approval",
    "operation.supply.shipment.approval.view": "View",
    "operation.supply.shipment.approval.print": "Print",
    "menu.projects.project.supply.shipment.detail": "IP Details",
    "operation.supply.shipment.detail.change": "Replace",
    "operation.supply.shipment.detail.changeRecord": "Replacement Records",
    "operation.supply.shipment.detail.edit": "Edit",
    "menu.projects.project.supply.recovery.detail": "IP Details",
    "operation.supply.recovery.detail.change": "Replace",
    "operation.supply.recovery.detail.changeRecord": "Replacement Records",
    "operation.supply.shipment.close": "Common-Close",
    "operation.supply.shipment.terminated": "Common-Terminate",
    "operation.supply.shipment.logistics.view": "View",
    "operation.supply.shipment.logistics.edit": "Edit",
    "operation.supply.drug_recovery.logistics.view": "View",
    "operation.supply.drug_recovery.logistics.edit": "Edit",
    "operation.supply.shipment.detail.view": "Detail",
    "operation.supply.drug_recovery.detail.view": "Detail",
    "operation.supply.shipment.contacts": "Contacts",
    "operation.supply.shipment.confirm-dtp": "DTP-Confirm",
    "operation.supply.shipment.cancel-dtp": "DTP-Cancel",
    "operation.supply.shipment.close-dtp": "DTP-Close",
    "operation.supply.shipment.terminated-dtp": "DTP-Terminate",

    "operation.supply.recovery.detail.view": "Detail",
    "operation.supply.recovery.list": "View",
    "operation.supply.recovery.add": "Add",
    "operation.supply.recovery.receive": "Receive",
    "operation.supply.recovery.confirm": "Deliver",
    "operation.supply.recovery.cancel": "Cancel",
    "operation.supply.recovery.lose": "Lost",
    "operation.supply.recovery.history": "View",
    "operation.supply.recovery.print": "Print",
    "operation.supply.recovery.determine": "Confirm",
    "operation.supply.recovery.close": "Close",
    "operation.supply.recovery.end": "Terminate",
    // "operation.supply.recovery.download": "Download",
    "operation.supply.recovery.reason": "Reason",
    "operation.supply.freeze.list": "View",
    "operation.supply.freeze.release": "Lifting Quarantine",
    "operation.supply.freeze.delete": "Lost/Wasted",
    "operation.supply.freeze.history": "View",
    "operation.supply.freeze.print": "Print",
    "operation.supply.freeze.approval": "Lifting Quarantine Approval",
    "operation.build.storehouse.add": "Add",
    "operation.build.storehouse.delete": "Delete",
    "operation.build.storehouse.edit": "Edit",
    "operation.build.storehouse.notice": "Expiration Reminder",
    "operation.build.storehouse.view": "View",
    "operation.build.storehouse.alarm": "Inventory Alert",
    "operation.build.site.view": "View",
    "operation.build.site.edit": "Edit",
    "operation.build.site.add": "Add",
    "operation.build.site.dispensing": "Quantity of Initial Shipment",
    "operation.build.site.supply-plan.view": "View",
    "operation.build.site.supply-plan.edit": "Edit",
    "operation.build.attribute.view": "View",
    "operation.build.attribute.edit": "Edit",
    "operation.build.attribute.history": "View Trail",
    "operation.build.code-rule.view": "View",
    "operation.build.code-rule.edit": "Edit",
    "operation.build.simulate-random.view": "View",
    "operation.build.simulate-random.edit": "Edit",
    "operation.build.simulate-random.add": "Add",
    "operation.build.simulate-random.run": "Run",
    "operation.build.simulate-random.site": "Overview",
    "operation.build.simulate-random.factor": "Detail",
    // "operation.build.simulate-random.download": "Download Data",
    "operation.build.randomization.type.view": "View",
    "operation.build.randomization.type.edit": "Edit",
    // "operation.build.randomization.edc.mapping": "Mapping rules of EDC",
    "operation.build.randomization.group.add": "Add",
    "operation.build.randomization.group.delete": "Delete",
    "operation.build.randomization.group.edit": "Edit",
    "operation.build.randomization.group.view": "View",
    "operation.build.randomization.group.inactivating": "Synchronize",
    "operation.build.randomization.factor.add": "Add",
    "operation.build.randomization.factor.view": "View",
    "operation.build.randomization.factor.delete": "Delete",
    "operation.build.randomization.factor.edit": "Edit",
    "operation.build.randomization.factor.set-toplimit": "Set Stratification Factor",
    "operation.build.randomization.list.view-summary": "View",
    "operation.build.randomization.list.upload": "Upload",
    "operation.build.randomization.list.generate": "Generate",
    "operation.build.randomization.list.sync": "Synchronize",
    "operation.build.randomization.list.active": "Enable/Disable Randomization List",
    // "operation.build.randomization.list.export": "Export",
    "operation.build.randomization.list.invalid": "Invalidate",
    "operation.build.randomization.list.edit": "Edit",
    "operation.build.randomization.list.segmentation.view": "View",
    "operation.build.randomization.list.segmentation.clear": "Clear Other Stratification factors",
    "operation.build.randomization.list.segmentation.site": "Assign Block to Site",
    "operation.build.randomization.list.segmentation.region": "Assign Block to Region",
    "operation.build.randomization.list.segmentation.country": "Assign Block to Country",
    "operation.build.randomization.list.segmentation.factor": "Assign Block to Stratification Factor",
    "operation.build.randomization.list.segmentation.activate": "Active",
    "operation.build.randomization.list.segmentation.deactivate": "Inactive",
    "operation.build.randomization.list.history": "View",
    "operation.build.randomization.list.print": "Print",
    "operation.build.randomization.list.attribute": "View",
    "operation.build.randomization.factor-in.view": "View",
    "operation.build.randomization.factor-in.add": "Add",
    "operation.build.randomization.factor-in.delete": "Delete",
    "operation.build.randomization.factor-in.set-people": "Set Number",
    "operation.build.randomization.form.add": "Add",
    "operation.build.randomization.form.delete": "Delete",
    "operation.build.randomization.form.edit": "Edit",
    "operation.build.randomization.form.list": "View",
    "operation.build.randomization.form.preview": "Preview",
    "operation.build.medicine.visit.update": "Modify",
    "operation.build.medicine.visit.drag": "Sort",
    "operation.build.medicine.visit.copy": "Copy Add",
    "operation.build.medicine.visit.push": "Release",
    "operation.build.medicine.visit.push.record": "Release Record",
    "operation.build.medicine.visit.add": "Add",
    "operation.build.medicine.visit.delete": "Delete",
    "operation.build.medicine.visit.edit": "Edit",
    "operation.build.medicine.visit.list": "View",
    "operation.build.medicine.visit.setting.edit": "Edit",
    "operation.build.medicine.visit.setting.list": "View",
    "operation.build.medicine.configuration.add": "Add",
    "operation.build.medicine.configuration.delete": "Delete",
    "operation.build.medicine.configuration.edit": "Edit",
    "operation.build.medicine.configuration.list": "View",
    "operation.build.medicine.configuration.setting.add": "Add",
    "operation.build.medicine.configuration.setting.delete": "Delete",
    "operation.build.medicine.configuration.setting.edit": "Edit",
    "operation.build.medicine.configuration.setting.list": "View",
    "operation.build.medicine.upload.list": "View",
    "operation.build.medicine.upload.upload": "Upload IP",
    // "operation.build.medicine.upload.downdata": "Download Data",
    "operation.build.medicine.packlist.upload": "Upload Packlist",
    "operation.build.medicine.upload.delete": "Mass Delete",
    "operation.build.medicine.upload.uploadHistory": "View",
    "operation.build.medicine.upload.print": "Print",
    "operation.build.medicine.package.setting": "Setting",
    "operation.build.medicine.batch.setting": "Setting",
    "operation.build.medicine.examine": "Approval",
    "operation.build.medicine.update": "Modify",
    "operation.build.medicine.release": "Release",
    "operation.build.medicine.otherm.add": "Add",
    "operation.build.medicine.otherm.delete": "Delete",
    "operation.build.medicine.otherm.edit": "Edit",
    "operation.build.medicine.otherm.list": "View",
    "operation.build.medicine.batch.list": "View",
    "operation.build.medicine.batch.edit": "Edit",
    "operation.build.medicine.batch.update": "Update",
    "operation.build.medicine.barcode.view": "View",
    "operation.build.medicine.barcode.add": "Generate Barcode",
    "operation.build.medicine.barcode.scan": "Scan for Warehousing",
    "operation.build.medicine.barcode.scanPackage": "Package Scanning",
    "operation.build.medicine.barcode.export": "Export",
    "operation.build.medicine.barcode_label.view": "View",
    "operation.build.medicine.barcode_label.add": "Add",
    "operation.build.medicine.barcode_label.preview": "Preview",
    "operation.build.medicine.barcode_label.send": "Send",
    "operation.build.supply-plan.add": "Add",
    "operation.build.supply-plan.delete": "Delete",
    "operation.build.supply-plan.edit": "Edit",
    "operation.build.supply-plan.view": "View",
    "operation.build.supply-plan.history": "Trail",
    "operation.build.supply-plan.medicine.add": "Add",
    "operation.build.supply-plan.medicine.delete": "Delete",
    "operation.build.supply-plan.medicine.edit": "Edit",
    "operation.build.supply-plan.medicine.view": "View",
    "operation.build.supply-plan.medicine.history": "Trail",
    "operation.build.history.view": "View",
    "operation.build.history.print": "Print",
    "operation.build.push.view": "View",
    "operation.build.push.all.send": "Push all",
    "operation.build.push.batch.send": "Batch Send",
    "operation.build.push.history": "Historical Data Push",
    "operation.build.push.send": "Resend",
    "operation.build.push.details": "Details",

    "operation.build.settings.user.view": "View",
    "operation.build.settings.user.add": "Add",
    //"operation.build.settings.user.edit": "操作管理",
    "operation.build.settings.user.role": "Add Roles",
    "operation.build.settings.user.site": "Add Sites",
    "operation.build.settings.user.depot": "Add Depots",
    "operation.build.settings.user.app": "APP Account Enable/Disable",
    "operation.build.settings.user.unbind": "Unbind",
    "operation.build.settings.user.unbind.batch": "Batch Unbinding",
    "operation.build.settings.users.invite-again": "Invite Again",
    "operation.build.settings.user.reauthorization": "Reauthorization",
    // "operation.build.settings.user.download": "下载",
    "operation.build.settings.user.history": "Trail",
    "operation.build.settings.user.print": "Print",
    "operation.build.settings.notice.view": "View",
    "operation.build.settings.notice.edit": "Edit",
    // "operation.build.settings.user.download": "Download Data",
    "operation.build.randomization.info.view": "View",
    // "operation.build.randomization.info.export": "Export",
    "operation.monitor.view": "View",
    "operation.monitor.edit": "Management",

    "operation.report.userRoleStatus.download": "Download",
    "operation.build.settings.user.download": "Download",
    "operation.report.userRoleAssignHistory.download": "Download",
    "operation.build.randomization.info.export": "Download",
    "operation.report.auditTrailExport.download": "Download",
    "operation.report.auditTrailExport.build": "Project Design",
    "operation.report.auditTrailExport.settings": "Project Settings",
    "operation.report.auditTrailExport.release-record": "Quarantine Management",
    "operation.report.auditTrailExport.order": "Shipments",
    "operation.report.auditTrailExport.drug_recovery": "Return Shipments",
    "operation.report.auditTrailExport.subject": "Subject",
    "operation.report.auditTrailExport.dispensing": "Subject Dispensation",
    "operation.report.auditTrailExport.ip": "IP",
    "operation.report.auditTrailExport.userLoginHistory": "Download",
    "operation.report.auditTrailExport.userRoleAssignHistory": "Download",
    "operation.report.siteIPStatisticsExport.download": "Download",
    "operation.report.siteIPStatisticsExport.download.template": "Custom Template",
    "operation.report.randomizationStatisticsExport.download": "Download",
    "operation.report.subjectStatisticsExport.download": "Download",
    "operation.report.forecastingPrediction.download": "Download",
    "operation.report.visitForecast.download": "Download",
    "operation.report.depotIPStatisticsExport.download": "Download",
    "operation.report.depotIPStatisticsExport.download.template": "Custom Template",
    "operation.report.userLoginHistory.download": "Download",
    "operation.projects.main.setting.permission.export": "Download",
    "operation.subject.download-random": "Download",
    "operation.subject.download-random.template": "Custom Template",
    "operation.subject.medicine.export": "Download",
    "operation.subject.medicine.export.template": "Custom Template",
    "operation.subject.download": "Download",
    "operation.subject.download.template": "Custom Template",
    "operation.report.ip.unblinding.download": "IP Unblinding Report Download",
    "operation.report.ip.unblinding.download.template": "IP Unblinding Report Custom Template",
    "operation.report.visitForecast.download.template": "Custom Template",

    // "operation.subject-dtp.download": "Download",
    // "operation.subject-dtp.download.template": "Custom Template",
    // "operation.subject-dtp.medicine.export": "Download",
    // "operation.subject-dtp.medicine.export.template": "Custom Template",
    // "operation.subject-dtp.download-random": "Download",
    // "operation.subject-dtp.download-random.template": "Custom Template",
    "operation.supply.storehouse.medicine.download": "Download",
    "operation.supply.storehouse.medicine.download.template": "Custom Template",
    "operation.build.randomization.list.export": "Download",
    "operation.build.medicine.upload.downdata": "Download",
    "operation.build.medicine.upload.downdata.template": "Custom Template",
    "operation.source.ip.upload.history.downdata": "Download",
    "operation.supply.site.medicine.download": "Download",
    "operation.supply.site.medicine.download.template": "Custom Template",
    "operation.build.simulate-random.download": "Download",
    "operation.supply.recovery.download": "Download",
    "operation.supply.recovery.download.template": "Custom Template",
    "operation.supply.shipment.download": "Download",
    "operation.supply.shipment.download.template": "Custom Template",
    "operation.build.simulate-random.pdf.download": "Download",
    "operation.build.projectNotificationsConfigurationReport.download": "Download",

    //多语言
    "operation.projects.project.multiLanguage.view": "View",
    "operation.projects.project.multiLanguage.add": "Add",
    "operation.projects.project.multiLanguage.edit": "Edit",
    "operation.projects.project.multiLanguage.delete": "Delete",
    "operation.projects.project.multiLanguage.trail": "Trail",
    "operation.projects.project.multiLanguage.details.view": "View",
    "operation.projects.project.multiLanguage.details.edit": "Edit",
    "operation.projects.project.multiLanguage.details.preview": "Preview",
    "operation.projects.project.multiLanguage.details.downloadTemplate": "Download Template",
    "operation.projects.project.multiLanguage.details.batchExport": "Export",

    //---------------------------------权限部分----------------------------------------
    "operation.subject.beReplace.info": "Replacing subject information",
    "operation.subject.replace.confirm": "Confirm replacement",
    "operation.subject.replace.confirm.is":
        "Subject replacement must comply with the protocol requirements. The system will process the replacement based on the submitted information. Please confirm the following details.",
    "projects.research.attribute": "Project Properties",
    "projects.contact.information": "Contact Information",
    "projects.research.site": "Managed by Site",
    "projects.research.dtp": "Direct to Patient (DTP)",
    "projects.research.site.describe": "Managed by Site: IP is dispensed by site and delivered to subject.",
    "projects.research.dtp.describe":
        "Direct to Patient (DTP): IP is dispensed from depot directly to subject, no need to go through site.",
    "projects.status.progress.describe": "In Progress: The project has started and is ongoing.",
    "projects.status.finish.describe": "Completed: All project implementation such as enrolment/dispensation has been completed.",
    "projects.status.close.describe":
        "Closed: All work has been completed. Project closed after reviewed by project management team.",
    "projects.status.pause.describe": "Paused: The project needs to be paused temporarily due to an exception.",
    "projects.status.terminate.describe": "Terminated:The project needs to be terminated due to an exception.",

    "projects.customer": "Customer",
    "projects.attribute": "Project Properties",
    "projects.brief": "Project Introduction",
    "placeholder.select.common": "Please Select ",
    "placeholder.select.common.email.language": "Please Select Email Language",
    "placeholder.select.common.stage": "Please Select Previous Stage",
    "placeholder.select.search": "Please select or enter search",
    "placeholder.input.common": "Please Enter",
    "placeholder.input.order": "Please Enter Shipment Number",
    "placeholder.input.email": "Please Enter Email",
    "placeholder.input.name": "Please Enter Name",
    "placeholder.input.contact": "Please enter, Multiple numbers can be separated by English commas.",
    "input.error.common": "Input error. Please enter again",
    "model.note.title.common": "Caution!",
    "model.note.content.research":
        "Once ‘Direct to Patient(DTP)’ is selected and saved, it cannot be modified. Please operate with caution.",
    "model.note.title.edc": "Confirmed to open ‘Sync EDC’?",
    "model.note.start.success": "Open Succeeded",
    "model.note.content.edc": "Once ‘Sync EDC’ is enabled, modification is not allowed.",
    "model.note.title.medicine.use": "Is it confirmed to make it available?",
    "model.note.title.medicine.lose": "Is it confirmed to make it lost/wasted?",
    "model.note.title.order.dtp.receive": "Is it confirmed to receive?",
    "model.note.content.order.dtp.receive1": "Current shipment is not in delivery.",
    "model.note.content.order.dtp.receive2": "Once the shipment is received, the status cannot be modified. ",
    "button.update": "Modify",
    "message.save.success": "Save Succeeded",
    "tool.tip.timezone":
        "Applicable to: All sites in project in a same timezone. It is used to calculate the time when integrated with external systems and operated by users.",
    "tool.tip.edc":
        "Once enabled, EDC can trigger to synchronize the operation data of this project. It cannot be closed after opening.",
    "tool.tip.elearning": "Once enabled, it can configure the project dimension requirements for users to learn eLearning courses. ",
    "tool.tip.elearning.yes.tp": "Yes: The user must complete the learning course before entering the project work.",
    "tool.tip.elearning.no.tp": "No: You can click to ignore the learning process and enter the project work.",
    "tool.tip.elearning.config": "Configurate the requires for users learning eLearning courses from project dimensions.",
    "tool.tip.elearning.config.yes":
        "Yes: Users must complete the course to enter the system/project.",
    "tool.tip.elearning.config.no": "No: Click to ignore learning process and enter the system/project.",
    "suppy.drug.order.visitNumber": "Visit Number",
    "input.error.only.number.letter": "Input error, Please enter 1-10 digits of number/letter.",
    "project.setting.divider.approval.control": "Site Shipment Application",
    "project.setting.divider.approval.control.tip": "Setting the name of the IP for which needs to request a site order by blinded role, or set the ratio parameter for which the order needs to be requested on a supply ratio.",
    "project.setting.switch.approval.control": "Approval Control",
    "project.setting.switch.approval.control.tip": "Once opened, the IP that are shipment (or by ratio supply) apply must be configured in Treatment Design.",
    // "project.setting.switch.approval.control.tip": "Once opened, the IP that are shipment apply must be configured in Treatment Design.",
    "project.setting.approval.method": "Approval Mode",
    "tool.tip.approval.control":
        "After open, blinded roles in the blinded project need to be allowed to creat orders after approval is succeed.",
    "project.setting.divider.unblind.control": "Unblinding Control",
    "project.setting.switch.unblind.control": "Unblinding Control",
    "project.setting.unblind.error1": "Please select unblinding type",
    "project.setting.unblind.error2": "Please select emergency unblinding control mode",
    "project.setting.unblind.error3": "Please select pv unblinding control mode",
    "project.de.isolation.approval": "Lifting Quarantine Approval",
    "project.de.isolation.approval.tip": "Once enabled, the operation of lifting quarantined IP is allowed to be completed after approved..",
    "project.reason.for.de.isolation": "Lifting Quarantine Reason",
    "project.setting.unblind.method": "Control Mode",
    "project.setting.checkbox.approval": "Approval Confirmation",
    "project.setting.checkbox.unblinded-code": "Unblinding Code",
    "tool.tip.unblind.control":
        "After opened, unblinding operation is allowed after approval or after providing the correct unblinding code.",
    "project.setting.unblind.confirm": "Confirmation Mode",
    "project.setting.checkbox.unblind.sms": "SMS",
    "project.setting.checkbox.unblind.process": "Process Operation",
    "project.setting.msg.unblind.sms":
        "Users with approval permissions need to configure a valid mobile phone number(+86). SMS approval is limited to Chinese mainland carriers.",
    "validator.msg.required": "Required",
    "validator.msg.number": "The number range is an integer between 0-999. Please re-enter.",
    "modal.title.user.role": "Role management",
    "form.label.unblinding.code.available": "Available",
    "form.label.unblinding.code.used": "Dispensed",
    "form.label.unblinding.code.indivual": " ",
    "tag.copy": "Copy",
    "subject.urgentUnblindingApproval.reason.other": "other",
    "subject.urgentUnblindingApproval.reason.sae": "SAE",
    "subject.urgentUnblindingApproval.reason.pregnancy": "Pregnancy",
    "subject.urgentUnblindingApproval.reason.policy": "Policy Requirements",
    "subject.urgentUnblindingApproval.pending": "Submit Applications",
    "subject.urgentUnblindingApproval.agree": "Approved",
    "subject.urgentUnblindingApproval.reject": "Denied",
    "subject.unblinding.reason.remark": "Remark",
    "subject.unblinding.application": "Unblinding Application",
    "subject.unblinding.approval": "Unblinding Approval",
    "subject.unblinding.application.alert":
        "If the actual distribution of ip is inconsistent with the random distribution of the system, please register in time",
    "subject.unblinding.application.type": "Application Method",
    "subject.unblinding.unblinded.code.confirm": "Unblinding code confirmation",
    "subject.unblinding.application.result.title": "Emergency Unblinding Application",
    "subject.unblinding.application.result.title.pv": "Pv Unblinding application",
    "subject.unblinding.application.result.title.ip": "Ip Unblinding application",
    "subject.unblinding.approval.result.title": "Emergency Unblinding Approval",
    "subject.unblinding.application.success": "Application succeeded",
    "subject.unblinding.approval.success": "Approval succeeded",
    "subject.unblinding.application.info":
        "The Emergency unblinding application has been submitted successfully, please wait for the approval of the approver.",
    "subject.unblinding.application.info.pv":
        "The pv unblinding application has been submitted successfully, please wait for the approval of the approver.",
    "subject.unblinding.application.info.ip":
        "The IP unblinding application has been submitted successfully, please wait for the approval of the approver.",
    "subject.unblinding.application.alert.ip": "Upon unblinding of the IP, it will be labeled as 'unblinded' and potentially removed from the trial. Please exercise caution when proceeding.",
    "subject.unblinding.success": "Unblinding Succeeded",
    "subject.unblinding.approval.number": "Approval Number",
    "subject.unblinding.approval.reason": "Reason",
    "subject.unblinding.approval.agree": "Approved",
    "subject.unblinding.approval.reject": "Deny",
    "subject.unblinding.application.resend": "Send SMS",
    "subject.edc.verification.factor": "Stratification Factor",
    "subject.edc.verification.source": "Data Source",
    "subject.edc.verification.return": "EDC Return",
    "subject.edc.verification.enter": "enter in IRT",
    "subject.edc.return.modification": "Back to revise",
    "subject.edc.continue.submitting": "Continue to randomize",
    "subject.edc.continue.dispense": "Continue to dispense",
    "subject.edc.continue.replace": "Continue to replace",
    "subject.edc.continue.register": "Continue Registration",
    "subject.edc.continue.update": "Continue Edit",
    "subject.edc.continue.screen": "Continue Screening",
    "subject.edc.confirm.lamination1": "EDC returns empty stratification/form information, you need to confirm whether the stratification/form information is entered correctly.",
    "subject.edc.confirm.lamination2": "EDC returns empty stratification information, you need to confirm whether the stratification information is entered correctly.",
    "subject.edc.confirm.lamination3": "EDC returns empty form information, you need to confirm whether the form information is entered correctly.",
    "subject.edc.interface.error": "Failed to check the subject status from EDC due to the interface's abnormal response.",
    "subject.edc.interface.site.error": "can not check site from EDC",
    "subject.edc.inconsistent.information": "Stratification information entered in IRT is not consistent with the one returned from EDC, please confirm the entered stratification information is correct or not.",
    "subject.edc.inconsistent.information1": "{label} information entered in IRT is not consistent with the one returned from EDC, please confirm the entered {label} information is correct or not.",
    "subject.edc.random.failure.filter.failed": "system checks the status of the subject is screening failure and does not allow further randomization.",
    "subject.edc.random.failure.screen.failed": "System checks the status of the subject is screening and does not allow further randomization.",
    "subject.edc.random.failure.exit": "Subject status is exited, randomization is not allowed.",
    "subject.edc.random.failure.complete.the.study": "system checks the status of the subject is complete study and does not allow further randomization.",
    "subject.edc.dispensing.failure.filter.failed": "system checks the status of the subject is screening failure and does not allow further dispensing.",
    "subject.edc.dispensing.failure.exit": "Subject status is exited, dispensing is not allowed.",
    "subject.edc.dispensing.failure.complete.the.study": "system checks the status of the subject is complete study and does not allow further dispensing.",
    "subject.edc.create.subject": "EDC did not create a subject and the returned information was empty.",
    "subject.edc.replace.inconsistent.information": "The stratification factor of the replacing subject entered in IRT is inconsistent with that returned from EDC. Please confirm whether the stratification factor is entered correct or not.",
    "subject.edc.replace.register.information.stratification": "stratification",
    "subject.edc.replace.register.information.form": "form",
    "subject.edc.replace.register.information.screen": "screening",
    "subject.edc.replace.register.information.subject.no": "Subject ID",
    "subject.edc.replace.register.information.cohort": "Cohort",
    "subject.edc.replace.register.information.stage": "Stage",
    "subject.edc.replace.register.information.a1": "The IRT enters",
    "subject.edc.replace.register.information.a2": "does not match the information returned by the EDC system, please confirm the",
    "subject.edc.replace.register.information.a3": "information entry is correct or not.",
    "subject.edc.replace.register.information.b": "information that is consistent with the EDC system return.",

    "subject.edc.subject": "EDC returns the subject status as ",
    "subject.edc.subject.exited": "Drop out",
    "subject.edc.subject.screen.no.random": "Screening-Nonrandomize",
    "subject.edc.subject.continue.update": ", please confirm.",
    "subject.edc.push.centre": "This subject data of EDC system  processing, if continue to modify, the EDC system will later complete the data overwrite operation.",
    "subject.edc.register.push.centre": "The EDC system will complete the data write operation later if the modification continues while the EDC system data is being processed.",
    "subject.edc.no.subject": "The subject is not available in the EDC system",
    "subject.edc.no.subject.push.centre": " and will be automatically created in the EDC system later if the modification is continued.",

    "subject.edc.replace.register.information1": "The information entered in IRT for stratification/form does not match the information returned by the EDC system, please confirm the stratification/form information entry is correct or not.",
    "subject.edc.replace.register.information2": "The information entered in IRT for stratification does not match the information returned by the EDC system, please confirm the stratification information entry is correct or not.",
    "subject.edc.replace.register.information3": "The information entered in IRT for form does not match the information returned by the EDC system, please confirm the form information entry is correct or not.",
    "subject.edc.register.no.subject": ", it will automatically create the subject in the EDC system if continuing registration.",
    "subject.edc.update.no.subject": ", it will automatically create the subject in the EDC system if continuing update.",
    "subject.edc.screen.no.subject": ", it will automatically create the subject in the EDC system if continuing screening.",
    "subject.edc.update.handle.subject": "The EDC system will complete the data write operation later if the modification continues while the EDC system data is being processed.",
    "subject.edc.update.cover.subject": "This subject data of EDC system  processing, if continue to modify, the EDC system will later complete the data overwrite operation.",
    "subject.edc.cover.tip": "The operation is successful and the EDC system will automatically complete the data overwrite after the lRT system data is pushed.",
    "subject.edc.no.mapping": "There is no mapping rule configured in the EDC system, IRT will not push data.",
    "subject.edc.complete.registration": "Subject：{subjectNo}，already exists in the EDC system and consistent with the IRT system. Continuing registration will complete subject registration in the IRT system.",
    "subject.edc.complete.cohort.registration": "Subject:{subjectNo}, cohort:{cohortName}, already exists in the EDC system and is not consistent with the IRT system. Continuing registration will complete subject registration in the IRT system.",
    "subject.edc.complete.cohort.update": "Subject:{subjectNo}, cohort:{cohortName}, already exists in the EDC system and is not consistent with the IRT system. Continuing update will complete subject update in the IRT system.",
    "subject.edc.complete.cohort.random": "Subject:{subjectNo}, cohort:{cohortName}, already exists in the EDC system and is not consistent with the IRT system. Continuing randomize will complete subject randomize in the IRT system.",
    "subject.edc.complete.cohort.screen": "Subject:{subjectNo}, cohort:{cohortName}, already exists in the EDC system and is not consistent with the IRT system. Continuing screening will complete subject screening in the IRT system.",
    "subject.edc.replace.error": "Replace failed",
    "subject.edc.replace.failure.filter.failed": "System checks the status of the subject is screening failure and does not allow further replace.",
    "subject.edc.replace.failure.exit": "System checks the status of the subject is exited and does not allow further replace.",
    "subject.edc.replace.failure.not-registered": "System checks the status of the subject is not registered and does not allow further replace.",
    "subject.edc.replace.failure.complete.the.study": "System checks the status of the subject is complete study and does not allow further replace.",
    "subject.edc.replace.failure.screen.no.random": "System checks the status of the subject is screening and does not allow further replace.",
    "subject.edc.site.empty": "The site information retrieval of EDC has failed, please manually determine if the current subject is consistent between IRT and EDC sites.",
    "subject.edc.site.inconsistent": "The site information returned by EDC does not match the site where the subject is located. Please confirm if the site information in the IRT is correct.",
    "tip.site.disable.title": "Is it confirmed to invalid? ",
    "tip.site.disable": "After invalid, site binding users will automatic inactive.",
    "tip.user.disable": "Is it confirmed to delete? After delete,site/depot and other data will automatic unbind",
    "tip.user.unbind": "After unbinding, users can no longer enter the project.",
    "tip.user.close": "After closing, users will not be able to access all items under the customer",
    "tip.sync.title": "Is it confirmed to synchronize?",
    "tip.copy.title": "Are you sure you want to copy?",
    "tip.copy.title2": "Are you sure to copy?",
    "tip.sync.content": "After synchronization, the randomization list will be automatically adapted to the latest stratification factors.",
    "toast.copy.success": "Copy succeeded",

    "env.user.edit.section1": "It is confirmed to assign unblinded role?",
    "env.user.edit.section2": "Current project is blinded.",
    "env.user.edit.section3": "Assign non-blind roles?",
    "env.user.edit.section4": "non-blind roles(",
    "env.user.edit.section5": ").",

    "order.logistics.sf": "SF Express",
    "order.logistics.ems": "EMS",
    "order.logistics.jd": "JD",
    "order.logistics.yt": "YTO Express",
    "order.logistics.yd": "UDA Express",
    "order.logistics.zt": "ZTO",
    "order.logistics.st": "STO Express",
    "order.logistics.jt": "J&T Express",
    "order.logistics.other": "Others",

    "logistics.confirm.deliver": "Confirmed To Deliver",
    "logistics.confirm.info": "Logistics Vendor",
    "logistics.info.isTransit": "Is it confirmed to deliver?",
    "logistics.info.isTransit.ok": "Once confirmed, the order status will be updated to Shipped.",
    "logistics.info.name": "Logistics",
    "logistics.other.vendor": "Other Vendor",
    "logistics.number": "Tracking Number",
    "logistics.info": "Logistics",
    "logistics.dispensing.dtpIp": "DTP IP",
    "logistics.dispensing.method": "Dispensation Method",
    "logistics.send.site": "Site(Site Inventory)",
    "logistics.send.site.info": "IP dispensed from site, and subjects get it from site.",
    "logistics.send.site.subject": "Site(Direct-to-Patient Shipment)",
    "logistics.send.site.subject.info": "Site will express IP to subjects directly.",
    "logistics.send.depot.subject": "Depot(Direct-to-Patient Shipment)",
    "logistics.send.depot.subject.info": "Depot will express IP to subjects directly.",

    "project.statistics.mode": "Data Push Mode",
    "project.statistics.select.mode": "Please select the data push mode",
    "project.statistics.real": "Real-time request by EDC",
    "project.statistics.active": "Active push from IRT",
    "project.statistics.mode.real.tp": "Real-time request by EDC: The randomization/dispensation request is initiated by EDC. Data will be transmitted after IRT complete the real-time request.",
    "project.statistics.mode.active.tp": "Active push from IRT: Both EDC and IRT can register subjects. After the IRT completes the randomization/dispensation operation, the IRT pushes the data to the EDC in real time, and the IRT can trace the push process log.",
    "project.statistics.url": "Please enter URL of EDC",
    "project.statistics.projectNo": "Please enter projects number",
    "project.statistics.folder.oid": "Folder OID",
    "project.statistics.form.oid": "Form OID",
    "project.statistics.field.oid": "Field OID",
    "project.statistics.dispensing.mapping": "Dispensation data mapping rules of EDC",
    "project.statistics.randomize.mapping": "EDC randomization data mapping",
    "project.statistics.randomize.number.oid": "Randomization ID OID",
    "project.statistics.randomize.group.oid": "Randomization group OID",
    "project.statistics.randomize.time.oid": "Randomization time OID",
    "project.statistics.dispensing.number.oid": "Dispensation OID",
    "project.statistics.dispensing.time.oid": "Dispensation time OID",
    "project.statistics.mapping": "Mapping rules of EDC",
    "project.statistics.all": "Push all",
    "project.statistics.pushing": "Pushing",
    "project.statistics.succeeded": "Succeeded",
    "project.statistics.failed": "Push failed",
    "project.statistics.lose": "Invalid",
    "project.received.failed": "Warehoused failure",
    "project.edc.failed": "Failed",
    "project.edc.processing": "Processing",
    "project.edc.return": "Return from EDC",
    "project.statistics.enter.number": "Please enter {label}",
    "project.statistics.batch.send": "Batch Send",
    "project.statistics.push.history": "Historical Data Push",
    "project.statistics.push.history.empty": "Push failed: No historical data detected.",
    "project.statistics.push.history.confirm": "Is it confirmed to proceed with the historical data push?",
    "project.statistics.push.history.scenario": "Historical data scenarios include: ",
    "project.statistics.select.data": "Please select the data to be pushed first",
    "project.statistics.subject.number": "Subject ID",
    "project.statistics.push.item": "Push Item",
    "project.statistics.randomize": "Randomize",
    "project.statistics.dispense": "Dispense",
    "project.statistics.push.mode": "Push Mode",
    "project.statistics.manual.push": "Manual Push",
    "project.statistics.system.auto": "System Auto",
    "project.statistics.push.time": "Push Time",
    "project.statistics.retry.times": "Retry times",
    "project.statistics.ls.resend": "Is it confirmed to resend the data ?",
    "project.statistics.operation.succeed": "After confirming, the system will push the data to the EDC within 30 seconds.",
    "project.statistics.resend": "Resend",
    "project.statistics.details": "Details",
    "project.statistics.randomization.number": "Randomization number",
    "project.statistics.group": "Group",
    "project.statistics.randomization.time": "Randomization Time",
    "project.statistics.return.edc": "Return from EDC",
    "project.statistics.visit.number": "Visit Number",
    "project.statistics.dispensing.time": "Dispensation Time",
    "project.statistics.registered": "Register",
    "project.statistics.update": "Modify",
    "project.statistics.out-visit-dispensing": "Unscheduled Dispensation",
    "project.statistics.replace": "Replace IP",
    "project.statistics.reissue": "Re-Dispense",
    "project.statistics.cancel": "Withdraw IP",
    "project.statistics.retrieval": "Retrieve IP",
    "project.statistics.realDispensing": "Actually Dispensed IP",
    "project.statistics.unknown": "Unknown",
    "project.statistics.screen": "Screening",

    "supply.plan.applicable.site": "Applicable Site",
    "supply.plan.all.site": "All Site",
    "supply.plan.status.invalid.title": "Is it confirmed to invalidate this supply plan?",
    "supply.plan.status.invalid.content":
        "The site has been assigned supply plan.  Once invalidated, the site will lose the association with the supply plan.",
    "supply.plan.status.applicable.site.title": "Is it confirmed to revise?",
    "supply.plan.status.applicable.site.content": "The system detected some sites have no binding supply plans.",
    "supply.plan.current": "Current Supply Plan",
    "storehouse.alert": "Alert Value",
    "permission.scope.study": "Study:Data permission roles of the study/project dimension",
    "permission.scope.depot": "Depot:Data permission roles of the depot dimension",
    "permission.scope.site": "Site: Data permission roles at site level",
    "project.role.invalid.title": "Is it confirmed to invalidate role?",
    "project.role.invalid.content": "After invalidated, the role assignment will be cancelled automatically.",
    "project.user.status.Valid": "Valid: Project authorization and user account activation succeeded",
    "project.user.status.Inactivated": "Enabled: The project is successfully authorized, but the user account is not activated;",
    "project.user.status.Invalid": "Invalid: The user account has unbound the project.",
    "customer.user.status.Enabled": "Enabled: The system has added the user account and sent the account activation email successfully;",
    "customer.user.status.Activated": "Activated: The user account has been activated successfully;",
    "customer.user.status.Closed": "Closed: The user account has been closed. After closing, the user cannot access all projects under the customer.",
    "random.factor.alert": "The stratification factor has been updated, please synchronize the randomization list in time.",
    "subject.list.alert": "Registration is not allowed in the draft status, and the cohort status needs to be modified to \"Enrolling\".",
    "subject.list.alert.modify": "Modify now",
    "cohort.status": "Cohort Status",

    "report": "Report",
    "report.role": "Object Role",
    "report.projectEnv": "Project/Environment",
    "report.latestDownloadTime": "Latest Download Time",
    "report.customTemplate": "Custom Template",
    "report.history": "History",
    "report.template": "Template",
    "report.template.name": "Template Name",
    "report.template.name.required": "Please enter a template name",
    "report.template.default": "Refer to Default Template",
    "report.template.name.default": "Default Template",
    "report.attributes": "Field Library",
    "report.attributes.unit": "Items",
    "report.custom": "Customize",
    "report.filename": "Filename",
    "report.filesize": "Filesize",
    "report.exportTime": "Export Time",
    "report.filename.search.required": "Please enter the filename",
    "report.storehouse": "Depot",

    "report.randomizationStatisticsExport": "Randomization Statistics Report",
    "report.subjectStatisticsExport": "Subject Statistics Report",
    "report.siteIPStatisticsExport": "Site IP Statistics Report",
    "report.depotIPStatisticsExport": "Depot IP Statistics Report",
    "report.userLoginHistory": "User Login History",
    "report.userRoleAssignHistory": "User Role Assign History",
    "report.userRoleStatus": "User Role Status Report",
    "report.auditTrailExport": "Audit Trail",
    "report.RandomizationSimulationPDFExport": "Randomization Simulation Report",
    "report.forecastingPrediction": "Forecasting Prediction Report",
    "report.visitForecast": "Visit Statistics Report",
    "report.forecast.depot": "Depot Name",
    "report.forecast.period": "Scheduled Dispensation Window",

    "report.projectPermissionConfigurationExport": "Project Permission Configuration Report",
    "report.ProjectNotificationsConfigurationReport": "Project Notifications Configuration Report",
    "report.configureReport": "Configuration Report",
    "report.sourceIPExport": "Source IP List",
    "report.sourceRandomizationListExport": "Source Randomization List Report",
    "report.randomizationSimulationReport": "Randomization Simulation Report",
    "report.randomizeReport": "Subject Detail Report",
    "report.dispenseReport": "Dispensation Report",
    "report.unblindingReport": "Unblinding Report",
    "report.roomNumberViewHistory": "Room Number View History",
    "report.depotItemReport": "Depot Item Report",
    "report.siteItemReport": "Site Item Report",
    "report.sourceIPUploadHistoryExport": "Source IP Upload History",
    "report.shipmentOrdersReport": "Shipments Report",
    "report.returnOrdersReport": "Return Shipments Report",
    "report.randomizationSimulationResult": "Randomization Simulation Result",
    "report.site.warning": "No Site Selected",
    "report.role.warning": "No Role Selected",
    "report.subject.warning": "No Subject Selected",
    "report.random.list.warning": "No Randomization List Selected",
    "report.stage.warning": "Please select Re-randomization",
    "report.cohort.warning": "Please select Cohort",
    "report.audit.trail.type.warning": "No Audit Trail Type Selected",
    "report.audit.trail.type.secret.warning": "No password is filled",
    "report.template.all": "All Templates",
    "report.template.delete.confirm": "Is it confirmed to delete?",
    "report.template.create.success": "Creat Succeeded",
    "report.template.edit.success": "Edit Successfully",

    "report.attributes.project": "Project",
    "report.attributes.project.number": "Project Number",
    "report.attributes.project.name": "Project Name",
    "report.attributes.info": "Basic Information",
    "report.attributes.info.user.name": "Name",
    "report.attributes.info.user.email": "Email",
    "report.attributes.info.user.role": "Role",
    "report.attributes.info.country": "Country (Stratification Property)",
    "report.attributes.info.region": "Region (Stratification Property)",
    "report.attributes.info.site.country": "Country",
    "report.attributes.info.site.region": "Region",
    "report.attributes.info.site.number": "Site Number",
    "report.attributes.info.site.name": "Site Name",
    "report.attributes.info.subject.number": "Subject ID",
    "report.attributes.info.status": "Status",
    "report.attributes.info.storehouse.name": "Depots",
    "report.attributes.random": "Subject Randomization",
    "report.attributes.random.factor": "Stratification Factor",
    "report.attributes.random.factor.calc": "Enter Stratification Field (Page)",
    "report.attributes.random.actual.factor": "Actual stratification",
    "report.attributes.random.g": "Group Value",
    "report.attributes.random.time": "Randomization/Enrolling Time",
    "report.attributes.random.group": "Group(randomized by system)",
    "report.attributes.random.sub.group": "Sub Group(randomized by system)",
    "report.attributes.random.subject.number.replace": "Replace subject ID",
    "report.attributes.random.number": "Randomization ID",
    "report.attributes.random.register.time": "Register Time",
    "report.attributes.random.register.operator": "Registered Operator",
    "report.attributes.random.operator": "Randomization Operator",
    "report.attributes.random.form": "Form configuration",
    "report.attributes.random.subject.replace.status": "Replace Subject Status",
    "report.attributes.random.cohort": "Cohort Name",
    "report.attributes.random.stage": "Stage",
    "report.attributes.random.subject.replace.time": "Replace Time",
    "report.attributes.random.subject.replace.number": "Replacing randomization ID",
    "report.attributes.random.config.code": "Group Code",
    "report.attributes.random.sign.out.operator": "Stop Operator",
    "report.attributes.random.sign.out.time": "Stop Operate Time",
    "report.attributes.random.sign.out.real.time": "Actual Stopped Date",
    "report.attributes.random.sign.out.reason": "Stop Reason",
    "report.attributes.random.screen.time": "Screening Date",
    "report.attributes.random.icf.time": "ICF Signed Date",
    "report.attributes.random.finish.remark": "Complete study-remark",
    "report.attributes.random.plan.time": "Scheduled Randomization/Enrollment Time",
    "report.attributes.random.sequence.number": "Random Sequence Number",
    "report.attributes.dispensing": "Subject dispensation",
    "report.attributes.dispensing.room": "Room Number",
    "report.attributes.dispensing.cycle.name": "Visit Name",
    "report.attributes.dispensing.type": "Dispensation Type",
    "report.attributes.dispensing.planTime": "Scheduled Visit",
    "report.attributes.dispensing.dose": "Dispensation Level",
    "report.attributes.dispensing.outsize": "Is it overdue",
    "report.attributes.dispensing.doseFormulas": "Dose Adjustment Form",
    "report.attributes.dispensing.time": "Operation Time",
    "report.attributes.dispensing.medicine": "IP Number",
    "report.attributes.dispensing.drug.name": "IP Name",
    "report.attributes.dispensing.label": "Dispensation IP label",
    "report.attributes.dispensing.medicine.replace": "Replaced IP number",
    "report.attributes.dispensing.medicine.real": "IP dispensed by the system",
    "report.attributes.dispensing.medicine.real.group": "Actually used IP group",
    "report.attributes.dispensing.drug.other.number": "Quantity of Unnumbered IP",
    "report.attributes.dispensing.useFormulas": "Formula Calculation Form",
    "report.attributes.dispensing.operator": "Dispensation operator",
    "report.attributes.dispensing.remark": "Dispensation Remark",
    "report.attributes.dispensing.out-visit-dispensing.reason": "Unscheduled Dispensation Reason",
    "report.attributes.dispensing.reissue.reason": "Re-dispensation reason",
    "report.attributes.dispensing.reissue.remark": "Re-Dispense-remark",
    "report.attributes.dispensing.out-visit-dispensing.remark": "Unscheduled dispense-remark",
    "report.attributes.dispensing.replace.remark": "Replace IP-remark",
    "report.attributes.dispensing.retrieval.remark": "Retrieve-remark",
    "report.attributes.dispensing.register.remark": "Actually Used IP Number-remark",
    "report.attributes.dispensing.invalid.remark": "Do Not Attend the Visit-remark",
    "report.attributes.dispensing.send.type": "Dispensation Method",
    "report.attributes.dispensing.logistics.info": "Logistics",
    "report.attributes.dispensing.logistics.remark": "Logistics-Remark",
    "report.attributes.dispensing.medicine.real.number": "Actually Used IP Number",
    "report.attributes.unblinding": "Unblinding",
    "report.attributes.unblinding.sponsor": "Whether the Sponsor has been notified or not",
    "report.attributes.unblinding.mark": "Unblinding Remark",
    "report.attributes.unblinding.reason": "Unblinding Reason",
    "report.attributes.unblinding.reason.mark": "Unblinding Reason Remark",
    "report.attributes.unblinding.operator": "Unblinding Operator",
    "report.attributes.unblinding.time": "Unblinding Operation Time",
    "report.attributes.research": "IP",
    "report.attributes.research.medicine.serial-number": "Sequence Number",
    "report.attributes.research.medicine.number": "IP Number",
    "report.attributes.research.medicine.code": "Short Code",
    "report.attributes.research.medicine.type": "Operation Type",
    "report.attributes.research.medicine.name": "IP Name",
    "report.attributes.research.batch": "Batch Number",
    "report.attributes.research.spec": "Specification",
    "report.attributes.research.expireDate": "Expiration date",
    "report.attributes.research.packageNumber": "Package Number",
    "report.attributes.research.package.serialNumber": "The package sequence number",
    "report.attributes.research.packageMethod": "Deliver Mode",
    "report.attributes.research.place": "Location",
    "report.attributes.research.order.number": "Shipment Number",
    "report.attributes.research.status": "Status",
    "report.attributes.research.reason": "Reason",
    "report.attributes.research.operator": "Operator",
    "report.attributes.research.time": "Time",
    "report.attributes.research.freeze.reason": "Quarantine Reason",
    "report.attributes.research.freeze.operator": "Quarantine Operator",
    "report.attributes.research.freeze.time": "Quarantine Time",
    "report.attributes.research.release.reason": "Lifting Quarantine Reason",
    "report.attributes.research.release.operator": "Lifting Quarantine Operator",
    "report.attributes.research.lost.reason": "Lost/Wasted Reason",
    "report.attributes.research.lost.operator": "Lost/Wasted Operator",
    "report.attributes.research.lost.time": "Lost/Wasted Operation Time",
    "report.attributes.research.use.reason": "Make Available Reason",
    "report.attributes.research.use.operator": "Make Available Operator",
    "report.attributes.research.use.time": "Make Available Time",
    "report.attributes.research.other": "Number",
    "report.attributes.order": "Shipments",
    "report.attributes.order.detail": "Shipments Details",
    "report.attributes.order.number": "Shipment Number",
    "report.attributes.order.status": "Status",
    "report.attributes.order.send": "Origin",
    "report.attributes.order.receive": "Destination",
    "report.attributes.order.medicineQuantity": "IP Quantity",
    "report.attributes.order.create.by": "Created By",
    "report.attributes.order.create.time": "Created Time",
    "report.attributes.order.cancel.by": "Canceller",
    "report.attributes.order.cancel.time": "Cancellation Time",
    "report.attributes.order.cancel.reason": "Reason for cancellation",
    "report.attributes.order.confirm.by": "Confirmed By",
    "report.attributes.order.confirm.time": "Confirm Time",
    "report.attributes.order.close.by": "Closed By",
    "report.attributes.order.close.time": "Closed Time",
    "report.attributes.order.close.reason": "Reason for Closure",
    "report.attributes.order.send.by": "Delivered By",
    "report.attributes.order.send.time": "Delivery time",
    "report.attributes.order.receive.by": "Receiver",
    "report.attributes.order.receive.time": "Received Time",
    "report.attributes.order.lost.by": "Lost By",
    "report.attributes.order.lost.time": "Lost/Wasted Operation Time",
    "report.attributes.order.lost.reason": "Lost Reason",
    "report.attributes.order.end.by": "Terminated By",
    "report.attributes.order.end.time": "Terminated Time",
    "report.attributes.order.end.reason": "Reason for Termination",
    "report.attributes.order.supplier": "Logistics Vendor",
    "report.attributes.order.supplier.other": "Other Vendor",
    "report.attributes.order.supplier.number": "Tracking Number",
    "report.attributes.order.expectedArrivalTime": "Expected Arrival Time",
    "report.attributes.order.actualReceiptTime": "Actual Receipt Time",

    "report.user.role.assign.name": "Name",
    "report.user.role.assign.email": "Email",
    "report.user.role.assign.operType": "Operation",
    "report.user.role.assign.content": "Operation Content",
    "report.user.role.assign.oper": "Operator",
    "report.user.role.assign.operTime": "Operation Time",

    "report.ip.statistics.status.available": "Available",
    "report.ip.statistics.status.toBeConfirmed": "To be Confirmed",
    "report.ip.statistics.status.delivered": "Confirmed",
    "report.ip.statistics.status.sending": "In Delivery",
    "report.ip.statistics.status.quarantine": "Quarantined",
    "report.ip.statistics.status.used": "Dispensed",
    "report.ip.statistics.status.lose": "Lost/Wasted",
    "report.ip.statistics.status.expired": "Expired",
    "report.ip.statistics.status.frozen": "Frozen",
    "report.ip.statistics.status.locked": "Locked",
    "report.user.login.ip": "IP",
    "report.user.login.time": "Operation time",
    "report.user.login.success": "Whether the login is successful",
    "report.simulate.random.name": "Name",
    "report.simulate.random.block": "Block",
    "report.simulate.random.group": "Group",
    "report.download.success": "Download succeeded, could download again in History",
    "report.random.statistics.month": "Monthly/Cumulative",
    "report.random.statistics.week": "Weekly/Cumulative",
    "report.subject.unblinding.urgent": "Emergency Unblinded",
    "report.subject.unblinding.pv": "PV Unblinded",
    "simulated.random.list.runCount": "Number of runs",
    "project_dynamics": "Notification",
    "project.number.name": "Please enter number or name",
    "project.focus.on": "Only followed items",
    "project.focus.on.cancel": "Unfollow",
    "project.focus.on.cancel.success": "Unfollow Succeeded",
    "project.focus.on.ok": "Follow",
    "project.focus.on.ok.success": "Follow Succeeded",
    "no.permission": "The current account/role has no permission to jump",
    "random.statistics.total": "Total",
    "random.statistics.country": "By Country",
    "random.statistics.region": "By Region",
    "random.statistics.site": "By Site",
    "random.statistics.factor": "By Stratification Factor",
    "random.statistics.month": "Monthly",
    "random.statistics.week": "Weekly",
    "random.statistics.current-month": "Current Month",
    "random.statistics.current-week": "Current Week",
    "random.statistics.cumulative": "Cumulative",
    "random.statistics.plan": "Plan",
    "random.statistics.actual": "Actual",
    "random.statistics.actual.cumulative": "实际累计入组",
    "random.statistics.plan.cumulative": "计划累计入组",
    "medicine.un_provide_date.days": "提前通知天数",


    "project.overview.carried.out": "Project In Progress",
    "project.overview.still.far.from.the.end": "There is still () until the end",
    "project.overview.year": "Year",
    "project.overview.month": "Month",
    "project.overview.day": "Day",
    "project.overview.week": "Week",
    "project.overview.hour": "Hour",

    "project.overview.time": "Project Period",
    "project.overview.site.number": "Site Quantity",
    "project.overview.random.number": "Randomization Number",
    "menu.message.center": "Message Center",
    "menu.message.center.single": "Message",
    "project.report.locus.select.time": "Time",
    "project.report.locus.checkbox.password": "Extracting Password",
    "project.report.locus.password": "Password",
    "system.message.center.see.more": "View More",


    "project.setting.cohort.copy.contain": "Include Project Configuration",
    "operation.settings.users.system.administrator": "System Administrator",
    "common.export.success": "Download Succeeded",


    "report.export.randomization.simulation.select.name": "Please select a name",

    "report.export.forecast.date": "Range of Predictions",

    "report.visit.forecast.visit.status": "Visit Status",
    "report.visit.forecast.notice.content": "Notification content",
    "report.visit.forecast.notice.time": "Push Time",
    "report.visit.forecast.notice.user": "Pushed by",
    "report.visit.forecast.notice.type": "Push Mode",
    "report.visit.forecast.notice.email": "Notification targets",

    "project.external.edc.rave.mapping.visitCode": "IRT Visit Number",
    "project.external.edc.rave.mapping.edcOid": "EDC OID",
    "project.external.edc.rave.mapping.folderOid": "Folder OID",
    "project.external.edc.rave.mapping.formOid": "Form OID",
    "project.external.edc.rave.mapping.ipNumberOid": "IP Number OID",
    "project.external.edc.rave.mapping.dispenseTimeOid": "Dispensation Time OID",


    "project.external.edc.rave.mapping.randomizationField": "Randomization Field",
    "project.external.edc.rave.mapping.randomizationCode": "Randomization Number",
    "project.external.edc.rave.mapping.randomizationTime": "Randomization Time",
    "project.external.edc.rave.mapping.group": "group",
    "project.external.edc.rave.mapping.factor": "factor",
    "project.external.edc.rave.mapping.cohort": "Cohort/Re-randomization",
    "project.external.edc.rave.mapping.fieldOid": "Field OID",


    "report.export.history.prompt": "Save the downloaded reports within the past 30 days.",
    "placeholder.select.common.project.type": "Please select project type",


    "export.notifications.configuration.report.type": "Email Type",
    "export.notifications.configuration.report.role": "Role",
    "export.notifications.configuration.report.content.configuration": "Content Configuration",
    "export.notifications.configuration.report.scene": "Scene",

    "drug.batch.treatmentDesign.openSetting": "Please select Dispensation Method",
    "drug.batch.treatmentDesign.formula": "Please select Formula",

    "visit.cycle.version.number.after.publishing": "After published, some visit configurations will no longer be allowed to be revised.",

    "drug.batch.treatmentDesign.formula.weight.last.calculation": "The calculated weight at the last visit",
    "drug.batch.treatmentDesign.formula.weight.last.actual": "The actual weight at the last visit",
    "drug.batch.treatmentDesign.formula.weight.random": "The weight at the random visit",

    "drug.batch.treatmentDesign.treatment.design.open.ip": "Open IP",
    "drug.batch.treatmentDesign.treatment.design.automatic.recode": "Automatic Assignment",
    "drug.batch.treatmentDesign.treatment.design.keep.decimal.places": "Keep Decimal Places",
    "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation": "Weight Comparison Calculation",
    "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.compared.with": "Compared with",
    "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.the.change.is": "The change is",
    "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.is.used.for.this.calculation": "Is used for this calculation",



    "drug.batch.treatmentDesign.treatment.design.custom.formula": "Custom formula",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.title": "Variable factors only support height and weight, and the input formula only supports the following symbols:",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.symbol": "Symbol",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation": "Explanation",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.eg": "E.g",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.footer": "Examples of mixed operations: 0.0061*{height}+0.0124*{weight}-0.0090",

    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.plus": "plus",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.sub": "sub",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.multiply": "multiply",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.division": "division",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.remainder": "remainder",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.integer.power": "integer power",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.brackets": "brackets",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.square.root": "square root",
    "drug.batch.treatmentDesign.treatment.design.custom.formula.explanation.cube.root": "cube root",

    "drug.batch.treatmentDesign.treatment.design.custom.formula.completion.instructions": "Completion Instructions",
    "project.bound.repeat": "Duplicate project binding, please confirm again.",



    "drug.batch.treatmentDesign.treatment.design.weight.title1": "Input symbols only support <, ≤, =,",
    "drug.batch.treatmentDesign.treatment.design.weight.title2": "and the value to be calculated is replaced with X.",


    "visit.management.allowed.to.randomize": "Random visits calculated based on Baseline do not need to configure the interval length and window period.",
    "visit.management.allowed.to.randomize2": "to configure the interval length and window period.",

    "visit.management.allowed.to.showDoseAdjustment": "Once enabled, it allows control over dose levels or visit judgment rules on the visit dimension.",

    "report.attributes.dispensing.medicine.is.replace": "Is Replaced IP number",
    "report.attributes.dispensing.medicine.is.real": "Is Real IP number",


    "only.one.row.can.be.edited.simultaneously": "Only one row can be edited simultaneously",

    "source.ip.upload.history.name": "Name",
    "source.ip.upload.history.rows": "Rows",


    "subject.visit.outsize": "Overdue",
    "subject.visit.outsize.undone": "undone:",
    "subject.visit.outsize.completed": "completed:",
    "subject.visit.in.progress": "In Progress",
    "subject.visit.completed.on.schedule": "Completed on schedule",
    "subject.visit.has.not.started": "Not Started",

    "subject.visit.cycle.item": "Visit Item",
    "subject.visit.cycle.item.site": "site:",
    "subject.visit.cycle.item.plan": "plan:",
    "subject.visit.cycle.item.actual": "actual:",

    "subject.visit.item.outsize": "Overdue",
    "subject.visit.item.undone": "undone",

    "subject.visit.item.in.progress": "In Progress",
    "subject.visit.item.completed": "Completed",
    "subject.visit.item.has.not.started": "Not Started",
    "subject.visit.item.on.schedule": "On Schedule",

    "calendar.button.today": "Today",
    "calendar.button.site.visit.matter.notice": "Notification",
    "calendar.button.site.visit.matter.view": "View",

    "calendar.button.site.visit.matter.app.notice": "APP Notification",
    "calendar.button.site.visit.matter.text.message": "SMS",
    "calendar.button.site.visit.matter.recipient": "Receiver",


    "calendar.button.site.visit.matter.notice.send": "Send Notification",
    "calendar.button.site.visit.matter.notice.history": "History",
    "calendar.button.site.visit.matter.notice.history.content": "Notification content",
    "calendar.button.site.visit.matter.notice.history.time": "Push Time",
    "calendar.button.site.visit.matter.notice.history.people": "Pushed by",
    "calendar.button.site.visit.matter.notice.history.way": "Push Mode",
    "calendar.button.site.visit.matter.notice.history.object": "Notification targets",

    "projects.notice.rules": "Notification Rules",
    "projects.notice.object": "Notification targets",

    "project.visit.cycle.calendar.summary.outsize.completed": "Out of window completed: Completed visits is not within the window period;",
    "project.visit.cycle.calendar.summary.outsize.undone": "Overdue incompleted: Visits that have exceeded the window period and are not completed;",
    "project.visit.cycle.calendar.summary.in.progress": "In progress: Uncompleted visits within the window period;",
    "project.visit.cycle.calendar.summary.completed.on.schedule": "Completed on time: Visits completed within the window period;",
    "project.visit.cycle.calendar.summary.has.not.started": "Not started: Future visits where the current date is before the minimum value of the window period.",



    "projects.notice.rules.template.a": "Template a",
    "projects.notice.rules.template.a.bracket": "{ }",
    "projects.notice.rules.template.a.content": "days later you have a visit task, please remember to arrange it in advance!",
    "projects.notice.rules.template.b": "Template b",
    "projects.notice.rules.template.b.content": "You have a visit task tomorrow, please remember to arrange it!",
    "projects.notice.rules.template.c": "Template c",
    "projects.notice.rules.template.c.content": "You have a visit task today, please remember to go for a check-up!",
    "projects.notice.rules.template.d": "Template d",
    "projects.notice.rules.template.d.content": "Did you forget to arrange the visit today? Come in and take a look!",

    "projects.notice.rules.template.title": "Rules",
    "projects.notice.rules.template.minimum": ": Minimum visit window period",
    "projects.notice.rules.template.day": "Day, ",
    "projects.notice.rules.template.select": "Push, Select",

    "projects.notice.visit.notification": "Visit Notification",
    "projects.notice.visit.notification.required.prefix": "Please enter other receiver cellphone number",
    "projects.notice.visit.notification.required.prefix.recipient": "Please select receiver ",
    "projects.notice.visit.notification.required.prefix.rules": "Please complete the rules",

    "configuration.export.treatment.design.shipment.other.drug": "* Is Unnumbered IP",


    "form.control.type.options.duplicate": "Duplicated Option",
    "form.control.type.options.tip.one": "Dose Level: Option values only support the enumerated applications provided by the system; ",
    "form.control.type.options.tip.two": "Visit Judgment: Supports custom or system-provided enumerated applications.",
    "form.control.type.options.one": "Initial Dose",
    "form.control.type.options.two": "Maintain the dose from the last time",
    "form.control.type.options.three": "Dose reduction, choose to decrease the subject's current dose by one dose level",

    "form.control.type.options.selectedRepeatedly": "Options cannot be selected repeatedly",

    "drug.configure.setting.dose.form": "Dose Form",
    "drug.configure.setting.dose.form.doseAdjustment": "Dose Adjustment",
    "drug.configure.setting.dose.form.type": "Dose Selection",
    "drug.configure.setting.dose.level": "Dose Level",
    "drug.configure.setting.dose.visit.judgment": "Visit Judgment",
    "drug.configure.setting.dose.level.set": "Dose Level Set",
    "drug.configure.setting.dose.level.set.tip1": "The sets of dose levels are ordered as follows: the lowest dose is entered first and the highest dose is entered last.The lowest dose should appear at the top of the table and the highest dose should appear at the bottom.Dispensation dose only supports IP in fixed quantities.",
    "drug.configure.setting.dose.visit.judgment.desc": "Allows setting the subsequent dose level after confirming the judgment rules on the visit dimension",
    "drug.configure.setting.dose.form.list.isFirstInitial": "Initial dose enabled at the first visit",
    "drug.configure.setting.dose.form.list.isDoseReduction": "Allows the subject to reduce the dose a certain number of times",
    "drug.configure.setting.dose.form.list.name": "Name/Value",
    "drug.configure.setting.dose.form.list.group": "Group",
    "drug.configure.setting.dose.form.list.dose.distribution": "Dispensation Dose",
    "drug.configure.setting.dose.form.list.initial.dose": "Initial Dose",
    "drug.configure.setting.dose.form.list.visit.inheritance": "Subsequent visits inherit",
    "drug.configure.setting.dose.form.list.visit.inheritance.dispensing": "Visit-based Inherited Dispensation",
    "drug.configure.setting.dose.form.list.visit.inheritance.stop": "Stop Dispensation",
    "drug.configure.setting.dose.form.list.visit.inheritance.main": "Main Visit",

    "drug.configure.setting.dose.form.list.name.selectedRepeatedly": "Name/value can't be duplicated",

    "visit.cycle.management.is.group": "The group has been modified, please remap the visit to the group.",
    "visit.cycle.management.drug.configure.is.group": "The group has been modified, please remap the IP name to the group.",
    "visit.cycle.management.drug.configure.is.group.label": "IP configuration has been modified, please  reconfigure the dispensation dose.",
    "random.list.inactivating.tips": "After invalidation, the invalid group can be synchronized with the activated randomization list through the synchronization operation, and the invalid randomization ID cannot be activated again.",
    "random.list.inactivating.success": "Synchronization was successful and the randomization list has skipped the invalid group randomization ID rules.",

    "common.export.format": "Export Format",

    "visit.cycle.management.is.group.visit.save.release.unpublished": "There are unpublished visits",

    "visit.cycle.management.is.group.visit.save": "The operation is successful, the visit plan needs to be released to take effect, ",
    "visit.cycle.management.is.group.visit.save.release": "Release immediately",

    "visit.cycle.management.is.group.visit.release.save": "Released successfully, the IP configuration needs to be re-associated to confirm the name of the visit, ",
    "visit.cycle.management.is.group.visit.release.save.treatmentDesign": "Go",

    "visit.cycle.management.is.group.visit.batch.save": "批次号已更新，请重新确认研究产品继承规则。",

    "env.copy.prod.isCopy.prompt.language": "The PROD environment does not generate subject/shipment data and can be directly configured for replication.",

    "env.copy.prod.isCopy.prompt.language.tips": "After copying, some core configurations within the environment are not allowed to be deleted/modified.",


    "user.batch.add.email.required": "Please enter email, separate multiple entries with a line break.",
    "user.batch.add.email.tip1": "Unable to add email to the list.",
    "user.batch.add.email.tip2": 'Unable to add email to the list. Click "Ignore and Continue" to bypass these emails.',
    "common.back.revise": "Back To Revise",
    "common.ignore.continue": "Ignore And Continue",
    "common.user.add.success": "User Added Successfully",
    "common.user.add.configure.permissions": "Configure Permissions",
    "common.user.add.configure.permissions.role": "Configure Roles",
    "common.user.add.configure.permissions.tip": 'You can click the "Configure Permissions" button to grant permissions to users in bulk.',
    "common.user.add.configure.permissions.role.tip": 'You can click the "Configure Roles" button to grant roles to users in bulk.',




    "notice.exclude_recipient_list.email": "Excluded Recipients",
    "notice.exclude_recipient_list.email.batch": "Mass Export",
    "notice.exclude_recipient_list.email.batch.tip": "Uploading Excel files is supported only",
    "notice.exclude_recipient_list.email.account": "Email Account",
    "notice.exclude_recipient_list.email.tip1": "None of the list email addresses are project users. Please re-enter.",
    "notice.exclude_recipient_list.email.tip2": "The list email address is not a project user. Clicking 'Ignore and Continue' indicates that the operation will only apply to the remaining email addresses.",
    "notice.exclude_recipient_list.email.tip3": "Strikethrough indicates that the email address is unbound.",

    "week.all": "Every day",
    "week.monday": "Mon",
    "week.tuesday": "Tue",
    "week.wednesday": "Wed",
    "week.thursday": "Thu",
    "week.friday": "Fri",
    "week.saturday": "Sat",
    "week.sunday": "Sun",
    "time.hour": "Hour",
    "time.minute": "Minute",


    "multiLanguage.allLanguage": "All Languages",
    "multiLanguage.enable.status": "Enabled Status",
    "multiLanguage.translation.progress": "Translation Progress",
    "multiLanguage.shared.system.library": "Shared System Library",
    "multiLanguage.language.library": "Language Library",
    "multiLanguage.language.library.system": "Project Library/System Library",
    "multiLanguage.language.library.construct": "Project Library/Project Design",
    "multiLanguage.language.library.eIRT": "eIRT Library",
    "multiLanguage.page.path": "Page Path",
    "multiLanguage.translation.status": "Translation Status",
    "multiLanguage.translation.status.yes": "Translated",
    "multiLanguage.translation.status.no": "Not Translated",
    "multiLanguage.translation.import": "Import",
    "multiLanguage.translation.no.data": "Enter search criteria and view results here.",
    "multiLanguage.translation.downloadTemplate.downloadCheckbox": "Download Checkbox",
    "multiLanguage.translation.downloadTemplate.downloadAll": "Download All",
    "multiLanguage.translation.custom": "Custom Translation",
    "multiLanguage.translation.placeholder.disabled": "Inputting {} is not allowed",

    "upload.click": "Click here",
    "upload.drag": " or drag and drop files to upload",
    "upload.excel.reminder": "Supported file formats for upload: csv/xls/xlsx. File size must not exceed 100M",
    "upload.import.success": "Save/import successful",
    "upload.excel.header.error": "The imported file header does not match the required content on the page. Please confirm and re-upload.",
    "upload.excel.key.not-match": "Key values not applicable to the current form were detected in the file. Please confirm and re-upload.",

    "projects.remark": "Remark",

    'tips.user.invalid': "Operation failed, current user status is invalid, please confirm with administrator.",
    "tips.user.Permission": "Permission changed, please contact administrator to confirm.",

    "common.select.all": "Select All",
    "common.select.invert.currentPage": "Invert page selection",
    "common.select.clearAll": "Clear All",

    "barcode.label.selectDrug.info": "Please select the IP ",
    "barcode.label": "Label",
    "barcode.label.number": "Label ID",
    "barcode.label.printCount": "Print Quantity",
    "barcode.label.printPaperSize": "Print Paper Size",
    "barcode.label.size": "Label Size",
    "barcode.label.baseComponent": "Basic Components",
    "barcode.label.send": "Send",
    "barcode.label.send.confirm": "Is it confirmed to send?",
    "barcode.label.codeManual": "Manual",
    "barcode.label.codeAuto": "Auto",
    "barcode.label.addSuccessTips": "Label Added Successfully",
    "barcode.label.preview": "Label Preview",
    "barcode.label.printPreview": "Print Preview",
    "barcode.label.component.delete": "Delete component?",
    "barcode.label.component.textBox": "TextBox",
    "barcode.label.sendSuccess": "Send Successfully",
    "barcode.label.component.smartAlign": "Smart Align",
    "barcode.label.attribute.textAlignLeft": "Left",
    "barcode.label.attribute.textAlignCenter": "Center",
    "barcode.label.attribute.textAlignRight": "Right",
    "barcode.label.attribute.fontFamilyMicrosoftYaHei": "MicrosoftYaHei",
    "barcode.label.attribute.fontFamilySong": "SimSun",
    "barcode.label.attribute.fontFamilyArial": "Arial",
    "barcode.label.attribute.fontFamilyTimesNewRoman": "TimesNewRoman",
    "barcode.label.attribute.text": "Text",
    "barcode.label.attribute.position": "Position",
    "barcode.label.attribute.fontFamily": "Font",
    "barcode.label.attribute.width": "Width",
    "barcode.label.attribute.height": "Height",
    "barcode.label.attribute.marginTop": "Top Margin",
    "barcode.label.attribute.marginLeft": "Left Margin",
    "barcode.label.template.select": "Select Template",
    "barcode.label.template.name.default": "Default Template",
    "barcode.label.template.linkTips": "Clicking 'Confirm' will overwrite the current content.",
    "barcode.label.template.emptyTips": "Drag and drop the basic components onto this area",

};
