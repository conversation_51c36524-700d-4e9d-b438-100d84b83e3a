import {Card, Col, message, Row, Spin, Tooltip} from "antd";
import {FormattedMessage, useTranslation} from "../../common/multilingual/component";
import React, {useEffect, useRef, useState} from "react";
import {useAuth} from "../../../context/auth";
import {useSafeState} from "ahooks";
import ReactEcharts from "echarts-for-react";
import {Title} from "components/title";
import {useFetch} from "../../../hooks/request";
import {subjectSiteStatistics, subjectStatistics} from "../../../api/project_dynamics";
import {exportReport} from "../../../api/report";
import {permissions} from "../../../tools/permission";
import {useGlobal} from "../../../context/global";
import {useSubject} from "./context";
import {CohortSelect} from "./cohort_select";
import {inRandomIsolation} from "../../../utils/in_random_isolation";
import {AuthWrap} from "../../common/auth-wrap";

export const ProjectSubjectStatistics = () => {
    const g = useGlobal()
    const intl = useTranslation();
    const home = useSubject()

    const {formatMessage} = intl;
    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null;
    const [pieOption, setPieOption] = useSafeState<any>({});
    const [barOption, setBarOption] = useSafeState<any>({});
    const cohortIds = auth.env?.cohorts
    const [cohortId, setCohortId] = useState<any>(null);
    const [secondStageName, setSecondStageName] = useState<any>("");
    const [attribute, setAttribute] = useState<any>(null);
    const pieRef = useRef<any>()
    const barRef = useRef<any>()
    const {runAsync: subjectSiteStatisticsRun, loading: getRandomStatisticsPieLoading} = useFetch(subjectSiteStatistics, {manual: true})
    const {runAsync: subjectStatisticsRun, loading: getRandomStatisticsBarLoading} = useFetch(subjectStatistics, {manual: true})
    useEffect(() => {
        if (
            attribute &&
            (auth.env?.cohorts.length === 0 ||cohortId)){
            subjectStatisticsFun()
            subjectSiteStatisticsFun()
        }

    }, [g.lang, attribute])
    const isReRandom = ()=>{
        let cohort = auth.env.cohorts.find((item:any)=>item.id === cohortId);
        let reRandomCohorts = auth.env.cohorts.filter((item:any)=>item.name === cohort.name);
    return (auth.project.info.type === 3 && !inRandomIsolation(auth.project.info.number) && auth.env.cohorts[0].id !== cohortId)||
        (auth.project.info.type === 2 && cohort.type === 1 && reRandomCohorts[0]?.id !== cohortId)
    }

    const subjectStatisticsFun = () => {
        subjectStatisticsRun({envId:envId, roleId:auth.project.permissions.role_id,cohortId: cohortId}).then((resp:any)=>{
            let array :any
            let colors :any
            if (!attribute?.info.random){
                if (attribute?.info.isScreen){
                    if (isReRandom()){
                        array = [
                            {name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,value:resp.data.toBeRandom},
                            {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                            {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                        ];
                        colors= [
                            '#165DFF',
                            '#F53F3F',
                            '#00B42A',
                        ]
                    }else {
                        if (attribute?.info.dispensing){
                            array = [
                                {name:formatMessage({id:"project.subject.statistics.info.status.register"}),value:resp.data.register},
                                {name:formatMessage({id:"subject.status.join"}),value:resp.data.join},
                                {name:formatMessage({id:"project.subject.statistics.info.status.screenSuccess"}),value:resp.data.screenSuccess},
                                {name:formatMessage({id:"project.subject.statistics.info.status.screenFail"}),value:resp.data.screenFail},
                                {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors= [
                                '#165DFF',
                                '#3491FA',
                                '#9FDB1D',
                                '#FADC19',
                                '#F53F3F',
                                '#00B42A',
                            ]
                        }else {
                            array = [
                                {name:formatMessage({id:"project.subject.statistics.info.status.register"}),value:resp.data.register},
                                {name:formatMessage({id:"project.subject.statistics.info.status.screenSuccess"}),value:resp.data.screenSuccess},
                                {name:formatMessage({id:"project.subject.statistics.info.status.screenFail"}),value:resp.data.screenFail},
                                {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors= [
                                '#165DFF',
                                '#9FDB1D',
                                '#FADC19',
                                '#F53F3F',
                                '#00B42A',
                            ]
                        }
                    }

                }else {
                    if (isReRandom()){
                        array = [
                            {name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,value:resp.data.toBeRandom},
                            {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                            {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                        ];
                        colors= [
                            '#165DFF',
                            '#F53F3F',
                            '#00B42A',
                        ]
                    }else {
                        if (attribute?.info.dispensing){
                            array = [
                                {name:formatMessage({id:"project.subject.statistics.info.status.register"}),value:resp.data.register},
                                {name:formatMessage({id:"subject.status.join"}),value:resp.data.join},
                                {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors= [
                                '#165DFF',
                                '#3491FA',
                                '#F53F3F',
                                '#00B42A',
                            ]
                        }else {
                            array = [
                                {name: formatMessage({id: "project.subject.statistics.info.status.register"}), value: resp.data.register},
                                {name: formatMessage({id: "project.subject.statistics.info.status.exit"}), value: resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors = [
                                '#165DFF',
                                '#F53F3F',
                                '#00B42A',
                            ]
                        }
                    }

                }

            }else{
                if (attribute.info.isScreen){
                    if(attribute.info.blind){
                        if (isReRandom()){
                            array = [
                                {name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,value:resp.data.toBeRandom},
                                {name:formatMessage({id:"project.subject.statistics.info.status.random"}),value:resp.data.random},
                                {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.unblinding"}),value:resp.data.unBlind},
                                {name:formatMessage({id:"project.subject.statistics.info.status.pv"}),value:resp.data.pvUnBlind},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors= [
                                '#165DFF',
                                '#3491FA',
                                '#F53F3F',
                                '#722ED1',
                                '#4E2BC0',
                                '#00B42A',
                            ]
                        }else {
                            array = [
                                {name:formatMessage({id:"project.subject.statistics.info.status.register"}),value:resp.data.register},
                                {name:formatMessage({id:"project.subject.statistics.info.status.screenSuccess"}),value:resp.data.screenSuccess},
                                {name:formatMessage({id:"project.subject.statistics.info.status.screenFail"}),value:resp.data.screenFail},
                                {name:formatMessage({id:"project.subject.statistics.info.status.random"}),value:resp.data.random},
                                {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.unblinding"}),value:resp.data.unBlind},
                                {name:formatMessage({id:"project.subject.statistics.info.status.pv"}),value:resp.data.pvUnBlind},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors= [
                                '#165DFF',
                                '#9FDB1D',
                                '#FADC19',
                                '#3491FA',
                                '#F53F3F',
                                '#722ED1',
                                '#4E2BC0',
                                '#00B42A',
                            ]
                        }
                    }else{
                        if (isReRandom()){
                            array = [
                                {name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,value:resp.data.toBeRandom},
                                {name:formatMessage({id:"project.subject.statistics.info.status.random"}),value:resp.data.random},
                                {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors= [
                                '#165DFF',
                                '#3491FA',
                                '#F53F3F',
                                '#00B42A',
                            ]
                        }else {
                            array = [
                                {name:formatMessage({id:"project.subject.statistics.info.status.register"}),value:resp.data.register},
                                {name:formatMessage({id:"project.subject.statistics.info.status.screenSuccess"}),value:resp.data.screenSuccess},
                                {name:formatMessage({id:"project.subject.statistics.info.status.screenFail"}),value:resp.data.screenFail},
                                {name:formatMessage({id:"project.subject.statistics.info.status.random"}),value:resp.data.random},
                                {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors= [
                                '#165DFF',
                                '#9FDB1D',
                                '#FADC19',
                                '#3491FA',
                                '#F53F3F',
                                '#00B42A',
                            ]
                        }

                    }
                }else{
                    if(attribute.info.blind){
                        if (isReRandom()){
                            array = [
                                {name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,value:resp.data.toBeRandom},
                                {name:formatMessage({id:"project.subject.statistics.info.status.random"}),value:resp.data.random},
                                {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.unblinding"}),value:resp.data.unBlind},
                                {name:formatMessage({id:"project.subject.statistics.info.status.pv"}),value:resp.data.pvUnBlind},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors= [
                                '#165DFF',
                                '#3491FA',
                                '#F53F3F',
                                '#722ED1',
                                '#4E2BC0',
                                '#00B42A',
                            ]
                        }else {
                            array = [
                                {name:formatMessage({id:"project.subject.statistics.info.status.register"}),value:resp.data.register},
                                {name:formatMessage({id:"project.subject.statistics.info.status.random"}),value:resp.data.random},
                                {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.unblinding"}),value:resp.data.unBlind},
                                {name:formatMessage({id:"project.subject.statistics.info.status.pv"}),value:resp.data.pvUnBlind},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors= [
                                '#165DFF',
                                '#3491FA',
                                '#F53F3F',
                                '#722ED1',
                                '#4E2BC0',
                                '#00B42A',
                            ]
                        }

                    }else{
                        if (isReRandom()){
                            array = [
                                {name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,value:resp.data.toBeRandom},
                                {name:formatMessage({id:"project.subject.statistics.info.status.random"}),value:resp.data.random},
                                {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors= [
                                '#165DFF',
                                '#3491FA',
                                '#F53F3F',
                                '#00B42A',
                            ]
                        }else {
                            array = [
                                {name:formatMessage({id:"project.subject.statistics.info.status.register"}),value:resp.data.register},
                                {name:formatMessage({id:"project.subject.statistics.info.status.random"}),value:resp.data.random},
                                {name:formatMessage({id:"project.subject.statistics.info.status.exit"}),value:resp.data.exit},
                                {name:formatMessage({id:"project.subject.statistics.info.status.finish"}),value:resp.data.finish},
                            ];
                            colors= [
                                '#165DFF',
                                '#3491FA',
                                '#F53F3F',
                                '#00B42A',
                            ]
                        }

                    }
                }
            }

            pieRef.current.dispose()
            setPieOption({
                color: colors,
                title: {
                    text: formatMessage({id:"project.statistics"}),
                    textStyle: {
                        color: '#677283',
                        fontWeight: 400,
                        fontSize: 12
                    },
                    left: 'center',
                    bottom: "center",
                },
                tooltip:{
                    trigger:'item'
                },
                series: [
                    {

                        type: 'pie',
                        data: array,
                        radius: ['40%', '65%'],
                        label:{
                            show: false
                        },
                        roseType: 'radius',
                        labelLine: {
                            show: false
                        },
                    },

                ],
                legend: {
                    itemWidth:6,
                    itemHeight:6,
                    icon:'circle',
                    bottom: 0,
                    show: true,
                    type: 'scroll',
                    formatter: function(name:any) {
                        // console.log("===" + name);
                        let value :any=  array.find((it:any)=> it.name === name).value
                        return name+":"+value
                    }
                },
            })
            pieRef.current.resize()
        })

    }
    const subjectSiteStatisticsFun = () => {
        subjectSiteStatisticsRun({envId:envId, roleId:auth.project.permissions.role_id, cohortId:cohortId}).then(
            (resp:any) => {
                let title :any = []
                let register :any = []
                let random :any = []
                let exited :any = []
                let unblinding :any = []
                let pvUnblinding :any = []
                let screenSuccess :any = []
                let screenFail :any = []
                let finish :any = []
                let toBeRandom :any = []
                let join :any = []
                resp.data && resp.data.forEach(
                    (item:any) => {
                        title.push(item.name)
                        let registerItem = item.subjectStatusCount.find((it:any)=> it.status === 1)?.count
                        registerItem? register.push(registerItem) : register.push(0)

                        let randomItem = item.subjectStatusCount.find((it:any)=> it.status === 3)?.count
                        randomItem? random.push(randomItem) : random.push(0)

                        let exitedItem = item.subjectStatusCount.find((it:any)=> it.status === 5)?.count
                        exitedItem ? exited.push(exitedItem) : exited.push(0)

                        let unblindingItem = item.subjectStatusCount.find((it:any)=> it.status === 6)?.count
                        unblindingItem? unblinding.push(unblindingItem) : unblinding.push(0)

                        let pvUnblindingItem = item.subjectStatusCount.find((it:any)=> it.status === 12)?.count
                        pvUnblindingItem? pvUnblinding.push(pvUnblindingItem) : pvUnblinding.push(0)


                        let screenSuccessItem = item.subjectStatusCount.find((it:any)=> it.status === 7)?.count
                        screenSuccessItem? screenSuccess.push(screenSuccessItem) : screenSuccess.push(0)

                        let ScreenFailItem = item.subjectStatusCount.find((it:any)=> it.status === 8)?.count
                        ScreenFailItem? screenFail.push(ScreenFailItem) : screenFail.push(0)

                        let finishItem = item.subjectStatusCount.find((it:any)=> it.status === 9)?.count
                        finishItem? finish.push(finishItem) : finish.push(0)

                        let toBeRandomItem = item.subjectStatusCount.find((it:any)=> it.status === 10)?.count
                        toBeRandomItem? toBeRandom.push(toBeRandomItem) : toBeRandom.push(0)

                        let joinItem = item.subjectStatusCount.find((it:any)=> it.status === 11)?.count
                        joinItem? join.push(joinItem) : join.push(0)
                    }
                )
                let server:any
                if (!attribute.info.random){
                    if (attribute.info.isScreen){
                        if (isReRandom()){
                            server = [
                                {
                                    name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,
                                    type: 'bar',
                                    data: toBeRandom,
                                    itemStyle: {
                                        color: "#165DFF"
                                    },
                                    barGap:"0%",


                                },
                                {
                                    name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                    barGap:"0%",
                                    type: 'bar',
                                    data: exited,
                                    itemStyle: {
                                        color: "#F53F3F"
                                    }
                                },
                                {
                                    name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                    barGap:"0%",
                                    type: 'bar',
                                    data: finish,
                                    itemStyle: {
                                        color: "#00B42A"
                                    }
                                }
                            ]
                        }else {
                            if (attribute.info.dispensing){
                                server = [
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.register"}),
                                        type: 'bar',
                                        data: register,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },
                                        barGap:"0%",
                                    },
                                    {
                                        name:formatMessage({id:"subject.status.join"}),
                                        type: 'bar',
                                        data: join,
                                        itemStyle: {
                                            color: "#3491FA"
                                        },
                                        barGap:"0%",
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.screenSuccess"}),
                                        type: 'bar',
                                        data: screenSuccess,
                                        itemStyle: {
                                            color: "#9FDB1D"
                                        },
                                        barGap:"0%",

                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.screenFail"}),
                                        type: 'bar',
                                        data: screenFail,
                                        itemStyle: {
                                            color: "#FADC19"
                                        },
                                        barGap:"0%",

                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }else {
                                server = [
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.register"}),
                                        type: 'bar',
                                        data: register,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },
                                        barGap:"0%",


                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.screenSuccess"}),
                                        type: 'bar',
                                        data: screenSuccess,
                                        itemStyle: {
                                            color: "#9FDB1D"
                                        },
                                        barGap:"0%",

                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.screenFail"}),
                                        type: 'bar',
                                        data: screenFail,
                                        itemStyle: {
                                            color: "#FADC19"
                                        },
                                        barGap:"0%",

                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }


                        }
                    }else {
                        if (isReRandom()){
                            server = [
                                {
                                    name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,
                                    barGap:"0%",
                                    type: 'bar',
                                    data: toBeRandom,
                                    itemStyle: {
                                        color: "#165DFF"
                                    },

                                },
                                {
                                    name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                    barGap:"0%",
                                    type: 'bar',
                                    data: exited,
                                    itemStyle: {
                                        color: "#F53F3F"
                                    }
                                },
                                {
                                    name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                    barGap:"0%",
                                    type: 'bar',
                                    data: finish,
                                    itemStyle: {
                                        color: "#00B42A"
                                    }
                                }
                            ]
                        }else {
                            if (attribute.info.dispensing){
                                server = [
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.register"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: register,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },
                                    },
                                    {
                                        name:formatMessage({id:"subject.status.join"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: join,
                                        itemStyle: {
                                            color: "#3491FA"
                                        },
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }else {
                                server = [
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.register"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: register,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },

                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }

                        }
                    }
                }else {
                    if (attribute?.info.isScreen){
                        if(attribute.info.blind){
                            if (isReRandom()){
                                server = [
                                    {
                                        name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,
                                        type: 'bar',
                                        data: toBeRandom,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },
                                        barGap:"0%",
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.random"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: random,
                                        itemStyle: {
                                            color: "#3491FA"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.unblinding"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: unblinding,
                                        itemStyle: {
                                            color: "#722ED1"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.pv"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: pvUnblinding,
                                        itemStyle: {
                                            color: "#4E2BC0"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }else {
                                server = [
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.register"}),
                                        type: 'bar',
                                        data: register,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },
                                        barGap:"0%",
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.screenSuccess"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: screenSuccess,
                                        itemStyle: {
                                            color: "#9FDB1D"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.screenFail"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: screenFail,
                                        itemStyle: {
                                            color: "#FADC19"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.random"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: random,
                                        itemStyle: {
                                            color: "#3491FA"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.unblinding"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: unblinding,
                                        itemStyle: {
                                            color: "#722ED1"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.pv"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: pvUnblinding,
                                        itemStyle: {
                                            color: "#4E2BC0"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }
                        }else{
                            if (isReRandom()){
                                server = [
                                    {
                                        name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,
                                        type: 'bar',
                                        data: toBeRandom,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },
                                        barGap:"0%",
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.random"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: random,
                                        itemStyle: {
                                            color: "#3491FA"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }else {
                                server = [
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.register"}),
                                        type: 'bar',
                                        data: register,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },
                                        barGap:"0%",
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.screenSuccess"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: screenSuccess,
                                        itemStyle: {
                                            color: "#9FDB1D"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.screenFail"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: screenFail,
                                        itemStyle: {
                                            color: "#FADC19"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.random"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: random,
                                        itemStyle: {
                                            color: "#3491FA"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }

                        }

                    }else{
                        if(attribute.info.blind){
                            if (isReRandom()){
                                server = [
                                    {
                                        name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,
                                        barGap:"0%",
                                        type: 'bar',
                                        data: toBeRandom,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.random"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: random,
                                        itemStyle: {
                                            color: "#3491FA"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.unblinding"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: unblinding,
                                        itemStyle: {
                                            color: "#722ED1"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.pv"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: pvUnblinding,
                                        itemStyle: {
                                            color: "#4E2BC0"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }else{
                                server = [
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.register"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: register,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.random"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: random,
                                        itemStyle: {
                                            color: "#3491FA"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.unblinding"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: unblinding,
                                        itemStyle: {
                                            color: "#722ED1"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.pv"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: pvUnblinding,
                                        itemStyle: {
                                            color: "#4E2BC0"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }

                        }else{
                            if (isReRandom()){
                                server = [
                                    {
                                        name:formatMessage({id:"subject.status.to.be.random"}) + secondStageName,
                                        barGap:"0%",
                                        type: 'bar',
                                        data: toBeRandom,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.random"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: random,
                                        itemStyle: {
                                            color: "#3491FA"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }else{
                                server = [
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.register"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: register,
                                        itemStyle: {
                                            color: "#165DFF"
                                        },
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.random"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: random,
                                        itemStyle: {
                                            color: "#3491FA"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.exit"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: exited,
                                        itemStyle: {
                                            color: "#F53F3F"
                                        }
                                    },
                                    {
                                        name:formatMessage({id:"project.subject.statistics.info.status.finish"}),
                                        barGap:"0%",
                                        type: 'bar',
                                        data: finish,
                                        itemStyle: {
                                            color: "#00B42A"
                                        }
                                    }
                                ]
                            }

                        }
                    }
                }
                let info :any = []
                server.forEach((value: any, index: any) => {
                    if (value.data.find((it:any)=> it !== 0)){
                        info.push(value)
                    }
                })
                barRef.current.dispose()
                setBarOption({
                    grid: {
                        bottom: title.length > 0 ? 60 : 30,
                    },
                    xAxis: {
                        type: 'category',

                        data: title,
                        axisLabel: {
                            interval: 0,
                            overflow: "truncate",
                            width: 100,
                        },
                    },
                    yAxis:{
                        type:(register.length > 0||
                            random.length > 0||
                            exited.length > 0||
                            unblinding.length > 0)  ? 'value' : 'category',
                        data: [0, 5, 10, 15, 20, 25],
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            show: true
                        },
                        axisLine:{
                            show:false
                        }
                    },
                    barWidth:"10%",
                    dataZoom: [{
                        type: "slider",
                        height: 7,
                        bottom: 25,
                        start: 0,
                        endValue: 2,
                        show: title.length > 3,
                        textStyle: false,
                        brushSelect: false,
                        handleStyle: {
                            opacity: 0
                        },
                        dataBackground: {
                            lineStyle: {
                                opacity: 0
                            },
                            areaStyle: {
                                opacity: 0
                            }
                        },
                        fillerColor: "#E3E4E6"
                    }],
                    tooltip: {
                        trigger: 'axis'
                    },
                    series: info,
                    legend: {
                        bottom: -5,
                        show: true,
                        icon:'rect',
                        itemHeight:8,
                        itemWidth:10,
                    },
                })
                barRef.current.resize()
            }
        )

    }

    const {runAsync: exportReportRun, loading: exportReportLoading} = useFetch(exportReport, {manual: true})

    const downloadData = () => {
        const params = {
            type: 2,
            projectId: auth.project.id,
            envId: envId,
            roleId: auth.project.permissions.role_id,
            cohortId: cohortId,
            cohortIds: cohortId? [cohortId]:[],
        };
        exportReportRun(params).then(() => {
            message.success(formatMessage({ id: "report.download.success" }));
        });
    }
    return (
        <>
            <Card headStyle={{ padding: "0 16px" }} bodyStyle={{ padding: "16px" }} title={
                <Row justify={"space-between"} align="middle">
                    <Row align="middle">
                        <Title name={<FormattedMessage id={"project.subject.statistics"} />}/>
                        <Tooltip
                            trigger={["hover", "click"]}
                            overlayInnerStyle={{ width: 310, fontSize: 12, background: "#646566" }}
                            placement="top"
                            title={
                                <>
                                    <Row><FormattedMessage id="project.subject.statistics.info" /></Row>
                                    <Row><FormattedMessage id="project.subject.statistics.info.register" /></Row>
                                    <Row><FormattedMessage id="project.subject.statistics.info.screenSuccess" /></Row>
                                    <Row><FormattedMessage id="project.subject.statistics.info.screenFail" /></Row>
                                    <Row><FormattedMessage id="project.subject.statistics.info.random" /></Row>
                                    <Row><FormattedMessage id="project.subject.statistics.info.exit" /></Row>
                                    <Row><FormattedMessage id="project.subject.statistics.info.unblinding" /></Row>
                                    <Row><FormattedMessage id="project.subject.statistics.info.pvUnblinding" /></Row>
                                    <Row><FormattedMessage id="project.subject.statistics.info.finish" /></Row>
                                </>
                            }>
                            <svg className="iconfont mouse" width={12} height={12} style={{ marginLeft: 4, cursor: "pointer"}} >
                                <use xlinkHref="#icon-xinxitishi"/>
                            </svg>
                        </Tooltip>
                    </Row>
                    <Col>
                        <AuthWrap
                            show={permissions(auth.project.permissions, "operation.project.subject.download")}
                            previewProps={{hideOnPreview: true}}
                        >
                            <Spin spinning={exportReportLoading}>
                                <div style={{ display: 'flex', alignItems: 'center',float:'right' }} onClick={downloadData}>
                                    <svg className="iconfont download-hover">
                                        <use xlinkHref="#icon-daochu"/>
                                    </svg>
                                </div>
                            </Spin>
                        </AuthWrap>
                    </Col>
                </Row>
            }
            >
                <CohortSelect cohortId={cohortId} setCohortId={(e:any)=>{
                    let cohort = auth.env.cohorts?.find((item:any)=>item.id === e);
                    let reRandomCohorts = auth.env.cohorts?.filter((item:any)=>item.name === cohort.name);
                    setSecondStageName(auth.project.info.type === 3? "(" +auth.env.cohorts[1]?.name +")":"("+reRandomCohorts[1]?.re_random_name+")")
                    setCohortId(e)
                }}
                              attribute={attribute} setAttribute={setAttribute}
                              cohortIds ={auth.env?.cohorts}
                />
                <Row gutter={24}>
                    <Col span={12}>
                        <Spin spinning={getRandomStatisticsPieLoading}>
                            <ReactEcharts ref={pieRef} style={{ flexGrow: 1}} option={pieOption}/>
                        </Spin>
                    </Col>
                    <Col span={12}>
                        <Spin spinning={getRandomStatisticsBarLoading}>
                            <ReactEcharts ref={barRef} style={{flexGrow: 1}} option={barOption}/>
                        </Spin>
                    </Col>
                </Row>
            </Card>
        </>
    )
};
