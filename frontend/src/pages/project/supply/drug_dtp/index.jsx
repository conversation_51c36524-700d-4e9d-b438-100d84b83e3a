import React from "react";
import {Tabs} from "antd";
import {FormattedMessage} from "../../../common/multilingual/component";
import {OtherMedicineSku} from "./other_medicine_sku";
import {Sku} from "./sku";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {Order} from "./order";
import {permissions} from "../../../../tools/permission";
import {PageContextProvider} from "../../../../context/page";

export const DrugDTP = (props) => {

    const [tabKey, setTabKey] = useSafeState("1");
    const auth = useAuth()


    const changeTab = (key) => {
        setTabKey(key)
    }
    return (
        <React.Fragment>
            <PageContextProvider>
                <Tabs size="small" defaultActiveKey="1" tabPosition="top" onChange={changeTab}>
                    {
                        permissions(auth.project.permissions, "operation.supply.drug.order.list") &&
                        <Tabs.TabPane tab={<FormattedMessage id="menu.projects.project.supply.drug.order" />}
                            key="1">
                            {tabKey === "1" ? <Order /> : null}
                        </Tabs.TabPane>
                    }
                    {
                        permissions(auth.project.permissions, "operation.supply.drug.single.sku") &&
                        <Tabs.TabPane tab={<FormattedMessage id="menu.projects.project.supply.drug.single" />}
                            key="2">
                            {tabKey === "2" ? <Sku /> : null}
                        </Tabs.TabPane>
                    }
                    {
                        permissions(auth.project.permissions, "operation.supply.drug.no_number.view") &&
                        <Tabs.TabPane tab={<FormattedMessage id="menu.projects.project.supply.drug.no_number" />}
                            key="3">
                            {tabKey === "3" ? <OtherMedicineSku /> : null}
                        </Tabs.TabPane>
                    }
                </Tabs>
            </PageContextProvider>
        </React.Fragment>
    )
};