import { PageContextProvider } from "../../../../../context/page";
import { useSafeState } from "ahooks";
import { useCacheTable } from "../../../../../hooks/cache-table";
import { Button, Table } from "antd";
import { FormattedMessage } from "../../../../common/multilingual/component";
import { GroupPaginationView } from "./groupPagination";
import React, { useEffect } from "react";
import { DrugModal } from "./drugModal";
import { TableCellPopover } from "components/popover";
import { fillTableCellEmptyPlaceholder } from "../../../../../components/table";

interface GroupTableProps {
	selectedIdsSet: Set<any>;
	allIdsSet: Set<any>;
	setAllIdsSet: any;
	setSelectedIdsSet: any;
	selectedGroupCountMap: Map<string, number>;
	setSelectedGroupCountMap: any;
	dataSource: any[];
	rowSelection: any;
	medicineListParams: {
		customerId: string | undefined
		envId: string
		sendId: string,
	}
	packageIsOpen: boolean
}

export const CacheGroupTable = (props: GroupTableProps) => {
	return (
		<PageContextProvider>
			<GroupTable {...props}></GroupTable>
		</PageContextProvider>
	)
}

function renderPackageMethod(packageMethod: string) {
	return (
		packageMethod === "true"
			?
			<FormattedMessage id="shipment.order.packageMethod.package" />
			:
			<FormattedMessage id="shipment.order.packageMethod.single" />
	);
}

const GroupTable = (props: GroupTableProps) => {

	var tableData = useCacheTable(props);
	tableData = fillTableCellEmptyPlaceholder(tableData)

	const [stockModalShow, setStockModalShow] = useSafeState(false)
	const [groupInfo, setGroupInfo] = useSafeState<any>(props.medicineListParams)

	const [empty, setEmpty] = useSafeState<boolean>(false)

	const openModal = (id: string, name: string, expirationDate: string, batchNumber: string, salts: any) => {
		setGroupInfo({
			id,
			name,
			expirationDate,
			batchNumber,
			salts,
			customerId: props.medicineListParams.customerId,
			envId: props.medicineListParams.envId,
			sendId: props.medicineListParams.sendId,
		})
		setStockModalShow(true)
	}

	useEffect(() => {
		props.setSelectedIdsSet(new Set())
		props.setSelectedGroupCountMap(new Map())
	}, [empty])

	return (
		<div>
			<PageContextProvider>
				<DrugModal
					show={stockModalShow}
					setShow={setStockModalShow}
					medicineGroup={groupInfo}
					allIdsSet={props.allIdsSet}
					setAllIdsSet={props.setAllIdsSet}
					selectedIdsSet={props.selectedIdsSet}
					setSelectedIdsSet={props.setSelectedIdsSet}
					selectedGroupCountMap={props.selectedGroupCountMap}
					setSelectedGroupCountMap={props.setSelectedGroupCountMap}
				/>

			</PageContextProvider>

			<Table
				className="mar-top-10"
				scroll={{ y: 400 }}
				dataSource={tableData}
				pagination={false}
				rowKey={(record: any) => (record.id)}
				rowSelection={props.rowSelection}
			>
				<Table.Column
					title={<FormattedMessage id="drug.configure.drugName" />}
					dataIndex="name"
					key="name"
					ellipsis
					render={(value: any, record: any, index: any) => {
						if (!value) return <></>;
						const items: string[] = [];
						(value as []).forEach((it: any) => {
							items.push(it);
						});
						return (
							items.length > 1 ? <TableCellPopover
								title={
									<span
										style={{
											display: "flex",
											alignItems: "center",
										}}
									>
										<svg
											className="iconfont"
											width={16}
											height={16}
										>
											<use xlinkHref="#icon-quanbuxuanxing"></use>
										</svg>

									</span>
								}
								items={items}
							/> : <div style={{
								display: '-webkit-box',
								WebkitLineClamp: 2,
								WebkitBoxOrient: 'vertical',
								overflow: 'hidden',
								textOverflow: 'ellipsis',
								whiteSpace: 'normal',
								wordBreak: 'break-word',
							}}>
								{items}
							</div>
						);
					}
					}
				/>
				<Table.Column
					title={<FormattedMessage id="drug.list.expireDate" />}
					dataIndex="expirationDate"
					key="expirationDate"
					width={120}
				/>
				<Table.Column
					title={<FormattedMessage id="drug.list.batch" />}
					dataIndex="batchNumber"
					key="batchNumber"
					ellipsis
					width={140}
				/>
				<Table.Column
					title={<FormattedMessage id="drug.list.availableCount" />}
					dataIndex="count"
					key="count"
					width={140}
					render={(value, record: any) => {
						return <Button type="link" size={"small"} onClick={() => {
							openModal(record.id, record.name, record.expirationDate, record.batchNumber, record.salts)
						}
						}>{value}</Button>
					}}
				/>
				{props.packageIsOpen &&
					<Table.Column
						width={110}
						title={<FormattedMessage id="shipment.order.package.method" />}
						dataIndex="packageMethod"
						key="packageMethod"
						ellipsis
						render={(value, record, index) => (renderPackageMethod(value))}
					/>
				}
				<Table.Column
					title={<FormattedMessage id="common.pagination.seleted" />}
					width={80}
					render={(_, record: any) => {
						return props.selectedGroupCountMap.get(record.id) || 0
					}}
				/>
			</Table>
			<div style={{ marginTop: 12 }}>
				<GroupPaginationView
					mode="SELECTABLE"
					selectedNumber={Array.from(props.selectedIdsSet)?.length}
					groupTotal={props.dataSource.reduce((prev, curr) => {
						return prev + curr.count
					}, 0)}
					empty={empty}
					setEmpty={setEmpty}
				></GroupPaginationView>
			</div>
		</div>
	)
}
