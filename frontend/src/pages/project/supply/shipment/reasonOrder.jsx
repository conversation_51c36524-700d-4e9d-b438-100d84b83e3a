import React from "react";
import { Form, Input, message, Modal } from "antd";
import { useFetch } from "../../../../hooks/request";
import { updateOrderStatus } from "../../../../api/order";
import { useSafeState } from "ahooks";
import { useIntl } from "react-intl";
import { useAuth } from "../../../../context/auth";


export const ReasonOrder = (props) => {
    const auth = useAuth();
    const [isVisible, setIsVisible] = useSafeState(false);
    const [orderId, setOrderId] = useSafeState(null);
    const [status, setStatus] = useSafeState(null);
    const [title, setTitle] = useSafeState(null);
    const [form] = Form.useForm();
    const intl = useIntl();
    const { formatMessage } = intl;


    const { runAsync: run_updateOrderStatus, loading: submitting } = useFetch(updateOrderStatus, { manual: true })
    const updateOrder = () => {
        form.validateFields().then(values => {
            run_updateOrderStatus({ id: orderId, status: status, roleId: auth.project.permissions.role_id, reason: values.reason }).then((data) => {
                message.success(data.msg);
                props.refresh();
                hide();
            });
        })
    }

    const show = (id, status, orderNumber) => {
        form.resetFields()
        setIsVisible(true)
        if (id) {
            setOrderId(id);
        }
        if (status) {
            setStatus(status);
            if (status === 9) {// 关闭订单
                setTitle(formatMessage({ id: 'shipment.close.order' }) + " - " + orderNumber)
            } else if (status === 4) {//丢失订单
                setTitle(formatMessage({ id: 'shipment.lose.order' }) + " - " + orderNumber)
            } else if (status === 8) {//终止订单
                setTitle(formatMessage({ id: 'shipment.end.order' }) + " - " + orderNumber)
            } else if (status === 5) {
                setTitle(formatMessage({ id: 'shipment.cancel.order' }) + " - " + orderNumber)
            }
        }
    }

    const hide = () => {
        setIsVisible(false);
        form.resetFields();
    };
    React.useImperativeHandle(props.bind, () => ({ show }));
    return (
        <Modal maskClosable={false} width={600} title={title} visible={isVisible} onOk={updateOrder} okText={formatMessage({ id: 'common.ok' })} onCancel={hide} confirmLoading={submitting} centered>
            <Form form={form} >
                <Form.Item label={formatMessage({ id: 'drug.freeze.reason' })} name="reason" className="mar-ver-5"
                    rules={[{ required: true }]}>
                    <Input.TextArea allowClear placeholder={formatMessage({ id: 'placeholder.input.common' })} />
                </Form.Item>
            </Form>
        </Modal>
    );
};


