import React from "react";
import {Form, Input, message, Modal} from "antd";
import {useFetch} from "../../../../hooks/request";
import {receiveOrder} from "../../../../api/order";
import {useSafeState} from "ahooks";
import {useIntl} from "react-intl";


export const Reason = (props) => {
    const [isVisible, setIsVisible] = useSafeState(false);
    const [receiveMedicineIds, setReceiveMedicineIds] = useSafeState([])
    const [orderId, setOrderId] = useSafeState(null);
    const [otherTableData, setOtherTableData] = useSafeState([]);
    const [form] = Form.useForm();
    const intl = useIntl();
    const {formatMessage} = intl;

    const {runAsync: run_receiveOrder,loading:submitting} = useFetch(receiveOrder, {manual: true});
    const save = () => {
        form.validateFields().then(values => {
            run_receiveOrder(
                {
                    id: orderId,
                    medicines: receiveMedicineIds,
                    otherMedicines: otherTableData,
                    reason:values.reason
                }).then(
                (data) => {
                    message.success(data.msg);
                    props.refresh();
                    hide();
                });
        })
    }

    const show = (orderId, receiveMedicineIds, otherTableData) => {
        form.resetFields()
        setIsVisible(true)
        if (receiveMedicineIds) {
            setReceiveMedicineIds(receiveMedicineIds);
        }
        if (orderId) {
            setOrderId(orderId);
        }
        if (otherTableData) {
            setOtherTableData(otherTableData);
        }
    }
    const hide = () => {
        setIsVisible(false);
        setReceiveMedicineIds(null);
        setOrderId(null);
        setOtherTableData(null);
        form.resetFields();
    };
    React.useImperativeHandle(props.bind, () => ({show}));
    return (
        <Modal maskClosable={false} width={500} title={formatMessage({id: 'shipment.receive.reason'})} 
            visible={isVisible} onOk={save}  okText={formatMessage({ id: 'common.ok' })} onCancel={hide} confirmLoading={submitting} centered>
            <Form form={form} layout="vertical">
                <Form.Item label={formatMessage({id: 'drug.freeze.reason'})} name="reason" className="mar-ver-5"
                        rules={[{required: true}]}>
                    <Input.TextArea allowClear autoSize={{minRows: 3, maxRows: 10}} placeholder={formatMessage({id: 'placeholder.input.common'})}/>
                </Form.Item>
            </Form>
        </Modal>
    );
};
