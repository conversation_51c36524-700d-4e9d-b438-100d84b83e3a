import React from "react";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import { Badge, Button, Form, Row, Space, Popover, InputNumber, Table, Input, Modal, message, Col } from "antd";
import Barcode from "react-barcode";
import { useFetch } from "../../../../hooks/request";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { medicineStatusData } from "../../../../tools/medicine_status";
import { medicineStatusColors } from "../../../../data/data";
import { Title } from "components/title";
import { combineRow } from "../../../../utils/merge_cell";
import _ from "lodash";
import {CustomDateTimePicker} from "../../../../components/CustomDateTimePicker";
import { useGlobal } from "context/global";
import { updateExpirationBatch } from "../../../../api/order";

export const BatchEditExpiration = (props) => {
    const g = useGlobal();
    const intl = useTranslation();
    const { formatMessage } = intl;
    const [visible, setVisible] = useSafeState(false);
    const [tableData, setTableData] = useSafeState([]);
    const [otherTableData, setOtherTableData] = useSafeState([]);
    const [packageIsOpen, setPackageIsOpen] = useSafeState(false);
    const [codeRule, setCodeRule] = useSafeState(null);
    const [orderId, setOrderId] = useSafeState(null);
    const [orderNumber, setOrderNumber] = useSafeState("");
    const auth = useAuth()
    const [form] = Form.useForm();
    const [otherForm] = Form.useForm();
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const projectId = auth.project.id;

    const { runAsync: updateExpirationBatchRun, loading: updateExpirationBatchLoading } = useFetch(updateExpirationBatch, { manual: true });


    const show = (id, orderNumber, packageIsOpen, codeRule, updateMedicines, updateOtherMedicines) => {
        setVisible(true);
        setOrderId(id);
        setOrderNumber(orderNumber);
        setPackageIsOpen(packageIsOpen);
        setCodeRule(codeRule);
        //排序
        updateMedicines = _.sortBy(updateMedicines, ["package_number", "name", "number"])
        if (packageIsOpen) {
            var tableData = combineRow(updateMedicines, "package_number", "package_number", false)
            tableData = combineRow(updateMedicines, "name", "name", false)
            setTableData(tableData);
        } else {
            setTableData(updateMedicines);
        }
        setOtherTableData(updateOtherMedicines)
    };

    const save = () => {
        let idList = [];
        let expirationDate = null;
        let batchNumber = null;
        let orderID = orderId;
        let otherList = [];
        let otherExpirationDate = null;
        let otherBatchNumber = null;
        let isPackage = packageIsOpen;
        // 使用 Promise.all() 等待两个异步操作完成
        Promise.all([
            form.validateFields(),
            otherForm.validateFields()
        ]).then(([values, otherValues]) => {
            // 编号研究产品
            if (tableData !== null && tableData.length > 0) {
                const idArray = tableData.map(obj => obj._id);
                idList = idArray;
            }
            if (values.expiration_date !== undefined && values.expiration_date !== null && values.expiration_date !== "" && values.expiration_date !== "-") {
                expirationDate = values.expiration_date;
            }
            if (values.batch_number !== undefined && values.batch_number !== null && values.batch_number !== "" && values.batch_number !== "-") {
                batchNumber = values.batch_number;
            }

            // 未编号研究产品
            if (otherTableData !== null && otherTableData.length > 0) {
                otherList = otherTableData;
                // otherList = _.cloneDeep(otherTableData);
                // otherList.forEach(item => {
                //     if (item.batch === "-") {
                //         item.batch = ""; // 将满足条件的元素的 batch 属性转换为空字符串
                //     }
                //     if (item.expire_date === "-") {
                //         item.expire_date = ""; // 将满足条件的元素的 expire_date 属性转换为空字符串
                //     }
                // });
            }
            if (otherValues.expire_date !== undefined && otherValues.expire_date !== null && otherValues.expire_date !== "" && otherValues.expire_date !== "-") {
                otherExpirationDate = otherValues.expire_date;
            }
            if (otherValues.batch !== undefined && otherValues.batch !== null && otherValues.batch !== "" && otherValues.batch !== "-") {
                otherBatchNumber = otherValues.batch;
            }

            updateExpirationBatchRun({
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                isPackage: isPackage,
                idList: idList,
                expirationDate: expirationDate,
                batchNumber: batchNumber,
                orderID: orderID,
                otherList: otherList,
                otherExpirationDate: otherExpirationDate,
                otherBatchNumber: otherBatchNumber,
            }).then(() => {
                message.success(formatMessage({ id: "common.success" }));
                hide(); 
            });
        });

    }

    React.useImperativeHandle(props.bind, () => ({ show }));


    const hide = () => {
        setVisible(false);
        setOrderId(null);
        setOrderNumber("");
        setTableData([]);
        setOtherTableData([]);
        form.resetFields();
        otherForm.resetFields();
        props.refresh();
    };

    const cancel = () => {
        hide();
        props.refresh();
    }

    function renderStatus(value, shortCode) {
        let code_rule = 1           // 自动编码
        if (shortCode === "") {
            code_rule = 0           // 手动上传
        }
        return <Badge color={medicineStatusColors[value]} text={medicineStatusData(code_rule, auth.project.info.research_attribute).find(it => (it.value === value)).label} />;
    }


    function renderPackageMethod(packageMethod) {
        return packageMethod === true ? (
            <FormattedMessage id="shipment.order.packageMethod.package" />
        ) : (
            <FormattedMessage id="shipment.order.packageMethod.single" />
        );
    }


    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Modal
                // className="custom-medium-modal"
                width={880}
                // title={<>{formatMessage({ id: "common.edit.batch" })}-<span style={{ fontWeight: 400 }}>{orderNumber}</span></>}
                title={<>{formatMessage({ id: "common.edit.batch" })}</>}
                open={visible}
                onCancel={hide}
                maskClosable={false}
                centered={true}
                bodyStyle={{ padding: 24 }}
                className="custom-small-modal"
                footer={
                    <Row justify={"end"}>
                        <Space>
                            <Button
                                onClick={() => {
                                    hide();
                                }}
                            >
                                {formatMessage({ id: "common.cancel" })}
                            </Button>
                            <Button type={"primary"} onClick={save}>
                                {formatMessage({ id: "common.ok" })}
                            </Button>
                        </Space>
                    </Row>
                }
            >
                <Row style={{ height: g.lang === "zh"?32:52, backgroundColor: "#165DFF1A", marginBottom: 12, paddingLeft: 12, paddingBottom: 12 }}>
                    <Col style={{ width: 24 }}>
                        <svg className="iconfont" width={16} height={16} style={{ marginRight: 12, marginTop: 8 }} fill={"#165DFF"}>
                            <use xlinkHref="#icon-xinxitishi1" ></use>
                        </svg>
                    </Col>
                    <Col style={{ width: "calc(100% - 50px)", marginTop: 6 }}>
                        {formatMessage({ id: "shipment.order.medicine.batch.info" })}
                    </Col>
                </Row>

                {tableData != null && tableData.length > 0 &&
                    <>
                        <div style={{ marginTop: 12, marginBottom: 12 }}>
                            <Title name={formatMessage({ id: 'drug.medicine' })}></Title>
                        </div>
                        <Form form={form} labelCol={{ span: g.lang === "zh"?4:5 }} style={{ marginTop: 12 }} layout="horizontal" >
                            <Form.Item 
                                label={formatMessage({ id: 'drug.list.expireDate' })} 
                                name="expiration_date" 
                                className="mar-ver-5"
                                rules={[{ required: true }]}
                                style={{paddingTop: "12px"}}
                            >
                                <CustomDateTimePicker ></CustomDateTimePicker>
                            </Form.Item>
                            <Form.Item 
                                label={formatMessage({ id: 'drug.list.batch' })} 
                                name="batch_number" 
                                className="mar-ver-5"
                                rules={[{ required: true }]}
                                style={{paddingTop: "12px"}}
                            >
                                <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" />
                            </Form.Item>
                        </Form>
                    </>
                }
                {otherTableData != null && otherTableData.length > 0 &&
                    <>
                        <div style={{ marginTop: 12, marginBottom: 12 }}>
                            <Title name={formatMessage({ id: 'shipment.other.drug' })}></Title>
                        </div>
                        <Form form={otherForm} labelCol={{ span: g.lang === "zh"?4:5 }} style={{ marginTop: 12 }} layout="horizontal" >
                            <Form.Item 
                                label={formatMessage({ id: 'drug.list.expireDate' })} 
                                name="expire_date" 
                                className="mar-ver-5"
                                rules={[{ required: true }]}
                                style={{paddingTop: "12px"}}
                            >
                                <CustomDateTimePicker ></CustomDateTimePicker>
                            </Form.Item>
                            <Form.Item 
                                label={formatMessage({ id: 'drug.list.batch' })} 
                                name="batch" 
                                className="mar-ver-5"
                                rules={[{ required: true }]}
                                style={{paddingTop: "12px"}}
                            >
                                <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" />
                            </Form.Item>
                        </Form>
                    </>
                }
            </Modal>
        </React.Fragment >
    )
};


