import React from "react";
import { Tabs } from "antd";
import { FormattedMessage } from "react-intl";
import { permissions } from "../../../../tools/permission";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { userStoreHouses } from "../../../../api/project_storehouse";
import { Sku } from "./sku";
import { Summary } from "./summary";
import { usePage } from "../../../../context/page";
import { OtherMedicineSku } from "./other_medicine_sku";

export const Main = (props) => {

    const [storehouses, setStorehouses] = useSafeState([]);
    const auth = useAuth();
    const page = usePage();
    const envId = auth.env ? auth.env.id : null;
    const projectId = auth.project.id;
    const customerId = auth.customerId;

    const { runAsync: run_userStoreHouses } = useFetch(userStoreHouses, { manual: true })

    const tabChange = () => {
        page.setCurrentPage(1);
    };

    React.useEffect(() => {
        run_userStoreHouses({
            customerId: customerId,
            projectId: projectId,
            envId: envId,
            roleId: auth.project.permissions.role_id
        }).then((response) => {
            let list = [];
            if (response) {
                let data = response.data
                if (data != null) {
                    data.forEach(item => {
                        list.push({ id: item.id, name: item.name })
                    });
                }
            }
            setStorehouses(list);
        });
    }, []);

    return (


        <Tabs tabBarStyle={{ height: "32px" }} size="small" onChange={tabChange} defaultActiveKey="1" tabPosition="top" destroyInactiveTabPane={true}>
            {
                permissions(auth.project.permissions, "operation.supply.storehouse.medicine.summary") ?
                    <Tabs.TabPane tab={<FormattedMessage id="projects.storehouse.statistics.summary" />} key="1">
                        <Summary storehouses={storehouses} />
                    </Tabs.TabPane>
                    :
                    null
            }
            {
                permissions(auth.project.permissions, "operation.supply.storehouse.medicine.singe") ?
                    <Tabs.TabPane tab={<FormattedMessage id="projects.storehouse.statistics.sku" />} key="2">
                        <Sku storehouses={storehouses} />
                    </Tabs.TabPane>
                    :
                    null
            }
            {
                permissions(auth.project.permissions, "operation.supply.storehouse.no_number.view") ?
                    <Tabs.TabPane tab={<FormattedMessage id="projects.storehouse.statistics.other.sku" />} key="3">
                        <OtherMedicineSku storehouses={storehouses} />
                    </Tabs.TabPane>
                    :
                    null
            }
        </Tabs>


    )
};
