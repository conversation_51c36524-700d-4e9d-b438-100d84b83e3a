import {useSafeState} from "ahooks";
import {useSubject} from "./context";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {getFormulaMedicine} from "../../../../api/dispensing";
import {Button, Col, Divider, Form, InputNumber, Row, Select, Tooltip} from "antd";
import {CloseCircleFilled, PlusOutlined} from "@ant-design/icons";
import React, {useEffect} from "react";
import styled from "@emotion/styled";
import {atuFormula, getFormulaMedicines, showDrugDTPend, updateFormValue} from "./get_formula_medicine";
import {permissions} from "../../../../tools/permission";
import {DTPOption, DTPOptionIntl} from "../../../../data/data";
import {useAtom} from "jotai/index";
import {doseSelectAtom, visitOpenAtom} from "./ctx";

interface DrugLabelInterface {
    drugNameOption: any,
    drugOption: any,
    form: any,
    drugLabelFrom:any
    setDrugLabelFrom:any
    record:any
    visit_id:any,
    drugNameFrom:any
    setDrugOption:any
}
export const DrugLabel = (props: DrugLabelInterface) => {
    const {drugNameOption, drugOption, form, drugLabelFrom, setDrugLabelFrom, record, visit_id, drugNameFrom, setDrugOption} = props
    const [drugSelect, setDrugSelect] = useSafeState<any>({});
    const [itemLen, setItemLen] = useSafeState<any>(false);

    const subjectCtx = useSubject();
    const intl = useTranslation();
    const {formatMessage} = intl;
    const auth = useAuth();
    const envId = auth.env.id;
    const [updateCount, setUpdateCount] = useSafeState<boolean>(permissions(auth.project.permissions, "operation.subject.medicine.formula.update"))

    const {runAsync: getFormulaMedicineRun, loading: getFormulaMedicineLoading} = useFetch(getFormulaMedicine, {manual: true, debounceWait: 500})

    const get_formula_medicine = (index:number, formKey:any, value:any) => {
        setUpdateCount(!permissions(auth.project.permissions, "operation.subject.medicine.formula.update"))
        getFormulaMedicines(index,formKey,value,form,getFormulaMedicineRun,drugOption, drugNameOption,visit_id, record, subjectCtx, record.cohortId, envId, drugLabelFrom, drugNameFrom)
        props.form.validateFields(["drugLabel"]).then()

    }

    // 设置研究产品标签选项
    const updateDrugLabelForm = (index:any, value:any, childIndex:any) => {
        let updateSet = false
        let updateIndex = -1
        let chlidIndex = -1
        for (let i = 0; i < form.getFieldsValue().drugLabel[index].labels?.length; i++) {
            if (form.getFieldsValue().drugLabel[index].labels[i]?.label !== undefined){
                updateSet = true
                updateIndex = index
                chlidIndex = i
                break
            }
        }
        if (updateSet){
            // 组间内 已经选了一个标签
            drugSelect[updateIndex] = drugOption.filter((item:any)=>
                item.id === drugOption[form.getFieldsValue().drugLabel[updateIndex].labels[chlidIndex].label].id
            )
            setDrugSelect(drugSelect)
            drugLabelFrom[index] = drugOption[form.getFieldsValue().drugLabel[updateIndex].labels[chlidIndex].label]?.customerCalculation
            updateFormValue(drugOption[form.getFieldsValue().drugLabel[updateIndex].labels[chlidIndex].label].customerCalculation, form, index, 1)

            atuFormula(index, getFormulaMedicineRun, envId, record.cohortId, record, visit_id, drugOption[value].id,subjectCtx, 1, form, drugOption, drugNameOption)
            //

        }else {
            // 一个标签都没选择
            let drugLabel:any[] = []
            form.getFieldsValue().drugLabel?.forEach(
                (item:any, i:any) => {
                    if (i === index){
                        item.form = {}
                    }
                    drugLabel.push(item)
                }
            )
            delete subjectCtx.formulaLabelRes[index]
            subjectCtx.setFormulaLabelRes({...subjectCtx.formulaLabelRes})
            delete drugSelect[index];
            setDrugSelect({...drugSelect})
            delete drugLabelFrom[index]
            // setDrugLabelFrom({...drugLabelFrom})
        }
    }

    const [dose, setDose ] = useAtom(doseSelectAtom);

    const setDrugSelectFun = () => {
        //  剂量控制 需去掉多余选项
        if (form.getFieldsValue()?.drugLabel){
            let newSelect :any = {}
            let dSelect :any = {}
            let drugLabels = [...form.getFieldsValue()?.drugLabel];
            drugLabels.forEach((it:any, number:any)=>{
                it.labels.forEach((label:any,childIndex:any) => {
                    if (label.label || label.label === 0) {
                        newSelect[number+""+childIndex] = drugOption.find((item:any)=>
                            item.index === label.label
                        )
                        dSelect[number] = drugOption.filter((item:any)=>
                            item.id === drugOption[form.getFieldsValue().drugLabel[number].labels[childIndex].label].id
                        )
                    }
                })

            })

            setDrugSelect({...dSelect})
            subjectCtx.setDrugSelect({...newSelect})
        }
    }


    useEffect(() => {
        if (subjectCtx.updateAdd !== 0 ){
            setDrugSelectFun()
        }
    },[subjectCtx.updateAdd])

    const changeLabel = (value: any, index: any, childIndex:any) => {
        if (value !== undefined) {
            let drug: any = {...subjectCtx.drugSelect, [index + "" + childIndex]: drugOption[value]}
            let drugLabels = [...form.getFieldsValue()?.drugLabel];
            delete drugLabels[index].labels[childIndex].count
            delete drugLabels[index].labels[childIndex].level
            delete drugLabels[index].labels[childIndex].dtp
            if (drugOption[value]?.dtp && drugOption[value]?.dtp?.length === 1) {
                drugLabels[index].labels[childIndex].dtp = drugOption[value].dtp[0]
            }
            form.setFieldsValue({...form.getFieldsValue(), drugLabel: drugLabels})
            subjectCtx.setDrugSelect(drug)
        } else {
            delete subjectCtx.drugSelect[index + "" + childIndex];
            subjectCtx.setDrugSelect({...subjectCtx.drugSelect})
        }
        updateDrugLabelForm(index, value, childIndex)
    }
    const childAddInit = (add:any, name:any) => {
        add()
        const childField = form.getFieldsValue().drugLabel[name]?.labels?.length - 1
        delete subjectCtx.drugSelect[name+""+childField]
    }

    const addInit = (add:any) => {
        setItemLen(form.getFieldsValue().drugLabel?.length === 1)

        add()
        let data :any = form.getFieldsValue().drugLabel?.slice(0,-1)
        form.setFieldsValue({...form.getFieldsValue, "drugLabel": [...data,{labels:[{}]}]})
        delete drugSelect[form.getFieldsValue().drugLabel.length];
        setDrugSelect({...drugSelect})
    }

    const removeInit = (index:any, remove:any) => {
        setItemLen(form.getFieldsValue().drugLabel?.length === 1)
        remove(index)
        if (form.getFieldsValue()?.drugLabel){
            let newSelect :any = {}
            let dSelect :any = {}
            let drugLabels = [...form.getFieldsValue()?.drugLabel];
            drugLabels.forEach((it:any, number:any)=>{
                it.labels.forEach((label:any,childIndex:any) => {
                    if (label?.label || label?.label === 0) {
                        newSelect[number+""+childIndex] = drugOption.find((item:any)=>
                            item.index === label.label
                        )
                        dSelect[number] = drugOption.filter((item:any)=>
                            item.id === drugOption[form.getFieldsValue().drugLabel[number].labels[childIndex].label].id
                        )

                    }
                })
            })
            setDrugSelect({...dSelect})
            subjectCtx.setDrugSelect({...newSelect})
        }

        let objectKey = Object.keys(subjectCtx.formulaLabelRes)
        objectKey.filter(i => i !== index+"").forEach((item:any,i)=>{
            subjectCtx.formulaLabelRes[i] = subjectCtx.formulaLabelRes[item]
        })
        delete subjectCtx.formulaLabelRes[objectKey.length - 1]
        subjectCtx.setFormulaLabelRes({...subjectCtx.formulaLabelRes})

        if (Array.isArray(drugLabelFrom)) {
            setDrugLabelFrom(drugLabelFrom?.filter((item:any, i:any)=> index !== i))
        }else{
            let drugNameFromKey = Object.keys(drugLabelFrom)
            drugNameFromKey.filter(i => i !== index+"").forEach((item:any,i)=>{
                drugLabelFrom[i] = drugLabelFrom[item]
            })
            delete drugLabelFrom[objectKey.length - 1]
            setDrugLabelFrom(drugLabelFrom)
        }
        setNumberSpec()
    }
    const setNumberSpec = () => {
        form.getFieldsValue().drugLabel.forEach(
            (labels:any, index:any) =>{
                labels?.labels?.forEach(
                    (item:any, childIndex:any)=>{
                        if (item?.label || item?.label === 0){
                            subjectCtx.drugSelect[index+""+childIndex] = drugOption[item.label]
                        }else{
                            subjectCtx.drugSelect[index+""+childIndex] = {}
                        }
                    }
                )
            }
        )
        subjectCtx.setDrugSelect({...subjectCtx.drugSelect})
    }

    const removeChildInit = (index:any, childIndex:any, remove:any) => {
        remove(childIndex)
        setNumberSpec()
        props.form.validateFields(["drugLabel"]).then()

    }

    const selectLen = () => {
        let length = 0;
        if (form.getFieldsValue()?.drugLabel){
            [...form.getFieldsValue()?.drugLabel]?.map((drug:any)=>
                drug.labels?.map((item:any)=> {
                    length ++
                })
            )
        }


        return length
    }

    return  <Form.Item label={formatMessage({id: "subject.dispensing.label.dispensing",})} className="full-width" style={{marginTop:12}}>
        <Form.List name={"drugLabel"} rules={[ValidatorList(props, 2, props.form, drugOption)]}>
            {(fields, {add, remove}, {errors}) => (
                <>
                    {fields.map((field, index) => (
                        <CustomCol key={field.key}>
                            {
                                subjectCtx.formulaForm
                                    .filter((item:any) => {
                                        return  drugLabelFrom[field.name]?.includes("{"+item.variable+"}")
                                    })
                                    .map(
                                        (item:any) =>
                                            <CustomerForm item={item} field={field} get_formula_medicine={get_formula_medicine}/>
                                    )
                            }
                            {

                                subjectCtx.formulaLabelRes[index]?.map(
                                    (item:any)=>
                                        !item?.customerFormula?
                                            null
                                            :
                                        item?.name?.find((it:any) => it.outSize === true) ?
                                            <div

                                                style={{
                                                    borderRadius: '2px 2px 2px 2px',
                                                    backgroundColor:"#fff7e6",
                                                    padding: '8px',
                                                    height: "auto",
                                                    marginLeft:subjectCtx.labelWidth,
                                                    marginTop: "12px",
                                                    marginBottom: "12px",
                                                    alignItems: 'center',
                                                    opacity: "0.1px",
                                                    color: "#677283",
                                                }}
                                            >计算错误，计算值无法匹配发放数量。</div>
                                            :
                                        item?.customerFormula !== 0 &&
                                        <div
                                            style={{
                                                borderRadius: '2px 2px 2px 2px',
                                                backgroundColor:"#fff7e6",
                                                padding: '8px',
                                                height: "auto",
                                                marginLeft:subjectCtx.labelWidth,
                                                marginTop: "12px",
                                                marginBottom: "12px",
                                                alignItems: 'center',
                                                opacity: "0.1px",
                                                color: "#677283",
                                            }}
                                        >
                                        <span >
                                            <svg className="iconfont" width={16} height={16} style={{marginBottom:"-4px",}}>
                                                    <use xlinkHref="#icon-jinggao"></use>
                                                </svg>
                                        </span>
                                            <span style={{
                                                fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                fontSize: "12px",
                                                fontWeight: 400,
                                                color: "#677283",
                                            }}>{formatMessage({id: "subject.dispensing.drug.formula.tip.start"})}</span>
                                            <span style={{
                                                fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                fontSize: "12px",
                                            }}>{item?.customerFormula}</span>

                                            <span style={{
                                                fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                fontSize: "12px",
                                                fontWeight: 400,
                                                color: "#677283",
                                            }}>{formatMessage({id: "subject.dispensing.drug.customer.formula.tip", },{ unit:item?.unit, spec:item?.spec.join(",")})}</span>
                                        </div>
                                )
                            }

                            <Form.Item name={[field.name, "labels"]}
                                       label={formatMessage({id:"subject.dispensing.drugLabel"})}
                                       style={{marginTop:12}}
                                       labelCol={{style:{ whiteSpace: "pre-wrap",width:subjectCtx.labelWidth, textAlign:"right", height:"auto"}}}
                            >
                                <Form.List name={[field.name, "labels"]}>
                                    {(childFields, {add:childAdd, remove:childRemove}, {errors}) => (
                                        <>
                                            {childFields?.map((childField:any, childIndex) => (
                                                <Row>
                                                    <Col style={{flex: 1}}>
                                                        <Row key={childField.key}
                                                             style={{marginBottom:12}}
                                                        >
                                                            <Form.Item

                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: formatMessage({
                                                                            id: "shipment.order.create.info",
                                                                        }),
                                                                    },
                                                                ]}
                                                                {...childField}
                                                                labelCol={{style:{ whiteSpace: "pre-wrap",width:subjectCtx.labelWidth, textAlign:"right", height:"auto"}}}

                                                                name={[childField.name, "label"]}
                                                                style={{flex: 1, }}
                                                            >
                                                                <Select
                                                                    placeholder={formatMessage({
                                                                        id: "placeholder.select.common",
                                                                    })}
                                                                    className="full-width"
                                                                    onChange={(value: any) => {
                                                                        changeLabel(value, field.name, childField.name)
                                                                        props.form.validateFields(["drugLabel"]).then()
                                                                    }}
                                                                    allowClear
                                                                    disabled={!dose}
                                                                >
                                                                    {
                                                                        drugSelect[index]?
                                                                            drugSelect[index].map((it: any) => (
                                                                                <Select.Option
                                                                                    disabled = {
                                                                                        form.getFieldsValue()?.drugLabel && [...form.getFieldsValue()?.drugLabel]?.find((drug:any)=>
                                                                                            drug.labels?.find((item:any)=> item?.label === it.index)
                                                                                        )
                                                                                    }
                                                                                    key={it.index}
                                                                                    value={it.index}
                                                                                >
                                                                                    {it.name}
                                                                                </Select.Option>
                                                                            ))
                                                                            :
                                                                            drugOption?.map((it: any) => (
                                                                                <Select.Option
                                                                                    disabled = {
                                                                                        form.getFieldsValue()?.drugLabel && [...form.getFieldsValue()?.drugLabel]?.find((drug:any)=>
                                                                                        drug?.labels?.find((item:any)=> item?.label === it.index)
                                                                                        )
                                                                                    }
                                                                                    key={it.index}
                                                                                    value={it.index}
                                                                                >
                                                                                    {it.name}
                                                                                </Select.Option>
                                                                            ))
                                                                    }
                                                                </Select>
                                                            </Form.Item>
                                                            {
                                                                subjectCtx.drugSelect[field.name+""+childField.name]?.values &&
                                                                <Form.Item
                                                                    style={{flex: 1, marginLeft:12}}
                                                                    {...childField}
                                                                    name={[childField.name, "count"]}
                                                                    rules={[
                                                                        {
                                                                            // required: !subjectCtx.formulaLabelRes[index]?.find(
                                                                            //     (item:any)=>
                                                                            //         item?.name?.find((it:any) => it.outSize === true)),
                                                                            required: true,
                                                                            message: formatMessage({id: "common.required.prefix",}),
                                                                        },
                                                                        {
                                                                            validator: (rule: any, value: any, callback: any) => {
                                                                                const reg = /^(\d+|(\d+~\d+))(,(\d+|(\d+~\d+)))*$/;
                                                                                if ((value || value === 0) && !subjectCtx.drugSelect[field.name+""+childField.name]?.values.custom_dispensing_number.find((item:any) => value === item)) {
                                                                                    return Promise.reject(
                                                                                        formatMessage({id: "subject.dispensing.drug.input.error"})
                                                                                    );
                                                                                }
                                                                                return Promise.resolve();
                                                                            },
                                                                        },
                                                                    ]}
                                                                >
                                                                    <InputNumber

                                                                        disabled={
                                                                            subjectCtx.drugSelect[field.name+""+childField.name]?.values?.automaticRecode
                                                                            &&
                                                                            !(
                                                                                subjectCtx.drugSelect[field.name+""+childField.name]?.values?.automaticRecode
                                                                                &&
                                                                                permissions(auth.project.permissions, "operation.subject.medicine.formula.update")
                                                                            )
                                                                            ||
                                                                            subjectCtx.formulaLabelRes[index]?.find(
                                                                                (item:any)=>
                                                                                    item?.name?.find((it:any) => item?.customerFormula && it.outSize === true && it.name === subjectCtx.drugSelect[field.name+""+childField.name]?.values.label))
                                                                        }
                                                                        // disabled={subjectCtx.drugSelect[field.name+""+childField.name]?.values?.automatic_recode}
                                                                        controls={false}
                                                                        className="full-width"
                                                                        addonAfter={subjectCtx.drugSelect[field.name+""+childField.name]?.values?.spec}
                                                                        placeholder={formatMessage({
                                                                            id: "subject.dispensing.placeholder.input.dispensing.count",
                                                                        })}
                                                                    ></InputNumber>
                                                                </Form.Item>
                                                            }

                                                        </Row>
                                                        {
                                                            record?.attribute?.info?.dtpRule === 1 && subjectCtx.drugSelect[field.name+""+childField.name]?.dtp &&
                                                            <Row style={{
                                                                display:

                                                                    showDrugDTPend(subjectCtx, field.name, childFields, form) < 0
                                                                        ?
                                                                        "initial":
                                                                        "none"


                                                            }}>

                                                            <Form.Item
                                                                style={{flex: 1, paddingBottom:12}}
                                                                {...childField}
                                                                name={[childField.name, "dtp"]}
                                                                rules={[{
                                                                    required: true,
                                                                    message: formatMessage({
                                                                        id: "drug.batch.treatmentDesign.openSetting",
                                                                    }),
                                                                },]}
                                                            >
                                                                    {
                                                                        <Select optionLabelProp="label" placeholder={formatMessage({id:"logistics.dispensing.method"})} disabled={subjectCtx.drugSelect[field.name+""+childField.name]?.dtp?.length === 1}>
                                                                            {
                                                                                subjectCtx.drugSelect[field.name+""+childField.name]?.dtp?.map((it:any)=>
                                                                                    GetDtpOption(it, formatMessage)

                                                                                )
                                                                            }
                                                                        </Select>
                                                                    }
                                                            </Form.Item>
                                                            </Row>


                                                        }

                                                    </Col>
                                                    <Col style={{ marginLeft: '6px', display: 'flex', alignItems: 'center', height:record?.attribute?.info?.dtpRule === 1 && subjectCtx.drugSelect[field.name+""+childField.name]?.dtp && showDrugDTPend(subjectCtx, field.name, childFields, form) < 0 && showDrugDTPend(subjectCtx, field.name, childFields, form) !== -3? 80:36}}>
                                                        {   dose && drugOption.length > selectLen() &&
                                                            <>
                                                            {
                                                                record?.attribute?.info?.dtpRule === 1 && subjectCtx.drugSelect[field.name+""+childField.name]?.dtp && showDrugDTPend(subjectCtx, field.name, childFields, form) < 0 && showDrugDTPend(subjectCtx, field.name, childFields, form) !== -3?
                                                                    <Divider type={"vertical"}style={{paddingRight:0,  borderLeft: "1px solid #E0E1E2", height:"90%"}} />
                                                                    :
                                                                    <Divider type={"vertical"}style={{paddingRight:0,  borderLeft: "0px solid #E0E1E2", height:48}} />

                                                            }

                                                                <Tooltip placement="top" title={formatMessage({id: "common.add"})}>
                                                                    <svg className="iconfont" width={16} height={16} onClick={() => {
                                                                        childAddInit(childAdd, field.name)
                                                                    }}
                                                                         style={{ cursor: "pointer"}}
                                                                         fill={"#999999"}
                                                                    >
                                                                        <use xlinkHref="#icon-zengjia"></use>
                                                                    </svg>
                                                                </Tooltip>
                                                            </>

                                                        }
                                                        {
                                                            childFields.length !== 1 &&
                                                            <Tooltip placement="top" title={formatMessage({id: "common.delete"})}>
                                                                <svg className="iconfont" width={16} height={16} onClick={() => {
                                                                    removeChildInit(field.name,childField.name, childRemove)
                                                                }}
                                                                     style={{marginLeft: 12, cursor: "pointer"}}
                                                                     fill={"#999999"}>
                                                                    <use xlinkHref="#icon-shanchu"></use>
                                                                </svg>
                                                            </Tooltip>
                                                        }

                                                    </Col>

                                                </Row>

                                            ))}
                                            {
                                                record?.attribute?.info?.dtpRule === 1 && showDrugDTPend(subjectCtx, field.name, childFields,form) > -1 &&
                                                <Row >

                                                    <Form.Item
                                                        style={{marginRight:
                                                                (drugOption.length > selectLen() && childFields.length !== 1)?
                                                                    68
                                                                    :
                                                                    (childFields.length === 1 && !(drugOption.length > selectLen()))
                                                                        ?
                                                                        12
                                                                        :
                                                                        36

                                                    }}
                                                        className={"full-width"}
                                                    >
                                                        {
                                                            <Select placeholder={formatMessage({id: "logistics.dispensing.method"})} optionLabelProp="label" disabled={true} value={showDrugDTPend(subjectCtx, field.name, childFields, form)}>
                                                                {
                                                                    DTPOption?.map((it:any)=>
                                                                        GetDtpOption(it.value, formatMessage)
                                                                    )
                                                                }
                                                            </Select>
                                                        }
                                                    </Form.Item>
                                                </Row>


                                            }
                                        </>
                                    )}
                                </Form.List>
                            </Form.Item>
                            {
                                // dose &&
                                <CloseCircleFilled
                                    className="delete-icon"
                                    style={{color: "#fe5b5a"}}
                                    onClick={() =>
                                        removeInit(field.name, remove)

                                    }
                                />
                            }

                        </CustomCol>
                    ))}

                            {
                                (subjectCtx.labelAdd || itemLen) && dose &&
                                drugOption.length > selectLen()
                                &&
                                <Form.Item style={{marginBottom: 0}} className={"full-width"}>
                                <Button
                                    type="dashed"
                                    onClick={() => addInit(add)}
                                    block
                                    icon={<PlusOutlined/>}
                                >
                                    <FormattedMessage id={"common.addTo"}/>
                                </Button>
                                </Form.Item>

                            }
                            <Form.ErrorList errors={errors}/>

                </>
            )}
        </Form.List>
    </Form.Item>
}



export const ValidatorList = (props: any, type: number, form:any, drugOption:any) => {
    // type === 1  研究产品名称
    // type === 2  研究产品标签
    const intl = useTranslation();
    const subjectCtx = useSubject();
    const errorInfo = type === 1 ? "subject.dispensing.selectDrugNumber" : "subject.dispensing.selectDrugNumber.label"
    const repeatedErrorInfo = type === 1 ? "subject.dispensing.selectedRepeatedly" : "subject.dispensing.label.selectedRepeatedly"
    const {formatMessage} = intl;
    return {
        validator: async (_: any, names: any[]) => {

            if (!(subjectCtx.formulaMedicine &&
                subjectCtx.formulaMedicine?.length > 0) && form.getFieldsValue().drugLabel?.length === 0 && form.getFieldsValue().openSetting?.length === 0){
                return Promise.reject(
                    new Error(
                        formatMessage({
                            id: errorInfo,
                        })
                    )
                );
            }

            if (
                // 补发校验
                props.type === 2 &&
                (!names || names?.length === 0)
            ) {
                return Promise.reject(
                    new Error(
                        formatMessage({
                            id: errorInfo,
                        })
                    )
                );
            }

            if (props.type === 2) {
                // if (form.getFieldsValue().drugLabel)
            }
            let repeated: boolean = true;
            let nameHash: any = {};
            let typeRepeated: boolean = true;
            let typeRepeatedHash: any = {};
            // 组合标签和对应组合标签下的子标签不可以重复
            let repeatedId: boolean = true;
            let nameHashId: any = {};
            names?.forEach(
                (it:any)=> [
                    it?.labels?.forEach((label:any)=>{
                        if (label?.label || label?.label === 0){
                            if (nameHash[label.label]) {
                                repeated = false;
                                return;
                            }
                            nameHash[label.label] = true;
                            // 盲态的组合标签，限制多选一发放；开放的，也限制多选一
                            let info :any = drugOption.find((item:any)=>label.label === item.index)


                            if (info?.openSetting){
                                if (typeRepeatedHash[info.openSetting]){
                                    typeRepeated = false;
                                    return;
                                }
                                typeRepeatedHash[info.openSetting] = true

                                // 组合标签和对应组合标签下的
                            }

                            let openSettingType :any = info?.openSetting? info?.openSetting : 3
                            if (nameHashId[info.id] && nameHashId[info.id] !== openSettingType){
                                typeRepeated = false;
                                return;
                            }
                            nameHashId[info.id] = openSettingType


                        }
                    })
                ]
            )
            names?.forEach((item: any) => {
                if (type !== 2) {
                    item?.labels?.forEach(
                        (it:any)=>{
                            if (it?.name || it?.name === 0) {
                                if (nameHash[it.name]) {
                                    repeated = false;
                                    return;
                                }
                                nameHash[it.name] = true;
                            }
                        }
                    )
                }

            });
            if (!typeRepeated) {
                return Promise.reject(
                    new Error(
                        formatMessage({
                            id: "subject.dispensing.label.typeSelectedRepeatedly",
                        })
                    )
                );
            }
            if (!repeated) {
                return Promise.reject(
                    new Error(
                        formatMessage({
                            id: repeatedErrorInfo,
                        })
                    )
                );
            }
        },
    }
}


export const CustomCol = styled(Col)`
  position: relative;
  background: #f8f9fa;
  padding: 16px 16px 8px 16px;
  margin-bottom: 16px;
  .ant-form-item {
    margin-bottom: 0;
  }

  .delete-icon {
    position: absolute;
    right: -3px;
    top: -3px;
  }
`;

interface CustomerFormInterface {
    item:any
    field:any
    get_formula_medicine:any

}

export const CustomerForm = (props:CustomerFormInterface) => {
    const {item, field, get_formula_medicine} = props
    const intl = useTranslation();
    const subjectCtx = useSubject();
    const {formatMessage} = intl;
    return  <Form.Item
        style={{marginBottom:12}}
        labelCol={{style:{ whiteSpace: "pre-wrap",width:subjectCtx.labelWidth, textAlign:"right", height:"auto"}}}
        rules={[
            { required: item.required },
            {
                validator: (_, value) => {
                    if(item.type !== undefined && item.type === "inputNumber"){
                        if (value !== null && value != undefined) {
                            if(item.range && item.range.min && item.range.min > value ){
                                return Promise.reject(new Error(formatMessage({id: "form.control.type.variableRange.validate.range"})));
                            }
                            if(item.range && item.range.max && item.range.max < value ){
                                return Promise.reject(new Error(formatMessage({id: "form.control.type.variableRange.validate.range"})));
                            }
                            if(item.formatType !== undefined && item.formatType === "decimalLength"){
                                if (item.length) {
                                    // 将数字转换为字符串
                                    var str = item.length.toString();
                                    // 使用split()方法将整数部分和小数部分分开
                                    var parts = str.split('.');
                                    //取整数部分
                                    let numberPart = parts[0];
                                    var valueDecimalPart = value.toString().split('.');
                                    if (valueDecimalPart && valueDecimalPart[0] && valueDecimalPart[0].length > numberPart) {
                                        return Promise.reject(new Error(formatMessage({id: "form.control.type.variableRange.validate.range"})));

                                    }
                                    if (valueDecimalPart && valueDecimalPart[1] && parts[1] && valueDecimalPart[1].length > parts[1]) {
                                        return Promise.reject(new Error(formatMessage( {id: "form.control.type.variableRange.validate.range"})));

                                    }
                                }
                            }else if(item.formatType !== undefined && item.formatType === "numberLength"){
                                if (
                                    item.length &&
                                    value.toString()
                                        .length >
                                    item.length
                                ) {
                                    return Promise.reject(new Error(formatMessage({id: "form.control.type.variableRange.validate.range"})));

                                }
                            }
                        }
                        return Promise.resolve();
                    }else{
                        if (
                            item.length &&
                            value != null &&
                            value.toString()
                                .length >
                            item.length
                        ) {
                            return Promise.reject(new Error(formatMessage({id: "form.control.type.variableRange.validate.range"})));
                        }
                        return Promise.resolve();
                    }
                },
            },
        ]}
        label={item.label} name={[field.name,"form", item.variable]}>
        <InputNumber
            placeholder={formatMessage({id: 'placeholder.input.common'})}
            precision={item.formatType==="numberLength"?0:undefined}
            step={(item.formatType !== undefined && item.formatType === "numberLength")?1:0.1}
            onChange={(value) => {
                get_formula_medicine(field.name,item.variable, value)
            }}
            className={"full-width"}
        />
    </Form.Item>
}


export const GetDtpOption = (value: any, formatMessage:any) => {
    const option = DTPOptionIntl(formatMessage).find((item:any) => item.value === value)
    const label = <div style={{display: 'flex'}}>
        <span style={{fontSize: '14px', color: 'rgba(46, 50, 58, 1)'}}>{option?.label}</span>
        <span>&nbsp;</span>
        <span style={{fontSize: '12px', color: 'rgba(103, 114, 131, 1)'}}>{option?.desc?.replace("。", "")?.replace(".", "")}</span>
    </div>
    return <Select.Option value={value} label={label}>
        <div style={{display: 'flex', flexDirection: 'column'}}>
            <span style={{fontSize: '14px', color: 'rgba(46, 50, 58, 1)'}}>{option?.label}</span>
            <span style={{fontSize: '12px', color: 'rgba(147, 150, 155, 1)'}}>{option?.desc}</span>
        </div>
    </Select.Option>
}
