import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const SubjectContext = React.createContext<{

        visible: boolean;
        setVisible: (data: boolean) => void;
        showData: any;
        setShowData: (data: any) => void;
        showTitle: any;
        setShowTitle: (data: any) => void;
        dispensingConfirmVisible: boolean;
        setDispensingConfirmVisible: (data: boolean) => void;
        unblindingApprovalVisible: boolean;
        setUnblindingApprovalVisible: (data: boolean) => void;
        pvUnblindingApprovalVisible: boolean;
        setPvUnblindingApprovalVisible: (data: boolean) => void;
        unblindingDetailsVisible: boolean;
        setUnblindingDetailsVisible: (data: boolean) => void;
        unblindingDetailsSign: string;
        setUnblindingDetailsSign: (data: string) => void;
        currentRecord: any;
        setCurrentRecord: (data: any) => void;
        refresh: number;
        setRefresh: (data: number) => void;
        componentRef: any;
        label: string,
        setLabel: (data: string) => void;
        selectOption: any,
        setSelectOption: (data: any) => void;
        cohortStatusModifyVisible:boolean;
        setCohortStatusModifyVisible:(data: any) => void;
        formulaData: any,
        setFormulaData: (data: any) => void;
        formulaMedicine: any,
        setFormulaMedicine: (data: any) => void;
        configData: any,
        setConfigData: (data: any) => void;
        refreshMedicine: any,
        setRefreshMedicine: (data: any) => void;
        formulaForm: any,
        setFormulaForm: (data: any) => void;
        formulaRes: any,
        setFormulaRes: (data: any) => void;
        labelWidth: any,
        setLabelWidth: (data: any) => void;
        drugSelect: any,
        setDrugSelect: (data: any) => void;
        nameSelect: any,
        setNameSelect: (data: any) => void;

        formulaLabelRes: any,
        setFormulaLabelRes: (data: any) => void;
        formulaNameRes: any,
        setFormulaNameRes: (data: any) => void;

        labelAdd: any,
        setLabelAdd: (data: any) => void;
        nameAdd: any,
        setNameAdd: (data: any) => void;
        listSiteId: any,
        setListSiteId: (data: any) => void;
        fieldSelect: any,
        setFieldSelect: (data: any) => void;
        joinTime: any,
        setJoinTime: (data: any) => void;
        inheritValue: any,
        setInheritValue: (data: any) => void;
        isPage: any,
        setIsPage: (data: any) => void;
        isLevel: any,
        setIsLevel: (data: any) => void;
        updateAdd: any,
        setUpdateAdd: (data: any) => void;
        drugOption: any,
        setDrugOption: (data: any) => void;
        drugNameOption: any,
        setDrugNameOption: (data: any) => void;
        cohortUpdate: any,
        setCohortUpdate: (data: any) => void;
        selectRecord: any,
        setSelectRecord: (data: any) => void;
        dispensingVisible: any,
        setDispensingVisible: (data: any) => void;

        isSelectDose: any,
        setIsSelectDose: (data: any) => void;
    }
    |
    null>(null);

export const SubjectProvider = ({children}: { children: ReactNode }) => {

    const [visible, setVisible] = useSafeState<boolean>(false);
    const [showData, setShowData] = useSafeState<any>([]);
    // const [siteTimeZone, setSiteTimeZone] = useSafeState<any>(null);
    const [showTitle, setShowTitle] = useSafeState<any>(null);
    const [dispensingConfirmVisible, setDispensingConfirmVisible] = useSafeState<boolean>(false);
    const [unblindingApprovalVisible, setUnblindingApprovalVisible] = useSafeState(false)
    const [pvUnblindingApprovalVisible, setPvUnblindingApprovalVisible] = useSafeState(false)
    const [unblindingDetailsVisible, setUnblindingDetailsVisible] = useSafeState(false)
    const [unblindingDetailsSign, setUnblindingDetailsSign] = useSafeState("")
    const [currentRecord, setCurrentRecord] = useSafeState(null)
    const [refresh, setRefresh] = useSafeState<number>(0);
    const [label, setLabel] = useSafeState<any>("");
    const [selectOption, setSelectOption] = useSafeState<any>({});
    const [cohortStatusModifyVisible, setCohortStatusModifyVisible] = useSafeState<any>(false);
    const [formulaData, setFormulaData] = useSafeState<any>(null)
    const [formulaMedicine, setFormulaMedicine] = useSafeState<any>([])
    const [formulaForm, setFormulaForm] = useSafeState<any>([])
    const [labelWidth, setLabelWidth] = useSafeState<any>([])

    const [configData, setConfigData] = useSafeState<any>({})
    const [refreshMedicine, setRefreshMedicine] = useSafeState<any>(0)
    const [formulaRes, setFormulaRes] = useSafeState<any>([])
    const [drugSelect, setDrugSelect] = useSafeState<any>({});
    const [listSiteId, setListSiteId] = useSafeState<any>(null);
    const [nameSelect, setNameSelect] = useSafeState<any>({});

    const [formulaLabelRes, setFormulaLabelRes] = useSafeState<any>({})
    const [formulaNameRes, setFormulaNameRes] = useSafeState<any>({})
    const [labelAdd, setLabelAdd] = useSafeState<any>(false)
    const [nameAdd, setNameAdd] = useSafeState<any>(false)
    const [fieldSelect, setFieldSelect] = useSafeState<any>(undefined)
    const [joinTime, setJoinTime] = useSafeState<any>(undefined)
    const [inheritValue, setInheritValue] = useSafeState<any>(false)
    const [isPage, setIsPage] = useSafeState<any>(false)
    const [isLevel, setIsLevel] = useSafeState<any>(false)
    const [updateAdd, setUpdateAdd] = useSafeState<any>(0);


    const [drugOption, setDrugOption] = useSafeState<any>([]);
    const [drugNameOption, setDrugNameOption] = useSafeState<any>([]);

    const [cohortUpdate, setCohortUpdate] = useSafeState<any>(0);
    const [selectRecord, setSelectRecord] = useSafeState<any>(null);
    const [dispensingVisible, setDispensingVisible] = useSafeState<any>(false);
    const [isSelectDose, setIsSelectDose] = useSafeState<any>(false);

    let componentRef: any = React.useRef();
    return (
        <SubjectContext.Provider
            value={
                {
                    visible, setVisible,
                    showData, setShowData,
                    // siteTimeZone, setSiteTimeZone,
                    showTitle, setShowTitle,
                    dispensingConfirmVisible, setDispensingConfirmVisible,
                    unblindingApprovalVisible, setUnblindingApprovalVisible,
                    pvUnblindingApprovalVisible, setPvUnblindingApprovalVisible,
                    unblindingDetailsVisible, setUnblindingDetailsVisible,
                    unblindingDetailsSign, setUnblindingDetailsSign,
                    currentRecord, setCurrentRecord,
                    refresh, setRefresh,
                    label,setLabel,
                    selectOption, setSelectOption,
                    cohortStatusModifyVisible,setCohortStatusModifyVisible,
                    componentRef,
                    formulaData, setFormulaData,
                    formulaMedicine, setFormulaMedicine,
                    configData, setConfigData,
                    refreshMedicine, setRefreshMedicine,
                    formulaForm, setFormulaForm,
                    formulaRes, setFormulaRes,
                    labelWidth, setLabelWidth,
                    drugSelect, setDrugSelect,
                    listSiteId,setListSiteId,
                    nameSelect, setNameSelect,
                    formulaLabelRes, setFormulaLabelRes,
                    formulaNameRes, setFormulaNameRes,
                    labelAdd, setLabelAdd,
                    nameAdd, setNameAdd,
                    fieldSelect, setFieldSelect,
                    joinTime, setJoinTime,
                    inheritValue, setInheritValue,
                    isPage, setIsPage,
                    isLevel, setIsLevel,
                    updateAdd, setUpdateAdd,
                    drugOption, setDrugOption,
                    drugNameOption, setDrugNameOption,
                    cohortUpdate, setCohortUpdate,
                    selectRecord, setSelectRecord,
                    dispensingVisible, setDispensingVisible,
                    isSelectDose, setIsSelectDose
                }
            }
        >
            {children}
        </SubjectContext.Provider>
    )
};

export const useSubject = () => {
    const context = React.useContext(SubjectContext);
    if (!context) {
        throw new Error("useNotice must be used in SubjectProvider");
    }
    return context;
};

