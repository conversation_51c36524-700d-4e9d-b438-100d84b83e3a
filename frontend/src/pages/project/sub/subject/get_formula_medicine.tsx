import {log} from "echarts/types/src/util/log";

export const getFormulaMedicines = (index: number, formKey: any, value: any,
                                    form: any, getFormulaMedicineRun: any, drugOption: any, drugNameOption: any,
                                    visit_id: any,
                                    record: any, subjectCtx: any,
                                    cohortId: any, envId: any,
                                    drugLabelFrom: any, drugNameFrom: any

) => {

    updateCommonForm(formKey, value, form, drugLabelFrom, drugNameFrom, index)

    let customForm: any = {}
    if (form.getFieldsValue().drugLabel && form.getFieldsValue().drugLabel[index]?.form) {
        Object.keys(form.getFieldsValue().drugLabel[index]?.form)?.forEach(
            (key: any) => {
                customForm[key] = form.getFieldsValue().drugLabel[index]?.form[key]
            }
        )
    }
    let labelOpts: any = {}
    let drugOpts: any = {}
    form.getFieldsValue()?.drugLabel?.forEach(
        (drugLabel: any, index: any) => {
            if (drugLabel.form) {
                Object.keys(drugLabel?.form)?.forEach(
                    (key: any) => {
                        customForm[key] = drugLabel?.form[key]
                    }
                )
            }

            for (let i = 0; i < drugLabel.labels?.length; i++) {
                if (drugLabel.labels[i]?.label || drugLabel.labels[i]?.label === 0) {
                    labelOpts[index] = drugOption[drugLabel.labels[i].label]
                }
            }
        }
    )
    form.getFieldsValue()?.openSetting?.forEach(
        (drug: any, index: any) => {
            if (drug.form) {
                Object.keys(drug?.form)?.forEach(
                    (key: any) => {
                        customForm[key] = drug?.form[key]
                    }
                )
            }

            for (let i = 0; i < drug.labels?.length; i++) {
                if (drug.labels[i]?.name || drug.labels[i]?.name === 0) {
                    drugOpts[index] = drugNameOption[drug.labels[i].name]
                }
            }
        }
    )



    getFormulaMedicineRun({
        ...{form: customForm},
        env_id: envId,
        cohort_id: cohortId,
        subject_id: record.id,
        visit_id,
    }).then(
        (value: any) => {
            if (value.data.customerFormulaMedicineRes) {
                setCustomFormulaNumber(form, drugOption,drugNameOption, value.data.customerFormulaMedicineRes, subjectCtx, null)
                Object.keys(labelOpts)?.forEach(
                    (opt: any) => {
                        subjectCtx.formulaLabelRes[opt] = value.data.customerFormulaMedicineRes.filter(
                            (item: any) => item.name.find((it: any) => it?.id === labelOpts[opt]?.id)
                        )
                    }
                )
                Object.keys(drugOpts)?.forEach(
                    (opt: any) => {
                        subjectCtx.formulaNameRes[opt] = value.data.customerFormulaMedicineRes.filter(
                            (item: any) => item.name.find((it: any) => it?.id === drugOpts[opt]?.id)
                        )
                    }
                )

                subjectCtx.setFormulaLabelRes({...subjectCtx.formulaLabelRes})
                subjectCtx.setFormulaNameRes({...subjectCtx.formulaNameRes})
            } else {
                delete subjectCtx.formulaLabelRes[index]
                subjectCtx.setFormulaLabelRes({...subjectCtx.formulaLabelRes})
            }
        }
    )
    return
}

const setCustomFormulaNumber = (form:any, drugOption:any,drugNameOption:any, data:any, subjectCtx:any, formulaType:any) => {
    let drugLabels: any[] = []
    let openSettings: any[] = []
    if (form.getFieldsValue()?.drugLabel) {
        drugLabels = [...form.getFieldsValue()?.drugLabel];
    }
    if (form.getFieldsValue()?.openSetting) {
        openSettings = [...form.getFieldsValue()?.openSetting];
    }
    if (formulaType === null || formulaType === 1) {
        drugLabels.forEach((item: any, index: any) => {
            item.labels?.forEach(
                (label:any, childIndex:any)=>{
                    let res :any = data.filter(
                        (item: any) => item.name.find((it: any) => it?.id === drugOption[label.label]?.id)
                    )
                    if (res?.length > 0){
                        let formulaCount :any = res[0].name?.find((it: any) => it.name === drugOption[label.label].name)
                        drugLabels[index].labels[childIndex].useFormula = res[0].useFormula
                        drugLabels[index].labels[childIndex].formulaCount = res[0].customerFormula
                        if (formulaCount?.number){
                            drugLabels[index].labels[childIndex].count = formulaCount?.number
                        }
                        if (!formulaCount?.number && drugOption[label.label]?.values?.automatic_recode){
                            drugLabels[index].labels[childIndex].count = null
                        }
                    }
                }
            )
        })
    }
    if (formulaType === null || formulaType === 2) {
        openSettings.forEach((item: any, index: any) => {
            item.labels?.forEach(
                (label:any, childIndex:any)=>{
                    let res :any = data.filter(
                        (item: any) => item.name.find((it: any) => it?.id === drugNameOption[label.name]?.id)
                    )
                    if (res?.length > 0){
                        let formulaCount :any = res[0].name?.find((it: any) => it.name === drugNameOption[label.name].name)
                        openSettings[index].labels[childIndex].useFormula = res[0].useFormula
                        openSettings[index].labels[childIndex].formulaCount = res[0].customerFormula
                        if (formulaCount?.number){
                            openSettings[index].labels[childIndex].count = formulaCount?.number
                        }
                        if (!formulaCount?.number && drugNameOption[label.name].automatic_recode){
                            openSettings[index].labels[childIndex].count = null
                        }
                    }
                }
            )
        })
    }



    form.setFieldsValue({...form.getFieldsValue(), drugLabel: drugLabels, openSetting: openSettings})
    form.validateFields(["drugLabel"]).then()
    form.validateFields(["openSetting"]).then()

}



export const atuFormula = (index:any,getFormulaMedicineRun:any, envId:any ,cohortId:any ,record:any,visit_id:any, optId:any, subjectCtx:any, type:any, form:any, drugOption:any, drugNameOption:any) => {

    let customForm:any = {}
    if (type === 1){
        form.getFieldsValue()?.drugLabel?.forEach(
            (drugLabel: any, index: any) => {
                if (drugLabel.form) {
                    Object.keys(drugLabel?.form)?.forEach(
                        (key: any) => {
                            customForm[key] = drugLabel?.form[key]
                        }
                    )
                }
            }
        )

    }else{
        form.getFieldsValue()?.openSetting?.forEach(
            (drug: any, index: any) => {
                if (drug.form) {
                    Object.keys(drug?.form)?.forEach(
                        (key: any) => {
                            customForm[key] = drug?.form[key]
                        }
                    )
                }
            }
        )
    }
    getFormulaMedicineRun({
        ...{form: customForm},
        env_id: envId,
        cohort_id: cohortId,
        subject_id: record.id,
        visit_id,
    }).then(
        (value: any) => {
            if (value.data.customerFormulaMedicineRes) {
                setCustomFormulaNumber(form, drugOption,drugNameOption, value.data.customerFormulaMedicineRes, subjectCtx, type)
                if (type === 1) {
                    subjectCtx.formulaLabelRes[index] = value.data.customerFormulaMedicineRes.filter(
                        (item: any) => item.name.find((it: any) => it?.id === optId)
                    )
                    subjectCtx.setFormulaLabelRes({...subjectCtx.formulaLabelRes})

                }else{
                    subjectCtx.formulaNameRes[index] = value.data.customerFormulaMedicineRes.filter(
                        (item: any) => item.name.find((it: any) => it?.id === optId)
                    )
                    subjectCtx.setFormulaNameRes({...subjectCtx.formulaNameRes})
                }

            } else {
                delete subjectCtx.formulaLabelRes[index]

                subjectCtx.setFormulaLabelRes({...subjectCtx.formulaLabelRes})
            }
        }
    )
}

export const updateCommonForm = (formKey: any, value: any, form: any, drugLabelFrom:any, drugNameFrom:any,index:any) => {


    let drugLabels: any[] = []
    let openSettings: any[] = []
    if (form.getFieldsValue()?.drugLabel){
        drugLabels = [...form.getFieldsValue()?.drugLabel];
    }
    if (form.getFieldsValue()?.openSetting){
        openSettings = [...form.getFieldsValue()?.openSetting];
    }

    drugLabels.forEach((item: any, index: any) => {
        if (drugLabelFrom[index]?.includes("{" + formKey + "}")){
            drugLabels[index].form = {... drugLabels[index].form,[formKey]:value}
        }


    })
    openSettings.forEach((item: any, index: any) => {
        if (drugNameFrom[index]?.includes("{" + formKey + "}")){
            openSettings[index].form = {...openSettings[index].form,[formKey]:value}
        }


    })
    form.setFieldsValue({...form.getFieldsValue(), drugLabel: drugLabels, openSetting: openSettings})
}

export const updateFormValue = (formula: any, form: any, index: any, typeForm: any) => {
    let drugLabels: any[] = []
    let openSettings: any[] = []
    if (form.getFieldsValue()?.drugLabel){
        drugLabels = [...form.getFieldsValue()?.drugLabel];
    }
    if (form.getFieldsValue()?.openSetting){
        openSettings = [...form.getFieldsValue()?.openSetting];
    }
    let formItem :any = {}
    drugLabels?.forEach(
        (labels: any) => {
            if (labels.form) {
                Object.keys(labels?.form)?.forEach(
                    (key: any) => {
                        if (formula?.includes("{" + key + "}")) {
                            formItem[key] = labels.form[key]
                        }
                    }
                )
            }
        })
    openSettings?.forEach(
        (labels: any) => {
            if (labels.form) {
                Object.keys(labels?.form)?.forEach(
                    (key: any) => {
                        if (formula?.includes("{" + key + "}")) {
                            formItem[key] = labels.form[key]

                        }
                    }
                )
            }
        })
    if (typeForm === 1){
        drugLabels[index].form = formItem
    }else{
        openSettings[index].form = formItem
    }
    form.setFieldsValue({...form.getFieldsValue(), drugLabel: drugLabels, openSetting: openSettings})
}


// 更新表单信息
export const updateInitForm = (subjectCtx:any , form:any,
                               drugLabelFrom:any,setDrugLabelFrom:any, drugNameFrom:any, setDrugNameFrom:any,
                               setDrugOption:any, setDrugNameOption:any
) => {
    let drugLabels: any[] = []
    let openSettings: any[] = []
    if (form.getFieldsValue()?.drugLabel){
        drugLabels = [...form.getFieldsValue()?.drugLabel];
    }
    if (form.getFieldsValue()?.openSetting){
        openSettings = [...form.getFieldsValue()?.openSetting];
    }

    let selectOption :any = {...subjectCtx.drugSelect}


    drugLabels.forEach((item:any, index:number)=>{
        if (item.labels?.length > 0){
            drugLabelFrom[index] = subjectCtx.drugOption[item.labels[0].label]?.customerCalculation
            item.labels.forEach((it:any, childIndex:number)=>{
                selectOption[index+""+childIndex] =  subjectCtx.drugOption[item.labels[childIndex].label]
            })
        }
    })
    subjectCtx.setDrugSelect({...selectOption})

    setDrugLabelFrom({...drugLabelFrom})

    let selectNameOption :any = {...subjectCtx.selectOption}

    openSettings.forEach((item:any, index:number)=>{
        if (item.labels?.length > 0){
            drugNameFrom[index] = subjectCtx.drugNameOption[item.labels[0].name]?.customerCalculation
            item.labels.forEach((it:any, childIndex:number)=>{
                selectNameOption[index+""+childIndex] =  subjectCtx.drugNameOption[item.labels[childIndex].name]
            })
        }
    })
    subjectCtx.setSelectOption({...selectNameOption})

    setDrugNameFrom({...drugNameFrom})
    subjectCtx.setUpdateAdd(subjectCtx.updateAdd+1)
}

// 剂量 过滤选项值
export const updateOption = (subjectCtx:any  ,form:any, drugOption:any, drugNameOption:any, setDrugOption:any, setDrugNameOption:any,) => {
    let drugLabels: any[] = []
    let openSettings: any[] = []
    if (form.getFieldsValue()?.drugLabel){
        drugLabels = [...form.getFieldsValue()?.drugLabel];
    }
    if (form.getFieldsValue()?.openSetting){
        openSettings = [...form.getFieldsValue()?.openSetting];
    }
    drugOption = subjectCtx.drugOption.filter((it:any)=>
        drugLabels.find((labels:any)=>
            labels.labels.find((label:any)=>label.label === it.index)
        )

    )
    setDrugOption([...drugOption])
    drugNameOption = subjectCtx.drugNameOption.filter((it:any)=>
        openSettings.find((labels:any)=>{
            labels.labels.find((label:any)=>label.name === it.index)
        })
    )
    setDrugNameOption([...drugNameOption])
    subjectCtx.setUpdateAdd(subjectCtx.updateAdd + 1)
}

export const showDrugDTPend = (subject:any, childKey :any, childFields :any, form:any) => {

    let dtp :number = -2


    let drugLabels: any[] = []
    if (form.getFieldsValue()?.drugLabel){
        drugLabels = [...form.getFieldsValue()?.drugLabel];
    }

    if (childFields?.length === 1 && drugLabels[0]?.labels[0]?.label === undefined) {
        return -3
    }

    for (let i = 0; i < childFields?.length; i++) {
        if (!subject.drugSelect[childKey + "" + i]) {
            continue
        }
        if (subject.drugSelect[childKey + "" + i]?.dtp?.length === 1) {
            if (dtp === -2){
                dtp = subject.drugSelect[childKey + "" + i]?.dtp[0]
            }else{
                if (subject.drugSelect[childKey + "" + i] && dtp !== subject.drugSelect[childKey + "" + i]?.dtp[0]){ //都是单选 其中一个不同
                    dtp = -1
                    break
                }
            }
        }else{ // 存在多选  全部显示
            dtp = -1
            break

        }
    }
    return dtp
}
export const showDTPend = (subject:any, childKey :any, childFields :any, form:any) => {
    let openSettings: any[] = []
    if (form.getFieldsValue()?.openSetting){
        openSettings = [...form.getFieldsValue()?.openSetting];
    }
    let dtp :number = -2
    if (childFields?.length === 1 && openSettings[0]?.labels[0]?.name === undefined) {
        return -3
    }
    for (let i = 0; i < childFields?.length; i++) {
        if (!subject.selectOption[childKey + "" + i]) {
            continue
        }
        if (subject.selectOption[childKey + "" + i]?.dtp?.length === 1) {
            if (dtp === -2){
                dtp = subject.selectOption[childKey + "" + i]?.dtp[0]
            }else{
                if (subject.selectOption[childKey + "" + i] && dtp !== subject.selectOption[childKey + "" + i]?.dtp[0]){ //都是单选 其中一个不同
                    dtp = -1
                    break
                }
            }
        }else{ // 存在多选  全部显示
            dtp = -1
            break

        }
    }
    return dtp
}