import React from "react";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {Button, Col, message, Modal, Row, Table} from "antd";
import {useSafeState} from "ahooks";
import {InfoCircleFilled} from "@ant-design/icons";
import styled from "@emotion/styled";
import {useGlobal} from "../../../../context/global";
import {useAuth} from "../../../../context/auth";

export const EdcVerificationTip = (props:any) => {
    const auth = useAuth();
    const intl = useTranslation();
    const {formatMessage} = intl;
    const g = useGlobal()
    const [visible, setVisible] = useSafeState<any>(false);
    const [record, setRecord] = useSafeState<any>(null);
    const [recordData, setRecordData] = useSafeState<any>([]);
    const [configureFields, setConfigureFields] = useSafeState<any>("");
    const [tip, setTip] = useSafeState<any>("");
    const [type, setType] = useSafeState<any>("");      // register、update、random、replace

    const stratificationBlock = auth.project.info.push_scenario !== undefined? auth.project.info.push_scenario.random_block_push: false;
    const formBlock = auth.project.info.push_scenario !== undefined? auth.project.info.push_scenario.form_random_block_push: false;
    const cohortBlock = auth.project.info.push_scenario !== undefined? auth.project.info.push_scenario.cohort_random_block_push: false;
    const projectType = auth.project.info.type;

    const show = (record:any, type: string) => {
        setVisible(true);
        setRecord(record);
        setRecordData(record.subjectReturnResultsData);

        let fields = "";
        let strTip = "";

        // 受试者号
        if(record.edcOpenSubjectNo){
            fields += formatMessage({ id: "subject.edc.replace.register.information.subject.no" }) +"/"
            if(!record.subjectNoIsAlike){
                strTip += formatMessage({ id: "subject.edc.replace.register.information.subject.no" }) +"/"
            }
        }
        // cohort
        if(record.edcOpenCohort){
            let key = "subject.edc.replace.register.information.cohort"
            if (projectType === 3) {
                key = "subject.edc.replace.register.information.stage"
            }
            fields += formatMessage({ id: key }) +"/"
            if(!record.cohortIsAlike){
                strTip += formatMessage({ id: key }) +"/"
            }
        }
        // 分层
        if(record.edcOpenStratification){
            fields += formatMessage({ id: "subject.edc.replace.register.information.stratification" }) +"/"
            if(!record.stratificationIsAlike){
                strTip += formatMessage({ id: "subject.edc.replace.register.information.stratification" }) +"/"
            }
        }
        // 表单
        if(record.edcOpenForm){
            fields += formatMessage({ id: "subject.edc.replace.register.information.form" }) +"/"
            if(!record.formIsAlike){
                strTip += formatMessage({ id: "subject.edc.replace.register.information.form" }) +"/"
            }
        }

        // 筛选
        if(record.edcOpenScreen){
            fields += formatMessage({ id: "subject.edc.replace.register.information.screen" }) +"/"
            if(!record.screenIsAlike){
                strTip += formatMessage({ id: "subject.edc.replace.register.information.screen" }) +"/"
            }
        }

        setType(type);
        setConfigureFields(fields.slice(0, -1));

        if(strTip === ""){
            strTip = fields.slice(0, -1);
            // 提示信息组装
            let tip = formatMessage({ id: "subject.edc.replace.register.information.a1" }) +
                " "+strTip +" "+
                formatMessage({ id: "subject.edc.replace.register.information.b" });
            setTip(tip);
        }else{
            strTip = strTip.slice(0, -1);
            // 提示信息组装
            let tip = formatMessage({ id: "subject.edc.replace.register.information.a1" }) +
                    " "+strTip +" "+
                    formatMessage({ id: "subject.edc.replace.register.information.a2" }) +
                    " "+strTip +" "+
                    formatMessage({ id: "subject.edc.replace.register.information.a3" });
            setTip(tip);
        }
    };

    const hide = () => {
        setVisible(false);
        setRecordData([]);
        setType("");
    };

    const random = () => {
        // 分层不一致根据配置决定是否阻断随机  增加表单，群组名称不一致的阻断
        let stratificationFlag = false;
        let formFlag = false;
        let cohortFlag = false;
        let labelList = [];

        // 分层
        if(record.edcOpenStratification && !record.stratificationIsAlike && stratificationBlock){
            stratificationFlag = true;
            labelList.push(formatMessage({ id: "projects.subject.stratification" }));
        }
        // 表单
        if(record.edcOpenForm && !record.formIsAlike && formBlock){
            formFlag = true;
            labelList.push(formatMessage({ id: "projects.subject.form" }));
        }
        // 群组名称
        if(projectType !== 1 && record.edcOpenCohort && !record.cohortIsAlike && cohortBlock){
            cohortFlag = true;
            if (projectType === 2) {
                labelList.push(formatMessage({ id: "projects.subject.cohortName" }));
            } else if (projectType === 3) {
                labelList.push(formatMessage({ id: "projects.subject.stageName" }));
            }

        }


        if(stratificationFlag || formFlag || cohortFlag){
            let labelStr = labelList.join("/");
            message.error(formatMessage({ id: "subject.edc.inconsistent.information1" }, { label: labelStr }));
        }else{
            props.random_save(); // 随机
            props.refresh();    // 刷新页面
            hide();
        }
    };

    // 修改/登记
    const registerUpdate = () => {
        props.register_update();    // 登记修改
        props.refresh();            // 刷新页面
        hide();
    };

    // 筛选
    const screen = () => {
        props.screen_save();        // 筛选
        props.refresh();            // 刷新页面
        hide();
    };

    React.useImperativeHandle(props.bind, () => ({show}));

    return (
        <React.Fragment>
            <Modal
                className="custom-small-modal"
                title={<FormattedMessage id="common.tips" />}
                visible={visible}
                centered
                maskClosable={false}

                onCancel={hide}
                destroyOnClose={true}
                footer={
                    <Row justify="end">
                        <Col style={{marginRight:"16px"}}>
                            <Button onClick={hide}>
                                <FormattedMessage id="subject.edc.return.modification" />
                            </Button>
                        </Col>
                        <Col>
                                {/*登记*/}
                                { type === "register" ?
                                    <Button type="primary" onClick={registerUpdate}> <FormattedMessage id="subject.edc.continue.register" /> </Button>
                                    :
                                    null
                                }

                                {/*修改*/}
                                { type === "update" ?
                                    <Button type="primary" onClick={registerUpdate}> <FormattedMessage id="subject.edc.continue.update" /> </Button>
                                    :
                                    null
                                }

                                {/*随机*/}
                                { type === "random" ?
                                    <Button type="primary" onClick={random}> <FormattedMessage id="subject.edc.continue.submitting" /> </Button>
                                    :
                                    null
                                }

                                {/*替换*/}
                                { type === "replace" ?
                                    <Button type="primary" onClick={random}> <FormattedMessage id="subject.edc.continue.replace" /> </Button>
                                    :
                                    null
                                }

                                {/*筛选*/}
                                { type === "screen" ?
                                    <Button type="primary" onClick={screen}> <FormattedMessage id="subject.edc.continue.screen" /> </Button>
                                    :
                                    null
                                }
                        </Col>
                    </Row>
                }
            >
                <TitleContent style={{height: g.lang === "en" ? "70px" : "50px"}}>
                    <Col style={{ fontSize: "14px", fontFamily: 'PingFang SC', marginTop: "3px", marginLeft: "8px", fontWeight: "400" }}>
                        <InfoCircleFilled style={{color:"#FFAE00"}}/> {tip}
                    </Col>
                </TitleContent>


                {/*替换（分层因素不一致）
                { type === "replace" ?
                    <TitleContent1 style={{height: g.lang === "en" ? "70px" : "30px"}}>
                        <Col style={{ fontSize: "14px", fontFamily: 'PingFang SC', marginTop: "3px", marginLeft: "8px", fontWeight: "500" }}>
                            <InfoCircleFilled style={{color:"#FFAE00"}}/> {tip}
                        </Col>
                    </TitleContent1>
                    :
                    null
                }

                登记
                {
                    type === "4" ?
                        <TitleContent2 style={{height: g.lang === "en" ? "70px" : "30px"}}>
                            <Col style={{ fontSize: "14px", fontFamily: 'PingFang SC', marginTop: "3px", marginLeft: "8px", fontWeight: "400" }}>
                                <InfoCircleFilled style={{color:"#FFAE00"}}/> {tip}
                            </Col>
                        </TitleContent2>
                        :
                        null
                }

                修改
                {
                    type === "5" ?
                        <TitleContent2 style={{height: g.lang === "en" ? "70px" : "30px"}}>
                            <Col style={{ fontSize: "14px", fontFamily: 'PingFang SC', marginTop: "3px", marginLeft: "8px", fontWeight: "400" }}>
                                <InfoCircleFilled style={{color:"#FFAE00"}}/> {tip}
                            </Col>
                        </TitleContent2>
                        :
                        null
                }

                随机-替换（EDC未返回）
                {
                    type === "1-1" || type === "2-2" || type === "3-3" || type === "4-1" || type === "5-1" ?
                        <TitleContent2 style={{height: g.lang === "en" ? "70px" : "30px"}}>
                            <Col style={{ fontSize: "14px", fontFamily: 'PingFang SC', marginTop: "3px", marginLeft: "8px", fontWeight: "400" }}>
                                <InfoCircleFilled style={{color:"#FFAE00"}}/> {tip}
                            </Col>
                        </TitleContent2>
                        :
                        null
                }*/}

                <StyleTable
                    className="mar-top-10 edc-verification-table"
                    size="small"
                    bordered
                    dataSource={recordData}
                    pagination={false}
                    rowKey={(record: any) => (record.id)}
                >
                    <Table.Column
                        title={configureFields}
                        key="label"
                        dataIndex="label"
                        width={254}
                        align="center"
                        render={(value, record: any, index) => (
                            record.sign ?
                                <>
                                    <svg className="iconfont" width={14} height={14}>
                                        <use xlinkHref="#icon-chenggong"></use>
                                    </svg>
                                    &nbsp;
                                    {value}
                                </>
                                :
                                <>
                                    <svg className="iconfont" width={14} height={14}>
                                        <use xlinkHref="#icon-cuowu"></use>
                                    </svg>
                                    &nbsp;
                                    {value}
                                </>
                        )
                        }
                    />
                    <Table.ColumnGroup title={formatMessage({id: 'subject.edc.verification.source'})}>
                        <Table.Column
                            title={formatMessage({id: 'subject.edc.verification.return'})}
                            key="edcValue"
                            dataIndex="edcValue"
                            align="center"
                            render={(value, record: any, index) => (
                                value === null || value === undefined || value === '' ? '-' : value
                            )}
                        />

                        <Table.Column
                            title={formatMessage({id: 'subject.edc.verification.enter'})}
                            key="irtValue"
                            dataIndex="irtValue"
                            align="center"
                            render={(value, record: any, index) => (
                                value === null || value === undefined || value === '' ? '-' : value
                            )}
                        />
                    </Table.ColumnGroup>
                </StyleTable>
            </Modal>
        </React.Fragment>
    )
};

// const TitleContent1 = styled.div`
//       height: 50px;
//       width: 590px;
//       left: 444px;
//       top: 322px;
//       border-radius: 2px;
//       background: rgba(255, 174, 0, 0.06);
//       border: 0.5px solid rgba(255, 174, 0, 0.5);
//       border-radius: 2px;
//
// `

const TitleContent = styled.div`
      height: 50px;
      width: 590px;
      left: 444px;
      top: 322px;
      border-radius: 2px;
      background: rgba(255, 174, 0, 0.06);
      border: 0.5px solid rgba(255, 174, 0, 0.5);
      border-radius: 2px;

`

const StyleTable = styled(Table)`
  thead.ant-table-thead-edc > tr > th {
    line-height: 20px !important;
  }
`;

const DividerLine = styled.div`
  position: absolute;
  width: 0.5px;
  height: 259px;
  top: -107px;
  left: 110px;
  background: #E3E4E6;
  transform: rotate(-77.5deg);
`;
