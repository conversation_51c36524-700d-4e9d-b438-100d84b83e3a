import React from "react";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {
    Button,
    Checkbox,
    Col,
    Divider,
    Form,
    Input,
    InputNumber,
    message,
    Modal,
    notification,
    Radio,
    Row,
    Select,
    Switch,
    Typography
} from "antd";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {
    atRandomForm,
    getSubjectReplaceFactor,
    getSubjectReplaceSite,
    subjectEdcVerification,
    subjectReplace,
    subjectReplaceAtRandom,
    subjectReplaceRandomNumber
} from "../../../../api/subject";
import {useSafeState} from "ahooks";
import {CheckCircleOutlined, CloseCircleFilled, InfoCircleFilled} from "@ant-design/icons";
import {useGlobal} from "../../../../context/global";
import styled from "@emotion/styled";
import {Title} from "../../../../components/title";
import {EdcVerificationTip} from "./edc_verification_tip";
import {blindData} from "../../../../data/data";
import {getProject} from "../../../../api/projects";
import {CustomConfirmModal} from "../../../../components/modal";
import {inRandomIsolation} from "../../../../utils/in_random_isolation";
import DatePicker from "../../../../components/DatePicker";
import {pushScenarioFilter} from "../../../../utils/irt_push_edc_util";
import dayjs from "dayjs";

export const Replace = (props:any) => {
    const auth = useAuth();
    const intl = useTranslation();
    const DatePickers: any = DatePicker;
    const {formatMessage} = intl;
    const projectId = auth.project.id;
    const projectType = auth.project.info.type;
    const pushScenario = auth.project.info.push_scenario !== undefined? auth.project.info.push_scenario.random_push: false;

    const [visible, setVisible] = useSafeState<any>(false);
    const [visibleConfirm, setVisibleConfirm] = useSafeState<any>(false);
    const [id, setId] = useSafeState<any>("");
    const [record, setRecord] = useSafeState<any>({});
    const [label, setLabel] = useSafeState<any>("");
    const [fields, setFields] = useSafeState<any>(null);
    const [atRandomFormList, setAtRandomFormList] = useSafeState<any>([]);
    const [replaceNumber, setReplaceNumber] = useSafeState<any>("");
    const [form] = Form.useForm();
    const { Text } = Typography;
    const connectEdc = auth.project.info.connect_edc;
    const pushMode = auth.project.info.push_mode;
    const edc_verification_tip_pt: any = React.useRef();
    const [factorSign, setFactorSign] = useSafeState<any>(false);

    const [edcSupplier, setEdcSupplier] = useSafeState(0);

    const { runAsync: runGetProject, loading } = useFetch(() => getProject({ id: projectId }), {
        refreshDeps: [projectId],
        onSuccess: (result:any) => {
            setEdcSupplier(result.data.info.edcSupplier)
        }
    });

    const show = (record :any, label :any, currentFields: any[], factorSign: any) => {
        setVisible(true);
        setId(record.id);
        setRecord(record);
        setLabel(label);
        runGetProject();

        // factorSign 为true 表示分层因素登记时不填，那么替换的时候就要填写
        // factorSign 为false 表示分层因素登记时填写了，那么替换的时候就无需填写
        setFactorSign(factorSign);
        if (factorSign){
            let randomFiles: any[] = [];
            let fields = JSON.parse(JSON.stringify(currentFields));
            fields.forEach((field:any) => {
                if (field.stratification) {
                    randomFiles.push(field);
                }
            });
            setFields(randomFiles);
        }

       // 在随机项目
       if ((projectType === 3 && !inRandomIsolation(auth.project.info.number)) ||(projectType === 2 && record.cohort?.type === 1)){
           getAtRandomFormList(record.id, factorSign);
       }
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
    };

    const {runAsync:subjectReplaceRun, loading:subjectReplaceLoading} = useFetch(subjectReplace, {manual: true});
    const {runAsync:getSubjectReplaceSiteRun, loading:getSubjectReplaceSiteLoading} = useFetch(getSubjectReplaceSite, {manual: true});
    const {runAsync:getSubjectReplaceFactorRun, loading:getSubjectReplaceFactorLoading} = useFetch(getSubjectReplaceFactor, {manual: true});
    const {runAsync:subjectReplaceAtRandomRun, loading:subjectReplaceAtRandomLoading} = useFetch(subjectReplaceAtRandom, {manual: true});
    const {runAsync:subjectReplaceRandomNumberRun, loading:subjectReplaceRandomNumberLoading} = useFetch(subjectReplaceRandomNumber, {manual: true});
    const {runAsync: getAtRandomFormRun, loading: atRandomFormLoading} = useFetch(atRandomForm, { manual: true });
    const {
        runAsync: edcSubjectVerificationRun,
        loading: edcSubjectVerificationRunLoading
    } = useFetch(subjectEdcVerification, { manual: true });
    // 受试者替换
    const save = () => {
        if (record.attribute.info.isRandomNumber && record.attribute.info.replaceRule === 1) {
            subjectReplaceRandomNumberRun({id,attributeId:record.attribute.id}).then((result:any)=>{
                form.setFieldValue("randomNumber",result.data)
                setReplaceNumber(result.data)
            }).then()
        }
        form.validateFields().then(
            (values) => {
                setReplaceNumber(values.randomNumber)
                setVisibleConfirm(true)
            }
        )
    };

    // 再随机
    const getAtRandomFormList = (id: any, factorSign: boolean) => {
        getAtRandomFormRun({
            subjectId: id,
        }).then((result: any) => {
            // 如果是随机前发药
            if (!factorSign) {
                let datas: any[] = [];
                let cohortId = result.data[0].cohort.id;
                // 数据筛选
                result.data.forEach((d: any) => {
                    let data = {cohort: {}, fields: [], randomNumber: "", replaceNumber: ""};
                    if (d.cohort.id == cohortId) {
                        data.cohort = d.cohort;
                        data.randomNumber = d.randomNumber;
                        data.replaceNumber = d.replaceNumber;
                    }else{
                        data = d;
                    }
                    datas.push(data);
                });
                setAtRandomFormList(datas);
            }else{
                setAtRandomFormList(result.data);
            }
        });
    };

    const hide_confirm = () => {
        setVisibleConfirm(false)
    };

    const edcTip = (record: any, type: string) => {
        edc_verification_tip_pt.current.show(record, type);
    };

    const edc_random_verification = () => {
        form.validateFields()
            .then(values => {
                edcSubjectVerificationRun( {id,...values} ).then((resp: any) => {
                    if(!resp.data.linkStatus){  // 请求接口响应异常
                        // irt 端受试者未登记，实际上没有去edc 端查
                        if (resp.data.edcSubjectStatus == 9) {
                            notification.open({
                                message: <div style={{ fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF", fontSize: "14px", }}><CloseCircleFilled style={{ color: "#F96964", paddingRight: "8px", }} />{formatMessage({ id: 'subject.edc.replace.error' })}</div>,
                                description: <div style={{ paddingLeft: "20px", color: "#646566" }} >{formatMessage({id: "subject.edc.replace.failure.not-registered"})}</div>,
                                duration: 5,
                                placement: "top",
                                style: {
                                    width: "720px",
                                    background: "#FEF0EF",
                                    borderRadius: "4px",
                                },
                            });
                            return
                        }

                        notification.open({
                            message: (
                                <div
                                    style={{
                                        fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                        fontSize: "14px",
                                    }}
                                >
                                    <CheckCircleOutlined
                                        style={{
                                            color: "#00BB00",
                                            paddingRight: "8px",
                                        }}
                                    />
                                    {formatMessage({ id: "common.success" })}
                                </div>
                            ),
                            description: (
                                <div style={{ paddingLeft: "20px", color: "#646566" }}>
                                    {formatMessage({
                                        id: "subject.edc.interface.error",
                                    })}
                                </div>
                            ),
                            duration: 5,
                            placement: "top",
                            style: {
                                width: "720px",
                                background: "#F0FFF0",
                                borderRadius: "4px",
                            },
                        });
                        save_confirm();
                    }else if(!resp.data.subjectIsExist){       // EDC不存在当前受试者
                        Modal.confirm({
                            centered: true,
                            title: formatMessage({ id: "common.please.confirm" }),
                            icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                            content: formatMessage({ id: "subject.edc.create.subject" }),
                            onOk: () => {
                                save_confirm();
                            },
                            cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                            okText: formatMessage({ id: "subject.edc.continue.submitting" }),
                        });
                    }else if (resp.data.siteStatus == 1) {
                        edcRandomLogic(resp);
                    }else if (resp.data.siteStatus == 2){
                        CustomConfirmModal({
                            icon: <InfoCircleFilled style={{ color: "#FFAE00"}} />,
                            title: formatMessage({ id: "common.tips" }),
                            content: formatMessage({ id: "subject.edc.site.inconsistent" }),
                            okText: formatMessage({ id: "subject.edc.continue.replace" }),
                            cancelText: formatMessage({ id: "common.cancel" }),
                            onOk: () =>
                                edcRandomLogic(resp)
                        });
                    }else{
                        CustomConfirmModal({
                            icon: <InfoCircleFilled style={{ color: "#FFAE00"}} />,
                            title: formatMessage({ id: "common.tips" }),
                            content: formatMessage({ id: "subject.edc.site.empty" }),
                            okText: formatMessage({ id: "subject.edc.continue.replace" }),
                            cancelText: formatMessage({ id: "common.cancel" }),
                            onOk: () =>
                                edcRandomLogic(resp)
                        });
                    }
                })
            })
            .catch(error => { })
    };

    // 随机逻辑
    const edcRandomLogic = (resp: any)=>{
        if(resp.data.edcSubjectStatus === 2){  // EDC筛选失败
            notification.open({
                message: <div style={{ fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF", fontSize: "14px", }}><CloseCircleFilled style={{ color: "#F96964", paddingRight: "8px", }} />{formatMessage({ id: 'subject.edc.replace.error' })}</div>,
                description: <div style={{ paddingLeft: "20px", color: "#646566" }} >{formatMessage({id: 'subject.edc.replace.failure.filter.failed'})}</div>,
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        }else if(resp.data.edcSubjectStatus === 5){ // EDC已退出
            notification.open({
                message: <div style={{ fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF", fontSize: "14px", }}><CloseCircleFilled style={{ color: "#F96964", paddingRight: "8px", }} />{formatMessage({ id: 'subject.edc.replace.error' })}</div>,
                description: <div style={{ paddingLeft: "20px", color: "#646566" }} >{formatMessage({id: 'subject.edc.replace.failure.exit'})}</div>,
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        }else if(resp.data.edcSubjectStatus === 6){ // EDC完成研究
            notification.open({
                message: <div style={{ fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF", fontSize: "14px", }}><CloseCircleFilled style={{ color: "#F96964", paddingRight: "8px", }} />{formatMessage({ id: 'subject.edc.replace.error' })}</div>,
                description: <div style={{ paddingLeft: "20px", color: "#646566" }} >{formatMessage({id: 'subject.edc.replace.failure.complete.the.study'})}</div>,
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        }else if(resp.data.edcSubjectStatus === 8){ // EDC筛选中-不可随机
            notification.open({
                message: <div style={{ fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF", fontSize: "14px", }}><CloseCircleFilled style={{ color: "#F96964", paddingRight: "8px", }} />{formatMessage({ id: 'subject.edc.replace.error' })}</div>,
                description: <div style={{ paddingLeft: "20px", color: "#646566" }} >{formatMessage({id: 'subject.edc.replace.failure.screen.no.random'})}</div>,
                duration: 5,
                placement: "top",
                style: {
                    width: "720px",
                    background: "#FEF0EF",
                    borderRadius: "4px",
                },
            });
        }else{  // 表单对比框
            edcTip(resp.data, "replace");
        }
    };
    const subject_replace = (values:any) =>{
        console.log(atRandomFormList);
        atRandomFormList.forEach((formList:any) => {
            formList.fields.forEach((item:any) => {
                if(item.type === "datePicker"){
                    values[formList.cohort.id+item.name] = values[formList.cohort.id+item.name]? dayjs(values[formList.cohort.id+item.name]).format("YYYY-MM-DD") : ""
                }else if (item.type === "timePicker"){
                    values[formList.cohort.id+item.name] = values[formList.cohort.id+item.name]? dayjs(values[formList.cohort.id+item.name]).format("YYYY-MM-DD HH:mm:ss") : ""
                }
            });
        });

        if ((projectType === 3 && !inRandomIsolation(auth.project.info.number)) ||(projectType === 2 && record.cohort?.type === 1)){   // 在随机项目
            subjectReplaceAtRandomRun({id, factorSign, ...values,attributeId:record.attribute.id}).then(
                (resp:any) => {
                    props.refresh();
                    hide();
                    hide_confirm()
                    if (resp.code === 0) {
                        message.success(resp.msg);
                    }else{
                        message.error(resp.msg);
                    }
                }
            )
        }else{                  // 非在随机项目
            subjectReplaceRun({id, factorSign, ...values,attributeId:record.attribute.id}).then(
                (resp:any) => {
                    props.refresh();
                    hide();
                    hide_confirm()
                    if (resp.code === 0) {
                        message.success(resp.msg);
                    }else{
                        message.error(resp.msg);
                    }
                }
            )
        }
    }
    const save_confirm = () => {
        form.validateFields()
            .then(values => {
                getSubjectReplaceSiteRun({envId:record.envId,projectSiteId:record.projectSiteID,shortName:values.subjectNumber}).then(
                    (resp:any)=>{
                        if (resp.data === true){
                            replaceFactor(values)
                        }else {
                            Modal.confirm({
                                centered: true,
                                title: formatMessage({ id: "subject.replace.site.tip.title" }),
                                icon: <InfoCircleFilled style={{ color: "#4072e2" }} />,
                                content: <span style={{color: 'red'}}>{formatMessage({id: 'subject.replace.site.tip.content'})}</span>,
                                onOk: () => {
                                    replaceFactor(values)
                                },
                                okButtonProps: { loading: subjectReplaceAtRandomLoading || subjectReplaceRandomNumberLoading },
                                okText: formatMessage({ id: "common.ok" }),
                            });
                        }
                    }
                )
            })
            .catch(error => { })
    };
    const replaceFactor = (values:any) =>{
        getSubjectReplaceFactorRun({...values,envId:record.envId,subjectId:record.id,shortName:values.subjectNumber}).then(
            (resp:any)=>{
                if (resp.data === true){
                    subject_replace(values)
                }else {
                    Modal.confirm({
                        centered: true,
                        title: formatMessage({ id: "subject.replace.site.tip.title" }),
                        icon: <InfoCircleFilled style={{ color: "#4072e2" }} />,
                        content: <span style={{color: 'red'}}>{formatMessage({id: 'subject.replace.factor.tip.content'})}</span>,
                        onOk: () => {
                            subject_replace(values)
                        },
                        okButtonProps: { loading: subjectReplaceAtRandomLoading || subjectReplaceRandomNumberLoading },
                        okText: formatMessage({ id: "common.ok" }),
                    });
                }
            }
        )
    }

    const g = useGlobal()
    const formItemLayout = {
        labelCol: { style: {   width: g.lang === "en"? "300px" : "150px" } },
    };

    React.useImperativeHandle(props.bind, () => ({show}));

    return (
        <React.Fragment>
            <Modal
                centered
                className="custom-medium-modal"
                title={<FormattedMessage id="subject.register.replace" />}
                open={visible}
                onCancel={hide}
                maskClosable={false}

                destroyOnClose={true}
                footer={
                    <Row justify="end">
                        <Col style={{marginRight:"16px"}}>
                            <Button onClick={hide}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                        </Col>
                        <Col>
                            <Button onClick={ save } type="primary" loading={subjectReplaceLoading || subjectReplaceAtRandomLoading}>
                                <FormattedMessage id="common.ok" />
                            </Button>
                        </Col>
                    </Row>
                }
            >
                <Form form={form} {...formItemLayout}>
                    {((projectType === 3 && !inRandomIsolation(auth.project.info.number)) ||(projectType === 2 && record.cohort?.type === 1)) ?
                        <>
                            <Form.Item
                                label={
                                    record.attribute?
                                        ((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText))))
                                        :formatMessage({id:"subject.number"})
                                }
                                labelCol={{ span: 8 }}
                            >
                                {record.shortname}
                            </Form.Item>
                            <Form.Item
                                label={
                                    formatMessage(
                                        {id:"subject.number.replace"},
                                        {label:record.attribute?((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText)))):formatMessage({id:"subject.number"})
                                        })
                                }
                                labelCol={{ span: 8 }}
                                name="subjectNumber"
                                rules={[{required: true}]}
                            >
                                <Input placeholder={formatMessage({id: "placeholder.input.common"})} allowClear className="full-width"/>
                            </Form.Item>

                            {/* 在随机多阶段 */}
                            {atRandomFormList != null ? atRandomFormList.map((atRandomForm:any, index1:number) => (
                                    <React.Fragment key={index1}>
                                        {/* 阶段名称 */}
                                        <Divider plain>{projectType === 3?atRandomForm.cohort.name:atRandomForm.cohort.reRandomName}</Divider>
                                        {/* 随机号 */}
                                        <Form.Item label={ formatMessage({id:"projects.randomization.randomNumber"}) } labelCol={{ span: 8 }}>
                                            {atRandomForm.randomNumber}
                                        </Form.Item>
                                        {/* 替换受试者的随机号 */}
                                        {
                                            record.attribute?
                                                record.attribute.info.replaceRule === 0 ?
                                                <Form.Item label={formatMessage({id:"subject.number.random.replace"})} name={atRandomForm.cohort.id+"randomNumber"} rules={[{required: true}]} labelCol={{ span: 8 }}>
                                                    <Input placeholder={formatMessage({id: "placeholder.input.common"})} allowClear className="full-width"/>
                                                </Form.Item>
                                                :null
                                            :null
                                        }

                                        {/*分层因素/表单*/}
                                        {atRandomForm.fields != null ? atRandomForm.fields.map((field:any, index2: number) => (
                                                <React.Fragment key={field.id}>
                                                    {field.type === 'select'
                                                        ?
                                                        <Form.Item rules={[{required: true}]} label={field.label} name={atRandomForm.cohort.id+field.name} labelCol={{ span: 8 }}>
                                                            <Select placeholder={formatMessage({id: "placeholder.select.common"})} className="full-width" options={field.options}>
                                                            </Select>
                                                        </Form.Item>
                                                        : null
                                                    }

                                                    {field.type === 'radio'
                                                        ?
                                                        <Form.Item rules={[{required: true}]} label={field.label} name={atRandomForm.cohort.id+field.name} labelCol={{ span: 8 }}>
                                                            <Radio.Group options={field.options}>
                                                            </Radio.Group>
                                                        </Form.Item>
                                                        : null
                                                    }
                                                    {field.type === "switch" ?
                                                        <Form.Item
                                                            label={field.label}
                                                            name={atRandomForm.cohort.id+field.name}
                                                            valuePropName="checked"
                                                            labelCol={{ span: 8 }}
                                                        >
                                                            <Switch
                                                                defaultChecked={false}
                                                            />
                                                        </Form.Item>
                                                        : null
                                                    }
                                                    {field.type === "checkbox" ?
                                                        <Form.Item
                                                            label={field.label}
                                                            name={atRandomForm.cohort.id+field.name}
                                                            labelCol={{ span: 8 }}
                                                        >
                                                            <Checkbox.Group
                                                                options={field.options}
                                                            ></Checkbox.Group>
                                                        </Form.Item>
                                                        : null
                                                    }
                                                    {field.type === "datePicker" ?
                                                        <Form.Item
                                                            label={field.label}
                                                            name={atRandomForm.cohort.id+field.name}
                                                            labelCol={{ span: 8 }}
                                                        >
                                                            <DatePickers
                                                                picker={field.datePicker}
                                                                format={field.dateFormat}
                                                                className="full-width"
                                                            ></DatePickers>
                                                        </Form.Item>
                                                        : null
                                                    }

                                                    {field.type === "timePicker" ?
                                                        <Form.Item
                                                            label={field.label}
                                                            name={atRandomForm.cohort.id+field.name}
                                                            labelCol={{ span: 8 }}
                                                        >
                                                            <DatePickers
                                                                showTime
                                                                className="full-width"
                                                                format={field.timeFormat}
                                                            />
                                                        </Form.Item>
                                                        : null
                                                    }

                                                    {field.type === "input" ?
                                                        <Form.Item
                                                            label={field.label}
                                                            name={atRandomForm.cohort.id+field.name}
                                                            labelCol={{ span: 8 }}
                                                        >
                                                            <Input.TextArea
                                                                allowClear
                                                                className="full-width"
                                                                placeholder={formatMessage({ id: 'common.required.prefix' })}
                                                            />
                                                        </Form.Item>
                                                        : null
                                                    }

                                                    {field.type === "inputNumber" ?
                                                        <Form.Item
                                                            rules={[
                                                                { required: field.required },
                                                                {
                                                                    validator: (_, value) => {
                                                                        if (value !== null && value != undefined) {
                                                                            if (field.range && field.range.min && field.range.min > value) {
                                                                                return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                                            }
                                                                            if (field.range && field.range.max && field.range.max < value) {
                                                                                return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                                            }
                                                                            if (field.formatType !== undefined && field.formatType === "decimalLength") {
                                                                                if (field.length) {
                                                                                    // 将数字转换为字符串
                                                                                    var str = field.length.toString();
                                                                                    // 使用split()方法将整数部分和小数部分分开
                                                                                    var parts = str.split('.');
                                                                                    //取整数部分
                                                                                    let numberPart = parts[0];
                                                                                    var valueDecimalPart = value.toString().split('.');
                                                                                    if (valueDecimalPart && valueDecimalPart[0] && valueDecimalPart[0].length > numberPart) {
                                                                                        return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                                                    }
                                                                                    if (valueDecimalPart && valueDecimalPart[1] && parts[1] && valueDecimalPart[1].length > parts[1]) {
                                                                                        return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                                                    }
                                                                                }
                                                                            } else if (field.formatType !== undefined && field.formatType === "numberLength") {
                                                                                if (
                                                                                    field.length &&
                                                                                    value != null &&
                                                                                    value.toString()
                                                                                        .length >
                                                                                    field.length
                                                                                ) {
                                                                                    return Promise.reject(new Error(formatMessage({ id: "form.control.type.variableRange.validate.range" })));
                                                                                }
                                                                            }
                                                                        }
                                                                        return Promise.resolve();
                                                                    },
                                                                },
                                                            ]}
                                                            label={field.label}
                                                            name={atRandomForm.cohort.id+field.name}
                                                            labelCol={{ span: 8 }}
                                                        >
                                                            <InputNumber
                                                                min={0}
                                                                precision={field.formatType === "numberLength" ? 0 : undefined}
                                                                step={(field.formatType !== undefined && field.formatType === "numberLength") ? 1 : 0.1}
                                                                className="full-width"
                                                                placeholder={formatMessage({ id: 'common.required.prefix' })}

                                                            />
                                                        </Form.Item>
                                                        : null
                                                    }

                                                    {field.type === "textArea" ?
                                                        <Form.Item
                                                            label={field.label}
                                                            name={atRandomForm.cohort.id+field.name}
                                                            labelCol={{ span: 8 }}
                                                        >
                                                            <Input.TextArea
                                                                allowClear
                                                                className="full-width"
                                                                placeholder={formatMessage({ id: 'common.required.prefix' })}
                                                            />
                                                        </Form.Item>
                                                        : null
                                                    }

                                                </React.Fragment>
                                            ))
                                            :null
                                        }
                                    </React.Fragment>
                                ))
                                :
                                    null
                            }
                        </>
                        :
                        <>
                            {(auth.project.info.type === 2 && record.cohort?.type === 0) && (
                                <Col>
                                    <Form.Item
                                        label={formatMessage({
                                            id: "projects.second",
                                        })}
                                    >
                                        {record.cohortName}
                                    </Form.Item>
                                </Col>
                            )}
                            <Form.Item
                                label={
                                    record.attribute?
                                        ((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText))))
                                        :formatMessage({id:"subject.number"})
                                }
                            >
                                {record.shortname}
                            </Form.Item>
                            <Form.Item label={ formatMessage({id:"projects.randomization.randomNumber"}) }>
                                {record.randomNumber}
                            </Form.Item>
                            <Form.Item
                                label={
                                    formatMessage(
                                        {id:"subject.number.replace"},
                                        {label:record.attribute?((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText)))):formatMessage({id:"subject.number"})
                                        })
                                }
                                name="subjectNumber"
                                rules={[{required: true}]}
                            >
                                <Input placeholder={formatMessage({id: "placeholder.input.common"})} allowClear className="full-width"/>
                            </Form.Item>
                            {
                                record.attribute?record.attribute.info.replaceRule === 0 ?
                                    <Form.Item label={formatMessage({id:"subject.number.random.replace"})} name="randomNumber" rules={[{required: true}]}>
                                        <Input placeholder={formatMessage({id: "placeholder.input.common"})} allowClear className="full-width"/>
                                    </Form.Item>
                                    :null:null
                            }
                            {fields != null ? fields.map((field:any, index:number) => (
                                    <React.Fragment key={field.id}>
                                        {field.type === 'select'
                                            ?
                                            <Form.Item style={{marginBottom: index+1 !== fields.length? "16px":"0px"}} rules={[{required: true}]} label={field.label} name={field.name} >
                                                <Select placeholder={formatMessage({id: "placeholder.select.common"})} disabled={field.disabled} className="full-width" options={field.options}>
                                                </Select>
                                            </Form.Item>
                                            : null
                                        }

                                        {field.type === 'radio'
                                            ?
                                            <Form.Item style={{marginBottom: index+1 !== fields.length? "16px":"0px"}} rules={[{required: true}]} label={field.label} name={field.name} >
                                                <Radio.Group disabled={field.disabled} options={field.options}>
                                                </Radio.Group>
                                            </Form.Item>
                                            : null
                                        }
                                    </React.Fragment>
                                ))
                                :null
                            }
                        </>
                    }
                </Form>
            </Modal>

            <Modal
                centered
                className="custom-small-modal"
                open={visibleConfirm}
                title={<FormattedMessage id="common.tips" />}
                confirmLoading={subjectReplaceLoading || subjectReplaceRandomNumberLoading || subjectReplaceAtRandomLoading}
                onCancel={hide_confirm}
                maskClosable={false}

                destroyOnClose={true}
                footer={
                    <Row justify="end">
                        <Col style={{marginRight:"16px"}}>
                            <Button onClick={hide_confirm}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                        </Col>
                        <Col>
                            { pushScenarioFilter(connectEdc, pushMode, edcSupplier, pushScenario)?
                                <Button loading={subjectReplaceLoading || edcSubjectVerificationRunLoading || subjectReplaceAtRandomLoading} onClick={edc_random_verification} type={"primary"}>{formatMessage({ id: 'common.ok' })}</Button>
                                :
                                <Button onClick={ save_confirm } type="primary" loading={subjectReplaceLoading || subjectReplaceAtRandomLoading}>
                                    <FormattedMessage id="operation.subject.replace.confirm" />
                                </Button>
                            }
                        </Col>
                    </Row>
                }
            >
            <TitleContent style={{height:g.lang === "en"? "60px": ""}}>
                <Col style={{fontSize:"14px", fontFamily: 'PingFang SC', marginTop:"6px", marginLeft:"6px", fontWeight: "400"}}> 
                <InfoCircleFilled style={{color:"#FFAE00"}}/> <FormattedMessage id="operation.subject.replace.confirm.is" /></Col>
            </TitleContent>
                {
                    visibleConfirm?
                    <Form {...formItemLayout} className="mar-top-5">
                        <Title name = {formatMessage({id: 'subject.replace.info.old'})}/>
                        <Form.Item
                            style={{ marginBottom:"12px"}}
                            label={
                                record.attribute?
                                ((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText))))
                                :formatMessage({id:"subject.number"})
                            }
                            labelCol={{ span: 8 }}
                        >
                            {record.shortname}
                        </Form.Item>
                        {((projectType === 3 && !inRandomIsolation(auth.project.info.number)) ||(projectType === 2 && record.cohort?.type === 1)) ?
                            <>
                                {atRandomFormList != null ? atRandomFormList.map((atRandomForm:any, index1:number) => (
                                        <Form.Item
                                            style={{marginBottom:"12px"}}
                                            label={ formatMessage({id:"projects.randomization.randomNumber"}) }
                                            labelCol={{ span: 8 }}>
                                            {atRandomForm.randomNumber}({projectType === 3?atRandomForm.cohort.name:atRandomForm.cohort.reRandomName})
                                        </Form.Item>
                                    ))
                                    :
                                    null
                                }
                            </>
                            :
                            <Form.Item style={{marginBottom:"12px"}}
                                   label={ formatMessage({id:"projects.randomization.randomNumber"}) }
                                   labelCol={{ span: 8 }}>
                                {record.randomNumber}
                            </Form.Item>
                        }
                        <Title name = {formatMessage({id: 'operation.subject.beReplace.info'})}/>
                        <Form.Item
                            style={{marginBottom:"12px"}}
                            label={
                                record.attribute?
                                ((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText))))
                                :formatMessage({id:"subject.number"})
                            }
                            labelCol={{ span: 8 }}
                        >
                            {form.getFieldsValue().subjectNumber}
                        </Form.Item>

                        {((projectType === 3 && !inRandomIsolation(auth.project.info.number)) ||(projectType === 2 && record.cohort?.type === 1)) ?
                            <>
                                {
                                    atRandomFormList != null ?
                                        record.attribute.info.replaceRule === 0 ?
                                            atRandomFormList.map((atRandomForm:any, index1:number) => (
                                                <>
                                                    <Form.Item
                                                        label={ formatMessage({id:"projects.randomization.randomNumber"}) }
                                                        labelCol={{ span: 8 }}>
                                                        {
                                                            (atRandomForm.randomNumber != blindData)?
                                                                form.getFieldsValue()[atRandomForm.cohort.id+'randomNumber']+"("+(projectType === 3?atRandomForm.cohort.name:atRandomForm.cohort.reRandomName)+")"
                                                                :
                                                                blindData+"("+(projectType === 3?atRandomForm.cohort.name:atRandomForm.cohort.reRandomName)+")"
                                                        }
                                                    </Form.Item>
                                                </>
                                            ))
                                            :
                                            atRandomFormList.map((atRandomForm:any, index1:number) => (
                                                <>
                                                    <Form.Item
                                                        label={ formatMessage({id:"projects.randomization.randomNumber"}) }
                                                        labelCol={{ span: 8 }}>
                                                        {
                                                            atRandomForm.replaceNumber+"("+(projectType === 3?atRandomForm.cohort.name:atRandomForm.cohort.reRandomName)+")"
                                                        }
                                                    </Form.Item>
                                                </>
                                            ))
                                    :null
                                }
                            </>
                            :
                            <Form.Item
                                label={ formatMessage({id:"projects.randomization.randomNumber"}) }
                                labelCol={{ span: 8 }}>
                                {
                                    (record.attribute && record.attribute.info.isRandomNumber)?
                                        replaceNumber
                                        :
                                        blindData
                                }
                            </Form.Item>
                        }
                    </Form>
                    :
                    null
                }
            </Modal>
            <EdcVerificationTip bind={edc_verification_tip_pt} refresh={props.refresh} random_save={save_confirm}/>
        </React.Fragment>
    )
};

const TitleContent = styled.div`
      height: 32px;
      width: 552px;
      left: 444px;
      top: 322px;
      border-radius: 2px;
      background: rgba(255, 174, 0, 0.06);
      border: 0.5px solid rgba(255, 174, 0, 0.5);
      border-radius: 2px;

`