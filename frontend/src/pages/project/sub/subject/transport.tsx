import React from "react";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {Button, Col, Form, Modal, Row, Select, message} from "antd";
import {useFetch} from "../../../../hooks/request";
import {useSafeState} from "ahooks";
import {transfer} from "../../../../api/subject";
import {useAuth} from "../../../../context/auth";
import {useGlobal} from "../../../../context/global";
import {CustomConfirmModal} from "../../../../components/modal";

export const Transport = (props:any) => {
    const auth = useAuth();
    const intl = useTranslation();
    const {formatMessage} = intl;
    const [visible, setVisible] = useSafeState<any>(false);
    const [id, setId] = useSafeState<any>("");
    const [record, setRecord] = useSafeState<any>({});
    const [sites, setSites] = useSafeState<any>([]);
    const [form] = Form.useForm();

    const show = (record:any, sites: any) => {
        setVisible(true);
        setId(record.id);
        setRecord(record)
        setSites(sites);
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
    };

    const {runAsync:transferRun, loading:transferLoading} = useFetch(transfer, {manual: true})


    // 受试者转运
    const save = () => {
        form.validateFields().then(values => {
            CustomConfirmModal({
                title: formatMessage({id:"subject.confirm.transport"}),
                content: formatMessage({id:"subject.confirm.transport.content"}),
                cancelText: formatMessage({id: 'common.cancel'}),
                okText: formatMessage({id: 'common.ok'}),
                onOk: () => {
                    transferRun({
                        "id": id,
                        "shortname":record.shortname,
                        ...values,
                    }).then(
                        (result: any) => {
                            message.success(result.msg).then();
                            props.refresh();
                            hide();
                        }
                    )
                }
            })
        }).catch(() => { })
    };

    React.useImperativeHandle(props.bind, () => ({show}));
    const g = useGlobal()
    const formItemLayout = {
        labelCol: {
            xs: { span: 30 },
            sm: { span: g.lang==="zh"? 5: 5 },
            // style: { width: 'auto' }
        },
        // wrapperCol: {
        //     xs: { span: 30 },
        //     sm: { span: g.lang==="zh"? 20: 19 },
        // },
    };

    const labelText = record.attribute?
                            ((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText))))
                            :formatMessage({id:"subject.number"})

    const fullLabel = `${labelText}：${record.shortname}`;

    return (
        <React.Fragment>
            <Modal
                className="custom-little-modal"
                title={<FormattedMessage id="subject.transport" />}
                open={visible}
                centered
                maskClosable={false}
                onCancel={hide}
                destroyOnClose={true}
                footer={
                    <Row justify="end">
                        <Col style={{marginRight:"16px"}}>
                            <Button onClick={hide}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                        </Col>
                        <Col>
                            <Button onClick={ save } type="primary" loading={transferLoading}>
                                <FormattedMessage id="common.ok" />
                            </Button>
                        </Col>
                    </Row>
                }
            >
                <Form 
                    form={form} 
                    // {...formItemLayout}
                    // labelCol={{ flex: '110px' }}
                    // labelAlign="left"
                    labelCol={{ style: {width: '90px'} }}
                    labelWrap
                    // wrapperCol={{ flex: 1 }}
                >
                    <Form.Item 
                        label={formatMessage({id: 'subject.current.site'})}  
                        className="mar-ver-5"
                    >
                        {record.siteNumber+"-"+record.siteName}
                    </Form.Item>
                    <Form.Item
                        label={
                            record.attribute?
                            ((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText))))
                            :formatMessage({id:"subject.number"})
                        }
                        style={{
                            minHeight: 32,
                        }}
                        className="mar-ver-5"
                    >
                        <div style={{ display: 'inline-block' }}>{record.shortname}</div>
                    </Form.Item>            
                    <Form.Item 
                        label={formatMessage({id: 'subject.new.site'})} 
                        name="newSite" 
                        rules={[{required: true}]} 
                        className="mar-ver-5"
                    >
                        <Select style={{ width: "100%" }} placeholder={formatMessage({ id: 'placeholder.select.common' })}>
                            {
                                sites?.map((item: any) => {
                                    if (item.id !== record.projectSiteID) {
                                        return    <Select.Option value={item.id}>{item.number+"-"+item.name}</Select.Option>
                                    }
                                })
                            }
                        </Select>
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    )
};