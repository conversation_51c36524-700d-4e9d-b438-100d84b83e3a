import {atom, useAtom} from 'jotai'

export const editVisibleAtom = atom(false)
export const listDataAtom = atom(null)
export const visitOpenAtom = atom("")
export const doseSelectAtom = atom(true)
export const showUnBlindAtom = atom(false)
export const showUnBlindIDAtom = atom("")
export const currentDispensingIDAtom = atom("")
export const currentMedicineAtom = atom({name:"", number:"", medicineId:""})
export const currentDispensingAtom = atom({visitInfo:""})
export const confirmVisibleAtom = atom(false)
