import {<PERSON><PERSON>, Col, DatePicker, Form, message, Row} from "antd";
import React, { useRef, useState, useEffect } from "react";
import moment from "moment";
import {useFetch} from "../../../../hooks/request";
import {updateSubjectJoinTime} from "../../../../api/subject";
import dayjs from "dayjs";
import {useTranslation} from "../../../common/multilingual/component";
import {permissions, permissionsCohort} from "../../../../tools/permission";
import {useAuth} from "../../../../context/auth";
import {useSubject} from "./context";


interface RandomTimeInterface {
    record:any
}
export const RandomTime = (props:RandomTimeInterface) => {
    const auth = useAuth();
    const subjectCtx = useSubject()
    const intl = useTranslation();
    const { formatMessage } = intl;

    const [open, setOpen] = useState(false);
    const datePickerRef = useRef<any>(null);
    const [selectedDate, setSelectedDate] = useState<moment.Moment | null>(null);

    const { runAsync: updateSubjectJoinTimeRun, loading: updateSubjectJoinTimeLoading } = useFetch(updateSubjectJoinTime, { manual: true });

    useEffect(() => {
        const handleClickOutside = (event: any) => {
            if (
                datePickerRef.current &&
                !datePickerRef.current.contains(event.target) &&
                document.getElementsByClassName("ant-picker-dropdown")?.length > 0 &&
                !document.getElementsByClassName("ant-picker-dropdown")?.[0]
                    .contains(event.target)
            ) {
                setOpen(false);
            }
        };

        document.addEventListener("click", handleClickOutside);
        return () => {
            document.removeEventListener("click", handleClickOutside);
        };
    }, []);

    const onChangeJoinTime = (date:any) => {
        {
            updateSubjectJoinTimeRun({id:props.record.id, joinTime: date?.format("YYYY-MM-DD"), roleId: auth.project.permissions.role_id,}).then(
                (res:any) => {
                    subjectCtx.setJoinTime(date?.format("YYYY-MM-DD"))
                    message.success(res.msg)
                })}
    }

    return (
        <div>
            <span style={{color:"#677283"}}>{formatMessage({id:"check.select.time"})}</span>：{subjectCtx.joinTime}
            {
                permissionsCohort(
                    auth.project.permissions,
                    "operation.subject.medicine.joinTime",
                    props.record?.cohortStatus
                ) && !props.record?.attribute?.info?.random &&
            <div ref={datePickerRef} style={{ display: "inline-block", }}>
                <DatePicker
                    disabledDate={(current: any) => {
                        return current < dayjs().year(2020).month(0).date(0) || current > dayjs();
                    }}
                    open={open}
                    value={selectedDate}
                    allowClear={false}
                    onChange={(date) => setSelectedDate(date)}
                    bordered={false}
                    getPopupContainer={(trigger) => trigger}
                    showToday={false}
                    inputRender={() => <Button size={"small"} type={"link"} onClick={() => setOpen(true)} >
                        <svg width={16} height={16} style={{paddingTop:2}}>
                            <use xlinkHref="#icon-bianji"></use>
                        </svg>
                    </Button>
                    }
                    suffixIcon={undefined}
                    renderExtraFooter={() => (
                        <Row justify="space-between" align="middle">
                            <Button type="link" onClick={() => setSelectedDate(moment())}>
                                {formatMessage({ id: "calendar.button.today" })}
                            </Button>
                            <Button
                                type="primary"
                                size="small"
                                onClick={() => {
                                    onChangeJoinTime(selectedDate);
                                    setOpen(false);
                                }}
                            >
                                {formatMessage({ id: "common.ok" })}
                            </Button>
                        </Row>
                    )}
                />
            </div>
            }


        </div>
    );
};
