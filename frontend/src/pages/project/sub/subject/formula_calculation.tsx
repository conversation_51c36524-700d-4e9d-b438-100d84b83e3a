import {<PERSON><PERSON>, <PERSON>box, Col, <PERSON><PERSON>icker, Divider, Form, Input, InputNumber, Row, Select, Space} from "antd";
import {MinusCircleFilled, PlusOutlined} from "@ant-design/icons";
import {TipInput} from "../../../../components/TipInput";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import React from "react";
import {useSubject} from "./context";
import {useFetch} from "../../../../hooks/request";
import {getFormulaMedicine} from "../../../../api/dispensing";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import dayjs from "dayjs";
import {permissions} from "../../../../tools/permission";
import {ComparisonSymbolsType, DTPOption, FormulaWeightType} from "../../../../data/data";
import {useGlobal} from '../../../../context/global';
import {showDTPend} from "./get_formula_medicine";
import {GetDtpOption} from "./drug_label";


interface FormulaCalculationInterface {
    form:any
    record:any
    visit_id:any
}

export const FormulaCalculation = (prop:FormulaCalculationInterface) => {
    const g = useGlobal()
    const subjectCtx = useSubject();
    const {form,record,visit_id} = prop
    const DatePickers: any = DatePicker
    const {runAsync: getFormulaMedicineRun, loading: getFormulaMedicineLoading} = useFetch(getFormulaMedicine, {manual: true, debounceWait: 500})

    const auth = useAuth();
    const envId = auth.env.id;
    const [updateCount, setUpdateCount] = useSafeState<boolean>(permissions(auth.project.permissions, "operation.subject.medicine.formula.update"))


    const intl = useTranslation();
    const {formatMessage} = intl;


    const get_formula_medicine = (formulaType:number) => {
        setUpdateCount(!permissions(auth.project.permissions, "operation.subject.medicine.formula.update"))
        let value :any = form.getFieldsValue()
        delete value["medicine"];
        const date = value.age ? dayjs(value.age).format("YYYY-MM-DD") : ""
        getFormulaMedicineRun({
            ...value,
            env_id:envId,
            cohort_id:record.cohortId,
            subject_id:record.id,
            visit_id,
            weight:value.weight,
            height:value.height,
            age:date
        }).then(
            (value:any) => {
                if (value.data.customerFormulaMedicineRes) {
                    subjectCtx.setFormulaRes(value.data.customerFormulaMedicineRes)
                }

                if (value.data.formulaMedicineRes && formulaType === 1){
                    subjectCtx.setConfigData(value.data.formulaMedicineRes)
                    let medicines :any = []
                    form.getFieldsValue().medicine.map((item:any, index:number)=>
                    {
                        let medicine :any = {}
                        medicine = form.getFieldsValue().medicine[index]
                        if (value.data.formulaMedicineRes[index].number !== 0) {
                            medicine.number = value.data.formulaMedicineRes[index].number
                        }else{
                            medicine.number = null
                        }
                        medicines.push(medicine)
                    })
                    form.setFieldsValue({...form.getFieldsValue(), medicine:medicines})
                    let validMedicines :any = []
                    medicines.forEach(
                        (_:any,index:number) => {
                            validMedicines.push(["medicine",index,"number"])
                        }
                    )

                    form.validateFields(validMedicines).then()
                }
            }
        )
    }
    React.useEffect(() => {
        if (subjectCtx.refreshMedicine){
            get_formula_medicine(1)
        }
        }, [subjectCtx.refreshMedicine]);

    const MinValidator = {
        validator: (rule:any, value:any, callback:any) => {

            if ((value || value === 0) && value < 0) {
                return Promise.reject(formatMessage({ id: "input.error.common" }));
            }
            return Promise.resolve();
        },
    };


    return <div>

        {(subjectCtx.formulaData?.height ||
            subjectCtx.formulaData?.weight ||
            subjectCtx.formulaData?.age) &&
            <div  style={{backgroundColor:"#F8F9FA", paddingTop:16, paddingRight:24}}>
                {
                    subjectCtx.formulaData?.height &&
                    <Form.Item rules={[{required: true}]} label={formatMessage({id: "drug.configure.formula.height"})}
                               className={"full-width"}
                               labelCol={{style:{ whiteSpace: "pre-wrap",width:120, textAlign:"right", height:"auto"}}}
                               name={"height"}>
                        <InputNumber placeholder={formatMessage({id:"common.required.prefix"})}  onChange={() => {get_formula_medicine(1)}} addonAfter={"cm"} className={"full-width"} />
                    </Form.Item>
                }
                {
                    subjectCtx.formulaData?.weight &&
                    <Form.Item rules={[{required: true}]} label={formatMessage({id: "drug.configure.formula.weight"})}
                               className={"full-width"}
                               labelCol={{style:{ whiteSpace: "pre-wrap",width:120, textAlign:"right", height:"auto"}}}

                               name={"weight"}>
                        <InputNumber placeholder={formatMessage({id:"common.required.prefix"})} onChange={() => {get_formula_medicine(1)}} addonAfter={"kg"} className={"full-width"}/>
                    </Form.Item>
                }
                {
                    subjectCtx.formulaData?.age &&
                    <Form.Item rules={[{required: true}]} label={formatMessage({id: "drug.configure.formula.date"})}
                               className={"full-width"}
                               style={{paddingRight:24}}
                               labelCol={{style:{ whiteSpace: "pre-wrap",width:120, textAlign:"right", height:"auto"}}}
                               name={"age"}>
                        <DatePickers format={'YYYY-MM-DD'} onChange={() => {get_formula_medicine(1)}} className={"full-width"}/>
                    </Form.Item>
                }
                {
                    subjectCtx.formulaData?.medicine_name &&
                    <Form.Item label={formatMessage({id: "shipment.medicine"})} rules={[{required: true}]}
                               labelCol={{style:{ whiteSpace: "pre-wrap",width:120, textAlign:"right", height:"auto"}}}
                    style={{marginBottom:0}}
                    >
                        <Form.List name="medicine">
                            {(fields) => (
                                <>
                                    {
                                        fields.map((field: any) => (
                                            <div key={field.key}>
                                                <Row key={field.key} justify="space-between" gutter={12}
                                                >
                                                    <Col span={12}>
                                                    <Form.Item  className="full-width"  name={[field.name, "name"]}>
                                                        <Input placeholder={formatMessage({id:"common.required.prefix"})} disabled={true} className="full-width"/>
                                                    </Form.Item>
                                                    </Col>
                                                    <Col span={12}>
                                                    <Form.Item
                                                        // dependencies={['weight']}
                                                        className="full-width"
                                                        rules={[
                                                            {
                                                                required: !subjectCtx.configData[field.name]?.out_size,
                                                                message: formatMessage({id: 'common.required.prefix'})
                                                            },
                                                            MinValidator,
                                                            // ({ getFieldValue }) => ({
                                                            //     validator(_, value) {
                                                            //         if (value) {
                                                            //             return Promise.resolve();
                                                            //         }
                                                            //         return Promise.reject(new Error(formatMessage({id: 'common.required.prefix'})));
                                                            //     },
                                                            // }),
                                                        ]}
                                                        name={[field.name, "number"]}>
                                                        <InputNumber
                                                            placeholder={formatMessage({id:"common.required.prefix"})}
                                                            className="full-width"
                                                            // style={{width: "97%"}}
                                                            disabled={ updateCount || subjectCtx.configData[field.name]?.out_size}
                                                            addonAfter={subjectCtx.formulaData.medicine_name[field.name]?.spec}
                                                        />
                                                    </Form.Item>
                                                    </Col>
                                                </Row>
                                                {
                                                    subjectCtx.configData[field.name]?.out_size &&
                                                    <Row
                                                        style={{
                                                            borderRadius: '2px 2px 2px 2px',
                                                            backgroundColor: "#fff7e6",
                                                            padding: '8px',
                                                            height: "auto",
                                                            marginLeft: "0px",
                                                            marginTop: "0px",
                                                            marginBottom:"16px",
                                                            alignItems: 'center',
                                                            opacity: "0.1px",
                                                            color: "#677283",
                                                            // marginRight:12
                                                        }}
                                                        className="full-width"
                                                    >
                                                        {formatMessage({id:"subject.dispensing.drug.input.error.age"})}
                                                    </Row>
                                                }

                                                {
                                                    record?.attribute?.info?.dtpRule === 1 &&
                                                    <Row
                                                        className="full-width"

                                                    >

                                                        <Form.Item
                                                            {...field}
                                                            name={[field.name, "dtp"]}
                                                            className="full-width"
                                                            rules={[{ required: true }]}

                                                        >
                                                            {
                                                                <Select
                                                                    className="full-width"
                                                                    disabled={subjectCtx.formulaData.medicine_name[field.name]?.dtp?.length === 1}
                                                                    placeholder={formatMessage({id:"logistics.dispensing.method"})}
                                                                    optionLabelProp="label"
                                                                >
                                                                    {
                                                                        subjectCtx.formulaData.medicine_name[field.name]?.dtp?.map((it:any)=>
                                                                            GetDtpOption(it, formatMessage)
                                                                        )
                                                                    }
                                                                </Select>
                                                            }
                                                        </Form.Item>
                                                    </Row>


                                                }



                                                {

                                                    !subjectCtx.configData[field.name]?.out_size && subjectCtx.configData[field.name]?.calculationType > 1 && subjectCtx.configData[field.name].number > 0 &&
                                                    <Row
                                                        style={{
                                                            borderRadius: '2px 2px 2px 2px',
                                                            backgroundColor: "#fff7e6",
                                                            padding: '8px',
                                                            height: "auto",
                                                            marginLeft: "0px",
                                                            marginTop: "0px",
                                                            alignItems: 'center',
                                                            opacity: "0.1px",
                                                            color: "#677283",
                                                            marginBottom:"16px",
                                                        }}
                                                        className="full-width"
                                                    >
                                                        {/* <Row> */}
                                                        {
                                                            subjectCtx.configData[field.name]?.calculationType === 2 &&
                                                            <Row>
                                                                {
                                                                    <span style={{
                                                                        fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                        fontSize: "12px",
                                                                        fontWeight: 400,
                                                                        color: "#677283",
                                                                    }}>
                                                                {formatMessage({id: "subject.dispensing.drug.formula.weight.tip.start"})}
                                                            </span>
                                                                }

                                                                {
                                                                    subjectCtx.configData[field.name] ?
                                                                        <span style={{
                                                                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                            fontSize: "12px",
                                                                            fontWeight: 500,
                                                                            color: "#1D2129",
                                                                        }}>
                                                                {subjectCtx.configData[field.name].number}
                                                            </span>
                                                                        : null
                                                                }
                                                                {
                                                                    subjectCtx.configData[field.name] ?
                                                                        <span style={{
                                                                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                            fontSize: "12px",
                                                                            fontWeight: 400,
                                                                            color: "#677283",
                                                                        }}>
                                                                {subjectCtx.configData[field.name].spec}
                                                            </span>
                                                                        : null
                                                                }
                                                                {
                                                                    subjectCtx.configData[field.name] ?
                                                                        <span style={{
                                                                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                            fontSize: "12px",
                                                                            fontWeight: 400,
                                                                            color: "#677283",
                                                                        }}>
                                                                {g.lang === "zh" ? "。" : "."}
                                                            </span>
                                                                        : null
                                                                }
                                                            </Row>
                                                        }
                                                        {
                                                            (subjectCtx.configData[field.name]?.calculationType === 3 || subjectCtx.configData[field.name]?.calculationType === 4) &&
                                                            <Row>
                                                                {
                                                                    <span style={{
                                                                        fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                        fontSize: "12px",
                                                                        fontWeight: 400,
                                                                        color: "#677283",
                                                                    }}>
                                                                {formatMessage({id: "subject.dispensing.drug.formula.tip.start"})}
                                                            </span>
                                                                }

                                                                {
                                                                    subjectCtx.configData[field.name] ?
                                                                        <span style={{
                                                                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                            fontSize: "12px",
                                                                            fontWeight: 500,
                                                                            color: "#1D2129",
                                                                        }}>
                                                                {subjectCtx.configData[field.name].number}
                                                            </span>
                                                                        : null
                                                                }
                                                                <span style={{
                                                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                    fontSize: "12px",
                                                                    fontWeight: 400,
                                                                    color: "#677283",
                                                                }}>
                                                            {formatMessage(
                                                                {id: "subject.dispensing.drug.formula.tip.end"},
                                                                {
                                                                    spec: subjectCtx.configData[field.name]?.spec,
                                                                    specifications: subjectCtx.configData[field.name]?.specifications,
                                                                    unit: subjectCtx.configData[field.name]?.unit,
                                                                },
                                                            )}
                                                        </span>
                                                                {
                                                                    subjectCtx.configData[field.name] ?
                                                                        <span style={{
                                                                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                            fontSize: "12px",
                                                                            fontWeight: 500,
                                                                            color: "#1D2129",
                                                                        }}>
                                                                {subjectCtx.configData[field.name].count}/{subjectCtx.configData[field.name].unit}
                                                            </span>
                                                                        : null
                                                                }
                                                                {
                                                                    subjectCtx.configData[field.name] ?
                                                                        <span style={{
                                                                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                            fontSize: "12px",
                                                                            fontWeight: 400,
                                                                            color: "#677283",
                                                                        }}>
                                                                {g.lang === "zh" ? "。" : "."}
                                                            </span>
                                                                        : null
                                                                }
                                                            </Row>
                                                        }
                                                        {
                                                            subjectCtx.configData[field.name]?.showComparisonType &&
                                                            <Row>
                                                                {/* {formatMessage(
                                                            {id: "subject.dispensing.drug.formula.two.tip"},
                                                            {
                                                                comparisonType: FormulaWeightType.find((item: any) => item.value === subjectCtx.configData[field.name]?.comparisonType)?.label,
                                                                radio: subjectCtx.configData[field.name]?.radio,
                                                                currentComparisonType: FormulaWeightType.find((item: any) => item.value === subjectCtx.configData[field.name]?.currentComparisonType)?.label,
                                                            },
                                                        )} */}

                                                                <span style={{
                                                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                    fontSize: "12px",
                                                                    fontWeight: 400,
                                                                    color: "#677283",
                                                                }}>
                                                            {formatMessage(
                                                                {id: "subject.dispensing.drug.formula.two.tip.start"},
                                                                {
                                                                    comparisonType: FormulaWeightType.find((item: any) => item.value === subjectCtx.configData[field.name]?.comparisonType)?.label,
                                                                    comparisonSymbols: ComparisonSymbolsType.find((item: any) => item.value === subjectCtx.configData[field.name]?.comparisonSymbols)?.label,
                                                                },
                                                            )}
                                                        </span>
                                                                {
                                                                    subjectCtx.configData[field.name] ?
                                                                        <span style={{
                                                                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                            fontSize: "12px",
                                                                            fontWeight: 500,
                                                                            color: "#1D2129",
                                                                        }}>
                                                                {subjectCtx.configData[field.name].radio}
                                                            </span>
                                                                        : null
                                                                }
                                                                {
                                                                    subjectCtx.configData[field.name] ?
                                                                        <span style={{
                                                                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                            fontSize: "12px",
                                                                            fontWeight: 500,
                                                                            color: "#1D2129",
                                                                        }}>
                                                                {"%"}
                                                            </span>
                                                                        : null
                                                                }
                                                                <span style={{
                                                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                                    fontSize: "12px",
                                                                    fontWeight: 400,
                                                                    color: "#677283",
                                                                }}>
                                                            {formatMessage(
                                                                {id: "subject.dispensing.drug.formula.two.tip.end"},
                                                                {
                                                                    currentComparisonType: FormulaWeightType.find((item: any) => item.value === subjectCtx.configData[field.name]?.currentComparisonType)?.label,
                                                                },
                                                            )}
                                                        </span>
                                                            </Row>
                                                        }

                                                    </Row>
                                                }

                                            </div>


                                        ))
                                    }
                                </>
                            )}
                        </Form.List>
                    </Form.Item>
                }
            </div>
        }
        {/*{*/}
        {/*    subjectCtx.formulaRes?.map(*/}
        {/*        (item:any)=>*/}
        {/*            item?.customerFormula !== 0 &&*/}
        {/*            <div*/}
        {/*                style={{*/}
        {/*                    borderRadius: '2px 2px 2px 2px',*/}
        {/*                    backgroundColor:"#fff7e6",*/}
        {/*                    padding: '8px',*/}
        {/*                    height: "auto",*/}
        {/*                    marginLeft:subjectCtx.labelWidth,*/}
        {/*                    marginTop: "12px",*/}
        {/*                    marginBottom: "12px",*/}
        {/*                    // display: 'flex',*/}
        {/*                    alignItems: 'center',*/}
        {/*                    width:730-subjectCtx.labelWidth,*/}
        {/*                    opacity: "0.1px",*/}
        {/*                    color: "#677283",*/}
        {/*                }}*/}
        {/*            >*/}
        {/*                <span >*/}
        {/*                    <svg className="iconfont" width={16} height={16} style={{marginBottom:"-4px",}}>*/}
        {/*                            <use xlinkHref="#icon-jinggao"></use>*/}
        {/*                        </svg>*/}
        {/*                </span>*/}
        {/*                <span style={{*/}
        {/*                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",*/}
        {/*                    fontSize: "12px",*/}
        {/*                    fontWeight: 400,*/}
        {/*                    color: "#677283",*/}
        {/*                }}>{formatMessage({id: "subject.dispensing.drug.formula.tip.start"})}</span>*/}
        {/*                <span style={{*/}
        {/*                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",*/}
        {/*                    fontSize: "12px",*/}
        {/*                }}>{item?.customerFormula}</span>*/}

        {/*                <span style={{*/}
        {/*                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",*/}
        {/*                    fontSize: "12px",*/}
        {/*                    fontWeight: 400,*/}
        {/*                    color: "#677283",*/}
        {/*                }}>{formatMessage({id: "subject.dispensing.drug.customer.formula.tip", },{ unit:item?.unit, spec:item?.spec.join(",")})}</span>*/}
        {/*            </div>*/}
        {/*    )*/}

        {/*}*/}


        </div>
}
