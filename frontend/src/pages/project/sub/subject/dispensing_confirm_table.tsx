import {Table, Typography} from "antd";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import React, {useEffect} from "react";
import ts from "typescript/lib/tsserverlibrary";
import {useSubject} from "./context";
import {projectTypes} from "../../../../data/data";
import {useSafeState} from "ahooks";
import {handlerRowSpan} from "../../../../utils/cell_merge";


interface DispensingConfirmCableInterface {
    data :any
    types :any

}
export const DispensingConfirmCable = (prop:DispensingConfirmCableInterface) => {
    const ctx = useSubject();
    const intl = useTranslation();
    const {formatMessage} = intl;
    const {data} = prop
    return <>
            <Table
                className="mar-top-10"
                size="small"
                dataSource={data}
                pagination={false}
                rowKey={(record: any) => (record.id)}
                bordered
            >
                {
                    !prop.data.find((it:any)=>it.ipName && it.ipName !== "") ?
                        <>
                            <Table.Column
                                title={formatMessage({id:"shipment.medicine"})  +"/" +  formatMessage({id:"report.attributes.dispensing.label"}) }
                                dataIndex="name"
                                key="name"
                            />
                        </>
                        :
                    <>
                        <Table.Column
                            title={formatMessage({id:"shipment.medicine"})  +"/" +  formatMessage({id:"report.attributes.dispensing.label"}) }
                            dataIndex="name"
                            key="name"
                            colSpan={2}
                            onCell={(_, index) => {
                                return handlerRowSpan(data, "name", index)
                            }}
                        />
                        <Table.Column
                            title={<FormattedMessage id="shipment.medicine"/>}
                            dataIndex="ipName"
                            key="name"
                            colSpan={0}
                        />
                    </>

                }

                <Table.Column
                    title={<FormattedMessage id="drug.freeze.count"/>}
                    dataIndex="count"
                    key="count"
                    ellipsis
                />
                <Table.Column
                    title={<FormattedMessage id="drug.configure.drugSpecifications"/>}
                    dataIndex="spec"
                    key="spec"
                    ellipsis
                    render={(value, record, index) => (value !== "" ? value : "-")}
                />
                {
                    ctx.isLevel && prop.types !== 2 &&
                    <Table.Column
                        title={<FormattedMessage id="report.attributes.dispensing.dose"/>}
                        dataIndex="level"
                        key="level"
                        ellipsis
                        render={(value, record, index) => (value !== "" ? value : "-")}
                    />
                }

            </Table>



    </>
}