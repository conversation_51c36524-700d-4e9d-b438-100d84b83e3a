import React from 'react';
import {Col, DatePicker, Form, Input, InputNumber, message, Modal, notification, Radio, Row, Select, Space} from "antd";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {realDispensing} from "../../../../api/dispensing";
import {TipInput} from "../../../../components/TipInput";
import {CustomConfirmModal} from "../../../../components/modal";
import {useGlobal} from "../../../../context/global";
import moment from "moment/moment";
import {subjectEdcVerification} from "../../../../api/subject";
import {getProject} from "../../../../api/projects";
import {useAuth} from "../../../../context/auth";
import {InfoCircleFilled, CheckCircleOutlined} from "@ant-design/icons";
import TextArea from "antd/es/input/TextArea";
import {pushScenarioFilter} from "../../../../utils/irt_push_edc_util";
import {getMedicineOther} from "../../../../api/drug_other";
import {getDrugNames} from "../../../../api/drug";
import { CustomDateTimePicker } from "../../../../components/CustomDateTimePicker";

export const DispensingRegister = (props:any) => {
    const auth = useAuth();
    const connectEdc = auth.project.info.connect_edc;
    const pushMode = auth.project.info.push_mode;
    const {list } = props
    const intl = useTranslation();
    const {formatMessage} = intl;
    const [selectOthers, setSelectOther] = useSafeState<any>(false);
    const [realDispensingData, setRealDispensingData] = useSafeState<any>({});
    const [realFrom] = Form.useForm();
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const pushScenario = auth.project.info.push_scenario !== undefined? auth.project.info.push_scenario.dispensing_push: false;

    const [expirationDateValue, setExpirationDateValue] = useSafeState(null);

    const real_dispensing = (data: any) => {
        setSelectOther(false)
        setRealDispensingData(data)
        setRealDispensingVisible(true)
    }
    const {runAsync: realDispensingRun, loading: realDispensingLoading} = useFetch(realDispensing, {manual: true})
    const { runAsync: getDrugNamesRun } = useFetch(getDrugNames, { debounceWait: 300, manual: true })

    const {
        runAsync: edcSubjectVerificationRun,
        loading: edcSubjectVerificationRunLoading,
    } = useFetch(subjectEdcVerification, { manual: true });
    const [edcSupplier, setEdcSupplier] = useSafeState(0);
    const { runAsync: runGetProject, loading } = useFetch(() => getProject({ id: projectId }), {
        refreshDeps: [projectId],
        onSuccess: (result:any) => {
            setEdcSupplier(result.data.info.edcSupplier);
        }
    });


    React.useImperativeHandle(props.bind, () => ({show}));
    const [realDispensingVisible, setRealDispensingVisible] = useSafeState<any>(false);
    const [drugNames, setDrugNames] = useSafeState([]);

    const show = (data:any) => {
        real_dispensing(data);
        runGetProject().then();
        getDrugNamesRun({ customerId, envId, types: "1" }).then(
            (result: any) => {
                if (result.data.drugNames != null) {
                    const options = result.data.drugNames.map((it: any) => ({
                        label: it,
                        value: it
                    }));
                    setDrugNames(options);
                }
            }
        );
    }
    const save = (response:any) => {
        realFrom.resetFields()
        if (response.data?.updateRegister){
            notification.open({
                message: (
                    <div
                        style={{
                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                            fontSize: "14px",
                            marginTop: "-8px",
                        }}
                    >
                        <Row >
                            <svg className="iconfont mouse" width={16} height={16} style={{ marginTop: "4px", marginRight: "10px" }}>
                                <use xlinkHref="#icon-wanchengyanjiu" />
                            </svg>
                            {formatMessage({ id: "subject.dispensing.registration.success" })}
                        </Row>
                        <Row >

                            {formatMessage({ id: "subject.dispensing.realDispensing.res" })}
                        </Row>
                    </div>
                ),
                duration: 7,
                placement: "top",
                style: {
                    width: "720px",
                    // height: "40px",
                    background: "#E2F7EC",
                    borderStyle: "solid",
                    border: "1px",
                    borderColor: "#41CC82",
                    borderRadius: "4px",
                },
            });

        }else{
            message.success(response.msg);
        }
        list(null, null)
        props.list()
        setRealDispensingVisible(false)
    }

    //EDC对接项目需要校验中心
    const real_dispensing_confirm = () => {
        if(pushScenarioFilter(connectEdc, pushMode, edcSupplier, pushScenario)){
            let subjectData = {
                id: realDispensingData.subjectId,
            };
            edcSubjectVerificationRun(subjectData).then((resp: any) => {
                if (!resp.data.linkStatus) {     // 请求接口响应异常
                    notification.open({
                        message: (
                            <div
                                style={{
                                    fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                    fontSize: "14px",
                                }}
                            >
                                <CheckCircleOutlined
                                    style={{
                                        color: "#00BB00",
                                        paddingRight: "8px",
                                    }}
                                />
                                {formatMessage({ id: "common.success" })}
                            </div>
                        ),
                        description: (
                            <div style={{ paddingLeft: "20px", color: "#646566" }}>
                                {formatMessage({
                                    id: "subject.edc.interface.error",
                                })}
                            </div>
                        ),
                        duration: 5,
                        placement: "top",
                        style: {
                            width: "720px",
                            background: "#F0FFF0",
                            borderRadius: "4px",
                        },
                    });
                    real_dispensing_confirm_method();
                }else{
                    // 1中心匹配 2中心不匹配 3 未获取到中心编号或者名称
                    if(resp.data.siteStatus == 1){
                        real_dispensing_confirm_method();
                    }else if(resp.data.siteStatus == 2){
                        CustomConfirmModal({
                            icon: <InfoCircleFilled style={{ color: "#FFAE00"}} />,
                            title: formatMessage({ id: "common.tips" }),
                            content: formatMessage({ id: "subject.edc.site.inconsistent" }),
                            okText: formatMessage({ id: "subject.edc.continue.dispense" }),
                            cancelText: formatMessage({ id: "common.cancel" }),
                            onOk: () =>
                                real_dispensing_confirm_method()
                        });
                    }else{
                        CustomConfirmModal({
                            icon: <InfoCircleFilled style={{ color: "#FFAE00"}} />,
                            title: formatMessage({ id: "common.tips" }),
                            content: formatMessage({ id: "subject.edc.site.empty" }),
                            okText: formatMessage({ id: "subject.edc.continue.dispense" }),
                            cancelText: formatMessage({ id: "common.cancel" }),
                            onOk: () =>
                                real_dispensing_confirm_method()
                        });
                    }
                }
            });
        }else{
            real_dispensing_confirm_method();
        }
    };

    const real_dispensing_confirm_method = () => {
        realFrom.validateFields().then(
            (value:any) => {
                let content:any = formatMessage({id: 'subject.dispensing.realDispensingConfirm'})

                let info :any = realDispensingData.otherDispensingMedicines?.find((it:any) => it.id === value?.number )
                if (!info) {
                    const medicineInfo = realDispensingData.dispensingMedicines?.find((it:any) => it.medicineId === value?.number )
                    if (medicineInfo.dtp === 1 || medicineInfo.dtp === 2) {
                        content = formatMessage({id: 'subject.dispensing.realDispensingConfirmDTP'})
                    }
                }else{
                    if (info?.dtp === 1 || info?.dtp === 2) {
                        content = formatMessage({id: 'subject.dispensing.realDispensingConfirmDTP'})
                    }
                }

                let data = {
                    ...value,
                    // expiration: value.expiration ? moment(value.expiration).format('YYYY-MM-DD') : "",
                    expiration: expirationDateValue ? expirationDateValue : "",
                    "subject_id": realDispensingData.subjectId,
                    "visit_id": realDispensingData.id,
                }
                if (info) {
                    data["be_other"] = {name:info?.name,batch:info?.batch,expiration_date:info?.expirationDate}
                }
                CustomConfirmModal({
                    title: formatMessage({id: 'subject.dispensing.realDispensingTip'}),
                    content: content,
                    okText:formatMessage({ id: 'common.ok' }),
                    cancelText : formatMessage({ id: 'common.cancel' }),
                    onOk: () =>
                        realDispensingRun(data).then(
                            (response:any) => {
                                save(response)

                            }
                        )
                });
            }
        )
    };

    const [selectOtherMax, setSelectOtherMax] = useSafeState<any>(-1);
    const selectOtherMaxValidator = {
        validator: () => {
            let count = realFrom.getFieldValue("count");
            if (count <= 0 ) {
                return Promise.reject(formatMessage({ id: "input.error.common" }));

            }
            if (count > selectOtherMax) {
                return Promise.reject(formatMessage({ id: "subject.dispensing.medicine.validator" }, {data:selectOtherMax}));
            }
            return Promise.resolve();
        },
    };

    const selectOther = (v:any) => {
        setSelectOther(false)
        // let otherDispensingMedicines = realDispensingData.otherDispensingMedicines?.find((it:any) => it.medicineOtherID === v )
        let info :any = realDispensingData.otherDispensingMedicines?.find((it:any) => it.id === v )
        if (info){
            let otherDispensingMedicines = realDispensingData.otherDispensingMedicines?.find((it: any) => it.id === realFrom.getFieldValue("number"))
            realFrom.setFieldsValue({...realFrom.getFieldsValue(), "name" :otherDispensingMedicines.name})
            setSelectOther(true)
            setSelectOtherMax(otherDispensingMedicines.count)
        }
    }
    const g = useGlobal();

    const layout = {
        labelCol: { style: {   width: g.lang === "en"? "180px": "140px" } }
    };
    return (

        <Modal
            centered
            className="custom-small-modal"
            // className={!selectOthers?"custom-small-modal":"custom-real-dispensing-modal"}
            title={formatMessage({id: 'subject.dispensing.number.register'})}
            visible={realDispensingVisible || edcSubjectVerificationRunLoading}
            onOk={real_dispensing_confirm}
            okText={formatMessage({ id: 'common.ok' })}
            onCancel={() => {
                setExpirationDateValue(null);
                setRealDispensingVisible(false)
                realFrom.resetFields()
            }}
            confirmLoading={realDispensingLoading}
            destroyOnClose={true}
            maskClosable={false}
            keyboard={false}

        >
            <Form form={realFrom} {...layout}>

                <Row gutter={12}>
                    <Col span={realDispensingData.dtp && selectOthers? 24 : 24} >
                        <Form.Item  label={formatMessage({id: 'subject.dispensing.form.number'})} name="number" rules={[{required: true}]}>
                            <Select onChange={selectOther} placeholder={formatMessage({ id: "placeholder.select.common"})}>
                                {
                                    realDispensingData.dispensingMedicines ?
                                        realDispensingData.dispensingMedicines.filter((it:any)=> !it.unReplace).map((value: any) =>
                                            <Select.Option value={value.medicineId}
                                                           key={value.medicineId}>{value.number}</Select.Option>
                                        )
                                        :
                                        null
                                }
                                {
                                    realDispensingData.otherDispensingMedicines?.map((it: any) =>
                                        <Select.Option value={it.id}
                                                       key={it.id}>{it.name+"["+it.batch+"]["+it.expireDate+"]"}</Select.Option>
                                    )
                                }
                            </Select>
                        </Form.Item>
                    </Col>


                </Row>
                <Form.Item initialValue={1} label={formatMessage({id: 'common.status'})} name="status">

                    {
                        !selectOthers?
                            <Radio.Group>
                                <Radio value={1}><FormattedMessage id="subject.dispensing.medicine.available.frozen" /></Radio>
                                <Radio value={2}><FormattedMessage id="medicine.status.lose" /></Radio>
                            </Radio.Group>
                            :
                            <span><FormattedMessage id="medicine.status.available" /></span>
                    }
                </Form.Item>
                {
                    !selectOthers ?
                        <Form.Item
                                   label={formatMessage({id: 'subject.dispensing.form.realNumber'})}

                                   name="real_number" rules={[{required: true}]}>
                            <Input placeholder={formatMessage({ id: "placeholder.input.common"})}/>
                        </Form.Item>
                        :
                        <Form.Item label={formatMessage({id: 'subject.dispensing.form.realNumber'})}
                                   rules={[{required: true}]}>
                            <Form.Item  name="name">
                                <Select placeholder={formatMessage({ id: 'placeholder.select.common' })} className="full-width" options={drugNames} />
                            </Form.Item>
                            <Space style={{marginBottom: "0"}} align="baseline">

                                <Form.Item  style={{marginBottom: "0"}} name="count" rules={[{required: true, message:formatMessage({id: 'subject.dispensing.placeholder.input.count'})}, selectOtherMaxValidator]}>
                                    <InputNumber className="full-width" title={formatMessage({id: 'subject.dispensing.placeholder.input.count'})} placeholder={formatMessage({id: 'subject.dispensing.placeholder.input.count'})}/>
                                </Form.Item>
                                <Form.Item style={{marginBottom: "0"}} name="batch" rules={[{required: true, message:formatMessage({id: 'subject.dispensing.placeholder.input.batch'})}]}>
                                    <TipInput title={formatMessage({id: 'subject.dispensing.placeholder.input.batch'})} placeholder={formatMessage({id: 'subject.dispensing.placeholder.input.batch'})}/>
                                </Form.Item>
                                {/* <Form.Item style={{marginBottom: "0"}} name="expiration" rules={[{required: true, message:formatMessage({id: 'subject.dispensing.placeholder.input.expiration'})}]}>
                                    <CustomDateTimePicker value={expirationDateValue} onChange={setExpirationDateValue}></CustomDateTimePicker>
                                </Form.Item> */}
                                {
                                    realDispensingData.dtp && 
                                        <Form.Item style={{marginBottom: "0", width: "90px"}}  name="from" rules={[{required: true, message:formatMessage({id: 'placeholder.select.common'})}]}>
                                            <Select>
                                                <Select.Option value={"site"} key={"site"}>{formatMessage({id:"common.site"})}</Select.Option>
                                                <Select.Option value={"store"} key={"store"}>{formatMessage({id:"storehouse.name"})}</Select.Option>
                                            </Select>
                                        </Form.Item>

                                }
                            </Space>
                            <Form.Item style={{marginTop:"16px", marginBottom: "0"}} name="expiration" rules={[{required: true, message:formatMessage({id: 'subject.dispensing.placeholder.input.expiration'})}]}>
                                {/* <DatePicker placeholder={formatMessage({id: 'subject.dispensing.placeholder.input.expiration'})}/> */}
                                <CustomDateTimePicker value={expirationDateValue} onChange={setExpirationDateValue} ph={'subject.dispensing.placeholder.input.expiration'}></CustomDateTimePicker>
                            </Form.Item>
                        </Form.Item>
                }
                <Form.Item style={{marginBottom: "0px"}} name="remark"
                           label={formatMessage({id: 'common.remark'})}>
                    <TextArea placeholder={formatMessage({id: 'common.required.prefix'})} allowClear
                              className="full-width" autoSize={{minRows: 2, maxRows: 6}} maxLength={500}/>
                </Form.Item>
            </Form>
        </Modal>
    )
}
