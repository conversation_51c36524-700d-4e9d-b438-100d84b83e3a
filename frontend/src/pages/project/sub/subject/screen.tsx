import React from "react";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {<PERSON>ton, Col, DatePicker, Form, Modal, notification, Radio, Row} from "antd";
import {useFetch} from "../../../../hooks/request";
import {useSafeState} from "ahooks";
import {screen, subjectEdcVerification, updateStatus} from "../../../../api/subject";
import {useAuth} from "../../../../context/auth";
import {useGlobal} from "../../../../context/global";
import dayjs from "dayjs";
import {pushScenarioFilter} from "../../../../utils/irt_push_edc_util";
import {CheckCircleOutlined, CloseCircleFilled, InfoCircleFilled} from "@ant-design/icons";
import {CustomConfirmModal} from "../../../../components/modal";
import {EdcVerificationTip} from "./edc_verification_tip";
import {AuthButton} from "../../../common/auth-wrap";

export const Screen = (props:any) => {
    const g = useGlobal()
    const auth = useAuth();
    const connectEdc = auth.project.info.connect_edc;
    const pushMode = auth.project.info.push_mode;
    const edcSupplier = auth.project.info.edc_supplier;
    const pushScenario = auth.project.info.push_scenario !== undefined? auth.project.info.push_scenario.screen_push: false;
    const intl = useTranslation();
    const DatePickers:any= DatePicker
    const {formatMessage} = intl;
    const [visible, setVisible] = useSafeState<any>(false);
    const [record, setRecord] = useSafeState<any>({});
    const [form] = Form.useForm();
    const edc_verification_tip_pt: any = React.useRef();

    const show = (record:any, type: any) => {
        setVisible(true);
        setRecord(record)
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
    };

    const {runAsync:screenRun, loading:screenLoading} = useFetch(screen, {manual: true})

    const {
        runAsync: edcSubjectVerificationRun,
        loading: edcSubjectVerificationRunLoading,
    } = useFetch(subjectEdcVerification, { manual: true });

    const edcTip = (record: any, type: string) => {
        edc_verification_tip_pt.current.show(record, type);
    };

    // todo 校验的提示逻辑参照修改操作，后续需要更改
    const edc_screen_verification = () => {
        form.validateFields()
            .then(values => {
                if (values["screenTime"] != null) {
                    values["screenTime"] = dayjs(values["screenTime"]).format("YYYY-MM-DD")
                }
                if (values["icfTime"] != null){
                    values["icfTime"] = dayjs(values["icfTime"]).format("YYYY-MM-DD")
                }
                edcSubjectVerificationRun(
                    {
                        "id": record.id,
                        ...values,
                    }
                ).then((resp: any) => {
                    if (!resp.data.linkStatus) {         // 受试者接口请求异常
                        save();
                        notification.open({
                            message: (
                                <div
                                    style={{
                                        fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                        fontSize: "14px",
                                    }}
                                >
                                    <CheckCircleOutlined
                                        style={{
                                            color: "#00BB00",
                                            paddingRight: "8px",
                                        }}
                                    />
                                    {formatMessage({ id: "common.success" })}
                                </div>
                            ),
                            description: (
                                <div style={{ paddingLeft: "20px", color: "#646566" }}>
                                    {formatMessage({
                                        id: "subject.edc.interface.error",
                                    })}
                                </div>
                            ),
                            duration: 5,
                            placement: "top",
                            style: {
                                width: "720px",
                                background: "#F0FFF0",
                                borderRadius: "4px",
                            },
                        });
                    } else {
                        if (resp.data.subjectIsExist) {   // EDC存在当前修改的受试者
                            if (!resp.data.edcMapping) {   // EDC没配置映射关系
                                Modal.confirm({
                                    centered: true,
                                    title: formatMessage({ id: "common.please.confirm" }),
                                    icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                    content: formatMessage({ id: "subject.edc.no.mapping" }),
                                    onOk: () => {
                                        save();
                                    },
                                    cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                    okText: formatMessage({ id: "subject.edc.continue.screen" }),
                                });
                            } else {   // EDC配置了映射关系
                                let errText = "";
                                if (resp.data.edcSubjectStatus === 2) {    // 筛选失败
                                    errText = formatMessage({ id: "subject.status.screen.fail" });
                                } else if (resp.data.edcSubjectStatus === 5) {  // 已退出
                                    errText = formatMessage({ id: "subject.edc.subject.exited" });
                                } else if (resp.data.edcSubjectStatus === 6) {  // 完成研究
                                    errText = formatMessage({ id: "subject.status.finish" });
                                } else if (resp.data.edcSubjectStatus === 8) {  // 筛选中-不可随机
                                    errText = formatMessage({ id: "subject.edc.subject.screen.no.random" });
                                }
                                if (errText !== "") {
                                    let content = formatMessage({ id: "subject.edc.subject" }) +
                                        errText +
                                        formatMessage({ id: "subject.edc.subject.continue.update" });

                                    Modal.confirm({
                                        centered: true,
                                        title: formatMessage({ id: "common.please.confirm" }),
                                        icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                        content: content,
                                        onOk: () => {
                                            edc_push_check(resp);
                                        },
                                        cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                        okText: formatMessage({ id: "subject.edc.continue.screen" }),
                                    });
                                } else {
                                    edc_push_check(resp);
                                }
                            }
                        } else {  // EDC不存在当前修改的受试者
                            let pushTip = "";
                            if (resp.data.subjectRegisterPushIn) {     // 登记推送中
                                pushTip = formatMessage({ id: "subject.edc.register.push.centre" });
                            } else {   // 非登记推送中
                                pushTip = formatMessage({ id: "subject.edc.screen.no.subject" });
                            }
                            if (pushTip !== "") {
                                Modal.confirm({
                                    centered: true,
                                    title: formatMessage({ id: "common.please.confirm" }),
                                    icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                    content:  (pushTip === formatMessage({ id: "subject.edc.screen.no.subject" }) ?
                                            <>
                                                <span style={{color: "#ff4d4f"}}>{formatMessage({ id: "subject.edc.no.subject" })}</span>
                                                <span>{formatMessage({ id: "subject.edc.screen.no.subject" })}</span>
                                            </>
                                            :pushTip
                                    ),
                                    onOk: () => {
                                        save();
                                    },
                                    cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                    okText: formatMessage({ id: "subject.edc.continue.screen" }),
                                });
                            } else {
                                Modal.confirm({
                                    centered: true,
                                    title: formatMessage({ id: "common.please.confirm" }),
                                    icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                                    content: formatMessage({ id: "subject.edc.update.no.subject" }),
                                    onOk: () => {
                                        save();
                                    },
                                    cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                                    okText: formatMessage({ id: "subject.edc.continue.screen" }),
                                });
                            }
                        }
                    }
                });
            })
            .catch(() => { })

    };

    const edc_push_check = (resp: any) => {
        let pushTip = "";
        if (resp.data.subjectRegisterPushIn) {     // 登记推送中
            pushTip = formatMessage({ id: "subject.edc.register.push.centre" });
        } else if (resp.data.subjectPushIn) {   // 非登记推送中
            pushTip = formatMessage({ id: "subject.edc.push.centre" });
        }

        if (pushTip !== "") {  // 存在推送中的数据
            Modal.confirm({
                centered: true,
                title: formatMessage({ id: "common.please.confirm" }),
                icon: <InfoCircleFilled style={{ color: "#FFAE00" }} />,
                content: pushTip,
                onOk: () => {
                    save();
                },
                cancelText: formatMessage({ id: "subject.edc.return.modification" }),
                okText: formatMessage({ id: "subject.edc.continue.screen" }),
            });
        } else {     // 不存在推送中的数据直接弹数据对比框
            edcTip(resp.data, "screen");
        }
    };


    // 受试者筛选
    const save = () => {
        form.validateFields()
            .then(values => {
                if (values["screenTime"] != null) {
                    values["screenTime"] = dayjs(values["screenTime"]).format("YYYY-MM-DD")
                }
                if (values["icfTime"] != null){
                    values["icfTime"] = dayjs(values["icfTime"]).format("YYYY-MM-DD")
                }
                screenRun({
                    "id": record.id,
                    "roleId": auth.project.permissions.role_id,
                    ...values,
                }).then(
                    () => {
                        props.refresh();
                        hide();
                    }
                )
            })
            .catch(() => { })
    };

    React.useImperativeHandle(props.bind, () => ({show}));

    const formItemLayout = {
        labelCol: { style: { width: intl.locale === "zh"? "150px": "210px"} },
    };

    return (
        <>
            <Modal
                className="custom-little-modal"
                title={<FormattedMessage id="subject.screen.title" />}
                open={visible}
                centered
                maskClosable={false}
                onCancel={hide}
                destroyOnClose={true}
                footer={
                    <Row justify="end">
                        <Col style={{marginRight:"16px"}}>
                            <Button onClick={hide}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                        </Col>
                        <Col>
                            {pushScenarioFilter(connectEdc, pushMode, edcSupplier, pushScenario)? (
                                <AuthButton
                                    show
                                    previewProps={{disabledClick: true}}
                                    loading={edcSubjectVerificationRunLoading || screenLoading}
                                    onClick={edc_screen_verification}
                                    type={"primary"}
                                >
                                    {formatMessage({ id: "common.ok" })}
                                </AuthButton>
                            ) : (
                                <AuthButton
                                    show
                                    previewProps={{disabledClick: true}}
                                    onClick={ save } type="primary" loading={screenLoading}
                                >
                                    <FormattedMessage id="common.ok" />
                                </AuthButton>
                            )}
                        </Col>
                    </Row>
                }
            >
                <Form form={form} {...formItemLayout}>
                    {auth.project.info.type === 2 && (
                        <Col>
                            <Form.Item
                                label={formatMessage({
                                    id: "projects.second",
                                })}
                            >
                                {record.cohort?.type === 1?record.cohortName + " - " + record.reRandomName:record.cohortName}
                            </Form.Item>
                        </Col>
                    )}
                    {auth.project.info.type === 3 && (
                        <Col>
                            <Form.Item
                                label={formatMessage({
                                    id: "common.stage",
                                })}
                                style={{marginBottom:0}}
                            >
                                {record.cohortName}
                            </Form.Item>
                        </Col>
                    )}
                    <Form.Item label={record.attribute?((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText)))):formatMessage({id:"subject.number"}) }>
                        {record.shortname}
                    </Form.Item>
                    <Form.Item rules={[{ required: true }]} label={formatMessage({id: "subject.screen.field"})} name={"isScreen"} style={{marginBottom:16}}>
                        <Radio.Group >
                            <Radio value={true}><FormattedMessage id="common.yes"/></Radio>
                            <Radio value={false}><FormattedMessage id="common.no"/></Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: 'subject.screen.time.field'})} name="screenTime" >
                        <DatePickers
                            disabledDate={(current: any) => {
                                return current < dayjs().year(2020).month(0).date(0) || current > dayjs();
                            }}
                            format={"YYYY-MM-DD"} placeholder={formatMessage({id: 'placeholder.select.common'})} className="full-width"/>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: 'subject.screen.ICF.field'})} name="icfTime" style={{marginBottom:0}}>
                        <DatePickers
                            disabledDate={(current: any) => {
                                return current < dayjs().year(2020).month(0).date(0) || current > dayjs();
                            }}
                            format={"YYYY-MM-DD"} placeholder={formatMessage({id: 'placeholder.select.common'})}
                            className="full-width"/>
                    </Form.Item>
                </Form>
            </Modal>

            <EdcVerificationTip
                bind={edc_verification_tip_pt}
                refresh={props.refresh}
                screen_save={save}
            />
        </>
    )
};