
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {useSubject} from "./context";
import {<PERSON>ton, Col, Divider, Form, InputNumber, Row, Select, Tooltip} from "antd";
import {CustomCol, CustomerForm, GetDtpOption, ValidatorList} from "./drug_label";
import {CloseCircleFilled, MinusCircleFilled, PlusOutlined} from "@ant-design/icons";
import React, {useEffect} from "react";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {getFormulaMedicine} from "../../../../api/dispensing";
import {useAuth} from "../../../../context/auth";
import {atuFormula, getFormulaMedicines, showDTPend, updateFormValue} from "./get_formula_medicine";
import {useGlobal} from "../../../../context/global";
import {permissions} from "../../../../tools/permission";
import {DTPOption, DTPOptionIntl} from "../../../../data/data";
import {useAtom} from "jotai/index";
import {doseSelectAtom} from "./ctx";

interface drugOpenProp {
    drugNameOption: any;
    record:any;
    visit_id:any;
    drugNameFrom:any;
    drugFormFrom:any;
    setDrugNameFrom:any;
    drugOption:any;
    type?: number;
    form?: any;
}

export const DrugOpen = (props: drugOpenProp) => {
    const {form, drugNameOption, record, visit_id, drugNameFrom, setDrugNameFrom, drugOption, drugFormFrom} = props
    const g = useGlobal();
    const intl = useTranslation();
    const {formatMessage, } = intl;
    const subject = useSubject();

    const [itemLen, setItemLen] = useSafeState<any>(false);


    const [drugSelect, setDrugSelect] = useSafeState<any>({});

    const {runAsync: getFormulaMedicineRun, loading: getFormulaMedicineLoading} = useFetch(getFormulaMedicine, {manual: true, debounceWait: 500})
    const auth = useAuth();
    const envId = auth.env.id;
    const [updateCount, setUpdateCount] = useSafeState<boolean>(permissions(auth.project.permissions, "operation.subject.medicine.formula.update"))

    const get_formula_medicine = (index:number, formKey:any, value:any) => {
        setUpdateCount(!permissions(auth.project.permissions, "operation.subject.medicine.formula.update"))

        getFormulaMedicines(index,formKey,value,form,getFormulaMedicineRun,drugOption, drugNameOption,visit_id, record, subject, record.cohortId, envId,drugFormFrom, drugNameFrom)
        // props.form.validateFields(["drugLabel"]).then()
        // props.form.validateFields(["openSetting"]).then()

    }

    const updateDrugLabelForm = (index:any, value:any) => {
        let updateSet = false
        let updateIndex = -1
        let chlidIndex = -1
        for (let i = 0; i < form.getFieldsValue().openSetting[index].labels?.length; i++) {
            if (form.getFieldsValue().openSetting[index].labels[i]?.name !== undefined){
                updateSet = true
                updateIndex = index
                chlidIndex = i
                break
            }
        }
        if (updateSet){
            // 组间内 已经选了一个标签
            drugSelect[updateIndex] = drugNameOption.filter((item:any)=>
                item.id === drugNameOption[form.getFieldsValue().openSetting[updateIndex].labels[chlidIndex].name].id
            )
            setDrugSelect(drugSelect)
            drugNameFrom[index] = drugNameOption[form.getFieldsValue().openSetting[updateIndex].labels[chlidIndex].name]?.customerCalculation
            updateFormValue(drugNameOption[form.getFieldsValue().openSetting[updateIndex].labels[chlidIndex].name]?.customerCalculation, form, index, 2)
            atuFormula(index, getFormulaMedicineRun, envId, record.cohortId, record, visit_id, drugNameOption[value].id,subject, 2, form, drugOption, drugNameOption)

        }else {
            // 一个名称都没选择
            let drugName:any[] = []
            form.getFieldsValue().openSetting?.forEach(
                (item:any, i:any) => {
                    if (i === index){
                        item.form = {}
                    }
                    drugName.push(item)
                }
            )
            delete subject.formulaNameRes[index]
            subject.setFormulaNameRes({...subject.formulaNameRes})
            delete drugSelect[index];
            setDrugSelect({...drugSelect})
            delete drugNameFrom[index]
        }
    }

    const changeName = (value: any, name: any, childName:any) => {
        if (value !== undefined) {
            subject.setSelectOption({
                ...subject.selectOption,
                [name+""+childName]: props.drugNameOption[value],
            });
            let newFormListOption: any = [];
            props.form
                .getFieldsValue()
                .openSetting?.map((it: any, index: any) => {
                if (index === name) {
                    it.labels[childName] = {
                        name: it.labels[childName].name,
                        count:
                            props.drugNameOption[value]
                                .custom_dispensing_number?.length === 1
                                ? props.drugNameOption[value]
                                    .custom_dispensing_number[0].value
                                : null,
                        dtp:
                            props.drugNameOption[value]
                                .dtp?.length === 1
                                ? props.drugNameOption[value]
                                    .dtp[0]
                                : null,
                    }
                    newFormListOption.push(it);

                } else {
                    newFormListOption.push(it);
                }
            });
            props.form.setFieldsValue({
                ...props.form.getFieldsValue,
            });
        } else {
            delete subject.selectOption[name+""+childName];
            subject.setSelectOption({...subject.selectOption});

        }
        if (props.type !== 2){
            updateDrugLabelForm(name, value)
        }
        props.form.validateFields(["drugLabel"]).then()
        props.form.validateFields(["openSetting"]).then()

    };

    const childAddInit = (add:any, name:any) => {
        add()
        let childName = form.getFieldsValue().openSetting[name].labels.length -1
        delete subject.selectOption[name+""+childName];
        subject.setSelectOption({...subject.selectOption});
    }

    const addInit = (add:any) => {
        setItemLen(form.getFieldsValue().openSetting?.length === 1)
        add()
        let data :any = form.getFieldsValue().openSetting?.slice(0,-1)
        form.setFieldsValue({...form.getFieldsValue, "openSetting": [...data,{labels:[{}]}]})
        delete drugSelect[form.getFieldsValue().openSetting.length];
        setDrugSelect({...drugSelect})
        props.form.validateFields(["drugLabel"]).then()
        props.form.validateFields(["openSetting"]).then()

    }

    const removeInit = (index:any, remove:any) => {
        setItemLen(form.getFieldsValue().openSetting?.length === 1)
        remove(index)
        let max = -1
        for(var key in subject.selectOption){
            if (Number(key) > index) {
                let newkey = Number(key) -1
                subject.selectOption[newkey] = subject.selectOption[key]
                if (max < Number(key)) {
                    max = Number(key)
                }
            }
        }
        delete subject.selectOption[max]
        drugSelectChange()

        let objectKey = Object.keys(subject.formulaNameRes)
        objectKey.filter(i => i !== index+"").forEach((item:any,i)=>{
            subject.formulaNameRes[i] = subject.formulaNameRes[item]
        })
        delete subject.formulaNameRes[objectKey.length - 1]
        subject.setFormulaNameRes({...subject.formulaNameRes})
        if (Array.isArray(drugNameFrom)) {
            setDrugNameFrom(drugNameFrom?.filter((item:any, i:any)=> index !== i))

        }else{
            let drugNameFromKey = Object.keys(drugNameFrom)
            drugNameFromKey.filter(i => i !== index+"").forEach((item:any,i)=>{
                drugNameFrom[i] = drugNameFrom[item]
            })
            delete drugNameFrom[objectKey.length - 1]
            setDrugNameFrom(drugNameFrom)
        }

        setNumberSpec()
        props.form.validateFields(["drugLabel"]).then()
        props.form.validateFields(["openSetting"]).then()

    }

    const setNumberSpec = () => {
        form.getFieldsValue().openSetting.forEach(
            (labels:any, index:any) =>{
                labels.labels?.forEach(
                    (item:any, childIndex:any)=>{
                        if (item.name || item.name === 0){
                            subject.selectOption[index+""+childIndex] = props.drugNameOption[item.name]
                        }else{
                            subject.selectOption[index+""+childIndex] = {}
                        }
                    }
                )
            }
        )
    }

    const childRemoveInit = (remove:any, name:any, childName:any)=>{
        remove(childName)
        drugSelectChange()
        props.form.validateFields(["drugLabel"]).then()
        props.form.validateFields(["openSetting"]).then()

    }
    const setDrugSelectFun = () => {
        //  剂量控制 需去掉多余选项
        drugSelectChange()
    }


    const selectLen = () => {
        let length = 0;
        if (form.getFieldsValue()?.openSetting){
            [...form.getFieldsValue()?.openSetting]?.map((drug:any)=>
                drug.labels?.map((item:any)=> {
                    length ++
                })
            )
        }


        return length
    }

    const drugSelectChange = () => {
        //  剂量控制 需去掉多余选项
        if (form.getFieldsValue()?.openSetting){
            let newSelect :any = {}
            let drugLabels = [...form.getFieldsValue()?.openSetting];
            drugLabels.forEach((it:any, number:any)=>{
                it.labels.forEach((label:any,childIndex:any) => {
                    if (label.name || label.name === 0) {
                        newSelect[number+""+childIndex] = drugNameOption.find((item:any)=>
                            item.key === label.name
                        )
                    }
                })
            })
            setDrugSelect({...newSelect})
            subject.setSelectOption({...newSelect})
            //
        }


    }

    const [dose,  ] = useAtom(doseSelectAtom);

    useEffect(() => {
        if (subject.updateAdd !== 0 ){
            setDrugSelectFun()
        }
    },[subject.updateAdd])

    return (
        <Form.Item
            style={{marginBottom: "22px"}}
            rules={[{required: true}]}
            required={props.type === 2}
            label={formatMessage({id: "subject.dispensing.drugName.dispensing"})}
            className="full-width"
        >
            <Form.List
                name={"openSetting"}
                rules={[
                    ValidatorList(props, 1, props.form, []),
                ]}
            >
                {(fields, {add, remove}, {errors}) => (
                    <>
                        {fields.map((field, index) => (
                            <CustomCol key={field.key}>
                                {
                                    subject.formulaForm
                                        .filter((item:any) => {
                                            return  drugNameFrom[field.name]?.includes("{"+item.variable+"}")
                                        })
                                        .map(
                                            (item:any) =>
                                                <CustomerForm item={item} field={field} get_formula_medicine={get_formula_medicine}/>
                                        )
                                }

                                {
                                    subject.formulaNameRes[index]?.map(
                                        (item:any)=>
                                            !item?.customerFormula?
                                                null
                                                :
                                            item?.name?.find((it:any) => it.outSize === true) ?
                                                <div

                                                    style={{
                                                        borderRadius: '2px 2px 2px 2px',
                                                        backgroundColor:"#fff7e6",
                                                        padding: '8px',
                                                        height: "auto",
                                                        marginLeft:subject.labelWidth,
                                                        marginTop: "12px",
                                                        marginBottom: "12px",
                                                        alignItems: 'center',
                                                        opacity: "0.1px",
                                                        color: "#677283",
                                                    }}
                                                >计算错误，计算值无法匹配发放数量。</div>
                                                :

                                            item?.customerFormula !== 0 &&
                                            <div
                                                style={{
                                                    borderRadius: '2px 2px 2px 2px',
                                                    backgroundColor:"#fff7e6",
                                                    padding: '8px',
                                                    height: "auto",
                                                    marginLeft:subject.labelWidth,
                                                    marginTop: "12px",
                                                    marginBottom: "12px",
                                                    alignItems: 'center',
                                                    opacity: "0.1px",
                                                    color: "#677283",
                                                }}
                                            >
                                        <span >
                                            <svg className="iconfont" width={16} height={16} style={{marginBottom:"-4px",}}>
                                                    <use xlinkHref="#icon-jinggao"></use>
                                                </svg>
                                        </span>
                                                <span style={{
                                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                    fontSize: "12px",
                                                    fontWeight: 400,
                                                    color: "#677283",
                                                }}>{formatMessage({id: "subject.dispensing.drug.formula.tip.start"})}</span>
                                                <span style={{
                                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                    fontSize: "12px",
                                                }}>{item?.customerFormula}</span>

                                                <span style={{
                                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                    fontSize: "12px",
                                                    fontWeight: 400,
                                                    color: "#677283",
                                                }}>{formatMessage({id: "subject.dispensing.drug.customer.formula.tip", },{ unit:item?.unit, spec:item?.spec.join(",")})}</span>
                                            </div>
                                    )

                                }


                                <Form.Item
                                    name={[field.name, "labels"]}
                                    label={formatMessage({id:"subject.dispensing.drugName"})}
                                    style={{marginTop:12}}
                                    labelCol={{style:{ whiteSpace: "pre-wrap",width:subject.labelWidth, textAlign:"right", height:"auto"}}}
                                >

                                <Form.List name={[field.name, "labels"]}>
                                    {(childFields, {add:childAdd, remove:childRemove}, {errors}) => (
                                        <>
                                            {childFields.map((childField:any, childIndex) => (

                                                <Row key={childField.key}
                                                     style={{marginBottom:12}}
                                                >
                                                    <Col style={{flex: 1}}>
                                                        <Row>

                                                        <Form.Item
                                                            style={{flex:1}}
                                                            {...childField}
                                                            labelCol={{style:{ whiteSpace: "pre-wrap",width:subject.labelWidth, textAlign:"right", height:"auto"}}}
                                                            name={[childField.name, "name"]}
                                                            fieldKey={[childField.name, "name"]}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: formatMessage({
                                                                        id: "shipment.order.create.info",
                                                                    }),
                                                                },
                                                            ]}
                                                        >
                                                            <Select
                                                                placeholder={formatMessage({
                                                                    id: "placeholder.select.common",
                                                                })}
                                                                className={"full-width"}
                                                                allowClear
                                                                onChange={(value: any) => {
                                                                    changeName(value, field.name, childField.name)
                                                                }}
                                                                disabled={!dose && props.type !== 2}
                                                            >
                                                                {
                                                                    drugSelect[index]?
                                                                        drugSelect[index]?.map(
                                                                            (value: any, index: any) => (
                                                                                <Select.Option
                                                                                    key={value.name + index}
                                                                                    value={value.key}
                                                                                    disabled = {
                                                                                        form.getFieldsValue()?.openSetting && [...form.getFieldsValue()?.openSetting]?.find((drug:any)=>
                                                                                            drug.labels?.find((item:any)=> item?.name === value.key)
                                                                                        )
                                                                                    }
                                                                                >
                                                                                    {value.name}
                                                                                    {value.is_other &&
                                                                                        props.drugNameOption?.find(
                                                                                            (item: any) =>
                                                                                                item.name ===
                                                                                                value.name &&
                                                                                                !item.is_other
                                                                                        ) &&
                                                                                        "(unnumbered)"}
                                                                                </Select.Option>
                                                                            )
                                                                        )
                                                                        :
                                                                        props.drugNameOption?.map(
                                                                            (value: any, index: any) => (
                                                                                <Select.Option
                                                                                    key={value.name + index}
                                                                                    value={value.key}
                                                                                    disabled = {
                                                                                        form.getFieldsValue()?.openSetting && [...form.getFieldsValue()?.openSetting]?.find((drug:any)=>
                                                                                            drug.labels?.find((item:any)=> item?.name === value?.key)
                                                                                        )
                                                                                    }
                                                                                >
                                                                                    {value.name}
                                                                                    {value.is_other &&
                                                                                        props.drugNameOption?.find(
                                                                                            (item: any) =>
                                                                                                item.name ===
                                                                                                value.name &&
                                                                                                !item.is_other
                                                                                        ) &&
                                                                                        "(unnumbered)"}
                                                                                </Select.Option>
                                                                            )
                                                                        )
                                                                }
                                                            </Select>
                                                        </Form.Item>
                                                        <Form.Item
                                                            className={"full-width"}
                                                            {...childField}
                                                            style={{flex:1, marginLeft:12}}
                                                            rules={[
                                                                {
                                                                    // required: !subject.formulaNameRes[index]?.find(
                                                                    //     (item:any)=>
                                                                    //         item?.name?.find((it:any) => it.outSize === true)),
                                                                    required: true,

                                                                    message: formatMessage({
                                                                        id: "common.required.prefix",
                                                                    }),
                                                                },
                                                                {
                                                                    validator: (rule: any, value: any, callback: any) => {
                                                                        const reg = /^(\d+|(\d+~\d+))(,(\d+|(\d+~\d+)))*$/;
                                                                        if (props.type !== 2) {
                                                                            if ((value || value === 0) && !subject.selectOption[field.name+""+childField.name]?.custom_dispensing_number.find((item: any) => value === item)) {
                                                                                return Promise.reject(
                                                                                    formatMessage({id: "subject.dispensing.drug.input.error"})
                                                                                );
                                                                            }
                                                                        }else{
                                                                            let maxNumber = NaN
                                                                            if (subject.selectOption[field.name+""+childField.name]?.max){
                                                                                maxNumber = subject.selectOption[field.name+""+childField.name].max
                                                                            }
                                                                            if (value === 0){
                                                                                return Promise.reject(
                                                                                    formatMessage({id: "subject.dispensing.drug.input.error"})
                                                                                );
                                                                            }

                                                                            if (value && value > maxNumber) {
                                                                                return Promise.reject(
                                                                                    formatMessage({id: "subject.dispensing.drug.limit"})
                                                                                );
                                                                            }
                                                                        }

                                                                        return Promise.resolve();
                                                                    },
                                                                },
                                                            ]}
                                                            name={[childField.name, "count"]}
                                                            fieldKey={[childField.name, "count"]}
                                                        >
                                                            <InputNumber
                                                                disabled={
                                                                    subject.selectOption[field.name+""+childField.name]?.automaticRecode
                                                                    &&
                                                                    !(
                                                                        subject.selectOption[field.name+""+childField.name]?.automaticRecode &&
                                                                        permissions(auth.project.permissions, "operation.subject.medicine.formula.update")
                                                                    )
                                                                    ||
                                                                    subject.formulaNameRes[index]?.find(
                                                                        (item:any)=>
                                                                            item?.name?.find((it:any) => item?.customerFormula && it.outSize === true && it.name === subject.selectOption[field.name+""+childField.name]?.name))
                                                                }
                                                                controls={false}
                                                                placeholder={formatMessage({
                                                                    id: "subject.dispensing.placeholder.input.dispensing.count",
                                                                })}
                                                                addonAfter={subject.selectOption[field.name+""+childField.name]?.spec}
                                                                step={1}
                                                                className={"full-width"}
                                                            />
                                                        </Form.Item>
                                                        </Row>
                                                            {
                                                                record?.attribute?.info?.dtpRule === 1 && subject.selectOption[field.name+""+childField.name]?.dtp &&
                                                                <Row  style={{
                                                                    display:

                                                                        showDTPend(subject, field.name, childFields, form) < 0 && showDTPend(subject, field.name, childFields, form) !== -3
                                                                            ?
                                                                        "initial":
                                                                        "none"

                                                                }}>

                                                                    <Form.Item
                                                                        style={{flex:1, marginTop:12}}
                                                                        {...childField}
                                                                        name={[childField.name, "dtp"]}
                                                                        rules={[{
                                                                            required: true,
                                                                            message: formatMessage({
                                                                                id: "drug.batch.treatmentDesign.openSetting",
                                                                            }),
                                                                        },]}

                                                                    >
                                                                        {
                                                                            <Select placeholder={formatMessage({id: "logistics.dispensing.method"})} optionLabelProp="label" disabled={subject.selectOption[field.name+""+childField.name]?.dtp?.length === 1}>
                                                                                {
                                                                                    subject.selectOption[field.name+""+childField.name]?.dtp?.map((it:any)=>
                                                                                        GetDtpOption(it, formatMessage)
                                                                                    )
                                                                                }
                                                                            </Select>
                                                                        }
                                                                    </Form.Item>
                                                                </Row>


                                                            }
                                                    </Col>
                                                    <Col
                                                        style={{bottom: 8, marginLeft: '6px', display: 'flex', alignItems: 'center'}}
                                                    >
                                                        {
                                                            record?.attribute?.info?.dtpRule === 1 && subject.selectOption[field.name+""+childField.name]?.dtp &&
                                                            showDTPend(subject, field.name, childFields, form)  < 0 && showDTPend(subject, field.name, childFields, form) !== -3 ?
                                                            <Divider type={"vertical"}style={{paddingRight:0,  borderLeft: "1px solid #E0E1E2", height:"90%", }} />
                                                                :
                                                                <Divider type={"vertical"}style={{paddingRight:0,  borderLeft: "0px solid #E0E1E2", height:"90%", }} />

                                                        }
                                                        {
                                                            (dose || props.type === 2) && props.drugNameOption?.length > selectLen() &&
                                                            <Tooltip placement="top" title={formatMessage({id: "common.add"})}>
                                                                <svg className="iconfont" width={16} height={16} onClick={() => {
                                                                    childAddInit(childAdd, field.name)
                                                                }} style={{color: "#fe5b5a", marginTop: 12, cursor: "pointer"}} fill={"#999999"}>
                                                                    <use xlinkHref="#icon-zengjia"></use>
                                                                </svg>
                                                            </Tooltip>
                                                        }
                                                        {
                                                            childFields.length !== 1 &&
                                                            <Tooltip style={{backgroundColor:"#772828"}} placement="top" title={formatMessage({id: "common.delete"})}>
                                                                <svg className="iconfont" width={16} height={16} onClick={() => {
                                                                    childRemoveInit(childRemove, field.name, childField.name)
                                                                }} style={{marginLeft: 12, marginTop: 12, cursor: "pointer"}} fill={"#999999"}>
                                                                    <use  xlinkHref="#icon-shanchu"></use>
                                                                </svg>
                                                            </Tooltip>
                                                        }
                                                    </Col>

                                                </Row>

                                            ))}
                                            {
                                                record?.attribute?.info?.dtpRule === 1 && showDTPend(subject, field.name, childFields, form) > - 1 &&
                                                <Row >

                                                    <Form.Item
                                                        style={{marginRight:childFields.length !== 1?65:38}}
                                                        className={"full-width"}
                                                    >
                                                        {
                                                            <Select optionLabelProp="label" placeholder={formatMessage({id: "logistics.dispensing.method"})} disabled={true} value={showDTPend(subject, field.name, childFields, form)}>
                                                                {
                                                                    DTPOption?.map((it:any)=>
                                                                        GetDtpOption(it.value, formatMessage)
                                                                    )
                                                                }
                                                            </Select>
                                                        }
                                                    </Form.Item>
                                                </Row>


                                            }
                                        </>
                                        )}
                                </Form.List>
                                </Form.Item>

                                <CloseCircleFilled
                                    onClick={() => {
                                        removeInit(field.name, remove)

                                    }}
                                    className="delete-icon"
                                    style={{color: "#fe5b5a"}}

                                />
                            </CustomCol>
                        ))}
                        {
                            (
                                subject.nameAdd
                                ||
                                itemLen
                            ) &&
                        <Form.Item style={{marginBottom: 0}} className={"full-width"}>
                            <Button
                                type="dashed"
                                onClick={() => addInit(add)}
                                block
                                icon={<PlusOutlined/>}
                            >
                                <FormattedMessage id={"common.addTo"}/>
                            </Button>

                        </Form.Item>
                        }
                        <Form.ErrorList errors={errors}/>
                    </>
                )}
            </Form.List>
        </Form.Item>
    );
};