import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {
    Alert,
    Button,
    Col, Collapse, Descriptions,
    Empty,
    Form,
    Input,
    message,
    Modal,
    Radio,
    Result,
    Row,
    Select, Spin,
    Steps,
    Tabs,
    Tooltip
} from "antd";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import {resendSms, urgentUnblindingApplication, urgentUnblindingApproval} from "../../../../api/subject";
import { useSafeState, useUpdateEffect } from "ahooks";
import { useGlobal } from "../../../../context/global";
import { useSubject } from "./context";
import moment from "moment";
import {permissions, permissionsCohort} from "../../../../tools/permission";
import styled from "@emotion/styled";
import {Title, TitleIcon} from "../../../../components/title";
import ReactToPrint from "react-to-print";
import EmptyImg from "../../../../images/empty.png";
import React, {useEffect} from "react";
import {useAtom} from "jotai/index";
import {
    confirmVisibleAtom,
    currentDispensingAtom,
    currentDispensingIDAtom,
    currentMedicineAtom,
    showUnBlindAtom,
    showUnBlindIDAtom
} from "./ctx";
import {
    GetMedicineUnBlind,
    medicineUnblindingApplication,
    UnblindingApproval
} from "../../../../api/dispensing";
import {CaretDownOutlined, CaretUpOutlined} from "@ant-design/icons";
import {unblindedApprovalStatus} from "../../../../data/data";

export const DispensingUnblindingApproval = (props:any) => {
    const g = useGlobal()
    const auth = useAuth();
    const ctx = useSubject()
    const intl = useTranslation();
    const { formatMessage } = intl;
    const [title, setTitle] = useSafeState<any>(formatMessage({id: "subject.unblinding.ip"}));
    const [form] = Form.useForm();
    const [approvalForm] = Form.useForm();
    const [reasonType, setReasonType] = useSafeState<any>(-1)
    const [approvalType, setApprovalType] = useSafeState<any>(0)
    const [approvalAgree, setApprovalAgree] = useSafeState<any>(0)
    const [applicationResultVisible, setApplicationResultVisible] = useSafeState<any>(false)
    const [approvalResultVisible, setApprovalResultVisible] = useSafeState<any>(false)
    const [approvalNumber, setApprovalNumber] = useSafeState<any>("")
    const [applicationTime, setApplicationTime] = useSafeState<any>("")
    const [approvalTime, setApprovalTime] = useSafeState<any>("")
    const [approvalUserOne, setApprovalUserOne] = useSafeState<any>("")
    const [approvalUserTwo, setApprovalUserTwo] = useSafeState<any>("")
    const timeZone = auth.project.info.timeZone !== undefined ? auth.project.info.timeZone : 8
    const [pendingApproval, setPendingApproval] = useSafeState<any>(false)
    const [codeValidate, setCodeValidate] = useSafeState({})
    const [passwordValidate, setPasswordValidate] = useSafeState({})
    const tab4Footer = {
        footer:
            <Row justify="end">
                <Col>
                    <div>
                        <ReactToPrint
                            trigger={() => {
                                return <Button>{<FormattedMessage id={"common.print"} />}</Button>;
                            }}
                            content={() => ctx.componentRef}
                        />
                    </div>
                </Col>
            </Row>
    }
    const [showUnBlind, setShowUnBlind ] = useAtom(showUnBlindAtom);
    const [dispensingId, setCurrentDispensingId ] = useAtom(currentDispensingIDAtom);
    const [dispensing, setCurrentDispensing ] = useAtom(currentDispensingAtom);
    const [currentMedicine, setCurrentMedicine ] = useAtom(currentMedicineAtom);

    const [tab1, setTab1 ] = useSafeState<any>(false);
    const [tab2, setTab2 ] = useSafeState<any>(false);
    const [tab3, setTab3 ] = useSafeState<any>(false);
    const [tab4, setTab4 ] = useSafeState<any>(false);

    const [footer, setFooter] = useSafeState<any>({footer:null})
    const afterUrgentUnblindingApplicationError = (e:any, params:any) => {
        const contentType = e.headers.get("content-type");
        if (contentType && contentType.indexOf("application/json") > -1) {
            e.json().then(
                (result:any) => {
                    switch (result.code) {
                        case 1000:
                            setCodeValidate({ validateStatus: "error", help: result.msg })
                            break;
                        case 1001:
                            setPasswordValidate({ validateStatus: "error", help: result.msg })
                            break;
                        case 1003:
                            setPasswordValidate({ validateStatus: "error", help: result.msg[0] })
                            setCodeValidate({ validateStatus: "error", help: result.msg[1] })
                            break;
                        default:
                            message.error(result.msg).then()
                            break;
                    }
                }
            );
        }
    }

    const {
        runAsync: medicineUnblindingApplicationRun,
        loading: medicineUnblindingApplicationLoading
    } = useFetch(medicineUnblindingApplication, { manual: true, onError: afterUrgentUnblindingApplicationError })

    const {
        runAsync: unblindingApprovalRun,
        loading: unblindingApprovalLoading
    } = useFetch(UnblindingApproval, { manual: true })



    const hide = () => {
        ctx.setCurrentRecord(null)
        setFooter({ footer: null })
        setApprovalType(0)
        setApprovalAgree(0)
        setReasonType(-1)
        setPendingApproval(false)
        ctx.setUnblindingDetailsSign("")
        setApplicationResultVisible(false)
        setApprovalResultVisible(false)
        ctx.setRefresh(ctx.refresh + 1)
        form.resetFields()
        approvalForm.resetFields()
        setApplicationTime("")
        setApprovalUserOne("")
        setApprovalUserTwo("")
        setCodeValidate({})
        setShowUnBlind(false)
        setCurrentDispensingId("")
        setCurrentDispensing({visitInfo: ""})
        setCurrentMedicine({medicineId: "", number: "", name: ""})
        setTab1(false)
        setTab2(false)
        setTab3(false)
        setTab4(false)
        setUnbindingDetail(null)
        setConfirmVisible(false)
        props.refresh()
    };

    const [, setConfirmVisible ] = useAtom(confirmVisibleAtom);


    // 揭盲申请
    const applicationOrApprovalOrUnblinding = () => {

        if (auth.project.info.unblinding_control === 0 || (auth.project.info.ip_unblinding_sms === 0 && auth.project.info.ip_unblinding_process === 0)){
            setConfirmVisible(true)
            return
        }
        if (pendingApproval) {
            approveUnblinding()
        } else {
            applicationUnblinding()
        }
        //可审批用户
        // approvedUsers();
    };

    const applicationUnblinding = ()=> {
        form.validateFields()
            .then(values => {
                setCodeValidate({})
                setPasswordValidate({})
                medicineUnblindingApplicationRun({
                    subjectId: ctx.currentRecord.id,
                    dispensingId: dispensingId,
                    medicineId: id,
                    roleId: auth.project.permissions.role_id,
                    ...values
                }).then((result:any) => {
                    const resp = result.data
                    if (resp.approvalNumber !== ""){
                        setApprovalNumber(resp.approvalNumber)
                        setApplicationTime(moment.unix(resp.applicationTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss'))
                        setApplicationResultVisible(true)
                    }else{
                        hide()
                        message.success(formatMessage({ id: 'subject.unblinding.success' }))
                    }


                })
            })
    }

    const approveUnblinding = () => {
        approvalForm.validateFields()
            .then(values => {
                unblindingApprovalRun({
                    attributeId: ctx.currentRecord.attribute.id,
                    subjectId: ctx.currentRecord.id,
                    approvalNumber: pendingApproval.number,
                    roleId: auth.project.permissions.role_id,
                    dispensingId: dispensingId,
                    medicineId: id,
                    ...values
                }).then((result:any) => {
                    const resp = result.data;
                    setApprovalTime(moment.unix(resp.approvalTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss'));
                    setApprovalResultVisible(true);
                })
            })
            .catch(error => {
            })
    }
    // 组装可审批用户
    const approvedUsers = (user:any) => {
        // 展示可揭盲的用户信息
        if (user?.length > 0) {
            let puau = [];
            for (let i = 0; i < user?.length; i++) {
                let namePhone = user[i].approvalName + "/" + user[i].approvalPhone;
                puau.push(namePhone);
                if (i == 2) {
                    break
                }
            }
            if (user.length > 3) {
                puau.push("...");
            }
            setApprovalUserOne(
                user.map((ccu:any) =>
                    <Row>
                            <span className={'.ant-steps-step-description'}>
                                {ccu.approvalName}/{ccu.approvalPhone}
                            </span>
                    </Row>
                )
            )
            setApprovalUserTwo(
                puau.map((ccu:string) =>
                    <Row>
                            <span className={'.ant-steps-step-description'}>
                                {ccu}
                            </span>
                    </Row>
                )
            )
        }
    }

    const {runAsync: GetMedicineUnBlindRun, loading: GetMedicineUnBlindLoading} = useFetch(GetMedicineUnBlind, {manual: true});
    const [id, setShowUnBlindID ] = useAtom(showUnBlindIDAtom);
    const [approvals, setApprovals ] = useSafeState<any>([])
    const [unbindingDetail, setUnbindingDetail ] = useSafeState<any>(null)
    const [approvalsUser, setApprovalUser ] = useSafeState<any>([])
    const [number, setNumber ] = useSafeState<any>("")
    const [name, setName ] = useSafeState<any>("")

    useEffect(
        () => {
            if (showUnBlind){
                if (showUnBlind && ctx?.currentRecord?.attribute) {
                    GetMedicineUnBlindRun({id:id, subjectId:ctx.currentRecord.id}).then((res:any) =>
                        {
                            console.log(auth.project.info)

                            setUnbindingDetail(res.data?.unBlinding)
                            setApprovals(res.data?.unblindingApprovals)
                            const approved = res.data?.unblindingApprovals?.find((i:any) => i.status === 1) || (res.data.unBlinding !== null && res.data?.unBlinding?.operationTime !== 0)
                            const needApproval = res.data?.unblindingApprovals?.find((i:any) => i.status === 0) && !approved
                            const needApplication = (!res.data?.unblindingApprovals || res.data?.unblindingApprovals?.every((i:any) => i.status !== 0)) && !approved
                            const tmpTab1 = permissionsCohort(auth.project.permissions, "operation.subject.unblinding-ip-application",ctx.currentRecord?.cohort.status) && needApplication
                            const tmpTab2 = permissionsCohort(auth.project.permissions, "operation.subject.unblinding-ip-approval",ctx.currentRecord?.cohort.status) && needApproval
                            setTab1(tmpTab1)
                            setTab2(tmpTab2)
                            setTab4(approved)
                            setName(res.data?.name)
                            setNumber(res.data?.number)
                            setTab3(permissions(auth.project.permissions, "operation.subject.unblinding-ip-log") && ctx.currentRecord && auth.project.info.unblinding_control && auth.project.info.unblinding_control === 1)
                            if (tmpTab1 || tmpTab2) {
                                setFooter({})
                            }
                            if (approved){
                                setFooter(tab4Footer)
                            }

                            if (needApproval) {
                                setTitle(formatMessage({ id: 'subject.unblinding.approval' }));
                                setPendingApproval(res.data?.unblindingApprovals.find((i:any) => i.status === 0));
                                setApplicationTime(moment.unix(res.data?.unblindingApprovals.find((i:any) => i.status === 0).applicationTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss'))

                            }
                            approvedUsers(res.data?.approvalUsers);
                            setApprovalUser(res.data?.approvalUsers)
                        }
                    )
                }
            }
            //可审批用户
        },
        [showUnBlind]
    );



    return (
        <>
            <Modal
                width={'800px'}
                title={title}
                open={showUnBlind}
                onCancel={hide}
                onOk={applicationOrApprovalOrUnblinding}
                okText={formatMessage({ id: 'common.ok' })}
                destroyOnClose={true}
                centered
                maskClosable={false}
                confirmLoading={medicineUnblindingApplicationLoading}
                {...footer}
            >
                {
                    ctx.currentRecord && (tab1 || tab2 || tab3 || tab4) ?
                        <Tabs  destroyInactiveTabPane={true} onChange={(key) => {
                            setFooter({})
                            if (key === "3") {
                                setFooter({ footer: null })
                            }if (key ==="4") {
                                setFooter(tab4Footer)
                            }
                        }}>
                            {
                                tab1 &&
                                    <Tabs.TabPane
                                        tab={
                                                formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.unblinding-ip' })
                                        }
                                        key="1">
                                        <Alert message={formatMessage({ id: 'subject.unblinding.application.alert.ip' })}
                                               type="warning"
                                               showIcon />
                                        <Form form={form} labelCol={{ span: g.lang === 'en' ? 8 : 4 }}>
                                            {auth.project.info.type === 2 && (
                                                <Form.Item
                                                    label={formatMessage({
                                                        id: "projects.second",
                                                    })}
                                                >
                                                    {ctx.currentRecord.cohort?.type === 1?ctx.currentRecord.cohortName + " - " + ctx.currentRecord.cohort?.reRandomName:ctx.currentRecord.cohortName}
                                                </Form.Item>
                                            )}
                                            {auth.project.info.type === 3 && (
                                                <Form.Item
                                                    label={formatMessage({
                                                        id: "common.stage",
                                                    })}
                                                >
                                                    {ctx.currentRecord.cohortName}
                                                </Form.Item>
                                            )}
                                            <Form.Item label={ctx.currentRecord?.attribute ? ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? (formatMessage({ id: "subject.number" })) : ((ctx.currentRecord.attribute.info.subjectReplaceText !== "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? ctx.currentRecord.attribute.info.subjectReplaceText : ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn !== "") ? (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : formatMessage({ id: "subject.number" })) : (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : ctx.currentRecord.attribute.info.subjectReplaceText)))) : formatMessage({ id: "subject.number" })}>
                                                <span>{ctx.currentRecord.shortname}</span>
                                            </Form.Item>
                                            <Form.Item
                                                label={formatMessage({ id: 'projects.randomization.randomNumber' })}>
                                                <span>{ctx.currentRecord.randomNumber}</span>
                                            </Form.Item>

                                            <Form.Item
                                                label={formatMessage({ id: 'visit.cycle.name' })}>
                                                <span>{renderVisit(dispensing?.visitInfo, dispensing, formatMessage)}</span>
                                            </Form.Item>

                                            <Form.Item
                                                label={formatMessage({ id: 'subject.dispensing.drugNumber' })}>
                                                <span>{currentMedicine?.name+"("+currentMedicine?.number+")"}</span>
                                            </Form.Item>
                                            <Form.Item label={formatMessage({ id: 'subject.unblinding.reason' })}
                                                       name="reasonStr"
                                                       rules={[{ required: true }]} >
                                                <Select onChange={(e) => {
                                                    setReasonType(e)
                                                    form.resetFields(["remark"])
                                                }
                                                }>
                                                    {ctx.currentRecord?.attribute?.info.unblindingReasonConfig.map((v:any) => <Select.Option key={v.reason} value={v.reason}>{v.reason}</Select.Option>)}
                                                </Select>
                                            </Form.Item>
                                            {
                                                ctx.currentRecord?.attribute?.info.unblindingReasonConfig.find((e:any) => e.reason === reasonType)?.allowRemark ?
                                                    <Form.Item
                                                        label={formatMessage({ id: 'subject.unblinding.reason.remark' })}
                                                        name="remark" rules={[{ required: true }]} >
                                                        <Input.TextArea allowClear className="full-width" />
                                                    </Form.Item> : null
                                            }

                                            <Form.Item
                                                {...passwordValidate}
                                                label={formatMessage({ id: 'common.password' })} name="password"
                                                rules={[{ required: true }]} >
                                                <Input.Password allowClear className="full-width" />
                                            </Form.Item>
                                            {
                                                (auth.project.info.unblinding_control === 1 && (auth.project.info.ip_unblinding_sms === 1 || auth.project.info.ip_unblinding_process === 1)) &&
                                                <span style={{ paddingLeft: 50 }}>
                                                            <FormattedMessage id={'subject.unblinding.application.type'} /> ：
                                                            <FormattedMessage id={'common.approval.confirm'} />
                                            </span>
                                            }
                                            {
                                                approvalType === 2 ?
                                                    <Form.Item
                                                        {...codeValidate}
                                                        label={formatMessage({ id: 'subject.unblinding.unblinded.code.confirm' })}
                                                        name="unblindingCode" rules={[{ required: true }]}>
                                                        <Input.Password allowClear className="full-width" />
                                                    </Form.Item>
                                                    :
                                                    null
                                            }
                                        </Form>
                                    </Tabs.TabPane>
                            }
                            {
                                tab2 &&
                                    <Tabs.TabPane
                                        tab={formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.unblinding-ip' })}
                                        key="2">
                                        {pendingApproval ?
                                            <>
                                                <StepsContainer>
                                                    <div style={{ display: "flex", alignItems: "center", marginBottom: 32 }}>
                                                        <TitleIcon />
                                                        <span style={{ color: "#4E5969" }}>{intl.formatMessage({ id: "subject.unblinding.approval.number" })}:</span>
                                                        <span style={{ marginLeft: 8, color: "#1D2129" }}>{pendingApproval.number}</span>
                                                    </div>
                                                    <Steps progressDot current={pendingApproval.status === 0 ? 0 : 1}>
                                                        <Steps.Step
                                                            title={formatMessage({ id: 'subject.urgentUnblindingApproval.pending' })}
                                                            description={
                                                                <>
                                                                    <Row>
                                                                        {pendingApproval.applicationByEmail}
                                                                    </Row>
                                                                    <Row>
                                                                        {moment.unix(pendingApproval.applicationTime).utc().add(timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')}
                                                                    </Row>
                                                                </>
                                                            } />
                                                        <Steps.Step title={
                                                            pendingApproval.status === 0 ?
                                                                <>
                                                                    <FormattedMessage id={"common.toBeApproved"} />
                                                                </>
                                                                :
                                                                pendingApproval.status === 1 ? <FormattedMessage id={"subject.urgentUnblindingApproval.agree"} /> :
                                                                    pendingApproval.status === 2 ? <FormattedMessage id={"subject.urgentUnblindingApproval.reject"} /> :
                                                                        null
                                                        }
                                                                    description={
                                                                        <div>

                                                                        <Tooltip
                                                                                placement="top"
                                                                                title={
                                                                                    <>
                                                                                        {approvalUserOne}
                                                                                    </>
                                                                                }
                                                                            >
                                                                                <div>
                                                                                    {approvalUserTwo}
                                                                                </div>
                                                                            </Tooltip>
                                                                        </div>
                                                                    }
                                                        />
                                                    </Steps>
                                                </StepsContainer>

                                                <Form form={approvalForm} labelCol={{ span: g.lang === 'en' ? 6 : 4 }}>
                                                    <Form.Item label={ctx.currentRecord?.attribute ? ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? (formatMessage({ id: "subject.number" })) : ((ctx.currentRecord.attribute.info.subjectReplaceText !== "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? ctx.currentRecord.attribute.info.subjectReplaceText : ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn !== "") ? (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : formatMessage({ id: "subject.number" })) : (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : ctx.currentRecord.attribute.info.subjectReplaceText)))) : formatMessage({ id: "subject.number" })}>
                                                        <span>{ctx.currentRecord.shortname}</span>
                                                    </Form.Item>
                                                    <Form.Item
                                                        label={formatMessage({ id: 'projects.randomization.randomNumber' })}>
                                                        <span>{ctx.currentRecord.randomNumber}</span>
                                                    </Form.Item>
                                                    <Form.Item
                                                        label={formatMessage({ id: 'visit.cycle.name' })}>
                                                        <span>{renderVisit(dispensing?.visitInfo, dispensing, formatMessage)}</span>
                                                    </Form.Item>
                                                    <Form.Item
                                                        label={formatMessage({ id: 'subject.dispensing.drugNumber' })}>
                                                        <span>{currentMedicine?.name+"("+currentMedicine?.number+")"}</span>
                                                    </Form.Item>
                                                    <Form.Item
                                                        label={formatMessage({ id: 'subject.unblinding.reason' })}>
                                                        <span>{pendingApproval.reasonStr}</span>
                                                        {pendingApproval.remark !== "" ?
                                                            <span>{' '}{pendingApproval.remark}</span>
                                                            : null}
                                                    </Form.Item>
                                                    <Form.Item
                                                        name={'agree'}
                                                        label={formatMessage({ id: 'common.approval.confirm' })}
                                                        rules={[{ required: true }]}
                                                    >
                                                        <Radio.Group onChange={e => setApprovalAgree(e.target.value)}>
                                                            <Radio value={1}><FormattedMessage
                                                                id={'subject.unblinding.approval.agree'} />
                                                            </Radio>
                                                            <Radio value={2}>
                                                                <FormattedMessage
                                                                    id={'subject.unblinding.approval.reject'} />
                                                            </Radio>
                                                        </Radio.Group>
                                                    </Form.Item>
                                                    {
                                                        approvalAgree === 2 ?
                                                            <Form.Item
                                                                name={'rejectReason'}
                                                                label={formatMessage({ id: 'subject.unblinding.approval.reason' })}
                                                                rules={[{ required: true }]}>
                                                                <Input.TextArea allowClear className="full-width" />
                                                            </Form.Item> : null
                                                    }
                                                </Form>

                                            </>
                                            : null}
                                    </Tabs.TabPane>
                            }
                            {
                                tab4 &&
                                <Tabs.TabPane
                                    tab={formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.unblinding-ip' })}
                                    key="4">
                                    <UnblindingSubjectDetails approvals={approvals} name={name} number={number} unbindingDetail={unbindingDetail}/>
                                </Tabs.TabPane>

                            }
                            {
                                tab3 &&
                                    <Tabs.TabPane
                                        tab={formatMessage({ id: 'menu.projects.project.subject.urgent-unblinding.approval-log' })}
                                        key="3">
                                        <DispensingUnBlindingApprovalLog approvals={approvals} approvalUsers={approvalsUser} />
                                    </Tabs.TabPane>
                            }


                        </Tabs>
                        : <Empty
                            image={<img src={EmptyImg} alt='empty' />}>
                        </Empty>
                }

            </Modal>

            <Modal
                destroyOnClose={true}
                footer={null}
                width={'600px'}
                onCancel={hide}
                centered
                maskClosable={false}
                title={ formatMessage({ id: 'subject.unblinding.application.result.title.ip' })}
                visible={applicationResultVisible}
            >
                <CustomResult
                    status="success"
                    title={formatMessage({ id: 'subject.unblinding.application.success' })}
                    subTitle={formatMessage({ id: 'subject.unblinding.application.info.ip' })}
                />
                <StepsContainer>
                    <div style={{ display: "flex", alignItems: "center", marginBottom: 32 }}>
                        <TitleIcon />
                        <span style={{ color: "#4E5969" }}>{intl.formatMessage({ id: "subject.unblinding.approval.number" })}:</span>
                        <span style={{ marginLeft: 8, color: "#1D2129" }}>{approvalNumber}</span>
                    </div>
                    <Steps progressDot current={0}>
                        <Steps.Step title={formatMessage({ id: 'subject.urgentUnblindingApproval.pending' })} description={
                            <>
                                <Row>
                                    <span className={'.ant-steps-step-description'}>
                                        {auth.user.info.name}/{auth.user.info.phone}
                                    </span>
                                </Row>
                                <Row>
                                    <span className={'.ant-steps-step-description'}>
                                        {applicationTime}
                                    </span>
                                </Row>
                            </>
                        } />
                        <Steps.Step title={formatMessage({ id: 'common.toBeApproved' })} description={
                            <>
                                <Tooltip
                                    placement="top"
                                    title={
                                        <>
                                            {approvalUserOne}
                                        </>
                                    }
                                >
                                    <>
                                        {approvalUserTwo}
                                    </>
                                </Tooltip>
                            </>
                        } />
                    </Steps>
                </StepsContainer>
            </Modal>
            {
                pendingApproval ?
                    <Modal
                        destroyOnClose={true}
                        footer={null}
                        width={'600px'}
                        onCancel={hide}
                        onOk={hide}
                        okText={formatMessage({ id: 'common.ok' })}
                        title={formatMessage({ id: 'subject.unblinding.application.result.title.ip' })}
                        visible={approvalResultVisible}
                        centered
                        maskClosable={false}

                    >
                        <CustomResult
                            status="success"
                            title={formatMessage({ id: 'subject.unblinding.approval.success' })}
                        />
                        <StepsContainer>
                            <div style={{ display: "flex", alignItems: "center", marginBottom: 32 }}>
                                <TitleIcon />
                                <span style={{ color: "#4E5969" }}>{intl.formatMessage({ id: "subject.unblinding.approval.number" })}:</span>
                                <span style={{ marginLeft: 8, color: "#1D2129" }}>{pendingApproval.number}</span>
                            </div>
                            <Steps progressDot current={approvalAgree === 0 ? 0 : 1}>
                                <Steps.Step title={<div style={{ fontSize: 14, }}>{formatMessage({ id: 'subject.urgentUnblindingApproval.pending' })}</div>} description={
                                    <div style={{ fontSize: 12, color: "#4E5969", alignItems: "center" }}>
                                        <Row>
                                            <span className={'.ant-steps-step-description'}>
                                                {pendingApproval.applicationByEmail}
                                            </span>
                                        </Row>
                                        <Row>
                                            <span className={'.ant-steps-step-description'}>
                                                {applicationTime}
                                            </span>
                                        </Row>
                                    </div>
                                } />
                                <Steps.Step title={
                                    <div style={{ fontSize: 14, }}>
                                        {
                                            approvalAgree === 1 ? <FormattedMessage id={"subject.urgentUnblindingApproval.agree"} /> :
                                                <FormattedMessage id={"subject.urgentUnblindingApproval.reject"} />
                                        }
                                    </div>
                                }
                                            description={
                                                <div style={{ fontSize: 12, color: "#4E5969", alignItems: "center" }}>
                                                    <div>
                                                        {auth.user.info.name}/{auth.user.info.phone}
                                                    </div>
                                                    <div>
                                                        {approvalTime}
                                                    </div>
                                                </div>


                                            } />
                            </Steps>
                        </StepsContainer>
                    </Modal>
                    : null
            }
            <ConfirmUnblindingInfo save={applicationUnblinding}/>
        </>
    )
};

interface DispensingUnBlindingApprovalLogInterface {
    approvals : any
    approvalUsers : any
}

export const DispensingUnBlindingApprovalLog = (prop:DispensingUnBlindingApprovalLogInterface) => {
    const {approvals, approvalUsers } = prop
    const intl = useTranslation();
    const { formatMessage } = intl;
    const ctx = useSubject();
    const auth = useAuth();
    const g = useGlobal();
    const researchAttribute = auth.project.info.research_attribute ? auth.project.info.research_attribute : 0;
    const activePanel = approvals?.length === 1 ? approvals[0].number : ""
    const [currentMedicine, setCurrentMedicine ] = useAtom(currentMedicineAtom);


    const [dispensing, setCurrentDispensing] = useAtom(currentDispensingAtom);

    const {
        runAsync: resendSmsRun,
        loading: resendSmsLoading
    } = useFetch(resendSms, { manual: true })
    const onResendSms = (subjectId: any, number: any, unblindingDetailsSign: any) => {
        resendSmsRun({ subjectId: subjectId, approvalNumber: number, unblindingDetailsSign: "4", medicineId:currentMedicine?.medicineId })
            .then((result: any) => {
                message.success(result.msg)
            })
    }

    // 空状态
    if (approvals?.length === 0) {
        return <Empty
            image={<img src={EmptyImg} alt='empty' />}>
        </Empty>
    }


    return (
            !approvals || approvals?.length === 0?
                <Empty
                    image={<img src={EmptyImg} alt='empty' />}>
                </Empty>
                :

        <div style={{position: 'relative', paddingBottom: '48px'}}>
            <div style={{maxHeight: '310px', overflowY: 'auto'}} >
                <div ref={el => (ctx.componentRef = el)}>
                    <Collapse
                        defaultActiveKey={activePanel}
                        expandIconPosition="right"
                        expandIcon={(p) => {
                            if (p.isActive) {
                                return <span >{intl.formatMessage({ id: "common.collapse" })}<CaretUpOutlined style={{ marginLeft: 4 }} /></span>
                            }
                            return <span style={{ marginRight: 4 }}>{intl.formatMessage({ id: "common.expand" })}<CaretDownOutlined style={{ marginLeft: 4 }} /></span>
                        }}

                    >
                        {
                            approvals?.map((it: any) =>
                                <Collapse.Panel
                                    key={it.number}
                                    header={
                                        <span style={{ color: "#1D2129", fontWeight: 600, fontSize: 14 }}>{it.number}</span>
                                    }
                                >
                                    <StepsContainer>
                                        <div style={{ display: "flex", alignItems: "center", marginBottom: 32 }}>
                                            <TitleIcon />
                                            <span style={{ color: "#4E5969" }}>{intl.formatMessage({ id: "subject.unblinding.approval.number" })}:</span>
                                            <span style={{ marginLeft: 8, color: "#1D2129" }}>{it.number}</span>
                                        </div>
                                        <Steps progressDot current={it.status === 0 ? 0 : 1}>
                                            <Steps.Step
                                                title={formatMessage({ id: 'subject.urgentUnblindingApproval.pending' })}
                                                description={
                                                    <>
                                                        <Row>
                                                        <span className={'.ant-steps-step-description'}>
                                                            {it.applicationByEmail}
                                                        </span>
                                                        </Row>
                                                        <Row>
                                                        <span className={'.ant-steps-step-description'}>
                                                            {moment.unix(it.applicationTime).utc().add(ctx.currentRecord?.timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')}
                                                        </span>
                                                        </Row>
                                                    </>
                                                } />
                                            <Steps.Step title={
                                                it.status === 0 ?
                                                    <>
                                                        <FormattedMessage id={"common.toBeApproved"} />
                                                    </> :
                                                    it.status === 1 ?
                                                        <FormattedMessage id={"subject.urgentUnblindingApproval.agree"} /> :
                                                        it.status === 2 ? <FormattedMessage id={"subject.urgentUnblindingApproval.reject"} /> :
                                                            null
                                            }
                                                        description={
                                                            it.status === 0 ?
                                                                <>
                                                                    <Tooltip
                                                                        placement="top"
                                                                        title={
                                                                            <>
                                                                                {

                                                                                    approvalUsers?.map((ccu: any) =>
                                                                                        <Row>
                                                                                        <span className={'.ant-steps-step-description'}>
                                                                                            {ccu.approvalName}/{ccu.approvalPhone}
                                                                                        </span>
                                                                                        </Row>
                                                                                    )

                                                                                }
                                                                            </>
                                                                        }
                                                                    >
                                                                        <>
                                                                            {

                                                                                approvalUsers?.map((ccu: any, index: number) =>
                                                                                    index < 3 ?
                                                                                        <Row>
                                                                                        <span className={'.ant-steps-step-description'}>
                                                                                            {ccu.approvalName}/{ccu.approvalPhone}
                                                                                        </span>
                                                                                        </Row>
                                                                                        :
                                                                                        index == 3 ?
                                                                                            <Row>
                                                                                            <span className={'.ant-steps-step-description'}>
                                                                                                ...
                                                                                            </span>
                                                                                            </Row>
                                                                                            :
                                                                                            null
                                                                                )

                                                                            }
                                                                        </>
                                                                    </Tooltip>
                                                                </>
                                                                :
                                                                <div style={{ color: "#4E5969", marginLeft: -20 }}>
                                                        <span className={'.ant-steps-step-description'}>
                                                            {it.approvalByEmail}
                                                        </span>
                                                                    <Row>
                                                            <span className={'.ant-steps-step-description'}>
                                                                {it.approvalTime !== 0 && moment.unix(it.approvalTime).utc().add(ctx.currentRecord?.timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')}
                                                            </span>
                                                                    </Row>
                                                                </div>
                                                        }
                                            />
                                        </Steps>
                                    </StepsContainer>
                                    <Form
                                        size="small"
                                        labelCol={{ span: g.lang === 'en' ? 7 : 4 }}
                                    >
                                        <Form.Item
                                            label={ctx.currentRecord?.attribute ? ((ctx.currentRecord?.attribute.info.subjectReplaceText === "" && ctx.currentRecord?.attribute.info.subjectReplaceTextEn === "") ? (formatMessage({ id: "subject.number" })) : ((ctx.currentRecord.attribute.info.subjectReplaceText !== "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? ctx.currentRecord.attribute.info.subjectReplaceText : ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn !== "") ? (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : formatMessage({ id: "subject.number" })) : (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : ctx.currentRecord.attribute.info.subjectReplaceText)))) : formatMessage({ id: "subject.number" })}>
                                            <span>{ctx.currentRecord?.shortname}</span>
                                        </Form.Item>
                                        <Form.Item
                                            label={formatMessage({ id: 'projects.randomization.randomNumber' })}>
                                            <span>{ctx.currentRecord?.randomNumber}</span>
                                        </Form.Item>
                                        <Form.Item
                                            label={formatMessage({ id: 'visit.cycle.name' })}>
                                            <span>{renderVisit(dispensing?.visitInfo, dispensing, formatMessage)}</span>
                                        </Form.Item>
                                        <Form.Item
                                            label={formatMessage({ id: 'subject.dispensing.drugNumber' })}>
                                            <span>{currentMedicine?.name+"("+currentMedicine?.number+")"}</span>
                                        </Form.Item>
                                        <Form.Item
                                            label={formatMessage({ id: 'subject.unblinding.reason' })}>
                                            <span>{it.reasonStr}</span>
                                            {it.remark !== "" ?
                                                <span>{' '}{it.remark}</span>
                                                : null}
                                        </Form.Item>
                                        <Form.Item
                                            label={formatMessage({ id: 'subject.unblinding.application.type' })}>
                                            <div style={{ display: "flex" }}>
                                            <span>{it.approvalType === 1 ? <FormattedMessage
                                                    id={"common.approval.confirm"} /> :
                                                it.approvalType === 2 ? <FormattedMessage
                                                    id={"project.setting.checkbox.unblinded-code"} /> : null}</span>
                                                <span>{" "}</span>
                                                <span>
                                                {/*紧急揭盲发送短信按钮*/}
                                                    {
                                                        (researchAttribute === 0 ?
                                                            permissionsCohort(auth.project.permissions, "operation.subject.unblinding-sms",ctx.currentRecord?.cohort?ctx.currentRecord.cohort.status:0) && (it.approvalType === 1 && auth.project.info.unblinding_sms === 1 && it.status === 0) && ctx.unblindingDetailsSign === "2"
                                                                ?
                                                                <Spin spinning={resendSmsLoading}>
                                                                    <Button size={'small'} type={"link"} onClick={() => onResendSms(ctx.currentRecord.id, it.number, 4)}><FormattedMessage id={'subject.unblinding.application.resend'} /></Button>
                                                                </Spin>
                                                                :
                                                                null
                                                            :
                                                            permissionsCohort(auth.project.permissions, "operation.subject-dtp.unblinding-sms",ctx.currentRecord?.cohort?ctx.currentRecord.cohort.status:0)) && (it.approvalType === 1 && auth.project.info.unblinding_sms === 1 && it.status === 0) && ctx.unblindingDetailsSign === "2"
                                                            ?
                                                            <Spin spinning={resendSmsLoading}>
                                                                <Button size={'small'} type={"link"} onClick={() => onResendSms(ctx.currentRecord.id, it.number, 4)}><FormattedMessage id={'subject.unblinding.application.resend'} /></Button>
                                                            </Spin>
                                                            :
                                                            null
                                                    }

                                                    {/*ip揭盲发送短信按钮*/}
                                                    {
                                                        researchAttribute === 0 && permissionsCohort(auth.project.permissions, "operation.subject.unblinding-ip-sms",ctx.currentRecord?.cohort?ctx.currentRecord.cohort.status:0) && (it.approvalType === 1 && auth.project.info.ip_unblinding_sms === 1 && it.status === 0) ?
                                                            <Spin spinning={resendSmsLoading}>
                                                                <Button size={'small'} type={"link"} onClick={() => onResendSms(ctx.currentRecord.id, it.number, 4)}><FormattedMessage id={'subject.unblinding.application.resend'} /></Button>
                                                            </Spin>
                                                            : null
                                                    }
                                            </span>
                                            </div>
                                        </Form.Item>
                                        {
                                            it.status !== 0 ?
                                                <Form.Item
                                                    label={formatMessage({ id: 'common.approval.confirm' })}>
                                                    <span>{unblindedApprovalStatus.find(i => i.value === it.status)?.label}</span>
                                                </Form.Item> : null
                                        }
                                        {
                                            it.status === 2 ?
                                                <Form.Item
                                                    style={{ marginBottom: 8 }}
                                                    label={formatMessage({ id: 'subject.unblinding.approval.reason' })}>
                                                    <span>{it.rejectReason}</span>
                                                </Form.Item>
                                                : null
                                        }
                                    </Form>
                                </Collapse.Panel>
                            )
                        }
                    </Collapse>
                </div>
            </div>
            {
                permissions(auth.project.permissions, "operation.subject.unblinding-ip-print") &&
                <div style={{
                    marginLeft: '-24px', padding: '16px 16px 0 0', width: `calc(100% + 48px)`, height: '48px', borderTop: '1px solid #f0f0f0',
                    position: 'absolute', bottom: 0, zIndex: 2, background: 'white'
                }}>
                    <div style={{display: 'flex', justifyContent: 'end'}}>
                        <ReactToPrint
                            trigger={() => {
                                return <Button>{<FormattedMessage id={"common.print"} />}</Button>;
                            }}
                            content={() => ctx.componentRef}
                        />
                    </div>
                </div>
            }
        </div>
    )
}


const StepsContainer = styled.div`
    margin-bottom: 18px;
    padding: 16px 12px;
    border-radius: 2px;
    background: rgba(227,228,230,0.2);
    .ant-steps-horizontal {
      .ant-steps-item:last-child {
        .ant-steps-item-title {
          width: 140px;
        }
        .ant-steps-item-content {
          display: flex;
          flex-direction: column;
          width: 100%;
        }
      }
    }
    
    .ant-steps-label-vertical .ant-steps-item-content .ant-steps-item-description {
        text-align: left !important;
        white-space: nowrap;
        // width: 200px !important;
    }
    .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-tail::after {
        background-color: #C8C9CC;
    }
`
const UnblindingSubjectDetails = (props:any) => {
    const auth = useAuth();
    const intl = useTranslation();
    const ctx = useSubject();
    const g = useGlobal()
    const projectType = auth.project.info.type;
    const {formatMessage} = intl;
    const {approvals,name, number, unbindingDetail} = props;
    function formatTimezoneOffset(offset: any) {
        const negative = offset < 0;
        offset = Math.abs(offset);
        const hours = Math.floor(offset);
        const minutes = Math.round((offset - hours) * 60); // 四舍五入处理 .5

        const sign = negative ? "-" : "";
        const hh = String(hours).padStart(2, '0');
        const mm = String(minutes).padStart(2, '0');

        return `${sign}${hh}:${mm}`;
    }

    const [dispensing, setCurrentDispensing] = useAtom(currentDispensingAtom);


    const formatTimezone = (timestamp: any, timezone: any) => {
        const tzMoment = moment.tz(timestamp * 1000, timezone);

        // 获取时区偏移分钟数（如 -330 表示 UTC-05:30）
        const offsetMinutes = tzMoment.utcOffset(); // 单位是分钟
        const totalHours = Math.abs(offsetMinutes);
        const hours = Math.floor(totalHours / 60);
        const minutes = totalHours % 60;

        // 构造 UTC±HH:mm 格式
        const sign = offsetMinutes < 0 ? "-" : "+";
        const formattedOffset = `${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

        // 格式化时间
        const formattedTime = tzMoment.format('YYYY-MM-DD HH:mm:ss');

        // 拼接结果
        return `${formattedTime}(UTC${formattedOffset})`;
    };
    return <div ref={el => (ctx.componentRef = el)}>

        <Title  name={formatMessage({ id: "common.ip.info" })}></Title>
    <Descriptions style={{marginTop: 12}} layout="horizontal" column={1} bordered size="small" className="full-width">
        {
            auth.project.info.type !== 1
                ?
                <Descriptions.Item label={auth.project.info.type === 2? <FormattedMessage id={"projects.second"}/>:<FormattedMessage id={"common.stage"}/>}>{
                    auth.project.info.type === 2 && auth.env.cohorts?.find((item:any)=>item.id === ctx.currentRecord?.cohortId)?.type === 1 ?
                        auth.env.cohorts?.find((item:any)=>item.id === ctx.currentRecord?.cohortId)?.name +" - "+  auth.env.cohorts?.find((item:any)=>item.id === ctx.currentRecord?.cohortId)?.re_random_name :
                        auth.env.cohorts?.find((item:any)=>item.id === ctx.currentRecord?.cohortId)?.name
                }</Descriptions.Item>
                :
                null
        }
        <Descriptions.Item label={ctx.currentRecord?.attribute ? ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((ctx.currentRecord.attribute.info.subjectReplaceText !== "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "")?ctx.currentRecord.attribute.info.subjectReplaceText:((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?ctx.currentRecord.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?ctx.currentRecord.attribute.info.subjectReplaceTextEn:ctx.currentRecord.attribute.info.subjectReplaceText)))) : formatMessage({ id: "subject.number" })}>
            {ctx.currentRecord?.info[0].value}
        </Descriptions.Item>

        {
            ctx.currentRecord?.attribute && ctx.currentRecord?.attribute.info.isRandomNumber
                ?
                <Descriptions.Item label={<FormattedMessage
                    id={"projects.randomization.randomNumber"}/>}>{ctx.currentRecord?.randomNumber}</Descriptions.Item>
                :
                null
        }
        {
            <Form.Item
                label={formatMessage({ id: 'visit.cycle.name' })}>
                <span>{renderVisit(dispensing?.visitInfo, dispensing, formatMessage)}</span>
            </Form.Item>
        }
        {
            <Descriptions.Item label={<FormattedMessage
                id={"drug.list.name"}/>}>{name}</Descriptions.Item>
        }
        {
            <Descriptions.Item label={<FormattedMessage
                id={"drug.list.drugNumber"}/>}>{number}</Descriptions.Item>
        }
        {
                <Descriptions.Item label={<FormattedMessage
                    id={"subject.unblinding.reason"}/>}>{unbindingDetail? unbindingDetail.reasonStr : approvals?.find((it:any)=> it.status === 1)?.reasonStr}</Descriptions.Item>
        }
        {
            <Descriptions.Item label={<FormattedMessage
                id={"report.attributes.unblinding.operator"}/>}>{unbindingDetail? unbindingDetail.operationMail : approvals?.find((it:any)=> it.status === 1)?.approvalByEmail}</Descriptions.Item>
        }
        {
            <Descriptions.Item label={<FormattedMessage
                id={"subject.unblinding.time"}/>}>{
                ctx.currentRecord?.tz === ""?
                moment
                    .unix(unbindingDetail?.operationTime? unbindingDetail.operationTime : approvals?.find((it:any)=> it.status === 1)?.approvalTime)
                    .utc()
                    .add(ctx.currentRecord?.timeZone, "hour")
                    .format("YYYY-MM-DD HH:mm:ss") +
                (ctx.currentRecord?.timeZone >= 0 ? "(UTC+" : "(UTC") +
                formatTimezoneOffset(ctx.currentRecord?.timeZone) +
                ")" :
                formatTimezone(unbindingDetail?.operationTime? unbindingDetail.operationTime : approvals?.find((it:any)=> it.status === 1)?.approvalTime, ctx.currentRecord?.tz)
            }</Descriptions.Item>
        }
    </Descriptions>
</div>

}


const renderVisit = (value: any, record: any, formatMessage:any) => {
    let name: any = value.name;
    !record.visitSign
        ? (name = value.name)
        : record.reissue === 1
            ? (name =
                value.name +
                "(" +
                formatMessage({ id: "subject.dispensing.reissue" }) +
                ")")
            : (name =
                value.name +
                "(" + record.outVisitStr +
                ")");
    return name;
};


interface ConfirmUnblindingInfoInterface {
    save :any
}

const ConfirmUnblindingInfo = (props:ConfirmUnblindingInfoInterface) => {
    const {save} = props
    const [currentMedicine,  ] = useAtom(currentMedicineAtom);
    const [confirmVisible, setConfirmVisible ] = useAtom(confirmVisibleAtom);
    const [dispensing,  ] = useAtom(currentDispensingAtom);
    const ctx = useSubject()
    const g = useGlobal()
    const intl = useTranslation();
    const { formatMessage } = intl;

    return <Modal
        open={confirmVisible}
        title={formatMessage({ id: 'subject.unblinding.confirmTip' })}
        centered
        onOk={save}
        onCancel={() => {
            setConfirmVisible(false)
        }}
        okText={formatMessage({ id: 'common.ok' })}
        destroyOnClose={true}
    >
        <Row className={"full-width"}><Alert className={"full-width"} message={formatMessage({ id: 'common.confirm.tip' })} type="info" showIcon /></Row>
        <Form.Item label={ctx.currentRecord?.attribute ? ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? (formatMessage({ id: "subject.number" })) : ((ctx.currentRecord.attribute.info.subjectReplaceText !== "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn === "") ? ctx.currentRecord.attribute.info.subjectReplaceText : ((ctx.currentRecord.attribute.info.subjectReplaceText === "" && ctx.currentRecord.attribute.info.subjectReplaceTextEn !== "") ? (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : formatMessage({ id: "subject.number" })) : (g.lang === "en" ? ctx.currentRecord.attribute.info.subjectReplaceTextEn : ctx.currentRecord.attribute.info.subjectReplaceText)))) : formatMessage({ id: "subject.number" })}>
            <span>{ctx.currentRecord?.shortname}</span>
        </Form.Item>
        <Form.Item
            label={formatMessage({ id: 'projects.randomization.randomNumber' })}>
            <span>{ctx.currentRecord?.randomNumber}</span>
        </Form.Item>

        <Form.Item
            label={formatMessage({ id: 'visit.cycle.name' })}>
            <span>{renderVisit(dispensing?.visitInfo, dispensing, formatMessage)}</span>
        </Form.Item>

        <Form.Item
            label={formatMessage({ id: 'subject.dispensing.drugNumber' })}>
            <span>{currentMedicine?.name+"("+currentMedicine?.number+")"}</span>
        </Form.Item>
    </Modal>
}

const CustomResult = styled(Result)`
  padding: 12px 0 24px 0;
    .ant-result-icon {
      margin-bottom: 6px;
      svg {
        width: 52px;
        height: 52px;
      }
    }
  .ant-result-title {
    font-family: 'Microsoft YaHei', sans-serif;
    font-size: 20px;
    color: rgba(29, 33, 41, 1);
    font-weight: 600;
  }
  .ant-result-subtitle {
    font-size: 14px;
    color: rgba(103, 114, 131, 1);
  }
    

`
