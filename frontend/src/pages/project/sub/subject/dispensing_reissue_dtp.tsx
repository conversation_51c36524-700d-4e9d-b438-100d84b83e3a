import {Button, Col, Form, Input, message, Modal, Row, Select} from "antd";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {useAuth} from "../../../../context/auth";
import {useSafeState, useSize} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {getVisit, reissueDispensingWithDTP} from "../../../../api/dispensing";
import React from "react";
import {useSubject} from "./context";
import {DispensingConfirm} from "./dispensing_confirm";
import {useGlobal} from "../../../../context/global";


export const DispensingReissueDTP = (props:any) => {

    const auth = useAuth();
    const intl = useTranslation();
    const {formatMessage} = intl;
    const ctx = useSubject();

    const [form] = Form.useForm();

    const [visible, setVisible] = useSafeState<any>(false);
    const [record, setRecord] = useSafeState<any>({});
    const projectId = auth.project.id;
    const customerId = auth.customerId;
    const envId = auth.env.id;
    const [drugOption, setDrugOption] = useSafeState<any>([]);
    const [option, setOption] = useSafeState<any>([]);
    const { TextArea } = Input;
    const [optionData, setOptionData] = useSafeState<any>([]);
    const [drugLabelDisabled, setDrugLabelDisabled] = useSafeState<any>(false);
    const dispensing_confirm_pt :any = React.useRef();

    const hide = () => {
        setOptionData([])
        setDrugLabelDisabled(false)
        setOption([])
        setVisible(false);
        form.resetFields();
    };

    const {runAsync: getVisitRun, loading: getVisitLoading} = useFetch(getVisit, {manual: true})

    const {runAsync: reissueDispensingWithDTPRun, loading: reissueDispensingWithDTPLoading} = useFetch(reissueDispensingWithDTP, {manual: true})


    const show = (record: any, type: any, id: any, visitId: any, visitOrder :any,dispensing:any) => {
        setRecord(record)
        get_visit(record.id,record.status,visitId,visitOrder,dispensing)
        setVisible(true);
    };

    // 获取访视周期选项
    const get_visit = (subjectId: any, status: any, visitId: any, visitOrder:any,dispensing:any) => {
        getVisitRun({
            customerId,
            projectId,
            envId,
            cohortId:record.cohortId,subjectId,status, roleId: auth.project.permissions.role_id}).then(
            (result:any) => {
                let data :any = result.data
                if (data){
                    let option :any= []
                    data.forEach(
                        (value:any) => {
                            if(!option.find((it:any) => it.id === value.visit_info.id)){
                                // if (value.visit_info.id <= visitId){
                                if (visitOrder.findIndex((it:any) => it === value.visit_info.id) !== -1 && dispensing.find((item:any)=> value.visit_info.id === item.visitInfo.visitCycleInfoId && item.status === 2)){

                                    option.push({id:value.visit_info.id , name:value.visit_info.name})
                                }
                            }
                        }
                    )
                    setOption(option)
                    setOptionData(data)

                    if (option.find((it:any) => (it.id === visitId))){
                        setDrugLabel(visitId,data)
                        // form.setFieldsValue({"visit": visitId})
                    }

                }
            }

        )
    }

    // 设置研究产品标签选项
    const setDrugLabel: any = (id: any, options: any) => {
        form.setFieldsValue({...form.getFieldsValue,"openSetting":[]})
        let option: any = []
        options.forEach(
            (value: any) => {
                if (value.visit_info.id === id) {
                    if (value.configures.open_setting === 1){
                        option.push({
                            id: value.configures.id,
                            name: value.configures.label,
                        })
                        // openSetting.push({id:value.configures.id , name:value.configures.label,values:value.configures.values})
                    }else{

                    }

                }
            }
        )
        setDrugOption(option)

        if (option && option.length === 1){
            form.setFieldsValue({...form.getFieldsValue,"drugLabel":option[0].id})
            setDrugLabelDisabled(true)
        }else{
            form.setFieldsValue({...form.getFieldsValue, "drugLabel":null})
            setDrugLabelDisabled(false)
        }
    }


    const save = () => {
        form.validateFields().then(
            value => {
                const visitName = option.find((it:any) =>it.id === value.visit).name
                const labelName = drugOption.find((it:any) =>it.id === value.label).name
                dispensing_confirm_pt.current.show(formatMessage({id: 'subject.dispensing.reissue.apply'}),record, visitName, labelName, 1, value)
            }
        )
    }

    const save_confirm = () => {
        // 发药
        form.validateFields().then(
            value => {
                const data = {
                    "subject_id": record.id,
                    "visit_id": value.visit,
                    "visit_label_id": value.label,
                    "remark":value.remark,
                    roleId: auth.project.permissions.role_id,
                }
                reissueDispensingWithDTPRun(data).then(
                    (response: any) => {
                        form.resetFields()
                        message.success(response.msg).then();
                        // props.subjectRefresh();
                        props.refresh()
                        hide()
                        ctx.setShowTitle(formatMessage({ id: "subject.dispensing.reissue.apply" }))
                        ctx.setShowData(response.data)
                        ctx.setVisible(true)
                        ctx.setDispensingConfirmVisible(false)
                    }
                )
            }
        )
    }
    const optionChanges = (v :any) => {
        setDrugLabel(v,optionData)
    }

    React.useImperativeHandle(props.bind, () => ({show}));
    const g = useGlobal()
    const subjectWidthRef :any = React.useRef();
    const size :any= useSize(subjectWidthRef)
    const formItemLayout = () => {
        let width = g.lang === "en"? 200: 122
        width = size?.width+ 10 < width ? width : size?.width+ 10
        return {
            labelCol: { style: {   width: width} },
        };
    }
    return (
        <Modal
            centered
            className="custom-medium-modal"
            title={formatMessage({ id: 'subject.dispensing.reissue.apply' })}
            visible={visible}
            onCancel={hide}
            
            maskClosable={false}
            footer={
                <Row justify="end">
                    <Col>
                        <Button className="mar-lft-10" onClick={save} type="primary" loading={reissueDispensingWithDTPLoading}>
                            <FormattedMessage id="common.save"/>
                        </Button>
                    </Col>
                </Row>
            }
        >
            <Form form={form} {...formItemLayout()}>
                <Form.Item  label={<span ref={subjectWidthRef}>{record.attribute?((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText)))):formatMessage({id:"subject.number"}) }</span>} className="mar-ver-5">
                    <Row className="mar-lft-10">{record.shortname}</Row>
                </Form.Item>
                {
                    !(!record.randomNumber || record.randomNumber ===  "" || record.status === 1 || record.status === 2 || !record.attribute.info.random) &&
                    <Form.Item  label={formatMessage({ id: 'projects.randomization.randomNumber' })} className="mar-ver-5">
                        <Row className="mar-lft-10">{record.randomNumber}</Row>
                    </Form.Item>
                }

                {
                        <>
                            <Form.Item  rules={[{ required: true }]} name="visit" label={formatMessage({ id: 'visit.cycle.name' })}>
                                <Select className="full-width" style={{ width: 120 }} onChange={optionChanges}>
                                    {
                                        option.map(
                                            (it:any) =>
                                                <Select.Option key={it.id} value={it.id}>{it.name}</Select.Option>
                                        )
                                    }
                                </Select>
                            </Form.Item>
                            <Form.Item  rules={[{ required: true }]} name="label" label={formatMessage({ id: 'subject.dispensing.drugLabel' })}>
                                <Select className="full-width" style={{ width: 120 }}>
                                    {
                                        drugOption.map(
                                            (it:any) =>
                                                <Select.Option key={it.id} value={it.id}>{it.name}</Select.Option>
                                        )
                                    }
                                </Select>
                            </Form.Item>
                            {/*<DrugOpen drugNameOption={drugNameOption} max={max} setMax={setMax} spec={spec} setSpec={setSpec}/>*/}

                            <Form.Item rules={[{ required: true }]} name="remark" label={formatMessage({ id: 'common.remark' })}>
                                <TextArea  allowClear className="full-width" autoSize={{ minRows: 2, maxRows: 6 }} maxLength={500}/>
                            </Form.Item>
                        </>
                }
            </Form>
            <DispensingConfirm bind={dispensing_confirm_pt} save={save_confirm}/>

        </Modal>

    )

}