import React from "react";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {Button, Col, Form, Input, Modal, Row} from "antd";
import {useFetch} from "../../../../hooks/request";
import {useSafeState} from "ahooks";
import {updateStatus} from "../../../../api/subject";
import {useAuth} from "../../../../context/auth";
import {useGlobal} from "../../../../context/global";
import {CustomConfirmModal} from "../../../../components/modal";
import DatePicker from "../../../../components/DatePicker";
import dayjs from "dayjs";
import styled from "@emotion/styled";
export const SignOut = (props:any) => {
    const auth = useAuth();
    const intl = useTranslation();
    const {formatMessage} = intl;
    const DatePickers: any = DatePicker;
    const [visible, setVisible] = useSafeState<any>(false);
    const [id, setId] = useSafeState<any>("");
    const [record, setRecord] = useSafeState<any>({});
    const [sign, setSign] = useSafeState<any>("0");
    const [form] = Form.useForm();

    const show = (record:any, type: any) => {
        setVisible(true);
        setId(record.id);
        setRecord(record)
        setSign(type);
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
    };

    const {runAsync:updateStatusRun, loading:updateStatusLoading} = useFetch(updateStatus, {manual: true})


    // 受试者退出
    const save = () => {
        form.validateFields()
        .then(values => {
            CustomConfirmModal({
                title: formatMessage({id:"subject.confirm.exited"}),
                cancelText: formatMessage({id: 'common.cancel'}),
                okText: formatMessage({id: 'common.ok'}),
                onOk: () => {
                    if (values["signOutRealTime"] != null){
                        values["signOutRealTime"] = dayjs(values["signOutRealTime"]).format("YYYY-MM-DD")
                    }
                    updateStatusRun({
                        "id": id,
                        "sign": sign,
                        ...values,
                    }).then(
                        () => {
                            props.refresh();
                            hide();
                        }
                    )
                }
            })
            
        })
        .catch(() => { })
    };

    React.useImperativeHandle(props.bind, () => ({show}));
    const g = useGlobal()
    const formItemLayout = {
        labelCol: { style: {   width: g.lang === "en"? "210px": "150px"} },
    };

    return (
        <React.Fragment>
            <Modal
                className="custom-medium-modal"
                title={<FormattedMessage id="subject.sign.out" />}
                open={visible}
                centered
                maskClosable={false}
                
                onCancel={hide}
                destroyOnClose={true}
                footer={
                    <Row justify="end">
                        <Col style={{marginRight:"16px"}}>
                            <Button onClick={hide}>
                                <FormattedMessage id="common.cancel" />
                            </Button>
                        </Col>
                        <Col>
                            <Button onClick={ save } type="primary" loading={updateStatusLoading}>
                                <FormattedMessage id="common.ok" />
                            </Button>
                        </Col>
                    </Row>
                }
            >
                <StyleFrom form={form} {...formItemLayout} labelWrap>
                    {auth.project.info.type === 2 && (
                        <Col>
                            <Form.Item
                                label={formatMessage({
                                    id: "projects.second",
                                })}
                            >
                                {record.cohort?.type === 1?record.cohortName + " - " + record.reRandomName:record.cohortName}
                            </Form.Item>
                        </Col>
                    )}
                    {auth.project.info.type === 3 && (
                        <Col>
                            <Form.Item
                                label={formatMessage({
                                    id: "common.stage",
                                })}
                            >
                                {record.cohortName}
                            </Form.Item>
                        </Col>
                    )}
                    <Form.Item
                        label={
                            record.attribute?
                            ((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn === "")?(formatMessage({ id: "subject.number" })):((record.attribute.info.subjectReplaceText !== "" && record.attribute.info.subjectReplaceTextEn === "")?record.attribute.info.subjectReplaceText:((record.attribute.info.subjectReplaceText === "" && record.attribute.info.subjectReplaceTextEn !== "")?(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:formatMessage({ id: "subject.number" })):(g.lang === "en"?record.attribute.info.subjectReplaceTextEn:record.attribute.info.subjectReplaceText))))
                            :formatMessage({id:"subject.number"})
                        }
                    >
                        {record.shortname}
                    </Form.Item>
                    {
                        record.group!== "" && record.attribute && record.attribute.info.random?
                            <Form.Item label={ formatMessage({id:"projects.randomization.randomNumber"}) }>
                                {record.randomNumber}
                            </Form.Item>
                            :null
                    }
                    <Form.Item label={formatMessage({id: 'subject.stop.time'})} name="signOutRealTime"
                               style={{marginBottom:16}}
                    >
                        <DatePickers
                            disabledDate={(current: any) => {
                                return current < dayjs().year(2020).month(0).date(0) || current > dayjs();
                            }}
                            format={"YYYY-MM-DD"} placeholder={formatMessage({id: 'placeholder.select.common'})}
                                     className="full-width"/>
                    </Form.Item>
                    <Form.Item
                        style={{marginBottom:0}}
                        label={formatMessage({id: 'subject.reason'})} name="reason" rules={[{required: true}]} >
                        <Input.TextArea placeholder={formatMessage({id:"common.required.prefix"})} allowClear className="full-width" showCount maxLength={500}/>
                    </Form.Item>
                </StyleFrom>
            </Modal>
        </React.Fragment>
    )
};

const StyleFrom = styled(Form)`
    .ant-form-item-label {
        padding: 0;
    }
    .ant-form-item-label label {
        //height: auto;
        color: #4e5969;
        opacity: 0.8;
    }

`;