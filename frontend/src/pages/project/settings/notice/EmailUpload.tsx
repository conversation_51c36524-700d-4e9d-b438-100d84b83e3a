import {CloseCircleFilled, DeleteOutlined, DownloadOutlined, EyeOutlined, StarOutlined, UploadOutlined} from '@ant-design/icons'
import styled from '@emotion/styled';
import {useSafeState} from 'ahooks';
import {Button, message, Tooltip, Upload, UploadProps} from 'antd'
import React from 'react';
import {useImperativeHandle} from 'react';
import {useIntl} from 'react-intl';
import { useEmails } from "../notice/hooks";

interface EmailUploadProps extends UploadProps {
    bind: any
    tips: string | React.ReactNode
    width?: number
    height?: number
    clickStyle?: React.CSSProperties
    buttonStyle?: React.CSSProperties
    fileSize?: number
}

export const EmailUpload = (props: EmailUploadProps) => {

    const ctx = useEmails();

    const {formatMessage} = useIntl()

    const [fileList, setFileList] = useSafeState<any>([])

    useImperativeHandle(props.bind, () => ({
        fileList,
        originFile: fileList.length > 0 ? fileList[0].originFileObj : null,
        reset: () => {
            setFileList([])
        }
    }))

    const beforeUpload = (file: any) => {
        if (props.fileSize && props.fileSize > 0) {
            const isLimit = file.size / 1024 / 1024 < (props.fileSize || 50)
            if (!isLimit) {
                message.error(formatMessage({id:'common.upload.fileSize'}))
            }
        }
        return false
    }

    const onChange = (info: any) => {
        if (info.fileList && info.fileList.length > 0) {
            setFileList([info.fileList[0]])
        } else {
            setFileList([])
        }
    }


    const handleOnChange = (fileList: any) => {
        setFileList(fileList);
    };

    const handleBeforeUpload = (file: any) => {
        const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
        if (!isJpgOrPng) {
            message.error('You can only upload JPG/PNG file!');
            return false;
        }
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isLt2M) {
            message.error('Image must smaller than 2MB!');
            return false;
        }
        return true;
    };

    const handlePreview = (file: any) => {
        if (file.url || file.preview) {
            window.open(file.url || file.preview);
        }
    };

    const handleDownload = (file: any) => {
        console.log('Handling download for:', file);
        // 在这里处理你的下载逻辑
    };

    const itemRender = (_: any, file: any, listType: any, actions: any) => {
        return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
                {/* 默认文件项渲染 */}
                <span style={{ flex: 1 }}>
                    {file.name}
                    {file.status === 'uploading' && <span> - Uploading</span>}
                    {file.status === 'done' && <span> - Done</span>}
                    {file.status === 'error' && <span> - Error</span>}
                </span>

                {/* 预览按钮 */}
                <Tooltip title="Preview">
                    <Button
                        type="link"
                        icon={<EyeOutlined />}
                        onClick={() => handlePreview(file)}
                        style={{ marginRight: 8 }}
                    />
                </Tooltip>

                {/* 删除按钮 */}
                <Tooltip title="Delete">
                    <Button
                        type="link"
                        icon={<DeleteOutlined />}
                        onClick={() => actions.remove(file)}
                        style={{ marginRight: 8 }}
                    />
                </Tooltip>
            </div>
        );
    };

    return (
        <NoTopUpload {...props}
            fileList={fileList}
            beforeUpload={beforeUpload}
            onChange={onChange}
            // showUploadList={{
            //     showPreviewIcon: true, // 确保预览按钮显示
            //     showRemoveIcon: true, // 显示删除按钮
            //     downloadIcon: <span style={{ color: '#165DFF', cursor: 'pointer', marginLeft: -50 }}>{formatMessage({id: 'common.details'})}</span>,
            //     removeIcon: <span style={{ color: '#165DFF', cursor: 'pointer', marginLeft: -20 }}>{formatMessage({id: 'common.delete'})}</span>,
            // }}
            itemRender={itemRender} // 使用自定义的 itemRender 函数
            onPreview={handlePreview}
        >
            {
                fileList.length === 0 && 
                    <div
                        // style={{
                        //     display: 'flex',
                        //     flexDirection: 'column',
                        //     justifyContent: 'center',
                        //     alignItems: 'center',
                        //     border: '1px solid #E3E4E6',
                        //     borderRadius: '2px',
                        //     width: props.width || 410,
                        //     height: props.height || 93,
                        //     ...props.clickStyle
                        // }}
                    >
                        <span style={{marginBottom: 8, color: '#ADB2BA'}}>
                            <Button type='dashed'  icon={<UploadOutlined />} disabled={ctx.disabled?false:true}>
                                {formatMessage({id: 'notice.exclude_recipient_list.email.batch'})}
                            </Button>
                            {
                                typeof props.tips === 'string' ?
                                    <span 
                                        style={{fontSize: 12}} 
                                        onClick={e => e.stopPropagation()}
                                    >
                                        {props.tips}
                                    </span>
                                    :
                                    props.tips
                            }
                        </span>
                    </div>
            }
        </NoTopUpload>
    )
}

const NoTopUpload = styled(Upload)`
    .ant-upload-list-item {
        margin-top: 0;
        width: 400px;
    }
`
