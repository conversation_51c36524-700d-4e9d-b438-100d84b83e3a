import React, {ReactNode, useState} from "react";
import {useSafeState} from "ahooks";
import {Form} from "antd";

export const NoticeContext = React.createContext<{
        roles: any[] ;
        setRoles: (data: any[]) => void;
        refresh: any;
        setRefresh: (data: any) => void;
        form: any;
        showForecast: any;
        setShowForecast: (data: any) => void;
        emailList: any;
        setEmailList: (emailList: any) => void;
        disabled: any;
        setDisabled: (disabled: any) => void;
        importExclusiveReceivers: any;
        setImportExclusiveReceivers: (v: any) => void;
        importExclusiveReceiversFile: any;
        setImportExclusiveReceiversFile: (v: any) => void;
        importExclusiveReceiversPreview: any;
        setImportExclusiveReceiversPreview: (v: any) => void;
        unbindEmailList: any;
        setUnbindEmailList: (unbindEmailList: any) => void;
    }
    |
    null>(null);

export const NoticeContextProvider = ({children}: { children: ReactNode }) => {

    const [roles, setRoles] = useSafeState<any[]>([]);

    const [form] = Form.useForm();
    const [refresh, setRefresh] = useSafeState<any>(true);
    const [showForecast, setShowForecast] = useState<boolean>(false);
    const [emailList, setEmailList] = useSafeState<any>([]);
    const [disabled, setDisabled] = useSafeState<any>(false);
    // 当前正在编辑的邮件的`导入收件人`
    const [importExclusiveReceivers, setImportExclusiveReceivers] = useSafeState<any>([]);
    // 当前正在编辑的邮件的`导入收件人文件`
    const [importExclusiveReceiversFile, setImportExclusiveReceiversFile] = useSafeState<any>(undefined);
    // 是否预览正在编辑的邮件的`导入收件人`
    const [importExclusiveReceiversPreview, setImportExclusiveReceiversPreview] = useSafeState<any>(false);
    //解绑和已开启的邮箱
    const [unbindEmailList, setUnbindEmailList] = useSafeState<any>([]);


    return (
        <NoticeContext.Provider
            value={
                {
                    roles, setRoles,
                    refresh, setRefresh,
                    form,
                    showForecast, setShowForecast,
                    emailList,setEmailList,
                    disabled,setDisabled,
                    importExclusiveReceivers,setImportExclusiveReceivers,
                    importExclusiveReceiversFile,setImportExclusiveReceiversFile,
                    importExclusiveReceiversPreview,setImportExclusiveReceiversPreview,
                    unbindEmailList,setUnbindEmailList,
                }
            }
        >
            {children}
        </NoticeContext.Provider>
    )
};

export const useNotice = () => {
    const context = React.useContext(NoticeContext);
    if (!context) {
        throw new Error("useNotice must be used in NoticeContextProvider");
    }
    return context;
};

