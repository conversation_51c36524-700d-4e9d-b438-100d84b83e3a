/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Space, Typography, Upload, message, Row } from "antd";
import type { UploadFile } from "antd/es/upload/interface";
import { LinkOutlined, UploadOutlined } from "@ant-design/icons";
import { useIntl } from "react-intl";
import { useSafeState, useMount } from "ahooks";
import ExcelJS from "exceljs";
import { useEmails } from "../notice/hooks";
import { ImportExclusiveReceiversPreview } from "./edit-exclusive-receivers-import-preview";
import lodash from "lodash";
import React from "react";

export const EditExclusiveReceiversImport = () => {
    const intl = useIntl();
    const ctx = useEmails();

    const [fileList, setFileList] = useSafeState<UploadFile[]>([]);

    // const test = (file: File) => {
    //     return new Promise(function (resolve, reject) {
    //         const reader: FileReader = new FileReader();
            
    //         reader.onload = function (e: ProgressEvent<FileReader>) {
    //             const data = new Uint8Array(e.target!.result as ArrayBuffer);
    //             const workbook = new ExcelJS.Workbook();
                
    //             workbook.xlsx.load(data)
    //                 .then(function () {
    //                     const worksheet = workbook.getWorksheet(1); // 获取第一个工作表
    //                     if (!worksheet) {
    //                         reject("Worksheet not found.");
    //                         return;
    //                     }

    //                     let result = worksheet.getColumn(1).values || [];
    //                     if (result.length > 3000) {
    //                         reject("The record count is over 3,000.");
    //                         return;
    //                     }

    //                     result = lodash
    //                         .chain(result)
    //                         .map(lodash.trim)
    //                         .compact()
    //                         .map(lodash.toLower)
    //                         .filter((it: any) => /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(it))
    //                         .uniq()
    //                         .value();

    //                     resolve(result.filter((it: any) => !!it));
    //                 })
    //                 .catch((error) => {
    //                     console.error("Error loading workbook:", error);
    //                     reject("Error loading workbook.");
    //                 });
    //         };

    //         reader.onerror = function (error) {
    //             console.error("File reading error:", error);
    //             reject("File reading error.");
    //         };

    //         reader.readAsArrayBuffer(file as Blob);
    //     });
    // };

    const test = (file: any) => {
        return new Promise(function (resolve, reject) {
            const reader = new FileReader();
            
            reader.onload = function (e) {
                const data = new Uint8Array(e.target!.result as ArrayBuffer);
                const workbook = new ExcelJS.Workbook();
                
                workbook.xlsx.load(data)
                    .then(function () {
                        const worksheet = workbook.getWorksheet(1); // 获取第一个工作表
                        const rowsData: any = [];
                        
                        worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
                            const rowData: any[] = [];
                            row.eachCell({ includeEmpty: false }, (cell, colNumber) => {
                                // 跳过第一列
                                if (rowNumber > 1) {
                                    // 检查是否有超链接
                                    if (cell.hyperlink) {
                                        const cellValue = cell.value;
                                        // 将单元格值添加到 rowData
                                        rowData.push(cellValue.text);
                                    } else {
                                        rowData.push(cell.value);
                                    }
                                }
                            });
                            if(rowData.length !== 0){
                                rowData.forEach(element => {
                                    rowsData.push(element + "");
                                });
                                
                            }
                        });
                        
                        resolve(rowsData);
                    })
                    .catch((error) => {
                        reject("Error loading workbook.");
                    });
            };
    
            reader.readAsArrayBuffer(file);
        });
    };

    const template = () => {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet("Sheet1");
        
        // 添加标题行
        worksheet.addRow([intl.formatMessage({ id: "notice.exclude_recipient_list.email.account" })]); // A1
        
        // 设置样式
        const headerRow = worksheet.getCell('A1');
        headerRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFD3D3D3' } // 灰色背景
        };
        headerRow.font = {
          bold: true,
          color: { argb: 'FF000000' } // 黑色文字
        };
        
        // 设置列宽
        worksheet.getColumn(1).width = 30;
        
        // ========== 关键修正 ========== //
        // 1. 先解锁所有单元格（必须包含空单元格）
        worksheet.eachRow({ includeEmpty: true }, (row) => {
          row.eachCell({ includeEmpty: true }, (cell) => {
            if (!cell.protection || cell.protection.locked !== false) {
              cell.protection = { locked: false }; // 明确设置为未锁定
            }
          });
        });
        
        // 2. 单独锁定A1
        worksheet.getCell('A1').protection = { locked: true };
        
        // 预先设置前几行几列的单元格为未锁定，即使它们当前为空
        for (let rowNumber = 2; rowNumber <= 1000; rowNumber++) { // 假设最多需要解锁到第100行
            for (let colNumber = 1; colNumber <= 1; colNumber++) { // 假设最多需要解锁到第26列(Z列)
            const cell = worksheet.getRow(rowNumber).getCell(colNumber);
            cell.protection = { locked: false };
            }
        }
        
        const randomPassword = generateRandomPassword(16);
        // 应用保护（必须最后执行）
        worksheet.protect(randomPassword, {
            selectLockedCells: true,    // 允许选择锁定的A1
            selectUnlockedCells: true,  // 允许选择其他单元格
            formatCells: true,          // 允许格式化
            formatColumns: true,        // 允许调整列宽
            formatRows: true,           // 允许调整行高
            insertRows: true,           // 允许插入行
            deleteRows: true,           // 允许删除行
            insertColumns: true,        // 允许插入列
            deleteColumns: true         // 允许删除列
        });
        // ========== 修正结束 ========== //
        
        // 生成文件
        workbook.xlsx.writeBuffer()
          .then((buffer) => {
            const blob = new Blob([buffer], { 
              type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" 
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "ExcludedRecipientTemplate.xlsx";
            a.click();
            URL.revokeObjectURL(url);
          })
          .catch(console.error);
    };
    

    function generateRandomPassword(length: any) {
        // 定义密码可能包含的字符集
        const lowerCaseLetters = 'abcdefghijklmnopqrstuvwxyz';
        const upperCaseLetters = lowerCaseLetters.toUpperCase();
        const numbers = '0123456789';
        const specialCharacters = '!@#$%^&*()-_=+[]{}|;:,.<>?/~`';
    
        // 创建一个所有候选字符的字符串
        const allCharacters = lowerCaseLetters + upperCaseLetters + numbers + specialCharacters;
    
        let password = '';
        for (let i = 0; i < length; i++) {
            // 从所有字符中随机选取一个字符并添加到密码字符串中
            const randomIndex = Math.floor(Math.random() * allCharacters.length);
            password += allCharacters[randomIndex];
        }
        return password;
    }

    const clear = () => {
        setFileList([]);
        ctx.setImportExclusiveReceivers([]);
        ctx.setImportExclusiveReceiversFile(undefined);
    };

    // useMount(() => {
    //     if (ctx.add && ctx.current) {
    //         setFileList(
    //             ctx.current.import_exclusive_receivers_file ? [{ uid: "", name: ctx.current.import_exclusive_receivers_file }] : [],
    //         );
    //     }
    // });

    return (
        <>
            <ImportExclusiveReceiversPreview />
            {fileList.length === 0 ? (
                <Row style={{ display: 'flex', alignItems: 'center', }}>
                    <Upload
                        accept=".xlsx"
                        beforeUpload={() => false}
                        fileList={fileList}
                        onChange={(v: any) => {
                            test(v.fileList[0].originFileObj)
                                .then((data: any) => {
                                    setFileList(v.fileList);
                                    console.log("kkk==" + JSON.stringify(data))
                                    ctx.setImportExclusiveReceivers(data);
                                    ctx.setImportExclusiveReceiversFile(v.fileList[0].originFileObj.name);
                                })
                                .catch((reason) => {
                                    message.error(reason);
                                });
                        }}
                    >
                        <Button type="dashed" icon={<UploadOutlined />}>
                            {intl.formatMessage({ id: "notice.exclude_recipient_list.email.batch" })}
                        </Button>
                    </Upload>
                    <Space size="small" style={{ marginLeft: "12px" }}>
                        <Typography.Text type="secondary">
                            {intl.formatMessage({ id: "notice.exclude_recipient_list.email.batch.tip" })}
                        </Typography.Text>
                        <Typography.Link onClick={template}>
                            {intl.formatMessage({ id: "common.download.template" })}
                        </Typography.Link>
                    </Space>
                </Row>
            ) : (
                <Space size={"middle"}>
                    <Typography.Text>
                        <LinkOutlined className="mr-5px" />
                        {fileList[0].name}
                    </Typography.Text>
                    <Typography.Link
                        onClick={() => {
                            ctx.setImportExclusiveReceiversPreview(true);
                        }}
                    >
                        {intl.formatMessage({ id: "common.details" })}
                    </Typography.Link>
                    <Typography.Link onClick={clear}>{intl.formatMessage({ id: "common.delete" })}</Typography.Link>
                </Space>
            )}
        </>
    );
};
