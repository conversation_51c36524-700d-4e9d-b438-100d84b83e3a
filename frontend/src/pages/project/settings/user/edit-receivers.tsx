import "@yaireo/tagify/dist/tagify.css";
import { useIntl } from "react-intl";
import { useEmails } from "../user/hooks";
// import Tags from "@yaireo/tagify/dist/react.tagify";
import { InputTag, Tag } from "@arco-design/web-react";
import { message } from "antd";
import '@arco-design/web-react/dist/css/arco.css'; // 引入样式

export const EditReceivers = (props: any) => {
    const intl = useIntl();
    const {form} = props
    const ctx = useEmails();

    return (
        <InputTag
            allowClear
            style={{
                backgroundColor: "#ffffff", 
                border: "1px solid #E3E4E6", 
                minHeight: "100px",  // 最小高度（允许内容过多时自动撑开）
            }}
            placeholder={intl.formatMessage({ id: "user.batch.add.email.required" })}
            className="w-full !bg-white !border !border-gray-500/20 !hover:border-blue-600"
            value={ctx.emailList}
            saveOnBlur
            // onChange={(v: any) => ctx.setEmailList(v)}
            onChange={(v: any) => {
                const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                // 过滤出符合邮箱规则的邮箱
                const validEmails = v.filter((email: any) => emailRegex.test(email.trim()));
                const trimmedArr = validEmails.map((item: any) => item.trim());
                const uniqueArray = [...new Set(trimmedArr)];
                ctx.setEmailList(uniqueArray);
                form.setFieldValue("emailList",uniqueArray); 
                form.validateFields(["emailList"]);
            }}
            onInputChange={(v)=>{    
                form.setFieldValue("emailList",v);   
                form.validateFields(["emailList"]);
            }}
            renderTag={({ label, value, closable, onClose }) => (
                <Tag
                    closable={closable}
                    onClose={onClose}
                    style={{
                        backgroundColor: "#f5f6f7",  // 统一背景色
                        color: "#2E323A",               // 文字颜色
                        border: "1px solid #DDDEDF", // 边框
                        margin: "4px 4px", // 上下 4px，左右 4px（保持默认间距）
                    }}
                >
                    {label}
                </Tag>
            )}
            // validate={(v: any) => {
            //     if (v) {
            //         const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            //         const ok = regex.test(v);
            //         if (!ok) {
            //             message.error(intl.formatMessage({ id: "notice.exclude_recipient_list.email.err" }));
            //             // v = null;
            //         }
            //         return ok;
            //     }
            //     return false;
            // }}
        />
        // <Tags
        //     // style={{ width: "100%" }}
        //     className="custom-tagify"
        //     settings={{
        //         pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        //         // pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        //         delimiters: ",|;| ",
        //     }}
        //     value={ctx.emailList}
        //     onChange={(e: any) => ctx.setEmailList(e.detail.tagify.getCleanValue().map((it: any) => it.value))}
        //     placeholder={intl.formatMessage({ id: "user.batch.add.email.required" })}
        // />
    );
};
