import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Drawer, message, Switch, Table} from "antd";
import {useFetch} from "../../../../hooks/request";
import {updateUserStorehouses} from "../../../../api/user";
import {projectUserStorehouses} from "../../../../api/project_site";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {permissions} from "../../../../tools/permission";

export const UserDepot = (props:any) => {

    const intl = useIntl();
    const {formatMessage} = intl;

    const auth = useAuth();

    const [visible, setVisible] = useSafeState<boolean>(false);
    const [data, setData] = useSafeState<any>(null); // 从env-users传递过来的用户信息（email、roles）
    const [storehousesData, setStorehousesData] = useSafeState<any>([]);
    const [userId, setUserId] = useSafeState<any>(null);
    const projectStatus = auth.project.status ? auth.project.status : 0

    const {runAsync: updateUserStorehousesRun, loading:updateUserStorehousesLoading} = useFetch(updateUserStorehouses, {manual: true})
    const {runAsync: projectUserStorehousesRun, loading:projectUserStorehousesLoading} = useFetch(projectUserStorehouses, {manual: true})




    const show = (data:any) => {
        if (data) {
            setData(data);
            setUserId(data.id);
            projectUserStorehousesRun({customerId:auth.customerId,envId: auth.env.id, userId: data.id}).then(
                (result:any) => {
                    if (result.data != null){
                        setStorehousesData(result.data)
                    }
                }
            )
        }
        setVisible(true);
    };

    const hide = () => {
        setVisible(false);
        setData(null);
        setStorehousesData([]);
        setUserId(null);
    };

    const switchChecked = (id?: string) => {
        updateUserStorehousesRun({customerId:auth.customerId,projectId:auth.project.id, envId:auth.env.id },
            { depots: storehousesData.filter((it:any) => it.checked || (id && it.id === id)).map((it:any) => it.value), userId: userId }).then(
            (resp:any) => {
                message.success(resp.msg)
                props.refresh()
            }
        )
    }

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Drawer
                className="drawer-width-percent"
                title={formatMessage({id: 'common.depot'}) + (data ? ` - ${data.info.name}` : '')}
                visible={visible}
                onClose={hide}
                
                maskClosable={false}
                bodyStyle={{padding: 24}}
                destroyOnClose
            >
                <Table
                    loading={projectUserStorehousesLoading}
                    size="small"
                    dataSource={storehousesData}
                    rowKey={(record:any) => record.value}
                    pagination={false}
                >
                    <Table.Column title={<FormattedMessage id="common.serial"/>} dataIndex="#" key="#" width={70} render={(text, record, index) => (index + 1)} />
                    <Table.Column title={<FormattedMessage id="projects.storehouse.name"/>} key="label" dataIndex="label" align="left" />
                    <Table.Column
                        title={formatMessage({id: 'common.operation'})} key="checked" dataIndex="checked" width={100}
                        render={
                            (text, record, index) => (
                                <Switch
                                    size="small"
                                    disabled={projectStatus===2 || data.unbind ||!permissions(auth.project.permissions, "operation.build.settings.user.depot")}
                                    checked={text}
                                    // checkedChildren={<CheckOutlined />} unCheckedChildren={<CloseOutlined />}
                                    onChange={
                                        checked => {
                                            const _data = [...storehousesData];
                                            _data[index].checked = checked;
                                            setStorehousesData(_data);
                                            switchChecked(_data[index].id)
                                        }
                                    }
                                />
                            )
                        }
                    />
                </Table>
            </Drawer>
        </React.Fragment>
    )
};

