import React, { useEffect } from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {<PERSON><PERSON>, Col, Drawer, message, Modal, Row, Switch, Table, Form, Checkbox, Tooltip, Spin, Popover, Dropdown, Menu, } from "antd";
import {useFetch} from "../../../../hooks/request";
import {list} from "../../../../api/roles";
import {useSafeState} from "ahooks";
import {useAuth} from "../../../../context/auth";
import {projectBatchUserSites, projectBatchUserStorehouses} from "../../../../api/project_site";
import {permissions} from "../../../../tools/permission";
import {updateUserRolesSitesDepots, getUserUnbindRoles} from "../../../../api/user";
import {
    CloseOutlined,
    DownOutlined,
  } from "@ant-design/icons";
import {CustomConfirmModal} from "../../../../components/modal";
import {useGlobal} from "../../../../context/global";
import { divide } from "lodash";
import { BatchSetSitesDepots } from "./batch_add_sites_depots/batch_set_sites_depots";
import {useProjectUser} from "./context";
import SubMenu from "antd/lib/menu/SubMenu";

export const UserAddRoles = (props: any) => {

    const g = useGlobal();
    const ctx = useProjectUser();

    const intl = useIntl();
    const {formatMessage} = intl;
    const auth = useAuth();

    const batch_add_set_sites_depots: any = React.useRef();

    const [form] = Form.useForm();

    const researchAttribute = auth.project.info.research_attribute === 1 ? 2 : 1;
    const projectStatus = auth.project.status ? auth.project.status : 0;

    const [roleCheckedMap, setRoleCheckedMap] = useSafeState<any>(new Map());
    const [roleSingleIndeterminateMap, setRoleSingleIndeterminateMap] = useSafeState<any>(new Map());
    const [roleSingleAllMap, setRoleSingleAllMap] = useSafeState<any>(new Map());

    const [siteIconColor, setSiteIconColor] = useSafeState(0);
    const [siteOpen, setSiteOpen] = useSafeState<any>(false);
    const [siteIndeterminate, setSiteIndeterminate] = useSafeState<any>(false);
    const [siteCheckAll, setSiteCheckAll] = useSafeState<any>(false);
    const [siteCheckedMap, setSiteCheckedMap] = useSafeState<any>(new Map());
    const [siteSingleIndeterminateMap, setSiteSingleIndeterminateMap] = useSafeState<any>(new Map());
    const [siteSingleAllMap, setSiteSingleAllMap] = useSafeState<any>(new Map());

    const [depotIconColor, setDepotIconColor] = useSafeState(0);
    const [depotOpen, setDepotOpen] = useSafeState<any>(false);
    const [depotIndeterminate, setDepotIndeterminate] = useSafeState<any>(false);
    const [depotCheckAll, setDepotCheckAll] = useSafeState<any>(false);
    const [depotCheckedMap, setDepotCheckedMap] = useSafeState<any>(new Map());
    const [depotSingleIndeterminateMap, setDepotSingleIndeterminateMap] = useSafeState<any>(new Map());
    const [depotSingleAllMap, setDepotSingleAllMap] = useSafeState<any>(new Map());

    const [unbind,setUnbind] = useSafeState(true);
    const [userList, setUserList] = useSafeState<any>([]); 
    const [visible, setVisible] = useSafeState<any>(false);

    const [sitesData, setSitesData] = useSafeState<any>([]);
    const [storehousesData, setStorehousesData] = useSafeState<any>([]);

    const [userRoleSiteDepotList, setUserRoleSiteDepotList] = useSafeState<any>([]);

    const { runAsync: projectBatchUserSitesRun, loading: projectBatchUserSitesLoading } = useFetch(projectBatchUserSites, { manual: true })

    const {runAsync: projectBatchUserStorehousesRun, loading:projectBatchUserStorehousesLoading} = useFetch(projectBatchUserStorehouses, {manual: true})

    const {runAsync: updateUserRolesSitesDepotsRun, loading:updateUserRolesSitesDepotsLoading} = useFetch(updateUserRolesSitesDepots, { manual: true })
    const {runAsync: getUserUnbindRolesRun, loading:getUserUnbindRolesLoading} = useFetch(getUserUnbindRoles, { manual: true })


    const show = (userData:any) => {
        setUserList(userData);
        setUnbind(false);
        let arr: any = [];
        userData.forEach((obj: any) => {
            setRoleCheckedMap((preRoleCheckedMap: any) => {
                const newRoleCheckedMap = new Map(preRoleCheckedMap);
                newRoleCheckedMap.set(obj.userId, []);
                return newRoleCheckedMap;
            });
            setSiteCheckedMap((preSiteCheckedMap: any) => {
                const newSiteCheckedMap = new Map(preSiteCheckedMap);
                newSiteCheckedMap.set(obj.userId, []);
                return newSiteCheckedMap;
            });
            setDepotCheckedMap((preDepotCheckedMap: any) => {
                const newDepotCheckedMap = new Map(preDepotCheckedMap);
                newDepotCheckedMap.set(obj.userId, []);
                return newDepotCheckedMap;
            });
            let userRoleSiteDepot: any = {};
            // 为对象添加属性和赋值
            userRoleSiteDepot.userId = obj.userId;
            userRoleSiteDepot.roleList = [];
            userRoleSiteDepot.siteList = [];
            userRoleSiteDepot.depotList = [];
            arr.push(userRoleSiteDepot);
        });
        setUserRoleSiteDepotList(arr);
        projectBatchUserSitesRun({ customerId: auth.customerId, envId: auth.env.id}).then(
            (result: any) => {
                if (result.code === 0 && result.data != null) {
                    setSitesData(result.data);
                }
            }
        );
        projectBatchUserStorehousesRun({customerId:auth.customerId,envId: auth.env.id}).then(
            (result:any) => {
                if (result.data != null){
                    setStorehousesData(result.data)
                }
            }
        );
        setVisible(true);
    };

    const hide = () => {
        setVisible(false);
        setSitesData([]);
        setStorehousesData([]);
        setUserRoleSiteDepotList([]);
        setUnbind(true);
        setSiteSingleIndeterminateMap(new Map());
        setSiteSingleAllMap(new Map());
        setDepotSingleIndeterminateMap(new Map());
        setDepotSingleAllMap(new Map());
        setRoleSingleIndeterminateMap(new Map())
        setRoleSingleAllMap(new Map());
        props.refresh();
    };

    const siteHide = () => {
        setSiteIndeterminate(false);
        setSiteCheckAll(false);
        setSiteOpen(false);
        setSiteIconColor(0);
    };

    const depotHide = () => {
        setDepotIndeterminate(false);
        setDepotCheckAll(false);
        setDepotOpen(false);
        setDepotIconColor(0);
    };

    const handleSiteOpenChange = (newOpen: boolean) => {
        if (newOpen != null) {
          if (newOpen === true) {
            setSiteIconColor(1);
          } else {
            setSiteIconColor(0);
          }
        }
        form.setFieldValue("sites", []);
        setSiteOpen(newOpen);
    };

    const handleDepotOpenChange = (newOpen: boolean) => {
        if (newOpen != null) {
          if (newOpen === true) {
            setDepotIconColor(1);
          } else {
            setDepotIconColor(0);
          }
        }
        form.setFieldValue("depots", []);
        setDepotOpen(newOpen);
    };

    const handleSiteMouseEnter = () => {
        setSiteIconColor(1);
    };
    
    const handleSiteMouseLeave = () => {
        setSiteIconColor(0);
    };

    const handleDepotMouseEnter = () => {
        setDepotIconColor(1);
    };
    
    const handleDepotMouseLeave = () => {
        setDepotIconColor(0);
    };


    const save = () => {
        getUserUnbindRolesRun(
            { customerId: auth.customerId, projectId: auth.project.id, envId: auth.env.id },
            { customerId: auth.customerId, projectId: auth.project.id, envId: auth.env.id, rolesSitesDepotList: userRoleSiteDepotList },
        ).then(
            (resp: any) => {
                let unbindRoleNames = resp.data
                if (unbindRoleNames.length > 0){
                    CustomConfirmModal({
                        title: formatMessage({id: 'env.user.edit.section3'}),
                        okText: formatMessage({id: 'common.ok'}),
                        content: formatMessage({id: 'env.user.edit.section4'}) + unbindRoleNames + formatMessage({id: 'env.user.edit.section5'}) ,
                        onOk: () => {
                            saveForm();
                        }
                    });
                }else{
                    saveForm();
                }
            }
        )

    };

    const saveForm = () => {
        updateUserRolesSitesDepotsRun(
            { customerId: auth.customerId, projectId: auth.project.id, envId: auth.env.id },
            { customerId: auth.customerId, projectId: auth.project.id, envId: auth.env.id, rolesSitesDepotList: userRoleSiteDepotList },
        ).then(
            (resp: any) => {
                message.success(resp.msg)
                props.refresh();
                hide();
            }
        )
    }

    const selectUserRole = (record: any, v: any) => {
        setRoleCheckedMap((preRoleCheckedMap: any) => {
            const newRoleCheckedMap = new Map(preRoleCheckedMap);
            newRoleCheckedMap.set(record.userId, v);
            return newRoleCheckedMap;
        });
        userRoleSiteDepotList.forEach((obj: any) => {
            if (obj.userId === record.userId) {
                obj.roleList = v;
            }
        });
    };
    

    const updateSiteAllClick = () => {
        userRoleSiteDepotList.forEach((obj: any) => {
            obj.siteList = form.getFieldValue("sites");
        });
        userList.forEach((obj: any) => {
            setSiteCheckedMap((preSiteCheckedMap: any) => {
                const newSiteCheckedMap = new Map(preSiteCheckedMap);
                newSiteCheckedMap.set(obj.userId, form.getFieldValue("sites"));
                return newSiteCheckedMap;
            });
        });
        siteHide();
    };

    const updateDepotAllClick = () => {
        userRoleSiteDepotList.forEach((obj: any) => {
            obj.depotList = form.getFieldValue("depots");
        });
        userList.forEach((obj: any) => {
            setDepotCheckedMap((preDepotCheckedMap: any) => {
                const newDepotCheckedMap = new Map(preDepotCheckedMap);
                newDepotCheckedMap.set(obj.userId, form.getFieldValue("depots"));
                return newDepotCheckedMap;
            });
        });
        depotHide();
    };

    useEffect(()=>{
        if(ctx.batchSelectionKey !== "" && ctx.batchSelectionData.size > 0){
            if(ctx.batchSelectionKey === "depots" && ctx.batchSelectionData.has("depots") && ctx.batchSelectionData.get("depots") !== undefined && ctx.batchSelectionData.get("depots") !== null){
                userRoleSiteDepotList.forEach((obj: any) => {
                    obj.depotList = ctx.batchSelectionData.get("depots");
                });
                userList.forEach((obj: any) => {
                    setDepotCheckedMap((preDepotCheckedMap: any) => {
                        const newDepotCheckedMap = new Map(preDepotCheckedMap);
                        newDepotCheckedMap.set(obj.userId, ctx.batchSelectionData.get("depots"));
                        return newDepotCheckedMap;
                    });
                    selectSingleAllUserDepot(obj, ctx.batchSelectionData.get("depots"));
                });
            };
            if(ctx.batchSelectionKey === "sites" && ctx.batchSelectionData.has("sites") && ctx.batchSelectionData.get("sites") !== undefined && ctx.batchSelectionData.get("sites") !== null){
                userRoleSiteDepotList.forEach((obj: any) => {
                    obj.siteList = ctx.batchSelectionData.get("sites");
                });
                userList.forEach((obj: any) => {
                    setSiteCheckedMap((preSiteCheckedMap: any) => {
                        const newSiteCheckedMap = new Map(preSiteCheckedMap);
                        newSiteCheckedMap.set(obj.userId, ctx.batchSelectionData.get("sites"));
                        return newSiteCheckedMap;
                    });
                    selectSingleAllUserSite(obj, ctx.batchSelectionData.get("sites"));
                });
            };
        }
    },[ctx.batchSelectionData, ctx.batchSelectionKey]);


    const onRoleSingleAllChange = (obj: any, e: any) => {
        // 使用map方法提取value属性组成新数组
        let valuesArray = obj.roleList.filter((item: any) => item.name !== "Project-Admin").map((item: any) => item.id);
        // setSiteCheckedList(e.target.checked ? valuesArray : []);
        setRoleSingleIndeterminateMap((preRoleSingleIndeterminateMap: any) => {
            const newRoleSingleIndeterminateMap = new Map(preRoleSingleIndeterminateMap);
            newRoleSingleIndeterminateMap.set(obj.userId, e.target.checked);
            return newRoleSingleIndeterminateMap;
        });
        setRoleSingleAllMap((preRoleSingleAllMap: any) => {
            const newRoleSingleAllMap = new Map(preRoleSingleAllMap);
            newRoleSingleAllMap.set(obj.userId, e.target.checked);
            return newRoleSingleAllMap;
        });
        selectSingleAllUserRole(obj, e.target.checked? valuesArray : []);
    };

    const selectSingleAllUserRole = (obj: any, v: any) => {
        let valuesArray = obj.roleList.filter((item: any) => item.name !== "Project-Admin").map((item: any) => item.id);
        // setSiteCheckedList(v);
        setRoleSingleIndeterminateMap((preRoleSingleIndeterminateMap: any) => {
            const newRoleSingleIndeterminateMap = new Map(preRoleSingleIndeterminateMap);
            newRoleSingleIndeterminateMap.set(obj.userId, !!v.length && v.length < valuesArray.length);
            return newRoleSingleIndeterminateMap;
        });
        setRoleSingleAllMap((preRoleSingleAllMap: any) => {
            const newRoleSingleAllMap = new Map(preRoleSingleAllMap);
            newRoleSingleAllMap.set(obj.userId, valuesArray.length === v.length);
            return newRoleSingleAllMap;
        });
        setRoleCheckedMap((preRoleCheckedMap: any) => {
            const newRoleCheckedMap = new Map(preRoleCheckedMap);
            newRoleCheckedMap.set(obj.userId, v);
            return newRoleCheckedMap;
        });
        userRoleSiteDepotList.forEach((record: any) => {
            if (record.userId === obj.userId) {
                record.roleList = v;
            }
        });
    };


    const onSiteCheckAllChange = (e: any) => {
        // 使用map方法提取value属性组成新数组
        let valuesArray = sitesData.map((item: any) => item.value);
        // setSiteCheckedList(e.target.checked ? valuesArray : []);
        form.setFieldValue("sites", e.target.checked ? valuesArray : []);
        setSiteIndeterminate(e.target.checked);
        setSiteCheckAll(e.target.checked);
        selectAllUserSite(e.target.checked? valuesArray : []);
    };

    const selectAllUserSite = (v: any) => {
        // setSiteCheckedList(v);
        setSiteIndeterminate(!!v.length && v.length < sitesData.length);
        setSiteCheckAll(sitesData.length === v.length);
    };


    const onSiteSingleAllChange = (obj: any, e: any) => {
        // 使用map方法提取value属性组成新数组
        let valuesArray = sitesData.map((item: any) => item.value);
        // setSiteCheckedList(e.target.checked ? valuesArray : []);
        setSiteSingleIndeterminateMap((preSiteSingleIndeterminateMap: any) => {
            const newSiteSingleIndeterminateMap = new Map(preSiteSingleIndeterminateMap);
            newSiteSingleIndeterminateMap.set(obj.userId, e.target.checked);
            return newSiteSingleIndeterminateMap;
        });
        setSiteSingleAllMap((preSiteSingleAllMap: any) => {
            const newSiteSingleAllMap = new Map(preSiteSingleAllMap);
            newSiteSingleAllMap.set(obj.userId, e.target.checked);
            return newSiteSingleAllMap;
        });
        selectSingleAllUserSite(obj, e.target.checked? valuesArray : []);
    };

    const selectSingleAllUserSite = (obj: any, v: any) => {
        // setSiteCheckedList(v);
        setSiteSingleIndeterminateMap((preSiteSingleIndeterminateMap: any) => {
            const newSiteSingleIndeterminateMap = new Map(preSiteSingleIndeterminateMap);
            newSiteSingleIndeterminateMap.set(obj.userId, !!v.length && v.length < sitesData.length);
            return newSiteSingleIndeterminateMap;
        });
        setSiteSingleAllMap((preSiteSingleAllMap: any) => {
            const newSiteSingleAllMap = new Map(preSiteSingleAllMap);
            newSiteSingleAllMap.set(obj.userId, sitesData.length === v.length);
            return newSiteSingleAllMap;
        });
        setSiteCheckedMap((preSiteCheckedMap: any) => {
            const newSiteCheckedMap = new Map(preSiteCheckedMap);
            newSiteCheckedMap.set(obj.userId, v);
            return newSiteCheckedMap;
        });
        userRoleSiteDepotList.forEach((record: any) => {
            if (record.userId === obj.userId) {
                record.siteList = v;
            }
        });
    };

    const onDepotCheckAllChange = (e: any) => {
        // 使用map方法提取value属性组成新数组
        let valuesArray = storehousesData.map((item: any) => item.value);
        form.setFieldValue("depots", e.target.checked ? valuesArray : []);
        setDepotIndeterminate(e.target.checked);
        setDepotCheckAll(e.target.checked);
        selectAllUserDepot(e.target.checked? valuesArray : []);
    };

    const selectAllUserDepot = (v: any) => {
        // setSiteCheckedList(v);
        setDepotIndeterminate(!!v.length && v.length < storehousesData.length);
        setDepotCheckAll(storehousesData.length === v.length);
    };

    const onDepotSingleAllChange = (obj: any, e: any) => {
        // 使用map方法提取value属性组成新数组
        let valuesArray = storehousesData.map((item: any) => item.value);
        setDepotSingleIndeterminateMap((preDepotSingleIndeterminateMap: any) => {
            const newDepotSingleIndeterminateMap = new Map(preDepotSingleIndeterminateMap);
            newDepotSingleIndeterminateMap.set(obj.userId, e.target.checked);
            return newDepotSingleIndeterminateMap;
        });
        setDepotSingleAllMap((preDepotSingleAllMap: any) => {
            const newDepotSingleAllMap = new Map(preDepotSingleAllMap);
            newDepotSingleAllMap.set(obj.userId, e.target.checked);
            return newDepotSingleAllMap;
        });
        // console.log("1==" + JSON.stringify(Object.fromEntries(depotSingleAllMap.entries())))
        selectSingleAllUserDepot(obj, e.target.checked? valuesArray : []);
    };

    
    const selectSingleAllUserDepot = (obj: any, v: any) => {
        // setSiteCheckedList(v);
        setDepotSingleIndeterminateMap((preDepotSingleIndeterminateMap: any) => {
            const newDepotSingleIndeterminateMap = new Map(preDepotSingleIndeterminateMap);
            newDepotSingleIndeterminateMap.set(obj.userId, !!v.length && v.length < storehousesData.length);
            return newDepotSingleIndeterminateMap;
        });
        setDepotSingleAllMap((preDepotSingleAllMap: any) => {
            const newDepotSingleAllMap = new Map(preDepotSingleAllMap);
            newDepotSingleAllMap.set(obj.userId, storehousesData.length === v.length);
            return newDepotSingleAllMap;
        });
        setDepotCheckedMap((preDepotCheckedMap: any) => {
            const newDepotCheckedMap = new Map(preDepotCheckedMap);
            newDepotCheckedMap.set(obj.userId, v);
            return newDepotCheckedMap;
        });
        userRoleSiteDepotList.forEach((record: any) => {
            if (record.userId === obj.userId) {
                record.depotList = v;
            }
        });

    };


    function statusItem(it:any) {
        let color: string;
        if (it.scope === "study") {
            color = "red"
        } else if (it.scope === "site") {
            color = "orange"
        } else {
            color = "green"
        }
        return it.name;
    }

    function getWidth() {
        let max = 980
        let width: any;
        if ((projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.role")) && 
            (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.site")) &&
            (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.depot"))) {
                width = max/5 - 5
        } else if (((projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.role")) && 
            (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.site")) &&
            !(projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.depot"))) || 
            (!(projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.role")) && 
            (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.site")) &&
            (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.depot"))) || 
            ((projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.role")) && 
            !(projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.site")) &&
            (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.depot")))) {
                width = max/4 - 5
        } else if (((projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.role")) && 
            !(projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.site")) &&
            !(projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.depot"))) || 
            (!(projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.role")) && 
            (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.site")) &&
            !(projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.depot"))) || 
            (!(projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.role")) && 
            !(projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.site")) &&
            (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.depot")))) {
                width = max/3 - 5
        } 
        return width + "px";
    }

    // 批量设置--中心、仓库 按钮单击事件
    const buttonMenuClick = (e: any) => {
        if (e.key === "depots") {
            //批量设置--仓库
            batch_add_set_sites_depots.current.show(e.key, storehousesData, form);
        } else if (e.key === "sites") {
            //批量设置--中心
            batch_add_set_sites_depots.current.show(e.key, sitesData, form);
        }
    };

    const menu = (
        <Menu 
            onClick={buttonMenuClick} 
            disabled={((sitesData === undefined && sitesData === null && sitesData.length === 0) && (storehousesData === undefined || storehousesData === null || storehousesData.length === 0))}
            mode="inline" // 必须设置为 inline 模式
            triggerSubMenuAction="hover" // 设置悬停触发
        >
            <Menu.Item key="depots" disabled={storehousesData === undefined && storehousesData === null && storehousesData.length === 0}>{formatMessage({ id: "common.depot" })} </Menu.Item>
            <Menu.Item key="sites" disabled={sitesData === undefined && sitesData === null && sitesData.length === 0}>{formatMessage({ id: "common.site" })} </Menu.Item>
        </Menu>
    );

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Modal
                className={"custom-batch-add-modal-batch"}
                title={<FormattedMessage id="common.user.add.configure.permissions" />}
                open={visible}
                onCancel={hide}
                centered
                maskClosable={false}
                destroyOnClose={true}
                footer={
                    <Row justify="end">
                            <Col>
                                <Button onClick={hide}>
                                    <FormattedMessage id="common.cancel" />
                                </Button>
                                <Button type="primary" onClick={save} loading={updateUserRolesSitesDepotsLoading || getUserUnbindRolesLoading}>
                                    <FormattedMessage id="common.ok" />
                                </Button>
                            </Col>

                    </Row>
                }
            >
                <Spin spinning={projectBatchUserSitesLoading || projectBatchUserStorehousesLoading}>
                    <Row justify="end" style={{marginTop: 20, marginBottom: 20}}>
                        <Col>
                            {
                                (projectStatus !== 2 && !unbind && (permissions(auth.project.permissions, "operation.build.settings.user.depot") || permissions(auth.project.permissions, "operation.build.settings.user.site")))?
                                <span style={{marginLeft: 8}}>
                                    <Dropdown
                                        overlay={menu}
                                        trigger={['hover','click']}
                                    >
                                        <Button>
                                            {formatMessage({ id: "common.setting.batch" })} <DownOutlined />
                                        </Button>
                                    </Dropdown>
                                </span>
                                :null
                            }
                        </Col>
                    </Row>
                    <Form form={form} >
                        {/* <div style={{display: "flex", justifyItems: "center", alignItems: "center", marginTop: "6px", marginBottom: "16px"}}><span className="blue-bar" style={{fontWeight:700}}></span><FormattedMessage id={'common.common'}/></div> */}
                        <Table
                            style={{marginTop:16}}
                            dataSource={userList}
                            pagination={false}
                            bordered
                            rowKey={(record: any) => (record.userId)}
                        >
                            <Table.Column 
                                title={<FormattedMessage id="common.full_name"/>}
                                dataIndex={'userName'} 
                                key="userName" 
                                ellipsis
                                className="custom-left-top-align"
                                render={
                                    (value, record: any) => (
                                        <span>
                                            {
                                                (value !== undefined && value !== null && value !== "")?
                                                value:"-"
                                            }
                                        </span>
                                    )
                                }
                            />
                            <Table.Column 
                                title={<FormattedMessage id="common.email"/>}
                                dataIndex={'email'} 
                                key="email" 
                                ellipsis
                                className="custom-left-top-align"
                                render={
                                    (value, record: any) => (
                                        <span>
                                            {
                                                (value !== undefined && value !== null && value !== "")?
                                                value:"-"
                                            }
                                        </span>
                                    )
                                }
                            />
                            {
                                (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.role"))?
                                <Table.Column 
                                    title={<FormattedMessage id="common.role"/>}
                                    dataIndex={'roleList'} 
                                    key="roleList" 
                                    ellipsis
                                    className="custom-left-top-align"
                                    render={(value: any, record: any, index: any) => 
                                        (
                                            <Form.Item
                                                // label={formatMessage({ id: "common.role" })}
                                                // name="roleList"
                                            >
                                                {
                                                    (value !== undefined && value !== null && value.length > 0)?
                                                    <div>
                                                        <Checkbox indeterminate={roleSingleIndeterminateMap?.get(record.userId)} onChange={(v:any) => {onRoleSingleAllChange(record, v)}} checked={roleSingleAllMap?.get(record.userId)}>
                                                            {formatMessage({id: "common.all.select",})}
                                                        </Checkbox>
                                                        <Checkbox.Group 
                                                            style={{ display: 'block' }} 
                                                            onChange={(v:any) => {selectSingleAllUserRole(record, v)}}
                                                            value={roleCheckedMap?.get(record.userId)}
                                                        >
                                                            <Row
                                                                gutter={24}>{value.filter((it: any) => it.status === 1 && it.template === researchAttribute && it.name !== "Customer-Admin" && it.name !== "Sys-Admin")
                                                                .map((it: any) =>
                                                                    <Col span={24} key={it.id}
                                                                        style={{ marginTop:8, }}
                                                                    >
                                                                        {/* <Checkbox disabled={it.name === "Project-Admin"}
                                                                                value={it.id}>{statusItem(it)}
                                                                        </Checkbox> */}
                                                                        <Checkbox 
                                                                            disabled={it.name === "Project-Admin"}
                                                                            value={it.id}
                                                                        >
                                                                            <Tooltip title={statusItem(it)}>
                                                                                <span 
                                                                                    style={{
                                                                                        display: 'inline-block',
                                                                                        // maxWidth: "100%", /* 根据父容器宽度自动调整 */
                                                                                        maxWidth:getWidth(),
                                                                                        whiteSpace: 'nowrap',
                                                                                        overflow: 'hidden',
                                                                                        textOverflow: 'ellipsis',
                                                                                        marginBottom:-4,
                                                                                        boxSizing: 'border-box', // 确保padding和border不会影响宽度计算
                                                                                    }}
                                                                                >
                                                                                    {statusItem(it)}
                                                                                </span>
                                                                            </Tooltip>
                                                                        </Checkbox>
                                                                    </Col>
                                                                )}
                                                            </Row>
                                                        </Checkbox.Group>
                                                    </div>:"-"
                                                }

                                            </Form.Item>
                                        )
                                    }
                                />:null
                            }

                            {
                                (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.site"))?
                                <Table.Column 
                                    title={
                                        <span>
                                            <FormattedMessage id="common.site"/>
                                            {/* <Popover
                                                placement="bottom"
                                                open={siteOpen}
                                                overlayStyle={{
                                                    height: '514px',
                                                    width: "328px",
                                                }}
                                                title={
                                                    <div>
                                                        {formatMessage({id: "common.setting.batch",})}
                                                        <Button
                                                            onClick={siteHide}
                                                            type="text"
                                                            icon={
                                                                <CloseOutlined
                                                                    style={{
                                                                        color: "#666666",
                                                                    }}
                                                                />
                                                            }
                                                            style={{
                                                                float: "right",
                                                                marginRight: -4,
                                                                width: "15px",
                                                                height: "15px",
                                                            }}
                                                        />
                                                    </div>
                                                }
                                                content={
                                                    <div>
                                                        <div
                                                            style={{
                                                                height: '342px',
                                                                maxHeight: "342px",
                                                                overflowX: "hidden",
                                                            }}
                                                        >
                                                            <Checkbox indeterminate={siteIndeterminate} onChange={onSiteCheckAllChange} checked={siteCheckAll}>
                                                                {formatMessage({id: "common.all.select",})}
                                                            </Checkbox>
                                                            <Form.Item
                                                                name="sites"
                                                            >
                                                                <Checkbox.Group 
                                                                    style={{ display: 'block', marginLeft: "24px"}} 
                                                                    onChange={(v:any) => {selectAllUserSite(v)}}
                                                                >
                                                                    <Row
                                                                        gutter={24}>{sitesData.map((it: any) =>
                                                                            <Col span={24} key={it.value}
                                                                                style={{ marginTop:8}}
                                                                            >
                                                                                <Checkbox value={it.value}>
                                                                                    <Tooltip title={it.label}>
                                                                                        <span 
                                                                                            style={{
                                                                                                display: 'inline-block',
                                                                                                maxWidth: "244px",
                                                                                                whiteSpace: 'nowrap',
                                                                                                overflow: 'hidden',
                                                                                                textOverflow: 'ellipsis',
                                                                                                marginBottom:-4,
                                                                                                boxSizing: 'border-box', // 确保padding和border不会影响宽度计算
                                                                                            }}
                                                                                        >
                                                                                            {it.label}
                                                                                        </span>
                                                                                    </Tooltip>
                                                                                </Checkbox>
                                                                            </Col>
                                                                        )}
                                                                    </Row>
                                                                </Checkbox.Group>
                                                            </Form.Item>
                                                        </div>
                                                        <div style={{height: "20px"}}>
                                                            <Row justify="end">
                                                                <span style={{ marginRight: "16px", marginTop: "0px",}} >
                                                                    <Button 
                                                                        type="primary" 
                                                                        onClick={updateSiteAllClick}
                                                                        style={{ 
                                                                            width: g.lang === "zh"?40:60, 
                                                                            height: 22,
                                                                            display: 'flex',
                                                                            justifyContent: 'center',
                                                                            alignItems: 'center',
                                                                            marginBottom: -20,
                                                                            marginLeft:"280px",
                                                                        }} 
                                                                    >
                                                                        <span 
                                                                            style={{
                                                                                fontSize: 12,
                                                                                fontWeight: 400,
                                                                                textAlign: 'center'
                                                                            }}
                                                                        >
                                                                            {formatMessage({ id: "common.ok" })}
                                                                        </span>
                                                                    </Button>
                                                                </span>
                                                            </Row>
                                                        </div>
                                                    </div>
                                                }
                                                style={{
                                                    height: "180px",
                                                    width: "260px !important",
                                                    left: "496px",
                                                    top: "155px",
                                                    borderRadius: "2px",
                                                    marginTop: "16px",
                                                    marginLeft: "12px",
                                                }}
                                                trigger="click"
                                                onOpenChange={handleSiteOpenChange}
                                            >
                                                <Tooltip
                                                    title={formatMessage({id: "common.setting.batch",})}
                                                >
                                                    <i
                                                        style={{
                                                            marginLeft: 8,
                                                            cursor: "pointer",
                                                            color: siteIconColor === 0 ? "#999999" : "#165DFF",
                                                        }}
                                                        className="iconfont icon-bianji"
                                                        onMouseEnter={handleSiteMouseEnter}
                                                        onMouseLeave={handleSiteMouseLeave}
                                                    ></i>
                                                </Tooltip>
                                            </Popover> */}
                                        </span>
                                    }
                                    dataIndex={'siteList'} 
                                    key="siteList" 
                                    ellipsis
                                    className="custom-left-top-align"
                                    render={(value: any, record: any, index: any) => 
                                        (
                                            <div>
                                                {
                                                    (sitesData !== undefined && sitesData !== null && sitesData.length > 0)?
                                                    <div>
                                                        <Checkbox indeterminate={siteSingleIndeterminateMap?.get(record.userId)} onChange={(v:any) => {onSiteSingleAllChange(record, v)}} checked={siteSingleAllMap?.get(record.userId)}>
                                                            {formatMessage({id: "common.all.select",})}
                                                        </Checkbox>
                                                        <Form.Item
                                                            // label={formatMessage({ id: "common.site" })}
                                                            // name="siteList"
                                                        >
                                                                <Checkbox.Group 
                                                                    style={{ display: 'block' }} 
                                                                    onChange={(v:any) => {selectSingleAllUserSite(record, v)}}
                                                                    value={siteCheckedMap?.get(record.userId)}
                                                                >
                                                                    <Row
                                                                        gutter={24}
                                                                    >
                                                                            {sitesData.map((it: any) =>
                                                                            <Col span={24} key={it.value}
                                                                                style={{ marginTop:8}}
                                                                            >
                                                                                {/* <Checkbox value={it.value}>{it.label}</Checkbox> */}
                                                                                <Checkbox value={it.value}>
                                                                                    <Tooltip title={it.label}>
                                                                                        <span 
                                                                                            style={{
                                                                                                display: 'inline-block',
                                                                                                // maxWidth: "100%", /* 根据父容器宽度自动调整 */
                                                                                                maxWidth:getWidth(),
                                                                                                whiteSpace: 'nowrap',
                                                                                                overflow: 'hidden',
                                                                                                textOverflow: 'ellipsis',
                                                                                                marginBottom:-4,
                                                                                                boxSizing: 'border-box', // 确保padding和border不会影响宽度计算
                                                                                            }}
                                                                                        >
                                                                                            {it.label}
                                                                                        </span>
                                                                                    </Tooltip>
                                                                                </Checkbox>
                                                                            </Col>
                                                                        )}
                                                                    </Row>
                                                                </Checkbox.Group>
                                                        </Form.Item>
                                                    </div>:"-"
                                                }
                                            </div>
                                        )
                                    }
                                />:null
                            }

                            {
                                (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.depot"))?
                                <Table.Column 
                                    title={
                                        <span>
                                            <FormattedMessage id="common.depot"/>
                                            {/* <Popover
                                                placement="bottom"
                                                open={depotOpen}
                                                overlayStyle={{
                                                    height: '514px',
                                                    width: "328px",
                                                }}
                                                title={
                                                    <div>
                                                        {formatMessage({id: "common.setting.batch",})}
                                                        <Button
                                                            onClick={depotHide}
                                                            type="text"
                                                            icon={
                                                                <CloseOutlined
                                                                    style={{
                                                                    color: "#666666",
                                                                    }}
                                                                />
                                                            }
                                                            style={{
                                                                float: "right",
                                                                marginRight: -4,
                                                                width: "15px",
                                                                height: "15px",
                                                            }}
                                                        />
                                                    </div>
                                                }
                                                content={
                                                    <div>
                                                        <div
                                                            style={{
                                                                height: '342px',
                                                                maxHeight: "342px",
                                                                overflowX: "hidden",
                                                            }}
                                                        >
                                                            <Checkbox indeterminate={depotIndeterminate} onChange={onDepotCheckAllChange} checked={depotCheckAll}>
                                                                {formatMessage({id: "common.all.select",})}
                                                            </Checkbox>
                                                            <Form.Item
                                                                name="depots"
                                                            >
                                                                <Checkbox.Group 
                                                                    style={{ display: 'block', marginLeft: "24px"}} 
                                                                    onChange={(v:any) => {selectAllUserDepot(v)}}
                                                                >
                                                                    <Row
                                                                        gutter={24}>{storehousesData.map((it: any) =>
                                                                            <Col span={24} key={it.value}
                                                                                style={{ marginTop:8}}
                                                                            >
                                                                                <Checkbox value={it.value}>
                                                                                    <Tooltip title={it.label}>
                                                                                        <span 
                                                                                            style={{
                                                                                                display: 'inline-block',
                                                                                                maxWidth: "244px",
                                                                                                whiteSpace: 'nowrap',
                                                                                                overflow: 'hidden',
                                                                                                textOverflow: 'ellipsis',
                                                                                                marginBottom:-4,
                                                                                                boxSizing: 'border-box', // 确保padding和border不会影响宽度计算
                                                                                            }}
                                                                                        >
                                                                                            {it.label}
                                                                                        </span>
                                                                                    </Tooltip>
                                                                                </Checkbox>
                                                                            </Col>
                                                                        )}
                                                                    </Row>
                                                                </Checkbox.Group>
                                                            </Form.Item>
                                                        </div>
                                                        <div style={{height: "20px"}}>
                                                            <Row justify="end">
                                                                <span style={{ marginRight: "16px",  marginTop: "0px", }}>
                                                                    <Button 
                                                                        type="primary" 
                                                                        onClick={updateDepotAllClick}
                                                                        style={{ 
                                                                            width: g.lang === "zh"?40:60, 
                                                                            height: 22,
                                                                            display: 'flex',
                                                                            justifyContent: 'center',
                                                                            alignItems: 'center',
                                                                            marginBottom: -20,
                                                                            marginLeft:"280px",
                                                                        }} 
                                                                    >
                                                                        <span 
                                                                            style={{
                                                                                fontSize: 12,
                                                                                fontWeight: 400,
                                                                                textAlign: 'center'
                                                                            }}
                                                                        >
                                                                            {formatMessage({ id: "common.ok" })}
                                                                        </span>
                                                                    </Button>
                                                                </span>
                                                            </Row>
                                                        </div>
                                                    </div>
                                                }
                                                style={{
                                                    height: "180px",
                                                    width: "260px !important",
                                                    left: "496px",
                                                    top: "155px",
                                                    borderRadius: "2px",
                                                    marginTop: "16px",
                                                    marginLeft: "12px",
                                                }}
                                                trigger="click"
                                                onOpenChange={handleDepotOpenChange}
                                            >
                                                <Tooltip
                                                    title={formatMessage({id: "common.setting.batch",})}
                                                >
                                                    <i
                                                        style={{
                                                            marginLeft: 8,
                                                            cursor: "pointer",
                                                            color: depotIconColor === 0 ? "#999999" : "#165DFF",
                                                        }}
                                                        className="iconfont icon-bianji"
                                                        onMouseEnter={handleDepotMouseEnter}
                                                        onMouseLeave={handleDepotMouseLeave}
                                                    ></i>
                                                </Tooltip>
                                            </Popover> */}
                                        </span>
                                    }
                                    dataIndex={'depotList'} 
                                    key="depotList" 
                                    ellipsis
                                    className="custom-left-top-align"
                                    render={(value: any, record: any, index: any) => 
                                        (
                                            <div>
                                                {
                                                    (storehousesData !== undefined && storehousesData !== null && storehousesData.length > 0)?
                                                    <div>
                                                        <Checkbox indeterminate={depotSingleIndeterminateMap?.get(record.userId)} onChange={(v:any) => {onDepotSingleAllChange(record, v)}}  checked={depotSingleAllMap?.get(record.userId)}>
                                                            {formatMessage({id: "common.all.select",})}
                                                        </Checkbox>
                                                        <Form.Item
                                                            // label={formatMessage({ id: "common.depot" })}
                                                            // name="depotList"
                                                        >
                                                                <Checkbox.Group 
                                                                    style={{ display: 'block' }} 
                                                                    onChange={(v:any) => {selectSingleAllUserDepot(record, v)}}
                                                                    value={depotCheckedMap?.get(record.userId)}
                                                                >
                                                                    <Row
                                                                        gutter={24}>{storehousesData.map((it: any) =>
                                                                            <Col span={24} key={it.value}
                                                                                // style={{ height: 32, display: "flex", width: "420px" }}
                                                                                style={{ marginTop:8}}
                                                                            >
                                                                                {/* <Checkbox value={it.value}>{it.label}</Checkbox> */}
                                                                                <Checkbox value={it.value}>
                                                                                    <Tooltip title={it.label}>
                                                                                        <span 
                                                                                            style={{
                                                                                                display: 'inline-block',
                                                                                                // maxWidth: "100%", /* 根据父容器宽度自动调整 */
                                                                                                maxWidth:getWidth(),
                                                                                                whiteSpace: 'nowrap',
                                                                                                overflow: 'hidden',
                                                                                                textOverflow: 'ellipsis',
                                                                                                marginBottom:-4,
                                                                                                boxSizing: 'border-box', // 确保padding和border不会影响宽度计算
                                                                                            }}
                                                                                        >
                                                                                            {it.label}
                                                                                        </span>
                                                                                    </Tooltip>
                                                                                </Checkbox>
                                                                            </Col>
                                                                        )}
                                                                    </Row>
                                                                </Checkbox.Group>
                                                        </Form.Item>
                                                    </div>:"-"
                                                }
                                            </div>
                                        )
                                    }
                                />:null
                            }

                        </Table>
                    </Form>
                </Spin>
            </Modal>
            <BatchSetSitesDepots bind={batch_add_set_sites_depots}/>
        </React.Fragment>
    );
};
