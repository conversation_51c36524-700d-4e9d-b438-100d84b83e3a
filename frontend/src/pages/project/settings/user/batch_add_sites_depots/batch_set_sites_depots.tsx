import React, { useEffect } from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, Input, message, Modal, Select, Radio, Space, Col, Row, Table, Switch, notification, But<PERSON>, Spin, } from "antd";
import {useSafeState} from "ahooks";
import {PlusOutlined, CloseCircleFilled, SearchOutlined, } from "@ant-design/icons";
import styled from "@emotion/styled";
import { useFetch } from "hooks/request";
import { json } from "stream/consumers";
import {useGlobal} from "../../../../../context/global";
import { PaginationView } from "./pagination";
import {useProjectUser} from "../context";
import { useCacheTable } from "./cache-table";

export const BatchSetSitesDepots = (props :any) => {

    const intl = useIntl();
    const {formatMessage} = intl;
    const g = useGlobal();
    const ctx = useProjectUser();

    const [visible, setVisible] = useSafeState<any>(false);


    const [selectedRowKeys, setSelectedRowKeys] = useSafeState<any[]>([]);

    const [key, setKey] = useSafeState<any>(null)
    const [data, setData] = useSafeState<any[]>([])

    const [oldForm, setOldForm] = useSafeState<any[]>([])

    const [form] = Form.useForm();

    const show = (key: any, data: any, oldForm: any) => {
        setVisible(true);
        setKey(key);
        // console.log("kkk==" + JSON.stringify(data));
        setData(data);
        setOldForm(oldForm);

    };

    const hide = () => {
        setVisible(false);
        setKey(null);
        setData([]);
        setSelectedRowKeys([]);
        form.resetFields();
    };
    useEffect(()=>{

    },[]);

    const save = () => {
        ctx.setBatchSelectionKey(key);
        ctx.setBatchSelectionData((preBatchSelectionData: any) => {
            const newBatchSelectionData = new Map(preBatchSelectionData);
            newBatchSelectionData.set(key, selectedRowKeys);
            return newBatchSelectionData;
        });
        hide();
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 5: 7 },
        },

    }

    const refresh = () => {
        setSelectedRowKeys([]);
    }

    const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
        setSelectedRowKeys(newSelectedRowKeys);
    };

    const rowSelection: any = {
        selectedRowKeys,
        type: "checkbox",
        onChange: onSelectChange,
        preserveSelectedRowKeys: true,
    };

    return (
        <React.Fragment>
            <Modal
                title={key === "depots"?intl.formatMessage({ id: "common.setting.batch" }) + intl.formatMessage({ id: "common.depot" }):intl.formatMessage({ id: "common.setting.batch" }) + intl.formatMessage({ id: "common.site" }) }
                visible={visible}
                onCancel={hide}
                
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-medium-modal-batch-sites-depots'
                okText={formatMessage({id: 'common.ok'})}
                onOk={save}
            >
                <div>
                    <Table
                        className="ant-table-cell-style mar-top-10"
                        size="small"
                        pagination={false}
                        rowKey={(record) => record.value}
                        dataSource={useCacheTable({ dataSource: data })}
                        // rowSelection={rowSelection}
                        rowSelection={{
                            ...rowSelection,
                            columnWidth: 20, // 可以尝试设置这个值
                        }}
                    >
                        <Table.Column
                            title={intl.formatMessage({ id: "common.serial" })}
                            dataIndex="#"
                            key="#"
                            width={70}
                            render={(text, record, index) => ((ctx.currentPage - 1) * ctx.pageSize + index + 1)}
                        />
                        <Table.Column
                            width={150}
                            title={key === "depots"?intl.formatMessage({ id: "common.depot" }):intl.formatMessage({ id: "common.site" }) }
                            dataIndex="label"
                            key="label"
                            ellipsis
                            render={(value: any, record: any, index: any) =>value}
                            filterIcon={(filtered: boolean) => (
                                <SearchOutlined style={{color: filtered ? "#1890ff" : "#677283"}}/>
                            )}
                            filterDropdown={({setSelectedKeys, selectedKeys, confirm}) => (
                                <div style={{padding: "8px 12px"}} className={"filename-search"}>
                                    <Input.Search
                                        placeholder={formatMessage({id: "common.required.prefix"})}
                                        enterButton
                                        value={selectedKeys[0]}
                                        onChange={(e) => {
                                            setSelectedKeys(e.target.value ? [e.target.value] : []);
                                        }}
                                        onSearch={() => confirm()}
                                        onPressEnter={() => confirm()}
                                    />
                                </div>
                            )}
                            onFilter={(value, record: any) => {
                                return record.label.toLowerCase().includes((value as string).toLowerCase());
                            }}
                        />
                    </Table>
                    {/* <PaginationView /> */}
                    <PaginationView
                        mode={"SELECTABLE"}
                        // mode={undefined}
                        selectedNumber={selectedRowKeys.length}
                        clearDisplay={true}
                        refresh={refresh}
                    />
                </div>
            </Modal>
        </React.Fragment>
    )
};

const SelectInput = styled.div`
    margin: 6px 0 0 0;
    .ant-input-group-addon {
        background: unset !important;
        border: unset !important;
    }
`