import React, { useEffect } from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {
    Button,
    Checkbox,
    Col,
    Form,
    Input,
    message as antd_message,
    Modal,
    Popover,
    Row,
    Spin,
    Tag,
    Tooltip,
    Select,
    Radio,
    Space,
} from "antd";

import {useFetch} from "../../../../hooks/request";
import {
    addProjectEnvironmentBatchUserVerify,
    addProjectEnvironmentBatchUser,
} from "../../../../api/projects";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {permissions} from "../../../../tools/permission";
import {useGlobal} from "../../../../context/global";
import { EditReceivers } from "./edit-receivers";
import {useProjectUser} from "./context";
import { UserAddPrompt } from "./env_user_batch_add_prompt";
import { UserAddTip } from "./env_user_batch_add_tip";


export const EnvUserBatchAdd = (props:any) => {

    const ctx = useProjectUser();
    const auth = useAuth();

    const customerId = auth.customerId;
    const projectStatus = auth.project.status ? auth.project.status : 0;

    const user_prompt: any = React.useRef();
    const user_tip: any = React.useRef();

    const intl = useIntl();
    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [form] = Form.useForm();
    const [unbind,setUnbind] = useSafeState(true);
    const {runAsync: addProjectEnvironmentBatchUserVerifyRun, loading:addProjectEnvironmentBatchUserVerifyLoading} = useFetch(addProjectEnvironmentBatchUserVerify, {manual: true})
    const {runAsync: addProjectEnvironmentBatchUserRun, loading:addProjectEnvironmentBatchUserLoading} = useFetch(addProjectEnvironmentBatchUser, {manual: true})


    const show = (data:any) => {
        setUnbind(false)
        setVisible(true);
    };

    const hide = () => {
        form.resetFields();
        setVisible(false);
        setUnbind(true);
        ctx.setEmailList([]);
        props.refresh();
    };

    useEffect(()=>{
        form.setFieldValue("emailList",  ctx.emailList);
    },[ctx.emailList]);

    // 提交数据
    const save = (values: any) => {
        const emailList = ctx.emailList;
        form.validateFields()
            .then(values => {
                const emailLanguage = values.emailLanguage;
                addProjectEnvironmentBatchUserVerifyRun({projectId: auth.project.id, envId:auth.env.id},
                    {
                        customerId: customerId,
                        projectId: auth.project.id,
                        envId: auth.env.id,
                        emailLanguage: emailLanguage,
                        emailList: emailList,
                    },).then(
                    (resp:any) => {
                        let data = resp.data
                        if (data !== undefined && data !== null && data.length > 0) {
                            user_prompt.current.show(customerId, emailLanguage, emailList, data);
                        } else {
                            addProjectEnvironmentBatchUserRun({projectId: auth.project.id, envId:auth.env.id},
                                {
                                    customerId: customerId,
                                    projectId: auth.project.id,
                                    envId: auth.env.id,
                                    emailLanguage: emailLanguage,
                                    emailList: emailList,
                                },).then(
                                (result:any) => {
                                    if((projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.role")) ||
                                        (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.site")) ||
                                        (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.depot"))){
                                            let data = result.data
                                            if (data !== undefined && data !== null && data.length > 0) {
                                                user_tip.current.show(data);
                                                hide();
                                            }
                                    } else {
                                        antd_message.success(formatMessage({id: 'common.success'}))
                                        hide();
                                    }
                                }
                            )
                        }
                    }
                )
            })
            .catch(error => {
            })
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const language = [
        {label:formatMessage({id:"common.email.language.zh"}), value:"zh"},
        {label:formatMessage({id:"common.email.language.en"}), value:"en"},
    ]

    const g = useGlobal()
    const formItemLayout = {
        labelCol: { style: {   width: g.lang === "en"? "120px": "88px"} },
    };

    return (
        <React.Fragment>
            <Modal
                className={"custom-small-modal"}
                title={<FormattedMessage id="common.add" />}
                open={visible}
                onCancel={hide}
                centered
                maskClosable={false}
                destroyOnClose={true}
                footer={
                    <Row justify="space-between">
                        <Col>

                        </Col>
                        {
                            (projectStatus !== 2 && !unbind && permissions(auth.project.permissions, "operation.build.settings.user.add"))?
                            <Col>
                                <Button onClick={hide}>
                                    <FormattedMessage id="common.cancel" />
                                </Button>
                                <Button type="primary" loading={addProjectEnvironmentBatchUserVerifyLoading || addProjectEnvironmentBatchUserLoading} onClick={save}>
                                    <FormattedMessage id="common.ok" />
                                </Button>
                            </Col>
                            :null
                        }

                    </Row>
                }
            >
                <Form form={form} {...formItemLayout}>
                    {
                        <Form.Item
                            label={formatMessage({ id: 'common.email.language' })}
                            name="emailLanguage"
                            style={{marginBottom:24}}
                            rules={[{ required: true, message: formatMessage({ id: 'placeholder.select.common.email.language' })}]}
                            // initialValue={"zh"}
                        >
                            {/* <Select 
                                className="full-width" 
                                options={language}
                            >
                            </Select> */}
                            <Radio.Group 
                                className="full-width"
                                disabled={props.disabled}
                            >
                                <Space >
                                    <Col
                                        style={{ marginRight: 24 }}
                                    >
                                        <Radio value={"zh"}>
                                            {
                                                intl.formatMessage({id: "common.email.language.zh"})
                                            }
                                        </Radio>
                                    </Col>
                                    <Col
                                        style={{ marginRight: 24 }}
                                    >
                                        <Radio value={"en"}>
                                            {
                                                intl.formatMessage({id: "common.email.language.en"})
                                            }
                                        </Radio>
                                    </Col>
                                </Space>

                            </Radio.Group>
                        </Form.Item>
                    }
                    <Form.Item
                        label={formatMessage({ id: 'common.email' })}
                        name="emailList"
                        style={{marginBottom:0}}
                        rules={[{ required: true, }]}
                    >
                        <EditReceivers form={form}/>
                        {/* <Input allowClear placeholder={formatMessage({id: "placeholder.input.common"})}/> */}
                    </Form.Item>
                </Form>
            </Modal>
            <UserAddPrompt bind={user_prompt} refresh={hide}/>
            <UserAddTip bind={user_tip} refresh={hide}/>
        </React.Fragment>
    )
};
