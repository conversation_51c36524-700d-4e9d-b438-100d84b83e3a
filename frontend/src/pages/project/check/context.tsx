import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const CheckContext = React.createContext<{
        timeZone: number ;
        setTimeZone: (data: number) => void;
    }
    |
    null>(null);

export const  CheckProvider = ({children}: { children: ReactNode }) => {

    const [timeZone,setTimeZone] = useSafeState<number>(8);



    return (
        < CheckContext.Provider
            value={
                {
                    timeZone,setTimeZone,


                }
            }
        >
            {children}
        </ CheckContext.Provider>
    )
};

export const useCheck = () => {
    const context = React.useContext( CheckContext);
    if (!context) {
        throw new Error("useNotice must be used in NoticeContextProvider");
    }
    return context;
};

