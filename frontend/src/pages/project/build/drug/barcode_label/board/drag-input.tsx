import React, {useEffect, useMemo, useRef, useState} from "react";
import {Input} from "antd";
import Barcode from "react-barcode";
import {defaultConfig} from "./template";
import {fontFamilyOptions, px2mm} from "./util";


const calcBarcodeSize = (size: number, defaultSize: number, base: number) => {
    if (!size || !defaultSize) return base
    return ((size / defaultSize) * base).toFixed(2)
}

const getFontFamily = (fontFamily: string) => {
    const font = fontFamilyOptions.find(it => it.value === fontFamily) || fontFamilyOptions[0]
    return `${font.font}`
}

export const DragBarcode = (props: any) => {

    const barcodeRef = useRef<HTMLDivElement>(null)
    const [defaultSize, setDefaultSize] = useState({width: 0, height: 0})
    const [config, setConfig] = useState<any>({
        ...defaultConfig,
        width: 1, height: 30,
    })

    useEffect(() => {
        const {clientWidth, clientHeight} = barcodeRef?.current || {}
        setDefaultSize({width: px2mm(clientWidth || 0), height: px2mm(clientHeight || 0)})
    }, [])

    const calcNewWidthMm = (newWeightMm: number) => {
        if (!props.defaultSize || !props.range) return newWeightMm
        // 手动输入宽度优先
        if (props.defaultSize.width > newWeightMm) return props.defaultSize.width
        // 超过最大宽度
        if (newWeightMm >= px2mm(props.range.x[1])) return px2mm(props.range.x[1])
        return newWeightMm
    }

    useEffect(() => {
        if (!props.field) return
        const field = props.field || {}
        const {width, height} = field
        setConfig({
            width: calcBarcodeSize(width, defaultSize.width, 1),
            height: calcBarcodeSize(height, defaultSize.height, 30),
            fontSize: field.fontSize,
            fontFamily: getFontFamily(field.fontFamily),
            fontWeight: field.fontWeight,
            fontColor: field.fontColor,
        })
    }, [props.field, defaultSize])

    useEffect(() => {
        if (!props.field) return
        if (props.field.key === props.activeKey) {
            // 延迟执行以确保DOM已更新
            const timer = setTimeout(() => {
                const newWidthMm = calcNewWidthMm(px2mm((barcodeRef?.current?.clientWidth || 0) + 2))
                // 如果宽度发生变化，通知父组件更新
                if (Math.abs(newWidthMm - props.field.width) > 0.5) { // 0.5mm的容差
                    props.onChange && props.onChange(props.field.key, 'width', calcNewWidthMm(newWidthMm))
                }
            }, 100)
            return () => clearTimeout(timer)
        }
    }, [props.field?.value, props.activeKey])

    return <div ref={barcodeRef} style={{display: 'flex'}}>
        <Barcode
            value={props.value}
            displayValue={true}
            height={config.height}
            width={config.width}
            fontSize={config.fontSize}
            font={config.fontFamily}
            fontOptions={config.fontWeight}
            lineColor={config.fontColor}
            margin={0}
            format="CODE128"
        />
    </div>
}

const justifyContentMap: {[key: string]: any} =  {
    left: 'start', center: 'center', right: 'end'
}

export const DragInput = (props: any) => {
    const textAreaRef = useRef<any>(null)
    const [textAreaHeight, setTextAreaHeight] = useState<number>(0)

    const calcNewHeightMm = (newHeightMm: number) => {
        // 手动输入高度优先
        if (props.defaultSize.height > newHeightMm) return props.defaultSize.height
        // 超过最大高度
        if (newHeightMm >= px2mm(props.range.y[1])) return px2mm(props.range.y[1])
        return newHeightMm
    }

    // 处理TextArea高度变化
    const handleTextAreaResize = () => {
        const textAreaElement = textAreaRef?.current?.resizableTextArea?.textArea
        if (!textAreaElement) return
        const newHeight = textAreaElement.scrollHeight
        const newHeightMm = calcNewHeightMm(px2mm(newHeight))
        // 如果高度发生变化，通知父组件更新
        if (Math.abs(newHeightMm - props.field.height) > 0) {
            setTextAreaHeight(newHeight)
            // 通知父组件更新高度
            props.onChange && props.onChange(props.field.key, 'height', newHeightMm)
        }
    }

    // 监听TextArea内容变化
    useEffect(() => {
        if (props.field.type === 'input' && props.field.key === props.activeKey) {
            // 延迟执行以确保DOM已更新
            const timer = setTimeout(() => {
                handleTextAreaResize()
            }, 100)
            return () => clearTimeout(timer)
        }
    }, [props.field.value, props.field.fontSize, props.activeKey])

    const getBackgroundColor = (field: any) => {
        const color = field.backgroundColor
        return !!color ? color : defaultConfig.backgroundColor
    }

    const inputNode = useMemo(() => {
        const field = props.field
        const fontStyle = {
            width: '100%',
            color: field.fontColor,
            fontFamily: getFontFamily(field.fontFamily),
            textAlign: field.textAlign,
            fontSize: `${field.fontSize}px`,
            fontWeight: field.fontWeight,
            backgroundColor: getBackgroundColor(field)
        }
        if (field.type === 'barcode') {
            return <div className={'drag-bar-code'} style={{
                justifyContent: justifyContentMap[field.textAlign],
                alignItems: 'center',
                height: '100%',
                backgroundColor: getBackgroundColor(field)
            }}>
                <DragBarcode
                    activeKey={props.activeKey}
                    value={field.value}
                    field={field}
                    defaultSize={props.defaultSize}
                    range={props.range}
                    onChange={props.onChange}
                />
            </div>
        }
        if (field.type === 'input') {
            return <div style={{
                position: 'relative', display: 'flex', alignItems: 'center',
                width: '100%', height: `calc(${field.height}mm - 2px)`, overflow: 'hidden',
                backgroundColor: getBackgroundColor(field)
            }}>
                <div style={{
                    position: 'fixed', top: 0, left: 0, width: '100%', height: '100%',
                    background: 'transparent', zIndex: 1
                }} />
                <Input.TextArea
                    ref={textAreaRef}
                    className={'drag-input'}
                    style={fontStyle}
                    autoFocus={false}
                    autoSize={{ minRows: 1 }}
                    value={field.value}
                    onKeyUp={e => e.stopPropagation()}
                    onChange={e => {
                        props.onChange(field.key, 'value', e.target.value)
                        // 延迟处理高度调整
                        setTimeout(() => {
                            handleTextAreaResize()
                        }, 50)
                    }}
                    onResize={handleTextAreaResize}
                />
            </div>
        }
        return <span style={fontStyle} className={'drag-input'}>
            {field.value}
        </span>
    }, [props.field, textAreaHeight, props.defaultSize])


    return <>
        {inputNode}
    </>
}
