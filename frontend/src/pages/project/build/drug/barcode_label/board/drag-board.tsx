import React, {useEffect, useImperative<PERSON><PERSON><PERSON>, useRef} from "react";
import Draggable from "react-draggable";
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>confirm, Space} from "antd";
import './index.less';
import {useDebounceFn, useSafeState} from "ahooks";
import {FieldAttribute} from "./drag-field-attribute";
import {BarcodeOutlined, CloseCircleTwoTone, InfoCircleFilled} from "@ant-design/icons";
import {
    calcDistance,
    checkBorderAlignment,
    componentPosition,
    getBindValue,
    innerArea,
    mm2px,
    position2number,
    px2mm
} from "./util";
import {createUUid} from "../../../../../../utils/uuid"
import {defaultConfig} from "./template";
import {useAtom} from "jotai";
import {barcodeLabelAtom} from "../context";
import _ from "lodash";
import {DragBarcode, DragInput} from "./drag-input";
import {FormattedMessage} from "../../../../../common/multilingual/component";
import {DragComponentDistance, DragRuler} from "./component";
import {useIntl} from "react-intl";
import {useGlobal} from "../../../../../../context/global";
import {TemplateSelect} from "./template-select";
import dayjs from "dayjs";

export const DragBoard = (props: any) => {
    const g = useGlobal()
    const {formatMessage} = useIntl()
    // 标签范围
    const [labelRange, setLabelRange] = useSafeState<any>({x: [0, 0], y: [0, 0]})
    const [labelZeroRange, setLabelZeroRange] = useSafeState<any>({x: [0, 0], y: [0, 0]})
    // 活动字段
    const [activeKey, setActiveKey] = useSafeState<string>('')
    const [isMove, setIsMove]  = useSafeState<boolean>(false)
    // 键盘移动状态 - 用于控制动画效果
    const [isKeyboardMoving, setIsKeyboardMoving] = useSafeState<boolean>(false)
    // 多选字段
    const [selectedKeys, setSelectedKeys] = useSafeState<string[]>([])
    // 水平尺状态
    const [showRuler, setShowRuler] = useSafeState<boolean>(false)
    const [rulerPosition, setRulerPosition] = useSafeState<{ x: number, y: number }>({x: 0, y: 0})
    // 距离显示状态
    const [componentDistances, setComponentDistances] = useSafeState<any[]>([])
    // 对齐高亮状态 - 存储对齐的边框信息
    const [alignedBorders, setAlignedBorders] = useSafeState<{[key: string]: string[]}>({})
    // 字段信息
    const [fieldsInfo, setFieldsInfo] = useSafeState<{[key: string]: any}>({})
    const [fields, setFields] = useSafeState<any[]>([])
    // 组件ref
    const dragBoardRef = useRef<HTMLDivElement>(null)
    const dragAreaRef = useRef<HTMLDivElement>(null)
    const dragBarcodeRef = useRef<HTMLDivElement>(null)
    const dragTextRef = useRef<HTMLDivElement>(null)
    const templateSelectRef = useRef<any>()
    // 标签ref
    const [, setLabelRef] = useAtom(barcodeLabelAtom)
    useImperativeHandle(props.bind, () => ({getFields, hide}))

    // 强制刷新
    const [forceRefresh, setForceRefresh] = useSafeState<number>(0)

    useEffect(() => {
        setLabelRef(dragAreaRef.current)
    }, [])

    const hide = () => {
        clearDragBoard()
        setForceRefresh(0)
        templateSelectRef?.current?.hide()
    }

    const getFields = () => {
        const keys = fields.map(it => it.key)
        return Object.keys(fieldsInfo).filter(it => keys.includes(it)).map(it => {
            const field = fieldsInfo[it]
            if (!field.backgroundColor) field.backgroundColor = defaultConfig.backgroundColor
            return field
        })
    }

    useEffect(() => {
        setTimeout(() => {
            const defaultOffset = {offsetTop: 0, offsetLeft: 0, clientWidth: 0, clientHeight: 0}
            const {offsetTop: boardTop, offsetLeft: boardLeft} = dragBoardRef?.current || defaultOffset
            const {
                offsetTop: dragTop, offsetLeft: dragLeft,
                clientWidth: width, clientHeight: height
            } = dragAreaRef?.current || defaultOffset
            const offsetTop = dragTop + boardTop
            const offsetLeft = dragLeft + boardLeft
            setLabelRange({
                x: [offsetLeft, offsetLeft + width] as [number, number],
                y: [offsetTop, offsetTop + height] as [number, number]
            })
            setLabelZeroRange({
                x: [0, width] as [number, number],
                y: [0, height] as [number, number]
            })
        }, 10)
    }, [props.labelSize])

    useEffect(() => {
        if (!props.bindData || props.bindData.length === 0 || fields.length === 0) return
        const newInfo = Object.keys(fieldsInfo).reduce((res: any, key) => {
            const item = fieldsInfo[key]
            item.value = getBindValue(props.bindData, item)
            res[key] = item
            return res
        }, {})
        setFieldsInfo(newInfo)
    }, [props.bindData])

    const defaultBarcodeNumber = () => {
        return props.bindData.find((it: any) => it.key === 'barcodeNumber')?.value || props.defaultBarcode
    }

    // 计算组件间距离
    const calculateDistances = (currentKey: string, currentPosition: any) => {
        const currentField = fieldsInfo[currentKey]
        if (!currentField) return []
        const distances = fields.filter(it => it.key !== currentKey).flatMap(it => {
            return calcDistance(currentPosition, currentField, fieldsInfo[it.key])
        })
        const directionMap: any = {horizontal: 'toY', vertical: 'fromX'}
        return _.uniqWith(_.sortBy(distances, 'distance'), (one: any, two: any) => {
            return one.direction === two.direction && one[directionMap[one.direction]] === two[directionMap[one.direction]]
        })

    }

    const onStart = (e: any, position: any, key: string) => {
        setActiveKey(key)
        // 拖拽开始时禁用键盘移动动画
        setIsKeyboardMoving(false)
        startAlignmentDetection(key, position)
        // 显示水平尺和垂直尺
        setShowRuler(true)
        setRulerPosition({x: position.x, y: position.y})
        // 计算并显示距离
        const distances = calculateDistances(key, position)
        setComponentDistances(distances)
    }

    const { run: onDrop } = useDebounceFn((e: any, position: any,  key: string) => {
        setIsMove(true)
        startAlignmentDetection(key, position)
        // 更新水平尺和垂直尺位置
        setRulerPosition({x: position.x, y: position.y})
        // 更新距离显示
        const distances = calculateDistances(key, position)
        setComponentDistances(distances)
    }, { wait: 25 })

    const onStop = (e: any, position: any, key: string) => {
        setIsMove(false)
        // 需要比onDrop中wait时间长
        setTimeout(hideAuxiliaryLine, 30)
        onForceChange(key, {
            marginLeft: px2mm(position2number(position.x)),
            marginTop: px2mm(position2number(position.y))
        })
    }

    // 隐藏辅助线
    const hideAuxiliaryLine = () => {
        setShowRuler(false)
        setAlignedBorders({})
        setComponentDistances([])
    }

    // 检测对齐
    const startAlignmentDetection = (key: string, _position: any) => {
        // 获取当前拖拽组件的实时位置
        const dragElement = document.querySelector(`[data-key="${key}"]`)
        if (!dragElement) return
        const rect = dragElement.getBoundingClientRect()
        const boardRect = dragAreaRef.current?.getBoundingClientRect()
        if (boardRect) {
            const currentPosition = {
                x: rect.left - boardRect.left,
                y: rect.top - boardRect.top
            }
            // 检测边框对齐
            const alignedBordersMap = checkBorderAlignment(key, currentPosition, fieldsInfo)
            setAlignedBorders(alignedBordersMap)
        }
    }

    const onClickComponent = (field: any, event?: React.MouseEvent) => {
        // 按住shift且当前已经有选中的字段
        if (event?.shiftKey && selectedKeys.length > 0) {
            setActiveKey('')
            const newSelectedKeys = selectedKeys.includes(field.key) ?
                selectedKeys.filter(key => key !== field.key) : [...selectedKeys, field.key]
            setSelectedKeys(newSelectedKeys)
        } else {
            // 普通点击，清除多选状态
            setSelectedKeys([field.key])
            setActiveKey(field.key)
        }
    }

    const onFieldChange = (key: string, label: string, value: any) => {
        onChange(key, {[label]: value})
    }

    const onChange = (key: string, values: any) => {
        // 宽高变化时需要判断边距是否超出标签
        const newInfo = {...fieldsInfo[key], ...values}
        const info = _.update(fieldsInfo, [key], () => ({
            ...newInfo,
            marginTop: addOffset(newInfo.marginTop, newInfo.height, 0, labelZeroRange.y),
            marginLeft: addOffset(newInfo.marginLeft, newInfo.width, 0, labelZeroRange.x),
        }))
        setFieldsInfo({...info})
    }

    const onForceChange = (key: string, values: any) => {
        onChange(key, values)
        setForceRefresh(forceRefresh + 1)
    }

    const addOffset = (positionMm: number, length: number, offset: number, range: [number, number]) => {
        const positionPx = mm2px(positionMm || 0) + offset
        const lengthPx = mm2px(length)
        if (positionPx <= range[0]) return 0
        if (positionPx + lengthPx >= range[1]) return px2mm(range[1] - lengthPx)
        return px2mm(positionPx)
    }

    // 实际允许的offset
    const allowOffset = (positionMm: number, length: number, offset: number, range: [number, number]) => {
        const positionPx = mm2px(positionMm || 0)
        const lengthPx = mm2px(length)
        if (positionPx + offset < range[0]) return range[0] - positionPx
        if (positionPx + lengthPx + offset > range[1]) return range[1] - positionPx - lengthPx
        return offset
    }

    const keyCodeMap: {[key: number]: any} = {
        37: {direction: 'left', offset: {x: -1, y: 0}},
        38: {direction: 'up', offset: {x: 0, y: -1}},
        39: {direction: 'right', offset: {x: 1, y: 0}},
        40: {direction: 'down', offset: {x: 0, y: 1}},
    }

    const calcOffset = (fields: any[], offset: any, step: number) => {
        const offsetX = fields
            .map(field => allowOffset(field.marginLeft, field.width, offset.x * step, labelZeroRange.x))
            .sort((a: number, b: number) => Math.abs(a) - Math.abs(b))
        const offsetY = fields
            .map(field => allowOffset(field.marginTop, field.height, offset.y * step, labelZeroRange.y))
            .sort((a: number, b: number) => Math.abs(a) - Math.abs(b))
        return {x: offsetX[0], y: offsetY[0]}
    }

    const { run: keyMove } = useDebounceFn((offset: any, targetKeys: any[], step: number = 1) => {
        if (!!offset && targetKeys.length > 0) {
            const actualOffset = calcOffset(targetKeys.map(it => fieldsInfo[it]), offset, step)
            // 如果有多选组件，同步微调所有选中的组件
            const updateInfo = targetKeys.reduce((res: any, key) => {
                const field = fieldsInfo[key]
                if (!field) return res
                const newMarginTop = addOffset(field.marginTop, field.height, actualOffset.y, labelZeroRange.y)
                const newMarginLeft = addOffset(field.marginLeft, field.width, actualOffset.x, labelZeroRange.x)
                if ((newMarginTop === field.marginTop) && (newMarginLeft === field.marginLeft)) return res
                res[key] = {...field, marginTop: newMarginTop, marginLeft: newMarginLeft}
                return res
            }, {})
            // 存在边界组件则全部不移动
            if (Object.keys(updateInfo).length !== targetKeys.length) return
            setFieldsInfo({...fieldsInfo, ...updateInfo})
        }
    }, { wait: 40 })

    const getMoveStep = (duration: number) => {
        if (duration <= 1) return 2
        if (duration <= 15) return duration * 4
        return 50
    }

    useEffect(() => {
        let start = 0
        const keyup = (e: any) => {
            const offset = keyCodeMap[e.keyCode]?.offset
            const targetKeys = selectedKeys.length > 1 ? selectedKeys : !!activeKey ? [activeKey] : []
            if (!!offset && targetKeys.length > 0) {
                setIsKeyboardMoving(false)
                setForceRefresh(forceRefresh + 1)
            }
        }
        const keydown = (e: any) => {
            if (start === 0) start = dayjs().unix()
            const duration = dayjs().unix() - start
            const step = getMoveStep(duration)
            const offset = keyCodeMap[e.keyCode]?.offset
            const targetKeys = selectedKeys.length > 1 ? selectedKeys : !!activeKey ? [activeKey] : []
            if (!!offset && targetKeys.length > 0) {
                setIsKeyboardMoving(true)
                keyMove(offset, targetKeys, step)
            }
        }

        window.addEventListener("keyup", keyup)
        window.addEventListener("keydown", keydown)
        return () => {
            window.removeEventListener("keyup", keyup)
            window.removeEventListener("keydown", keydown)
        }
    },[activeKey, selectedKeys, forceRefresh])


    const getDragStyle = (field: any): any => {
        const info = fieldsInfo[field.key] || field
        const baseStyle = {
            position: 'absolute',
            width: `calc(${info.width}mm)`,
        }
        // 如果是输入框且处于编辑状态，并且高度未超过标签，使用自动高度
        const overflowY = info.height > px2mm(labelZeroRange.y[1])
        if (info.type === 'input' && !overflowY) {
            return {...baseStyle, minHeight: `calc(${info.height}mm)`, height: 'auto'}
        }
        // 其他情况使用固定高度
        return {...baseStyle, height: `calc(${info.height}mm)`}
    }

    const getBorderStyle = (field: any): any => {
        const borders = alignedBorders[field.key]
        if (!borders || borders.length === 0) return {}
        return borders.reduce((res: any, item) => {
            res[`border${item}`] = '1px solid #52c41a'
            return res
        }, {})
    }

    const addComponent = (e: any, position: any, ref: any, type: string) => {
        const borderSize = type === 'barcode' ? {x: 3, y: 6} : {x: 3, y: 2}
        const value = type === 'barcode' ? defaultBarcodeNumber() : formatMessage({id: 'barcode.label.component.textBox'})
        const bind = type === 'barcode' ? 'barcodeNumber' : ''
        const width = ref?.current?.clientWidth + borderSize.x
        const height = ref?.current?.clientHeight + borderSize.y
        const {scrollLeft, scrollTop} = dragBoardRef?.current || {scrollLeft: 0, scrollTop: 0}
        const x = {
            allowRange: labelRange.x,
            positionStart: position2number(position.x) + scrollLeft,
            length: width + 2
        }
        const y = {
            allowRange: labelRange.y,
            positionStart: position2number(position.y) + scrollTop,
            length: height + 2
        }

        if (innerArea(x, y, 0.3)) {
            const point = componentPosition(x, y)
            const newField =  {
                ...defaultConfig, width: px2mm(width), height: px2mm(height),
                key: createUUid(), value: value, marginLeft: px2mm(point[0]), marginTop: px2mm(point[1]),
                type: type, bind: bind
            }
            setFields([...fields, newField])
            setFieldsInfo({...fieldsInfo, [newField.key]: newField})
            setActiveKey(newField.key)
            setSelectedKeys([newField.key])
        }
    }

    const removeComponent = (field: any) => {
        setFields(fields.filter(it => it.key !== field.key))
        setSelectedKeys([])
        setTimeout(() => {
            setActiveKey('')
            hideAuxiliaryLine()
        }, 10)
    }

    const clearDragBoard = () => {
        setActiveKey('')
        setSelectedKeys([])
        setFields([])
        setFieldsInfo({})
        setForceRefresh(forceRefresh + 1)
    }


    // 一键智能对齐功能 - 同时处理水平和垂直对齐
    const smartAlign = () => {
        // 如果有多选组件，只对选中的组件进行对齐
        const targetKeys = selectedKeys.length > 1 ? selectedKeys : fields.map(it => it.key)
        if (targetKeys.length < 2) return

        const tolerance = 2 // 容差范围，单位：mm
        const allFields = fields.filter(it => targetKeys.includes(it.key)).map(field => fieldsInfo[field.key] || field)
        const updatedFieldsInfo = {...fieldsInfo}
        // 第一步：水平对齐（按 marginTop 分组）
        const horizontalGroups: any[][] = []
        allFields.forEach(field => {
            let addedToGroup = false
            for (let group of horizontalGroups) {
                // 检查是否在容差范围内
                const avgTop = group.reduce((sum, f) => sum + f.marginTop, 0) / group.length
                if (Math.abs(field.marginTop - avgTop) <= tolerance) {
                    group.push(field)
                    addedToGroup = true
                    break
                }
            }
            if (!addedToGroup) horizontalGroups.push([field])
        })
        // 对每个水平组内的组件进行对齐
        horizontalGroups.forEach((group) => {
            if (group.length > 1) {
                const avgTop = group.reduce((sum, f) => sum + f.marginTop, 0) / group.length
                group.forEach(field => {
                    const newMarginTop = Math.max(0, Math.min(avgTop, px2mm(labelZeroRange.y[1]) - field.height))
                    updatedFieldsInfo[field.key] = {
                        ...field,
                        marginTop: Math.round(newMarginTop * 100) / 100
                    }
                })
            }
        })
        // 第二步：垂直对齐（按 marginLeft 分组，使用更新后的字段信息）
        const verticalGroups: any[][] = []
        const fieldsForVertical = allFields.map(field => updatedFieldsInfo[field.key] || field)
        fieldsForVertical.forEach(field => {
            let addedToGroup = false
            for (let group of verticalGroups) {
                // 检查是否在容差范围内
                const avgLeft = group.reduce((sum, f) => sum + f.marginLeft, 0) / group.length
                if (Math.abs(field.marginLeft - avgLeft) <= tolerance) {
                    group.push(field)
                    addedToGroup = true
                    break
                }
            }
            if (!addedToGroup) verticalGroups.push([field])
        })
        // 对每个垂直组内的组件进行对齐
        verticalGroups.forEach((group) => {
            if (group.length > 1) {
                const avgLeft = group.reduce((sum, f) => sum + f.marginLeft, 0) / group.length
                group.forEach(field => {
                    const newMarginLeft = Math.max(0, Math.min(avgLeft, px2mm(labelZeroRange.x[1]) - field.width))
                    updatedFieldsInfo[field.key] = {
                        ...updatedFieldsInfo[field.key],
                        marginLeft: Math.round(newMarginLeft * 100) / 100
                    }
                })
            }
        })
        setFieldsInfo(updatedFieldsInfo)
        setForceRefresh(forceRefresh + 1)
    }

    const linkTemplate = (template: any) => {
        updateTemplate(template)
    }

    const updateTemplate = (template: any) => {
        setActiveKey('')
        setSelectedKeys([])
        hideAuxiliaryLine()
        const {fields, labelSize} = template
        setFieldsInfo(fields.reduce((res: { [key: string]: any; }, item: any) => {
            item.value = getBindValue(props.bindData, item)
            res[item.key] = item
            return res
        }, {}))
        props.setLabelSize && props.setLabelSize(labelSize)
        setTimeout(() => setFields(fields), 20)
    }

    const BarcodeDrag = <><BarcodeOutlined />  <FormattedMessage id={'barcode'} /></>
    const TextBoxDrag = <>Aa <FormattedMessage id={'barcode.label.component.textBox'} /></>

    return  <div className={'board-container'}>
        <div style={{width: '100%', display: 'flex', marginBottom: '16px', justifyContent: 'space-between'}}>
            <div style={{position: 'relative', display: 'flex'}}>
                <div>
                    <div className="drag-component">{BarcodeDrag}</div>
                    <div style={{position: 'absolute', top: 0, left: 0, zIndex: 3}}>
                        <Draggable
                            handle=".drag-handle"
                            position={{x: 0, y: 0}}
                            scale={1}
                            onStop={(e, position) => addComponent(e, position,  dragBarcodeRef, 'barcode')}
                        >
                            <div className={'drag-component drag-handle'}>
                                {BarcodeDrag}
                            </div>
                        </Draggable>
                    </div>
                    <div ref={dragBarcodeRef} style={{position: 'absolute', top: 0, left: 0, zIndex: -2}}>
                        <DragBarcode value={defaultBarcodeNumber()} />
                    </div>
                </div>
                <div style={{marginLeft: '12px'}}>
                    <div className="drag-component" ref={dragTextRef}>{TextBoxDrag}</div>
                    <div style={{position: 'absolute', top: 0, left: 0, zIndex: 2}}>
                        <Draggable
                            handle=".drag-handle"
                            position={{x: g.lang === 'zh' ? 103.8 : 115.8, y: 0}}
                            scale={1}
                            onStop={(e, position) => addComponent(e, position, dragTextRef, 'input')}
                        >
                            <div className={'drag-component drag-handle'}>
                                {TextBoxDrag}
                            </div>
                        </Draggable>
                    </div>
                </div>
                <div style={{marginLeft: '16px'}}>
                    <Divider type={'vertical'} style={{borderLeft: '1px solid rgba(227, 228, 230, 1)', height: '20px', margin: 0}} />
                    <TemplateSelect bind={templateSelectRef} allLabel={props.allLabel} callback={linkTemplate}/>
                </div>
            </div>
            <Space>
                {fields.length >= 2 && <Button onClick={smartAlign} type={'link'} style={{padding: 0}}>
                    <FormattedMessage id={'barcode.label.component.smartAlign'} />
                    {selectedKeys.length > 1 && <span style={{marginLeft: 4, fontSize: '12px', color: '#666'}}>
                        ({selectedKeys.length})
                    </span>}
                </Button>}
                {fields.length > 0 && <Button onClick={clearDragBoard} type={'link'} style={{padding: 0}}>
                    <FormattedMessage id={'common.pagination.empty'} />
                </Button>}
            </Space>

        </div>
        <div className={'board-drag'} ref={dragBoardRef}>
            {/* 顶部水平尺 */}
            <div className="ruler-container ruler-horizontal-top">
                <div className="ruler-scale">
                    {Array.from({length: Math.ceil(props.labelSize.width / 5) + 1}, (_, i) => (
                        <div
                            key={i}
                            className="ruler-mark"
                            style={{left: `${mm2px(i * 5)}px`, height: i % 2 === 0 ? '6px' : '3px'}}
                        >
                            {i % 2 === 0 && i > 0 && (<span className="ruler-number">{i * 5}</span>)}
                        </div>
                    ))}
                </div>
            </div>
            {/* 左侧垂直尺 */}
            <div className="ruler-container ruler-vertical-left">
                <div className="ruler-scale">
                    {Array.from({length: Math.ceil(props.labelSize.height / 5) + 1}, (_, i) => (
                        <div
                            key={i}
                            className="ruler-mark"
                            style={{top: `${mm2px(i * 5)}px`, width: i % 2 === 0 ? '6px' : '3px'}}
                        >
                            {i % 2 === 0 && i > 0 && (<span className="ruler-number">{i * 5}</span>)}
                        </div>
                    ))}
                </div>
            </div>
            {/* 拖拽框 */}
            <div
                ref={dragAreaRef}
                className={'board-label'}
                style={{
                    position: 'relative',
                    width: `calc(${props.labelSize.width}mm)`,
                    height: `calc(${props.labelSize.height}mm)`,
                    border: '1px solid rgba(235, 237, 240, 1)',
                }}
                onClick={(e) => {
                    // 点击空白区域取消多选
                    if (e.target === e.currentTarget) {
                        setSelectedKeys([])
                        setActiveKey('')
                    }
                }}
            >
                {/* 水平尺 */}
                {showRuler && <DragRuler
                    rulerPosition={rulerPosition}
                    labelSize={props.labelSize}
                    range={labelZeroRange}
                    field={fieldsInfo[activeKey]}
                />}

                {/* 组件间距离显示 */}
                {componentDistances.map((distance, index) =>
                    <DragComponentDistance index={index} distance={distance} />
                )}

                {/* 空提示 */}
                {fields.length === 0 && <div style={{
                    fontWeight: "normal", fontSize: '15px', color: 'rgba(200, 201, 204, 1)', width: '100%', height: '100%',
                    display: 'flex', alignItems: 'center', justifyContent: 'center', overflow: 'hidden'
                }}>
                    <FormattedMessage id={'barcode.label.template.emptyTips'} />
                </div>}

                {fields.map(it =>
                    <Draggable
                        key={it.key}
                        bounds="parent"
                        handle=".handle"
                        position={{x: mm2px(fieldsInfo[it.key]?.marginLeft), y: mm2px(fieldsInfo[it.key]?.marginTop)}}
                        // defaultPosition={{x: mm2px(it.marginLeft), y: mm2px(it.marginTop)}}
                        // grid={[25, 25]}
                        // scale={1}
                        onStart={(e, position,) => onStart(e, position, it.key)}
                        onDrag={(e,  position) => onDrop(e, position, it.key)}
                        onStop={(e,  position) => onStop(e, position, it.key)}
                    >
                        <div
                            key={it.key}
                            data-key={it.key}
                            style={{
                                ...getDragStyle(it),
                                // 添加对齐边框高亮样式
                                ...getBorderStyle(it)
                            }}
                            onClick={(e) => onClickComponent(it, e)}
                            className={`drag-block ${it.key === activeKey ? 'handle drag-block-active' : ''} ${selectedKeys.includes(it.key) ? 'drag-block-active' : ''} ${isKeyboardMoving ? 'drag-block-keyboard-moving' : ''}`}
                        >
                            <DragInput
                                field={fieldsInfo[it.key] || it}
                                defaultSize={{
                                    width: fieldsInfo[it.key]?.manualWidth || it.width,
                                    height: fieldsInfo[it.key]?.manualHeight || it.height
                                }}
                                isMove={isMove}
                                activeKey={activeKey}
                                onChange={onFieldChange}
                                range={labelZeroRange}
                            />
                            {/* {it.key === activeKey && showRuler && <DragRuleStatic labelSize={props.labelSize} />}*/}
                            {it.key === activeKey &&  <Popconfirm
                                title={<FormattedMessage id={'barcode.label.component.delete'} />}
                                okText={<FormattedMessage id={'common.yes'} />}
                                cancelText={<FormattedMessage id={'common.no'} />}
                                onConfirm={() => removeComponent(it)}
                            >
                                 <span
                                     className={'drag-remove-icon'}
                                     style={{display: 'flex', position: 'absolute', top: -7, right: -7, cursor: 'pointer'}}
                                 >
                                    <CloseCircleTwoTone style={{fontSize: '14px', color: 'rgba(187, 187, 187, 1)'}} />
                                </span>
                            </Popconfirm>}
                        </div>
                    </Draggable>
                )}
            </div>
        </div>
        <div className={'board-operation'}>
            {/*triangle*/}
            <div className="triangle-border"><div className="triangle-inner"></div></div>
            {/*字段属性*/}
            <div className={g.lang === 'zh' ? 'field-attribute field-attribute-zh': 'field-attribute'}>
                <FieldAttribute
                    disabled={!activeKey}
                    fieldKey={activeKey}
                    fieldsInfo={fieldsInfo}
                    range={labelZeroRange}
                    onChange={onForceChange}
                    bindData={props.bindData || []}
                />
            </div>
        </div>
    </div>
}