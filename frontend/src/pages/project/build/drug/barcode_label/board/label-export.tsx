import React, { useRef, useImperativeHandle } from 'react';
import {Button, message, Modal, Space} from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import html2canvas from 'html2canvas';
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { FormattedMessage } from '../../../../../common/multilingual/component'
import { useSafeState } from 'ahooks'
import {LabelPreview, PreviewPrintLabel} from "./preview"
import {mm2px} from "./util";
import {useIntl} from "react-intl";

export const LabelExport: React.FC<{ bind: any; }> = (props) => {
    const [visible, setVisible] = useSafeState<boolean>(false)
    const [loading, setLoading] = useSafeState<boolean>(false)
    const [printData, setPrintData] = useSafeState<any[]>([])
    const [printInfo, setPrintInfo] = useSafeState<any>({printSize: {}, labelSize: {}})
    const [scale, setScale] = useSafeState<number>(1)
    const {formatMessage} = useIntl()
    
    const containerRef = useRef<HTMLDivElement>(null)
    const printRef = useRef<any>()
    useImperativeHandle(props.bind, () => ({show}))

    const show = (printData: any[], printInfo: any) => {
        setScale(calcScale(printInfo.labelSize.width))
        setPrintData(printData)
        setPrintInfo(printInfo)
        setVisible(true)
    };

    const hide = () => {
        setVisible(false)
        setScale(1)
        setPrintInfo({printSize: {}, labelSize: {}})
        setPrintData([])
    }

    const calcScale = (labelWidth: number) => {
        const containerWidth = 804
        if (labelWidth <= containerWidth) return 1
        return Math.round((containerWidth / labelWidth) * 1000) / 1000
    }

    // 将标签转换为图片
    const convertLabelToImage = async (labelElement: HTMLElement): Promise<string> => {
        try {
            const canvas = await html2canvas(labelElement, {
                backgroundColor: 'white',
                scale: 2, // 提高图片质量
                useCORS: true,
                allowTaint: true,
                width: labelElement.offsetWidth,
                height: labelElement.offsetHeight
            });
            return canvas.toDataURL('image/png')
        } catch (error) {
            console.error('转换图片失败:', error)
            throw error;
        }
    };

    const printPreview = () => {
        printRef.current?.preview(printData, printInfo)
    }

    // 导出为Excel
    const exportToExcel = async () => {
        if (!containerRef.current) return
        setLoading(true)
        try {
            const labelElements = containerRef.current.querySelectorAll('.board-label')
            if (labelElements.length === 0) return
            const labelWidth = mm2px(printInfo.labelSize.width)
            const labelHeight = mm2px(printInfo.labelSize.height)
            // 创建Excel工作簿
            const workbook = new ExcelJS.Workbook()
            const worksheet = workbook.addWorksheet('Sheet1')
            // 设置列宽
            worksheet.columns = [
                { header: formatMessage({id: 'common.serial'}), key: 'index', width: 10 },
                { header: formatMessage({id: 'barcode.label'}), key: 'image', width: labelWidth}
            ]
            // 转换每个标签为图片并添加到Excel
            for (let i = 0; i < labelElements.length; i++) {
                const labelElement = labelElements[i] as HTMLElement
                const imageDataUrl = await convertLabelToImage(labelElement)
                // 将base64图片添加到Excel
                const imageId = workbook.addImage({base64: imageDataUrl, extension: 'png'})
                const row = worksheet.addRow({
                    index: i + 1,
                    image: '' // 图片将通过addImage方法添加
                });
                // 设置行高
                row.height = labelHeight * 1.8
                // 添加图片到单元格
                worksheet.addImage(imageId, {
                    tl: { col: 1, row: i + 1 }, // 从第二列开始
                    ext: { width: labelWidth, height: labelHeight } // 图片尺寸
                })
            }
            // 生成文件名
            const now = new Date();
            const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const fileName = `${formatMessage({id: 'barcode.label'})}_${timestamp}.xlsx`
            // 导出文件
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], { 
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
            })
            saveAs(blob, fileName);
            message.success(formatMessage({id: 'common.export.success'}))
            hide();
        } catch (error) {
            message.error(formatMessage({id: 'common.download.fail'}))
        } finally {
            setLoading(false)
        }
    }

    return (
        <Modal
            title={<FormattedMessage id="form.preview" />}
            open={visible}
            onCancel={hide}
            centered
            className="custom-medium-modal"
            footer={<Space>
                <Button key="cancel" onClick={hide}>
                    <FormattedMessage id={'common.cancel'} />
                </Button>
                <Button key="print" onClick={printPreview}>
                    <FormattedMessage id={'barcode.label.printPreview'} />
                </Button>
                <Button
                    key="export"
                    type="primary"
                    icon={<DownloadOutlined />}
                    loading={loading}
                    onClick={exportToExcel}
                >
                    <FormattedMessage id="common.export" />
                </Button>
            </Space>}
        >
            <div 
                ref={containerRef}
                style={{padding: '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>
                {printData.map((fields, index) => (
                    <div key={index} style={{marginBottom: '10px', scale: `${scale}`}}>
                        <LabelPreview
                            fields={fields}
                            labelSize={printInfo.labelSize}
                        />
                    </div>
                ))}
            </div>
            <PreviewPrintLabel bind={printRef} />
        </Modal>
    )
}
