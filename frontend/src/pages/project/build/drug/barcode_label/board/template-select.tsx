import {Button, Form, Popover, Select, Space} from "antd";
import {FormattedMessage} from "../../../../../common/multilingual/component";
import React, {useImperativeHandle} from "react";
import {useSafeState} from "ahooks";
import {defaultTemplate} from "./template";
import {useIntl} from "react-intl";
import {useFetch} from "../../../../../../hooks/request";
import {getPreviewBarcodeLabel} from "../../../../../../api/barcode_label";
import {useAuth} from "../../../../../../context/auth";
import {useGlobal} from "../../../../../../context/global";

export const TemplateSelect = (props: any) => {

    const {formatMessage} = useIntl()
    const [open, setOpen] = useSafeState<boolean>(false)
    const [selectTemplate, setSelectTemplate] = useSafeState<any>({})
    const g = useGlobal()

    useImperativeHandle(props.bind, () => ({hide}))

    const auth = useAuth()
    const envId = auth.env ? auth.env.id : null
    const customerId = auth.customerId
    const projectId = auth.project.id

    const {runAsync: getPreviewData, loading: previewLoading} = useFetch((id) => getPreviewBarcodeLabel(id, {
        envId, projectId, customerId, cohortId: ""
    }), {manual: true})

    const hide = () => {
        setSelectTemplate({})
    }

    const getOptions = () => {
        const userOptions = props.allLabel.map((it: any) => ({
            value: it['_id'], label: it['label_number']
        }))
        const defaultOptions = [
            {value: 'system001', label: formatMessage({id: 'barcode.label.template.name.default'}), template: defaultTemplate}
        ]
        return [...defaultOptions, ...userOptions]
    }

    const confirm = () => {
        if (!selectTemplate.value) {
            return
        }
        if (!!selectTemplate.template) {
            props.callback && props.callback(selectTemplate.template)
            setOpen(false)
            return
        }
        getPreviewData(selectTemplate.value).then((resp: any) => {
            if (resp && resp.code === 0) {
                props.callback && props.callback(resp.data.template)
                setOpen(false)
            }
        })
    }


    const templateContent = () => {
        return <div style={{width: g.lang === 'zh' ? '280px' : '320px'}}>
            <div style={{fontWeight: 'bold', fontSize: '14px', color: '#1D2129'}}>
                <FormattedMessage id={'barcode.label.template.select'} />
            </div>
            <div style={{fontWeight: 'normal', fontSize: '13px', color: 'rgba(173, 178, 186, 1)', marginTop: '4px'}}>
                <FormattedMessage id={'barcode.label.template.linkTips'} />
            </div>
            <div style={{marginTop: '12px'}}>
                <Form.Item label={<FormattedMessage id={'common.name'} />} required>
                    <Select
                        style={{flex: 1}}
                        options={getOptions()}
                        value={selectTemplate.value}
                        onChange={(value, option) => setSelectTemplate(option || {})}
                        placeholder={formatMessage({id: 'placeholder.select.common'})}
                        showSearch
                        optionFilterProp={'label'}
                        allowClear
                    />
                </Form.Item>
            </div>
            <div style={{display: 'flex', justifyContent: 'end'}}>
                <Space>
                    <Button onClick={() => setOpen(false)}><FormattedMessage id={'common.cancel'} /></Button>
                    <Button disabled={!selectTemplate.value} onClick={confirm} type={'primary'}><FormattedMessage id={'common.ok'} /></Button>
                </Space>
            </div>
        </div>
    }

    return  <Popover
        content={templateContent()}
        trigger="click"
        placement="bottomLeft"
        open={open}
        onOpenChange={setOpen}
    >
        <Button type={'link'} onClick={() => setOpen(true)}>
            <FormattedMessage id={'common.template'} />
        </Button>
    </Popover>
}