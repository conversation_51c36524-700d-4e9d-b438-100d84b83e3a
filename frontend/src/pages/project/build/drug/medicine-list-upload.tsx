import React, { cloneElement, useState } from 'react';
import { useIntl } from "react-intl";
import { DatePicker, Form, Input, message, Modal, Select, Tabs, Space, Row, Col, Popover, Button } from "antd";
import moment from "moment";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { useSafeState } from "ahooks";
import { getDrugNamesByType, getPackageConfigure } from "../../../../api/drug";
import { storehouses as getStorehouses } from "../../../../api/project_storehouse";
import { downloadTemplate, uploadMedicines } from "../../../../api/medicine";
import { SingleUpload } from "../../../../components/SingleUpload";
import { useGlobal } from "../../../../context/global";
import styled from "@emotion/styled";
import { CustomDateTimePicker } from "../../../../components/CustomDateTimePicker";


export const MedicineUpload = (props: any) => {
    const singleUpload = React.useRef<any>(null)

    const g = useGlobal()
    const auth = useAuth()
    const intl = useIntl();
    const { formatMessage } = intl;
    const DatePickers: any = DatePicker

    const [visible, setVisible] = useSafeState<any>(false);
    const [submitting, setSubmitting] = useSafeState<any>(false);

    const [form] = Form.useForm();
    const [singleDrugNames, setSingleDrugNames] = useSafeState<any>(null);
    const [packageDrugNames, setPakcageDrugNames] = useSafeState<any>(null);
    const [drugNameOptions, setDrugNameOptions] = useSafeState<any>([]);
    const [storehouses, setStorehouses] = useSafeState<any>([]);
    const [drugSpecs, setDrugSpecs] = useSafeState<any>([]);
    const [showDrugSpecs, setShowDrugSpecs] = useSafeState<any>([]);
    const [packageIsOpen, setPackageIsOpen] = useSafeState<any>(false);
    const [packageConfig, setPackageConfig] = useSafeState<any>([]);
    const [activeKey, setActiveKey] = useSafeState<string>("single");
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const customerId: (any) = auth.customerId;

    const [popoverOpen, setPopoverOpen] = useState(false);
    const [expirationDateValue, setExpirationDateValue] = useState(null);


    const { runAsync: getDrugNamesRun } = useFetch(getDrugNamesByType, { manual: true })
    const { runAsync: getStorehousesRun } = useFetch(getStorehouses, { manual: true })
    const { runAsync: uploadMedicinesRun } = useFetch(uploadMedicines, { manual: true })
    const { runAsync: downloadTemplateRun } = useFetch(downloadTemplate, { manual: true })
    const { runAsync: getPackageConfigureRun } = useFetch(getPackageConfigure, { manual: true });



    const popoverOpenChange = (newOpen: boolean) => {
        if (singleUpload.current.fileList.length < 1) {
            message.error(formatMessage({ id: 'projects.randomization.confirmUpload' }));
            return false;
        }
        if (form.getFieldValue("expirationDate") === null || form.getFieldValue("expirationDate") === undefined ||
            form.getFieldValue("batch") === null || form.getFieldValue("batch") === undefined || form.getFieldValue("batch") === "") {
            form.validateFields().then((value: any) => {
                setPopoverOpen(newOpen);
            }).catch(() => {
                setSubmitting(false)
            })
        } else {
            form.validateFields().then((value: any) => {
                setSubmitting(true);
                uploadMedicinesRun(formData()).then(
                    (resp: any) => {
                        message.success(resp.msg);
                        props.refresh();
                        hide();
                    }
                ).catch(() => {
                    setSubmitting(false)
                })
            }).catch(() => {
                setSubmitting(false)
            })
        }
    };

    const handleTabChange = (tabName: string) => {
        setActiveKey(tabName);
        if (tabName === "single") {
            setDrugNameOptions(singleDrugNames)
        } else if (tabName === "package") {
            setDrugNameOptions(packageDrugNames)
        }
    };

    const handleSelectChange = (event: any) => {
        if (event != null) {
            setShowDrugSpecs([])
            var splitDrugNames = event.split("、")
            let showDatas: any = []
            let selectDrugSpecs: any = []
            for (let index = 0; index < splitDrugNames.length; index++) {
                var data: any = {}
                const drugName = splitDrugNames[index];
                data = { drugName: drugName }
                // 处理选择事件，可以根据选择的值执行相应操作
                if (drugSpecs !== undefined && drugSpecs !== null) {
                    var drugSpec = drugSpecs[drugName]
                    const options = drugSpec.filter((item: any) => item !== "");
                    selectDrugSpecs.push(options)
                } else {
                    selectDrugSpecs.push([])
                }
                showDatas.push(data)
            }
            form.setFieldValue("drugNames", showDatas);
            setShowDrugSpecs(selectDrugSpecs)
        } else {
            form.setFieldValue("drugNames", [{ "drugName": [], "spec": null }]);
        }

    };


    const show = () => {
        setVisible(true);
        form.setFieldValue("drugNames", [{ "drugName": [], "spec": null }]);
        //获取研究产品名称
        getDrugNamesRun({ customerId, envId, type: 2 }).then(
            (result: any) => {
                let data = result.data
                if (data.singleDrugNames != null) {
                    const options = data.singleDrugNames.map((it: any) => ({
                        label: it,
                        value: it
                    }));
                    setSingleDrugNames(options)
                    setDrugNameOptions(options);
                }
                if (data.packageDrugNames != null) {
                    const options = data.packageDrugNames.map((it: any) => ({
                        label: it,
                        value: it
                    }));
                    setPakcageDrugNames(options)
                    if (data.singleDrugNames === null) {
                        setDrugNameOptions(options);
                    }
                }
                if (data.drugSpecs != null) {
                    setDrugSpecs(data.drugSpecs);
                }
            }
        )
        //获取仓库
        getStorehousesRun({ customerId: customerId, projectId: projectId, envId: envId, roleId: auth.project.permissions.role_id }).then(
            (result: any) => {
                let data = result.data
                if (data != null) {
                    const options = data.map((it: any) => ({
                        label: it.name,
                        value: it.id
                    }));
                    setStorehouses(options);
                }
            }
        )
        //获取配置信息
        getPackageConfigureRun({ customerId, projectId, envId }).then(
            (result: any) => {
                if (result.data != null) {
                    form.setFieldsValue(result.data);
                    if (result.data.isOpen) {
                        setPackageIsOpen(true);
                        let mixedPackage: any = [];
                        result.data?.mixedPackage.forEach((it: any) => {
                            if (it.isMixed && it.packageConfig.length > 0) {
                                let drugNames: any = [];
                                it.packageConfig.forEach((packageConfig: any) => {
                                    drugNames.push(packageConfig.name)
                                })
                                mixedPackage.push(drugNames)
                            }
                        });
                        setPackageConfig(mixedPackage)
                    }
                }
            }
        );

    };

    const hide = () => {
        form.setFieldValue("drugNames", [{ "drugName": "", "spec": "" }]);
        setVisible(false);
        setPopoverOpen(false);
        setPackageIsOpen(false);
        form.resetFields();
        singleUpload.current.reset()
        setSubmitting(false);
    };

    const formData = () => {
        const form_data = new FormData();
        form_data.append('envId', envId);
        form_data.append('projectId', projectId);
        form_data.append('customerId', customerId);
        const drugNames = form.getFieldsValue().drugNames;
        var length = drugNames.length.toString();
        for (let index = 0; index < length; index++) {
            // 数组
            form_data.append(`drugNames[${index}].drugName`, drugNames[index].drugName);
            form_data.append(`drugNames[${index}].spec`, drugNames[index].spec ? drugNames[index].spec : "");
        }
        form_data.append('drugNameLength', length);
        form_data.append('storehouseId', form.getFieldsValue().storehouseId ? form.getFieldsValue().storehouseId : "");
        // form_data.append('expirationDate', form.getFieldsValue().expirationDate ? moment(form.getFieldsValue().expirationDate).format('YYYY-MM-DD') : "");
        form_data.append('expirationDate', (form.getFieldsValue().expirationDate && form.getFieldsValue().expirationDate !== undefined && form.getFieldsValue().expirationDate !== "-") ? form.getFieldsValue().expirationDate : "")
        form_data.append('batch', form.getFieldsValue().batch ? form.getFieldsValue().batch : "");
        form_data.append('file', singleUpload.current.originFile);
        return form_data;
    }

    const save = () => {
        if (singleUpload.current.fileList.length < 1) {
            message.error(formatMessage({ id: 'projects.randomization.confirmUpload' }));
            return false;
        }
        form.validateFields().then((value: any) => {
            setSubmitting(true);
            uploadMedicinesRun(formData()).then(
                (resp: any) => {
                    message.success(resp.msg);
                    setPopoverOpen(false);
                    props.refresh();
                    hide();
                }
            ).catch(() => {
                setPopoverOpen(false);
                setSubmitting(false)
            })
        }).catch(() => {
            setPopoverOpen(false);
            setSubmitting(false)
        })
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang === "zh" ? 6 : 6 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang === "zh" ? 18 : 18 },
        },
    }

    return (
        <React.Fragment>
            <Modal
                className='custom-small-modal'
                title={formatMessage({ id: "drug.medicine.upload" })}
                visible={visible}
                onCancel={hide}
                maskClosable={false}
                // onOk={save}
                confirmLoading={submitting}
                okText={formatMessage({ id: 'common.ok' })}
                centered
                footer={
                    <Row justify="end">
                        <Col style={{ marginRight: "16px" }}>
                            <Button onClick={hide}>
                                {formatMessage({ id: "common.cancel" })}
                            </Button>
                        </Col>
                        <Col>
                            <Popover
                                // content={<a onClick={popoverHide}>Close</a>}
                                title={formatMessage({ id: 'common.tips' })}
                                trigger="click"
                                open={popoverOpen}
                                content={
                                    <div style={{ width: "230px" }}>
                                        <Row>
                                            <span>{formatMessage({ id: 'common.tips.expireDate_batch' })}</span>
                                        </Row>
                                        <Row justify="end">
                                            <span style={{ marginRight: "16px", marginTop: "8px" }}>
                                                <Button loading={submitting} onClick={save} type="primary"
                                                    style={{
                                                        width: 64,
                                                        height: 32,
                                                        display: 'flex',
                                                        justifyContent: 'center',
                                                        alignItems: 'center'
                                                    }}
                                                >
                                                    <span
                                                        style={{
                                                            fontSize: 12,
                                                            fontWeight: 400,
                                                            textAlign: 'center'
                                                        }}
                                                    >
                                                        {formatMessage({ id: "common.tips.expireDate_batch.know" })}
                                                    </span>
                                                </Button>
                                            </span>
                                        </Row>
                                    </div>
                                }
                                onOpenChange={popoverOpenChange}
                            >
                                <Button loading={submitting} type="primary">
                                    {formatMessage({ id: "common.ok" })}
                                </Button>
                            </Popover>
                        </Col>
                    </Row>
                }
            >
                <Form form={form} layout="horizontal" {...formItemLayout}>
                    <Form.Item label={formatMessage({ id: 'drug.configure.drugName' })} name="drugNames" rules={[{ required: true }]} style={{ marginBottom: 0 }}>
                        <Form.List name="drugNames">
                            {(fields) => (
                                <div
                                    style={{
                                        display: "flex",
                                        flexDirection: "column",
                                    }}
                                >
                                    {fields.map((field: any, index: any) => (
                                        <Col key={field.drugName}>
                                            <Row>
                                                <Form.Item
                                                    {...field}
                                                    name={[field.name, "drugName"]}
                                                    fieldKey={[field.fieldKey, "drugName"]}
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: formatMessage({
                                                                id: "drug.configure.drugName",
                                                            }),
                                                        },
                                                    ]}
                                                    style={{
                                                        width: 280,
                                                        flex: 2,
                                                        margin: "0 0 16px 0",
                                                    }}
                                                ><CustomSelect
                                                        allowClear
                                                        placeholder={formatMessage({
                                                            id: "drug.configure.drugName",
                                                        })}
                                                        className="full-width"
                                                        onChange={(e: any) =>
                                                            handleSelectChange(e)
                                                        }
                                                        options={drugNameOptions}
                                                        dropdownRender={(menu) => (
                                                            <>
                                                                {singleDrugNames !== null &&
                                                                    packageDrugNames !== null &&
                                                                    singleDrugNames.length >
                                                                    0 &&
                                                                    packageDrugNames.length >
                                                                    0 && (
                                                                        <Tabs
                                                                            size="small"
                                                                            destroyInactiveTabPane
                                                                            style={{
                                                                                margin:
                                                                                    "-10px 12px 0 12px",
                                                                            }}
                                                                            activeKey={activeKey}
                                                                            onChange={
                                                                                handleTabChange
                                                                            }
                                                                            tabBarStyle={{
                                                                                marginBottom: 0,
                                                                            }}
                                                                        >
                                                                            <Tabs.TabPane
                                                                                tab={formatMessage({
                                                                                    id: "shipment.order.packageMethod.single",
                                                                                })}
                                                                                key="single"
                                                                            />
                                                                            <Tabs.TabPane
                                                                                tab={formatMessage({
                                                                                    id: "shipment.order.packageMethod.package",
                                                                                })}
                                                                                key="package"
                                                                            />
                                                                        </Tabs>
                                                                    )}
                                                                {cloneElement(menu)}
                                                            </>
                                                        )}

                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    {...field}
                                                    name={[field.name, "spec"]}
                                                    fieldKey={[field.fieldKey, "spec"]}
                                                    style={{ width: 160, paddingLeft: 10 }}
                                                    rules={[{
                                                        message: formatMessage({
                                                            id: "drug.configure.spec",
                                                        }), required: showDrugSpecs[field.name] !== undefined && showDrugSpecs[field.name] !== null && showDrugSpecs[field.name].length > 0
                                                    }]}
                                                >
                                                    <Select allowClear placeholder={formatMessage({ id: "drug.configure.spec" })} value={field.spec} className="full-width"
                                                        disabled={showDrugSpecs[field.name] === undefined || showDrugSpecs[field.name] === null || showDrugSpecs[field.name].length === 0}>
                                                        {
                                                            showDrugSpecs[field.name] !== undefined && showDrugSpecs[field.name] !== null && showDrugSpecs[field.name].map((e: any) => <Select.Option value={e}>{e}</Select.Option>)
                                                        }
                                                    </Select>
                                                </Form.Item>
                                                {/* </Space> */}
                                            </Row>
                                        </Col>
                                    ))}
                                </div>
                            )}
                        </Form.List>
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'storehouse.name' })} name="storehouseId" rules={[{ required: true }]}>
                        <Select placeholder={formatMessage({ id: 'placeholder.select.common' })} className="full-width" options={storehouses}>
                        </Select>
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'drug.list.expireDate' })} name="expirationDate"  >
                        {/* <DatePickers placeholder={formatMessage({ id: 'placeholder.select.common' })} className="full-width"></DatePickers> */}
                        <CustomDateTimePicker value={expirationDateValue} disabledDate={null} disabledTime={null} onChange={setExpirationDateValue} ph={'placeholder.input.common'}></CustomDateTimePicker>
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'drug.list.batch' })} name="batch"  >
                        <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear className="full-width" />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'drug.upload.uploadDrug' })} name="file" required>
                        <SingleUpload
                            bind={singleUpload}
                            accept='application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            tips={
                                <div style={{ fontSize: 12, color: '#ADB2BA', marginLeft: "16px", marginRight: "16px" }} onClick={e => e.stopPropagation()}>
                                    <span >{formatMessage({ id: 'common.upload.excel.tip' })}{g.lang === 'zh' ? ', ' : ' '}</span>
                                    <a onClick={() => downloadTemplateRun({ customerId, envId }).then()}>{formatMessage({ id: 'common.download.template' })}</a>
                                    {g.lang === 'zh' ? '。' : '.'}
                                </div>
                            }
                            width={445}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment >
    )
};


const CustomSelect = styled(Select)`
  .ant-select-selector {
    border-radius: 2px 0 0 2px !important;
  }
  .ant-select-item-option-active:hover {
    background: #000 !important;
  }
`;
