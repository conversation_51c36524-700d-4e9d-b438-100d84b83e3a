import {useGlobal} from "../../../../context/global";
import {useSafeState} from "ahooks";
import {useDrug} from "./context";
import {useAuth} from "../../../../context/auth";
import {useIntl} from "react-intl";
import {Col, Form, Input, InputNumber, Row, Select, Space, Switch, Tooltip} from "antd";
import React, {useState} from "react";
import {TipInputNumber} from "../../../../components/TipInput";

interface WeightColProp {
    field: any;
    attribute: any;
}
export const WeightCol = (props: WeightColProp) => {
    const gb = useGlobal();
    const [visible, setVisible] = useSafeState<boolean>(false);
    const { field, attribute } = props;
    const drug = useDrug();
    const auth = useAuth();
    const intl = useIntl();
    const { formatMessage } = intl;

    const changeWeight = (value: any, field: any) => {
        if (field.name + 1 > drug.configValues?.length) {
            drug.configValues.push({
                calculationInfo: { comparisonSwitch: value },
            });
        } else {
            drug.configValues[field.name].calculationInfo.comparisonSwitch = value;
        }
        drug.setConfigValues(drug.configValues);
    };

    return (
        <>
            {(drug.formulaType === 2 ||
                drug.formulaType === 3 ||
                drug.formulaType === 4) && (
                <>
                    <Form.Item
                        {...field}
                        name={[field.name, "calculationInfo", "comparisonSwitch"]}
                        style={{ marginBottom: "8px" }}
                        label={formatMessage({
                            id: "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation",
                        })}
                        valuePropName="checked"
                    >
                        <Switch
                            size="small"
                            onChange={(value) => {
                                changeWeight(value, { ...field });
                            }}
                        />
                    </Form.Item>
                    {drug.configValues[field.name]?.calculationInfo?.comparisonSwitch &&
                        gb.lang === "zh" && (
                            <>
                                <Row>
                                    <Form.Item
                                        style={{ width: "200px", marginBottom: "8px" }}
                                        colon={false}
                                        label={formatMessage({
                                            id: "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.compared.with",
                                        })}
                                        {...field}
                                        name={[field.name, "calculationInfo", "comparisonType"]}
                                        rules={[
                                            {
                                                required: true,
                                                message: formatMessage({
                                                    id: "placeholder.select.common",
                                                }),
                                            },
                                        ]}
                                    >
                                        <Select
                                            style={{ width: "160px" }}
                                            placeholder={formatMessage({
                                                id: "placeholder.select.common",
                                            })}
                                        >
                                            <Select.Option value={1}>
                                                {formatMessage({
                                                    id: "drug.batch.treatmentDesign.formula.weight.last.calculation",
                                                })}
                                            </Select.Option>
                                            <Select.Option value={2}>
                                                {formatMessage({
                                                    id: "drug.batch.treatmentDesign.formula.weight.last.actual",
                                                })}
                                            </Select.Option>
                                            {attribute?.info.random && drug.groupSelect !== "N/A" && (
                                                <Select.Option value={3}>
                                                    {formatMessage({
                                                        id: "drug.batch.treatmentDesign.formula.weight.random",
                                                    })}
                                                </Select.Option>
                                            )}
                                        </Select>
                                    </Form.Item>
                                    <Form.Item colon={false}>
                                        <span style={{marginRight:6}}>变化</span>
                                        <Space>

                                            <Form.Item
                                                name={[field.name, "calculationInfo", "comparisonSymbols"]}
                                                style={{ marginBottom: "8px" }}
                                            >
                                                <Select>
                                                    <Select.Option value={1}>{"<"}</Select.Option>
                                                    <Select.Option value={2}>{"<="}</Select.Option>
                                                    <Select.Option value={3}>{">="}</Select.Option>
                                                    <Select.Option value={0}>{">"}</Select.Option>
                                                </Select>
                                            </Form.Item>
                                            <Form.Item
                                                {...field}
                                                style={{ marginBottom: "8px" }}
                                                name={[field.name, "calculationInfo", "comparisonRatio"]}
                                                // label={formatMessage({
                                                //   id: "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.the.change.is",
                                                // })}
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: formatMessage({
                                                            id: "placeholder.input.common",
                                                        }),
                                                    },
                                                ]}
                                            >
                                                <InputNumber
                                                    placeholder={formatMessage({
                                                        id: "placeholder.input.common",
                                                    })}
                                                    // addonBefore={">"}
                                                    addonAfter={"%"}
                                                    style={{ width: 130, }}
                                                />
                                            </Form.Item>

                                        </Space>
                                    </Form.Item>

                                    ,
                                </Row>
                                <Form.Item
                                    {...field}
                                    style={{ width: "190px", marginTop: 6, marginBottom: "8px" }}
                                    colon={false}
                                    label={formatMessage({
                                        id: "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.is.used.for.this.calculation",
                                    })}
                                    rules={[
                                        {
                                            required: true,
                                            message: formatMessage({
                                                id: "placeholder.select.common",
                                            }),
                                        },
                                    ]}
                                    name={[
                                        field.name,
                                        "calculationInfo",
                                        "currentComparisonType",
                                    ]}
                                >
                                    <Select
                                        style={{ width: 160 }}
                                        placeholder={formatMessage({
                                            id: "placeholder.select.common",
                                        })}
                                    >
                                        <Select.Option value={1}>
                                            {formatMessage({
                                                id: "drug.batch.treatmentDesign.formula.weight.last.calculation",
                                            })}
                                        </Select.Option>
                                        <Select.Option value={2}>
                                            {formatMessage({
                                                id: "drug.batch.treatmentDesign.formula.weight.last.actual",
                                            })}
                                        </Select.Option>
                                        {attribute?.info.random && drug.groupSelect !== "N/A" && (
                                            <Select.Option value={3}>
                                                {formatMessage({
                                                    id: "drug.batch.treatmentDesign.formula.weight.random",
                                                })}
                                            </Select.Option>
                                        )}
                                    </Select>
                                </Form.Item>
                            </>
                        )}

                    {drug.configValues[field.name]?.calculationInfo?.comparisonSwitch &&
                        gb.lang === "en" && (
                            <>
                                <Row>
                                    <Form.Item
                                        // style={{width: "200px"}}
                                        style={{ marginBottom: "8px" }}
                                        colon={false}
                                        label={formatMessage({
                                            id: "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.compared.with",
                                        })}
                                        {...field}
                                        name={[field.name, "calculationInfo", "comparisonType"]}
                                        rules={[
                                            {
                                                required: true,
                                                message: formatMessage({
                                                    id: "placeholder.select.common",
                                                }),
                                            },
                                        ]}
                                    >
                                        <Select
                                            style={{ width: "160px", marginBottom: "8px" }}
                                            placeholder={formatMessage({
                                                id: "placeholder.select.common",
                                            })}
                                        >
                                            <Select.Option value={1}>
                                                {formatMessage({
                                                    id: "drug.batch.treatmentDesign.formula.weight.last.calculation",
                                                })}
                                            </Select.Option>
                                            <Select.Option value={2}>
                                                {formatMessage({
                                                    id: "drug.batch.treatmentDesign.formula.weight.last.actual",
                                                })}
                                            </Select.Option>
                                            {attribute?.info.random && drug.groupSelect !== "N/A" && (
                                                <Select.Option value={3}>
                                                    {formatMessage({
                                                        id: "drug.batch.treatmentDesign.formula.weight.random",
                                                    })}
                                                </Select.Option>
                                            )}
                                        </Select>
                                    </Form.Item>
                                </Row>
                                <Row>
                                    <Form.Item colon={false}
                                               style={{marginBottom:8}}
                                               label={formatMessage({
                                                   id: "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.the.change.is",
                                               })}>
                                        <Space>
                                            <Form.Item
                                                name={[field.name, "calculationInfo", "comparisonSymbols"]}
                                                style={{  width:60 }}
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: formatMessage({
                                                            id: "placeholder.input.common",
                                                        }),
                                                    },
                                                ]}
                                            >
                                                <Select>
                                                    <Select.Option value={1}>{"<"}</Select.Option>
                                                    <Select.Option value={2}>{"<="}</Select.Option>
                                                    <Select.Option value={3}>{">="}</Select.Option>
                                                    <Select.Option value={0}>{">"}</Select.Option>
                                                </Select>
                                            </Form.Item>
                                            <Form.Item
                                                {...field}
                                                style={{ width:160 }}
                                                name={[field.name, "calculationInfo", "comparisonRatio"]}

                                                rules={[
                                                    {
                                                        required: true,
                                                        message: formatMessage({
                                                            id: "placeholder.input.common",
                                                        }),
                                                    },
                                                ]}
                                            >
                                                <InputNumber
                                                    placeholder={formatMessage({
                                                        id: "placeholder.input.common",
                                                    })}
                                                    addonAfter={"%"}
                                                    style={{ width: 160, marginRight: 2 }}
                                                />
                                            </Form.Item>
                                        </Space>
                                    </Form.Item>
                                    <span style={{ marginTop: 6 }}>{","}</span>
                                </Row>
                                <Form.Item
                                    {...field}
                                    style={{ marginTop: 6, marginBottom: "8px" }}
                                    colon={false}
                                    label={formatMessage({
                                        id: "drug.batch.treatmentDesign.treatment.design.weight.comparison.calculation.is.used.for.this.calculation",
                                    })}
                                    rules={[
                                        {
                                            required: true,
                                            message: formatMessage({
                                                id: "placeholder.select.common",
                                            }),
                                        },
                                    ]}
                                    name={[
                                        field.name,
                                        "calculationInfo",
                                        "currentComparisonType",
                                    ]}
                                >
                                    <Select
                                        style={{ width: 160 }}
                                        placeholder={formatMessage({
                                            id: "placeholder.select.common",
                                        })}
                                    >
                                        <Select.Option value={1}>
                                            {formatMessage({
                                                id: "drug.batch.treatmentDesign.formula.weight.last.calculation",
                                            })}
                                        </Select.Option>
                                        <Select.Option value={2}>
                                            {formatMessage({
                                                id: "drug.batch.treatmentDesign.formula.weight.last.actual",
                                            })}
                                        </Select.Option>
                                        {attribute?.info.random && drug.groupSelect !== "N/A" && (
                                            <Select.Option value={3}>
                                                {formatMessage({
                                                    id: "drug.batch.treatmentDesign.formula.weight.random",
                                                })}
                                            </Select.Option>
                                        )}
                                    </Select>
                                </Form.Item>
                            </>
                        )}
                </>
            )}
        </>
    );
};

interface FormulaProp {
    field: any;
    form: any;
}

export const Formula = (props: FormulaProp) => {
    const gob = useGlobal();
    const { field, form } = props;
    const drug = useDrug();
    const intl = useIntl();
    const { formatMessage } = intl;
    const [tooltipVisible, setTooltipVisible] = useState(false);
    const [tooltipIndex, setTooltipIndex] = useState(0);

    const changeIndexVisible = (vis: any, inx: any) => {
        setTooltipVisible(vis);
        setTooltipIndex(inx);
    };

    return (
        <Form.List name={[field.name, "calculationInfo", "formulas"]}>
            {(fields: any, { add, remove }) => (
                <Row>
                    {fields.map((childField: any, index: any) => (
                        <Row key={childField.key} style={{ marginTop: 12 }}>
                            <Tooltip
                                visible={index === tooltipIndex ? tooltipVisible : false}
                                trigger={["hover"]}
                                overlayInnerStyle={{ width: "241px", marginLeft: "-20px" }}
                                color={"white"}
                                placement="top"
                                title={
                                    <>
                                        <Row
                                            style={{
                                                color: "#677283",
                                                fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                                fontSize: "12px",
                                                fontWeight: 400,
                                                lineHeight: "22px",
                                                letterSpacing: "0px",
                                                textAlign: "left",
                                            }}
                                        >
                                            <Col>
                                                <svg className="iconfont" width={8} height={8}>
                                                    <use xlinkHref="#icon-xinxitishi2"></use>
                                                </svg>
                                                <span style={{ marginLeft: "8px" }}>
                          {formatMessage({
                              id: "drug.batch.treatmentDesign.treatment.design.weight.title1",
                          })}
                        </span>
                                            </Col>
                                        </Row>
                                        <Row
                                            style={{
                                                color: "#677283",
                                                fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                                fontSize: "12px",
                                                fontWeight: 400,
                                                lineHeight: "22px",
                                                letterSpacing: "0px",
                                                textAlign: "left",
                                                marginLeft: "15px",
                                            }}
                                        >
                                            {formatMessage({
                                                id: "drug.batch.treatmentDesign.treatment.design.weight.title2",
                                            })}
                                        </Row>
                                        <Row
                                            style={{
                                                color: "#677283",
                                                fontFamily: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial, sans-serif",
                                                fontSize: "12px",
                                                fontWeight: 400,
                                                lineHeight: "22px",
                                                letterSpacing: "0px",
                                                textAlign: "left",
                                                marginLeft: "15px",
                                            }}
                                        >
                                            <span>{"eg:"}</span>
                                            <span style={{ color: "#1D2129" }}>
                        {"X≤1；1<X≤12；1<X；1=X"}
                      </span>
                                        </Row>
                                    </>
                                }
                            >
                                <Form.Item
                                    {...childField}
                                    name={[childField.name, "expression"]}
                                >
                                    <Input
                                        onFocus={() => changeIndexVisible(true, index)}
                                        onBlur={() => changeIndexVisible(false, index)}
                                        onMouseEnter={() => changeIndexVisible(true, index)}
                                        onMouseLeave={() => changeIndexVisible(false, index)}
                                        style={{ width: gob.lang === "zh" ? "260px" : "200px" }}
                                        placeholder={formatMessage({
                                            id:
                                                drug.formulaType === 1
                                                    ? "drug.configure.formula.age.range"
                                                    : "drug.configure.formula.weight.range",
                                        })}
                                        addonAfter={
                                            drug.formulaType === 1
                                                ? formatMessage({
                                                    id: "drug.configure.formula.age.name",
                                                })
                                                : "KG"
                                        }
                                    />
                                </Form.Item>
                            </Tooltip>
                            <Form.Item
                                {...childField}
                                name={[childField.name, "value"]}
                                style={{ marginLeft: 8 }}
                            >
                                {gob.lang === "zh" ? (
                                    <InputNumber
                                        style={{width: "100px" }}
                                        placeholder={formatMessage({
                                            id: "drug.configure.drugNumber",
                                        })}
                                    />
                                ) : (
                                    <TipInputNumber
                                        trigger={["hover"]}
                                        className="full-width mar-lft-5"
                                        placeholder={formatMessage({
                                            id: "drug.configure.drugNumber",
                                        })}
                                        title
                                    />
                                )}
                            </Form.Item>
                            {
                                <Tooltip
                                    placement="top"
                                    title={formatMessage({ id: "common.add" })}
                                >
                                    <svg
                                        className="iconfont"
                                        width={16}
                                        height={16}
                                        onClick={() => {
                                            add();
                                        }}
                                        style={{
                                            color: "#fe5b5a",
                                            marginLeft: 12,
                                            marginTop: 10,
                                            cursor: "pointer",
                                        }}
                                    >
                                        <use xlinkHref="#icon-zengjia"></use>
                                    </svg>
                                </Tooltip>
                            }
                            {fields.length !== 1 && (
                                <Tooltip
                                    placement="top"
                                    title={formatMessage({ id: "common.delete" })}
                                >
                                    <svg
                                        className="iconfont"
                                        width={16}
                                        height={16}
                                        onClick={() => {
                                            remove(childField.name);
                                        }}
                                        style={{
                                            color: "#fe5b5a",
                                            marginLeft: 12,
                                            marginTop: 10,
                                            cursor: "pointer",
                                        }}
                                    >
                                        <use xlinkHref="#icon-shanchu"></use>
                                    </svg>
                                </Tooltip>
                            )}
                        </Row>
                    ))}
                </Row>
            )}
        </Form.List>
    );
};