import React, {useEffect} from "react";
import {
    <PERSON><PERSON>,
    Spin,
    Table,
    ConfigProvider,
    Empty,
    Row,
    Col,
    Input,
    message, Modal, Form,
} from "antd";
import { BatchManagementEdit } from "./batch-management-edit";
import { BatchManagementMultipleEdit } from "./batch-management-multipleEdit";
import { batchManagementTypes } from "../../../../data/data.jsx";
import { permissions } from "../../../../tools/permission";
import { useAuth } from "../../../../context/auth";
import { useFetch } from "../../../../hooks/request";
import { getBatchList } from "../../../../api/medicine";
import { useSafeState } from "ahooks";
import { usePage } from "../../../../context/page";
import { PaginationView } from "../../../../components/pagination";

import styled from "@emotion/styled";
import {CloseCircleFilled, PlusOutlined, SearchOutlined} from "@ant-design/icons";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import EmptyImg from "images/empty.png";
import _ from "lodash";
import {useDrug} from "./context";
import {getBatchGroupNum} from "../../../../api/project_site";
import {getDepotBatchGroup, updateDepotBatchGroup} from "../../../../api/project_storehouse";
import {getProjectAttributes} from "../../../../api/randomization";
import {BatchGroupStore} from "../site/batch_group_store";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import {AuthButton} from "../../../common/auth-wrap";


export const BatchManagement = (props: any) => {
    const auth = useAuth();
    const intl = useTranslation();
    const ctx = useDrug()
    const page = usePage();
    const { formatMessage } = intl;
    const [queryValue, setQueryValue] = useSafeState(null);
    const [queryTypes, setQueryTypes] = useSafeState(null);
    const [data, setData] = useSafeState<any>([]);
    const [updateBatchs, setUpdateBatchs] = useSafeState([]);
    const [updateBatchIds, setUpdateBatchIds] = useSafeState([]);
    const [IPInheritance, setIPInheritance] = useSafeState(false);
    const batch_management_edit = React.useRef();
    const batch_management_multEdit = React.useRef();

    const projectStatus = auth.project.status ? auth.project.status : 0;

    const { runAsync: getBatchListRun, loading: getBatchListLoading } =
        useFetch(getBatchList, { manual: true });
    const { runAsync: runGetProjectAttributesAttr } = useFetch(getProjectAttributes, { manual: true });
    const getList = () => {
        setUpdateBatchs([]);
        setUpdateBatchIds([]);
        runGetProjectAttributesAttr({env:auth.env.id}).then(
            (res:any) => {
                if (res?.data) {
                    setIPInheritance(res?.data?.find((item:any)=>item.info.IPInheritance === true))
                }
            }
        )
        setData([]);
        page.setTotal(0);
        page.setData([]);
        const trimmedValue = (queryValue !== null && queryValue !== undefined) ? (queryValue as string).trim() : '';

        getBatchListRun({
            customerId: auth.customerId,
            envId: auth.env.id,
            roleId: auth.project.permissions.role_id,
            queryValue: trimmedValue,
            queryTypes: queryTypes,
            start: (page.currentPage - 1) * page.pageSize,
            limit: page.pageSize,
        }).then((result: any) => {
            page.setTotal(result.data.total);
            page.setData(result.data.items);
            setData(fillTableCellEmptyPlaceholder(result.data.items ? result.data.items : []));
        });
    };

    function renderType(type: any) {
        return batchManagementTypes.find((it: any) => it.key === type)?.value;
    }

    const edit = (record: any) => {
        // @ts-ignore
        batch_management_edit.current.show(record);
    };

    const editMultiple = () => {
        if (updateBatchs.length <= 0) {
            message.warn(
                formatMessage({
                    id: "menu.projects.project.build.randomization.tooltip",
                })
            );
        } else {
            //验证是否同一个批次有效期的
            var batchNumber = updateBatchs[0]["batchNumber"];
            var expireDate = updateBatchs[0]["expirationDate"];
            var name = updateBatchs[0]["name"];
            var flag = true;
            updateBatchs.forEach((it: any) => {
                if (
                    it["name"] !== name ||
                    it["batchNumber"] !== batchNumber ||
                    it["expirationDate"] !== expireDate
                ) {
                    flag = false;
                    return;
                }
            });
            if (flag) {
                // @ts-ignore
                batch_management_multEdit.current.show(updateBatchs);
            } else {
                message.warn(
                    formatMessage({
                        id: "drug.batch.management.update.info",
                    })
                );
                return false;
            }
        }
    };

    const onSearch = (e: any) => {
        setQueryValue(e.target.value);
        page.setCurrentPage(1);
    };

    const setBatchGroupVisible = () => {
        ctx.setBatchGroup(true)
    }

    const rowSelection: any = {
        type: "checkbox",
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            setUpdateBatchs(selectedRows);
            var newSelectRowKeys: any = _.uniq(selectedRowKeys)
            setUpdateBatchIds(newSelectRowKeys);
        },
        selectedRows: updateBatchs,
        selectedRowKeys: updateBatchIds,
        preserveSelectedRowKeys: true,
    };

    const resetSelected = () => {
        setUpdateBatchs([]);
        setUpdateBatchIds([]);
    }

    const onFilterType = (filters: any) => {
        var types = filters["type"];
        if (types != null) {
            setQueryTypes(types.join(","));
        } else {
            setQueryTypes(null);
        }
        page.setCurrentPage(1);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(getList, [
        queryValue,
        queryTypes,
        page.currentPage,
        page.pageSize,
    ]);
    // React.useImperativeHandle(props.bind, () => ({refresh: getList}));

    const refreshList = () => {
        getList();
        resetSelected()
    };

    return (
        <React.Fragment>
            <Row gutter={8} justify="space-between">
                <Col xs={12} sm={12} md={12} lg={4} style={{ paddingRight: 0 }}>
                    <Input
                        placeholder={formatMessage({
                            id: "placeholder.input.common",
                        }) as string}
                        onChange={onSearch}
                        style={{ width: 220, marginRight: 12 }}
                        suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                    />
                </Col>
                <MarLft12Col>
                    {
                        permissions(
                            auth.project.permissions,
                            "operation.build.medicine.batch.setting"
                        ) && IPInheritance &&
                        <AuthButton
                            onClick={
                                () => {
                                    setBatchGroupVisible()
                                }
                            }
                        >
                            {formatMessage({id:"common.setting", allowComponent: true})}
                        </AuthButton>
                    }


                    {permissions(
                        auth.project.permissions,
                        "operation.build.medicine.batch.update"
                    ) ? (
                        <AuthButton onClick={editMultiple} type="primary">
                            <FormattedMessage id="common.update" />
                        </AuthButton>
                    ) : null}
                </MarLft12Col>
            </Row>
            <Spin spinning={getBatchListLoading}>
                <ConfigProvider
                    renderEmpty={() => {
                        return (
                            <Empty
                                image={
                                    <img
                                        src={EmptyImg}
                                        style={{ width: 300, height: 213 }}
                                    />
                                }
                                imageStyle={{
                                    height: 240,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            />
                        );
                    }}
                >
                    <Table
                        className="mar-top-10"
                        dataSource={data}
                        pagination={false}
                        rowSelection={rowSelection}
                        rowKey={(record: any) =>
                            record.batchNumber +
                            record.expirationDate +
                            record.name +
                            record.position +
                            record.type
                        }
                        onChange={(
                            pagination,
                            filters: any,
                            sorter,
                            extra: {}
                        ) => onFilterType(filters)}
                    >
                        <Table.Column
                            title={<FormattedMessage id={"common.serial" } />}
                            dataIndex="#"
                            key="#"
                            width={70}
                            render={(text, record, index) => ((page.currentPage - 1) * page.pageSize + index + 1)}
                        />
                        <Table.Column
                            title={<FormattedMessage id="drug.list.batch" />}
                            key={"batchNumber"}
                            dataIndex={"batchNumber"}
                            ellipsis
                        />
                        <Table.Column
                            title={
                                <FormattedMessage id="drug.list.expireDate" />
                            }
                            key={"expirationDate"}
                            dataIndex={"expirationDate"}
                            ellipsis
                        />
                        <Table.Column
                            title={
                                <FormattedMessage id="drug.configure.drugName" />
                            }
                            key={"name"}
                            dataIndex={"name"}
                            ellipsis
                        />
                        <Table.Column
                            title={
                                <FormattedMessage id="projects.statistics.sku.place" />
                            }
                            key={"position"}
                            dataIndex={"position"}
                            ellipsis
                        />
                        <Table.Column
                            title={<FormattedMessage id="drug.other.singleCount" />}
                            key={"count"}
                            dataIndex={"count"}
                            width={120}
                            ellipsis
                        />
                        <Table.Column
                            title={<FormattedMessage id="common.type" />}
                            key={"type"}
                            dataIndex={"type"}
                            width={90}
                            filters={[
                                {
                                    text: (
                                        <FormattedMessage id="common.depot" />
                                    ),
                                    value: 2,
                                },
                                {
                                    text: (
                                        <FormattedMessage id="common.site" />
                                    ),
                                    value: 1,
                                },
                                {
                                    text: (
                                        <FormattedMessage id="batchMag.type.order" />
                                    ),
                                    value: 3,
                                },
                            ]}
                            render={(value, record, index) => renderType(value)}
                            ellipsis
                        />
                        {permissions(
                            auth.project.permissions,
                            "operation.build.medicine.batch.edit"
                        ) && projectStatus !== 2 ? (
                            <Table.Column
                                title={
                                    <FormattedMessage id="common.operation" />
                                }
                                align="center"
                                width={120}
                                render={(value, record, index) => (
                                    <React.Fragment>
                                        <AuthButton
                                            size="small"
                                            type="link"
                                            onClick={() => edit(record)}
                                        >
                                            {" "}
                                            <FormattedMessage id="common.edit" />
                                        </AuthButton>
                                    </React.Fragment>
                                )}
                            />
                        ) : null}
                    </Table>
                </ConfigProvider>
            </Spin>
            <PaginationView mode="SELECTABLE" selectedNumber={updateBatchIds?.length} clearDisplay={true} refresh={refreshList} />
            <BatchManagementEdit
                bind={batch_management_edit}
                refresh={getList}
            />
            <BatchManagementMultipleEdit
                bind={batch_management_multEdit}
                refresh={getList}
            />
            <BatchGroupSetting/>
        </React.Fragment>
    );
};


const BatchGroupSetting = (prop:any)=> {
    const auth = useAuth();
    const intl = useTranslation();

    const { formatMessage } = intl;

    const ctx = useDrug()
    const [form] :any = Form.useForm()
    const [batchLabel, setBatchLabel] = useSafeState([])
    const [groupLabel, setGroupLabel] = useSafeState([])
    const { runAsync: getBatchGroupNumRun, loading:getBatchGroupNumLoading } = useFetch(getBatchGroupNum, { manual: true });
    const { runAsync: updateDepotBatchGroupRun, loading:updateDepotBatchGroupLoading } = useFetch(updateDepotBatchGroup, { manual: true });
    const { runAsync: getDepotBatchGroupRun, loading:getDepotBatchGroupLoading } = useFetch(getDepotBatchGroup, { manual: true });
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const getBatchGroupNumFun = () => {
        getBatchGroupNumRun({projectId:projectId, envId:envId}).then(
            (res:any) => {
                setBatchLabel(res.data.batch?res.data.batch:[])
                setGroupLabel(res.data.group?res.data.group:[])
            }
        )
    }

    const hide = ()=> {
        setUseStorehousesData([])
        ctx.setBatchGroup(false)
        form.setFieldsValue({})
    }

    useEffect(() => {
        form.setFieldsValue(
            {
                info:[{batchGroupAlarm:[{"info":[{}]}]}]
            }
        )
        if (ctx.batchGroup) {
            getBatchGroupNumFun()
            getDepotBatchGroupRun({envId:envId}).then(
                (res:any) => {
                    if (res.data.info?.length > 0) {
                        setUseStorehousesData(res.data.info?.map((it:any)=> it.depot))
                        form.setFieldsValue(
                            {
                                info:res.data?.info
                            }
                        )
                    }
                }
            )

        }
    },[ctx.batchGroup])



    const [useStorehousesData, setUseStorehousesData] = useSafeState([]);

    const save = () => {

        form.validateFields().then((value:any) => {
            updateDepotBatchGroupRun({
                customerId: auth.customerId,
                envId: auth.env.id,
                projectId : auth.project.id,
                ...value
            }).then(
                () => {
                    hide()

                }
            )

        })
    }


    return <Modal
        className="custom-medium-modal-site-add"
        title={formatMessage({id:"common.setting"})}
        open={ctx.batchGroup}
        centered
        destroyOnClose
        onCancel={hide}
        onOk={save}
        okText={formatMessage({ id: "common.ok" })}
>
        <Form form={form}>
            <Form.List name={"info"}>
                {(fields: any, {add: addx, remove:removex}) => (
                    <>
                        {fields.map((field: any, index: any) => (
                            <CustomCol>
                                <BatchGroupStore
                                    useStorehousesData={useStorehousesData}
                                    setUseStorehousesData={setUseStorehousesData}
                                    field={field}
                                    types={2}
                                    form={form}
                                    batchLabel={batchLabel}
                                    groupLabel={groupLabel}
                                />
                                {
                                    fields?.length > 1 && <CloseCircleFilled
                                        className="delete-icon"
                                        style={{color: "#fe5b5a"}}
                                        onClick={() => {
                                            removex(field.name)
                                            setUseStorehousesData(form.getFieldsValue().info?.map((it:any) => it.depot))
                                        }}
                                    />
                                }

                            </CustomCol>
                        ))}
                        {
                            <Form.Item>
                                <AuthButton
                                    block
                                    type="dashed"
                                    icon={<PlusOutlined/>}
                                    onClick={() => {
                                        addx()
                                        const data: any = form.getFieldsValue().info?.slice(0,-1) || []
                                        form.setFieldsValue({"info": [...data, {batchGroupAlarm:[{"info":[{}]}]}]})
                                    }}
                                >
                                   <span>{formatMessage({id: 'drug.batch.management.storeGroup.add'})}</span>
                                </AuthButton>
                            </Form.Item>
                        }
                    </>
                )}
            </Form.List>
        </Form>
    </Modal>
}


const MarLft12Col = styled(Col)`
    padding-left: 0 !important;

    & > * {
        margin-left: 12px;
    }
`;


export const CustomCol = styled(Col)`
  position: relative;
  background: #f8f9fa;
  margin-bottom: 16px;
  .delete-icon {
    position: absolute;
    right: -3px;
    top: -3px;
  }
`;