import React, {useState} from 'react';
import {FormattedMessage, useIntl} from "react-intl";
import {Button, Col, Form, Input, Modal, Row, Switch, message, notification} from "antd";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {getVisitSettings, setUpVisit} from "../../../../api/visit";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../../context/global";
import styled from "@emotion/styled";
import {useDrug} from "./context";
import {
    useNavigate,
} from "react-router-dom";
import {
    getIsBlindedRole,
  } from "../../../../api/drug";
import {permissionsCohort} from "../../../../tools/permission";
import _ from 'lodash';


export const VisitSetting = (props:any) => {
    const auth = useAuth()
    const intl = useIntl();
    const {formatMessage} = intl;
    const drug = useDrug();

    const [isOpen, setIsOpen] = useState(false);
    const [isBlindedRole, setIsBlindedRole] = useSafeState<any>(false);
    const [visible, setVisible] = useSafeState(false);
    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState(null);

    const [form] = Form.useForm();
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const roleId = auth.project.permissions.role_id;
    const cohortId = props.cohort ? props.cohort.id : null;
    const projectStatus = auth.project.status ? auth.project.status : 0;
    const lockConfig = auth.env.lockConfig ? auth.env.lockConfig : false;
    const customerId = auth.customerId;

    const { runAsync: getIsBlindedRoleRun, loading: getIsBlindedRoleLoading } = useFetch(
        getIsBlindedRole,
        { manual: true }
    );

    const {runAsync: getVisitSettingsRun, loading: getVisitSettingsLoading} = useFetch(getVisitSettings, {manual: true});

    const {runAsync: setUpVisitRun, loading: setUpVisitLoading} = useFetch(setUpVisit, {manual: true});

    const getBlindedRole = () => {
        getIsBlindedRoleRun({
          roleId: roleId,
        }).then((result: any) => {
          setIsBlindedRole(result.data)
        });
    };

    const show = (id: any) => {
        setVisible(true);
        getBlindedRole();
        setSend(true);
        getVisitSettingsRun({
            customerId,
            projectId,
            envId,
            cohortId: cohortId,
            roleId: roleId,
        }).then((result: any) => {
            form.setFieldsValue({...result.data});
            setOldData(result.data);
            setIsOpen(result.data.isOpen);
        });
    }

    const hide = () => {
        getVisitSettingsRun({
            customerId,
            projectId,
            envId,
            cohortId: cohortId,
            roleId: roleId,
        }).then((result: any) => {
            form.setFieldsValue({...result.data});
            setOldData(result.data);
            setIsOpen(result.data.isOpen);
        });
        setSend(true);
        // props.refresh();
        setVisible(false);
        // form.resetFields();
    };

    const formChange = () => {
        const a = oldData;;
        // console.log("1===" + JSON.stringify(a)); 
        let b = form.getFieldsValue();
        // console.log("2===" + JSON.stringify(b)); 
        if (!compareObjects(a, b)) {
            setSend(false);
        } else {
            setSend(true);
        }
    };

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1: any, obj2: any) {
        for (let key in obj1) { 
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                if (!arraysAreEqual(obj1[key], obj2[key])) {
                    return false;
                }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1: any, arr2: any) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }

    const navigate = useNavigate(); // 创建一个导航函数

    const handleClick = () => {
      // 在这里定义跳转逻辑
    //   navigate('/target-page'); // 替换 '/target-page' 为目标页面的路径
      if(permissionsCohort(auth.project.permissions,"operation.build.medicine.visit.push",props.cohort?.status) && !lockConfig && projectStatus !== 2){
        drug.setVisitView(true);
        notification.destroy();
      }
    };

    const save = () => {
        form.validateFields().then(values => {
            setUpVisitRun(
                {
                    customerId: customerId,
                    projectId: projectId, 
                    envId: envId, 
                    cohortId: cohortId, 
                    roleId: roleId,
                },{...values}
            ).then(
                () => {
                    props.refresh();
                    message.success(formatMessage({id: "common.success"}));
                    hide();
                }
            )
        }).catch(() => {})
    };

    const close = () => {
        form.validateFields().then(values => {
            values.nameZh = "";
            values.nameEn = "";
            setUpVisitRun(
                {
                    customerId: customerId,
                    projectId: projectId, 
                    envId: envId, 
                    cohortId: cohortId, 
                    roleId: roleId,
                },{...values}
            ).then(
                () => {
                    props.refresh();
                    form.resetFields();
                    message.success(formatMessage({id: "common.success"}));
                    // hide();
                }
            )
        }).catch(() => {})
    };

    const g = useGlobal()

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 5: 7 },
        },

    }

    React.useImperativeHandle(props.bind, () => ({show}));
    return (
        <React.Fragment>
            <Modal
                forceRender
                className="custom-visit-setting-modal"
                title={<FormattedMessage id={"common.setting"} />}
                open={visible}
                onCancel={hide}
                destroyOnClose={true}

                centered={true}

                maskClosable={false}
                footer={
                    isOpen?
                    <Row justify="end" style={{paddingRight:14}}>
                        <Col>
                            <Button onClick={hide} >
                                <FormattedMessage id="common.cancel" />
                            </Button>
                            <Button disabled={send} onClick={save} type="primary" loading={getVisitSettingsLoading || setUpVisitLoading}>
                                <FormattedMessage id="common.ok" />
                            </Button>
                        </Col>
                    </Row>:null
                }
            >
                <Form form={form} onValuesChange={formChange} {...formItemLayout}>
                    <Form.Item
                        name="isOpen"
                        label={formatMessage({
                            id: "visit.cycle.setting.unscheduled_visit",
                        })}
                    >
                        <Switch
                            size={"small"}
                            onChange={(e) =>{
                                setIsOpen(form.getFieldValue("isOpen"));
                                if(!e){
                                    close();
                                }
                            }}
                            checked={
                                isOpen
                            }
                            disabled={(isBlindedRole || !permissionsCohort(auth.project.permissions,"operation.build.medicine.visit.setting.edit",props.cohort?.status)) ? true : false}
                        ></Switch>
                    </Form.Item>
                    {
                        isOpen && 
                        <Form.Item 
                            name="nameZh"
                            label={formatMessage({
                            id: "visit.cycle.setting.nameZh",
                            })}
                            rules={[{ required: true }]}
                        >
                            <Input 
                                className="full-width" 
                                placeholder={formatMessage({id: 'placeholder.input.common'})} 
                                allowClear 
                                disabled={(isBlindedRole || !permissionsCohort(auth.project.permissions,"operation.build.medicine.visit.setting.edit",props.cohort?.status))  ? true : false}
                            />
                        </Form.Item>
                    }
                    {
                        isOpen && 
                        <Form.Item 
                            name="nameEn"
                            label={formatMessage({
                            id: "visit.cycle.setting.nameEn",
                            })}
                            rules={[{ required: true }]}
                        >
                            <Input 
                                className="full-width" 
                                placeholder={formatMessage({id: 'placeholder.input.common'})} 
                                allowClear 
                                disabled={(isBlindedRole || !permissionsCohort(auth.project.permissions,"operation.build.medicine.visit.setting.edit",props.cohort?.status))  ? true : false}
                            />
                        </Form.Item>
                    }
                </Form>
            </Modal>
        </React.Fragment>
    )
};

const CusSelect = styled.div`

  .custom-select .ant-select-selector {
    background-color: #8d4343;
  }
  .custom-select .ant-select-selection-item {
    background-color: #8d4343;
  }
`