import React from "react";
import { useIntl } from "react-intl";
import {
    Modal,
    Form,
    Table,
    Spin,
    Badge,
    Row,
    Col,
    Radio,
    RadioChangeEvent,
    Input,
    message,
    Select,
} from "antd";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { useGlobal } from "../../../../context/global";
import { Title } from "../../../../components/title";
import { getGroupNameDataNumber, getMedicineName, toExamineFlowPath } from "api/medicine";
import { useFetch } from "hooks/request";
import { medicineStatusColors } from "data/data";
import { medicineStatusData } from "tools/medicine_status";
import FormattedMessage from "react-intl/src/components/message";
import { combineRow } from "../../../../utils/merge_cell";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";

export const MedicineFolwPath = (props: any) => {

    const auth = useAuth()
    const intl = useIntl();
    const { formatMessage } = intl;
    const g = useGlobal()

    const [visible, setVisible] = useSafeState<any>(false);
    const [title, setTitle] = useSafeState<any>("");
    const [tip, setTip] = useSafeState<any>("");
    const [tableData, setTableData] = useSafeState<any>([]);
    const [selecteds, setSelecteds] = useSafeState<any>([]);
    const [selectedIds, setSelectedIds] = useSafeState<any>([]);
    const [medicineNames, setMedicineNames] = useSafeState<any>([]);
    const [sign, setSign] = useSafeState<any>(0);
    const [radioValue, setRadioValue] = useSafeState<any>(null);
    const [remarkValue, setRemarkValue] = useSafeState<any>("");
    const [isOpenPackage, setIsOpenPackage] = useSafeState<any>(false);
    const customerId = auth.customerId;
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;

    // 获取研究产品数据
    const { runAsync: groupNameDataNumberRun, loading: groupNameDataNumberLoading } = useFetch(getGroupNameDataNumber, { manual: true })
    const { runAsync: toExamineFlowPathRun } = useFetch(
        toExamineFlowPath,
        {
            manual: true,
        }
    );
    const { runAsync: getMedicineNameRun, loading: getMedicineNameLoading } = useFetch(getMedicineName, { manual: true })

    const show = (sign: any) => {

        // 展示模态框
        setVisible(true);
        setSign(sign);

        // 标题和提示
        if (sign === 1) {           // 审核
            setTitle(formatMessage({ id: "medicine.but.examine" }))
            setTip(formatMessage({ id: "medicine.status.examine.tip" }))

            // 获取数据
            getDataList(22);
        } else if (sign === 2) {     // 修改
            setTitle(formatMessage({ id: "medicine.but.update" }))
            setTip(formatMessage({ id: "medicine.status.update.tip" }))

            // 获取数据
            getDataList(23);
            medicineName();
        } else {                    // 放行
            setTitle(formatMessage({ id: "medicine.but.release" }))
            setTip(formatMessage({ id: "medicine.status.release.tip" }))

            // 获取数据
            getDataList(0);
        }
    };

    // 保存
    const save = () => {
        if (selecteds == null || selecteds.length == 0) {
            message.error(
                intl.formatMessage({
                    id: "shipment.order.create.info",
                })
            );
        } else {
            if (sign === 1 && radioValue == null) {
                message.error(
                    intl.formatMessage({
                        id: "medicine.status.examine.confirm",
                    })
                );
            } else {
                toExamineFlowPathRun({
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    sign: sign,
                    radioValue: radioValue,
                    remarkValue: remarkValue,
                    medicineParameter: selecteds
                }).then((result: any) => {
                    message.success(result.msg)
                    props.refresh();
                    hide();
                });
            }
        }
    };

    // 关闭模态框
    const hide = () => {
        setVisible(false);
        setTableData([]);
        setTitle("");
        setTip("");
        setSelecteds([]);
        setSelectedIds([]);
        setSign(0);
        setRadioValue(null);
        setRemarkValue("");
        setMedicineNames([]);
    };

    // 查询数据
    const getDataList = (status: any) => {
        setTableData([]);
        groupNameDataNumberRun({ customerId: customerId, projectId: projectId, envId: envId, status: status }).then(
            (result: any) => {
                let packageDrug = false;
                let resultData = result.data;
                if (resultData != null) {
                    setIsOpenPackage(resultData.packageIsOpen);
                    if (resultData.packageIsOpen) {      // 开启包装
                        packageDrug = true;
                    }
                    let data: { count: number, batchNumber: string, expirationDate: string, name: string }[] = [];
                    resultData.data.forEach((d: any, index: number) => {
                        let medicineIds: string[] = []; // 修正了变量声明
                        d.medicineIds.forEach((medicineId: any) => {
                            medicineIds.push(medicineId._id)
                        });
                        let result = {
                            i: index,
                            count: d.count,
                            batchNumber: d.batchNumber,
                            expirationDate: d.expirationDate,
                            name: d.name,
                            status: status,
                            mesicineIds: medicineIds,
                            packageNumber: d.packageNumber,
                            packageDrug: packageDrug,
                        };
                        data.push(result);
                    });
                    var medicineData = combineRow(data, "packageNumber", "packageNumber", false)
                    setTableData(fillTableCellEmptyPlaceholder(medicineData ? medicineData : []));
                }
            }
        )
    };

    // 查询可扫码的药物名称
    const medicineName = () => {
        getMedicineNameRun({ projectId: projectId, envId: envId }).then(
            (result: any) => {
                setMedicineNames(result.data);
            }
        )
    };

    function renderStatus(value: any) {
        let code_rule = 1; // 自动编码
        // @ts-ignore
        return (
            <Badge
                color={medicineStatusColors[value]}
                text={
                    medicineStatusData(
                        code_rule,
                        auth.project.info.research_attribute
                    ).find((it) => it.value === value)?.label ?? "-"
                }
            />
        );
    }

    const onChangeRadio = (e: RadioChangeEvent) => {
        setRadioValue(e.target.value);

    };

    const onChangeRemark = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setRemarkValue(e.target.value);

    };

    // 表格复选框
    const rowSelection = {
        renderCell: (value: any, record: any, index: any, originNode: any) => {
            let obj = {
                children: originNode,
                props: { rowSpan: tableData[index].packageNumberRowSpan }
            };
            return obj;
        },
        type: "checkbox" as "checkbox",
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            if (isOpenPackage) {
                let keys = [];
                let rows = [];
                for (let td of selectedRows) {
                    if (td.packageNumber) {
                        let tableF = tableData.filter((item: any) => item.packageNumber === td.packageNumber);
                        let tableM = tableF.map((item: any) => item.i);
                        if (td.i === tableF[0].i) {
                            keys.push(...tableM);
                            rows.push(...tableF);
                        }
                    } else {
                        keys.push(td.i);
                        rows.push(td);
                    }
                }
                setSelecteds(rows);
                setSelectedIds(keys);
            } else {
                setSelecteds(selectedRows);
                setSelectedIds(selectedRowKeys);
            }
        },
        selectedRows: selecteds,
        selectedRowKeys: selectedIds,
    };

    const isRowSelected = (rowKey: any) => {
        return selectedIds.includes(rowKey);
    };


    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Spin spinning={groupNameDataNumberLoading}>
                <Modal
                    className="custom-large-modal"
                    title={title}
                    centered={true}
                    maskClosable={false}
                    visible={visible}
                    onCancel={hide}
                    onOk={save}
                    okText={formatMessage({ id: "common.ok" })}
                >
                    <div style={{ marginTop: 12 }}>
                        <Row style={{ height: 32, backgroundColor: "#165DFF1A", marginBottom: 12, paddingLeft: 12, paddingBottom: 12 }}>
                            <Col style={{ width: 24 }}>
                                <svg className="iconfont" width={16} height={16} style={{ marginRight: 12, marginTop: 8 }} fill={"#165DFF"}>
                                    <use xlinkHref="#icon-xinxitishi1" ></use>
                                </svg>
                            </Col>
                            <Col style={{ width: "calc(100% - 50px)", marginTop: 6 }}>
                                {tip}
                            </Col>
                        </Row>
                        {/*<div style={{marginBottom: "10px"}}>*/}
                        {/*<svg className="iconfont" width={16} height={16} style={{ marginBottom: "-3px", marginRight:6, marginLeft:4 }}>*/}
                        {/*<use xlinkHref="#icon-xinxitishi1"></use>*/}
                        {/*</svg>*/}
                        {/*<span>*/}
                        {/*{ tip }*/}
                        {/*</span>*/}
                        {/*</div>*/}

                        <div style={{ marginBottom: 12 }}>
                            <Title name={formatMessage({ id: "drug.medicine" })}></Title>
                        </div>
                        <Table
                            className="mar-top-5"
                            pagination={false}
                            dataSource={tableData}
                            rowKey={(record: any) => record.i}
                            rowSelection={rowSelection}
                            scroll={{ y: 170 }}
                        >

                            {
                                isOpenPackage &&
                                <Table.Column
                                    title={<FormattedMessage id="drug.medicine.packlist" />}
                                    dataIndex={"packageNumber"}
                                    key="packageNumber"
                                    ellipsis
                                    align="left"
                                    render={(value, record, index) => {
                                        let newValue = ""
                                        if (value !== undefined && value !== "") {
                                            newValue = value
                                        } else {
                                            newValue = "-"
                                        }
                                        let obj = {
                                            children: newValue,
                                            // props: { rowSpan: data[index].packageNumberRowSpan }
                                            props: { rowSpan: tableData[index].packageNumberRowSpan }
                                        }
                                        return obj
                                    }}
                                />
                            }

                            {/*{*/}
                            {/*isOpenPackage ?*/}
                            {/*<Table.Column*/}
                            {/*title={formatMessage({ id: "drug.medicine.packlist" })}*/}
                            {/*dataIndex={"packageNumber"}*/}
                            {/*key="packageNumber"*/}
                            {/*ellipsis*/}
                            {/*width={150}*/}
                            {/*/>*/}
                            {/*:*/}
                            {/*null*/}
                            {/*}*/}

                            {sign === 2 ?
                                <Table.Column
                                    title={formatMessage({ id: "drug.configure.drugName" })}
                                    dataIndex={"name"}
                                    key="name"
                                    ellipsis
                                    width={180}
                                    render={(text: any, record: any, index: any) => (
                                        isRowSelected(record.i) ?
                                        <Select
                                            placeholder={formatMessage({ id: "placeholder.select.common" })}
                                            defaultValue={text}
                                            onChange={value => { record.newName = value; }}
                                            style={{ width: 140 }}
                                        >
                                            {
                                                medicineNames.map((name: any) =>
                                                    <Select.Option value={name}>{name}</Select.Option>
                                                )
                                            }
                                        </Select>
                                        :
                                        (
                                            record["name"]
                                                ?
                                                record["name"]
                                                :
                                                "-"
                                        )
                                    )
                                    }
                                />
                                :
                                <Table.Column
                                    title={formatMessage({ id: "drug.configure.drugName" })}
                                    dataIndex={"name"}
                                    key="name"
                                    ellipsis
                                    width={180}
                                    render={(text, record: any, index) => {
                                        return record["name"]
                                            ?
                                            record["name"]
                                            :
                                            "-"
                                    }}
                                />
                            }
                            <Table.Column
                                title={formatMessage({ id: "drug.list.expireDate" })}
                                dataIndex={"expirationDate"}
                                key="expirationDate"
                                ellipsis
                                render={
                                    (value) => (
                                        (value === undefined || value === null || value === "") ? '-' : value
                                    )}
                            />
                            <Table.Column
                                title={formatMessage({ id: "drug.list.batch" })}
                                dataIndex={"batchNumber"}
                                key="batchNumber"
                                ellipsis
                                render={
                                    (value) => (
                                        (value === undefined || value === null || value === "") ? '-' : value
                                    )}
                            />
                            <Table.Column
                                width={150}
                                title={formatMessage({ id: "drug.freeze.count" })}
                                dataIndex={"count"}
                                key="count"
                                ellipsis
                                render={
                                    (value) => (
                                        (value === undefined || value === null || value === "") ? '-' : value
                                    )}
                            />
                            <Table.Column
                                width={150}
                                title={formatMessage({ id: "drug.list.status" })}
                                dataIndex={"status"}
                                key="status"
                                ellipsis
                                render={(value, record, index) =>
                                    renderStatus(value)
                                }
                            />
                        </Table>
                    </div>

                    {
                        sign === 1 ?
                            <Form
                                style={{ paddingTop: 20 }}
                                labelCol={{ span: g.lang === "en" ? 4 : 2 }}
                            >
                                {
                                    selecteds != null && selecteds.length > 0 ?
                                        <>
                                            <Form.Item label={formatMessage({ id: "medicine.status.examine.confirm" })}>
                                                <Radio.Group onChange={onChangeRadio} value={radioValue}>
                                                    <Radio value={1}>{formatMessage({ id: "project.task.approvalStatus.pass" })}</Radio>
                                                    <Radio value={2}>{formatMessage({ id: "project.task.approvalStatus.reject" })}</Radio>
                                                </Radio.Group>
                                            </Form.Item>
                                            {
                                                radioValue === 2 ?
                                                    <Form.Item label={formatMessage({ id: "subject.finish.remark" })}>
                                                        <Input.TextArea
                                                            rows={2}
                                                            placeholder={formatMessage({ id: "placeholder.input.common" })}
                                                            value={remarkValue}
                                                            onChange={onChangeRemark}
                                                        />
                                                    </Form.Item>
                                                    :
                                                    null
                                            }
                                        </>
                                        :
                                        null

                                }

                            </Form>
                            :
                            null

                    }
                </Modal>


            </Spin>
        </React.Fragment>
    )
};