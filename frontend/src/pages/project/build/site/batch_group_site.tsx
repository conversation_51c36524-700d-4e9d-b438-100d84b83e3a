import React, {useEffect} from "react"
import {FormattedMessage} from "react-intl";
import {Button, Col, Form} from "antd";
import {CloseCircleFilled, PlusOutlined} from "@ant-design/icons";
import styled from "@emotion/styled";
import {BatchGroupItem} from "./batch_group_item";
import {useSafeState} from "ahooks";


interface BatchGroupInterface {
    form :any,
    batchLabel :any,
    groupLabel :any,
}

export const BatchGroupSite = (props: BatchGroupInterface) => {

    const {form, batchLabel, groupLabel} = props
    const [selectedBatchLabel, setSelectedBatchLabel] = useSafeState<any>([])

    const updateSelectedBatchLabel = () => {
        setSelectedBatchLabel(form.getFieldsValue().batchGroupAlarm?.filter((it: any) => !!it.batch).map((it: any) => it.batch) || [])
    }

    useEffect(() => {
        updateSelectedBatchLabel()
    }, [])

    return <Col>
        <Form.List name={"batchGroupAlarm"}>
            {(fields: any, {add: addx, remove}) => (<>
                {fields.map((childField: any, index: any) => (
                    <CustomCol style={{marginLeft: 0}} key={childField.key}>
                        <BatchGroupItem
                            form={form}
                            fieldName={['batchGroupAlarm', index, 'info']}
                            field={childField}
                            batchLabel={batchLabel}
                            groupLabel={groupLabel}
                            selectedBatchLabel={selectedBatchLabel}
                            onBatchLabelChange={updateSelectedBatchLabel}
                            addInfo={() => {
                                // 直接使用Form.List默认逻辑处理
                            }}
                        />
                        <CloseCircleFilled
                            className="delete-icon"
                            style={{color: "#fe5b5a"}}
                            onClick={() => {
                                remove(childField.name)
                                updateSelectedBatchLabel()
                            }}
                        />
                    </CustomCol>
                ))}
                <Button
                    block
                    type="dashed"
                    icon={<PlusOutlined />}
                    disabled={form.getFieldsValue().batchGroupAlarm?.length === batchLabel.length}
                    onClick={() => {
                        addx()
                        const data: any = form.getFieldsValue().batchGroupAlarm?.slice(0,-1) || []
                        form.setFieldsValue({...form.getFieldsValue, "batchGroupAlarm": [...data, {"info":[{}]}]})
                    }}
                >
                    <span><FormattedMessage id="common.addTo"/></span>
                </Button>
            </>)}
        </Form.List>
    </Col>

}

const CustomCol = styled(Col)`
  position: relative;
  background: #f8f9fa;
  padding: 16px;
  margin-bottom: 16px;
  .delete-icon {
    position: absolute;
    right: -3px;
    top: -3px;
  }
`;