import React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { message, Modal, Table } from "antd";
import { useFetch } from "../../../../hooks/request";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { addOrder } from "../../../../api/order";
import { getSupplyPlanDetail } from "../../../../api/supply_plan";
import { siteMedicine } from "../../../../api/medicine";


export const AddOrder = (props) => {
    const intl = useIntl()
    const { formatMessage } = intl;

    const [visible, setVisible] = useSafeState(false);
    const [planName, setPlanName] = useSafeState(null);
    const [supplyId, setSupplyId] = useSafeState(null);
    const [tableData, setTableData] = useSafeState([]);
    const [sendId, setSendId] = useSafeState(null);
    const [receiveId, setReceiveId] = useSafeState(null);
    const auth = useAuth()
    const projectId = auth.project ? auth.project.id : null;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const { runAsync: run_addOrder, loading: submitting } = useFetch(addOrder, { manual: true })
    const { runAsync: run_getSupplyPlanDetail, loading } = useFetch(getSupplyPlanDetail, { manual: true })
    const { runAsync: run_siteMedicine } = useFetch(siteMedicine, { manual: true })

    const show = (id, name, storehouseId, siteId) => {
        setSupplyId(id);
        setVisible(true);
        setPlanName(formatMessage({ id: "menu.projects.project.build.plan" }) + " : " + name);
        setSendId(storehouseId[0]);
        setReceiveId(siteId);
        //获取供应计划
        run_siteMedicine({ customerId, projectId, envId }, { siteId: siteId }).then(result => {
            let data = result.data
            if (data['siteOrder'] && data['siteOrder'].filter(item => item["receiveId"] === siteId).length > 0) {
                message.error(formatMessage({ id: "project.site.add_order_error" })).then()
            } else {
                run_getSupplyPlanDetail({ siteId, roleId: auth.project.permissions.role_id, envId: envId }).then((result) => {
                    let supplyPlan = result.data
                    setTableData(supplyPlan);
                });
            }
        })
    };

    const hide = () => {
        setVisible(false);
    };

    function stringFormat(string, list) {
        let s = string;
        for (let i = 0; i < list.length; i++) {
            s = s.replace("{" + i + "}", list[i]);
        }
        return s;
    }

    const save = () => {
        if (tableData && tableData.length) {
            if (tableData.filter(item => item.storehouseName === "").length > 0) {
                message.error(formatMessage({ id: "projects.site.no.storehouse" })).then()
                return;
            }
            if (tableData.filter(item => item.init_supply === 0).length === tableData.length) {
                message.error(formatMessage({ id: "projects.site.supplyPlan.0init_supply" })).then()
                return;
            }

            let filter = tableData.filter(item => item.count < item.init_supply);
            if (filter.length > 0) {
                let msg = formatMessage({ id: "projects.site.not.enough.medicine" });
                message.error(stringFormat(msg, [filter.map(item => item.medicine_name).join(",")])).then()
                return;
            }
            run_siteMedicine({ customerId, projectId, envId }, { siteId: receiveId }).then((result => {
                let data = result.data
                if (data['siteOrder'] && data['siteOrder'].filter(item => item["receiveId"] === receiveId).length > 0) {
                    message.error(formatMessage({ id: "project.site.add_order_error" }));
                } else {
                    run_addOrder({
                        customerId, projectId, envId,
                        sendId: sendId,
                        receiveId: receiveId,
                        drugNames: tableData.map(item => item.medicine_name),
                        drugNamesWithSalts: tableData.map(item => {
                            return { "salt": item.salt, "saltName": item.saltName }
                        }),
                        mode: 4,
                        supplyId: supplyId,
                    }).then(() => {
                        message.success(formatMessage({ id: 'common.success' }))
                        props.refresh();
                        hide();
                    });
                }
            }));
        }
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Modal
                width={600}
                title={planName}
                visible={visible}
                onCancel={hide}

                maskClosable={false}
                centered
                destroyOnClose
                className='custom-large-modal'
                okButtonProps={{ loading: submitting }}
                onOk={save}
                okText={formatMessage({ id: "common.ok" })}
            >
                <Table
                    loading={loading} width={1000} size="small"
                    dataSource={tableData}
                    pagination={(tableData || []).length > 10 ? { pageSize: 10 } : false}
                    rowKey={(record) => (record)}
                >
                    <Table.Column
                        title={<FormattedMessage id="storehouse.name" />}
                        dataIndex={"storehouseName"}
                        key="storehouseName"
                    />
                    <Table.Column
                        title={<FormattedMessage id="drug.configure.drugName" />}
                        dataIndex={"medicine_name"}
                        key="medicine_name"
                    />
                    <Table.Column
                        title={<FormattedMessage id="shipment.order.availableCount" />}
                        dataIndex={"count"}
                        key="count"
                    />
                    <Table.Column
                        title={<FormattedMessage id="projects.supplyPlan.initSupply" />}
                        dataIndex={"init_supply"}
                        key="initSupply"
                    />
                    <Table.Column
                        title={<FormattedMessage id="projects.supplyPlan.warning" />}
                        dataIndex={"warning"}
                        key="warning"
                    />
                    <Table.Column
                        title={<FormattedMessage id="projects.site.medicine" />}
                        dataIndex={"siteMedicine"}
                        key="siteMedicine"
                    />
                </Table>
            </Modal>
        </React.Fragment>
    )
};
