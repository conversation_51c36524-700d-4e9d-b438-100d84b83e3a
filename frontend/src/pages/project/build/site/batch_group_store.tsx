import React, {useEffect} from "react";
import {useIntl} from "react-intl";
import {Col, Divider, Form, Row, Select} from "antd";
import {CloseOutlined, PlusOutlined} from "@ant-design/icons";
import styled from "@emotion/styled";
import {useFetch} from "../../../../hooks/request";
import {getStoreListOption} from "../../../../api/project_site";
import {useSafeState} from "ahooks";
import {useAuth} from "../../../../context/auth";
import {BatchGroupItem} from "./batch_group_item";


interface BatchGroupInterface {
    types :any,
    form :any,
    batchLabel :any,
    groupLabel :any,
    field?:any,
    useStorehousesData?:any,
    setUseStorehousesData?:any,
}

export const BatchGroupStore = (props: BatchGroupInterface) => {
    const auth = useAuth();
    const projectId = auth.project ? auth.project.id : null;
    const customerId = auth.customerId;
    const envId = auth.env ? auth.env.id : null;
    const intl = useIntl()
    const {form, batchLabel, groupLabel, field,useStorehousesData, setUseStorehousesData} = props
    const {formatMessage} = intl;
    const [storehousesData, setStorehousesData] = useSafeState([]);
    const { runAsync: run_getStoreListOption, loading: getStoreListOptionLoading } = useFetch(getStoreListOption, { manual: true });
    const [selectedBatchLabel, setSelectedBatchLabel] = useSafeState<any>({})


    const getBatchLabel = (storehouseId: string) => {
        return batchLabel.filter((item: any)=> item.storehouseId == storehouseId).filter((item: any, index: number, self: any[]) => {
            return self.findIndex((it: any) => it.label === item.label && it.value === item.value) === index
        })
    }

    const updateSelectedBatchLabel = (storeIndex: number) => {
        const data = form.getFieldsValue().info[storeIndex].batchGroupAlarm
        const selected = data.filter((it: any) => !!it.batch).map((it: any) => it.batch) || []
        setSelectedBatchLabel( {...selectedBatchLabel, [useStorehousesData[storeIndex]]: selected})
    }

    useEffect(() => {
        const info = form.getFieldsValue().info || []
        const selectedBatchLabel = info.reduce((res: any, item: any, index: number) => {
            res[useStorehousesData[index]] = item.batchGroupAlarm?.filter((it: any) => !!it.batch).map((it: any) => it.batch) || []
            return res
        }, {})
        setSelectedBatchLabel(selectedBatchLabel)
    }, [useStorehousesData])

    const getStorehouses = () => {
        const params = {
            "projectId": projectId,
            "customerId": customerId,
            "envId": envId,
        };
        run_getStoreListOption({ ...params }).then((result:any) => {
            const storehouses = result.data
            setStorehousesData(storehouses)
            const storeInfo = form.getFieldsValue().info || []
            if (storeInfo.length === 1 && !storeInfo[0].depot && storehouses.length === 1) {
                form.setFieldsValue({"info": [{depot: storehouses[0].id, batchGroupAlarm:[{"info":[{}]}]}]})
                setUseStorehousesData([storehouses[0].id])
            }
        })
    }
    React.useEffect(() => {
        getStorehouses()
    }, [])

    const changeStore = (field: any) => {
        setUseStorehousesData(form.getFieldsValue().info?.map((it: any) => it.depot))
        const info = form.getFieldsValue().info[field.name]
        info.batchGroupAlarm.forEach((item: any, index: any)=> {
            // 以数组形式传递嵌套路径
            const fieldName = ['info', field.name, "batchGroupAlarm", index, 'batch']
            if (!info.depot || !batchLabel.find((it: any) => it.value === item.batch && info.depot == it.storehouseId)) {
                form.setFields([{name: fieldName, value: null}])
            }
        })
    }

    return <StoreCol style={{padding: "16px 16px 0 16px"}}>
        <Form.Item
            rules={[{required: true}]}
            required={false}
            name = {[field.name,"depot"]}
            label={formatMessage({id:"storehouse.name"})}
        >
            <Select
                placeholder={formatMessage({id: "storehouse.name"})} allowClear={true}
                onChange={() => changeStore(field)
            }>
                {storehousesData?.map((it:any) =>
                    <Select.Option
                        value={it.id}
                        disabled={useStorehousesData?.find((id:any)=> id === it.id)}
                    >
                        {it.name}
                    </Select.Option>
                )}
            </Select>
        </Form.Item>
        <div style={{display: 'flex'}}>
            <Form.Item className={'transparent_item'} label={formatMessage({id:"storehouse.name"})} />
            <div style={{flex: 1}} >
                <Form.List name={[field.name, "batchGroupAlarm"]}>
                    {(fields: any, {add: addx, remove}) => (
                        <>
                            {fields.map((childField: any, index: any) => (
                                <CustomCol key={childField.key}>
                                    <Row style={{display: 'flex'}}>
                                        <Col style={{display: 'flex', alignItems: 'center', padding: '12px 0'}}>
                                            <Divider
                                                type={"vertical"}
                                                style={{margin: '0 12px 0 0', borderLeft: "1px solid #E0E1E2", height: '100%'}}
                                            />
                                        </Col>
                                        <Col style={{flex: 1}}>
                                            <BatchGroupItem
                                                form={form}
                                                fieldName={['info', field.name, "batchGroupAlarm", index, 'info']}
                                                field={childField}
                                                batchLabel={getBatchLabel(useStorehousesData[field.name])}
                                                selectedBatchLabel={selectedBatchLabel[useStorehousesData[field.name]] || []}
                                                onBatchLabelChange={() => updateSelectedBatchLabel(field.name)}
                                                groupLabel={groupLabel}
                                                addInfo={() => {
                                                    // 直接使用Form.List默认逻辑处理
                                                }}
                                            />
                                        </Col>
                                    </Row>
                                    <Row style={{paddingLeft: '12px', color: 'rgba(103, 103, 103, 1)', marginTop: '8px'}}>
                                        <Col
                                            style={{cursor: "pointer", marginRight: '24px', display: 'flex', alignItems: 'center'}}
                                            onClick={() => {
                                                addx()
                                                const data: any = form.getFieldsValue().info || [{"info":[{}]}]
                                                if (data) {
                                                    data[field.name]?.batchGroupAlarm.forEach((it:any,index:number)=>{
                                                        if (!it){data[field.name].batchGroupAlarm[index] = {"info":[{}]}}})
                                                }
                                                form.setFieldsValue({ "info":[...data]})
                                            }}
                                        >
                                            <PlusOutlined style={{marginRight: '6px', fontSize: '12px'}} />
                                            <span>{formatMessage({id: 'drug.batch.management.group.add'})}</span>
                                        </Col>
                                        {
                                            fields?.length > 1 &&
                                            <Col
                                                style={{cursor: "pointer", display: 'flex', alignItems: 'center'}}
                                                onClick={() => {
                                                    remove(childField.name)
                                                    updateSelectedBatchLabel(field.name)
                                                }}
                                            >
                                                <CloseOutlined style={{marginRight: '6px', fontSize: '12px'}} />
                                                <span>{formatMessage({id: 'drug.batch.management.group.del'})}</span>
                                            </Col>
                                        }

                                    </Row>
                                </CustomCol>
                            ))}
                        </>
                    )}
                </Form.List>
            </div>
        </div>
    </StoreCol>

}

const StoreCol =  styled(Col)`
  .transparent_item {
    .ant-form-item-label > label {
      color: transparent;
      &::after {
        color: transparent;
      }
    }
  }
`

const CustomCol = styled(Col)`
  position: relative;
  background: #f8f9fa;
  padding-bottom: 16px;
`;