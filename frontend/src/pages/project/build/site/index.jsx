import {PageContextProvider} from "context/page";
import {PaginationView} from "components/pagination";
import {Main} from "./main";
import {SiteProvider} from "./context";
import {SupplyPlanMedicine} from "./medicine";

export const ProjectSite = (props) => {
    return (
        <>
            <SiteProvider>
                <PageContextProvider>
                    <Main/>
                    <PaginationView minNumberRequired={20} />
                    <SupplyPlanMedicine/>
                </PageContextProvider>
            </SiteProvider>
        </>
    )
};

