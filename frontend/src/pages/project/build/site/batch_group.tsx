import {FormattedMessage, useIntl} from "react-intl";
import {Button, Col, Divider, Form, InputNumber, Row, Select} from "antd";
import {CloseCircleFilled, CloseOutlined, MinusCircleFilled, PlusOutlined} from "@ant-design/icons";
import React from "react";
import styled from "@emotion/styled";
import {useFetch} from "../../../../hooks/request";
import {getStoreListOption} from "../../../../api/project_site";
import {useSafeState} from "ahooks";
import {useAuth} from "../../../../context/auth";


interface BatchGroupInterface {
    types :any,
    form :any,
    batchLabel :any,
    groupLabel :any,
    field?:any,
    useStorehousesData?:any,
    setUseStorehousesData?:any,
}

export const BatchGroup = (props: BatchGroupInterface) => {
    const auth = useAuth();
    const projectId = auth.project ? auth.project.id : null;
    const customerId = auth.customerId;
    const envId = auth.env ? auth.env.id : null;
    const intl = useIntl()
    const {types, form, batchLabel, groupLabel, field,useStorehousesData, setUseStorehousesData} = props
    const {formatMessage} = intl;
    const [storehousesData, setStorehousesData] = useSafeState([]);
    const { runAsync: run_getStoreListOption, loading: getStoreListOptionLoading } = useFetch(getStoreListOption, { manual: true });
    const getStorehouses = () => {
        const params = {
            "projectId": projectId,
            "customerId": customerId,
            "envId": envId,
        };
        run_getStoreListOption({ ...params }).then(
            (result:any) => {
                setStorehousesData(result.data)
            }
        )
    };
    React.useEffect(() => {
        if (types === 2) {
            getStorehouses()
        }
    }, []);

    const changeStore = (field:any) => {
        setUseStorehousesData(form.getFieldsValue().info?.map((it:any)=> it.depot))
        let data = form.getFieldsValue().info[field.name].batchGroupAlarm
        data.forEach((item:any, index:any)=>{
            if (form.getFieldsValue().info[field.name].depot) {
                const select = batchLabel.find((it:any)=> it.value === item.batch && form.getFieldsValue().info[field.name].depot == it.storehouseId)
                if (!select) {
                    form.setFields([
                        {
                            name: ['info', field.name, "info",index, "batch" ], // 以数组形式传递嵌套路径
                            value: null
                        }

                    ]);
                }
            }else{
                form.setFields([
                    {
                        name: ['info', field.name, "info",index, "batch" ], // 以数组形式传递嵌套路径
                        value: null
                    }

                ]);
            }
        })
    }

    const validator = {
        validator: (rule:any, value:any, callback:any) => {
            if (value && value > 0) {
                return Promise.resolve();
            }
            return Promise.reject("请输入正整数");

        }
    };

    return <Col>
        {
            types === 2 &&
            <Form.Item
                style={{
                    padding: "16px 16px 0px 16px",

                }}
                name = {[field.name,"depot"]}
                label={formatMessage({id:"storehouse.name"})}
        >
            <Select placeholder={formatMessage({id: "storehouse.name"})} allowClear={true} onChange={() => {
                changeStore(field)
            }}>
                {
                    storehousesData.map((it:any)=>
                        <Select.Option value={it.id} disabled={
                            useStorehousesData?.find((id:any)=>
                                id === it.id
                            )
                        }>{it.name}</Select.Option>
                    )
                }
            </Select>
        </Form.Item>
        }
        <div
        >
            <Form.List name={field? [field.name,"batchGroupAlarm"] :"batchGroupAlarm"}>
                {(fields: any, {add: addx, remove}) => (
                    <>
                        {fields.map((childField: any, index: any) => (
                            <CustomCol
                                style={{marginLeft: types === 2 ?40:0}}
                                key={childField.key}
                            >
                                <Row>
                                    {
                                        types === 2 &&
                                        <Col span={1}>
                                            <Divider type={"vertical"} style={{paddingRight:0,  borderLeft: "1px solid #E0E1E2", height:"90%"}} />
                                        </Col>
                                    }


                                <Col span={ types === 2 ?  23 : 24}>
                                    <Form.Item
                                        className={"full-width"}
                                        {...childField}
                                        name={[childField.name, "batch"]}
                                    >
                                        <Select placeholder={formatMessage({id: "drug.list.batch"})} options={
                                            types ===2 ?  batchLabel.filter((it:any)=> it.storehouseId == useStorehousesData[field.name])
                                                : batchLabel
                                        }>

                                        </Select>
                                    </Form.Item>
                                    <Form.Item name={[childField.name, "info"]}>
                                        <Form.List name={[childField.name, "info"]}>
                                            {(fields: any, {add, remove:removeChild}) => (
                                                <>
                                                    {fields.map((field: any, index: any) => (

                                                        <Row key={field.key} gutter={12}>
                                                            <Col span={7}>
                                                                <Form.Item
                                                                    {...field}
                                                                    name={[field.name, "group"]}
                                                                >
                                                                    <Select
                                                                        className={"full-width"}
                                                                        options={groupLabel}>

                                                                    </Select>
                                                                </Form.Item>
                                                            </Col>
                                                            <Col span={7}>
                                                                <Form.Item
                                                                    {...field}
                                                                    name={[field.name, "warn"]}
                                                                    rules={[validator]}
                                                                >
                                                                    <InputNumber className={"full-width"}/>
                                                                </Form.Item>
                                                            </Col>
                                                            <Col span={7}>
                                                                <Form.Item {...field}
                                                                           rules={[validator]}
                                                                           className={"full-width"}
                                                                           name={[field.name, "estimate"]}
                                                                >
                                                                    <InputNumber className={"full-width"}/>
                                                                </Form.Item>
                                                            </Col>
                                                            <Col span={3}>
                                                                <svg className="iconfont" width={16} height={16} onClick={() => {
                                                                    add()
                                                                    let data :any = form.getFieldsValue().info
                                                                    if (data) {
                                                                        data[field.name]?.batchGroupAlarm.forEach((it:any,index:number)=>{
                                                                            if (!it){data[field.name].batchGroupAlarm[index] = {"info":[{}]}}})
                                                                        form.setFieldsValue({ "info":[...data]})
                                                                    }else {
                                                                        form.setFieldsValue({ "info":[{batchGroupAlarm:[{"info":[{}]}]}]})
                                                                    }
                                                                }}
                                                                     style={{marginLeft: 12, marginTop: 10, cursor: "pointer"}}
                                                                     fill={"#999999"}>
                                                                    <use xlinkHref="#icon-zengjia"></use>
                                                                </svg>
                                                                {fields.length > 1 && (
                                                                    <svg
                                                                        onClick={() => {
                                                                            removeChild(field.name);
                                                                        }}
                                                                        className="iconfont" width={16} height={16}
                                                                        style={{marginLeft: 12, marginTop: 10, cursor: "pointer"}} fill={"#999999"}
                                                                    >
                                                                        <use xlinkHref="#icon-shanchu"></use>

                                                                    </svg>
                                                                )}
                                                            </Col>
                                                        </Row>

                                                    ))}

                                                    {
                                                        types === 2 &&
                                                    <Row>
                                                        <Col style={{marginLeft: 12, marginTop: 10, cursor: "pointer"}}
                                                             onClick={() => {
                                                                 addx()
                                                                 let data :any = form.getFieldsValue().info
                                                                 if (data) {
                                                                     data[field.name]?.batchGroupAlarm.forEach((it:any,index:number)=>{
                                                                         if (!it){data[field.name].batchGroupAlarm[index] = {"info":[{}]}}})
                                                                     form.setFieldsValue({ "info":[...data]})
                                                                 }else {
                                                                     form.setFieldsValue({ "info":[{batchGroupAlarm:[{"info":[{}]}]}]})
                                                                 }
                                                             }}
                                                        >
                                                            <PlusOutlined />
                                                            添加批次号组
                                                        </Col>
                                                        <Col style={{marginLeft: 12, marginTop: 10, cursor: "pointer"}}
                                                             onClick={() => {
                                                                 remove(childField.name)
                                                             }}
                                                        >
                                                            <CloseOutlined />
                                                            <span>删除批次号组</span>
                                                        </Col>
                                                    </Row>
                                                    }

                                                </>
                                            )}
                                        </Form.List>

                                    </Form.Item>
                                </Col>
                                </Row>

                                {
                                    types !== 2 &&
                                    // dose &&
                                    <CloseCircleFilled
                                        className="delete-icon"
                                        style={{color: "#fe5b5a"}}
                                        onClick={() =>
                                            remove(childField.name)

                                        }
                                    />
                                }
                            </CustomCol>
                        ))}
                        {

                            types !== 2 &&
                                <Button
                                    type="dashed"
                                    onClick={() => {
                                        addx()
                                        if (types === 2) {
                                            let data :any = form.getFieldsValue().info
                                            if (data) {
                                                data[field.name]?.batchGroupAlarm.forEach((it:any,index:number)=>{
                                                    if (!it){data[field.name].batchGroupAlarm[index] = {"info":[{}]}}})
                                                form.setFieldsValue({ "info":[...data]})
                                            }else {
                                                form.setFieldsValue({ "info":[{batchGroupAlarm:[{"info":[{}]}]}]})
                                            }
                                        }else {
                                            let data :any = form.getFieldsValue().batchGroupAlarm?.slice(0,-1)
                                            if (data) {
                                                form.setFieldsValue({...form.getFieldsValue, "batchGroupAlarm": [...data, {"info":[{}]}]})
                                            }else {
                                                form.setFieldsValue({...form.getFieldsValue, "batchGroupAlarm": [{"info":[{},],}]})

                                            }
                                        }


                                    }}
                                    block
                                    icon={<PlusOutlined/>}
                                >
                                    <FormattedMessage id="common.add"/>
                                </Button>
                        }
                    </>

                )}

            </Form.List>

        </div>

    </Col>

}

export const CustomCol = styled(Col)`
  position: relative;
  background: #f8f9fa;
  padding: 16px 16px 8px 16px;
  margin-bottom: 12px;
  .delete-icon {
    position: absolute;
    right: -3px;
    top: -3px;
  }
`;