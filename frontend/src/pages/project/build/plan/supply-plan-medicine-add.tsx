import React from 'react';
import {Alert, Form, Input, InputNumber, message, Modal, Radio, Row, Select, Space} from 'antd'
import {useIntl} from "react-intl";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {addSupplyMedicinePlan, getMedicineConfigures, updateSupplyMedicinePlan} from "../../../../api/supply_plan";
import {useGlobal} from '../../../../context/global';
import {QuestionCircleFilled} from "@ant-design/icons";


export const SupplyPlanMedicineAdd = (props:any) => {
    // const g = useGlobal()
    // const auth = useAuth();
    // const intl = useIntl();
    // const {formatMessage} = intl;

    // const [visible, setVisible] = useSafeState<any>(false);
    // const [form] = Form.useForm();
    // const { Option } = Select;
    // const [item, setItem] = useSafeState<any>({});
    // const [medicine, setMedicine] = useSafeState<any>([]);
    // const [planId, setPlanId] = useSafeState<any>(null);
    // const [forecast, setForecast] = useSafeState<any>(false);
    // const [buffer, setBuffer] = useSafeState<any>(false);
    // const [secondSupply, setSecondSupply] = useSafeState<any>(false);
    // const [na, setNa] = useSafeState<any>(false);
    // const [warning, setWarning] = useSafeState<any>(0);
    // const [secondSupplyValue, setSecondSupplyValue] = useSafeState<any>(0);


    // const projectId = auth.project.id;
    // const envId = auth.env ? auth.env.id : null;
    // const customerId = auth.customerId;

    // const {runAsync:updateSupplyMedicinePlanRun, loading:updateSupplyMedicineLoading} = useFetch(updateSupplyMedicinePlan, {manual: true})
    // const {runAsync: addSupplyMedicinePlanRun, loading:addSupplyMedicinePlanLoading} = useFetch(addSupplyMedicinePlan, {manual: true})
    // const {runAsync: getMedicineConfiguresRun, loading:getMedicineConfiguresLoading} = useFetch(getMedicineConfigures, {manual: true})


    // const show = (id:any, item:any) => {
    //     setPlanId(id)
    //     setNa(true)
    //     setVisible(true)
    //     if (item) {
    //         setWarning(item.buffer)
    //         setSecondSupplyValue(item.secondSupply)
    //         setItem(item)
    //         if (item.autoSupplySize?.findIndex((value:any) => value === 0 ) !== -1) {
    //             setNa(false)
    //         }
    //         if (item.autoSupplySize?.find((value:any) => value === 3 )) {
    //             setForecast(true)
    //         }
    //         if (item.autoSupplySize?.find((value:any) => value === 2 )) {
    //             setSecondSupply(true)
    //         }
    //         if (item.autoSupplySize?.find((value:any) => value === 1 )) {
    //             setBuffer(true)
    //         }
    //         form.setFieldsValue({ ...item })
    //     }else{
    //         setWarning("")
    //         setSecondSupplyValue("")
    //     }
    //     list()
    // };

    // const hide = () => {
    //     setPlanId(null)
    //     setItem({})
    //     form.resetFields()
    //     setVisible(false);
    //     setForecast(false)
    //     setBuffer(false)
    //     setSecondSupply(false)

    // };

    // const save = () => {
    //     form.validateFields().then(
    //         () => {
    //             if (item.id !== undefined) {
    //                 updateSupplyMedicinePlanRun({id:item.id}, { "info": form.getFieldsValue() }).then(
    //                     (resp:any) => {
    //                         message.success(resp.msg)
    //                         props.refresh(planId)
    //                         hide();
    //                     }
    //                 )

    //             } else {
    //                 const data = {
    //                     "projectId": projectId,
    //                     "customerId": customerId,
    //                     "envId": envId,
    //                     "supplyPlanId": planId,
    //                     "info": form.getFieldsValue()
    //                 }
    //                 addSupplyMedicinePlanRun(data).then(
    //                     (resp:any) => {
    //                         message.success(resp.msg)
    //                         props.refresh(planId)
    //                         hide();
    //                     }
    //                 )
    //             }
    //         }
    //     ).catch(() => { })
    // };

    // const list = () => {
    //     const data = {
    //         "projectId": projectId,
    //         "customerId": customerId,
    //         "envId": envId,
    //     }
    //     getMedicineConfiguresRun(data).then(
    //         (result: any) => {
    //             setMedicine(result.data != null ? result.data : [])
    //         }
    //     )
    // }

    // // eslint-disable-next-line react-hooks/exhaustive-deps
    // // React.useEffect(list, [list]);

    // React.useImperativeHandle(props.bind, () => ({ show }));

    // const formItemLayout = {
    //     labelCol: {
    //         xs: { span: 24 },
    //         sm: { span: g.lang==="zh"? 6: 9 },
    //     },
    //     wrapperCol: {
    //         xs: { span: 24 },
    //         sm: { span: g.lang==="zh"? 18: 16 },
    //     },
    // }

    // const selectAutoSupplySize = (value:any, option:any) => {
    //     if (value === 1) {
    //         form.setFieldsValue({...form.getFieldsValue, autoSupplySize:form.getFieldsValue().autoSupplySize.filter((value:any)=> value !== 2)})
    //     }
    //     if (value === 2) {
    //         form.setFieldsValue({...form.getFieldsValue, autoSupplySize:form.getFieldsValue().autoSupplySize.filter((value:any)=> value !== 1)})
    //     }
    //     if (value === 0) {
    //         form.setFieldsValue({...form.getFieldsValue, autoSupplySize:[0,]})
    //     }else{
    //         form.setFieldsValue({...form.getFieldsValue, autoSupplySize:form.getFieldsValue().autoSupplySize.filter((value:any)=> value !== 0)})
    //     }
    //     value = form.getFieldsValue().autoSupplySize
    //     changeAutoSupplySize(value,null)
    // }
    // const changeAutoSupplySize = (value:any, option:any) => {
    //     if (value.find((item:any) => item === 3)){
    //         setForecast(true)
    //     }else{
    //         setForecast(false)
    //     }
    //     if (value.find((item:any) => item === 1)){
    //         setBuffer(true)
    //     }else{
    //         setBuffer(false)
    //     }
    //     if (value.find((item:any) => item === 2)){
    //         setSecondSupply(true)
    //     }else{
    //         setSecondSupply(false)
    //     }
    //     if (value.findIndex((item:any) => item === 0) !== -1){
    //         setNa(false)
    //     }else{
    //         setNa(true)
    //     }
    // }

    // const unDistributionDateValidator = {
    //     validator: (rule: any, value: any, callback: any) => {
    //         if (value < (form.getFieldsValue().unProvideDate + form.getFieldsValue().notCountedDate)) {
    //                 return Promise.reject(formatMessage({ id: 'projects.supplyPlan.unDistributionDate.err' }));
    //         }
    //         return Promise.resolve();
    //     }
    // };

    // const warnValidator = {
    //     validator: (rule: any, value: any, callback: any) => {
    //         if (value === 0) {
    //             return Promise.reject(formatMessage({ id: 'projects.supplyPlan.unDistributionDate.err' }));
    //         }
    //         return Promise.resolve();
    //     }
    // };

    // const forecastMaxValidator = {
    //     validator: (rule :any, value :any , callback:any) => {
    //         if (value < form.getFieldsValue().forecastMin) {
    //             return Promise.reject(formatMessage({id: 'projects.supplyPlan.max.alert'}));
    //         }
    //         return Promise.resolve();
    //     }
    // };

    // return (
    //     <Modal
    //         className='custom-small-modal'
    //         // title={formatMessage({ id: 'menu.projects.project.build.drug' })}
    //         title={item.id?formatMessage({ id: 'common.edit' }):formatMessage({ id: 'common.add' })}
    //         onCancel={hide}
    //         visible={visible}
            
    //         maskClosable={false}
    //         centered
    //         destroyOnClose
    //         okText={formatMessage({id: 'common.ok'})}
    //         onOk={save}
    //     >
    //         {
    //             visible ?
    //                 <Form form={form} layout="horizontal" {...formItemLayout} >
    //                     <Form.Item
    //                         label={formatMessage({ id: 'shipment.medicine' })}
    //                         name="medicineName"
    //                         rules={[{ required: true }]}
    //                     >
    //                         <Select placeholder={formatMessage({id: 'placeholder.select.common'})}                        
    //                         onChange={(value: any) => {
    //                             console.log(value);
    //                         }}>
    //                             {medicine.map((value:any) => <Option key={value.name} value={value.name}>{value.name}</Option>)}
    //                         </Select>
    //                     </Form.Item>
    //                     {
    //                         <>
    //                             <Form.Item

    //                                 label={formatMessage({ id: 'projects.supplyPlan.initSupply' })}
    //                                 name="initSupply"
    //                                 rules={[{ required: true }]}

    //                             >
    //                                 <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" />
    //                             </Form.Item>
    //                             <Form.Item

    //                                 label={formatMessage({ id: 'projects.supplyPlan.warning' })}
    //                                 name="warning"
    //                                 rules={[{ required: true }]}
    //                             >
    //                                 <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" />
    //                             </Form.Item></>
    //                     }

    //                     <Form.Item
    //                         tooltip={{ title: <>
    //                             <Row>{formatMessage({id:"projects.supplyPlan.secondSupply.tip"})}</Row>
    //                             <Row>{formatMessage({id:"projects.supplyPlan.forecast.tip"})}</Row>
    //                             <Row>{formatMessage({id:"projects.supplyPlan.buffer.tip"})}</Row>
    //                             <Row>{formatMessage({id:"projects.supplyPlan.na.tip"})}</Row>
    //                             </>, icon: <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer",}}/>, overlayStyle: { whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto' } }}
    //                         label={formatMessage({ id: 'projects.supplyPlan.autoSupplySize' })}
    //                         name="autoSupplySize"
    //                         rules={[{ required: true }]}

    //                     >
    //                         <Select placeholder={formatMessage({id: 'placeholder.select.common'})} mode={"multiple"} onChange={changeAutoSupplySize} onSelect={(value, option)=> selectAutoSupplySize(value,option)}>
    //                             <Option value={0}>NA</Option>
    //                             <Option value={1}>{formatMessage({ id: 'shipment.mode.max' })}</Option>
    //                             <Option value={2}>{formatMessage({ id: 'shipment.mode.reSupply' })}</Option>
    //                             <Option value={3}>{formatMessage({ id: 'shipment.mode.forecast' })}</Option>
    //                         </Select>
    //                     </Form.Item>
    //                         {
    //                             !na && <Alert style={{marginLeft: g.lang==="zh" ? 138 : 207, marginBottom:20}} message={formatMessage({ id: 'projects.supplyPlan.na.alert' })}
    //                                           type="warning"
    //                                           showIcon />
    //                         }



    //                     {
    //                         na && <>
    //                             {
    //                                 buffer &&
    //                                 <Form.Item
    //                                     label={formatMessage({ id: 'projects.supplyPlan.buffer' })}
    //                                     name="buffer"
    //                                     rules={[{ required: true }]}

    //                                 >
    //                                     <InputNumber onChange={(value) => {setWarning(value)}} placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" />
    //                                 </Form.Item>
    //                             }
    //                             {
    //                                 warning === 0 && buffer && <Alert style={{marginLeft:138, marginBottom:20}} message={formatMessage({ id: 'projects.supplyPlan.order.fail' })}
    //                                                   type="warning"
    //                                                   showIcon />
    //                             }
    //                             {
    //                                 forecast &&
    //                                 <Form.Item
    //                                     label={formatMessage({ id: 'projects.supplyPlan.forecast' })}
    //                                     required={true}
    //                                     // rules={[{ required: true }]}
    //                                 >
    //                                     <Input.Group compact>
    //                                         <Form.Item style={{marginBottom:0}} name="forecastMin" rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) +formatMessage({ id: 'projects.supplyPlan.forecast' }) }]}>
    //                                             <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1}/>

    //                                         </Form.Item>
    //                                         <span style={{marginTop: 6 ,marginLeft:5, marginRight:5}}>~</span>
    //                                         <Form.Item style={{marginBottom:0}} name="forecastMax" rules={[forecastMaxValidator,{ required: true, message:formatMessage({id: 'placeholder.input.common'}) +formatMessage({ id: 'projects.supplyPlan.forecast' }) }]}>
    //                                             <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1}/>

    //                                         </Form.Item>
    //                                         <span style={{marginTop: 6 ,marginLeft:5, marginRight:5}}>{formatMessage({id:"project.overview.day"})}</span>
    //                                     </Input.Group>
    //                                 </Form.Item>
    //                             }
    //                             {
    //                                 secondSupply &&
    //                                 <>
    //                                     <Form.Item
    //                                         label={formatMessage({ id: 'projects.supplyPlan.secondSupply' })}
    //                                         name="secondSupply"
    //                                         rules={[{ required: true }]}

    //                                     >
    //                                         <InputNumber  onChange={(value) => {setSecondSupplyValue(value)}}  placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" />
    //                                     </Form.Item>
    //                                     {secondSupplyValue === 0 && <Alert style={{marginLeft: 138, marginBottom: 20}}
    //                                                                        message={formatMessage({id: 'projects.supplyPlan.order.fail'})}
    //                                                                        type="warning"
    //                                                                        showIcon/>}
    //                                 </>

    //                             }
    //                             <Form.Item
    //                                 tooltip={{ title: formatMessage({ id: 'projects.supplyPlan.unDistributionDate.tip' }), icon: <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer",}}/>, overlayStyle: { whiteSpace: 'pre-wrap', maxWidth: '400px', width: 'auto' } }}
    //                                 label={formatMessage({ id: 'projects.supplyPlan.unDistributionDate' })}
    //                                 name="unDistributionDate"
    //                                 rules={[unDistributionDateValidator]}

    //                             >
    //                                 <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" />
    //                             </Form.Item>
    //                         </>

    //                     }
    //                     <Form.Item
    //                         tooltip={{ title: formatMessage({ id: 'projects.supplyPlan.unProvideDate.tip' }), icon: <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer",}}/>, overlayStyle: { whiteSpace: 'pre-wrap', maxWidth: '400px', width: 'auto' }}}
    //                         label={formatMessage({ id: 'projects.supplyPlan.unProvideDate' })}
    //                         name="unProvideDate"
    //                     >
    //                         <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" />
    //                     </Form.Item>

    //                     {
    //                         na &&
    //                             <Form.Item
    //                                 tooltip={{ title: formatMessage({ id: 'projects.supplyPlan.notCountedDate.tip' }), icon: <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer",}}/>, overlayStyle: { whiteSpace: 'pre-wrap', maxWidth: '400px', width: 'auto' } }}
    //                                 label={formatMessage({ id: 'projects.supplyPlan.notCountedDate' })}
    //                                 name="notCountedDate"
    //                             >
    //                                 <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" />
    //                             </Form.Item>

    //                     }

    //                     <Form.Item
    //                         label={formatMessage({ id: 'projects.supplyPlan.validityReminder' })}
    //                         name="validityReminder"
    //                     >
    //                         <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})}  precision={0} min={0} step={1} className="full-width" />
    //                     </Form.Item>
    //                     {
    //                         na &&
    //                         <Form.Item
    //                             label={formatMessage({ id: 'projects.supplyPlan.supplyMode' })}
    //                             name="supplyMode"
    //                             rules={[{ required: true }]}
    //                         >
    //                             <Radio.Group>
    //                                 {
    //                                     !auth.attribute.info.blind?
    //                                         <Space direction="vertical">
    //                                             <Radio value={1}>{formatMessage({ id: 'projects.supplyPlan.allSupply' })}</Radio>
    //                                             <Radio value={2}>{formatMessage({ id: 'projects.supplyPlan.singleSupply' })}</Radio>
    //                                         </Space>
    //                                         :
    //                                         <Space direction="vertical">

    //                                             <Radio value={1}>{formatMessage({ id: 'projects.supplyPlan.allSupply' })}</Radio>
    //                                             <Radio value={3}>{formatMessage({ id: 'projects.supplyPlan.allSupplyAndMedicine' })}</Radio>
    //                                             <Radio value={4}>{formatMessage({ id: 'projects.supplyPlan.singleSupplyAndMedicine' })}</Radio>
    //                                         </Space>
    //                                 }

    //                             </Radio.Group>
    //                         </Form.Item>
    //                     }

    //                 </Form>

    //                 :
    //                 null
    //         }
    //     </Modal>
    // )

}