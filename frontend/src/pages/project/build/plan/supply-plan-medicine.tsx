import React, {useState} from 'react';
import {
    <PERSON><PERSON>,
    Col,
    Drawer,
    Form,
    Input,
    InputNumber,
    message,
    Row,
    Select,
    Spin,
    Table,
    Tooltip,
    Typography
} from "antd";
import {FormattedMessage, useIntl} from "react-intl";
import {permissions} from "../../../../tools/permission";
import {useAuth} from "../../../../context/auth";
import {useProjectPlan} from "./context";
import {useFetch} from "../../../../hooks/request";
import {batchSupplyMedicinePlan, getMedicineConfigures, getSupplyPlanMedicineList} from "../../../../api/supply_plan";
import {useSafeState} from "ahooks"
import styled from "@emotion/styled";
import {useGlobal} from '../../../../context/global';
import {PlusOutlined, QuestionCircleFilled} from "@ant-design/icons";


export const SupplyPlanMedicine = (props:any) => {
    const g = useGlobal()
    const [form] = Form.useForm();

    const auth = useAuth();
    const ctx = useProjectPlan();
    const intl = useIntl();
    const {formatMessage} = intl;

    const [height, setHeight] = useSafeState<any>(window.innerHeight - 232);

    const [visible, setVisible] = useSafeState<any>(false);
    const [planId, setPlanId] = useSafeState<any>(null);
    const [data, setData] = useState<any>([]);
    const [title, setTitle] = useSafeState<any>("");
    const projectStatus = auth.project.status ? auth.project.status : 0;

    const permission = auth.project.permissions;

    const [editingKey, setEditingKey] = useState(false);

    const [deleteId, setDeleteId] = useState("");

    const [medicine, setMedicine] = useSafeState<any>([]);

    const [forecast, setForecast] = useSafeState(new Map());
    const [buffer, setBuffer] = useSafeState(new Map());
    const [secondSupply, setSecondSupply] = useSafeState(new Map());
    const [na, setNa] = useSafeState(new Map());
    const [warning, setWarning] = useSafeState(new Map());
    const [secondSupplyValue, setSecondSupplyValue] = useSafeState(new Map());
    const [addLoading, setAddLoading] = useSafeState<any>(false);

    const [medicineNameValue, setMedicineNameValue] = useState(new Map());
    const [medicinesBlind, setMedicinesBlind] = useState(new Map());
    const [autoSupplySizeValues, setAutoSupplySizeValues] = useState(new Map());
    const [supplyModeValue, setSupplyModeValue] = useState(new Map());
    

    const [result, setResult] = useSafeState(new Map());

    const isForecast = (index:any) => forecast.has(index) && forecast.get(index) === true;
    const isBuffer = (index:any) => buffer.has(index) && buffer.get(index) === true;
    const isSecondSupply = (index:any) => secondSupply.has(index) && secondSupply.get(index) === true;
    const isNa = (index:any) => !na.has(index) || na.get(index) === true;
    const isWarning = (index:any) => warning.has(index) && warning.get(index) === 0;
    const isSecondSupplyValue = (index:any) => secondSupplyValue.has(index) && secondSupplyValue.get(index) === 0;

    const [editLoading, setEditLoading] = useSafeState<any>(false);


    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;


    const {runAsync:getSupplyPlanMedicineListRun, loading:getSupplyPlanMedicineListLoading} = useFetch(getSupplyPlanMedicineList, {manual: true})

    const {runAsync: getMedicineConfiguresRun, loading:getMedicineConfiguresLoading} = useFetch(getMedicineConfigures, {manual: true})

    const {runAsync: batchSupplyMedicinePlanRun, loading:batchSupplyMedicinePlanLoading} = useFetch(batchSupplyMedicinePlan, {manual: true})

    const show = (item:any) => {
        setPlanId(item.id)
        setTitle(item.name)
        getMedicine(item.id)
        list()
        setVisible(true)
    }
    const hide = () => {
        setPlanId(null)
        setTitle("")
        setData([])
        setVisible(false);
        form.resetFields()
        setForecast(new Map())
        setBuffer(new Map())
        setSecondSupply(new Map())
        cancelRowHide()
        setDeleteId("")
    };

    const list = () => {
        const data = {
            "projectId": projectId,
            "customerId": customerId,
            "envId": envId,
        }
        getMedicineConfiguresRun(data).then(
            (result: any) => {
                setMedicine(result.data != null ? result.data : [])
                if (result.data != null){
                    const medicinesBlind = new Map();
                    result.data.forEach((value:any) => {
                        medicinesBlind.set(value.name, value.isBlind)
                    })
                    setMedicinesBlind(medicinesBlind)
                }
            }
        )
    }


    const getMedicine = (id:any) => {
        setData([]);
        getSupplyPlanMedicineListRun({id:id,roleId:auth.project.permissions.role_id}).then(
            (result:any) => {
                let ret = convect(result.data === null ? [] : result.data)
                ret.map((item: any) => {
                    // 修改属性值
                    if(item.supplyMode === 0){
                        item.supplyMode = null;
                    }
                    return item;
                });
                setData(ret)
                if(result.data === null){
                    setData([]);
                    addMedicine();
                    setEditingKey(true);
                }
            }
        )
    }
    const handleResize = () => {
        setHeight(window.innerHeight - 232);
    };
    React.useEffect(
        () => {
            // 监听
            window.addEventListener("resize", handleResize);
            // 销毁
            return () => window.removeEventListener("resize", handleResize);
        }
    );

    const convect = (data:any) => {
        let ret :any = []
        data.forEach((value:any) => {
            ret.push({...value.info, "id": value.id, "supplyPlanId": value.supplyPlanId})
        })
        return ret
    }

    const addMedicine = () => {
        let formData = form.getFieldsValue();
        let dt = data;
        const newRow = {
            // 这里根据你的需求，设置新行的数据
            // 例如：id, name, age 等字段
            medicineName: null,
            isBlind:null,
            warning: null,
            buffer: null,
            forecastMin: null,
            forecastMax: null,
            secondSupply: null,
            initSupply: null,
            unDistributionDate: null,
            notCountedDate: null,
            unProvideDate: null,
            validityReminder: null,
            autoSupply: null,
            autoSupplySize: [],
            supplyMode: null,
            id: null,
            supplyPlanId: null,
        };
        setData([...data, newRow]);
        dt.push(newRow)
        delete formData["medicineName" + (dt.length-1)];
        delete formData["isBlind" + (dt.length-1)];
        delete formData["autoSupplySize" + (dt.length-1)];
        delete formData["initSupply" + (dt.length-1)];
        delete formData["warning" + (dt.length-1)];
        delete formData["buffer" + (dt.length-1)];
        delete formData["secondSupply" + (dt.length-1)];
        delete formData["unDistributionDate" + (dt.length-1)];
        delete formData["unProvideDate" + (dt.length-1)];
        delete formData["notCountedDate" + (dt.length-1)];
        delete formData["validityReminder" + (dt.length-1)];
        delete formData["forecastMin" + (dt.length-1)];
        delete formData["forecastMax" + (dt.length-1)];
        delete formData["supplyMode" + (dt.length-1)];
        editRow(dt, "add")
        
    };


    const editRow = (recordArr: any, t: any) => {
        let formData = form.getFieldsValue();
        if(t === null){
            if(recordArr !== null && recordArr.length > 0){
                setEditingKey(true);
                for (let i = 0; i < recordArr.length; i++) {
                    const record = recordArr[i];
                    setVisible(true);
                    updateField("warning", i, record.buffer)
                    updateField("secondSupplyValue", i, record.secondSupply)
                    updateField("na", i, true)
                    if (record.autoSupplySize?.findIndex((value:any) => value === 0 ) !== -1) {
                        updateField("na", i, false)
                    }
                    if (record.autoSupplySize?.find((value:any) => value === 3 )) {
                        updateField("forecast", i, true)
                    }
                    if (record.autoSupplySize?.find((value:any) => value === 2 )) {
                        updateField("secondSupply", i, true)
                    }
                    if (record.autoSupplySize?.find((value:any) => value === 1 )) {
                        updateField("buffer", i, true)
                    }
                    formData["medicineName" + i] = record.medicineName
                    formData["isBlind" + i] = record.isBlind
                    formData["autoSupplySize" + i] = record.autoSupplySize
                    formData["initSupply" + i] = record.initSupply
                    formData["warning" + i] = record.warning
                    formData["buffer" + i] = record.buffer
                    formData["dispensingAlarm" + i] = record.dispensingAlarm
                    formData["secondSupply" + i] = record.secondSupply
                    formData["unDistributionDate" + i] = record.unDistributionDate
                    formData["unProvideDate" + i] = record.unProvideDate
                    formData["notCountedDate" + i] = record.notCountedDate
                    formData["validityReminder" + i] = record.validityReminder
                    formData["forecastMin" + i] = record.forecastMin
                    formData["forecastMax" + i] = record.forecastMax
                    formData["supplyMode" + i] = record.supplyMode 
                }
                form.setFieldsValue(formData);
            }else{
                setWarning(new Map())
                setSecondSupplyValue(new Map())
            }
        }else if(t === "add"){
            if(recordArr !== null && recordArr.length > 0){
                setEditingKey(true);
                for (let i = 0; i < recordArr.length; i++) {
                    const record = recordArr[i];
                    setVisible(true)
                    updateField("warning", i, record.warning)
                    updateField("secondSupplyValue", i, record.secondSupply)
                    updateField("na", i, true)
                    if (record.autoSupplySize?.findIndex((value:any) => value === 0 ) !== -1) {
                        updateField("na", i, false)
                    }
                    if (record.autoSupplySize?.find((value:any) => value === 3 )) {
                        updateField("forecast", i, true)
                    }
                    if (record.autoSupplySize?.find((value:any) => value === 2 )) {
                        updateField("secondSupply", i, true)
                    }
                    if (record.autoSupplySize?.find((value:any) => value === 1 )) {
                        updateField("buffer", i, true)
                    }
                    if(formData["autoSupplySize" + i] == 0){
                        updateField("supplyModeValue", i, 0)
                        formData["supplyMode" + i] = "-";
                        updateField("na", i, false)
                    }
                    // else{
                    //     formData["supplyMode" + i] = null;
                    //     updateField("na", i, true)
                    //     updateField("supplyModeValue", i, null)
                    // }
                    // updateField("supplyModeValue", i, formData.getFieldsValue("supplyModeValue" + i))
                    // // // let formData = form.getFieldsValue();
                    // formData["supplyMode" + i] = null;
                }
                form.setFieldsValue(formData);
            }else{
                setWarning(new Map())
                setSecondSupplyValue(new Map())
            }
        }else {
            if(recordArr !== null && recordArr.length > 0){
                setEditingKey(true);
                for (let i = 0; i < recordArr.length+1; i++) {
                    if(i < t){
                        setVisible(true)
                        updateField("warning", i, formData["warning" + i])
                        updateField("secondSupplyValue", i, formData["secondSupplyValue" + i])
                        updateField("na", i, true)
                        if (formData["autoSupplySize" + i]?.findIndex((value:any) => value === 0 ) !== -1) {
                            updateField("na", i, false)
                        }
                        if (formData["autoSupplySize" + i]?.find((value:any) => value === 3 )) {
                            updateField("forecast", i, true)
                        }
                        if (formData["autoSupplySize" + i]?.find((value:any) => value === 2 )) {
                            updateField("secondSupply", i, true)
                        }
                        if (formData["autoSupplySize" + i]?.find((value:any) => value === 1 )) {
                            updateField("buffer", i, true)
                        }
                    }else if(i >= t){
                        setVisible(true)
                        updateField("warning", i, formData["warning" + (i+1)])
                        updateField("secondSupplyValue", i, formData["secondSupplyValue" + (i+1)])
                        // updateField("na", i, true)
                        if (formData["autoSupplySize" + (i+1)]?.findIndex((value:any) => value === 0 ) !== -1) {
                            updateField("na", i, false)
                        }
                        if (formData["autoSupplySize" + (i+1)]?.find((value:any) => value === 3 )) {
                            updateField("forecast", i, true)
                        }
                        if (formData["autoSupplySize" + (i+1)]?.find((value:any) => value === 2 )) {
                            updateField("secondSupply", i, true)
                        }
                        if (formData["autoSupplySize" + (i+1)]?.find((value:any) => value === 1 )) {
                            updateField("buffer", i, true)
                        }
                        formData["medicineName" + i] = formData["medicineName" + (i+1)]
                        formData["isBlind" + i] = formData["isBlind" + (i+1)]
                        formData["autoSupplySize" + i] = formData["autoSupplySize" + (i+1)]
                        formData["initSupply" + i] = formData["initSupply" + (i+1)]
                        formData["warning" + i] = formData["warning" + (i+1)]
                        formData["buffer" + i] = formData["buffer" + (i+1)]
                        formData["dispensingAlarm" + i] = formData["dispensingAlarm" + (i+1)]
                        formData["secondSupply" + i] = formData["secondSupply" + (i+1)]
                        formData["unDistributionDate" + i] = formData["unDistributionDate" + (i+1)]
                        formData["unProvideDate" + i] = formData["unProvideDate" + (i+1)]
                        formData["notCountedDate" + i] = formData["notCountedDate" + (i+1)]
                        formData["validityReminder" + i] = formData["validityReminder" + (i+1)]
                        formData["forecastMin" + i] = formData["forecastMin" + (i+1)]
                        formData["forecastMax" + i] = formData["forecastMax" + (i+1)]
                        formData["supplyMode" + i] = formData["supplyMode" + (i+1)]
                    }
                }
                form.setFieldsValue(formData);
            }else{
                setWarning(new Map())
                setSecondSupplyValue(new Map())
            }
        }
        
    };

    const saveRow = async (recordArr: any) => {
        // setEditLoading(true)
        // 保存编辑后的数据
        // 更新data状态变量
        // 清空editingKey状态变量
        form.validateFields().then(
            (values) => {
                setEditLoading(true)
                if(recordArr !== null && recordArr.length > 0){
                    const dataArr = []
                    for (let i = 0; i < recordArr.length; i++) {
                        const info = {
                            "medicineName": form.getFieldsValue()["medicineName" + i],
                            "isBlind":form.getFieldsValue()["isBlind" + i],
                            "autoSupplySize":  form.getFieldsValue()["autoSupplySize" + i],
                            "initSupply":  form.getFieldsValue()["initSupply" + i],
                            "warning":  form.getFieldsValue()["warning" + i],
                            "buffer":  form.getFieldsValue()["buffer" + i],
                            "dispensingAlarm":  form.getFieldsValue()["dispensingAlarm" + i],
                            "secondSupply":  form.getFieldsValue()["secondSupply" + i],
                            "unDistributionDate":  form.getFieldsValue()["unDistributionDate" + i],
                            "unProvideDate":  form.getFieldsValue()["unProvideDate" + i],
                            "notCountedDate":  form.getFieldsValue()["notCountedDate" + i],
                            "validityReminder":  form.getFieldsValue()["validityReminder" + i],
                            "forecastMin":  form.getFieldsValue()["forecastMin" + i],
                            "forecastMax":  form.getFieldsValue()["forecastMax" + i],
                            "supplyMode":  form.getFieldsValue()["supplyMode" + i]
                        }
                        const data = {
                            "id": recordArr[i].id,
                            "projectId": projectId,
                            "customerId": customerId,
                            "envId": envId,
                            "supplyPlanId": planId,
                            "info": info
                        }
                        dataArr.push(data);
                    }
                    batchSupplyMedicinePlanRun({deleteIds:deleteId},dataArr).then(
                        (resp:any) => {
                            setEditLoading(false)
                            if(resp.data === null || resp.data.length === 0){
                                cancelRowHide();
                                list()
                                getMedicine(planId)
                                setDeleteId("")
                                message.success(resp.msg)
                            }else{
                                resp.data.forEach((element: any) => {
                                    updateField("result", element.index, element)
                                });
                            }
                        }
                    )
                }
            }
        ).catch(() => { })
    };

    const cancelRowHide = () => {
        setNa(new Map())
        form.resetFields()
        setForecast(new Map())
        setBuffer(new Map())
        setSecondSupply(new Map())
        setEditingKey(false);
        setResult(new Map());
        setDeleteId("")
    };
    
    const cancelRow = () => {
        // const newData = data.filter((item: any) => item.id !== null);
        // setData(newData);
        list();
        getMedicine(planId);
        // setData([])
        setNa(new Map());
        form.resetFields();
        setForecast(new Map());
        setBuffer(new Map());
        setSecondSupply(new Map());
        setEditingKey(false);
        setResult(new Map());
        setDeleteId("")
    };
      
    const deleteRow = (record: any, index:any) => {
        let formData = form.getFieldsValue();
        // 删除行数据
        // 更新data状态变量
        let newData = data;
        if(record.id !== null){
            if(deleteId.length !== 0){
                setDeleteId(deleteId  + "," + record.id)
            }else{
                setDeleteId(record.id) 
            }
            newData = data.filter((item: any) => item.id !== record.id);
            setData(newData)
            editRow(newData, index)
        }else{
            newData = data.filter((item: any, i: any) => i !== index);
            setData(newData)
            editRow(newData, index)
        }
    };

    const EditableCell = ({
        editing,
        dataIndex,
        title,
        inputType,
        record,
        index,
        children,
        ...restProps
      }: any) => {
        const inputNode = inputType === 'number' ? <InputNumber /> : <Input />;
        return (
          <td {...restProps}>
            {editing ? (
              <Form.Item
                name={dataIndex}
                style={{ margin: 0 }}
                rules={[
                  {
                    required: true,
                    message: `Please Input ${title}!`,
                  },
                ]}
              >
                {inputNode}
              </Form.Item>
            ) : (
              children
            )}
          </td>
        );
    };



    const forecastMaxValidator = (index: any) => ({
        validator: (rule :any, value :any , callback:any) => {
          if (value < form.getFieldsValue()["forecastMin" + index]) {
            return Promise.reject(formatMessage({id: 'projects.supplyPlan.max.alert'}));
          }
          return Promise.resolve();
        }
    });
      

    const unDistributionDateValidator = (index: any) => ({
        validator: (rule: any, value: any, callback: any) => {
            if (value < (form.getFieldsValue()["unProvideDate" + index] + form.getFieldsValue()["notCountedDate" + index]) && isNa(index)) {
                    return Promise.reject(formatMessage({ id: 'projects.supplyPlan.unDistributionDate.err' }));
            }
            return Promise.resolve();
        }
    });

    const selectAutoSupplySize = (value:any, option:any, index: any) => {
        let formData = form.getFieldsValue();
        if (value === 1) {
            const updatedAutoSupplySize = form.getFieldsValue()["autoSupplySize" + index].filter((value: any) => value !== 2);
            formData["autoSupplySize" + index] = updatedAutoSupplySize;
            form.setFieldsValue(formData);
        }
        if (value === 2) {
            const updatedAutoSupplySize = form.getFieldsValue()["autoSupplySize" + index].filter((value: any) => value !== 1);
            formData["autoSupplySize" + index] = updatedAutoSupplySize;
            form.setFieldsValue(formData);
        }
        if (value === 0) {
            const updatedAutoSupplySize = [0,];
            formData["autoSupplySize" + index] = updatedAutoSupplySize;
            form.setFieldsValue(formData);
        }else{
            const updatedAutoSupplySize = form.getFieldsValue()["autoSupplySize" + index].filter((value: any) => value !== 0);
            formData["autoSupplySize" + index] = updatedAutoSupplySize;
            form.setFieldsValue(formData);
        }
        value = formData["autoSupplySize" + index]
        changeAutoSupplySize(index)(value, null);
        // changeAutoSupplySize(value,null);
        updateField("supplyModeValue", index, null)
        // let formData = form.getFieldsValue();
        formData["supplyMode" + index] = null;
        form.setFieldsValue(formData);
    }

    
    const changeMedicineName = (index: any) => (value: any, option: any) => {
        let formData = form.getFieldsValue();
        if (value !== medicineNameValue) {
            if(result.has(index) && result.get(index).medicineName){
                var medicineValue = result.get(index)
                medicineValue.medicineName = ""
                medicineValue.isBlind = null
                updateField("result", index, medicineValue)
            }
        }
        updateField("medicineNameValue", index, value)

        

        //更新列表记录上的药物盲态
        var medicineData = data[index]
        let medicineName = medicineData.medicineName
        medicineData.isBlind = medicinesBlind.get(value)
        medicineData.medicineName = value       
        data[index] =  medicineData
        if (medicinesBlind.get(medicineName) !== medicinesBlind.get(value) && formData["supplyMode" + index] !== 1){
            value = formData["autoSupplySize" + index]
            changeAutoSupplySize(index)(value, null);
            updateField("supplyModeValue", index, null)
            formData["supplyMode" + index] = null;
            form.setFieldsValue(formData);
        }
    }

    

    const changeAutoSupplySize = (index: any) => (value: any, option: any) => {
        if (value.find((item:any) => item === 3)){
            updateField("forecast", index, true)
        }else{
            updateField("forecast", index, false)
        }
        if (value.find((item:any) => item === 1)){
            updateField("buffer", index, true)
        }else{
            updateField("buffer", index, false)
        }
        if (value.find((item:any) => item === 2)){
            updateField("secondSupply", index, true)
        }else{
            updateField("secondSupply", index, false)
        }
        if (value.findIndex((item:any) => item === 0) !== -1){
            updateField("na", index, false)
        }else{
            updateField("na", index, true)
        }

        if (value !== autoSupplySizeValues) {
            if(result.has(index) && result.get(index).autoSupplySize){
                var medicineValue = result.get(index)
                medicineValue.autoSupplySize = ""
                updateField("result", index, medicineValue)
            }
        }
        updateField("autoSupplySizeValues", index, value)
    }

    const changeSupplyMode = (index: any) => (value: any, option: any) => {
        if (value !== supplyModeValue) {
            if(result.has(index) && result.get(index).supplyMode){
                var medicineValue = result.get(index)
                medicineValue.supplyMode = ""
                updateField("result", index, medicineValue)
            }
        }
        updateField("supplyModeValue", index, value)
    }

    const updateField = (type: any, index: any, newValue: any) => {
        if(type === "na"){
            setNa((prevNa) => {
                const newNa = new Map(prevNa);
                newNa.set(index, newValue);
                return newNa;
            });
        }else if(type === "buffer"){
            setBuffer((prevBuffer) => {
                const newBuffer = new Map(prevBuffer);
                newBuffer.set(index, newValue);
                return newBuffer;
            });
        }else if(type === "forecast"){
            setForecast((prevForecast) => {
                const newForecast = new Map(prevForecast);
                newForecast.set(index, newValue);
                return newForecast;
            });
        }else if(type === "secondSupply"){
            setSecondSupply((prevSecondSupply) => {
                const newSecondSupply = new Map(prevSecondSupply);
                newSecondSupply.set(index, newValue);
                return newSecondSupply;
            });
        }else if(type === "warning"){
            if (newValue === 0 || warning.get(index) === 0){
                setWarning((prevWarning) => {
                    const newWarning = new Map(prevWarning);
                    newWarning.set(index, newValue);
                    return newWarning;
                });
            }else {
                warning.set(index, newValue);
                setWarning(warning)
            }
        }else if(type === "result"){
            setResult((prevResult) => {
                const newResult = new Map(prevResult);
                newResult.set(index, newValue);
                return newResult;
            });

        }else if(type === "autoSupplySizeValues"){
            setAutoSupplySizeValues((prevSelectValues) => {
                const newSelectValues = new Map(prevSelectValues);
                newSelectValues.set(index, newValue);
                return newSelectValues;
            });
        }else if(type === "medicineNameValue"){
            setMedicineNameValue((prevMedicineNameValue) => {
                const newMedicineNameValue = new Map(prevMedicineNameValue);
                newMedicineNameValue.set(index, newValue);
                return newMedicineNameValue;
            });
        }else if(type === "supplyModeValue"){
            setSupplyModeValue((prevSupplyModeValue) => {
                const newSupplyModeValue = new Map(prevSupplyModeValue);
                newSupplyModeValue.set(index, newValue);
                return newSupplyModeValue;
            });
        }else if(type === "secondSupplyValue"){
            if (newValue === 0 || secondSupplyValue.get(index) === 0){
                setSecondSupplyValue((prevSecondSupplyValue) => {
                    const newSecondSupplyValue = new Map(prevSecondSupplyValue);
                    newSecondSupplyValue.set(index, newValue);
                    return newSecondSupplyValue;
                });
            }else {
                secondSupplyValue.set(index, newValue);
                setSecondSupplyValue(secondSupplyValue)
            }

        }

        
        
    };

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 6: 9 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 18: 16 },
        },
    }

    React.useImperativeHandle(props.bind, () => ({show}));

    return <CustomDrawer
        title={<div style={{ outline: 'none', border: 'none', backgroundColor: '#F9FAFB' }}>{formatMessage({id: 'menu.projects.project.build.plan'}) + "-" + title}</div>}
        // title={formatMessage({id: 'menu.projects.project.build.plan'}) + "-" + title}
        width={"100%"}
        onClose={hide}
        visible={visible}
        
        maskClosable={false}
        headerStyle={{ backgroundColor: '#F9FAFB', border: 'none', outline: 'none' }}
        footer={
            editingKey?
            (
                permissions(permission, "operation.build.supply-plan.medicine.edit") && projectStatus !== 2 ?
                <div style={{ textAlign: 'right' }}>
                    {
                        data.length > 0 && data[0].id !== null?
                        <Button  onClick={() => cancelRow()}>
                            {formatMessage({id: 'common.cancel'})}
                        </Button>:null
                    }
                    <Button type="primary" loading={editLoading} onClick={() => saveRow(data)} style={{marginLeft:"8px"}} >
                        {formatMessage({id: 'common.save'})}
                    </Button>
                </div>:null
            ):
            (

                permissions(permission, "operation.build.supply-plan.medicine.edit") && projectStatus !== 2 ?
                <div style={{ textAlign: 'right' }}>
                    <Button type="primary" loading={editLoading} disabled={editingKey} onClick={() => editRow(data, null)}>
                        {formatMessage({id: 'common.edit'})}
                    </Button>
                </div>:null
            )
        }
    >
        <Spin spinning={getSupplyPlanMedicineListLoading}>
            {/* {permissions(permission, "operation.build.supply-plan.medicine.add") && projectStatus !== 2 &&
                <Row justify='end' style={{marginTop: 6, marginBottom: 16, marginRight:14}}>
                    <Button type='primary' onClick={() => addMedicine()}>{formatMessage({id: 'common.add'})}</Button>
                </Row>
            } */}
            <Form form={form} component={false} >
            <Table
                size="small"
                dataSource={data}
                rowKey={(record:any,index:any) => (index)}
                pagination={false}
                scroll={{y: height}}
                style={{marginLeft:14,marginRight:14}}
                className='custom-table'
                components={{
                    body: {
                        cell: EditableCell,
                        // cell: (props: any) => <EditableCell {...props} title={title} />,
                    },
                }}
            >
                <Table.Column width={70} title={<FormattedMessage id="common.serial"/>} dataIndex="#" key="#"
                              render={(value, record, index) => (index + 1)}/>
                <Table.Column width={150}
                              title={
                                <span>
                                    {
                                        editingKey?
                                        <span style={{ color: 'red', display: 'inline-block', verticalAlign: 'middle' , marginTop: "2px", marginRight: "5px"}} >*</span>:
                                        null
                                    }
                                    {formatMessage({id: 'shipment.medicine'})}
                                </span>
                              }
                              dataIndex="medicineName"
                              key="medicineName" ellipsis={!editingKey}
                              render={(value, record, index) => {
                                    const fieldName = `medicineName${index}`;
                                    return editingKey ? (
                                        // 显示编辑框
                                        <span>
                                            <Form.Item
                                                // name="medicineName"
                                                name={"medicineName" + index}
                                                rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'shipment.medicine'}) }]}
                                            >
                                                <Select  value={medicineNameValue} onChange={changeMedicineName(index)} placeholder={formatMessage({id: 'placeholder.select.common'})} style={{width: "114px",  marginTop:"14px"}}>
                                                    {medicine.map((me:any) => <Select.Option key={me.name} value={me.name} >{me.name}</Select.Option>)}
                                                </Select>
                                            </Form.Item>
                                            {
                                                result.has(index) && result.get(index).medicineName &&
                                                <Col 
                                                    style={{
                                                        color: "#F96964", 
                                                        fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                        fontSize: "12px", 
                                                        fontWeight: 400, 
                                                        lineHeight: "17px", 
                                                        letterSpacing: "0px", 
                                                        marginTop: "-10px",
                                                    }}
                                                >
                                                    {
                                                        result.get(index).medicineName
                                                    }
                                                </Col>
                                            }
                                        </span>
                                    ) : (
                                      // 显示文本
                                      value
                                    );
                                }}
                />
                <Table.Column width={g.lang==="zh"?170:180}
                    title={
                        <span>
                            {
                                editingKey ?
                                <span style={{ color: 'red', display: 'inline-block', verticalAlign: 'middle' , marginTop: "2px", marginRight: "5px"}} >*</span>:
                                null
                            }
                                <span>
                                    {formatMessage({id: 'projects.supplyPlan.autoSupplySize'})}
                                </span>
                                <span style={{marginLeft:"4px"}}>
                                    <Tooltip title={
                                            <>
                                            <Row>{formatMessage({id:"projects.supplyPlan.secondSupply.tip"})}</Row>
                                            <Row>{formatMessage({id:"projects.supplyPlan.forecast.tip"})}</Row>
                                            <Row>{formatMessage({id:"projects.supplyPlan.buffer.tip"})}</Row>
                                            <Row>{formatMessage({id:"projects.supplyPlan.na.tip"})}</Row>
                                            </>
                                        }
                                        overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                    >
                                        <span>
                                            <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                        </span>
                                    </Tooltip>
                                </span>
                        </span>
                      }
                              dataIndex="autoSupplySize" key="autoSupplySize"
                              render={(array: any, record: any, index: any) => {
                                let data = []
                                if (array?.findIndex((value:any)=> value === 0) !== -1 ) {
                                    data.push(<Row>NA</Row>)
                                }
                                if (array?.find((value:any)=> value === 1)) {
                                    data.push(<Row>{formatMessage({id: 'shipment.mode.max'})}</Row>)
                                }
                                if (array?.find((value:any)=> value === 2)){
                                    data.push(<Row>{formatMessage({id: 'shipment.mode.reSupply'})}</Row>)
                                }
                                if (array?.find((value:any)=> value === 3)) {
                                    data.push(<Row>{formatMessage({id: 'shipment.mode.forecast'})}</Row>)
                                }
                                return editingKey ? (
                                    // 显示编辑框
                                    <span>
                                        <Form.Item
                                            name={"autoSupplySize" + index}
                                            rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'projects.supplyPlan.autoSupplySize'}) }]}
                                        >
                                            <Select showArrow={true} showSearch={false} placeholder={formatMessage({id: 'placeholder.select.common'})} mode={"multiple"} value={autoSupplySizeValues} onChange={changeAutoSupplySize(index)} onSelect={(value, option)=> selectAutoSupplySize(value,option,index)} style={{marginTop:"14px"}}>
                                                <Select.Option value={0}>NA</Select.Option>
                                                <Select.Option value={1}>{formatMessage({ id: 'shipment.mode.max' })}</Select.Option>
                                                <Select.Option value={2}>{formatMessage({ id: 'shipment.mode.reSupply' })}</Select.Option>
                                                <Select.Option value={3}>{formatMessage({ id: 'shipment.mode.forecast' })}</Select.Option>
                                            </Select>
                                        </Form.Item>
                                        {
                                            !isNa(index) && 
                                            <Col 
                                                style={{
                                                    color: "#FFAE00",
                                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                    fontSize: "12px",
                                                    fontWeight: 400,
                                                    lineHeight: "17px",
                                                    letterSpacing: "0px",
                                                    marginTop: "-10px",
                                                    marginBottom:10,
                                                }}
                                            >
                                                {formatMessage({ id: 'projects.supplyPlan.na.alert' })}
                                            </Col>
                                        }
                                        {
                                            result.has(index) && result.get(index).autoSupplySize &&
                                            <Col 
                                                style={{
                                                    color: "#F96964", 
                                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                    fontSize: "12px", 
                                                    fontWeight: 400, 
                                                    lineHeight: "17px", 
                                                    letterSpacing: "0px", 
                                                    marginTop: "-10px",
                                                }}
                                            >
                                                {
                                                    result.get(index).autoSupplySize
                                                }
                                            </Col>
                                        }
                                    </span>
                                ) : (
                                  // 显示文本
                                    <>{data.map(value => value)}</>
                                );
                            }}  
                />
                <Table.Column width={g.lang==="zh"?110:230}
                              title={
                                <span>
                                    {
                                        editingKey?
                                        <span style={{ color: 'red', display: 'inline-block', verticalAlign: 'middle' , marginTop: "2px", marginRight: "5px"}} >*</span>:
                                        null
                                    }
                                    {formatMessage({id: 'projects.supplyPlan.initSupply'})}
                                </span>
                              }
                              dataIndex="initSupply" key="initSupply"
                              ellipsis={!editingKey}
                              render={(value, record, index) => {
                                return editingKey ? (
                                    // 显示编辑框
                                    <Form.Item
                                        name={"initSupply" + index}
                                        rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'projects.supplyPlan.initSupply'}) }]}
                                    >
                                        <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" style={{marginTop:"14px"}}/>
                                    </Form.Item>
                                ) : (
                                  // 显示文本
                                  value
                                );
                            }}
                />
                <Table.Column width={g.lang==="zh"?160:210}
                              title={
                                <span>
                                    {
                                        editingKey?
                                        <span style={{ color: 'red', display: 'inline-block', verticalAlign: 'middle' , marginTop: "2px", marginRight: "5px"}} >*</span>:
                                        null
                                    }
                                    {formatMessage({id: 'projects.supplyPlan.warning.site'})}
                                    <span style={{marginLeft:"4px"}}>
                                        <Tooltip title={
                                                <>
                                                    <Row>{formatMessage({id:"projects.supplyPlan.warning.tip"})}</Row>
                                                </>
                                            }
                                            overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                        >
                                            <span>
                                                <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                            </span>
                                        </Tooltip>
                                    </span>
                                </span>
                              }
                              dataIndex="warning"
                              key="warning"
                              ellipsis={!editingKey}
                              render={(value, record, index) => {
                                return editingKey ? (
                                    // 显示编辑框
                                    <Form.Item
                                        name={"warning" + index}
                                        rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'projects.supplyPlan.warning'}) }]}
                                    >
                                        <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" style={{marginTop:"14px"}}/>
                                    </Form.Item>
                                ) : (
                                  // 显示文本
                                  value
                                );
                            }}  
                />
                <Table.Column width={g.lang==="zh"?168:267}
                              title={
                                  <span>
                                    {
                                        editingKey?
                                        <span style={{ color: 'red', display: 'inline-block', verticalAlign: 'middle' , marginTop: "2px", marginRight: "5px"}} >*</span>:
                                        null
                                    }
                                      {formatMessage({id: 'projects.supplyPlan.warning.dispensing'})}
                                      <span style={{marginLeft:"4px"}}>
                                            <Tooltip title={
                                                    <>
                                                        <Row>{formatMessage({id:"projects.supplyPlan.warning.dispensing.tip"})}</Row>
                                                    </>
                                                }
                                                overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                            >
                                                <span>
                                                    <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                                </span>
                                            </Tooltip>
                                        </span>
                                </span>
                              }
                              dataIndex="dispensingAlarm"
                              key="dispensingAlarm" ellipsis={!editingKey}
                              render={(value, record, index) => {
                                  return editingKey ? (
                                      // 显示编辑框
                                      <Form.Item
                                          name={"dispensingAlarm" + index}
                                      >
                                          <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" style={{marginTop:"14px"}}/>
                                      </Form.Item>
                                  ) : (
                                      // 显示文本
                                      value
                                  );
                              }}
                />
                <Table.Column width={g.lang==="zh"?110:110}
                              title={
                                <span>
                                    {
                                        editingKey?
                                        <span style={{ color: 'red', display: 'inline-block', verticalAlign: 'middle' , marginTop: "2px", marginRight: "5px"}} >*</span>:
                                        null
                                    }
                                    {formatMessage({id: 'projects.supplyPlan.buffer'})}
                                </span>
                              }
                              dataIndex="buffer"
                              key="buffer" ellipsis={!editingKey}
                              render={(value, record, index) => {
                                return editingKey ? (
                                    // 显示编辑框
                                    <span >
                                        <span style={{marginBottom:"0px"}}>
                                        <Form.Item
                                            name={"buffer" + index}
                                            rules={[{ 
                                                required: isBuffer(index) && isNa(index) ?true:false, 
                                                message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'projects.supplyPlan.buffer'})  
                                            }]}
                                            
                                        >
                                            <InputNumber disabled={(isBuffer(index) && isNa(index)) ?false:true} onChange={(value) => {updateField("warning", index, value)}} placeholder={isBuffer(index) && isNa(index)?formatMessage({id: 'placeholder.input.common'}):"-"} precision={0} min={0} step={1} className="full-width" style={{marginTop:"14px"}}/>
                                        </Form.Item>
                                        </span>
                                        {
                                            isWarning(index) && isBuffer(index) && isNa(index) &&
                                            <Col 
                                                style={{
                                                    color: "#FFAE00", 
                                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                    fontSize: "12px", 
                                                    fontWeight: 400, 
                                                    lineHeight: "17px", 
                                                    letterSpacing: "0px", 
                                                    marginTop: "-10px",
                                                }}
                                            >
                                                {formatMessage({ id: 'projects.supplyPlan.order.fail' })}
                                            </Col>
                                         }
                                    </span>
                                ) : (
                                  // 显示文本
                                  value
                                );
                            }}  
                />
                <Table.Column width={g.lang==="zh"?96:114} 
                              title={
                                <span>
                                    {
                                        editingKey?
                                        <span style={{ color: 'red', display: 'inline-block', verticalAlign: 'middle' , marginTop: "2px", marginRight: "5px"}} >*</span>:
                                        null
                                    }
                                    {formatMessage({id: 'projects.supplyPlan.secondSupply'})}
                                </span>
                              }
                              dataIndex="secondSupply" key="secondSupply" ellipsis={!editingKey}
                              render={(value, record, index) => {
                                return editingKey ? (
                                    // 显示编辑框
                                    <span>
                                        <Form.Item
                                            name={"secondSupply" + index}
                                            rules={[{ required: isSecondSupply(index) && isNa(index) ?true:false, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'projects.supplyPlan.secondSupply'}) }]}

                                        >
                                            <InputNumber  disabled={isSecondSupply(index) && isNa(index) ?false:true} onChange={(value) => {updateField("secondSupplyValue", index, value)}}  placeholder={isSecondSupply(index) && isNa(index) ?formatMessage({id: 'placeholder.input.common'}):"-"} precision={0} min={0} step={1} className="full-width" style={{marginTop:"14px"}}/>
                                        </Form.Item>
                                        {
                                            isSecondSupplyValue(index) && isSecondSupply(index) && isNa(index) &&
                                            <Col 
                                                style={{
                                                    color: "#FFAE00", 
                                                    fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                                    fontSize: "12px", 
                                                    fontWeight: 400, 
                                                    lineHeight: "17px", 
                                                    letterSpacing: "0px", 
                                                    marginTop: "-10px",
                                                }}
                                            >
                                                {formatMessage({ id: 'projects.supplyPlan.order.fail' })}
                                            </Col>
                                         }
                                    </span>
                                ) : (
                                  // 显示文本
                                  value
                                );
                            }}  
                />
                <Table.Column 
                    width={g.lang==="zh"?120:141}
                    title={
                        <span>
                            <span>
                                {formatMessage({id: 'projects.supplyPlan.unDistributionDate'})}
                            </span>
                            <span style={{marginLeft:"4px"}}>
                                <Tooltip title={formatMessage({id:"projects.supplyPlan.unDistributionDate.tip"})}
                                    overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                >
                                    <span>
                                        <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                    </span>
                                </Tooltip>
                            </span>
                        </span>
                    }
                    dataIndex="unDistributionDate" 
                    key="unDistributionDate"
                    ellipsis={!editingKey}
                    render={(value, record, index) => {
                        return editingKey ? (
                            // 显示编辑框
                            <Form.Item
                                name={"unDistributionDate" + index}
                                rules={[unDistributionDateValidator(index)]}
                            >
                                <InputNumber disabled={isNa(index)?false:true} placeholder={isNa(index)?formatMessage({id: 'placeholder.input.common'}):"-"} precision={0} min={0} step={1} className="full-width" style={{marginTop:"14px"}}/>
                            </Form.Item>
                        ) : (
                            // 显示文本
                            value
                        );
                    }}
                />
                <Table.Column 
                    width={g.lang==="zh"?120:215} 
                    title={
                        <span>
                            <span>
                                {formatMessage({id: 'projects.supplyPlan.unProvideDate'})}
                            </span>
                            <span style={{marginLeft:"4px"}}>
                                <Tooltip title={formatMessage({id:"projects.supplyPlan.unProvideDate.tip"})}
                                    overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                >
                                    <span>
                                        <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                    </span>
                                </Tooltip>
                            </span>
                        </span>
                    }
                    dataIndex="unProvideDate" 
                    key="unProvideDate"
                    ellipsis={!editingKey}
                    render={(value, record, index) => {
                        return editingKey ? (
                            // 显示编辑框
                            <Form.Item
                                name={"unProvideDate" + index}
                            >
                                <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1} className="full-width" style={{marginTop:"14px"}}/>
                            </Form.Item>
                        ) : (
                            // 显示文本
                            value
                        );
                    }}
                />
                <Table.Column 
                    width={g.lang==="zh"?120:140} 
                    title={
                        <span>
                            <span>
                                {formatMessage({id: 'projects.supplyPlan.notCountedDate'})}
                            </span>
                            <span style={{marginLeft:"4px"}}>
                                <Tooltip title={formatMessage({id:"projects.supplyPlan.notCountedDate.tip"})}
                                    overlayStyle={{whiteSpace: 'pre-wrap', maxWidth: '600px', width: 'auto'}}
                                >
                                    <span>
                                        <QuestionCircleFilled style={{color:"#D0D0D0", cursor: "pointer"}} />
                                    </span>
                                </Tooltip>
                            </span>
                        </span>
                    }
                    dataIndex="notCountedDate" 
                    key="notCountedDate"
                    ellipsis={!editingKey}
                    render={(value, record, index) => {
                        return editingKey ? (
                            // 显示编辑框
                            <Form.Item
                                name={"notCountedDate" + index}
                            >
                                <InputNumber disabled={isNa(index) ?false:true} placeholder={isNa(index) ?formatMessage({id: 'placeholder.input.common'}):"-"} precision={0} min={0} step={1} className="full-width" style={{marginTop:"14px"}}/>
                            </Form.Item>
                        ) : (
                            // 显示文本
                            value
                        );
                    }}
                />
                <Table.Column 
                    width={g.lang==="zh"?100:160} 
                    title={formatMessage({id: 'projects.supplyPlan.validityReminder'})}
                    dataIndex="validityReminder" 
                    key="validityReminder"
                    ellipsis={!editingKey}
                    render={(value, record, index) => {
                        return editingKey ? (
                            // 显示编辑框
                            <Form.Item
                                name={"validityReminder" + index}
                            >
                                <InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})}  precision={0} min={0} step={1} className="full-width" style={{marginTop:"14px"}}/>
                            </Form.Item>
                        ) : (
                            // 显示文本
                            value
                        );
                    }}
                />
                <Table.Column width={g.lang==="zh"?220:220}
                              title={
                                <span>
                                    {
                                        editingKey?
                                        <span style={{ color: 'red', display: 'inline-block', verticalAlign: 'middle' , marginTop: "2px", marginRight: "5px"}} >*</span>:
                                        null
                                    }
                                    {formatMessage({id: 'projects.supplyPlan.forecast'})}
                                </span>
                              }
                              dataIndex="forecast" key="forecast"
                              ellipsis={!editingKey}
                              render={(value, record: any, index) => {
                                    return editingKey ? (
                                        // 显示编辑框
                                        <Form.Item
                                            rules={[{ required: isForecast(index) && isNa(index) ?true:false, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'projects.supplyPlan.forecast'}) }]}
                                        >
                                            <Input.Group style={{ display: "flex", alignItems: "center"}}>
                                                <Form.Item style={{marginBottom:0}} name={"forecastMin" + index} rules={[{ required: isForecast(index) && isNa(index) ?true:false, message:formatMessage({id: 'placeholder.input.common'}) +formatMessage({ id: 'projects.supplyPlan.forecast' }) }]}>
                                                    <InputNumber placeholder={isForecast(index) && isNa(index) ?formatMessage({id: 'placeholder.input.common'}):"-"} precision={0} min={0} step={1} style={{marginTop:"14px"}} disabled={isForecast(index) && isNa(index) ?false:true}/>
                                                </Form.Item>
                                                <span style={{marginTop: 18 ,marginLeft:5, marginRight:5}}>~</span>
                                                <Form.Item style={{marginBottom:0}} name={"forecastMax" + index} rules={[forecastMaxValidator(index),{ required: isForecast(index) && isNa(index) ?true:false, message:formatMessage({id: 'placeholder.input.common'}) +formatMessage({ id: 'projects.supplyPlan.forecast' }) }]}>
                                                    <InputNumber placeholder={isForecast(index) && isNa(index) ?formatMessage({id: 'placeholder.input.common'}):"-"} precision={0} min={0} step={1} style={{marginTop:"14px"}} disabled={isForecast(index) && isNa(index) ?false:true}/>

                                                </Form.Item>
                                                {/* <span style={{marginTop: 6 ,marginLeft:5, marginRight:5}}>{formatMessage({id:"project.overview.day"})}</span> */}
                                            </Input.Group>
                                        </Form.Item>

                                    ) : (
                                        // 显示文本
                                        ((record.forecastMin !== 0 || record.forecastMax !== 0) && record.autoSupplySize?.find((it:any)=> it === 3)) ? record.forecastMin + "~" + record.forecastMax : "-"
                                    );
                                }}
                />
                <Table.Column 
                    width={g.lang==="zh"?200:200} 
                    title={
                        <span>
                            {
                                editingKey?
                                <span style={{ color: 'red', display: 'inline-block', verticalAlign: 'middle' , marginTop: "2px", marginRight: "5px"}} >*</span>:
                                null
                            }
                            {formatMessage({id: 'projects.supplyPlan.supplyMode'})}
                        </span>
                    }
                    dataIndex="supplyMode" key="supplyMode"
                    ellipsis={!editingKey}
                    render={(value, record:any, index) => {
                        let data = ""
                        if (value === 1) {
                            data = formatMessage({id: 'projects.supplyPlan.allSupply'})
                        }
                        if (value === 2) {
                            data = formatMessage({id: 'projects.supplyPlan.singleSupply'})
                        }
                        if (value === 3) {
                            data = formatMessage({id: 'projects.supplyPlan.allSupplyAndMedicine'})
                        }
                        if (value === 4) {
                            data = formatMessage({id: 'projects.supplyPlan.singleSupplyAndMedicine'})
                        }
                        return editingKey ? (
                            // 显示编辑框
                            <span>
                                <Form.Item
                                    name={"supplyMode" + index}
                                    rules={[{ required: isNa(index) ?true:false, message:formatMessage({id: 'placeholder.input.common'}) + formatMessage({id: 'projects.supplyPlan.supplyMode'}) }]}
                                >
                                    {

                                        record.isBlind ?
                                        <Select value={supplyModeValue} onChange={changeSupplyMode(index)} placeholder={isNa(index)?formatMessage({id: 'placeholder.select.common'}):"-"} style={{marginTop:"14px"}} disabled={isNa(index)?false:true}>
                                                <Select.Option value={1}>{formatMessage({ id: 'projects.supplyPlan.allSupply' })}</Select.Option>
                                                <Select.Option value={3}>{formatMessage({ id: 'projects.supplyPlan.allSupplyAndMedicine' })}</Select.Option>
                                                <Select.Option value={4}>{formatMessage({ id: 'projects.supplyPlan.singleSupplyAndMedicine' })}</Select.Option>
                                            </Select>
                                            :
                                            <Select value={supplyModeValue} onChange={changeSupplyMode(index)} placeholder={isNa(index)?formatMessage({id: 'placeholder.select.common'}):"-"} style={{marginTop:"14px"}} disabled={isNa(index)?false:true}>
                                                <Select.Option value={1}>{formatMessage({ id: 'projects.supplyPlan.allSupply' })}</Select.Option>
                                                <Select.Option value={2}>{formatMessage({ id: 'projects.supplyPlan.singleSupply' })}</Select.Option>
                                            </Select>
                                    
                                }
                                </Form.Item>
                                {
                                    result.has(index) && result.get(index).supplyMode &&
                                    <Col 
                                        style={{
                                            color: "#F96964", 
                                            fontFamily: "'Helvetica NEUE', HELVETICA, 'PINGFANG SC', 'HIRAGINO SANS GB', 'MICROSOFT YAHEI', 微软雅黑, ARIAL, SANS-SERIF",
                                            fontSize: "12px", 
                                            fontWeight: 400, 
                                            lineHeight: "17px", 
                                            letterSpacing: "0px", 
                                            marginTop: "-10px",
                                        }}
                                    >
                                        {
                                            result.get(index).supplyMode
                                        }
                                    </Col>
                                }
                            </span>
                        ) : (
                            // 显示文本
                            data
                        );
                    }}
                />
                { editingKey?
                    <Table.Column
                        width={g.lang==="zh"?70:98} fixed='right'
                        title={<FormattedMessage id="common.operation"/>}
                        render={(item, record, index) => {
                            return (
                                <span>
                                {
                                    permissions(permission, "operation.build.supply-plan.medicine.delete") && projectStatus !== 2 && data.length > 1 ?
                                    <Typography.Link style={{marginLeft:"8px"}} onClick={() => deleteRow(record, index)}
                                        // disabled={editingKey?false:true}
                                    >
                                        {formatMessage({id: 'common.delete'})}
                                    </Typography.Link>:null
                                }
                                </span>
                            );
                        }}
                    />:null
                }

            </Table>
            {
                (   permissions(permission, "operation.build.supply-plan.medicine.add") && projectStatus !== 2 && editingKey &&
                    <Button
                        block
                        type="dashed"
                        style={{height:36}}
                        className="mar-top-10"
                        icon={<PlusOutlined />}
                        onClick={() => addMedicine()}
                    >
                        <span style={{fontSize:12}}>{intl.formatMessage({ id: "common.add" })}</span>
                    </Button>
                )
            }
            </Form>
        </Spin>
        {/* <SupplyPlanMedicineAdd bind={medicine_ref} refresh={getMedicine}/> */}
    </CustomDrawer>;

}

const CustomDrawer = styled(Drawer)`
  .ant-drawer-header {
    padding: 16px 8px 16px 24px !important;
  }
`
