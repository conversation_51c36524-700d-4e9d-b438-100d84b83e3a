import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const ProjectPlanContext = React.createContext<{
        timeZone: number ;
        setTimeZone: (data: number) => void;
    }
    |
    null>(null);

export const  ProjectPlanProvider = ({children}: { children: ReactNode }) => {

    const [timeZone,setTimeZone] = useSafeState<number>(8);



    return (
        < ProjectPlanContext.Provider
            value={
                {
                    timeZone,setTimeZone,


                }
            }
        >
            {children}
        </ ProjectPlanContext.Provider>
    )
};

export const useProjectPlan = () => {
    const context = React.useContext( ProjectPlanContext);
    if (!context) {
        throw new Error("useNotice must be used in NoticeContextProvider");
    }
    return context;
};

