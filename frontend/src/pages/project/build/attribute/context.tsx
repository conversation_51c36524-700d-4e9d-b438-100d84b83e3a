import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const AttributeContext = React.createContext<{
        timeZone: number ;
        setTimeZone: (data: number) => void;
    }
    |
    null>(null);

export const  AttributeProvider = ({children}: { children: ReactNode }) => {

    const [timeZone,setTimeZone] = useSafeState<number>(8);

    return (
        < AttributeContext.Provider
            value={
                {
                    timeZone,setTimeZone,
                }
            }
        >
            {children}
        </ AttributeContext.Provider>
    )
};

export const useAttribute = () => {
    const context = React.useContext( AttributeContext);
    if (!context) {
        throw new Error("useNotice must be used in NoticeContextProvider");
    }
    return context;
};

