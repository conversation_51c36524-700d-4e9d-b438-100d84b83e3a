import React from "react";
import { FormattedMessage, useIntl } from "react-intl";
import { Cascader, Form, Input, message, Modal, Select, Switch, Row, Tooltip, } from "antd";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../../context/auth";
import { useGlobal } from "../../../../context/global";
import { useFetch } from "../../../../hooks/request";
import { save } from "../../../../api/project_storehouse";
import { allCountries } from "../../../../api/country";
import styled from "@emotion/styled";
import _ from "lodash";

export const Add = (props) => {
    const intl = useIntl();
    const { formatMessage } = intl;

    const [visible, setVisible] = useSafeState(false);
    const [infoId, setInfoId] = useSafeState(null);
    const [onlyId, setOnlyId] = useSafeState(null);
    const [showSupplier, setShowSupplier] = useSafeState(false);
    const [supplier, setSupplier] = useSafeState(null);
    const [countries, setCountries] = useSafeState([]);
    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState(null);
    const [form] = Form.useForm();
    const auth = useAuth()
    const g = useGlobal()
    const { runAsync: run_save, loading: saveLoading } = useFetch(save, { manual: true })
    const { runAsync: run_allCountries } = useFetch(allCountries, { manual: true })

    const languageHandler = (lang) => {
        let language = "en";
        switch (lang) {
            case 'en':
                language = "en";
                break;
            case 'zh':
                language = "cn";
                break;
            case 'ko':
                language = "ko";
                break;
        }
        return language
    };

    const onChange = (value) => {
        // console.log(`selected ${value}`);
    };
    const onSearch = (value) => {
        // console.log('search:', value);
    };

    const show = (info) => {
        setVisible(true);
        setSend(true);
        getCountries();
        if (info) {
            setShowSupplier(info.connected)
            form.setFieldsValue({ ...info });
            setInfoId(info.id);
            setOnlyId(info.onlyId)
            setOldData(info);
            setSend(true);
            if (info.connected) {
                setSupplier(info.supplier);
            }
        }
    };

    const getCountries = () => {
        run_allCountries({}).then((result) => {
            let data = result.data
            const layer1Array = []
            for (let i = 0; i < data.length; i++) {
                const country = data[i]
                const layer2Array = []
                const stateArray = country.state
                if (stateArray != null && stateArray.length > 1) {
                    for (let j = 0; j < stateArray.length; j++) {
                        const layer3Array = []
                        const state = stateArray[j]
                        const cityArray = state.city
                        if (cityArray != null) {
                            for (let k = 0; k < cityArray.length; k++) {
                                const city = cityArray[k];
                                let layer3 = {
                                    value: city.code,
                                    label: city[languageHandler(g.lang)],
                                };
                                layer3Array.push(layer3);
                            }
                        }
                        let layer2 = {
                            value: state.code,
                            label: state[languageHandler(g.lang)],
                            children: layer3Array
                        }
                        layer2Array.push(layer2)
                    }
                } else if (stateArray != null) {
                    const state = stateArray[0]
                    const cityArray = state.city
                    for (let k = 0; k < cityArray.length; k++) {
                        const city = cityArray[k]
                        let layer2 = {
                            value: city.code,
                            label: city[languageHandler(g.lang)],
                        }
                        layer2Array.push(layer2)
                    }
                }
                const layer1 = {
                    value: country.code,
                    label: country[languageHandler(g.lang)],
                    children: layer2Array
                };
                layer1Array.push(layer1)
            }
            setCountries(layer1Array)
        })
    }

    const hide = () => {
        setInfoId(null);
        setOnlyId(null);
        setSend(true);
        setOldData(null);
        setVisible(false);
        form.resetFields();
    };

    const onSave = () => {
        form.validateFields().then(values => {
            run_save(
                {
                    ...values,
                    id: infoId,
                    customerId: auth.customerId,
                    projectId: auth.project.id,
                    envId: auth.env.id,
                }).then((result) => {
                    props.refresh();
                    hide();
                    message.success(result.msg);
                })
        })
    }

    const phoneNumberValidator = {
        validator: (rule, value, callback) => {
            const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
            if (value && !reg.test(value)) {
                return Promise.reject(formatMessage({ id: 'validator.message.phone' }));
            }
            return Promise.resolve();
        }
    };
    const handlerConnected = (checked) => {
        setShowSupplier(checked);
        if (checked) {
            setSupplier("baicheng");
            form.setFieldsValue({ "supplier": "baicheng" });
        }
        formChange();
    }

    function filter(inputValue, path) {
        return path.some(option => option.label.toLowerCase().indexOf(inputValue.trim().toLowerCase()) > -1);
    }

    React.useImperativeHandle(props.bind, () => ({ show }));

    const formChange = () => {
        if (infoId) {
            const a = _.cloneDeep(oldData);
            if (a.country === null) {
                a.country = [];
            }
            // console.log("1===" + JSON.stringify(a)); 
            let b = _.cloneDeep(form.getFieldValue());
            if (b.country === undefined) {
                b.country = [];
            }
            // console.log("2===" + JSON.stringify(b)); 
            if (!compareObjects(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }

        }
    };

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1, obj2) {
        for (let key in obj1) {
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                    if (!arraysAreEqual(obj1[key], obj2[key])) {
                        return false;
                    }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1, arr2) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang === "zh" ? 5 : 8 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang === "zh" ? 19 : 16 },
        },
    }

    return (
        <React.Fragment>
            <Modal
                title={
                    <Row>
                        <FormattedMessage id={!infoId ? "common.add" : "common.edit"} />
                        {
                            infoId ?
                                <Tooltip
                                    trigger={["hover", "click"]}
                                    overlayInnerStyle={{ fontSize: 12, background: "#575758", width: "320px" }}
                                    placement="bottom"
                                    title={intl.formatMessage({ id: "form.onlyID" }) + ":  " + onlyId}
                                >
                                    <svg className="iconfont" width={20} height={20} style={{ marginLeft: "8px", marginTop: "4px" }}>
                                        <use xlinkHref="#icon-biaodanbianhao" fill="#adb2ba"></use>
                                    </svg>
                                </Tooltip> : null
                        }
                    </Row>
                }
                visible={visible}
                onCancel={hide}

                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                confirmLoading={saveLoading}
                okText={formatMessage({ id: 'common.ok' })}
                onOk={onSave}
                okButtonProps={{ disabled: infoId ? send : false }}
            >
                <CustomForm form={form} onValuesChange={formChange} layout="horizontal" {...formItemLayout}>
                    <Form.Item label={formatMessage({ id: 'storehouse.name' })} name="storehouseId"
                        rules={[{ required: true }]} >
                        <Select placeholder={formatMessage({ id: 'placeholder.select.common' })} className="full-width" disabled={infoId}
                            // showArrow={true}
                            showSearch
                            onChange={onChange}
                            onSearch={onSearch}
                            filterOption={(input, option) =>
                                (option?.label ?? '').toLowerCase().includes(input.trim().toLowerCase())
                            }
                            allowClear>
                            {
                                props.options ?
                                    props.options.map(
                                        (it, index) => (
                                            <Select.Option key={it.id}
                                                value={it.id}
                                                label={it.name}>{it.name}</Select.Option>
                                        )
                                    )
                                    :
                                    null
                            }
                        </Select>
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'storehouse.country.region' })} name="country"
                    >
                        <Cascader
                            placeholder={formatMessage({ id: 'placeholder.select.common' })}
                            options={countries}
                            showSearch={{ filter }}
                        />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'common.contacts' })} name="contacts" rules={[{ required: true }]}
                    >
                        <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" allowClear />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'common.phone' })} name="phone"
                        rules={[phoneNumberValidator]}>
                        <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" allowClear />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'common.email' })} name="email" >
                        <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" allowClear />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'common.address' })} name="address" >
                        <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" allowClear />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'projects.storehouse.connected' })} name="connected"
                        valuePropName='checked'>
                        <Switch size="small" defaultChecked={false} onChange={handlerConnected} />
                    </Form.Item>
                    {showSupplier &&
                        <>
                            <Form.Item label={formatMessage({ id: 'projects.storehouse.logistics.supplier' })} name="supplier" >
                                <Select placeholder={formatMessage({ id: 'placeholder.select.common' })} defaultValue="baicheng" rules={[{ required: true }]} onChange={(v) => { setSupplier(v) }}>
                                    <Select.Option value="shengsheng"><FormattedMessage id="projects.storehouse.logistics.shengsheng" /></Select.Option>
                                    <Select.Option value="catalent"><FormattedMessage id="projects.storehouse.logistics.catalent" /></Select.Option>
                                    <Select.Option value="baicheng"><FormattedMessage id="projects.storehouse.logistics.baicheng" /></Select.Option>
                                    <Select.Option value="eDRUG"><FormattedMessage id="projects.storehouse.logistics.eDRUG" /></Select.Option>
                                </Select>
                            </Form.Item>
                            {
                                supplier === "catalent" && <Form.Item className='custom-long-label' label={formatMessage({ id: 'projects.storehouse.not.included' })} name="notIncluded" valuePropName='checked'>
                                    <Switch size={"small"} defaultChecked={false} />
                                </Form.Item>
                            }

                        </>
                    }
                </CustomForm>
            </Modal>
        </React.Fragment>
    )
};

// https://github.com/ant-design/ant-design/issues/5285
const CustomForm = styled(Form)`
    .ant-form-item-label {
        white-space: normal;
    }
    .ant-form-item-label label {
        text-align: right;
    }

    .custom-long-label label {
        height: 36px;
    }
`
