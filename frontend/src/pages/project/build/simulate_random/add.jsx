import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Col, Form, Input, InputNumber, message, Modal, Row, Select, Space, Spin, Tooltip,} from "antd";
import {useSafeState} from "ahooks";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {add, factor, update} from "../../../../api/simulate_random";
import {useGlobal} from "../../../../context/global";
import {getRandomList} from "../../../../api/randomization";
import {permissionsCohort} from "../../../../tools/permission";
import _ from "lodash";

export const Add = (props) => {

    const g = useGlobal()
    const intl = useIntl();
    const { formatMessage } = intl;

    const [visible, setVisible] = useSafeState(false);
    const [infoId, setInfoId] = useSafeState(null);
    const [onlyId, setOnlyId] = useSafeState(null);
    const [submitting, setSubmitting] = useSafeState(false);
    const [factors, setFactors] = useSafeState([]);
    const [form] = Form.useForm();
    const auth = useAuth()
    const projectType = auth.project.info.type;
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const cohorts = auth.env ? auth.env.cohorts : null;
    const customerId = auth.customerId;
    const [randomList, setRandomList] = useSafeState([]);
    const { runAsync: run_add } = useFetch(add, { manual: true });
    const { runAsync: run_factor } = useFetch(factor, { manual: true });
    const { runAsync: run_update } = useFetch(update, { manual: true });
    const { runAsync: getRandomListRun, loading: getRandomListLoading } = useFetch(getRandomList, { manual: true });


    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState(null);

    const show = (info) => {
        setVisible(true);
        setSend(true);
        if (info) {
            setOldData(info);
            form.setFieldsValue({ ...info });
            setInfoId(info.id);
            setOnlyId(info.onlyId);
            if(info.cohortId !== "" && info.cohortId != null){
                getRandomListData(info.cohortId);
                getFactors(info.cohortId)
            }
        }
        // 基本研究项目
        if(projectType === 1){
            getRandomListData();
            getFactors()
        }
    };

    const hide = () => {
        setSend(true);
        setOldData(null);
        setVisible(false);
        form.resetFields();
        setInfoId(null);
        setOnlyId(null);
        setSubmitting(false);
        setRandomList([]);
    };

    const formChange = () => {
        if (infoId){
            // const a = _.cloneDeep(oldData);
            // let aRandomListIds = ["0"];
            // if (a.randomListIds){
            //     aRandomListIds = a.randomListIds;
            // }
            // a.randomListIds = aRandomListIds
            // const b = _.cloneDeep(form.getFieldsValue());

            // if (!compareObjects(a, b)) {
            //   setSend(false);
            // } else {
            //     setSend(true);
            // }
            setSend(false);
        }
    };

    //比较两个JavaScript对象是否相同
    // function compareObjects(obj1, obj2) {
    //     for (let key in obj1) {
    //         if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
    //             if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
    //             if (!arraysAreEqual(obj1[key], obj2[key])) {
    //                 return false;
    //             }
    //             } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
    //                 if (!compareObjects(obj1[key], obj2[key])) {
    //                     return false;
    //                 }
    //             } else {
    //                 if (obj1[key] !== obj2[key]) {
    //                     return false;
    //                 }
    //             }
    //         }
    //     }
    //     return true;
    // }

    //比较两个数组是否相同
    function arraysAreEqual(arr1, arr2) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }

    const getRandomListData =(cohortId) => {
        setRandomList([]);
        getRandomListRun({
            projectId: projectId,
            env: envId,
            cohort: cohortId,
            customer: customerId,
            roleId: auth.project.permissions.role_id,
        }).then((result) => {
            if(result.code === 0){
                setRandomList(result.data);
            }else{
                setRandomList([]);
            }

        });
    };
    const getFactors =(cohortId) => {
        setFactors([]);
        run_factor({
            envId: envId,
            cohortId: cohortId,
        }).then((result) => {
            if(result.code === 0){
                setFactors(result.data);
            }else{
                setRandomList([]);
            }

        });
    };

    const save = () => {
        form.validateFields().then(values => {
            setSubmitting(true);
            if (infoId == null) {
                run_add({
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    cohortId: values.cohortId,
                }, { ...values }).then((resp) => {
                    message.success(resp.msg)
                    props.refresh();
                    hide();
                }).catch(() => setSubmitting(false));
            } else {
                run_update({ id: infoId, cohortId: values.cohortId }, { ...values }).then((resp) => {
                    message.success(resp.msg)
                    props.refresh();
                    hide();
                }).catch(() => setSubmitting(false));
            }
        }).catch(errors => {
            setSubmitting(false);
        })
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang === "zh" ? 4 : 8 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang === "zh" ? 20 : 18 },
        },
    }

    return (
        <React.Fragment>
            <Modal
                title={
                    <Row>
                        <FormattedMessage id={!infoId ? "common.add" : "common.edit"} />
                        {
                            infoId?
                            <Tooltip
                                trigger={["hover", "click"]}
                                overlayInnerStyle={{ fontSize: 12, background: "#575758", width: "320px" }}
                                placement="bottom"
                                title={intl.formatMessage({ id: "form.onlyID" })+ ":  " + onlyId}
                            >
                                <svg className="iconfont" width={20} height={20} style={{ marginLeft: "8px", marginTop: "3px" }}>
                                    <use xlinkHref="#icon-biaodanbianhao" fill="#adb2ba"></use>
                                </svg>
                            </Tooltip>:null
                        }
                    </Row>
                }
                open={visible}
                onCancel={hide}
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                okButtonProps={{
                    // disabled: infoId?send:false,
                    loading: submitting }}
                onOk={save}
                okText={formatMessage({ id: 'common.ok' })}
            >
                <Spin spinning={getRandomListLoading}>
                <Form form={form} onValuesChange={formChange} layout="horizontal" {...formItemLayout}>
                    {projectType === 2 && (
                        <Form.Item label={formatMessage({ id: "projects.second" })} name="cohortId" rules={[{ required: true }]}>
                            <Select className="full-width" placeholder={formatMessage({ id: 'placeholder.select.common' })}
                                    onSelect={(value) => {
                                        form.setFieldsValue({ "randomListIds":[] });
                                        form.setFieldsValue({ "factorRatio":[] });
                                        getRandomListData(value);
                                        getFactors(value);
                                    }}
                            >
                                {
                                    cohorts.filter((value)=>permissionsCohort(auth.project.permissions,"operation.build.simulate-random.add",value.status)).map((value) =>{
                                        console.log(value)
                                        let name = value.name;
                                        if (value.type === 1){
                                            name = value.name + " - "+value.re_random_name
                                        }
                                        return <Select.Option value={value.id}>{name}</Select.Option>
                                    }
                                    )
                                }
                            </Select>
                        </Form.Item>
                    )}
                    {projectType === 3 && (
                        <Form.Item
                            label={formatMessage({ id: "report.attributes.random.stage" })}
                            name="cohortId"
                            rules={[{ required: true }]}>
                            <Select className="full-width" placeholder={formatMessage({ id: 'placeholder.select.common' })}
                                    onSelect={(value) => {
                                        form.setFieldsValue({ "randomListIds":[] });
                                        form.setFieldsValue({ "factorRatio":[] });
                                        getRandomListData(value);
                                        getFactors(value);
                                    }}
                            >
                                {
                                    cohorts.map((value) =>
                                        <Select.Option value={value.id}>{value.name}</Select.Option>
                                    )
                                }
                            </Select>
                        </Form.Item>
                    )}
                    <Form.Item label={formatMessage({ id: 'simulate_random.name' })} name="name"
                        rules={[{ required: true }]}>
                        <Input placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" allowClear />
                    </Form.Item>
                    {
                        !getRandomListLoading?
                            <Form.Item rules={[{ required: true, message:formatMessage({id:"placeholder.select.common"}) + formatMessage({id: 'projects.randomization.list'}) }]} name="randomListIds" label={formatMessage({id: 'projects.randomization.list'})}>
                            <Select showArrow={true} showSearch={false} className="full-width" placeholder={formatMessage({ id: 'placeholder.select.common' })}
                                    style={{ width: 120 }} mode="multiple">
                                {
                                    randomList?.map((it) =>
                                        it.status === 1 ?
                                            <Select.Option key={it._id} value={it._id}>{it.name}</Select.Option>
                                            :
                                            null
                                    )
                                }
                            </Select>
                        </Form.Item>:null
                    }

                    <Form.Item label={formatMessage({ id: 'simulate_random.country.count' })} name="countryQuantity"
                        rules={[{ required: true }]}>
                        <InputNumber placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" allowClear />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'simulate_random.region.count' })} name="regionQuantity"
                               rules={[{ required: true }]}>
                        <InputNumber placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" allowClear />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'simulate_random.site.count' })} name="siteQuantity"
                               rules={[{ required: true }]}>
                        <InputNumber placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" allowClear />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'simulate_random.run.count' })} name="runQuantity"
                        rules={[{ required: true }]}>
                        <InputNumber placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" rules={[{ required: true }]} allowClear />
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: 'simulate_random.subject.count' })} name="subjectQuantity"
                        rules={[{ required: true }]}>
                        <InputNumber placeholder={formatMessage({ id: 'placeholder.input.common' })} className="full-width" rules={[{ required: true }]} allowClear />
                    </Form.Item>
                    {factors != null && factors.length > 0 ? (
                        <Form.Item label={formatMessage({id: 'simulate_random.factor.ratio'})}>
                            <Form.List name="factorRatio">
                                {(fields, {add, remove}) => (
                                    <>
                                        {factors.map((factor, index) => (
                                            <div key={factor.id} style={{
                                                backgroundColor: '#F8F9FA',
                                                padding: '12px',
                                                paddingBottom: '0px',
                                                marginBottom: '12px'
                                            }}>
                                                {factor.options.map((option, optionIndex) => (
                                                    <Row>

                                                        <Form.Item
                                                            name={[index,optionIndex, 'factorName']}
                                                            initialValue={factor.name}
                                                            hidden
                                                        >
                                                        </Form.Item>
                                                        <Form.Item
                                                            name={[index,optionIndex, 'optionValue']}
                                                            initialValue={option.value}
                                                            hidden
                                                        >
                                                        </Form.Item>
                                                        <Col style={{flex: 1,marginRight: '8px'}}>
                                                        <Form.Item
                                                            name={[index,optionIndex, 'label']}
                                                            initialValue={`${factor.label}: ${option.label}`}
                                                        >
                                                            <Input
                                                                style={{width: '100%'}}
                                                                disabled/>
                                                        </Form.Item>
                                                        </Col>
                                                        <Col style={{flex: 1}}>
                                                        <Form.Item
                                                            name={[index,optionIndex, 'ratio']}
                                                        >

                                                            <InputNumber
                                                                style={{width: '100%'}}
                                                                min={1}
                                                                precision={0}
                                                                placeholder={formatMessage({id: 'common.ratio'})}
                                                            />
                                                        </Form.Item>
                                                        </Col>
                                                    </Row>
                                                ))}
                                            </div>
                                        ))}
                                    </>
                                )}
                            </Form.List>
                        </Form.Item>
                    ) : null}


                </Form>
                </Spin>
            </Modal>
        </React.Fragment>
    )
};