import React from "react";
import {Button, Checkbox, Form, Input, message, Modal, Radio, Space, Switch} from "antd";
import {FormattedMessage, useIntl} from "react-intl";
import {useFetch} from "../../../../hooks/request";
import {addRandomization, updateRandomization} from "../../../../api/randomization";
import {useSafeState} from "ahooks";
import {useAuth} from "../../../../context/auth";
import {useGlobal} from "../../../../context/global";
import {MinusCircleFilled, PlusOutlined} from "@ant-design/icons";
import {CustomConfirmModal} from "../../../../components/modal";
import _ from "lodash";


export const RandomListGroup = (props:any) => {
    const [isModalGroupVisible, setIsModalGroupVisible] = useSafeState<any>(false);
    const [group, setGroup] = useSafeState<any>(null)
    const [groupForm] = Form.useForm();

    const auth = useAuth();
    const intl = useIntl();
    const {formatMessage} = intl;

    const {runAsync: addRandomizationRun, loading:addRandomizationLoading} = useFetch(addRandomization, {manual: true})
    const {runAsync: updateRandomizationRun, loading:updateRandomizationLoading} = useFetch(updateRandomization, {manual: true})


    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const cohortId = props.cohort ? props.cohort.id : null;
    const customerId = auth.customerId;
    const [title, setTitle] = useSafeState("");
    const [isEdit, setIsEdit] = useSafeState(false);
    const [isCopyData, setIsCopyData] = useSafeState<any>(false);
    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState<any>(null);
    const [subGroup, setSubGroup] = useSafeState<any>([])
    const [isOldSubGroup,setIsOldSubGroup] = useSafeState(false);
    const [isSubGroup,setIsSubGroup] = useSafeState(false);
    const [statusDisable,setStatusDisabled] = useSafeState(false)
    const g = useGlobal()
    const formItemLayout = {
        labelCol: { style: {   width: g.lang === "en"? "190px": "106px"} },
    };
    // 展示对话框
    const handleGroupCancel = () => {
        hide()
    };
    const updateGroup = () => {
        groupForm.validateFields().then(
            values =>{
                if (values.status === 2 && oldData.status !== 2){
                    CustomConfirmModal({
                        title: formatMessage({id: 'common.confirm.inactivate.group'}),
                        content: <>
                        <span style={{color: "red"}}>
                            {formatMessage({id: 'random.list.inactivating.tips'})}
                        </span>
                        </>,
                        okText: formatMessage({id: 'common.ok'}),
                        cancelText: formatMessage({id: 'common.cancel'}),
                        onOk: () => {
                            update(values)
                        }
                    })
                }else{
                    update(values)
                }



            }
        ).catch(() => { })
    }


    const formChange = () => {
        if(isEdit){
            const a = oldData;;
            // console.log("1===" + JSON.stringify(a)); 
            let b = groupForm.getFieldsValue();
            // console.log("2===" + JSON.stringify(b)); 
            if (!compareObjects(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }
        }
    };

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1: any, obj2: any) {
        for (let key in obj1) { 
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                if (!arraysAreEqual(obj1[key], obj2[key])) {
                    return false;
                }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        for (let key in obj2) {
            if (!obj1.hasOwnProperty(key) && (key === "subGroup") && obj2[key] !== null && obj2[key] !== undefined) {
                return false;
            } 
        }
        return true;
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1: any, arr2: any) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }

    const update = (values:any)=>{
        if (values.subGroup){
            const subgroupNames = values.subGroup.map((item: any) => item.name); // 获取所有子组的名称
            const uniqueNames = new Set(subgroupNames); // 将名称转换为集合
            if (subgroupNames.length !== uniqueNames.size) {
                message.error(formatMessage({id:"randomization.config.subGroup.duplicate"}))
                return
            }
        }
        const ret = group?.id? {...groupForm.getFieldsValue(),"id":group.id}: groupForm.getFieldsValue();
        ret.isCopyData = isCopyData;
        const data = {
            "projectId": projectId,
            "envId": envId,
            "cohortId": cohortId,
            "customerId": customerId,
            "group": ret,
        }
        if (group?.id){
            updateRandomizationRun(data).then(
                () => {
                    props.refresh()
                    groupForm.resetFields()
                    setIsModalGroupVisible(false);
                }
            )
        }else {
            addRandomizationRun(data).then(
                () => {
                    props.refresh()
                    groupForm.resetFields()
                    setIsModalGroupVisible(false);
                }
            )
        }
    }
    const show = (item:any) => {
        setSend(true);
        setIsEdit(false);
        setIsCopyData(false);
        setSubGroup([]);
        setTitle(formatMessage({id: 'randomization.config.groupAdd'}))
        setGroup(null)
        groupForm.resetFields()
        setIsModalGroupVisible(true)
        if (item){
            setOldData(item);
            setGroup(item)
            groupForm.setFieldsValue({...item})
            if (item.subGroup != null && item.subGroup.length>0){
                setIsSubGroup(true);
                setIsOldSubGroup(true);
                setSubGroup(item.subGroup);
            }
            setStatusDisabled(item.status === 2);
            setIsEdit(true);
            setSend(true);
            setIsCopyData(item?.isCopyData);
            setTitle(formatMessage({id: 'randomization.config.groupEdit'}));
        }
    }
    React.useImperativeHandle(props.bind, () => ({show}));
    const hide = () => {
        setOldData(null);
        setSend(true);
        setIsModalGroupVisible(false);
        setGroup(null);
        setIsSubGroup(false);
        setIsOldSubGroup(false);
        setIsEdit(false);
        setIsCopyData(false);
        setSubGroup([]);
        groupForm.resetFields();
    };
    return (
        <Modal className="custom-small-modal" 
            confirmLoading={updateRandomizationLoading || addRandomizationLoading} 
            destroyOnClose={true} width={400} title={title} visible={isModalGroupVisible} 
            onOk={updateGroup} 
            okText={formatMessage({ id: 'common.ok' })}
            onCancel={handleGroupCancel} 
            centered
            maskClosable={false}
            okButtonProps={{ disabled: isEdit?send:false }}
            
        >
            <Form form={groupForm} onValuesChange={formChange} {...formItemLayout} >
                <Form.Item label={formatMessage({id: 'randomization.config.code'})} name="code"  rules={[{ required: true }]}>
                    <Input disabled={isEdit?isCopyData:false} placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear className="full-width"/>
                </Form.Item>
                <Form.Item label={formatMessage({id: 'common.name'})} name="name"  rules={[{ required: true }]}>
                    <Input disabled={isEdit?isCopyData:false} placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear className="full-width"/>
                </Form.Item>
                <Form.Item label={
                    <>
                        <FormattedMessage id='randomization.config.subGroup'/>
                    </>}>
                    <Switch 
                        disabled={isEdit?isCopyData:false} 
                        checked={isSubGroup} 
                        // onChange={setIsSubGroup}
                        onChange={(e) => {
                            setIsSubGroup(e);
                            if (isOldSubGroup !== e) {
                                setSend(false);
                            } else {
                                setSend(true);
                            }
                        }} 
                        size="small"
                    />
                </Form.Item>
                {
                    isSubGroup ?
                        <Form.Item style={{marginLeft:g.lang === "en"? "190px": "106px"}} name="subGroup" >
                        <Form.List name="subGroup">
                            {(fields,{ add, remove }) => (
                                <>
                                    {
                                        fields.map((field:any, index: any) => (
                                            <Space key={field.key}align="baseline" size={0}>
                                                <div style={{ display: 'flex', height:45, backgroundColor: "#F8F9FA", paddingTop: 7, paddingLeft: 7, marginRight: 10, marginBottom: 5}} >
                                                    <Form.Item
                                                        {...field}
                                                        name={[field.name, 'name']}
                                                        rules={[{ required: true,message: formatMessage({id: 'placeholder.input.common'}) + g.lang === "zh"?"":" "+ formatMessage({id: 'randomization.config.subGroup'}),whitespace:true}
                                                        ]}
                                                        style={{ width:g.lang === "en"? "270px": "350px" ,marginRight:"24px"}}
                                                    >
                                                        <Input disabled={(isEdit&&isCopyData)?subGroup[index]?.isCopyData:false} placeholder={formatMessage({id: 'placeholder.input.common'})} />
                                                    </Form.Item>
                                                    <Form.Item
                                                        {...field}
                                                        valuePropName='checked'
                                                        name={[field.name, 'blind']}
                                                        style={{width:"70px"}}
                                                    >
                                                        <Checkbox disabled={(isEdit&&isCopyData)?subGroup[index]?.isCopyData:false}>{formatMessage({id: 'projects.attributes.isBlind.blind'})}</Checkbox>
                                                    </Form.Item>
                                                </div>
                                                {
                                                    fields.length > 1 && !subGroup[index]?.isCopyData ?
                                                        <MinusCircleFilled style={{color: "#F96964", marginRight:"24px"}}  onClick={() => remove(field.name)}/>
                                                        :null
                                                }
                                            </Space>
                                        ))
                                    }
                                    {
                                            <Form.Item style={{ width: '100%'}}>
                                                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}className="mar-ver-5"><FormattedMessage id="common.addTo" /></Button>
                                            </Form.Item>
                                    }
                                </>
                            )}
                        </Form.List>
                    </Form.Item>
                        :null
                }
                <Form.Item label={formatMessage({id: 'common.status'})} name="status" className="mar-ver-5"
                           initialValue={1}>
                    <Radio.Group >
                        <Radio value={1} disabled={statusDisable||(isEdit?isCopyData:false)}>{formatMessage({id: "common.effective"})}</Radio>
                        <Radio value={2} disabled={group == null||(isEdit?isCopyData:false)}>{formatMessage({id: "common.invalid"})}</Radio>
                    </Radio.Group>
                </Form.Item>
            </Form>
        </Modal>

    );
};
