import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, message, Modal, Select} from "antd";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {randomListUpdate} from "../../../../api/randomization";
import {useGlobal} from "../../../../context/global";
import {userSites} from "../../../../api/subject";
import _ from "lodash";


export const RandomListUpdate = (props:any) => {

    const g = useGlobal()
    const auth = useAuth();
    const intl = useIntl();

    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [sites, setSites] = useSafeState([]);
    const [paramSites, setParamSites] = useSafeState<any>([])
    const [siteMode, setSiteMode] = useSafeState<any>({});
    const [siteOpen, setSiteOpen] = useSafeState(false);
    const [paramSitesOp, setParamSitesOp] = useSafeState<any>(1);
    const [search,setSearch] = useSafeState(0);
    const [id, setId] = useSafeState("");
    const [name, setName] = useSafeState("");
    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState([]);

    const projectId = auth.project.id;
    const envId = auth.env? auth.env.id:null;
    const customerId :any= auth.customerId;

    const [form] = Form.useForm();

    const { runAsync: randomListUpdateRun, loading: randomListUpdateLoading } = useFetch(randomListUpdate, {manual: true});
    const { runAsync: userSitesRun, loading: userSitesLoading } = useFetch(userSites,{ manual: true });

    const show = (item: any) => {
        setVisible(true);
        setSend(true);
        // 查询中心
        userSitesRun({
            projectId: projectId,
            customerId: customerId,
            envId: envId,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            setSites(result.data);
        });

        // 赋值
        if(item.site_ids == null || item.site_ids.length == 0){
            setParamSitesOp(1);
            setParamSites([]);
            setOldData([]);
            setSiteMode({});
        }else{
            setParamSitesOp(item.site_ids);
            setParamSites(item.site_ids);
            setOldData(item.site_ids);
            setSiteMode({mode: "multiple"});
        };
        setId(item._id);
        setName(item.name);
    };

    const hide = () => {
        setVisible(false);
        setParamSitesOp(1);
        setParamSites([]);
        setSiteMode({});
        setId("");
        setName("");
        setSend(true);
        setOldData([]);
    };

    const save = () => {
        randomListUpdateRun({id: id, siteIds: paramSites}).then(
            (data: any) => {
                if(data.code === 0){
                    message.success(data.msg);
                    props.refresh();
                    hide();
                }else{
                    message.success(data.msg);
                }
            }
        );
    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const formChange = (a: any, b: any) => {
        if(id){
            // console.log("1===" + JSON.stringify(a)); 
            // console.log("2===" + JSON.stringify(b)); 
            if (!arraysAreEqual(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }
        }
    };

    //比较两个数组是否相同
    function arraysAreEqual(arr1: any, arr2: any) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }


    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 4: 4 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 20: 20 },
        },
    }

    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id="common.edit" />}
                visible={visible}
                onCancel={hide}
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                okButtonProps={{disabled: id?send:false, loading:randomListUpdateLoading}}
                onOk={save}
                okText={formatMessage({id: 'common.ok'})}
            >
                <Form form={form} layout="horizontal" {...formItemLayout}>
                    <Form.Item label={formatMessage({ id: 'common.name' })} name="name">
                        {name}
                    </Form.Item>

                    <Form.Item label={<span style={{color:'#1D2129'}}><FormattedMessage id="common.site"/></span>} rules={[{ required: true }]}>
                        <Select
                            value={paramSitesOp}
                            {...siteMode}
                            open={siteOpen}
                            onBlur={() => {
                                setSiteOpen(false);
                                if (paramSitesOp !== null){
                                    setSearch(search + 1)};
                            }
                            }
                            onDropdownVisibleChange={(visible: boolean) => setSiteOpen(visible)}
                            onChange={(value: any) => {
                                if (value === 1 || (Array.isArray(value) && (value.find((i: any) => i === 1)|| value.length === 0))) {
                                    if (siteMode.mode != null){
                                        setSiteMode({});
                                        setSiteOpen(true);
                                    }
                                    setParamSites([]);
                                    setParamSitesOp(1);
                                    setSearch(search + 1);
                                    setSiteOpen(false);
                                    formChange(oldData, []);
                                } else {
                                    setSiteOpen(true);
                                    if (siteMode.mode !==  "multiple"){
                                        setSiteMode({mode: "multiple"});
                                        setSearch(search + 1);
                                        setSiteOpen(true);
                                    }
                                    if (!Array.isArray(value)) {
                                        let siteIds = [value];
                                        setParamSites(siteIds);
                                        setParamSitesOp(siteIds);
                                        formChange(oldData, siteIds);
                                    }else{
                                        setParamSites(value);
                                        setParamSitesOp(value);
                                        formChange(oldData, value);
                                    }
                                }
                            }}
                        >
                            <Select.Option value={1}>{formatMessage({id: "supply.plan.all.site"})}</Select.Option>
                            {
                                sites?.map((item: any) => {
                                    return <Select.Option value={item.id}>{item.number+"-"+item.name}</Select.Option>
                                })
                            }
                        </Select>
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    )
};