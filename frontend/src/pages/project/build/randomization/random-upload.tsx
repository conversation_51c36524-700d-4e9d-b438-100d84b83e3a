import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Button, Col, Form, Input, message, Modal, Row, Select, Upload} from "antd";
import {useAuth} from "../../../../context/auth";
import {useSafeState} from "ahooks";
import {useFetch} from "../../../../hooks/request";
import {downloadTemplate, getLastGroup, uploadRandomizationFiles} from "../../../../api/randomization";
import {useGlobal} from "../../../../context/global";
import {nilObjectId} from "../../../../data/data";
import {userSites} from "../../../../api/subject";
import {CloseCircleFilled, InfoCircleFilled, UploadOutlined} from "@ant-design/icons";


export const RandomUpload = (props:any) => {
    // const singleUpload = React.useRef<any>(null)

    const g = useGlobal()
    const auth = useAuth();
    const intl = useIntl();

    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [lastGroups, setLastGroups] = useSafeState<any>([]);
    const [sites, setSites] = useSafeState([]);
    const [paramSites, setParamSites] = useSafeState<any>([])
    const [siteMode, setSiteMode] = useSafeState<any>({});
    const [siteOpen, setSiteOpen] = useSafeState(false);
    const [paramSitesOp, setParamSitesOp] = useSafeState<any>(1);
    const [search,setSearch] = useSafeState(0);

    const projectId = auth.project.id;
    const envId = auth.env? auth.env.id:null;
    const cohortId = props.cohort? props.cohort.id:null;
    const customerId :any= auth.customerId;
    const lastId = props.cohort ? props.cohort.lastId : null;
    const projectType = auth.project.info.type;

    const [form] = Form.useForm();
    const groupTipError = (e:any, params:any) => {
        const contentType = e.headers.get("content-type");
        if (contentType && contentType.indexOf("application/json") > -1) {
            e.json().then(
                (result:any) => {
                    switch (result.code) {
                        case 1004:
                            Modal.confirm({
                                centered: true,
                                title: formatMessage({ id: "common.tips" }),
                                icon: <InfoCircleFilled style={{ color: "#4072e2" }} />,
                                content: <div style={{ height: g.lang === "zh"?20:40 }}>{result.msg}</div>,
                                onOk: () => {
                                    const payload = handle_files("true")
                                    if (payload.get('files') === 'null') {
                                        message.error(formatMessage({id: 'projects.randomization.confirmUpload'}));
                                        return
                                    }
                                    payload.append("siteIds",paramSites);
                                    uploadRandomizationFilesRun(payload).then(
                                        (data: any) => {
                                            message.success(data.msg);
                                            saveCallback();
                                            hide();
                                        }
                                    )
                                },
                                okText: formatMessage({ id: "common.ok" }),
                            });
                            break;
                        default:
                            message.error(result.msg).then()
                            break;
                    }
                }
            );
        }
    }
    const {runAsync: getLastGroupRun} = useFetch(getLastGroup, {manual: true})
    const {runAsync: uploadRandomizationFilesRun, loading:uploadRandomizationFilesLoading} = useFetch(uploadRandomizationFiles, {manual: true,onError:groupTipError})
    const {runAsync: downloadTemplateRun} = useFetch(downloadTemplate, {manual: true})
    const { runAsync: userSitesRun, loading: userSitesLoading } = useFetch(
        userSites,
        { manual: true }
    );

    const show = () => {
        setVisible(true);

        // 如果是在随机项目获取上一阶段组别信息
        if((projectType === 3 || (projectType === 2 && props.cohort?.type === 1)) && lastId != null && lastId !== nilObjectId){
            getLastGroupRun({projectId, envId, lastId}).then(
                (result:any) => {
                    let data = result.data;
                    const options :any = [];
                    if (data != null) {
                        data.forEach((it:any) => {
                            options.push({
                                label: it.name,
                                value: it.name
                            });
                        });
                    }
                    setLastGroups(options);
                }, () => {}
            )
        };

        // 查询中心
        userSitesRun({
            projectId: projectId,
            customerId: customerId,
            envId: envId,
            roleId: auth.project.permissions.role_id,
        }).then((result: any) => {
            setSites(result.data);
        });
    };

    const hide = () => {
        setVisible(false);
        // singleUpload.current.reset();
        setParamSitesOp(1);
        setParamSites([]);
        setSiteMode({});
        setFileList([])
        form.resetFields();

    };

    const validateFieldsForm = () => {
        form.validateFields().then(
            () =>{
                save()
            }
        ).catch(() => {})
    }

    const download_template = () => {
        downloadTemplateRun({envId:envId,cohortId:cohortId}).then()
    }

    const handle_files = (needSkip:any) => {
        const form_data = new FormData();
        form_data.append('env', envId);
        form_data.append('project', projectId);
        form_data.append('customer', customerId);
        form_data.append('cohort', cohortId);
        form_data.append('name',form.getFieldsValue().name);
        form_data.append('lastGroup', form.getFieldsValue().lastGroup);
        form_data.append('groupSize',form.getFieldsValue().groupSize);
        form_data.append('files', fileList.length > 0 ? fileList[0].originFileObj : null)
        form_data.append('needSkip',needSkip)
        return form_data;
    }
    const save = () => {
        const payload = handle_files("false")
        if (payload.get('files') === 'null') {
            message.error(formatMessage({id: 'projects.randomization.confirmUpload'}));
            return
        }
        payload.append("siteIds",paramSites);
        uploadRandomizationFilesRun(payload).then(
            (data: any) => {
                message.success(data.msg);
                saveCallback();
                hide();
            }
        )
    }
    const saveCallback = () => {
        props.refresh()
        form.resetFields()
        // singleUpload.current.reset()
    }

    React.useImperativeHandle(props.bind, () => ({ show }));

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 4: 4 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 20: 20 },
        },
    }
    const [fileList, setFileList] = useSafeState<any>([])


    const beforeUpload = (file: any) => {
        if (props.fileSize && props.fileSize > 0) {
            const isLimit = file.size / 1024 / 1024 < (props.fileSize || 50)
            if (!isLimit) {
                message.error(formatMessage({id:'common.upload.fileSize'}))
            }
        }
        return false
    }

    const onChange = (info: any) => {
        if (info.fileList && info.fileList.length > 0) {
            setFileList([info.fileList[0]])
        } else {
            setFileList([])
        }
    }

    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id="projects.randomization.upload" />}
                open={visible}
                onCancel={hide}

                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                okButtonProps={{loading:uploadRandomizationFilesLoading}}
                onOk={validateFieldsForm}
                okText={formatMessage({id: 'common.ok'})}
            >
                <Form form={form} layout="horizontal" {...formItemLayout}>
                    {
                        (projectType === 3 || (projectType === 2 && props.cohort?.type === 1)) && lastId != null && lastId !== nilObjectId?
                            <Form.Item label={formatMessage({ id: 'projects.randomization.last.group' })} name="lastGroup" rules={[{ required: true }]}>
                                <Select placeholder={formatMessage({id: 'placeholder.select.common'})} className="full-width"  options={lastGroups} />
                            </Form.Item>
                            : null
                    }
                    <Form.Item label={formatMessage({ id: 'common.name' })} name="name" rules={[{ required: true }]}>
                        <Input allowClear placeholder={formatMessage({id: 'placeholder.input.common'})}/>
                    </Form.Item>

                    <Form.Item label={<span style={{color:'#1D2129'}}><FormattedMessage id="common.site"/></span>} rules={[{ required: true }]}>
                        <Select
                            value={paramSitesOp}
                            {...siteMode}
                            open={siteOpen}
                            onBlur={() => {
                                setSiteOpen(false);
                                if (paramSitesOp !== null){
                                    setSearch(search + 1)};
                                }
                            }
                            onDropdownVisibleChange={(visible: boolean) => setSiteOpen(visible)}
                            onChange={(value: any) => {
                                if (value === 1 || (Array.isArray(value) && (value.find((i: any) => i === 1)|| value.length === 0))) {
                                    if (siteMode.mode != null){
                                        setSiteMode({});
                                        setSiteOpen(true);
                                    }
                                    setParamSites([]);
                                    setParamSitesOp(1);
                                    setSearch(search + 1);
                                    setSiteOpen(false);
                                } else {
                                    setSiteOpen(true);
                                    if (siteMode.mode !==  "multiple"){
                                        setSiteMode({mode: "multiple"});
                                        setSearch(search + 1);
                                        setSiteOpen(true);
                                    }
                                    if (!Array.isArray(value)) {
                                        let siteIds = [value];
                                        setParamSites(siteIds);
                                        setParamSitesOp(siteIds);
                                    }else{
                                        setParamSites(value);
                                        setParamSitesOp(value);
                                    }
                                }
                            }}
                        >
                            <Select.Option value={1}>{formatMessage({id: "supply.plan.all.site"})}</Select.Option>
                            {
                                sites?.map((item: any) => {
                                    return <Select.Option value={item.id}>{item.number+"-"+item.name}</Select.Option>
                                })
                            }
                        </Select>
                    </Form.Item>

                    <Form.Item label={formatMessage({ id: 'randomization.config.groupSize' })} name="groupSize" rules={[{ required: true }]}>
                        <Input className="full-width" placeholder={formatMessage({id: 'randomization.config.groupSize.placeholder'})} />
                    </Form.Item>
                    <Form.Item label={formatMessage({id: 'common.upload'})} name="files" required>
                        {/*<SingleUpload*/}
                        {/*    accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"*/}
                        {/*    bind={singleUpload}*/}
                        {/*    tips={*/}
                        {/*        <div style={{fontSize: 12, color: '#ADB2BA'}} onClick={e => e.stopPropagation()}>*/}
                        {/*            <span>{formatMessage({id: 'common.upload.excel.tip'})}{g.lang === 'zh' ? ', ' : ' '}</span><a onClick={download_template}>{formatMessage({ id: 'common.download.template' })}</a>{g.lang === 'zh' ? '。' : '.'}*/}
                        {/*        </div>*/}
                        {/*    }*/}
                        {/*    width={460}*/}
                        {/*/>*/}
                        <>

                            <Row>
                                <Col>
                                    <div style={{marginBottom: 8, color: '#ADB2BA'}}>
                                        <Upload
                                            accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                            fileList={fileList}
                                            beforeUpload={beforeUpload}
                                            onChange={onChange}
                                            showUploadList={{ removeIcon:<CloseCircleFilled />}}>
                                        <Button type='dashed' icon={<UploadOutlined/>}>
                                            {formatMessage({id: 'common.upload.add'})}</Button>
                                        </Upload>
                                    </div>

                                </Col>
                                <Col>
                                    <div  style={{marginLeft:6, marginTop: 6}} onClick={e => e.stopPropagation()}>
                                        <span style={{fontSize: 14, color: '#ADB2BA'}}>{formatMessage({id: 'common.upload.excel.tip2'})}{g.lang === 'zh' ? '，' : ' '}</span><a
                                        onClick={download_template}>{formatMessage({id: 'common.download.template'})}</a>{g.lang === 'zh' ? '。' : '.'}
                                    </div>
                                </Col>
                            </Row>
                                <Row>
                                    <Col style={{width:"100%"}}>
                                    <div style={{backgroundColor: '#F8F9FA',width:'100%',padding: 12}}>
                                        <p style={{padding: 0, margin: 0,color:"#677283"}}>{formatMessage({id: 'common.upload.excel.tip3'})}</p>
                                        <p style={{padding: 0, margin: 0,color:"#677283"}}>{formatMessage({id: 'common.upload.excel.tip4'})}</p>
                                        <p style={{padding: 0, margin: 0,color:"#677283"}}>{formatMessage({id: 'common.upload.excel.tip5'})}</p>
                                        <p style={{padding: 0, margin: 0,color:"#677283"}}>{formatMessage({id: 'common.upload.excel.tip6'})}</p>
                                    </div>
                                    </Col>
                                </Row>

                        </>

                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    )
};