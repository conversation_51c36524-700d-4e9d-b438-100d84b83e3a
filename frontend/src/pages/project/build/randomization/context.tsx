import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const ProjectRandomizationContext = React.createContext<{
        timeZone: number ;
        setTimeZone: (data: number) => void;
    }
    |
    null>(null);

export const  ProjectRandomizationProvider = ({children}: { children: ReactNode }) => {

    const [timeZone,setTimeZone] = useSafeState<number>(8);

    return (
        < ProjectRandomizationContext.Provider
            value={
                {
                    timeZone,setTimeZone,
                }
            }
        >
            {children}
        </ ProjectRandomizationContext.Provider>
    )
};

export const useProjectRandomization = () => {
    const context = React.useContext( ProjectRandomizationContext);
    if (!context) {
        throw new Error("useNotice must be used in NoticeContextProvider");
    }
    return context;
};

