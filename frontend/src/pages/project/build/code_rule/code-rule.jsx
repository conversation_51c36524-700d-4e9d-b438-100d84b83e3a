import React, {useEffect} from "react";
import {Button, Col, Form, Radio, Row, Spin} from "antd";
import {permissions, permissionsCohort} from "../../../../tools/permission";
import {useSafeState} from "ahooks";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {getBarcodeConfig, saveBarcodeConfig} from "../../../../api/barcode";
import {CustomConfirmModal} from "components/modal";
import {Title as CustomTitle} from "../../../../components/title";
import styled from "@emotion/styled";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";

export const CodeRule = (props) => {
  const [data, setData] = useSafeState({});
  const {formatMessage} = useTranslation()
  const [form] = Form.useForm();
  const auth = useAuth();
  const projectId = auth.project.id;
  const envId = auth.env ? auth.env.id : null;
  const projectStatus = auth.project.status ? auth.project.status : 0;
  const customerId = auth.customerId;
  const [disabled, setDisabled] = useSafeState({ disabled: true });
  const [refresh, setRefresh] = useSafeState(0);
  const { runAsync: run_getBarcodeConfig, loading } = useFetch(
    getBarcodeConfig,
    { manual: true }
  );
  const { runAsync: run_saveBarcodeConfig } = useFetch(saveBarcodeConfig, {
    manual: true,
  });

  useEffect(() => {
    setDisabled({
      disabled: !permissionsCohort(
        auth.project.permissions,
        "operation.build.code-rule.edit",
          props.cohort?props.cohort.status:0
      ),
    });
    run_getBarcodeConfig({
      projectId: projectId,
      customerId: customerId,
      envId: envId,
      cohortId: props.cohortId ? props.cohortId : null,
    }).then((response) => {
      let result = response.data;
      form.setFieldsValue({ codeRule: result.codeRule });
      setData(result);
      setDisabled({
        disabled:
          result.codeConfigInit ||
          !permissionsCohort(
            auth.project.permissions,
            "operation.build.code-rule.edit", props.cohort?props.cohort.status:0
          ) || projectStatus === 2,
      });
    });
  }, [refresh]);

  const onSaveBarcodeConfig = () => {
    form.validateFields().then((values) => {
      CustomConfirmModal({
        title: formatMessage({
          id: "projects.attributes.code.rule.confirm.title",
        }),
        content: formatMessage({
          id: "projects.attributes.code.rule.confirm.msg",
        }),
        okText: formatMessage({ id: "common.ok" }),
        onOk: () =>
          run_saveBarcodeConfig({ barcodeRuleId: data.id }, { ...values })
            .then(() => {
              auth.setCodeRule(values["codeRule"]);
              setRefresh(refresh + 1);
            })
            .catch(() => {}),
      });
    });
  };
  return (
    <React.Fragment>
      <Spin spinning={loading}>
        {loading ? null : permissions(
            auth.project.permissions,
            "operation.build.attribute.view"
          ) ? (
          <CodeRuleWrap
            style={{
              height:
                auth.project.info.type === 1
                  ? "calc(100vh - 100px)"
                  : "calc(100vh - 160px)",
            }}
          >
            <Form form={form} layout="vertical" className="mar-top-8">
              {/*编码配置*/}
              <CustomTitle
                name={formatMessage({
                  id: "projects.attributes.code.config",
                })}
              />
              <Row gutter={24}>
                <Col>
                  <Form.Item className="mar-ver-5" name="codeRule">
                    <Radio.Group {...disabled}>
                      <Radio value={0}>
                        <FormattedMessage id="projects.attributes.code.rule.manual" />
                      </Radio>
                      <Radio value={1}>
                        <FormattedMessage id="projects.attributes.code.rule.auto" />
                      </Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
            {projectStatus !== 2 && !disabled.disabled ? (
              <Row justify="end" className="code-footer">
                <Button type="primary" onClick={onSaveBarcodeConfig}>
                  {formatMessage({ id: "common.save" })}
                </Button>
              </Row>
            ) : null}
          </CodeRuleWrap>
        ) : null}
      </Spin>
    </React.Fragment>
  );
};

const CodeRuleWrap = styled.div`
  position: relative;

  .code-footer {
    border-top: 1px solid #e3e4e6;
    position: absolute;
    bottom: 0;
    right: -24px;
    width: calc(100% + 48px);
    padding: 16px 24px 0 0;
  }
`;
