import React, { useEffect, useState } from "react";
import { Dropdown, Menu, Row, Tooltip } from "antd";
import { matchPath, useLocation, useNavigate } from "react-router-dom";
import { auth_project, menu_project } from "../../../data/menu";
import styled from "@emotion/styled";
import { FormattedMessage, useIntl } from "react-intl";
import {
  BuildOutlined,
  HomeOutlined,
  MedicineBoxOutlined,
  SettingOutlined,
  SolutionOutlined,
} from "@ant-design/icons";
import { useSafeState } from "ahooks";
import { useAuth } from "../../../context/auth";
import { LayoutContext } from "./context";
import { useGlobal } from "../../../context/global";
import { TooltipParagraph } from "components/TooltipParagraph";
import {getProjectAttributes} from "../../../api/randomization";
import {useFetch} from "../../../hooks/request";

export const Nav = () => {
  const g = useGlobal();
  const intl = useIntl();
  const { formatMessage } = intl;
  const navigate = useNavigate();
  const location = useLocation();
  const [menu, setMenu] = useSafeState([]);
  const auth = useAuth();
  const projectType = auth.project.info.type;
  const connectEdc = auth.project ? auth.project.info.connect_edc : null;
  const pushMode = auth.project ? auth.project.info.push_mode : null;

    const { runAsync: runGetProjectAttributesAttr } = useFetch(getProjectAttributes, { manual: true });


    useEffect(() => {
      runGetProjectAttributesAttr({env:auth.env.id}).then(
        (value) => {
          // 先过滤掉value 中的脏数据
          let retCohorts = [];
          if (projectType == 1) {
            retCohorts = value.data
          } else {
            value.data.forEach((cohort) => {
              let matchedData = auth.env.cohorts.find((item) => item.id === cohort.cohortId);
              if (matchedData) {
                retCohorts.push(cohort);
              }
            });
          }
          let random = false;
          let dispensing  = false;
          if (retCohorts?.find((item)=>item.info.random === true)){
              random = true
          }
          if (retCohorts?.find((item)=>item.info.dispensing === true)){
              dispensing = true
          }
          auth.setIsRandomDispensing({random,dispensing})
        }
      )
    }, [auth.project.permissions, auth.env, auth.attributeUpdate]);

  useEffect(() => {
    let m = auth_project(
      menu_project,
      auth.project.permissions ? auth.project.permissions.page : []
    );
    if (!auth.isRandomDispensing.dispensing) {
      m = auth_dispensing_menu(m);
    }
    if (!auth.isRandomDispensing.random) {
      m = auth_random_menu(m);
    }

    if (connectEdc === 2 || (connectEdc === 1 && pushMode === 1)) {
      m = auth_push_menu(m);
    }
    setMenu([...m]);
  }, [auth.project.permissions, auth.isRandomDispensing]);

  const auth_dispensing_menu = (menu) => {
    const hideMenu = [
      "menu.projects.project.supply",
      "menu.projects.project.supply.storehouse",
      "menu.projects.project.supply.storehouse.summary",
      "menu.projects.project.supply.storehouse.single",
      "menu.projects.project.supply.drug",
      "menu.projects.project.supply.drug.order",
      "menu.projects.project.supply.drug.single",
      "menu.projects.project.supply.drug.no_number",
      "menu.projects.project.supply.site",
      "menu.projects.project.supply.site.summary",
      "menu.projects.project.supply.site.single",
      "menu.projects.project.supply.site.no_number",
      "menu.projects.project.supply.shipment",
      "menu.projects.project.supply.drug_recovery",
      "menu.projects.project.supply.release-record",
      "menu.projects.project.build.plan",
      "menu.projects.project.build.storehouse",
      "menu.projects.project.subject.visit.cycle",
        "menu.projects.project.build.drug"
    ];
    menu.forEach((it) => {
      if (!it.children) {
        // 一级菜单
        if (hideMenu.findIndex((m) => m === it.text) > -1) {
          it.show = false;
        }
      } else {
        // 二级菜单
        if (hideMenu.findIndex((m) => m === it.text) > -1) {
          it.show = false;
        }
        it.children.forEach((child) => {
          if (hideMenu.findIndex((m) => m === child.text) > -1) {
            child.show = false;
          }
        });
      }
    });
    return menu;
  };

  const auth_random_menu = (menu) => {
    const hideMenu = ["menu.projects.project.build.simulate_random"];
    menu.forEach((it) => {
      if (!it.children) {
        // 一级菜单
        if (hideMenu.findIndex((m) => m === it.text) > -1) {
          it.show = false;
        }
      } else {
        // 二级菜单
        if (hideMenu.findIndex((m) => m === it.text) > -1) {
          it.show = false;
        }
        it.children.forEach((child) => {
          if (hideMenu.findIndex((m) => m === child.text) > -1) {
            child.show = false;
          }
        });
      }
    });
    return menu;
  };

  const auth_push_menu = (menu) => {
    const hideMenu = ["menu.projects.project.build.push"];
    menu.forEach((it) => {
      if (!it.children) {
        // 一级菜单
        if (hideMenu.findIndex((m) => m === it.text) > -1) {
          it.show = false;
        }
      } else {
        // 二级菜单
        if (hideMenu.findIndex((m) => m === it.text) > -1) {
          it.show = false;
        }
        it.children.forEach((child) => {
          if (hideMenu.findIndex((m) => m === child.text) > -1) {
            child.show = false;
          }
        });
      }
    });
    return menu;
  };

  // 页面刷新时，控制Menu打开和选中的项
  const menu_default_keys = () => {
    let defaultOpenKeys = [];
    let defaultSelectedKeys = [];
    // const path = window.location.pathname;
    const path = location.pathname;
    if (defaultOpenKeys.length === 0 && defaultSelectedKeys.length === 0) {
      menu_project
        .filter((it) => !it.children && it.show)
        .forEach((item) => {
          if (path === item.path) {
            defaultOpenKeys = [];
            defaultSelectedKeys = [item.path];
          }
        });
    }
    if (defaultOpenKeys.length === 0 && defaultSelectedKeys.length === 0) {
      menu_project
        .filter((it) => it.children && it.show)
        .forEach((it) => {
          it.children.forEach((child) => {
            const match = matchPath(path, it.path + child.path);
            if (match != null) {
              defaultOpenKeys = [it.path];
              defaultSelectedKeys = [it.path + child.path];
            }
          });
        });
    }
    return [defaultOpenKeys, defaultSelectedKeys];
  };

  const rootSubmenuKeys = menu
    .filter((it) => !!it.children)
    .map((it) => it.path);

  const [openKeys, setOpenKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useSafeState(null);
  const layout_context = React.useContext(LayoutContext);

  React.useEffect(() => {
    setOpenKeys(menu.map((item) => item.path));

    setSelectedKeys(menu_default_keys()[1]);
  }, [location.pathname, menu, layout_context.collapsed]);

  const onOpenChange = (keys) => {
    setOpenKeys(keys);
  };
  const icons = {
    home: <HomeOutlined />,
    subject: <SolutionOutlined />,
    supply: <MedicineBoxOutlined />,
    build: <BuildOutlined />,
    setting: <SettingOutlined />,
  };

  return (
    <>
      <LayoutMenu
        theme="light"
        mode="inline"
        selectedKeys={selectedKeys}
        openKeys={openKeys}
        onOpenChange={onOpenChange}
        onSelect={(item) => setSelectedKeys([item.key])}
      >
        {menu
          .filter((m) => m.show)
          .map((it) =>
            it.children ? (
              <Menu.SubMenu
                key={it.path}
                // openKeys={openKeys}
                // onOpenChange={onOpenChange}
                icon={icons[it.icon]}
                title={<FormattedMessage id={it.text} />}
              >
                {!!layout_context.collapsed && (
                  <Row
                    style={{
                      margin: "0px 16px",
                      lineHeight: "40px",
                      color: "#93969B",
                      fontSize: 12,
                    }}
                  >
                    <FormattedMessage id={it.text} />
                  </Row>
                )}
                {it.children
                  .filter((i) => i.show)
                  .map((child) => (
                    <Menu.Item
                      key={it.path + child.path}
                      onClick={() => navigate(it.path + child.path)}
                    >
                      <TooltipParagraph
                        tooltip={formatMessage({ id: child.text })}
                        outStyle={{ color: "inherit" }}
                      >
                        {formatMessage({ id: child.text })}
                      </TooltipParagraph>
                    </Menu.Item>
                  ))}
              </Menu.SubMenu>
            ) : (
              <Menu.Item
                className="ant-custom-menu"
                key={it.path}
                // openKeys={openKeys}
                // onOpenChange={onOpenChange}
                onClick={() => navigate(it.path)}
                // onClick={!layout_context.collapsed?() => navigate(it.path):null}
                title={false}
              >
                <Dropdown
                  disabled={!layout_context.collapsed}
                  placement="right"
                  trigger={["hover"]}
                  dropdownRender={() => (
                    <TooltipWrap onClick={(e) => e.stopPropagation()}>
                      {<FormattedMessage id={it.text} />}
                    </TooltipWrap>
                  )}
                >
                  {icons[it.icon]}
                </Dropdown>
                <span>{<FormattedMessage id={it.text} />}</span>
              </Menu.Item>
            )
          )}
      </LayoutMenu>
      <div>
        <Prompt>
          <Tooltip
            placement="top"
            title={
              layout_context.collapsed ? (
                <FormattedMessage id="common.expand" />
              ) : (
                <FormattedMessage id="common.collapse" />
              )
            }
          >
            <Toggle
              className="nav-toggle"
              onClick={layout_context.toggle}
              style={{
                display: g.hover ? "block" : "none",
                cursor: " pointer",
              }}
            >
              <Footer
                style={{
                  width: "24px",
                  height: "24px",
                  borderRadius: "50%",
                  backgroundColor: "#fff",
                  position: "relative",
                  boxShadow: "0 2px 6px rgba(0, 0, 0, 0.25)",
                  transform: layout_context.collapsed
                    ? "rotate(0)"
                    : "rotate(180deg)",
                }}
              >
                <svg
                  t="1684896289855"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="2908"
                  width="24px"
                  height="24px"
                  style={{
                    position: "absolute",
                    left: "50%",
                    top: "50%",
                    transform: "translate(-50%, -50%)",
                  }}
                  className="iconfont"
                >
                  <path
                    d="M0 512a512 512 0 1 0 1024 0A512 512 0 1 0 0 512z"
                    fill="#FFFFFF"
                    p-id="2909"
                  ></path>
                  <path
                    d="M448 392.533c-12.8-12.8-12.8-29.866 0-42.666 12.8-12.8 29.86699999-12.8 42.667 0l145.066 145.066c17.067 17.067 17.067 38.4 0 55.467L490.667 695.467c-12.8 12.8-29.86699999 12.8-42.667 0s-12.8-29.86699999 0-42.66699999l128.00000001-128.00000001-128.00000001-132.267z"
                    fill="#3370FF"
                    p-id="2910"
                  ></path>
                  <path
                    d="M469.333 712.533c-8.53299999 0-21.333-4.266-25.6-12.8-17.066-17.066-17.066-38.4 0-55.466l123.734-123.734L443.733 396.8c-17.066-12.8-17.066-38.4 0-51.2 17.067-17.067 38.4-17.067 55.467 0l145.067 145.067c8.533 8.533 12.8 21.333 12.80000001 34.133s-8.534 21.333-17.06700001 29.867L494.933 699.733c-4.266 8.534-17.066 12.8-25.6 12.8z m-17.066-324.266L588.8 524.79999999 452.26699999 661.333c-8.534 8.534-8.534 21.334 1e-8 29.867 8.533 8.533 21.333 8.533 29.866-1e-8L627.2 546.133c4.267-4.26599999 8.53299999-12.8 8.533-21.333s-4.26599999-17.067-8.533-21.333L482.13300001 358.4c-8.533-8.533-21.333-8.533-29.86600001 0-4.267 4.267-4.267 21.333 0 29.867z"
                    fill="#3370FF"
                    p-id="2911"
                  ></path>
                </svg>
              </Footer>
            </Toggle>
          </Tooltip>
        </Prompt>
      </div>
    </>
  );
};

const LayoutMenu = styled(Menu)`
  border-right: none !important;
  max-height: calc(100vh - 50px);
  overflow-y: auto;
  overflow-x: hidden;

  .ant-menu-sub.ant-menu-inline {
    background: #fff;
  }

  &.ant-menu.ant-menu-inline-collapsed {
    width: 100%;

    > .ant-menu-item,
    > .ant-menu-submenu > .ant-menu-submenu-title {
      padding: 0 16px !important;
    }
  }
`;

const Prompt = styled.div`
  position: absolute;
  top: 70px;
  right: -10px;
`;

const Footer = styled.div`
  position: absolute;
  top: -6px;
  right: 0px;
  // z-index: 99;
`;
const Toggle = styled.span`
  cursor: pointer;
  color: #88909b;
  font-size: 18px;
  transition: color 0.3s;
`;

const TooltipWrap = styled.div`
  padding: 10px 28px;
  position: relative;
  right: -18px;
  background: #fff;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.16);
  border-radius: 4px;
  color: #93969b;
  font-size: 12px;

  .ant-btn-text {
    color: inherit;
    padding: 0;
    height: unset;
    font-size: 12px;
    cursor: unset;
  }
`;
