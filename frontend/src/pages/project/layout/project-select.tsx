import styled from "@emotion/styled"
import {Dropdown, Empty, Input, Spin} from "antd"
import {SearchOutlined} from "@ant-design/icons"
import {useDebounce, useSafeState, useUpdateEffect} from "ahooks"
import {BeradcrumbItemUI, Triangle, UpTriangle} from "pages/common/ui"
import {useAuth} from "context/auth"
import {useFetch} from "hooks/request"
import {getProjectApi, getUserProjectEnvironmentRoles} from "api/user"
import {useCallback} from "react"
import {menuSelect} from "../../../tools/permission";
import {useNavigate} from "react-router-dom";

export const Project = () => {
    const auth = useAuth();
    const project = auth.project
    const [keyword, setKeyword] = useSafeState<string | null>(null);
    const debounceValue = useDebounce(keyword, { wait: 500 });
    const [projectMenuVisible, setProjectMenuVisible] = useSafeState(false);
    const [itemList, setItemList] = useSafeState<any[]>([]);
    const navigate = useNavigate();
    const { run: getProjects, loading } = useFetch(
        getProjectApi,
        {
            manual: true,
            onSuccess: (resp: any) => {
                if (!resp.data) {
                    setItemList([]);
                    return;
                }
                const data = resp.data as any[];
                const p = JSON.parse(sessionStorage.getItem("current_project")!);
                p.customer_id = auth.customerId
                const index = data.findIndex(it => it.id === p.id);
                // 当没有关键字搜索的时候，需要确保列表中的第一条数据是当前选中的数据
                if (!keyword) {
                    if (index !== -1) {
                        data.splice(index, 1);
                    }
                    data.unshift(p);
                } else {
                    // 如果含有关键字，且结果含有当前的项目Id,则将其置顶
                    if (index !== -1) {
                        data.splice(index, 1);
                        data.unshift(p);
                    }
                }
                setItemList(data);
            }
        })
    useUpdateEffect(() => {
        if (!projectMenuVisible) {
            setKeyword(null);
            return;
        }
        getProjects(debounceValue);
    }, [projectMenuVisible, debounceValue])

    const { runAsync: roleRunAsync } = useFetch(getUserProjectEnvironmentRoles, { manual: true })


    const onSelect = useCallback((p: any) => {
        // 修改 customer_id
        auth.setCustomerId(p.customer_id)
        sessionStorage.setItem("customerId", p.customer_id)

        // 缺少权限异常
        let env: any = {}
        let cohort: any = null
        if (p.envs.find((item: any) => item.name === auth.env.name)) {
            env = p.envs.find((item: any) => item.name === auth.env.name)
        } else {
            env = p.envs[0]
        }
        if (env.cohorts && env.cohorts.length > 0) {
            if (auth.cohort != null) {
                cohort = env.cohorts.find((item: any) => auth.cohort.name === item.name)
            }
            if (cohort === undefined || cohort === null) {
                cohort = env.cohorts[0]
            }
        }
        // 获取权限信息 写入state session
        setRole(p, env, cohort)
    }, [])

    const setRole = (p: any, env: any, cohort: any) => {
        let permissions: any = {}

        roleRunAsync({
            customerId: p.customer_id,
            projectId: p.id,
            envId: env.id,
            cohortId: cohort?.id
        }).then(
            (result: any) => {
                result.data = result.data.filter((it: any) => it.name !== "Customer-Admin" && it.name !== "Project-Admin" && it.name !== "Sys-Admin")
                let permission = result.data.find((item: any) => auth.project.permissions.role === item.role)
                if (permission) {
                    permissions = permission

                } else {
                    permissions = result.data[0]

                }
                p.permissions = permissions
                 // 修改权限 project
                auth.setProject(p);
                //  修改 env
                auth.setEnv(env)
                //  修改 cohort
                auth.setCohort(cohort)
                sessionStorage.setItem("current_project", JSON.stringify(p))
                sessionStorage.setItem("env", JSON.stringify(env));
                sessionStorage.setItem("cohort", JSON.stringify(cohort));
                setProjectMenuVisible(false);

                //  重新加载页面
                let path = menuSelect(permissions)
                navigate(path);
                window.location.reload();
            }
        )
    }
    const menu = (
        <MenuContainer>
            <HeaderContainer>
                <Input
                    style={{ marginBottom: 8, width: 220 }}
                    placeholder="请输入"
                    value={keyword as string}
                    onChange={(e) => { setKeyword(e.target.value) }}
                    suffix={<SearchOutlined style={{ color: "#BFBFBF" }}></SearchOutlined>}
                />
            </HeaderContainer>
            <BodyContainer>
                <Spin spinning={loading}>
                    {itemList && itemList.length !== 0
                        ? (
                            <SelectListContainer>
                                {itemList?.map(item => (
                                    <SelectItemContainer key={item.id} onClick={() => { onSelect(item) }}>
                                        <ItemBody>
                                            <div className={(auth?.project?.id === item?.id) ? "project-selected" : undefined}>
                                                <ItemNumber>{item?.info?.number}</ItemNumber>
                                                <ItemName>{item?.info?.name}</ItemName>
                                            </div>
                                            {auth?.project?.id === item?.id && <CheckIcon></CheckIcon>}
                                        </ItemBody>
                                    </SelectItemContainer>
                                ))}
                            </SelectListContainer>
                        )
                        : <Empty></Empty>
                    }
                </Spin>
            </BodyContainer>
        </MenuContainer>
    )


    return (
        <>
            <Dropdown
                visible={projectMenuVisible}
                onVisibleChange={v => setProjectMenuVisible(v)}
                trigger={['click']}
                overlay={menu}
            >
                <span className="mouse" style={{ display: "flex", alignItems: "center" }}>
                    <BeradcrumbItemUI><span style={{color:"#1D2129" }}>{project.info.number}</span></BeradcrumbItemUI>
                    <span style={{ marginLeft: 4 }}>
                        {!projectMenuVisible
                            ? <Triangle></Triangle>
                            : <UpTriangle></UpTriangle>}
                    </span>
                </span>

            </Dropdown>

        </>
    )
}


const MenuContainer = styled.div`
    position: absolute;
    width: 256px;
    background: #FFFFFF;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.2);
    border-radius: 2px;
`

const HeaderContainer = styled.div`
    padding: 16px 16px 0px 16px;
`
const BodyContainer = styled.div`
    padding: 0px 12px 16px 16px;
`

const SelectListContainer = styled.div`
    max-height: 390px;
    overflow-y: auto;
    ::-webkit-scrollbar {
        width: 4px;
    }
    ::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: rgba(0, 0, 0, 0.1);
        -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
    }   
`

const SelectItemContainer = styled.div`
    height: 58px;
    width: 220px;
    border-radius: 2px;
    background: #F6F7F9;
    margin-bottom: 8px;
    cursor: pointer;
    &:hover *, .project-selected *{
        color: #165DFF !important;
    }
    :last-of-type {
        margin-bottom: 2px;
    }
`

const ItemBody = styled.div`
    padding: 8px;
    display: flex;
    align-items: center;
`
const ItemNumber = styled.div`
    color: #1D2129;
    font-weight: 600;
`

const ItemName = styled.div`
    color: #4E5969;
    font-weight: 400;
    max-width: 176px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
`


const CheckIcon = styled.div`
    display: inline-block;
    margin-left: auto;
    width: 8px;
    height: 16px;
    border-right: 2px solid #165DFF;
    border-bottom: 2px solid #165DFF;
    transform: rotate(40deg);
`