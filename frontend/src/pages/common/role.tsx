import {useSafeState} from "ahooks";
import {Dropdown, Menu, message, Space, Spin} from "antd";
import {getUserProjectEnvironmentRoles} from "api/user";
import {useAuth} from "context/auth";
import {useFetch} from "hooks/request";
import {useCallback, useEffect} from "react";
import {Triangle, UpTriangle} from "./ui";
import {menuSelect} from "../../tools/permission";
import {useNavigate} from "react-router-dom";
import {useIntl} from "react-intl";

interface Role {
    role: string;
    role_id: string;
}
export const RoleDropdown = () => {
    const auth = useAuth();
    const intl = useIntl();
    const [visible, setVisible] = useSafeState<boolean>(false);
    const [options, setOptions] = useSafeState<Role[]>([]);
    const { runAsync: roleRunAsync, loading } = useFetch(getUserProjectEnvironmentRoles, {manual: true}
    )
    const getRoles = useCallback(() => {
        setOptions([]);
        roleRunAsync({
            customerId: auth.customerId,
            projectId: auth.project?.id,
            envId: auth.env?.id,
        }).then((resp:any)=> setOptions(resp.data))
    }, [auth.customerId, auth.project, auth.env]);

    useEffect(() => {
        if (visible) {
            getRoles();
        }
    }, [getRoles, visible]);
    const navigate = useNavigate();

    const onSelect = useCallback((r: any) => {
        if (r.page === undefined){
            message.success(intl.formatMessage({ id: "project.noPermission" }));
            return
        }
        setVisible(false);
        //  刷新权限
        let project: any = {}
        project = auth.project
        project.permissions = r
        auth.setProject(project)
        //  更新 sessions
        sessionStorage.setItem("current_project", JSON.stringify(project))

        //  重新加载页面
        let path = menuSelect(r)
        navigate(path);
        window.location.reload();
    }, [])
    const menu = (
        <Menu>
            <Spin spinning={loading}>
                {options.map(item => (
                    <Menu.Item
                        key={item.role_id}
                        onClick={() => { onSelect(item) }} style={{color:auth.project.permissions.role === item.role?"#165DFF":"#1d2129"}}>
                        {item.role}
                    </Menu.Item>))}
            </Spin>
        </Menu>
    );
    return (
        <div style={{ marginLeft: 12, display: "flex", alignItems: "center" }}>
            {/* <svg className="iconfont" width={22} height={22}>
                <use xlinkHref="#icon-jiaose"></use>
            </svg> */}
            <Dropdown
                transitionName=''
                visible={visible && !loading}
                trigger={['click']}
                onVisibleChange={v => setVisible(v)}
                overlay={menu}
            >
                {/*<div style={{ padding: '0 4px', color: "#1D2129" }} className="mouse">*/}
                <Space style={{ padding: '0 4px', color: "#1D2129"}} className="mouse">
                    {auth.project?.permissions?.role}
                    {!visible
                        ? <Triangle></Triangle>
                        : <UpTriangle></UpTriangle>}
                    </Space>

                {/*</div>*/}
            </Dropdown>
            {/*{<Triangle*/}
            {/*    // onClick={() => {setVisible(!visible)}}*/}
            {/*           onMouseOver={() => {setVisible(!visible)}}></Triangle>}*/}

        </div>
    )
}