import React, {ReactNode, useState} from 'react';
import {Typography} from 'antd';
import {ParagraphProps} from 'antd/lib/typography/Paragraph';
import {FormattedMessage} from "react-intl";
import {useFetch} from "../../hooks/request";
import {readOperation} from "../../api/page_notice";

export interface ILabelValueProps {
  label?: ReactNode;
  value: ReactNode;
  valueConfig?: ParagraphProps;
  colon?: boolean;
  tooltip?: boolean;
  fun?:string;
}
type Props = ILabelValueProps;

const { Paragraph } = Typography;

export function LabelValue(props: Readonly<Props>) {
  const { label, value, colon, tooltip, valueConfig,fun } = props;
  const rows = tooltip ? 1 : 3;
  const [key, setKey] = useState(0);
  const [fold, setFold] = useState(true);

  const { runAsync: run_read } = useFetch(readOperation, { manual: true });

//   const onRead = (fun) => {
//     run_read(fun).then(() => {

//   })
// }


  const onExpand = () => {
    // onRead(fun);
    setFold(false);
  };
  const onCollapse = () => {
    setFold(true);
    setKey(key + 1);
  };
  return (
    <>
      <div key={key}>
        <Paragraph
          ellipsis={{
            rows,
            expandable: !tooltip,
            onExpand: onExpand,
          }}
          {...valueConfig}
        >
          {value}
          {!fold && (
            <span className="value-collapse" onClick={onCollapse}>
              {<FormattedMessage id="common.collapse" />}
            </span>
          )}
        </Paragraph>
      </div>
    </>
  );
}

export default LabelValue;