import React from 'react';
import {Col, Row, Table} from "antd";
import {FormattedMessage} from "react-intl";
import moment from "moment";
import {useAuth} from "../../context/auth";

export const OperationListTable = (props) =>{
    const auth = useAuth()
    
    return (
        <React.Fragment>
            <div
                className="mar-all-10">

            <Row className={"history_title"}>{<FormattedMessage id={props.keys+""} />}</Row>
            <Row>{<FormattedMessage id="projects.name" />}:{auth.project.info.name}</Row>
            <Row>{<FormattedMessage id="projects.sponsor" />}:{auth.project.info.sponsor}</Row>

            <Row className="mar-top-10">
                <Col span={8}>{<FormattedMessage id="history.sign" />}:</Col>
                <Col span={8}>{<FormattedMessage id="history.date" />}:</Col>
            </Row>

            <Table
                loading={props.loading}
                style={{height: props.tableHeight}}
                className="mar-top-10"
                size="small"
                dataSource={props.data}
                rowKey={(record) => (record.id)}
                pagination={false}
            >
                <Table.Column title={<FormattedMessage id="common.serial"/>} dataIndex="#" key="#" width={42} render={(text, record, index) => (index + 1)} />
                <Table.Column title={<FormattedMessage id="common.operator"/>} key="user" dataIndex="user"
                    width={200} align="left" ellipsis
                    render={
                        (value, record, index) => (
                            <div>
                                {record.user_name}<br></br>
                            </div>
                        )
                    }/>
                    <Table.Column
                        title={<FormattedMessage id="common.operation.time"/>}
                        key="time"
                        dataIndex="time"
                        align="left"
                        width={150}
                        render={
                            (value, record, index) => (
                                (value === null || value === 0) ? '' : moment.unix(value).utc().add(props.timeZone, "hour").format('YYYY-MM-DD HH:mm:ss')
                            )
                        }/>
                    <Table.Column title={<FormattedMessage id="common.operation.type"/>} key="operation_type"
                                dataIndex="operation_type" align="left" ellipsis width={80}
                    />
                    <Table.Column title={<FormattedMessage id="common.operation.content"/>} key="fields"
                                dataIndex="fields" align="left" ellipsis
                                render={
                                    (value, record, index) => (
                                        value && value.map(item =>
                                            <div
                                        style={{ whiteSpace:'pre-line', width:'300px' }}
                                    >
                                        {item}
                                    </div>
                                        )
                                    )
                                }
        
                    />
            </Table>
            </div>

        </React.Fragment>
    );
}