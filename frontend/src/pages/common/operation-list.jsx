import React from "react";
import { FormattedMessage } from "react-intl";
import { <PERSON><PERSON>, Col, ConfigProvider, Empty, Modal, Pagination, Row, Table } from "antd";
import ReactToPrint from "react-to-print";

import { OperationListTable } from "./operation-list-table"
import moment from "moment";
import { useSafeState } from "ahooks";
import { useFetch } from "../../hooks/request";
import { getOperationList } from "../../api/history";
import { useAuth } from "../../context/auth";
import { fillTableCellEmptyPlaceholder } from "../../components/table";
import {useGlobal} from "../../context/global";

import EmptyImg from 'images/empty.png';
export const OperationList = (props) => {
    const auth = useAuth();
    const g = useGlobal();
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const [visible, setVisible] = useSafeState(false);
    const [key, setKey] = useSafeState(false);
    const [id, setId] = useSafeState("");
    const [oid, setOid] = useSafeState("");
    const [timeZone, setTimeZone] = useSafeState(null);
    const [currentPage, setCurrentPage] = useSafeState(1);
    const [pageSize, setPageSize] = useSafeState(20);
    const [total, setTotal] = useSafeState(0);
    const [data, setData] = useSafeState([]);
    const [loading, setLoading] = useSafeState(false);
    const [tableHeight, setTableHeight] = useSafeState(300);
    const [refresh, setRefresh] = useSafeState(0);
    const [print, setPrint] = useSafeState(true);
    const { runAsync } = useFetch(getOperationList, { manual: true })
    const show = (key, oid, id, timeZone, siteTimeZone) => {
        if (props.permission !== undefined) {
            setPrint(props.permission)
        }
        setVisible(true);
        setLoading(true);
        setKey(key);
        setId(id);
        setOid(oid);
        if (siteTimeZone) {
            setTimeZone(siteTimeZone)
        } else {
            setTimeZone(auth.project.info.timeZone !== undefined ? auth.project.info.timeZone : 8);
        }

        setRefresh(refresh + 1)
    };

    const hide = () => {
        setPrint(true)
        setData([]);
        setTotal(0);
        setVisible(false);
    };

    React.useEffect(
        () => {
            setTableHeight(document.documentElement.clientHeight - 130);
        },
        []
    );

    const getList = (info) => {
        runAsync({
            projectId: projectId,
            envId: envId,
            oid: oid,
            operatorID: id,
            start: (currentPage - 1) * pageSize,
            limit: pageSize,
            roleId: auth.project.permissions.role_id,
            module: key,
            keyword: info ? info : ""
        }).then((result) => {
            let data = result.data
            setTotal(data.total);
            setData(fillTableCellEmptyPlaceholder(data.items));
            setLoading(false);
        });
    }

    React.useEffect(
        () => {
            if (visible) {
                getList()
            }
        }, [refresh, currentPage, pageSize, projectId, envId]
    )
    let componentRef = React.useRef();
    React.useImperativeHandle(props.bind, () => ({ show }));
    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id={"common.history"} />}
                visible={visible}
                onCancel={hide}
                centered
                maskClosable={false}

                destroyOnClose={true}
                className="custom-large-modal"
                footer={
                    data.length > 20 && (
                        <Row justify="end">
                            <Col>
                                <Pagination
                                    hideOnSinglePage={false}
                                    className="text-right"
                                    current={currentPage}
                                    pageSize={pageSize}
                                    pageSizeOptions={['10', '20', '50', '100']}
                                    total={total}
                                    showSizeChanger
                                    showTotal={(total, range) => (`${range[0]} - ${range[1]} / ${total}`)}
                                    onChange={(page, pageSize) => {
                                        setCurrentPage(page);
                                    }}
                                    onShowSizeChange={(current, size) => {
                                        setCurrentPage(1);
                                        setPageSize(size);
                                    }}
                                />
                            </Col>
                        </Row>
                    )
                }
            >
                <Row justify={"end"}>
                    <div>
                        {
                            print && <ReactToPrint
                                trigger={() => {
                                    return <Button type={"primary"}>{<FormattedMessage id={"common.print"} />}</Button>;
                                }}
                                content={() => componentRef}
                            />
                        }
                    </div>
                </Row>
                <ConfigProvider
                    renderEmpty={
                        () => {
                            return <Empty
                                image={<img src={EmptyImg} style={{ width: 200, height: 142 }}></img>}
                            />
                        }
                    }
                >
                    <Table
                        loading={loading}
                        style={{ marginTop: 16 }}
                        dataSource={data}
                        rowKey={(record) => (record.time)}
                        pagination={false}
                    >
                        <Table.Column title={<FormattedMessage id="common.serial" />} dataIndex="#" key="#" width={64}
                            render={(text, record, index) => ((currentPage - 1) * pageSize + index + 1)} />
                        <Table.Column title={<FormattedMessage id="common.operator" />} key="user" dataIndex="user"
                            width={180} align="left" ellipsis
                            render={
                                (value, record, index) => record.user_name
                            } />
                        <Table.Column
                            title={<FormattedMessage id="common.operation.time" />}
                            key="time_str"
                            dataIndex="time_str"
                            align="left"
                            width={240}
                            ellipsis
                        />
                        <Table.Column title={<FormattedMessage id="common.operation.type" />} key="operation_type"
                            dataIndex="operation_type" align="left" ellipsis width={100}
                        />
                        <Table.Column title={<FormattedMessage id="common.operation.content" />} key="fields"
                            dataIndex="fields" align="left" ellipsis
                            render={
                                (value, record, index) => (
                                    value && value.map(item =>
                                        <div
                                            style={{ whiteSpace: 'pre-line', width: '380px' }}
                                        >
                                            {item}
                                        </div>
                                    )
                                )
                            }
                        />
                    </Table>
                </ConfigProvider>
                <div
                    style={{ display: "none" }}
                >
                    <div
                        ref={el => (componentRef = el)}>
                        <OperationListTable keys={"history.users"} data={data} loading={loading} tableHeight={tableHeight} timeZone={timeZone} />
                    </div>

                </div>
            </Modal>
        </React.Fragment>
    )
};