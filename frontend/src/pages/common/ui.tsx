import styled from "@emotion/styled";
import {Tag} from "antd";

export const BeradcrumbItemUI = styled.span`
    color: #4E5969;
    :hover {
        cursor: pointer;
        color: #165DFF;
    }
`
// 倒三角
export const Triangle = styled.div`
    width: 0;
    height: 0;
    border-top: 6px solid #333333;
    border-right: 6px solid transparent;
    border-left: 6px solid transparent;
`

export const UpTriangle = styled.div`
    width: 0;
    height: 0;
    border-bottom: 6px solid #333333;
    border-right: 6px solid transparent;
    border-left: 6px solid transparent;
`

export const EnvTag = (prop: any) => {
    let defaultStyle = {
        background: "rgba(255, 92, 0, 0.1)",
        borderRadius: "2px",
        color: "#FF5C00",
    }
    switch (prop.type) {
        case "DEV":
            defaultStyle = {
                background: "rgba(116, 106, 237, 0.1)",
                borderRadius: "2px",
                color: "#746AED",
            }
            break
        case "PROD":
            defaultStyle = {
                background: "rgba(22, 93, 255, 0.1)",
                borderRadius: "2px",
                color: "#165DFF",
            }
            break
        case "UAT":
            defaultStyle = {
                background: "rgba(65, 204, 130, 0.1)",
                borderRadius: "2px",
                color: "#41CC82",
            }
            break
        case "TEST":
            defaultStyle = {
                background: "rgba(255, 174, 0, 0.1)",
                borderRadius: "2px",
                color: "#FFAE00",
            }
            break
    }
    const style = {
        ...defaultStyle,
        // maxWidth: 100,
        whiteSpace: "break-spaces",
        display: "block",
        border: 'none'
    }
    return <Tag className="mouse" style={style} title={prop.type} {...prop}>{prop.type}</Tag>
}