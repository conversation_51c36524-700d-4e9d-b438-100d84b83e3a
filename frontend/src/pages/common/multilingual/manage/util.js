import {message as zhmessage} from "../../../../locales/zh";
import {message as enmessage} from "../../../../locales/en";

export const flatModules = (modules) => {
    return {
        fields: getFields(modules),
        options: modules.map(it => getOption(it))
    }
}

export const getAllKeys = (modules) => {
    return getFields(modules).map(it => it.key)
}


const getItem = (module, path) => {
    if ("children" in module) {
        return module.children.flatMap(it => getItem(it, !!module.key ? [...path, module.key] : path))
    }
    return {...module, value: zhmessage[module.key] +  enmessage[module.key], path: path}
}

const getFields = (modules) => {
    return modules.reduce((res, item) => {
        const path = !!item.key ? [item.key] : []
        const items = item.children.flatMap(it => getItem(it, path))
        res = res.concat(items)
        return res
    }, [])
}

const getOption = (module) => {
    const children = module.children.filter((it) => "children" in it).map(it => getOption(it))
    const option = {
        value: module.key,
        name: module.name,
        label: zhmessage[module.name] || module.name
    }
    if (children.length > 0) option.children = children
    return option
}


export const fieldType  = [
    {value: 'label', label: '字段名称'},
    {value: 'menu', label: '菜单名称'},
    {value: 'tips', label: '页面静态标签'},
    {value: 'button', label: '按钮名称'},
    {value: 'option', label: '控件选项值'},
    {value: 'placeholder', label: '控件提示文案'},
    {value: 'warn', label: '操作提醒文案'},
]

export const splitModule = (module, subModule) => {
    const fields = module.children
    const fieldKeys = fields.map(it => it.key)
    const subFields = subModule.children.map(it => it.children).flat()
    const subKeys = subFields.map(it => it.key)
    // TODO 后续调整
    const children = subModule.children.map(it => ({...it, name: it.title}))

    children.push({
        key: 'more',
        name: '管理模块多出的字段',
        children: subFields.filter(it => !fieldKeys.includes(it.key))
    })

    children.push({
        key: 'less',
        name: '管理模块少掉的字段',
        children: fields.filter(it => !subKeys.includes(it.key))
    })

    return {...module, children: children}
}


const api = 'http://localhost:8001'
const http = async (path, data, method) => {
    const config = {
        method: method,
        headers: {'Content-Type': 'application/json'}
    }
    if (method === 'post') config.body = JSON.stringify(data || {})
    return window.fetch(`${api}/${path}`, config).then(
        async response => {
            if (response.ok) {
                const data = await response.json()
                return data
            } else {
                return Promise.reject(response)
            }
        }
    )
}

export const get = async (path) => http(path, {}, 'get')
export const post = async (path, data) => http(path, data, 'post')