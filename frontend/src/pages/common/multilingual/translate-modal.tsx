import React, { useEffect, useImperativeHandle, useRef } from "react";
import { Button, Form, Input, InputRef, message, Modal } from "antd";
import { useSafeState } from "ahooks";
import { useAtom } from "jotai";
import { customLanguage<PERSON>tom, customLocales<PERSON>tom } from "./context";
import { useFetch } from "../../../hooks/request";
import { updateTranslate } from "../../../api/multi_language";
import { FormattedMessage, useIntl } from "react-intl";
import { getLanguageCountry, getLocalesData } from "./util";
import { LanguageLibraryOption } from "../../main/multiLanguage/translate/data";
import { extractPlaceholders, highlightPlaceholders } from "./translate-utils";
import { TranslateTemplateInput } from "./translate-input";
import _ from "lodash";

const { data, flatModules, flatTypes } = getLocalesData("zh");

const getTranslateData = (field: any, languageInfo: any, library: number, translateValue: string) => {
    const languageLibrary = LanguageLibraryOption.find((it) => it.value === library);
    const path = flatModules.filter((p) => field.path?.includes(p.value));
    const type = flatTypes.find((it) => it.value === field.type);
    return {
        id: languageInfo.id,
        languageId: languageInfo.id,
        customerId: languageInfo.customerId,
        projectId: languageInfo.projectId,
        envId: null,
        cohortId: null,
        languageLibrary: 1,
        // 字段参数
        key: field.key,
        translateValue: translateValue, // 翻译值
        type: field.type,
        pagePath: field.path,
        // 轨迹使用参数
        languageName: languageInfo.languageName, // 语言名
        languageLibraryValueZh: languageLibrary?.labelCn,
        languageLibraryValueEn: languageLibrary?.labelEn,
        pagePathValueZh: path.map((it) => it.label).join(" / "),
        pagePathValueEn: path.map((it) => it.labelEn).join(" / "),
        typeValueZh: type?.label,
        typeValueEn: type?.labelEn,
        nameValueZh: field.defaultLabel?.cn,
        nameValueEn: field.defaultLabel?.en,
    };
};

export const TranslateModal = (props: { bind: any; height?: number }) => {
    const { formatMessage } = useIntl();
    const [visible, setVisible] = useSafeState<boolean>(false);
    const [field, setField] = useSafeState<any>({});
    const inputRef = useRef<InputRef>(null);
    const [translateValue, setTranslateValue] = useSafeState<string>("");
    const [originalText, setOriginalText] = useSafeState<string>("")

    const { runAsync: doUpdateTranslate, loading } = useFetch(updateTranslate, { manual: true });
    const [translateMessages, setTranslateMessages] = useAtom(customLocalesAtom);
    const [languageInfo] = useAtom(customLanguageAtom);

    useImperativeHandle(props.bind, () => ({ show }));

    const show = (key: string) => {
        const field = data.find((it) => it.key === key);
        if (!field) return;
        setField(field);
        setTranslateValue(translateMessages[key] || "");
        const languageCode = getLanguageCountry(languageInfo.baseLanguageCode).languageCode
        setOriginalText(!field.defaultLabel ? "" : (field.defaultLabel[languageCode] || ""))
        setVisible(true);
        inputFocus();
    };

    const hide = () => {
        setField({});
        setTranslateValue("");
        setOriginalText("")
        setVisible(false);
    };

    const inputFocus = () => {
        setTimeout(() => {
            inputRef?.current?.focus();
        }, 0);
    };

    // 单个翻译编辑
    const translate = () => {
        // 校验翻译值，不允许用户自行输入占位符
        const morePlaceholders = _.difference(
            extractPlaceholders(translateValue),
            extractPlaceholders(field.defaultLabel.en),
        );
        if (morePlaceholders.length > 0) {
            message.error({
                content: `不允许输入[${morePlaceholders.join(";")}]`,
            });
            return;
        }
        const data = getTranslateData(field, languageInfo, 1, translateValue);
        doUpdateTranslate({ customerId: languageInfo.customerId, projectId: languageInfo.projectId }, data).then(() => {
            setTranslateMessages({ ...translateMessages, [field.key]: translateValue });
            message.success(formatMessage({ id: "message.save.success" }));
            hide();
        });
    };

    return (
        <Modal
            open={visible}
            centered={true}
            onCancel={hide}
            title={<FormattedMessage id={"multiLanguage.translation.custom"} />}
            closable
            className={"custom-tips-modal-with-content"}
            style={{ minHeight: 400, maxHeight: 600 }}
            maskClosable={false}
            onOk={translate}
            okButtonProps={{ loading: loading }}
            forceRender={true}
        >
            <Form labelCol={{ span: 5 }} style={props.height ? { height: props.height } : {}}>
                <Form.Item label={getLanguageCountry(languageInfo.baseLanguageCode).label}>
                    <div style={{ padding: "5px 0" }}>{highlightPlaceholders(originalText)}</div>
                </Form.Item>
                <Form.Item label={languageInfo.languageName}>
                    {extractPlaceholders(originalText)?.length > 0 ? (
                        <TranslateTemplateInput
                            translationText={translateMessages[field.key] || ""}
                            originalText={originalText}
                            baseLanguageCode={languageInfo.baseLanguageCode}
                            onChange={(value: string) => setTranslateValue(value)}
                        />
                    ) : (
                        <Input
                            onClick={() => inputFocus()}
                            ref={inputRef}
                            value={translateValue}
                            onChange={(e) => setTranslateValue(e.target.value)}
                        />
                    )}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export const TranslateSimpleInput = (props: any) => {
    const { formatMessage } = useIntl();
    const [translateMessages, setTranslateMessages] = useAtom(customLocalesAtom);
    const [languageInfo] = useAtom(customLanguageAtom);
    const [value, setValue] = useSafeState<string>("");

    const { runAsync: doUpdateTranslate, loading } = useFetch(updateTranslate, { manual: true });

    useEffect(() => {
        setValue(props.value);
    }, [props.value]);

    const onSave = () => {
        const data = getTranslateData(props.field, languageInfo, 1, value);
        doUpdateTranslate({ customerId: languageInfo.customerId, projectId: languageInfo.projectId }, data).then(() => {
            setTranslateMessages({ ...translateMessages, [props.field.key]: value });
            message.success(formatMessage({ id: "message.save.success" }));
            props.onComplete && props.onComplete();
        });
    };

    return (
        <Input.Group compact>
            <Input
                placeholder={formatMessage({ id: "placeholder.input.common" })}
                value={value}
                onChange={(e) => setValue(e.target.value)}
                suffix={
                    <Button size="small" type="primary" onClick={onSave} loading={loading}>
                        {formatMessage({ id: "common.save" })}
                    </Button>
                }
            />
        </Input.Group>
    );
};
