import {BaseLocaleLanguage, LocaleItem, LocaleItemType, LocaleOption} from "./index"
import {allFields, editFields, editFlatModules, editModules} from "./locales"
import {message as zh} from "../../../locales/zh";
import {message as en} from "../../../locales/en";
import _ from "lodash";

const localeItemTypeOptions: { key: LocaleItemType, label: { [key in BaseLocaleLanguage]: string } }[] = [
    {key: 'label', label: {cn: '字段名称', en: 'Field'}},
    {key: 'menu', label: {cn: '菜单名称', en: 'Menu'}},
    {key: 'tips', label: {cn: '页面静态标签', en: 'Tooltip'}},
    {key: 'button', label: {cn: '按钮名称', en: 'Button'}},
    {key: 'option', label: {cn: '控件选项值', en: 'Option'}},
    {key: 'placeholder', label: {cn: '控件提示文案', en: 'Placeholder'}},
    {key: 'warn', label: {cn: '操作提醒文案', en: 'Warn'}},
]

const getTypeOptions = (baseLanguage: BaseLocaleLanguage) => {
    return localeItemTypeOptions.map(it => ({
        value: it.key,
        label: it.label[baseLanguage]
    }))
}

// 字段类型默认赋值
const getType = (item: LocaleItem): LocaleItemType => {
    return item.type || (item.key.startsWith("menu.") ? "menu" : "label")
}

const getFlatTypeOptions = () => {
    return localeItemTypeOptions.map(it => ({
        value: it.key,
        label: it.label.cn,
        labelEn: it.label.en
    }))
}

// 国家与地区类型，保持与语言选择一致
type countryType = "zh" | "en"
type languageType = { key: countryType, label: string, code: string, languageCode: BaseLocaleLanguage }
export const languageCountryCode: { [key: string]: languageType } = {
    zh: {key: "zh", label: "中文", code: "zh-CN", languageCode: "cn"},
    cn: {key: "zh", label: "中文", code: "zh-CN", languageCode: "cn"}, // TODO 后续移除
    en: {key: "en", label: "English", code: "en-US", languageCode: "en"},
}
export const getLanguageCountry = (language: string) => {
    return languageCountryCode[language] || languageCountryCode.en
}

const getLocaleItems = (localeItems: LocaleItem[], customMessage:{ [key: string]: string }, language: BaseLocaleLanguage): LocaleItem[] => {
    return localeItems.map((it: LocaleItem) => {
        const systemLabel = typeof it.label !== "string" ? it.label[language] : it.label
        const customerLabel = customMessage[it.key]
        return {
            ...it,
            label: customerLabel || systemLabel,
            defaultLabel: typeof it.label !== "string" ? it.label : {cn: it.label, en: it.label},
            type: getType(it),
            translation: !!customerLabel
        }
    })
}

const transEnModules = (modules: LocaleOption[]) => {
    modules.forEach(it => {
        it.label = it.labelEn
        if (it.children && it.children.length > 0) transEnModules(it.children)
    })
    return modules
}

const getLocaleModules = (modules: LocaleOption[], language: BaseLocaleLanguage) => {
    if (language === 'en') return transEnModules(_.cloneDeep(modules))
    return modules
}


export const getLocaleMirrorsItems = (): any => {
    const modules = editFlatModules.reduce((res: { [key: string]: any }, item) => {
        res[item.value] = {cn: item.label, en: item.labelEn}
        return res
    }, {})
    const types = localeItemTypeOptions.reduce((res: { [key: string]: any }, item) => {
        res[item.key] = item.label
        return res
    }, {})
    const data= editFields.map((it: LocaleItem) => {
        const type = getType(it)
        const label = it.label as { cn: string, en: string }
        return {
            key: it.key,
            pathCode: it.path,
            typeCode: type,
            nation: {
                cn: {label: label.cn},
                en: {label: label.en}
            }
        }
    })

    return {
        modules: modules,
        typeOptions: types,
        data: data,
    }
}

// 获取全部默认字段及模块信息，多语言管理模块使用
export const getLocalesData = (language: string) => {
    const languageCode = getLanguageCountry(language).languageCode
    return {
        data: getLocaleItems(editFields, {}, languageCode),
        modules: getLocaleModules(editModules, languageCode),
        flatModules: editFlatModules,
        types: getTypeOptions(languageCode),
        flatTypes: getFlatTypeOptions()
    }
}

// 多语言context使用
const message: { [key in countryType]: any } = {'zh': zh, 'en': en}
export const getLocales = (customMessage: {[key: string]: string}, language: string): {} => {
    const lang = getLanguageCountry(language)
    return {...message[lang.key], ...customMessage}
}