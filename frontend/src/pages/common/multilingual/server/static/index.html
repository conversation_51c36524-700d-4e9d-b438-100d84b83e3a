<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>多语言脚本</title>
</head>
<body>
<!--<button onclick="addKey()">Add</button>-->
<button onclick="read()">Read</button>

<pre id="output"></pre>

<script>
    function addKey() {
        fetch(`/add-key`).then(response => response.text()).then(data => {
            document.getElementById('output').textContent = data
        }).catch(error => console.error('Error:', error))
    }

    function read() {
        fetch(`/read-file`).then(response => response.json()).then((data) => {
            document.getElementById('output').textContent = ""
            const output = document.getElementById('output')
            data.forEach(it => {
                const li = document.createElement('li')
                li.textContent = `${it.key} ${!!it.type ? (` 类型:${it.type}`) : ''}`
                output.appendChild(li)
            })

        }).catch(error => console.error('Error:', error));
    }
</script>
</body>
</html>