import fs from 'fs'
import * as babel from '@babel/core'
import babelParser  from '@babel/parser'
import generate from '@babel/generator';
import t from '@babel/types'

export const addKey = (file, declarationName, field) => {
    return new Promise((resolve, reject) => {
        fs.readFile(file, 'utf8', (err, data) => {
            if (err) reject(err)
            const root  = babelParser.parse(data,{
                sourceType: 'module',
                plugins: ['typescript'],
            })
            babel.traverse(root, {
                enter(path) {
                    if (path.node.declaration && path.node.declaration.declarations[0].id.name === declarationName) {
                        const objectExpression = t.objectExpression([
                            t.objectProperty(t.identifier('key'), t.stringLiteral(field.key)),
                            t.objectProperty(t.identifier('type'), t.stringLiteral(field.type)),
                        ]);
                        path.node.declaration.declarations[0].init.elements.push(objectExpression)
                    }
                }
            })
            fs.writeFile(file, generate.default(root, {}, data).code, 'utf8', (err) => {
                if (err) reject(err)
                console.log('File content successfully modified.');
                resolve('success')
            })
        })
    })
}

