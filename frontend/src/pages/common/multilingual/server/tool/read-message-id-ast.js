import * as babel from '@babel/core'
import babelParser from '@babel/parser'
import t from '@babel/types'
import fs from "fs";

const types = {
    // 控件选项值：基于控件下的选项枚举值，如下拉、单选等控件
    option: ['Select.Option', 'options', 'Select'],
    // 按钮名称：操作按钮name
    button:  ['Button'],
    // 页面静态标签：tooltip、解释显示说明文案
    tips: ['Tooltip', 'notification.info'],
    // 控件提示文案：控件内的提示文案
    placeholder: ['placeholder'],
    // 操作提醒文案：操作反馈提醒
    warn: ['message.error', 'Alert']
}

const METHOD_TYPE_MAP = Object.keys(types).reduce((res, type) => {
    types[type].forEach(it => res[it] = type)
    return res
}, {})

export const readMessageIds = (file) => {
    return new Promise((resolve, reject) => {
        fs.readFile(file, 'utf8', (err, data) => {
            if (err) reject(err)
            const ast = babelParser.parse(data, {
                sourceType: 'module',
                plugins: ['jsx', 'typescript'],
            })
            // 直接使用的id
            const messageIds = getMessageIds(ast)
            const optionConstNames = getImportVars(ast, '../data/data')
            const componentConstName = getImportVars(ast, '../components/')
            const commonConstName = getImportVars(ast, '../common/')
            resolve({
                ids: messageIds,
                importConstNames: [...optionConstNames, ...componentConstName, ...commonConstName],
                fileDeclaration: readDeclaration(ast)
            })
        })
    })
}

export const readOptionMessageId = (file) => {
    return new Promise((resolve, reject) => {
        fs.readFile(file, 'utf8', (err, data) => {
            if (err) reject(err)
            const ast = babelParser.parse(data, {
                sourceType: 'module',
                plugins: ['jsx', 'typescript'],
            })
            const messageIds = getConstMessageIds(ast)
            messageIds.forEach(item => {
                item.ids = item.ids.map(it => ({...it, type: 'option'}))
            })
            resolve(messageIds)
        })
    })
}


// 读取组件名
const readDeclaration = (ast) => {
    const results = []
    babel.traverse(ast, {
        ExportDeclaration(path) {
            if (t.isExportDeclaration(path.node)) {
                if (!["value", "type"].includes(path.node.exportKind)) {
                    const node = t.isIdentifier(path.node.declaration) ? path.node.declaration : path.node.declaration.declarations[0].id
                    results.push(node.name)
                }
            }
        },
    })
    return results
}

// 按const定义获取id
const getConstMessageIds = (ast) => {
    const results = []
    babel.traverse(ast, {
        ExportDeclaration(path) {
            if (t.isExportDeclaration(path.node)) {
                const node = path.node.declaration.declarations[0]
                const newAst = {
                    type: 'File',
                    program: {type: 'Program', body: [t.cloneNode(node)]}
                };
                results.push({name: node.id.name, ids: getMessageIds(newAst)})
            }
        },
    })
    return results
}


const getMessageIds = (ast) => {
    const results = []
    babel.traverse(ast, {
        // 处理JSX元素中的FormattedMessage
        JSXElement(path) {
            if (path?.node?.openingElement?.name?.name === 'FormattedMessage') {
                const componentName = findClosestComponent(path);
                const methodName = getCallerMethodChain(path);

                const messageIds = getJSXMessageId(path.node);
                if (messageIds && messageIds.length > 0) {
                    messageIds.forEach(messageId => {
                        results.push({
                            key: messageId,
                            type: METHOD_TYPE_MAP[methodName] || METHOD_TYPE_MAP[componentName]
                        });
                    })
                }
            }
        },

        CallExpression(path) {
            if (isFormatMessageCall(path.node)) {
                const methodName = getCallerMethodChain(path);
                const componentName = findClosestComponent(path);

                const messageIds = getFunctionMessageId(path.node);

                if (messageIds && messageIds.length > 0) {
                    messageIds.forEach(messageId => {
                        results.push({
                            key: messageId,
                            type: METHOD_TYPE_MAP[methodName] || METHOD_TYPE_MAP[componentName]
                        });
                    })
                }
            }
        }
    })
    return Array.from(new Set(results))
}

const findClosestComponent = (path) => {
    let currentPath = path.parentPath;
    while (currentPath) {
        const componentName = currentPath.parent?.name?.name
        if (componentName in METHOD_TYPE_MAP) {
            return componentName;
        }
        currentPath = currentPath.parentPath;
    }
    return null
}

const getJSXMessageId = (node) => {
    const formattedMessageProps = node.openingElement.attributes
    // 遍历属性查找id
    const idProp = formattedMessageProps.find(prop =>
        prop.type === 'JSXAttribute' && prop.name.name === 'id'
    )
    if (!idProp) return null
    const idNode = idProp.value
    if (idNode?.type === 'JSXExpressionContainer') return getJSXExpressionContainerValue(idNode?.expression)
    return [idNode.value]
}


const getCallerMethodChain = (path) => {
    // path.parentPath.parentPath?.parentPath?.node?.callee
    let currentPath = path.parentPath;
    while (currentPath) {
        const currentCallee = currentPath.node?.callee
        if (currentCallee?.name in METHOD_TYPE_MAP) return currentCallee.name
        const callName = [currentCallee?.object?.name, currentCallee?.property?.name].join('.')
        if (callName in METHOD_TYPE_MAP) return callName
        // 上层遍历
        currentPath = currentPath.parentPath
    }
    return null
}


const getFunctionMessageId = (node) => {
    const args = node.arguments
    const properties = args.length > 0 ? args[0].properties : []
    const idProp = properties.find(prop => prop.type === 'ObjectProperty' && prop.key.type === 'Identifier' && prop.key.name === 'id')
    if (!idProp) return []
    if (idProp.value.type === 'ConditionalExpression') return getJSXExpressionContainerValue(idProp.value)
    if (idProp.value.type === 'StringLiteral') return [idProp.value.value]
    return []
}

// 判断是否为 formatMessage 调用
const isFormatMessageCall = (node) => {
    return (node?.callee?.type === 'Identifier' && node?.callee?.name === 'formatMessage') || node?.callee?.property?.name === 'formatMessage'
}

const getJSXExpressionContainerValue = (expression) => {
    if (!expression) return []
    if (expression.type === 'StringLiteral') return [expression.value]
    if (expression.type === 'ConditionalExpression') {
        return [...getJSXExpressionContainerValue(expression.consequent), ...getJSXExpressionContainerValue(expression.alternate)]
    }
    return []
}

const getImportVars = (ast, importPath) => {
    const importedVars = new Set()
    const useSet = new Set()
    // 步骤1：收集导入的变量
    babel.traverse(ast, {
        ImportDeclaration(path) {
            if (path.node.source.value.includes(importPath)) {
                path.node.specifiers.forEach(spec => {
                    if (t.isImportSpecifier(spec) && !!spec.imported.name) {
                        importedVars.add(spec.local.name)
                    }
                });
            }
        }
    });

    // 检测JSX中的引用
    babel.traverse(ast, {
        JSXElement(path) {
            const componentName = path.node.openingElement.name.name;
            if (importedVars.has(componentName)) {
                useSet.add(componentName)
            }

            path.traverse({
                JSXAttribute(jsxPath) {
                    const value = jsxPath.node.value;
                    if (t.isJSXExpressionContainer(value)) {
                        checkExpression(value.expression, jsxPath);
                    }
                },
                JSXExpressionContainer(exprPath) {
                    checkExpression(exprPath.node.expression, exprPath);
                }
            });
        },
    });

    function checkExpression(node, currentPath) {
        if (t.isIdentifier(node)) {
            const binding = currentPath?.scope?.getBinding(node.name);
            if (binding?.path.isImportSpecifier()) {
                useSet.add(node.name)
            }
        } else if (t.isMemberExpression(node)) {
            checkExpression(node.object, currentPath);
        } else if (t.isCallExpression(node)) {
            node.arguments.forEach(arg => checkExpression(arg, currentPath));
        }
    }

    babel.traverse(ast, {
        ArrowFunctionExpression(path) {
            path.traverse({
                Identifier(innerPath) {
                    const nodeName = innerPath.node.name
                    if (importedVars.has(nodeName)) {
                        const binding = innerPath.scope.getBinding(nodeName);
                        if (binding && binding.path.isImportSpecifier()) {
                            useSet.add(nodeName)
                        }
                    }
                }
            });
        }
    });
    return Array.from(useSet)
}
