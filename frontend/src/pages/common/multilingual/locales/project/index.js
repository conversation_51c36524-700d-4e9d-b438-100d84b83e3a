import {home_overview} from "./home/<USER>";
import {home_random} from "./home/<USER>";
import {home_subject} from "./home/<USER>";
import {home_depot_ip} from "./home/<USER>";
import {home_site_ip} from "./home/<USER>";
import {home_analysis} from "./home/<USER>";
import {home_dynamics} from "./home/<USER>";
import {home_task} from "./home/<USER>";
import {subject_visit} from "./subject/visit";
import {subject_subjects} from "./subject/subjects";
import {supply_freeze} from "./supply/freeze";
import {supply_shipment_return} from "./supply/shipment_return";
import {supply_shipment} from "./supply/shipment";
import {supply_site} from "./supply/site";
import {supply_depot} from "./supply/depot";
import {setting_notice} from "./setting/notice";
import {setting_user} from "./setting/user";
import {build_storehouse} from "./build/storehouse";
import {build_site} from "./build/site";
import {build_attribute} from "./build/attribute";
import {build_push_statistics} from "./build/push_statistics";
import {build_random_simulate} from "./build/random_simulate";
import {build_supply_plan} from "./build/supply_plan";
import {build_history} from "./build/history";
import {build_randomization_design} from "./build/randomization_design";
import {build_randomization_form} from "./build/randomization_form";
import {build_treatment_visit_management} from "./build/treatment_visit_management";
import {build_treatment_design} from "./build/treatment_design";
import {build_treatment_barcode} from "./build/treatment_barcode";
import {build_treatment_ip_list} from "./build/treatment_ip_list";
import {build_treatment_ip_no_number} from "./build/treatment_ip_no_number";
import {build_treatment_drug_batch} from "./build/treatment_drug_batch";
import {build_treatment_barcode_label} from "./build/treatment_barcode_label";

export const project = {
    key: "project_detail",
    name: "menu.projects.project",
    children: [
        {
            key: "home",
            name: "menu.projects.project.home",
            children: [
                {
                    key: "home_overview",
                    name: "menu.projects.project.overview",
                    children: home_overview
                },
                {
                    key: "home_random",
                    name: "menu.projects.project.random.statistics",
                    children: home_random
                },
                {
                    key: "home_subject",
                    name: "menu.projects.project.subject.statistics",
                    children: home_subject
                },
                {
                    key: "home_depot_ip",
                    name: "menu.projects.project.depot.ip.statistics",
                    children: home_depot_ip
                },
                {
                    key: "home_site_ip",
                    name: "menu.projects.project.site.ip.statistics",
                    children: home_site_ip
                },
                {
                    key: "home_analysis",
                    name: "menu.projects.project.analysis",
                    children: home_analysis
                },
                {
                    key: "home_dynamics",
                    name: "menu.projects.project.dynamics",
                    children: home_dynamics
                },
                {
                    key: "home_task",
                    name: "menu.projects.project.task",
                    children: home_task
                }
            ]
        },
        {
            key: "subject",
            name: "menu.projects.project.sub",
            children: [
                {
                    key: "subject_subjects",
                    name: "menu.projects.project.subject",
                    children: subject_subjects
                },
                {
                    key: "subject_visit",
                    name: "menu.projects.project.subject.visit.cycle",
                    children: subject_visit
                }
            ]
        },
        {
            key: "supply",
            name: "menu.projects.project.supply",
            children: [
                {
                    key: "supply_depot",
                    name: "menu.projects.project.supply.storehouse",
                    children: supply_depot
                },
                {
                    key: "supply_site",
                    name: "menu.projects.project.supply.site",
                    children: supply_site
                },
                {
                    key: "supply_shipment",
                    name: "menu.projects.project.supply.shipment",
                    children: supply_shipment
                },
                {
                    key: "supply_shipment_return",
                    name: "menu.projects.project.supply.drug_recovery",
                    children: supply_shipment_return
                },
                {
                    key: "supply_freeze",
                    name: "menu.projects.project.supply.release-record",
                    children: supply_freeze
                }
            ]
        },
        {
            key: "build",
            name: "menu.projects.project.build",
            children: [
                {
                    key: "build_storehouse",
                    name: "menu.projects.project.build.storehouse",
                    children: build_storehouse
                },
                {
                    key: "build_site",
                    name: "menu.projects.project.build.site",
                    children: build_site
                },
                {
                    key: "build_attribute",
                    name: "menu.projects.project.build.attributes",
                    children: build_attribute
                },
                {
                    key: "build_push_statistics",
                    name: "menu.projects.project.build.push",
                    children: build_push_statistics
                },
                {
                    key: "build_random_simulate",
                    name: "menu.projects.project.build.simulate_random",
                    children: build_random_simulate
                },
                {
                    key: "build_randomization",
                    name: "menu.projects.project.build.randomization",
                    children: [
                        {
                            key: "build_randomization_design",
                            name: "menu.projects.project.build.randomization.design",
                            children: build_randomization_design
                        },
                        {
                            key: "build_randomization_form",
                            name: "menu.projects.project.build.randomization.form",
                            children: build_randomization_form
                        }
                    ]
                },
                {
                    key: "build_treatment",
                    name: "menu.projects.project.build.drug",
                    children: [
                        {
                            key: "build_treatment_visit_management",
                            name: "menu.projects.project.build.drug.visit",
                            children: build_treatment_visit_management
                        },
                        {
                            key: "build_treatment_design",
                            name: "menu.projects.project.build.drug.config",
                            children: build_treatment_design
                        },
                        {
                            key: "build_treatment_barcode",
                            name: "menu.projects.project.build.drug.barcode",
                            children: build_treatment_barcode
                        },
                        {
                            key: "build_treatment_ip_list",
                            name: "menu.projects.project.build.drug.list",
                            children: build_treatment_ip_list
                        },
                        {
                            key: "build_treatment_ip_no_number",
                            name: "menu.projects.project.build.drug.no_number",
                            children: build_treatment_ip_no_number
                        },
                        {
                            key: "build_treatment_drug_batch",
                            name: "menu.projects.project.build.drug.batch",
                            children: build_treatment_drug_batch
                        },
                        {
                            key: "build_treatment_barcode_label",
                            name: "menu.projects.project.build.drug.barcode_label",
                            children: build_treatment_barcode_label
                        }
                    ]
                },
                {
                    key: "build_supply_plan",
                    name: "menu.projects.project.build.plan",
                    children: build_supply_plan
                },
                {
                    key: "build_history",
                    name: "menu.projects.project.build.history",
                    children: build_history
                }
            ]
        },
        {
            key: "setting",
            name: "menu.projects.project.settings",
            children: [
                {
                    key: "setting_notice",
                    name: "menu.projects.project.settings.notice",
                    children: setting_notice
                },
                {
                    key: "setting_user",
                    name: "menu.projects.project.settings.user",
                    children: setting_user
                }
            ]
        },
    ]
}
