export const build_randomization_form = [
    {
        key: "form.control.type.options.one"
    },
    {
        key: "form.control.type.options.two"
    },
    {
        key: "form.control.type.options.tip.one",
        type: "tips"
    },
    {
        key: "form.control.type.options.tip.two",
        type: "tips"
    },
    {
        key: "form.control.type.options.three"
    },
    {
        key: "common.required.prefix"
    },
    {
        key: "form.control.type.options"
    },
    {
        key: "form.control.type.options.duplicate"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "common.addTo"
    },
    {
        key: "common.current.time"
    },
    {
        key: "form.control.type.format.characterLength"
    },
    {
        key: "form.control.type.format.numberLength"
    },
    {
        key: "form.control.type.format.decimalLength"
    },
    {
        key: "form.control.type.format.checkbox"
    },
    {
        key: "form.control.type.options.lack"
    },
    {
        key: "form.control.type.options.selectedRepeatedly"
    },
    {
        key: "common.add"
    },
    {
        key: "common.edit"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.ok"
    },
    {
        key: "form.application.type"
    },
    {
        key: "form.application.type.register"
    },
    {
        key: "form.application.type.formula"
    },
    {
        key: "drug.configure.setting.dose.form.doseAdjustment"
    },
    {
        key: "randomization.config.factor.calc"
    },
    {
        key: "form.field.label"
    },
    {
        key: "form.modify"
    },
    {
        key: "form.required"
    },
    {
        key: "form.application.type.variable"
    },
    {
        key: "form.control.type"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "form.control.type.inputNumber"
    },
    {
        key: "form.control.type.input"
    },
    {
        key: "form.control.type.textArea"
    },
    {
        key: "form.control.type.select"
    },
    {
        key: "form.control.type.checkbox"
    },
    {
        key: "form.control.type.radio"
    },
    {
        key: "form.control.type.switch"
    },
    {
        key: "form.control.type.date"
    },
    {
        key: "form.control.type.dateTime"
    },
    {
        key: "form.control.type.format"
    },
    {
        key: "form.control.type.variableFormat"
    },
    {
        key: "form.control.type.label"
    },
    {
        key: "form.control.type.variableRange"
    },
    {
        key: "form.control.type.variableRange.validate"
    },
    {
        key: "form.control.type.variableRange.validate.range"
    },
    {
        key: "form.control.type.variableRange.min",
        type: "placeholder"
    },
    {
        key: "form.control.type.variableRange.max",
        type: "placeholder"
    },
    {
        key: "form.picker.max.tip"
    },
    {
        key: "common.status"
    },
    {
        key: "common.effective"
    },
    {
        key: "common.invalid"
    },
    {
        key: "common.tips"
    },
    {
        key: "common.confirm.delete"
    },
    {
        key: "form.preview"
    },
    {
        key: "common.serial"
    },
    {
        key: "form.field.name"
    },
    {
        key: "form.list.modify"
    },
    {
        key: "common.operation"
    },
    {
        key: "common.delete"
    },
    {
        key: "form.control.type.format.limit"
    },
    {
        key: "common.yes",
        type: "option"
    },
    {
        key: "common.no",
        type: "option"
    },
    {
        key: "form.control.type.variableFormat.tip1",
        type: "tips"
    },
    {
        key: "form.control.type.variableFormat.tip2",
        type: "tips"
    },
    {
        key: "form.control.type.variableFormat.tip3",
        type: "tips"
    }
];