export const build_treatment_ip_list = [
    {
        key: "report.download.success"
    },
    {
        key: "common.download.fail",
        type: "warn"
    },
    {
        key: "drug.list.delete.info"
    },
    {
        key: "common.tips"
    },
    {
        key: "common.confirm.delete"
    },
    {
        key: "common.ok"
    },
    {
        key: "subject.dispensing.placeholder.input.batch",
        type: "placeholder"
    },
    {
        key: "medicine.but.examine"
    },
    {
        key: "medicine.but.update"
    },
    {
        key: "medicine.but.release"
    },
    {
        key: "drug.list.drugNumber",
        type: "placeholder"
    },
    {
        key: "drug.list.delete"
    },
    {
        key: "common.download.data"
    },
    {
        key: "menu.settings"
    },
    {
        key: "common.upload"
    },
    {
        key: "common.serial"
    },
    {
        key: "drug.configure.drugName"
    },
    {
        key: "drug.list.serialNumber"
    },
    {
        key: "drug.list.expireDate"
    },
    {
        key: "drug.list.batch"
    },
    {
        key: "drug.medicine.packlist"
    },
    {
        key: "drug.medicine.package.serial_number"
    },
    {
        key: "drug.list.entryTime"
    },
    {
        key: "barcode"
    },
    {
        key: "form.preview"
    },
    {
        key: "packageBarcode"
    },
    {
        key: "drug.list.status"
    },
    {
        key: "medicine.status.examine.tip"
    },
    {
        key: "medicine.status.update.tip"
    },
    {
        key: "medicine.status.release.tip"
    },
    {
        key: "shipment.order.create.info",
        type: "warn"
    },
    {
        key: "medicine.status.examine.confirm",
        type: "warn"
    },
    {
        key: "drug.medicine"
    },
    {
        key: "placeholder.select.common",
        type: "placeholder"
    },
    {
        key: "drug.freeze.count"
    },
    {
        key: "project.task.approvalStatus.pass"
    },
    {
        key: "project.task.approvalStatus.reject"
    },
    {
        key: "subject.finish.remark"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "projects.randomization.confirmUpload",
        type: "warn"
    },
    {
        key: "drug.medicine.upload"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.tips.expireDate_batch"
    },
    {
        key: "common.tips.expireDate_batch.know"
    },
    {
        key: "shipment.order.packageMethod.single"
    },
    {
        key: "shipment.order.packageMethod.package"
    },
    {
        key: "drug.configure.spec"
    },
    {
        key: "storehouse.name"
    },
    {
        key: "drug.upload.uploadDrug"
    },
    {
        key: "common.upload.excel.tip"
    },
    {
        key: "common.download.template"
    },
    {
        key: "drug.configure.package.setting.error1",
        type: "warn"
    },
    {
        key: "drug.configure.package.setting.error2",
        type: "warn"
    },
    {
        key: "common.update.fail",
        type: "warn"
    },
    {
        key: "drug.other.add.error",
        type: "warn"
    },
    {
        key: "drug.configure.package.setting.error",
        type: "warn"
    },
    {
        key: "common.setting"
    },
    {
        key: "drug.medicine.package.mixed"
    },
    {
        key: "drug.other.count"
    },
    {
        key: "common.addTo"
    },
    {
        key: "drug.medicine.setting.application.err2"
    },
    {
        key: "drug.medicine.setting.application.err1"
    },
    {
        key: "drug.medicine.package.message"
    },
    {
        key: "common.success"
    },
    {
        key: "drug.medicine.setting.application.err4"
    },
    {
        key: "drug.medicine.setting.application.err3"
    },
    {
        key: "drug.medicine.package.other.message"
    },
    {
        key: "projects.supplyPlan.unProvideDate"
    },
    {
        key: "projects.supplyPlan.unProvideDate.tip"
    },
    {
        key: "common.required.prefix",
        type: "placeholder"
    },
    {
        key: "common.setting"
    },
    {
        key: "drug.medicine.package.setting.isOpen"
    },
    {
        key: "drug.medicine.package.setting.info"
    },
    {
        key: "drug.list.name"
    },
    {
        key: "randomization.config.factor"
    },
    {
        key: "common.yes"
    },
    {
        key: "common.no"
    },
    {
        key: "common.operation"
    },
    {
        key: "common.edit"
    },
    {
        key: "common.delete"
    },
    {
        key: "project.setting.divider.approval.control"
    },
    {
        key: "project.setting.divider.approval.control.tip"
    },
    {
        key: "drug.medicine.order.supply.ratio"
    },
    {
        key: "form.required"
    },
    {
        key: "common.settings"
    }
];