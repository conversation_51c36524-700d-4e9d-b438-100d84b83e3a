import {LocaleItem, LocaleModule, LocaleOption} from "../index";
import {localesAll, localesProject} from "./module";


const Locales: LocaleModule[] = localesAll
const editLocales: LocaleModule[] = localesProject

const getItem = (module: LocaleModule | LocaleItem, path: string[]): LocaleItem[] | LocaleItem => {
    if ("children" in module) {
        return module.children.flatMap(it => getItem(it, !!module.key ? [...path, module.key] : path))
    }
    return {...module, path: path}
}

// 模块选项
const getOption = (module: LocaleModule): LocaleOption => {
    const children = module.children
        .filter((it: LocaleModule | LocaleItem) => "children" in it)
        .map(it => getOption(it as LocaleModule))
    const option: LocaleOption = {value: module.key, label: module.title, labelEn: module.titleEn || module.title}
    if (children.length > 0) option.children = children
    return option
}

const getFlatOption = (module: LocaleModule): LocaleOption[] => {
    const option: LocaleOption = {value: module.key, label: module.title, labelEn: module.titleEn || module.title}
    const children = module.children
        .filter((it: LocaleModule | LocaleItem) => "children" in it)
        .map(it => getFlatOption(it as LocaleModule))
        .flat()
    return [option, ...children]
}

const getFields = (locales: LocaleModule[]): LocaleItem[] => {
    return locales.reduce((res: LocaleItem[], item: LocaleModule) => {
        const path = !!item.key ? [item.key] : []
        const items = item.children.flatMap(it => getItem(it, path))
        res = res.concat(items)
        return res
    }, [])
}

const getModules = (locales: LocaleModule[]): LocaleOption[] => {
    return locales.map(it => getOption(it))
}

const getFlatModules = (locales: LocaleModule[]): LocaleOption[] => {
    return locales.map(it => getFlatOption(it)).flat()
}

export const allFields: LocaleItem[] = getFields(Locales)
export const allModules: LocaleOption[] = getModules(Locales)
export const allFlatModules: LocaleOption[] = getFlatModules(Locales)

export const editFields: LocaleItem[] = getFields(editLocales)
export const editModules: LocaleOption[] = getModules(editLocales)
export const editFlatModules: LocaleOption[] = getFlatModules(editLocales)