import React from "react";
import {Dropdown, Menu} from "antd";
import {useGlobal} from "../../context/global";
import {useSafeState} from "ahooks";


export const Language = () => {

    const g = useGlobal()

    const [visible, setVisible] = useSafeState(false);

    const setLanguage = (lang) => {
        g.setLanguage(lang);
        setVisible(false);
    };

    return (
        <Dropdown
            visible={visible}
            trigger={['hover', 'click']}
            onVisibleChange={v => setVisible(v)}
            overlay={
                <React.Fragment>
                    <Menu defaultSelectedKeys={[g.lang]}>
                        <Menu.Item key='zh' onClick={() => setLanguage('zh')}>中文</Menu.Item>
                        <Menu.Item key='en' onClick={() => setLanguage('en')}>English</Menu.Item>
                        {/*<Menu.Item key='ko' onClick={() => setLanguage('ko')}>한국어</Menu.Item>*/}
                    </Menu>
                </React.Fragment>
            }>
            <svg className="iconfont mouse" width={22} height={22} >
                <use xlinkHref="#icon-qiehuanyuyan"></use>
            </svg>
        </Dropdown>
    )
};
