import {useLocation, useNavigate} from "react-router-dom";
import styled from "@emotion/styled";
import {Button, Spin, Typography} from "antd";
import {useMount, useSafeState} from "ahooks";
import {verify} from "../api/user";
import {useAuth} from '../context/auth';
import {useFetch} from '../hooks/request';
import {useGlobal} from "../context/global";
import {getCloudUrl} from "../api/env";

export const Login = () => {

    const location = useLocation();
    const navigate = useNavigate();
    const auth = useAuth();
    const g = useGlobal();
    const [loading, setLoading] = useSafeState(false);
    const {run: run_getCloudUrl} = useFetch(getCloudUrl,{manual:true,onSuccess: (result)=>{
            window.location = result.data + '/login'
            // setUrl(result.data + '/login')
        }})
    const { run: run_verify } = useFetch(
        verify,
        {
            manual: true,
            onSuccess: (result, params) => {
                if (result && result.code === 0) {
                    sessionStorage.setItem("token", result.data.token);
                    auth.setUser(result.data.user);
                    setTimeout(() => { navigate("/") }, 1000)
                }
            }
        }
    );

    useMount(
        () => {
            setLoading(true);
            let { token, lang } = location.state || { token: null };
            if (lang === 'zh' || lang === 'en') {
                g.setLanguage(lang)
            }
            if (token && token.length === 64) {
                run_verify({ token,type:1 });
            } else {
                run_getCloudUrl({})
            }
        }
    );
    if (loading) {
        return (
            <div style={{ height: "100vh", width: "100vw", display: "flex" }}>
                <div style={{ margin: "auto" }}>
                    <Spin/>
                </div>
            </div>
        )
    }
    return (
        <LoginPage>
            <Typography.Title level={2}>IRT网址正式升级</Typography.Title>
            <br />
            <br />
            <Typography.Paragraph>
                1.IRT系统网址已正式迁移至：https://cloud.clinflash.com 中，请各位用户及时记录新的网址。<br />
                2.Cloud登录账户同IRT系统登录账号 。<br />
                <br />
                1. The website of the IRT system has been officially migrated to: https://cloud.clinflash.com, please
                record the new website in time.<br />
                2. The Cloud login account is the same as the IRT system login account.<br />
            </Typography.Paragraph>
            <br />
            <br />
            <Button type="primary" onClick={() => window.location = ""}>登录Cloud / Cloud
                Login </Button>
        </LoginPage>
    );
};

const LoginPage = styled.div`
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center 0;
    zoom: 1;
    padding: 315px 200px;
    /* display: flex;
    justify-content: start;
    align-items: start; */
`;
