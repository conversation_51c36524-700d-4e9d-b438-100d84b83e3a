import {useUpdateEffect, useWebSocket} from "ahooks";
import {useAuth} from "context/auth";
import {useIntl} from "react-intl";
import {useNavigate} from "react-router-dom";
import {updateStatus} from "../api/user";
import {useFetch} from "../hooks/request";

export const WS = () => {

    const navigate = useNavigate();
    const intl = useIntl();
    const token = sessionStorage.getItem("token");
    const auth = useAuth();
    const {runAsync:updateUserStatusRun, loading:updateUserStatusLoading} = useFetch(updateStatus, {manual: true});
    

    const { readyState, sendMessage, latestMessage, disconnect, connect } = useWebSocket(
        // `${window.location.hostname === "localhost" ? "ws://localhost:8080" : `wss://${window.location.host}`}/ws/${sessionStorage.getItem("token")}/connect`,
        // `wss://cloud-dev.clinflash.com/ws/${sessionStorage.getItem("token")}/connect`,
        `wss://${new URL(auth.cloudUrl).hostname}/ws/${sessionStorage.getItem("token")}/connect`,
        {
            reconnectLimit: 20,
            reconnectInterval: 30000,
        }

    );

    const save = (id: any) => {
        updateUserStatusRun({id}).then(
            (result:any) => {
                
            }
        )

    };

    useUpdateEffect(
        () => {
            switch (latestMessage?.data) {
                case "password":
                    // message.info(intl.formatMessage({id: "tips.user.password-changed"}));
                    // setTimeout(() => { 
                    //     auth.setIdle(true);
                    //     sessionStorage.setItem("lock", "");
                    // }, 1000)
                    sessionStorage.removeItem("token");
                    var url = auth.cloudUrl.replace("/login", "");
                    var cloudUrl: any = url + "?notify=password";
                    window.location = cloudUrl;
                    break;
                case "disable":
                    // message.info(intl.formatMessage({id: "tips.user.disabled"}));
                    save(auth.user.id)
                    setTimeout(() => { 
                        sessionStorage.removeItem("token");
                        var url = auth.cloudUrl.replace("/login", "");
                        var cloudUrl: any = url + "?notify=disable";
                        window.location = cloudUrl;
                    }, 0)
                    // window.open(cloudUrl,"_self");
                    // navigate(cloudUrl); // 使用navigate函数跳转到登录页
                    // auth.user.info.status = 1
                    // save(auth.user.id)
                    break;
                case "delete":
                    // message.info(intl.formatMessage({id: "tips.user.deleted"}));
                    save(auth.user.id)
                    setTimeout(() => { 
                        sessionStorage.removeItem("token");
                        var url = auth.cloudUrl.replace("/login", "");
                        var cloudUrl: any = url + "?notify=delete";
                        window.location = cloudUrl;
                    }, 0)
                    break;
                default:
                    break;
            }
        },
        [latestMessage]
    );

    return (
        <>
        </>
    );
};