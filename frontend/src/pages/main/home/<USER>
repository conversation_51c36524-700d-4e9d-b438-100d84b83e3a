import {Checkbox, Col, Input, Row} from "antd";
import React from "react";
import {useIntl} from "react-intl";
import {useHome} from "./context";
import {usePage} from "../../../context/page";
import {SearchOutlined} from "@ant-design/icons";

export const Search = () => {
    const intl = useIntl();
    const {formatMessage} = intl;
    const home = useHome()
    const page = usePage()
    return (
        <Row gutter={8} justify="space-between">
            <Col>
                {/*<Customers projectAdmin={false} customerAdmin={false} />*/}
                <Input
                    allowClear
                    placeholder={formatMessage({id: "project.number.name"})}
                    suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                    onChange={(e) => {
                        home.setKeyword(e.target.value);
                        page.setCurrentPage(1);
                    }}
                    style={{width: "300px", marginRight: 12}}
                />
                <Checkbox onChange={(e) => {
                    home.setFocusOn(e.target.checked);
                    page.setCurrentPage(1);
                }}>
                    {formatMessage({id: 'project.focus.on'})}
                </Checkbox>
            </Col>
        </Row>
    );
};