import { <PERSON><PERSON>, Col, message, Row, Select, Space, Switch, Table, Typography } from "antd";
import React, { useEffect, useRef } from "react";
import { useSafeState } from "ahooks";
import { FormattedMessage, useIntl } from "react-intl";
import { useFetch } from "../../../hooks/request";
import { deleteLanguage, getList, getName, updateLanguage } from "../../../api/multi_language";
import { getViewMultiLanguage } from "../../../api/projects";
import { useMain } from "../context";
import { useGlobal } from "../../../context/global";
import { useAuth } from "../../../context/auth";
import { Add } from "./add";
import { Edit } from "./edit";
import { Show } from "./translate/show";
import { getProjectEnvironmentRolesPermissions } from "../../../api/user";
import { CustomConfirmModal } from "../../../components/modal";
import { HistoryList } from "./history-list";
import { getLocalesData } from "../../common/multilingual/util";
import _ from "lodash";

export const Main = () => {
    const g = useGlobal();
    const auth = useAuth();
    const [languages, setLanguages] = useSafeState([]);
    const [nameList, setNameList] = useSafeState([]);

    const intl = useIntl();

    const { runAsync: getNameRun, loading: getNameLoading } = useFetch(getName, { manual: true });
    const { runAsync: listRun, loading: getListLoading } = useFetch(getList, { manual: true });
    const { runAsync: runGetProjectList, loading: getProjectListLoading } = useFetch(getViewMultiLanguage, {
        manual: true,
    });
    const { runAsync: roleRunAsync, loading: getPermissionsLoading } = useFetch(
        getProjectEnvironmentRolesPermissions,
        { manual: true },
    );
    const { runAsync: updateLanguageRun, loading: updateLanguageLoading } = useFetch(updateLanguage, { manual: true });
    const { runAsync: deleteLanguageRun, loading: deleteLanguageLoading } = useFetch(deleteLanguage, { manual: true });

    const main = useMain();
    const [projects, setProjects] = useSafeState<any[]>([]);
    const [selectProject, setSelectProject] = useSafeState<string>("");

    const langAdd = useRef<any>();
    const langEdit = useRef<any>();
    const langShow = useRef<any>();
    const langHistory = useRef<any>();

    const [permissionList, setPermissionList] = useSafeState<any[]>([]);

    const { formatMessage } = intl;

    useEffect(() => {
        runGetProjectList().then((res: any) => {
            const projects = res.data?.map((project: any) => ({
                ...project,
                label: `${project.info.number}`,
                value: project.id,
            }));
            setProjects(projects || []);
            if (projects.length > 0) onProjectChange(projects[0]);
        });
    }, []);


    useEffect(() => {
        initData();
    }, [main.project?.id]);

    const getFieldCount = () => {
        const cohortCount = main.project?.info.type !== 1 ? main.project.envs.reduce((total: number, env: any) => {
            total += (env.cohorts?.length || 0);
            return total
        }, 0) : 1
        //项目库/系统库
        const systemLibraryCount = _.uniqBy(getLocalesData("zh")?.data || [], "key",).length;
        //项目库/项目构建----todo待完善
        const projectDesignCount = cohortCount * 0;
        //eIRT库----todo待完善
        const eIRTLibraryCount = 0;
        return systemLibraryCount + projectDesignCount + eIRTLibraryCount;
    }

    const initData = () => {
        if (!main.project?.id) return
        getNameRun({}).then((result: any) => setNameList(result.data));
        roleRunAsync({ projectId: main.project.id }).then((res: any) => {
            setPermissionList(res.data || [])
            if (!res.data?.includes("operation.projects.project.multiLanguage.view")) {
                setLanguages([])
            } else {
                refreshLanguages()
            }
        });
    };

    const refreshLanguages = () => {
        listRun({ customerId: main.project.customerId, projectId: main.project.id,}).then((result: any) => {
            const languages = result.data.map((it: any) => {
                it.progress = ["zh", "en"].includes(it.code) ? "100%" : divideToPercent(it.translationQuantity, getFieldCount());
                return it
            });
            setLanguages(languages);
        });
    }


    const onSelectChange = (value: any) => {
        const project = projects.find(it => it.value === value)
        onProjectChange(project)
    }

    const onProjectChange = (project: any) => {
        if (!project) return
        main.setProject(project);
        auth.setReportProjectId(project.value);
        setSelectProject(project.value);
    }

    // 语言-轨迹
    const trailLang = (record: any) => {
        langHistory.current.show(main.project?.customerId, main.project?.id, permissionList);
    };

    //添加语言
    const addLang = (record: any) => {
        // 使用 map 提取 id
        const languageNameList = languages ? languages.map((item: any) => item.language) : [];
        langAdd.current.show(main.project?.customerId, main.project?.id, nameList, languageNameList);
    };

    //编辑语言
    const editLang = (record: any) => {
        // 使用 map 提取 id
        const languageNameList = languages ? languages.map((item: any) => item.language) : [];
        langEdit.current.show(main.project?.customerId, main.project?.id, nameList, languageNameList, record);
    };

    //删除语言
    const deleteLang = (record: any) => {
        CustomConfirmModal({
            title: formatMessage({ id: "common.confirm.delete" }),
            okText: formatMessage({ id: "common.ok" }),
            cancelText: formatMessage({ id: "common.cancel" }),
            onOk: () => {
                deleteLanguageRun({ id: record.id }).then(() => {
                    message.success(formatMessage({ id: "message.save.success" }));
                    initData();
                });
            },
        });
    };

    const showLang = (record: any) => {
        // 使用 map 提取 id
        const languageNameList = languages ? languages.map((item: any) => item.language) : [];
        langShow.current.show(main.project, nameList, languageNameList, record, permissionList);
    };


    const onStatusChange = (status: boolean, record: any) => {
        updateLanguageRun({ customerId: main.project?.customerId, projectId: main.project?.id }, {
            id: record.id,
            customerId: main.project?.customerId,
            projectId: main.project?.id,
            code: record.code,
            language: record.language,
            status: status ? 1 : 0,
            sharedSystemLibrary: record.sharedSystemLibrary
        }).then((resp: any) => {
            message.success(resp.msg);
            initData();
        });
    };

    const getOperateBtn = (record: any) => {
        if (isDefaultLanguage(record.code)) return "-";
        const allowEdit = permissionList.includes("operation.projects.project.multiLanguage.edit")
        const allowDel = permissionList.includes("operation.projects.project.multiLanguage.delete")
        if (!allowEdit && !allowDel) return "-"
        return  <Space size={12}>
            {allowEdit && <Typography.Link onClick={() => editLang(record)}>
                {formatMessage({ id: "common.edit" })}
            </Typography.Link>}
            {allowDel && record.progress === "0%" && (<Typography.Link onClick={() => deleteLang(record)}>
                {formatMessage({ id: "common.delete" })}
            </Typography.Link>)}
        </Space>;
    };

    return (
        <>
            <Row align={"middle"}>
                {formatMessage({ id: "menu.projects" })}：
                <Select
                    style={{ width: 300 }}
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ maxWidth: 600 }}
                    onChange={onSelectChange}
                    value={selectProject}
                    showSearch
                    loading={getProjectListLoading}
                    options={projects}
                    filterOption={(input, option) =>
                        option.children.trim().toLowerCase().indexOf(input.trim().toLowerCase()) >= 0
                    }
                />
            </Row>
            <Row justify="space-between" style={{ marginTop: "16px" }}>
                <Col className="mar-rgt-12">
                    <Row justify="space-between">
                        <Row>
                            <div style={{ display: "flex", alignItems: "center"}}>
                                <svg className="iconfont" width={16} height={16}>
                                    <use xlinkHref="#icon-quanbuyuyan"></use>
                                </svg>
                                <span style={{ color: "#1D2129", fontWeight: 600, paddingLeft: "8px"}}>
                                    {formatMessage({ id: "multiLanguage.allLanguage" })}
                                </span>
                            </div>
                        </Row>
                    </Row>
                </Col>
                <Row justify="end">
                    <Col className="mar-rgt-12">
                        {permissionList.includes("operation.projects.project.multiLanguage.trail") ? (
                            <Button onClick={trailLang}>
                                <FormattedMessage id="common.history" />
                            </Button>
                        ) : null}
                        {permissionList.includes("operation.projects.project.multiLanguage.add") ? (
                            <Button type="primary" onClick={addLang} style={{ marginLeft: "12px" }}>
                                <FormattedMessage id="common.addTo" />
                            </Button>
                        ) : null}
                    </Col>
                </Row>
            </Row>
            <Table
                dataSource={languages}
                loading={getListLoading || getPermissionsLoading}
                rowKey={(record: any) => record.id}
                className="mar-top-16"
                pagination={false}
                scroll={{ y: "calc(100vh - 190px)" }}
            >
                <Table.Column
                    title={formatMessage({ id: "common.language" })}
                    dataIndex="language"
                    ellipsis
                    render={(value, record) => {
                        if (permissionList.includes("operation.projects.project.multiLanguage.details.view")) {
                            return <Button size="small" type="link" onClick={() => showLang(record)}>
                                {value}
                            </Button>
                        }
                        return value;
                    }}
                />
                <Table.Column
                    title={formatMessage({ id: "multiLanguage.enable.status" })}
                    dataIndex="status"
                    render={(value, record: any, index) => (
                        <Switch
                            disabled={!permissionList.includes("operation.projects.project.multiLanguage.edit") || isDefaultLanguage(record.code)}
                            onChange={(v: any) => onStatusChange(v, record)}
                            loading={updateLanguageLoading}
                            defaultChecked={value === 1}
                            size="small"
                        />
                    )}
                />
                <Table.Column
                    title={formatMessage({ id: "multiLanguage.translation.progress" })}
                    dataIndex="progress"
                />
                <Table.Column
                    title={formatMessage({ id: "multiLanguage.shared.system.library" })}
                    dataIndex="sharedSystemLibrary"
                    render={(value) => formatMessage({ id: value ? "common.yes" : "common.no" })}
                />
                <Table.Column
                    title={formatMessage({ id: "common.operation" })}
                    dataIndex="#"
                    width={g.lang === "zh" ? 250 : 300}
                    render={(_, record: any) => getOperateBtn(record)}
                />
            </Table>

            <Add bind={langAdd} refresh={refreshLanguages} />
            <Edit bind={langEdit} refresh={refreshLanguages} />
            <Show bind={langShow} refresh={refreshLanguages} />
            <HistoryList bind={langHistory} refresh={refreshLanguages} />
        </>
    );
};

const divideToPercent = (num1: any, num2: any) => {
    if (num1 === 0 || num2 === 0) return "0%";
    const result = (num1 / num2) * 100;
    const completionRate = (Math.floor(result * 100) / 100).toFixed(1);
    return completionRate === "0.0" ? "0.1%" : `${completionRate}%`;
};

const isDefaultLanguage = (code: string) => {
    return ["zh", "en"].includes(code)
}