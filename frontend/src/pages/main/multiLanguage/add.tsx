import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, message, Modal, Select, Radio, Space, Col } from "antd";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../context/global";
import { useFetch } from "hooks/request";
import {addLanguage} from "../../../api/multi_language";

export const Add = (props :any) => {

    const intl = useIntl();
    const {formatMessage} = intl;
    const g = useGlobal();

    const [visible, setVisible] = useSafeState<any>(false);

    const [customerId, setCustomerId] = useSafeState<any>(null);
    const [projectId, setProjectId] = useSafeState<any>(null);

    const [languageTotalOptions, setLanguageTotalOptions] = useSafeState<any>([]);
    const [languageOptions, setLanguageOptions] = useSafeState<any>([]);

    const [form] = Form.useForm();

    const { runAsync: addLanguageRun, loading: addLanguageLoading } = useFetch(addLanguage,{ manual: true });

    React.useImperativeHandle(props.bind, () => ({ show }));

    const show = (customerId: any, projectId: any, initOptions: any, languageNameList: any) => {
        setVisible(true);
        // 使用 filter 和 includes 去掉 arrayA 中存在于 arrayB 的元素
        const differenceArray = initOptions.filter((item: any) => !languageNameList.includes(item.name));
        const arr = differenceArray.map((item: any)=> {
            return {label: item.name, value: item.code, baseCode: item.baseCode};
        });
        setLanguageTotalOptions(initOptions);
        setLanguageOptions(arr);
        setCustomerId(customerId);
        setProjectId(projectId);
    };

    const hide = () => {
        setVisible(false);
        setCustomerId(null);
        setProjectId(null);
        form.resetFields();
        props.refresh();
    };

    const save = () => {
        form.validateFields().then(values => {
            const option = languageTotalOptions?.find((item: any) => item.code === values.code);
            addLanguageRun({ customerId:  customerId, projectId: projectId }, {
                customerId: customerId,
                projectId: projectId,
                code: values.code,
                language: option.name,
                baseLanguageCode: option.baseCode,
                status: 1,
                sharedSystemLibrary: values.sharedSystemLibrary,
            }).then((resp:any) => {
                message.success(resp.msg)
                props.refresh();
                hide();
            })
        }).catch(() => {})
    };

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang === "zh" ? 5: 7 },
        },
    }

    return (
        <React.Fragment>
            <Modal
                title={<FormattedMessage id={"common.addTo"} />}
                open={visible}
                onCancel={hide}
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                okText={formatMessage({id: 'common.ok'})}
                okButtonProps={{loading: addLanguageLoading}}
                onOk={save}
            >
                <Form form={form} {...formItemLayout}>
                    <Form.Item
                        label={formatMessage({id: 'common.language'})}
                        name='code'
                        rules={[{required: true}]}
                    >
                        <Select
                            className="full-width"
                            allowClear
                            placeholder={formatMessage({id: 'placeholder.select.common'})}
                            options={languageOptions}
                        />
                    </Form.Item>
                    <Form.Item
                        className="mar-ver-5" name="sharedSystemLibrary"
                        label={formatMessage({ id: 'multiLanguage.shared.system.library' })}
                        style={{marginBottom:24}}
                    >
                        <Radio.Group className="full-width">
                            <Space >
                                <Col style={{ marginRight: 24 }}>
                                    <Radio value={1}>
                                        {intl.formatMessage({id: "common.yes"})}
                                    </Radio>
                                </Col>
                                <Col style={{ marginRight: 24 }}>
                                    <Radio value={0}>
                                        {intl.formatMessage({id: "common.no"})}
                                    </Radio>
                                </Col>
                            </Space>

                        </Radio.Group>
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    )
};
