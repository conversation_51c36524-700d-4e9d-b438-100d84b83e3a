import React, {useEffect} from "react";
import {Bad<PERSON>, Menu} from "antd";
import {useNavigate} from "react-router-dom";
import {useRafInterval, useSafeState} from "ahooks";
import {useFetch} from "../../../hooks/request";
import styled from "@emotion/styled";
import {useGlobal} from "../../../context/global";
import {messageSystemQuantity, messageQuantity, mailAuth } from "../../../api/page_notice";

export const Message = () => {
    const g = useGlobal();
    const navigate = useNavigate();
    // const [mailAuthority, setMailAuthority] = useSafeState(0);

    const { runAsync: run_message_quantity } = useFetch(messageQuantity, { manual: true });
    const { runAsync: run_message_system_quantity } = useFetch(messageSystemQuantity, { manual: true });
    const { runAsync: run_mailAuth } = useFetch(mailAuth, { manual: true });

    
    const getNew = () => {
        onMailAuth()
    }

    const onMailAuth = () => {
        run_mailAuth({ 
        }).then((resp) => {
            const result = resp.data;
            // setMailAuthority(result)
            if(result === 0){
                getMessageSystemQuantity()
            }else if(result === 1){
                getMessageQuantity()
            }
        })
    }

    const getMessageQuantity = () => {
        run_message_quantity({
        }).then((data) => {
            const result = data.data;
            g.setQuantity(result)
        });
    }

    const getMessageSystemQuantity = () => {
        run_message_system_quantity({
        }).then((data) => {
            const result = data.data;
            g.setQuantity(result)
        });
    }

    useRafInterval(() => getNew(), 1000 * 60 * 5);
    useEffect(onMailAuth, [g.lang]);

    return (
        <div
            style={{ display: "flex", marginRight: "12px", position: "relative" }}
            onClick={()=> navigate("/message-center")}
        >
            <Badge dot={g.quantity > 0} style={{ position: "absolute", left: "14px", top: "3px" }} />
            <svg offset={[-8]} className="iconfont mouse" width={22} height={22}>
                <use xlinkHref="#icon-xiaoxi"/>
            </svg>

        </div>
    );
};
