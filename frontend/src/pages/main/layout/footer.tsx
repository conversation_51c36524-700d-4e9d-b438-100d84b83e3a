import {ReactNode} from "react";
import {Layout} from "antd";
import styled from "@emotion/styled";

const Footer = ({ children }: { children: ReactNode }) => {
    return (
        <LayoutFooter className="footer">
            {children}
        </LayoutFooter>
    )
};

export default Footer;

const LayoutFooter = styled(Layout.Footer)`
    margin        : 0 8px !important;
    height         : 50px !important;
    line-height    : 50px !important;
    position       : relative;
    bottom         : 0;
    z-index        : 1;
    background     : white !important;
    border-top     : #f0f0f0 solid 1px;
    display        : flex;
    flex-direction : column;
    justify-content: center;
`;