import React from "react";
import {Dropdown, Menu} from "antd";
import {
    DatabaseOutlined,
    HomeOutlined,
    LockOutlined,
    MedicineBoxOutlined,
    MenuOutlined,
    ProjectOutlined,
    SettingOutlined,
    SmileOutlined,
    TeamOutlined,
    UserOutlined
} from "@ant-design/icons";
import {FormattedMessage} from "react-intl";
import {useLocation, useNavigate} from "react-router-dom";
import {MainContext} from "../context";
import {useSafeState} from "ahooks";

const Navigation = () => {

    const context = React.useContext(MainContext);

    const navigate = useNavigate();

    const location = useLocation();

    const [current, setCurrent] = useSafeState("/home");

    const [visible, setVisible] = useSafeState(false);

    const icons = {
        home: <HomeOutlined />,
        project: <ProjectOutlined />,
        site: <MedicineBoxOutlined />,
        storehouse: <DatabaseOutlined />,
        setting: <SettingOutlined />,
        smile: <SmileOutlined />,
        user: <UserOutlined />,
        team: <TeamOutlined />,
        lock: <LockOutlined />,

    };

    const push = (path) => {
        setVisible(false);
        navigate(path);
    };

    React.useEffect(
        () => {
            setCurrent(location.pathname);
        },
        [location.pathname]
    );

    return (
        <React.Fragment>
            <Dropdown
                open={visible}
                trigger={['hover', 'click']}
                onOpenChange={v => setVisible(v)}
                overlay={
                    <Menu style={{ minWidth: "150px", width: "auto" }} onClick={(e) => setCurrent(e.key)} selectedKeys={current} >
                        {
                            context.menu.filter(item => item.show).map(
                                it => (
                                    it.children
                                        ?
                                        <Menu.ItemGroup title={<span>{icons[it.icon]} <FormattedMessage id={it.text} /></span>} key={it.path}>
                                            {
                                                it.children.filter(item => item.show).map(
                                                    child => (
                                                        <Menu.Item
                                                            key={it.path + child.path} onClick={() => push(it.path + child.path)}
                                                            className={window.location.pathname === (it.path + child.path) ? "text-primary" : ""}
                                                        >
                                                            <span> {<FormattedMessage id={child.text} />}</span>
                                                        </Menu.Item>
                                                    )
                                                )
                                            }
                                        </Menu.ItemGroup>
                                        :
                                        <Menu.Item
                                            key={it.path} onClick={() => push(it.path)}
                                            className={window.location.pathname === it.path ? "text-primary" : ""}
                                        >
                                            {icons[it.icon]}
                                            <span> {<FormattedMessage id={it.text} />}</span>
                                        </Menu.Item>

                                )
                            )
                        }
                        {/* {
                            context.menu.filter(it => !it.children && it.show).map(
                                it => (
                                    <Menu.Item
                                        key={it.path} onClick={() => push(it.path)}
                                        className={window.location.pathname === it.path ? "text-primary" : ""}
                                    >
                                        {icons[it.icon]}<span> {<FormattedMessage id={it.text} />}</span>
                                    </Menu.Item>
                                )
                            )
                        }
                        {
                            context.menu.filter(it => !!it.children && it.show).map(
                                parent => (
                                    <Menu.ItemGroup title={<FormattedMessage id={parent.text} />} key={parent.path}>
                                        {
                                            parent.children.filter(item => item.show).map(
                                                child => (
                                                    <Menu.Item
                                                        key={parent.path + child.path} onClick={() => push(parent.path + child.path)}
                                                        className={window.location.pathname === (parent.path + child.path) ? "text-primary" : ""}
                                                    >
                                                        {icons[child.icon]}<span> {<FormattedMessage id={child.text} />}</span>
                                                    </Menu.Item>
                                                )
                                            )
                                        }
                                    </Menu.ItemGroup>
                                )
                            )
                        } */}
                    </Menu>
                }
            >
                <div style={{ padding: '0 12px' }}><MenuOutlined /></div>
            </Dropdown>
        </React.Fragment>
    )
};

export default React.memo(Navigation);
