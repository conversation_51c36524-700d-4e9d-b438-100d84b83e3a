import React from "react";
import {Menu} from "antd";
import {
    DatabaseOutlined,
    HomeOutlined,
    LockOutlined,
    MedicineBoxOutlined,
    SettingOutlined,
    SmileOutlined,
    TeamOutlined,
    UserOutlined,
} from "@ant-design/icons";
import {FormattedMessage} from "react-intl";
import {useLocation, useNavigate} from "react-router-dom";
import {useMount, useSafeState} from "ahooks";
import {auth_main, menu_main} from "../../../data/menu";
import {useAuth} from "../../../context/auth";
import {useFetch} from "../../../hooks/request";
import {getMainRolePermission} from "../../../api/user";
import styled from "@emotion/styled";

export const Navigation = () => {
    const navigate = useNavigate();

    const location = useLocation();

    const [current, setCurrent] = useSafeState("/");

    const icons = {
        // TODO
        home: <HomeOutlined />,
        project: <i className="iconfont icon-quanbuxiangmu" style={{ marginRight: 4 }} />,
        site: <MedicineBoxOutlined />,
        storehouse: <DatabaseOutlined />,
        // TODO
        setting: <SettingOutlined />,
        smile: <SmileOutlined />,
        user: <UserOutlined />,
        team: <TeamOutlined />,
        lock: <LockOutlined />,
        report: <i className="iconfont icon-baobiao" style={{ marginRight: 4 }} />,
        multiLanguage: <i className="iconfont icon-quanbuyuyan" style={{ marginRight: 4 }} />,
    };

    React.useEffect(() => {
        setCurrent(location.pathname);
    }, [location.pathname]);
    const auth = useAuth();
    const [menu, setMenu] = useSafeState([]);
    const { runAsync: getMainRolePermissionRun } = useFetch(getMainRolePermission, { manual: true });

    useMount(() => {
        getMainRolePermissionRun().then((result) => {
            setMenu(auth_main(menu_main, result.data));
            auth.setPermissions(result.data);
        });
    });

    return (
        <React.Fragment>
            <StyledMenu mode="horizontal" onClick={(e) => setCurrent(e.key)} selectedKeys={current}>
                {menu
                    .filter((item) => item.show)
                    .map((it) =>
                        it.children ? (
                            <Menu.SubMenu key={it.path} icon={icons[it.icon]} title={<FormattedMessage id={it.text} />}>
                                {it.children
                                    .filter((item) => item.show)
                                    .map((child) => (
                                        <Menu.Item
                                            key={it.path + child.path}
                                            onClick={() => navigate(it.path + child.path)}
                                        >
                                            <span> {<FormattedMessage id={child.text} />}</span>
                                        </Menu.Item>
                                    ))}
                            </Menu.SubMenu>
                        ) : (
                            <Menu.Item key={it.path} onClick={() => navigate(it.path)}>
                                {icons[it.icon]}
                                <span>{<FormattedMessage id={it.text} />}</span>
                            </Menu.Item>
                        ),
                    )}
            </StyledMenu>
        </React.Fragment>
    );
};

const StyledMenu = styled(Menu)`
    height: 50px;
    
    &.ant-menu-horizontal {
        border-bottom: none;
    }
`;
