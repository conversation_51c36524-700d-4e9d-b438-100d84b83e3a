import {ExclamationCircleFilled, QuestionCircleFilled} from "@ant-design/icons";
import styled from "@emotion/styled";
import {Checkbox, Col, Form, Row, Switch, Table, Tooltip} from "antd";
import {ColumnsType} from "antd/lib/table";
import React, {useEffect, useState} from "react";
import {FormattedMessage, useIntl} from "react-intl";
import useSafeState from "ahooks/lib/useSafeState";

type ParamsType =
  | "unblindingSms"
  | "unblindingProcess"
  | "pvUnblindingSms"
  | "pvUnblindingProcess"
  | "unblindingType"
  | "pvUnblindingType"
  | "unblindingCode"
    | "ipUnblindingType"
    | "ipUnblindingSms"
    | "ipUnblindingProcess"
;

interface ProjectNoticeInterface {
  project: any;
  form: any;
  disable: any;
  unblindingControl: any;
}

export const UnblindingControlView = (props: ProjectNoticeInterface) => {
  const [data, setData] = useState([
    {
      unblindingType: props.project.info.unblindingType,
      unblindingSms: props.project.info.unblindingSms,
      unblindingProcess: props.project.info.unblindingProcess,
      unblindingCode: props.project.info.unblindingCode,
    },
    {
      pvUnblindingType: props.project.info.pvUnblindingType,
      pvUnblindingSms: props.project.info.pvUnblindingSms,
      pvUnblindingProcess: props.project.info.pvUnblindingProcess
    },
    {
      ipUnblindingType: props.project.info.ipUnblindingType,
      ipUnblindingSms: props.project.info.ipUnblindingSms,
      ipUnblindingProcess: props.project.info.ipUnblindingProcess
    },
  ]);

  const intl = useIntl();
  const [unblindingComfirm, setUnblindingComfirm] = useState(false);
  const [pvComfirm, setPvConfirm] = useState(false);
  const [ipComfirm, setIpConfirm] = useState(false);
  const [unblindingControl, setUnblindingControl] = useSafeState(0);
  const { formatMessage } = intl;

  useEffect(() => {
    setUnblindingComfirm(
      !!data[0].unblindingSms || !!data[0].unblindingProcess
    );
    setPvConfirm(!!data[1].pvUnblindingSms || !!data[1].pvUnblindingProcess);
    setIpConfirm(!!data[2].ipUnblindingSms || !!data[2].ipUnblindingProcess);
    setUnblindingControl(props.unblindingControl);
  }, [props.unblindingControl]);

  const handleChangeConfirm = (e: any, index: number) => {
    if (index === 0) {
      setUnblindingComfirm(e.target.checked);
    } else if (index === 1){
      setPvConfirm(e.target.checked);
    }
    else if (index === 2){
      setIpConfirm(e.target.checked);
    }


    let value :number = 0

    if (e.target.checked) {
      value = 1
    }

    if (index === 0 ){
      data[index]["unblindingSms"] = 0;
      data[index]["pvUnblindingProcess"] = value;
    }
    if (index === 1 ){
      data[index]["pvUnblindingSms"] = 0;
      data[index]["pvUnblindingProcess"] = value;
    }
    if (index === 2 ){
      data[index]["ipUnblindingSms"] = 0;
      data[index]["ipUnblindingProcess"] = value;
    }


    let indexType:string = "approval"
    if (index === 1) {
      indexType = "pvApproval"
    }
    if (index === 2) {
      indexType = "ipApproval"
    }

    setData([...data]);
    handleData([...data]);
    approvalChange(
      e,
      index,
      indexType
    )
  };

  const SmsTip = (
    <span style={{ color: "#677283", marginLeft: 12 }}>
      <ExclamationCircleFilled style={{ color: "#FFAE00", marginRight: 4 }} />
      <FormattedMessage id={"project.setting.msg.unblind.sms"} />
    </span>
  );

  const approvalChange = (e: any, index: number, type: any) => {

    if(type === "approval"){
      data[index]["unblindingType"] = e.target.checked;
    } else if(type === "pvApproval"){
      data[index]["pvUnblindingType"] = e.target.checked;
    } else if(type === "ipApproval"){
      data[index]["ipUnblindingType"] = e.target.checked;
    }

    if (!e.target.checked) {
      if (type === "pvApproval") {
        setPvConfirm(false);
        data[index].pvUnblindingSms = 0;
        data[index].pvUnblindingProcess = 0;
      }
      if (type === "ipApproval") {
        setIpConfirm(false);
        data[index].ipUnblindingSms = 0;
        data[index].ipUnblindingProcess = 0;
      }
      if (type === "approval" && (data[index].unblindingCode === 0 || data[index].unblindingCode === false)) {
        setUnblindingComfirm(false);
        data[index].unblindingCode = 0;
        data[index].unblindingProcess = 0;
        data[index].unblindingSms = 0;
      }

    } 

    if (
      index === 0 &&
      (data[index].unblindingCode ||
        data[index].unblindingProcess ||
        data[index].unblindingSms)
    ) {
      data[index].unblindingType = 1;
    }

    if (
      index === 0 &&
      (data[index].unblindingProcess || data[index].unblindingSms)
    ) {
      setUnblindingComfirm(true);
    } else if (
      index === 1 &&
      (data[index].pvUnblindingProcess || data[index].pvUnblindingSms)
    ) {
      setPvConfirm(true);
      data[index].pvUnblindingType = 1;
    } else if (
        index === 2 &&
        (data[index].ipUnblindingProcess || data[index].ipUnblindingSms)
    ) {
      setIpConfirm(true);
      data[index].ipUnblindingType = 1;
    }
    setData([...data]);
    handleData([...data]);

  };

  const handleChange = (e: any, index: number, type: ParamsType) => {
    data[index][type] = e.target.checked;
    if (e.target.checked) {
      if(type === "unblindingSms"){
        setUnblindingComfirm(false);
        data[index].unblindingProcess = 1;
        data[index].unblindingSms = 1;
      }

      if(type === "pvUnblindingSms"){
        setPvConfirm(e.target.checked);
        data[index].pvUnblindingSms = 1;
        data[index].pvUnblindingProcess = 1;
      }

      if(type === "ipUnblindingSms"){
        setIpConfirm(e.target.checked);
        data[index].ipUnblindingSms = 1;
        data[index].ipUnblindingProcess = 1;
      }
    }

    if (!e.target.checked) {
      if (type === "pvUnblindingType") {
        setPvConfirm(false);
        data[index].pvUnblindingSms = 0;
        data[index].pvUnblindingProcess = 0;
      }

      if (type === "pvUnblindingSms" && (data[index].pvUnblindingProcess === 0 || data[index].pvUnblindingProcess === false)) {
        data[index]["pvUnblindingType"] = e.target.checked;
        setPvConfirm(false);
        data[index].pvUnblindingProcess = 0;
      }

      if (type === "pvUnblindingProcess") {
        data[index]["pvUnblindingType"] = e.target.checked;
        setPvConfirm(false);
        data[index].pvUnblindingSms = 0;
      }
      
      if (type === "unblindingType") {
        setUnblindingComfirm(false);
        data[index].unblindingCode = 0;
        data[index].unblindingProcess = 0;
        data[index].unblindingSms = 0;
      }

      if (type === "unblindingCode" && (data[index].unblindingProcess === 0 || data[index].unblindingProcess === false) && (data[index].unblindingSms === 0 || data[index].unblindingSms === false)) {
        data[index]["unblindingType"] = e.target.checked;
        setUnblindingComfirm(false);
        data[index].unblindingProcess = 0;
        data[index].unblindingSms = 0;
      }

      if (type === "unblindingSms" && (data[index].unblindingProcess === 0 || data[index].unblindingProcess === false)) {
        data[index]["unblindingType"] = e.target.checked;
        setUnblindingComfirm(false);
        data[index].unblindingProcess = 0;
        if(data[index].unblindingCode === 0 || data[index].unblindingCode === false){
          data[index].unblindingCode = 0;
        }
      }

      if (type === "unblindingProcess") {
        data[index]["unblindingType"] = e.target.checked;
        setUnblindingComfirm(false);
        data[index].unblindingSms = 0;
        if(data[index].unblindingCode === 0 || data[index].unblindingCode === false){
          data[index].unblindingCode = 0;
        }
      }
      if (type === "ipUnblindingType") {
        data[index]["ipUnblindingType"] = e.target.checked;
        setIpConfirm(false);
        data[index].ipUnblindingSms = 0;
        data[index].ipUnblindingProcess = 0;
      }
      if (type === "ipUnblindingProcess") {
        data[index]["ipUnblindingType"] = e.target.checked;
        setIpConfirm(false);
        data[index].ipUnblindingSms = 0;
      }
    }

    if (
      index === 0 &&
      (data[index].unblindingCode ||
        data[index].unblindingProcess ||
        data[index].unblindingSms)
    ) {
      data[index].unblindingType = 1;
    }

    if (
      index === 0 &&
      (data[index].unblindingProcess || data[index].unblindingSms)
    ) {
      setUnblindingComfirm(true);
    } else if (
      index === 1 &&
      (data[index].pvUnblindingProcess || data[index].pvUnblindingSms)
    ) {
      setPvConfirm(true);
      data[index].pvUnblindingType = 1;
    } else if (
        index === 2 &&
        (data[index].ipUnblindingProcess || data[index].ipUnblindingSms)
    ) {
      console.log("dfd")
      setIpConfirm(true);
      data[index].ipUnblindingType = 1;
    }
    setData([...data]);
    handleData([...data]);
  };

  const ControlSwitch = (props: any) => {
    const isOpen = props.value === 1;
    return (
      <Switch
        size="small"
        checked={isOpen}
        disabled={props.disabled}
        onChange={() => {
          const value = props.value === 0 ? 1 : 0;
          props.onChange(value);
        }}
      />
    );
  };

  const typeArr :({ types: ParamsType; intl: string } | { types: ParamsType; intl: string } | { types: ParamsType; intl: string })[] = [
      {types:"unblindingType", intl:"notice.subject.unblinding"},
      {types:"pvUnblindingType", intl:"notice.subject.pv.unblinding"},
      {types:"ipUnblindingType", intl:"notice.subject.ip.unblinding"},
  ]

  const columns = [
    {
      title:
        <span>
          <span>
            {formatMessage({ id: 'notice.subject.unblinding.type' })}
          </span>
          <span style={{ color: 'red', display: 'inline-block', verticalAlign: 'middle', marginTop: "2px", marginRight: "5px" }} >*</span>
        </span>
      ,
      width: 200,
      render: (_, record, index) => (
        <Checkbox
          disabled={props.disable}
          checked={record[typeArr[index].types]}
          onChange={(e) =>
            handleChange(
              e,
              index,
                typeArr[index].types
            )
          }
        >
            <FormattedMessage id={typeArr[index].intl} />
        </Checkbox>
      ),
    },
    {
      title:
        <span>
          <span>
            {formatMessage({ id: 'project.setting.unblind.method' })}
          </span>
          <span style={{ color: 'red', display: 'inline-block', verticalAlign: 'middle', marginTop: "2px", marginRight: "5px" }} >*</span>
        </span>,
      render: (_, record, index) => (
        <>
          {index === 0 && (
            <div>
              <Row>
                <Checkbox
                  disabled={props.disable}
                  checked={unblindingComfirm}
                  onChange={(e) => handleChangeConfirm(e, index)}
                >
                  <FormattedMessage id={"project.setting.checkbox.approval"} />
                </Checkbox>
              </Row>
              <div className="control-wrap">
                <div className="line" />
                <div className="control-items">
                  <Row justify="start" >
                    <Col>
                      <Checkbox
                        disabled={props.disable}
                        checked={record.unblindingSms}
                        onChange={(e) => handleChange(e, index, "unblindingSms")}
                      >
                        <FormattedMessage id={"project.setting.checkbox.unblind.sms"} />

                      </Checkbox>
                    </Col>
                    <Col style={{
                      width: "88%",
                      lineHeight: "22px",
                      marginTop: 6,
                      marginLeft: -12,
                      color: "#677283",
                    }}>
                      {!!record.unblindingSms && SmsTip}
                    </Col>
                  </Row>
                  <Row>
                    <Checkbox
                      disabled={props.disable}
                      checked={record.unblindingProcess}
                      onChange={(e) =>
                        handleChange(e, index, "unblindingProcess")
                      }
                    >
                      <FormattedMessage id={"project.setting.checkbox.unblind.process"} />
                    </Checkbox>
                  </Row>
                </div>
              </div>
              <Row justify="start">
                <Col>
                  <Checkbox
                    disabled={props.disable}
                    checked={record.unblindingCode}
                    onChange={(e) => handleChange(e, index, "unblindingCode")}
                  >
                    <FormattedMessage id={"project.setting.checkbox.unblinded-code"} />
                  </Checkbox>
                </Col>
              </Row>
            </div>
          )
          }
          {
          index === 1 && (
            <div>
              <Checkbox
                disabled={props.disable}
                checked={pvComfirm}
                onChange={(e) => handleChangeConfirm(e, index)}
              >
                <FormattedMessage id={"project.setting.checkbox.approval"} />
              </Checkbox>
              <div className="control-wrap">
                <div className="line" />
                <div className="control-items">
                  <Row justify="start">
                    <Col>
                      <Checkbox
                        disabled={props.disable}
                        checked={record.pvUnblindingSms}
                        onChange={(e) =>
                          handleChange(e, index, "pvUnblindingSms")
                        }
                      >
                        <FormattedMessage id={"project.setting.checkbox.unblind.sms"} />

                      </Checkbox>
                    </Col>
                    <Col style={{
                      width: "88%",
                      lineHeight: "22px",
                      marginTop: 6,
                      marginLeft: -12,
                      color: "#677283",
                    }}>
                      {!!record.pvUnblindingSms && SmsTip}
                    </Col>
                  </Row>
                  <Row>
                    <Checkbox
                      disabled={props.disable}
                      checked={record.pvUnblindingProcess}
                      onChange={(e) =>
                        handleChange(e, index, "pvUnblindingProcess")
                      }
                    >
                      <FormattedMessage id={"project.setting.checkbox.unblind.process"} />
                    </Checkbox>
                  </Row>
                </div>
              </div>
            </div>
          )}
          {
          index === 2 &&
          <div>
            <Checkbox
                disabled={props.disable}
                checked={ipComfirm}
                onChange={(e) => handleChangeConfirm(e, index)}
            >
              <FormattedMessage id={"project.setting.checkbox.approval"} />
            </Checkbox>
            <div className="control-wrap">
              <div className="line" />
              <div className="control-items">
                <Row justify="start">
                  <Col>
                    <Checkbox
                        disabled={props.disable}
                        checked={record.ipUnblindingSms}
                        onChange={(e) =>
                            handleChange(e, index, "ipUnblindingSms")
                        }
                    >
                      <FormattedMessage id={"project.setting.checkbox.unblind.sms"} />

                    </Checkbox>
                  </Col>
                  <Col style={{
                    width: "88%",
                    lineHeight: "22px",
                    marginTop: 6,
                    marginLeft: -12,
                    color: "#677283",
                  }}>
                    {!!record.ipUnblindingSms && SmsTip}
                  </Col>
                </Row>
                <Row>
                  <Checkbox
                      disabled={props.disable}
                      checked={record.ipUnblindingProcess}
                      onChange={(e) =>
                          handleChange(e, index, "ipUnblindingProcess")
                      }
                  >
                    <FormattedMessage id={"project.setting.checkbox.unblind.process"} />
                  </Checkbox>
                </Row>
              </div>
            </div>
          </div>
          }
        </>
      ),
    },
  ] as ColumnsType<any>;


  // const handleSubmit = () => {
  //   if (!data[0].unblindingType && !data[1].pvUnblindingType) {
  //     return message.error("请选择揭盲类型");
  //   }
  //   if (
  //     !!data[0].unblindingType &&
  //     !data[0].unblindingCode &&
  //     !data[0].unblindingProcess &&
  //     !data[0].unblindingSms
  //   ) {
  //     return message.error("请选择紧急揭盲的控制方式");
  //   }
  //   if (
  //     !!data[1].pvUnblindingType &&
  //     !data[1].pvUnblindingProcess &&
  //     !data[1].pvUnblindingSms
  //   ) {
  //     return message.error("请选择PV揭盲的控制方式");
  //   }

  //   const submitData = { ...data[0], ...data[1] };
  //   Object.keys(submitData).forEach((key) => {
  //     submitData[key as ParamsType] = +(submitData[key as ParamsType] as
  //       | boolean
  //       | number);
  //   });
  // };

  const onUnblindControl = (value: any) => {
    if (value) {
      setUnblindingControl(1);
    } else {
      setUnblindingControl(0);
      setUnblindingComfirm(false);
      setPvConfirm(false);
      setIpConfirm(false)
      var data = [
        {
          unblindingType: 0,
          unblindingSms: 0,
          unblindingProcess: 0,
          unblindingCode: 0,
        },
        {
          pvUnblindingType: 0,
          pvUnblindingSms: 0,
          pvUnblindingProcess: 0,
        },
        {
          ipUnblindingType: 0,
          ipUnblindingSms: 0,
          ipUnblindingProcess: 0,
        },
      ];
      setData(data);
      //handleData(data);
    }
  };


  const handleData = (data: any) => {
    const submitData = { ...data[0], ...data[1],...data[2] };
    Object.keys(submitData).forEach((key) => {
      submitData[key as ParamsType] = +(submitData[key as ParamsType] as
        | boolean
        | number);
    });
    const tmpInfo = props.form.getFieldValue("info");
    props.project.info.unblindingType = submitData["unblindingType"];
    props.project.info.unblindingSms = submitData["unblindingSms"];
    props.project.info.unblindingCode = submitData["unblindingCode"];
    props.project.info.unblindingProcess = submitData["unblindingProcess"];
    props.project.info.pvUnblindingType = submitData["pvUnblindingType"];
    props.project.info.pvUnblindingSms = submitData["pvUnblindingSms"];
    props.project.info.pvUnblindingProcess = submitData["pvUnblindingProcess"];
    props.project.info.ipUnblindingType = submitData["ipUnblindingType"];
    props.project.info.ipUnblindingSms = submitData["ipUnblindingSms"];
    props.project.info.ipUnblindingProcess = submitData["ipUnblindingProcess"];
    tmpInfo.unblindingType = submitData["unblindingType"];
    tmpInfo.unblindingSms = submitData["unblindingSms"];
    tmpInfo.unblindingCode = submitData["unblindingCode"];
    tmpInfo.unblindingProcess = submitData["unblindingProcess"];
    tmpInfo.pvUnblindingType = submitData["pvUnblindingType"];
    tmpInfo.pvUnblindingSms = submitData["pvUnblindingSms"];
    tmpInfo.pvUnblindingProcess = submitData["pvUnblindingProcess"];
    tmpInfo.ipUnblindingType = submitData["ipUnblindingType"];
    tmpInfo.ipUnblindingSms = submitData["ipUnblindingSms"];
    tmpInfo.ipUnblindingProcess = submitData["ipUnblindingProcess"];
    props.form.setFieldsValue({ ...props.project, info: tmpInfo });
  }

  return (
    <Wrap>
      <Form.Item
        label={
          <>
            <FormattedMessage
              id={"project.setting.switch.unblind.control"}
            />
            <Tooltip
              overlayInnerStyle={{ width: 500 }}
              placement="top"
              title={
                <FormattedMessage id="tool.tip.unblind.control" />
              }
            >
              <QuestionCircleFilled
                style={{ marginLeft: "4px", color: "#D0D0D0" }}
              />
            </Tooltip>
          </>
        }
        name={["info", "unblindingControl"]}
        className="mar-ver-5"
      >
        <ControlSwitch
          disabled={props.disable}
          onChange={onUnblindControl}
        />
      </Form.Item>

      {unblindingControl === 1 &&
        <Table dataSource={data} columns={columns} pagination={false} bordered style={{ marginBottom: "20px", marginTop: "20px" }} />
      }

    </Wrap>
  );
};

const Wrap = styled.div`
  .control-wrap {
    margin-left: 8px;
    display: flex;
    align-items: center;

    .line {
      background: #e0e1e2;
      width: 1px;
      height: 40px;
    }

    .control-items {
      margin-left: 16px;
    }
  }
  .ant-checkbox-wrapper {
    line-height: 32px;
  }
`;
