import React, {useEffect} from "react"
import {Form, Input, message, Modal, Radio, Select} from "antd";
import {useIntl} from "react-intl";
import {useGlobal} from "../../../context/global";
import {useFetch} from "hooks/request";
import {add, hasBindUser, listPool, update} from "api/projects_roles";
import {useProjectRole} from "pages/main/projects/project-role-context";
import {useAuth} from "context/auth";
import {useMount, useSafeState} from "ahooks";
import {QuestionCircleFilled} from "@ant-design/icons";
import {CustomConfirmModal} from "../../../components/modal";
import _ from "lodash";

export const ProjectRoleEdit = (props:any) => {

    const g = useGlobal()
    const auth = useAuth()
    const ctx = useProjectRole()
    const intl = useIntl();
    const {formatMessage} = intl;

    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState([]);

    const [form] = Form.useForm();
    const hide = () => {
        setSend(true);
        form.resetFields()
        ctx.setRole(null)
        ctx.setEditVisible(false)
    }

    const scope = [
        {label:"study", value:"study"},
        {label:"site", value:"site"},
        {label:"depot", value:"depot"},
    ]

    const {runAsync:addRun, loading: addLoading} = useFetch(add, {manual:true});
    const {runAsync:updateRun, loading: updateLoading} = useFetch(update, {manual:true});
    const {runAsync:hasBindUserRun, loading: hasBindUserLoading} = useFetch(hasBindUser, {manual:true});
    const {runAsync:listPoolRun, } = useFetch(listPool, {manual:true});

    const formChange = () => {
        if (ctx.role) {
            const a = _.cloneDeep(oldData[0]);
            // console.log("1===" + JSON.stringify(a)); 
            let b = _.cloneDeep(form.getFieldsValue());
            // console.log("2===" + JSON.stringify(b)); 
            if (!compareObjects(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }

        }
    }

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1: any, obj2: any) {
        for (let key in obj1) {
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                    if (!arraysAreEqual(obj1[key], obj2[key])) {
                        return false;
                    }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1: any, arr2: any) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }

    const updateValue = (value:any) =>{
        updateRun({id:ctx.role.id}, {...value}).then(
            (result:any) => {
                message.success(result.msg).then(() => {});
                hide()
                ctx.setRefresh(ctx.refresh+1)
            }
        )
    }

    const save = () => {
        form.validateFields().then(
            (value) => {
                if (ctx.role){
                    // update
                    value.permissions = ctx.role.permissions
                    value.projectId = ctx.role.projectId
                    value.customerId = ctx.role.customerId
                    value.name = ctx.role.name
                    value.template = ctx.role.template
                    if (value.status === 1){
                        updateValue(value)
                    }else if (value.status === 2){
                        hasBindUserRun({id:ctx.role.id}).then(
                            (result:any)=>{
                                if  (result.data >0){
                                    CustomConfirmModal({
                                        title: formatMessage({id:"project.role.invalid.title"}),
                                        content: formatMessage({id:"project.role.invalid.content"}),
                                        okText: formatMessage({id: 'common.ok'}),
                                        onOk: () => {
                                            updateValue(value)
                                        }
                                    })
                                }else {
                                    updateValue(value)
                                }
                            }
                        )
                    }

                }else{
                    value.permissions = []
                    value.projectId = props.project.id
                    value.customerId = auth.customerId
                    const role = ctx.rolesPool.find((i:any)=>i.id === value.roleId);
                    value.name = role.name
                    value.template = props.project.info.researchAttribute ===0?1:2
                    addRun({projectId:props.project.id,customerId:auth.customerId,...value}).then(
                        (result:any) => {
                            message.success(result.msg).then(() => {});
                            hide()
                            ctx.setRefresh(ctx.refresh+1)
                        }
                    )
                }
            }
        )
    }
    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 4: 8 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 20: 18 },
        },
    };

    const setInit = () => {
        if(ctx.editVisible){
            form.setFieldsValue({...ctx.role})
        }
    }

    useEffect(setInit, [ctx.editVisible])
    useMount(()=>{
        listPoolRun({template:props.project.info.researchAttribute===0?1:2}).then((result:any)=>{
            ctx.setRolesPool(result.data)
            setOldData(result.data);
        })
    })
    return (
        <React.Fragment>
            <Modal
                destroyOnClose={true}
                width={500}
                title={ctx.role? formatMessage({id: "common.edit"}):formatMessage({id: "common.addTo"})}
                visible={ctx.editVisible}
                onOk={save}
                okText={formatMessage({ id: 'common.ok' })}
                onCancel={hide}
                centered
                maskClosable={false}
                okButtonProps={{ disabled: ctx.role ? send : false }}
                confirmLoading = {updateLoading || addLoading}
            >
                <Form
                    form={form}
                    {...formItemLayout}
                    onValuesChange={formChange}
                >
                    {ctx.role?
                        <Form.Item label={formatMessage({id: "roles.name"})} >
                            {ctx.role.name}
                        </Form.Item>:
                        <Form.Item label={formatMessage({id: "roles.name"})} name="roleId"
                                   rules={[{required: true}]}>
                            <Select className="full-width" disabled={ctx.role}>
                                {
                                    ctx.rolesPool.map((value: any) =>
                                        <Select.Option value={value.id}>{value.name}</Select.Option>
                                    )
                                }
                            </Select>
                        </Form.Item>
                    }
                    <Form.Item label={formatMessage({id: "common.classification"})} name="scope"
                               rules={[{required: true}]}
                               tooltip={{
                                   title:
                                       <>
                                           {formatMessage({id: 'permission.scope.study'})}<br/>
                                           {formatMessage({id: 'permission.scope.depot'})}<br/>
                                           {formatMessage({id: 'permission.scope.site'})}
                                       </>
                                   ,
                                   icon: <QuestionCircleFilled style={{color: "#D0D0D0"}}/>
                               }}>
                        <Select className="full-width" options={scope}>
                        </Select>
                    </Form.Item>
                        <Form.Item label={formatMessage({id: "common.status"})} name="status"  initialValue={1}>
                        <Radio.Group disabled={ctx.role?.name === "Project-Admin"}>
                            <Radio value={1}>{formatMessage({id: "common.effective"})}</Radio>
                            <Radio value={2}>{formatMessage({id: "common.invalid"})}</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: "common.description"})} name="description">
                        <Input.TextArea showCount autoSize={{ minRows: 3, maxRows: 10 }} maxLength={500}/>
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    )
}