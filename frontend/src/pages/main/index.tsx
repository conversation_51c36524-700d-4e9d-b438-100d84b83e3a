import {Layout} from 'antd';
import {Outlet} from "react-router-dom";
import {MainContextProvider} from './context';
import {Content} from './layout/content';
import {Header} from "./layout/header";
import {PageContextProvider} from "../../context/page";

export const Main = () => {
    return (
        <MainContextProvider>
            <PageContextProvider>
            <Layout className="right">
                <Header />
                <Content>
                    <Outlet />
                </Content>
            </Layout>
            </PageContextProvider>
        </MainContextProvider>
    );
};

