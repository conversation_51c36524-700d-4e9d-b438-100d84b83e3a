import {Col, Input, Modal, Row, Table, Typography} from "antd";
import {useSafeState} from "ahooks";
import {useImperativeHandle} from "react";
import {useFetch} from "../../../hooks/request";
import {FormattedMessage, useIntl} from "react-intl";
import {downloadHistoryReport, getDownloadHistory} from "../../../api/report";
import {SearchOutlined} from "@ant-design/icons";
import {useMain} from "../context";

const formatFileSize = (fileSize: number) => {
    let temp;
    if (fileSize < 1024) {
        return fileSize + "B";
    } else if (fileSize < 1024 * 1024) {
        temp = fileSize / 1024;
        temp = temp.toFixed(2);
        return temp + "KB";
    } else if (fileSize < 1024 * 1024 * 1024) {
        temp = fileSize / (1024 * 1024);
        temp = temp.toFixed(2);
        return temp + "MB";
    } else {
        temp = fileSize / (1024 * 1024 * 1024);
        temp = temp.toFixed(2);
        return temp + "GB";
    }
};

export const DownloadHistory = (props: any) => {
    const [visible, setVisible] = useSafeState<boolean>(false);
    const { runAsync: get_download_history } = useFetch(getDownloadHistory, { manual: true });
    const { runAsync: download_history } = useFetch(downloadHistoryReport, { manual: true });
    const [historyList, setHistoryList] = useSafeState([]);
    const [reportName, setReportName] = useSafeState<string>("");
    const intl = useIntl();
    const main = useMain();

    const { formatMessage } = intl;

    const show = (type: number, reportKey: string) => {
        get_download_history({
            reportType: type,
            projectId: main.project.id,
            envId: main.env.id,
        }).then((res: any) => {
            setHistoryList(res.data);
        });
        setReportName(formatMessage({ id: reportKey }));
        setVisible(true);
    };

    const hide = () => {
        setVisible(false);
    };

    const download = (id: string) => {
        download_history(id);
    };

    useImperativeHandle(props.bind, () => ({ show }));

    // @ts-ignore
    return (
        <>
            <Modal
                open={visible}
                centered
                onCancel={hide}
                title={formatMessage({id: "report.history"}) + "-" + reportName}
                closable
                footer={null}
                className="custom-large-modal"
                style={{minHeight: 400, maxHeight: 600}}
                maskClosable={false}
            >
                {/*<Row>*/}
                {/*    <Col span={24} style={{ border: '1px solid #165DFF', borderRadius: '2px 2px 2px 2px', backgroundColor:"#e8efff" ,padding: '10px', height: "32px", marginBottom: "24px", display: 'flex', alignItems: 'center'}}>*/}
                {/*        <div style={{ verticalAlign: 'middle' }}>*/}
                {/*            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2725" width="16" height="16" style={{ verticalAlign:"middle",}}><path d="M512 83.2a428.8 428.8 0 1 1 0 857.6 428.8 428.8 0 0 1 0-857.6z m44.8 345.6h-89.6v256h-44.8v64h172.8v-64h-44.8l6.4-256z m-89.6 0h-38.4v64h44.8v-64zM512 256c-38.4 0-64 25.6-64 64s25.6 64 64 64 64-25.6 64-64-25.6-64-64-64z" fill="#2B74EA" p-id="2726"></path></svg>*/}
                {/*            /!*<span style={{ paddingLeft: '8px' }}><FormattedMessage id="report.export.history.prompt"/></span>*!/*/}
                {/*        </div>*/}
                {/*    </Col>*/}
                {/*</Row>*/}
                <Table
                    dataSource={historyList}
                    rowKey={(record: any) => record.id}
                    pagination={false}
                    scroll={{ x: 800 }}
                >
                    <Table.Column
                        title={formatMessage({id: "common.serial"})}
                        render={(value, record, index) => index + 1}
                        dataIndex="#"
                        width={60}
                    />
                    <Table.Column
                        title={formatMessage({id: "report.filename"})}
                        ellipsis
                        dataIndex={"filename"}
                        width={500}
                        filterIcon={(filtered: boolean) => (
                            <SearchOutlined style={{color: filtered ? "#1890ff" : "#677283"}}/>
                        )}
                        filterDropdown={({setSelectedKeys, selectedKeys, confirm}) => (
                            <div style={{padding: "8px 12px"}} className={"filename-search"}>
                                <Input.Search
                                    placeholder={formatMessage({id: "report.filename.search.required"})}
                                    enterButton
                                    value={selectedKeys[0]}
                                    onChange={(e) => {
                                        setSelectedKeys(e.target.value ? [e.target.value] : []);
                                    }}
                                    onSearch={() => confirm()}
                                    onPressEnter={() => confirm()}
                                />
                            </div>
                        )}
                        onFilter={(value, record: any) => {
                            return record.filename.toLowerCase().includes((value as string).toLowerCase());
                        }}
                    />
                    <Table.Column
                        title={formatMessage({id: "report.filesize"})}
                        dataIndex={"fileSize"}
                        render={(value) => {
                            return formatFileSize(value);
                        }}
                        width={90}
                    />
                    <Table.Column
                        title={formatMessage({id: "report.exportTime"})}
                        dataIndex={"downloadTimeStr"}
                        width={230}
                    />
                    <Table.Column
                        title={formatMessage({id: "common.operation"})}
                        width={100}
                        fixed="right"
                        render={(_, record: any) => (
                            <Typography.Link
                                onClick={() => {
                                    download(record.id);
                                }}
                            >
                                {formatMessage({id: "common.download"})}
                            </Typography.Link>
                        )}
                    />
                </Table>
            </Modal>
        </>
    );
};
