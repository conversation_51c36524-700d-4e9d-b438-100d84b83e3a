import {Button, Form, Input, message, Row, Space} from "antd";
import Modal from "antd/lib/modal/Modal";
import {useSafeState} from "ahooks";
import {MutableRefObject, useEffect, useImperativeHandle} from "react";
import {useIntl} from "react-intl";
import {createTemplates, updateTemplates} from "../../../api/report";
import {useFetch} from "../../../hooks/request";
import {FieldPicker} from "./field_picker";
import {useMain} from "../context";
import {useGlobal} from "../../../context/global";
import {useReport} from "./context";

interface CreateProps {
    bind: MutableRefObject<any>;
    template: Template;
    defaultFields: any[];
    customFields: any[];
    refresh: Function;
}

interface Template {
    id?: string;
    name?: string;
    type: number;
    targetFields: string[];
}

export const Custom = (props: CreateProps) => {
    const [visible, setVisible] = useSafeState(false);
    const intl = useIntl();
    const [customFields, setCustomFields] = useSafeState<any[]>([]);
    const [targetKeys, setTargetKeys] = useSafeState<any[]>([]);
    const [form] = Form.useForm();
    const { runAsync: runCreateTemplate } = useFetch(createTemplates, { manual: true });
    const { runAsync: runUpdateTemplate } = useFetch(updateTemplates, { manual: true });
    const [defaultDisable, setDefaultDisable] = useSafeState<boolean>(false);
    const main = useMain();
    const g = useGlobal()
    const reportContext = useReport();

    const { formatMessage } = intl;

    const show = () => {
        setVisible(true);
    };

    const hide = () => {
        setTargetKeys([]);
        form.resetFields();
        setDefaultDisable(false);
        setVisible(false);
    };

    const handleOk = () => {
        form.validateFields().then((value) => {
            if (props.template.id) {
                runUpdateTemplate(props.template.id, {
                    name: value.name,
                    fields: targetKeys,
                    projectId: main.project.id,
                    envId: main.env.id,
                }).then(() => {
                    message.success(formatMessage({ id: "report.template.edit.success" }));
                    setVisible(false);
                    props.refresh();
                });
            } else {
                runCreateTemplate({
                    name: value.name,
                    fields: targetKeys,
                    projectId: main.project.id,
                    envId: main.env.id,
                    type: props.template.type,
                }).then(() => {
                    message.success(formatMessage({ id: "report.template.create.success" }));
                    setVisible(false);
                    props.refresh();
                });
            }
        });
    };

    useImperativeHandle(props.bind, () => ({ show }));

    useEffect(() => {
        setTargetKeys(props.template.targetFields || []);
        form.resetFields();
    }, [props.template]);

    const formatFields = (fields: any[]) => {
        return fields.map((field: any) => {
            field.title = formatMessage({ id: field.key });
            if (field.children) {
                field.children = field.children.map((child: any) => {
                    child.title = formatMessage({ id: child.key });
                    if (
                        child.key === "report.attributes.info.subject.number"
                    ) {
                        let subjectReplaceText = formatMessage({ id: "report.attributes.info.subject.number" })
                        if(g.lang === "zh"){
                            if(main.attr?.info.subjectReplaceText !== null && main.attr?.info.subjectReplaceText !== ""){
                                subjectReplaceText = main.attr?.info.subjectReplaceText
                            }
                            // else if(main.attr?.info.subjectReplaceTextEn !== null && main.attr?.info.subjectReplaceTextEn !== ""){
                            //     subjectReplaceText = main.attr?.info.subjectReplaceTextEn
                            // }
                        }else if(g.lang === "en"){
                            if(main.attr?.info.subjectReplaceTextEn !== null && main.attr?.info.subjectReplaceTextEn !== ""){
                                subjectReplaceText = main.attr?.info.subjectReplaceTextEn
                            }else if(main.attr?.info.subjectReplaceText !== null && main.attr?.info.subjectReplaceText !== ""){
                                subjectReplaceText = main.attr?.info.subjectReplaceText
                            }
                        }
                        child.title = subjectReplaceText;
                    }
                    if (
                        child.key === "report.attributes.random.subject.number.replace"
                    ) {
                        let subjectReplaceText = formatMessage({ id: "report.attributes.random.subject.number.replace" })
                        if(g.lang === "zh"){
                            if(main.attr?.info.subjectReplaceText !== null && main.attr?.info.subjectReplaceText !== ""){
                                subjectReplaceText = main.attr?.info.subjectReplaceText
                            }
                            // else if(main.attr?.info.subjectReplaceTextEn !== null && main.attr?.info.subjectReplaceTextEn !== ""){
                            //     subjectReplaceText = main.attr?.info.subjectReplaceTextEn
                            // }
                        }else if(g.lang === "en"){
                            if(main.attr?.info.subjectReplaceTextEn !== null && main.attr?.info.subjectReplaceTextEn !== ""){
                                subjectReplaceText = main.attr?.info.subjectReplaceTextEn
                            }else if(main.attr?.info.subjectReplaceText !== null && main.attr?.info.subjectReplaceText !== ""){
                                subjectReplaceText = main.attr?.info.subjectReplaceText
                            }
                        }
                        child.title = formatMessage({ id: "subject.replace" })  + "  " + subjectReplaceText;
                    }

                    if (child.key === "report.attributes.dispensing.out-visit-dispensing.reason"){
                        child.title = reportContext.outVisitStr + formatMessage({id:"common.reason"})

                    }
                    if (child.key === "report.attributes.dispensing.out-visit-dispensing.remark"){
                        child.title = reportContext.outVisitStr + "-" + formatMessage({id:"subject.unblinding.reason.remark"})
                    }
                    return child;
                });
            }
            return field;
        });
    };

    useEffect(() => {
        if (props.customFields) {
            setCustomFields(formatFields(props.customFields));
        }
    }, [props.customFields]);

    const useDefault = () => {
        let keys: any[] = [];
        props.defaultFields.forEach((field) => {
            keys.push(field.key);
        });
        if (main.project.info.type === 1) {
            keys = keys.filter(
                (key) => key !== "report.attributes.random.stage" && key !== "report.attributes.random.cohort",
            );
        } else if (main.project.info.type === 2) {
            keys = keys.filter((key) => key !== "report.attributes.random.stage");
        } else if (main.project.info.type === 3) {
            keys = keys.filter((key) => key !== "report.attributes.random.cohort");
        }
        setTargetKeys(keys);
        setTimeout(() => {
            setDefaultDisable(true);
        }, 100);
    };

    useEffect(() => {
        if (defaultDisable) {
            setDefaultDisable(false);
        }
    }, [targetKeys]);

    return (
        <Modal
            open={visible}
            centered
            onCancel={hide}
            maskClosable={false}
            title={props.template?.id ? formatMessage({ id: "common.edit" }) : formatMessage({ id: "common.create" })}
            destroyOnClose
            className="custom-medium-modal field-picker"
            footer={
                <Row justify={"space-between"}>
                    <Button type={"link"} onClick={useDefault} disabled={defaultDisable}>
                        {formatMessage({ id: "report.template.default" })}
                    </Button>
                    <Space>
                        <Button
                            onClick={() => {
                                hide();
                            }}
                        >
                            {formatMessage({ id: "common.cancel" })}
                        </Button>
                        <Button type={"primary"} onClick={handleOk}>
                            {formatMessage({ id: "common.ok" })}
                        </Button>
                    </Space>
                </Row>
            }
        >
            <Form form={form}>
                <Form.Item
                    label={formatMessage({ id: "report.template.name" })}
                    rules={[
                        {
                            required: true,
                            message: formatMessage({ id: "report.template.name.required" }),
                        },
                    ]}
                    name={"name"}
                    initialValue={props.template.name}
                >
                    <Input placeholder={formatMessage({ id: "common.required.prefix" })} showCount maxLength={50} />
                </Form.Item>
            </Form>
            <FieldPicker data={customFields} targetKeys={targetKeys} setTargetKeys={setTargetKeys}></FieldPicker>
        </Modal>
    );
};
