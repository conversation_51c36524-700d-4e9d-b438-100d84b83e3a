import {Checkbox, Col, Input, Popover, Row, Space, Tree} from "antd";
import {CloseOutlined, SearchOutlined} from "@ant-design/icons";
import {useIntl} from "react-intl";
import {DataNode} from "antd/es/tree";
import {useSafeState} from "ahooks";
import {useEffect} from "react";
import styled from "@emotion/styled";
import React from "react";

interface FieldPickerProps {
    data: any[];
    targetKeys: string[];
    setTargetKeys: Function;
}

export const FieldPicker = (props: FieldPickerProps) => {
    const intl = useIntl();
    const { formatMessage } = intl;
    const [checkedSet, setCheckedSet] = useSafeState<Set<string>>(
        new Set(
            props.targetKeys.concat(
                props.data
                    .filter((item) => {
                        return item.required;
                    })
                    .map((item) => item.key),
            ),
        ),
    );
    const [total, setTotal] = useSafeState<number>(0);

    const generateLeftTree = (treeNodes: DataNode[] = [], checkedKeys: string[] = []): DataNode[] => {
        const cur = (treeNodes: DataNode[] = [], checkedKeys: string[] = []): DataNode[] =>
            treeNodes.map(({ children, required, ...props }: any) => {
                return {
                    ...props,
                    children: cur(children, checkedKeys),
                    disabled: required,
                };
            });

        return cur(treeNodes, checkedKeys);
    };

    const generateRightTree = (treeNodes: DataNode[] = [], checkedKeys: string[] = []): DataNode[] => {
        const nodes: DataNode[] = [];

        checkedKeys.forEach((key) => {
            treeNodes.forEach(
                (node) =>
                    node.children ?
                    node.children.forEach((child: any) => {
                        if (child.key === key && !child.required) {
                            nodes.push(child);
                        }
                    }):(
                        node.key === key &&
                        nodes.push(node)
                    ),
            );
        });

        return nodes;
    };

    const [leftTreeData, setLeftTreeData] = useSafeState<DataNode[]>(generateLeftTree(props.data, props.targetKeys));

    useEffect(() => {
        let count: number = 0;
        props.data.forEach((node) => {
            if (node.required) {
                count++;
                return;
            }
            node.children ?
                node.children.forEach(() => {
                    count++;
                }):(
                    count++
                );
        });
        setTotal(count);
    }, []);

    useEffect(() => {
        setCheckedSet(new Set(props.targetKeys));
    }, [props.targetKeys]);

    const checkedAll = (checked: boolean) => {
        if (checked) {
            props.data.forEach((node) => {
                if (node.children) {
                    node.children &&
                        node.children.forEach((child: any) => {
                            checkedSet.add(child.key);
                        });
                } else {
                    checkedSet.add(node.key);
                }
            });
        } else {
            checkedSet.clear();
        }
        setCheckedSet(new Set(checkedSet));
        props.setTargetKeys(Array.from(checkedSet));
    };

    const onLeftCheck = (_: any, { checked, node }: any) => {
        if (node.children && node.children.length) {
            if (checked) {
                node.children.forEach((child: any) => checkedSet.add(child.key as string));
            } else {
                node.children.forEach((child: any) => checkedSet.delete(child.key as string));
            }
        } else {
            if (checked) {
                checkedSet.add(node.key as string);
            } else {
                checkedSet.delete(node.key as string);
            }
        }
        setCheckedSet(new Set(checkedSet));
        props.setTargetKeys(Array.from(checkedSet));
    };

    const onDrop = ({ dragNode, dropPosition }: any) => {
        const requiredKeys: any[] = [];
        props.data.forEach((field) => {
            if (field.required) {
                requiredKeys.push(field.key);
            }
            if (field.children) {
                field.children.forEach((child: any) => {
                    if (child.required) {
                        requiredKeys.push(child.key);
                    }
                });
            }
        });
        if (dropPosition === -1) {
            dropPosition = 0;
        }

        const keys = [...props.targetKeys.filter((key) => requiredKeys.indexOf(key) === -1)];
        const prePos = keys.indexOf(dragNode.key as string);
        keys.splice(prePos, 1);
        keys.splice(dropPosition, 0, dragNode.key as string);
        setCheckedSet(new Set(requiredKeys.concat(keys)));
        props.setTargetKeys(requiredKeys.concat(keys));
    };

    const onSearch = (e: any) => {
        const value = e.target.value;
        const data = generateLeftTree(props.data, props.targetKeys);
        data.forEach((node) => {
            // @ts-ignore
            if (node.title.indexOf(value) > -1) return;
            let children: DataNode[] = [];
            node.children &&
                node.children.forEach((child) => {
                    // @ts-ignore
                    if (child.title.indexOf(value) > -1) {
                        children.push(child);
                    }
                });
            node.children = children;
        });
        setLeftTreeData(
            data.filter((node) => {
                return node.children && node.children.length !== 0;
            }),
        );
    };

    return (
        <FieldPickerBody>
            <Row wrap={false}>
                <Col span={12}>
                    <Left>
                        <LeftHeader>
                            <Checkbox
                                style={{ marginLeft: 12 }}
                                onChange={(e) => {
                                    checkedAll(e.target.checked);
                                }}
                                checked={props.targetKeys.length === total}
                                indeterminate={props.targetKeys.length !== 0 && props.targetKeys.length !== total}
                            >
                                {formatMessage({ id: "report.attributes" })}
                                <span style={{ fontSize: 12, fontWeight: 400, color: "#4E5969" }}>
                                    ({props.targetKeys.length}/{total}
                                    {formatMessage({ id: "report.attributes.unit" })})
                                </span>
                            </Checkbox>
                        </LeftHeader>
                        <LeftBody>
                            <Input
                                placeholder={formatMessage({ id: "common.required.prefix" })}
                                suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                                onChange={onSearch}
                            />
                            <Tree
                                blockNode
                                style={{ marginTop: 8 }}
                                checkable
                                defaultExpandAll
                                treeData={leftTreeData}
                                checkedKeys={props.targetKeys}
                                onCheck={onLeftCheck}
                            />
                        </LeftBody>
                    </Left>
                </Col>
                <Col span={12}>
                    <RightHeader>
                        <Row justify={"space-between"}>
                            <Col>
                                {formatMessage({ id: "report.custom" })}
                                <span style={{ fontSize: 12, fontWeight: 400, color: "#4E5969" }}>
                                    ({props.targetKeys.length}
                                    {formatMessage({ id: "report.attributes.unit" })})
                                </span>
                            </Col>
                            <Col>
                                <Popover
                                    arrowPointAtCenter
                                    color={"white"}
                                    placement={"topRight"}
                                    overlayClassName={"field-picker"}
                                    overlayStyle={{ maxWidth: 800 }}
                                    content={
                                        props.targetKeys.length ? (
                                            <Preview>
                                                {props.data.map((item) => {
                                                    if (item.required) {
                                                        return (
                                                            <PreviewContent>
                                                                {formatMessage({ id: item.key })}
                                                            </PreviewContent>
                                                        );
                                                    }
                                                    if (item.children && item.children.length) {
                                                        for (const child of item.children) {
                                                            if (child.required) {
                                                                return (
                                                                    <PreviewContent>
                                                                        {formatMessage({ id: child.key })}
                                                                    </PreviewContent>
                                                                );
                                                            }
                                                        }
                                                    }
                                                })}
                                                {generateRightTree(props.data, props.targetKeys).map((node) => {
                                                    return <PreviewContent>{node.title}</PreviewContent>;
                                                })}
                                            </Preview>
                                        ) : null
                                    }
                                >
                                    <PreviewBtn>{formatMessage({ id: "form.preview" })}</PreviewBtn>
                                </Popover>
                            </Col>
                        </Row>
                    </RightHeader>
                    <RightBody className={"right-tree"}>
                        {props.data.map((item) => {
                            if (item.required) {
                                return (
                                    <Row>
                                        <div style={{ lineHeight: "36px", marginLeft: 5, color: "#4E5969" }}>
                                            <Space>
                                                <svg
                                                    width="11"
                                                    height="11"
                                                    viewBox="0 0 11 11"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path
                                                        fillRule="evenodd"
                                                        clipRule="evenodd"
                                                        d="M1.48394 10.0078H1.48032C1.34991 10.0072 1.22516 9.95435 1.13392 9.86114C0.955879 9.68835 0.934132 9.41014 1.0831 9.21173L3.2547 6.22028L1.62675 4.592C1.46626 4.43224 1.43383 4.18437 1.54784 3.98875C1.726 3.73477 1.96572 3.53011 2.2446 3.39401C2.51978 3.26013 2.82194 3.19089 3.12796 3.19153C3.35757 3.19159 3.58558 3.22909 3.80304 3.30265L3.88639 3.31123C3.96168 3.3165 4.02832 3.32401 4.09082 3.33405C4.6033 3.30119 5.08616 3.08243 5.44872 2.71878C5.8442 2.43898 6.11684 2.0178 6.2102 1.54245C6.2102 1.46693 6.2102 1.38746 6.20714 1.31251C6.10674 0.973975 6.18335 0.607719 6.41106 0.337845C6.61987 0.125813 6.90523 0.00687412 7.2028 0.00781808C7.45956 0.00807848 7.70829 0.096957 7.90699 0.259448L7.93255 0.284756C8.17843 0.524749 10.6314 2.97905 10.655 3.00264C10.8673 3.21345 10.9862 3.50049 10.9853 3.79955C10.9853 4.09911 10.8652 4.38615 10.652 4.59646C10.4851 4.74388 10.2684 4.82241 10.0458 4.81618C9.91376 4.81318 9.7822 4.8001 9.65218 4.77702C9.58407 4.77306 9.51592 4.77148 9.44769 4.77231H9.42823C9.02429 4.82317 8.65818 5.0355 8.41343 5.36089C7.99492 5.78015 7.7233 6.32359 7.63924 6.90994C7.65113 7.01218 7.67256 7.18771 7.67644 7.21103C7.93911 8.01354 7.63408 8.89266 6.93078 9.36011C6.85202 9.40881 6.7612 9.43462 6.66857 9.4345C6.53618 9.43501 6.40915 9.38249 6.31574 9.28871L4.70676 7.67767L1.77345 9.9073C1.69077 9.97202 1.58893 10.0074 1.48394 10.0078Z"
                                                        fill="#ADB2BA"
                                                    />
                                                </svg>
                                                <span>{formatMessage({ id: item.key })}</span>
                                            </Space>
                                        </div>
                                    </Row>
                                );
                            }
                            if (item.children && item.children.length) {
                                for (const child of item.children) {
                                    if (child.required) {
                                        return (
                                            <Row>
                                                <div style={{ lineHeight: "36px", marginLeft: 5, color: "#4E5969" }}>
                                                    <Space>
                                                        <svg
                                                            width="11"
                                                            height="11"
                                                            viewBox="0 0 11 11"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                fillRule="evenodd"
                                                                clipRule="evenodd"
                                                                d="M1.48394 10.0078H1.48032C1.34991 10.0072 1.22516 9.95435 1.13392 9.86114C0.955879 9.68835 0.934132 9.41014 1.0831 9.21173L3.2547 6.22028L1.62675 4.592C1.46626 4.43224 1.43383 4.18437 1.54784 3.98875C1.726 3.73477 1.96572 3.53011 2.2446 3.39401C2.51978 3.26013 2.82194 3.19089 3.12796 3.19153C3.35757 3.19159 3.58558 3.22909 3.80304 3.30265L3.88639 3.31123C3.96168 3.3165 4.02832 3.32401 4.09082 3.33405C4.6033 3.30119 5.08616 3.08243 5.44872 2.71878C5.8442 2.43898 6.11684 2.0178 6.2102 1.54245C6.2102 1.46693 6.2102 1.38746 6.20714 1.31251C6.10674 0.973975 6.18335 0.607719 6.41106 0.337845C6.61987 0.125813 6.90523 0.00687412 7.2028 0.00781808C7.45956 0.00807848 7.70829 0.096957 7.90699 0.259448L7.93255 0.284756C8.17843 0.524749 10.6314 2.97905 10.655 3.00264C10.8673 3.21345 10.9862 3.50049 10.9853 3.79955C10.9853 4.09911 10.8652 4.38615 10.652 4.59646C10.4851 4.74388 10.2684 4.82241 10.0458 4.81618C9.91376 4.81318 9.7822 4.8001 9.65218 4.77702C9.58407 4.77306 9.51592 4.77148 9.44769 4.77231H9.42823C9.02429 4.82317 8.65818 5.0355 8.41343 5.36089C7.99492 5.78015 7.7233 6.32359 7.63924 6.90994C7.65113 7.01218 7.67256 7.18771 7.67644 7.21103C7.93911 8.01354 7.63408 8.89266 6.93078 9.36011C6.85202 9.40881 6.7612 9.43462 6.66857 9.4345C6.53618 9.43501 6.40915 9.38249 6.31574 9.28871L4.70676 7.67767L1.77345 9.9073C1.69077 9.97202 1.58893 10.0074 1.48394 10.0078Z"
                                                                fill="#ADB2BA"
                                                            />
                                                        </svg>
                                                        <span>{formatMessage({ id: child.key })}</span>
                                                    </Space>
                                                </div>
                                            </Row>
                                        );
                                    }
                                }
                            }
                        })}
                        <Tree
                            style={{ marginTop: 8 }}
                            blockNode
                            draggable
                            treeData={generateRightTree(props.data, props.targetKeys)}
                            titleRender={(node) => {
                                return (
                                    <Row justify={"space-between"}>
                                        <Col>{node.title}</Col>
                                        <Col>
                                            <CloseOutlined
                                                onClick={() => {
                                                    checkedSet.delete(node.key as string);
                                                    setCheckedSet(new Set(checkedSet));
                                                    props.setTargetKeys(Array.from(checkedSet));
                                                }}
                                                style={{ color: '#677283' }}
                                            />
                                        </Col>
                                    </Row>
                                );
                            }}
                            onDrop={onDrop}
                        />
                    </RightBody>
                </Col>
            </Row>
        </FieldPickerBody>
    );
};

const FieldPickerBody = styled.div`
    width: 100%;
    border: 1px solid #dddedf;
    border-bottom: 0;
`;

const LeftHeader = styled.div`
    height: 36px;
    line-height: 36px;
    border-bottom: 1px solid #dddedf;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 400;
`;

const RightHeader = styled.div`
    height: 36px;
    border-bottom: 1px solid #dddedf;
    line-height: 36px;
    padding: 0 12px;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 400;
`;

const PreviewBtn = styled.div`
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
    :hover {
        color: #165dff;
    }
`;

const Preview = styled.div`
    background-color: #f2f3f5;
    overflow-y: hidden;
    overflow-x: auto;
    white-space: nowrap;
`;

const PreviewContent = styled.span`
    margin: 0 12px;
    height: 38px;
    line-height: 38px;
    font-size: 10px;
    font-weight: 500;
    font-family: PingFang SC;
    color: #677283;
    display: inline-block;
`;

const LeftBody = styled.div`
    padding: 8px 12px 0 12px;
    overflow: auto;
    height: 420px;
`;

const RightBody = styled.div`
    padding: 8px 12px 0 12px;
    overflow: auto;
    height: 420px;
`;

const Left = styled.div`
    border-right: 1px solid #dddedf;
    height: 100%;
`;
