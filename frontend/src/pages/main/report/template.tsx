import {Button, message, Modal, Row, Space, Table, Typography,} from "antd";
import {useSafeState} from "ahooks";
import React, {MutableRefObject, useEffect, useImperativeHandle, useRef,} from "react";
import {Custom} from "./custom";
import {useFetch} from "../../../hooks/request";
import {copyTemplates, deleteTemplates, getTemplates,} from "../../../api/report";
import {useIntl} from "react-intl";
import {InfoCircleFilled, PlusOutlined} from "@ant-design/icons";
import {useMain} from "../context";
import {Title as CustomTitle} from "components/title";

interface Report {
    name: string;
    type: number;
    fields: any[];
    customFields: any[];
}

interface TemplateProps {
    bind: MutableRefObject<any>;
    report: Report;
    exportBind: MutableRefObject<any>;
    record: any;
    isOpenPackage: any;
}

export const Template = (props: TemplateProps) => {
    const [visible, setVisible] = useSafeState<boolean>(false);
    const create: any = useRef();
    const { runAsync: runGetTemplates } = useFetch(getTemplates, {
        manual: true,
    });
    const { runAsync: runDeleteTemplate } = useFetch(deleteTemplates, {
        manual: true,
    });
    const { runAsync: runCopyTemplates } = useFetch(copyTemplates, {
        manual: true,
    });
    const [templates, setTemplates] = useSafeState([]);
    const intl = useIntl();
    const [template, setTemplate] = useSafeState<any>([]);
    const { formatMessage } = intl;
    const main = useMain();
    const [roles, setRoles] = useSafeState<any[]>([]);

    const show = ({ roles }: any) => {
        setRoles(roles);
        setVisible(true);
    };

    const hide = () => {
        setRoles([]);
        setVisible(false);
    };

    const get_templates = () => {
        runGetTemplates({
            type: props.report.type,
            projectId: main.project.id,
            envId: main.env.id,
        }).then((res: any) => {
            const list = res.data || [];
            list.unshift({
                name: formatMessage({ id: "report.template.name.default" }),
            });
            setTemplates(list);
        });
    };

    const edit = (record: any) => {
        setTemplate({
            id: record.id,
            name: record.name,
            targetFields: record.fields,
            type: props.report.type,
        });
        create.current.show();
    };

    const del = (record: any) => {
        Modal.confirm({
            centered: true,
            title: formatMessage({ id: "report.template.delete.confirm" }),
            icon: <InfoCircleFilled style={{ color: "#4072e2" }} />,
            content: <div style={{ height: 8 }}></div>,
            onOk: () => {
                runDeleteTemplate(record.id).then(() => {
                    get_templates();
                    message.success(formatMessage({ id: "common.delete.ok" }));
                });
            },
            okText: formatMessage({ id: "common.ok" }),
        });
    };

    const copy = (record: any) => {
        runCopyTemplates(record.id).then(() => {
            get_templates();
            message.success(formatMessage({ id: "common.copy.ok" }));
        });
    };

    const add = () => {
        let targetFields: any[] = [];
        let customFields = props.report.customFields;
        customFields.forEach((menu: any) => {
            if (menu.required) {
                targetFields.push(menu.key);
            }
            menu.children?.forEach((item: any) => {
                if (item.required) {
                    targetFields.push(item.key);
                }
            });
        });
        setTemplate({
            fields: template.fields,
            type: props.report.type,
            targetFields: targetFields,
        });
        create.current.show();
    };

    useImperativeHandle(props.bind, () => ({ show }));
    useEffect(() => {
        setTemplate({
            ...template,
            fields: props.report && props.report.customFields,
        });
        if (props.report) {
            get_templates();
        }
    }, [props.report]);

    return (
        <>
            <Modal
                open={visible}
                centered
                onCancel={hide}
                title={`${formatMessage({ id: "report.customTemplate" })}-${
                    props.report
                        ? formatMessage({ id: props.report.name })
                        : null
                }`}
                destroyOnClose
                className={"custom-large-modal"}
                footer={null}
                maskClosable={false}
            >
                <Row justify={"space-between"}>
                    <CustomTitle
                        name={intl.formatMessage({ id: "report.template.all" })}
                    />
                    <Button
                        onClick={() => {
                            add();
                        }}
                        type={"primary"}
                    >
                        <PlusOutlined />
                        {formatMessage({ id: "common.create" })}
                    </Button>
                </Row>

                <Table
                    dataSource={templates}
                    rowKey={"id"}
                    className="mar-top-16"
                    pagination={false}
                >
                    <Table.Column
                        title={formatMessage({ id: "common.serial" })}
                        render={(value, record, index) => index + 1}
                        dataIndex="#"
                        width={60}
                    />
                    <Table.Column
                        title={formatMessage({ id: "report.template.name" })}
                        dataIndex="name"
                        width={600}
                        ellipsis
                    />
                    <Table.Column
                        title={formatMessage({ id: "common.operation" })}
                        render={(_, record: any, index) => {
                            return (
                                <>
                                    <Space>
                                        <Typography.Link
                                            onClick={() => {
                                                props.exportBind.current.show({
                                                    type: props.record.type,
                                                    name: props.record.name,
                                                    multiDefaultFields:props.record.multiDefaultFields,
                                                    defaultFields:
                                                        props.record
                                                            .defaultFields,
                                                    customizable: true,
                                                    templateId: record.id,
                                                    roles,
                                                    isOpenPackage:
                                                        props.isOpenPackage,
                                                    template:
                                                        record.fields?.map(
                                                            (
                                                                field: string
                                                            ) => ({
                                                                key: field,
                                                            })
                                                        ),
                                                });
                                            }}
                                        >
                                            {formatMessage({
                                                id: "common.download",
                                            })}
                                        </Typography.Link>
                                        {index !== 0 ? (
                                            <>
                                                <Typography.Link
                                                    onClick={() => {
                                                        edit(record);
                                                    }}
                                                >
                                                    {formatMessage({
                                                        id: "common.edit",
                                                    })}
                                                </Typography.Link>
                                                <Typography.Link
                                                    onClick={() => {
                                                        copy(record);
                                                    }}
                                                >
                                                    {formatMessage({
                                                        id: "common.copy",
                                                    })}
                                                </Typography.Link>
                                                <Typography.Link
                                                    onClick={() => {
                                                        del(record);
                                                    }}
                                                >
                                                    {formatMessage({
                                                        id: "common.delete",
                                                    })}
                                                </Typography.Link>
                                            </>
                                        ) : null}
                                    </Space>
                                </>
                            );
                        }}
                    />
                </Table>
            </Modal>
            <Custom
                bind={create}
                template={template}
                defaultFields={(props.report && props.report.fields) || []}
                customFields={(props.report && props.report.customFields) || []}
                refresh={get_templates}
            ></Custom>
        </>
    );
};
