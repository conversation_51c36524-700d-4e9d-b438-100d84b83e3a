import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const DepotsContext = React.createContext<{
        keyword: string;
        setKeyword: (data: string) => void;
    }
    |
    null>(null);

export const  DepotsProvider = ({children}: { children: ReactNode }) => {

    const [keyword, setKeyword] = useSafeState<string>("");


    return (
        < DepotsContext.Provider
            value={
                {
                    keyword,setKeyword,
                }
            }
        >
            {children}
        </ DepotsContext.Provider>
    )
};

export const useDepots = () => {
    const context = React.useContext( DepotsContext);
    if (!context) {
        throw new Error("useNotice must be used in NoticeContextProvider");
    }
    return context;
};

