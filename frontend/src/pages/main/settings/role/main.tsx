import {Button, Col, Form, Input, message, Radio, Row, Table,} from "antd";
import React, {useEffect} from "react";
import {useMount, useSafeState} from "ahooks";
import {useRole} from "./context";
import {useFetch} from "../../../../hooks/request";
import {list} from "../../../../api/roles";
import {Result} from "../../../../types/result";
import {useIntl} from "react-intl";
import {permissions} from "../../../../tools/permission";
import {useAuth} from "../../../../context/auth";
import {permissionsDisable} from "../../../../data/data";
import {fillTableCellEmptyPlaceholder} from "../../../../components/table";
import {all} from "../../../../api/menu";
import {MenuPermission} from "../../../../types/menu";
import {SearchOutlined} from "@ant-design/icons";
import {listRolesExport} from "../../../../api/customer";


export const Main = () => {
    const ctx = useRole()
    const auth = useAuth()
    const [form] = Form.useForm();
    const intl = useIntl();
    const {formatMessage} = intl;
    const [tableHeight, setTableHeight] = useSafeState<any>(300);
    const {runAsync: allRun, loading: allLoading} = useFetch(all, {manual: true});
    const [buttonLoading, setButtonLoading] = useSafeState<any>(false);

    const {runAsync: listRolesExportRun, loading: listRolesExportLoading} = useFetch(listRolesExport, {manual: true});

    const {runAsync, loading: listLoading} = useFetch(list, {manual: true});

    function escapeRegexSpecialChars(str: any) {
        // 正则表达式特殊字符
        var specialChars = /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g;

        // 使用replace方法和函数作为第二个参数来转义每个匹配项
        return str.replace(specialChars, '\\$&');
    }

    const getList = () => {
        let key = ctx.keyword;
        if(ctx.keyword !== null && ctx.keyword !== undefined){
            key = ctx.keyword.trim();
        }
        runAsync({"template": ctx.template, "keywords": key.trim() , all : "1"}).then(
            (result: any) => {
                const res = result as Result;
                ctx.setRoles((fillTableCellEmptyPlaceholder(res.data)));
            }
        )
    }
    useEffect(
        () => {
            setTableHeight(document.documentElement.clientHeight - 190);
        },
        []
    );
    useEffect(getList, [ctx.template, ctx.keyword,ctx.refresh])
    useMount(()=>{
        allRun({}).then((result:any)=>{
            ctx.setMenu(result.data)
        })
    })
    const onChange = (e: any) => {
        ctx.setTemplate(e.target.value)
    }
    const onChangeInput = (e: any) => {
        ctx.setKeyword(e.target.value);
    }


    const leadingOut = () => {
        setButtonLoading(true);
        listRolesExportRun({
            template: ctx.template,
            keywords: ctx.keyword,
        }).then(
            message.success(formatMessage({ id: "common.export.success" })),
        )
        setButtonLoading(false);
    }


    function getChildren(n: any,currentRole:any) {
        let arr: any[] = []
        let researchAttribute = 0
        if (ctx.template === 2) {
            researchAttribute = 1
        }
        n.forEach(
            (value: MenuPermission) => {
                let menuItem: any = {}
                let role = ctx.rolesPool.find((it: any) => it.name === currentRole.name)
                if (value.researchAttribute.includes(researchAttribute)) {
                    if(value.text === "menu.report" && (currentRole.name === "Sys-Admin" || currentRole.name === "Customer-Admin")){
                        // console.log("value===" + JSON.stringify(value) + ":" + currentRole.name + "=" + role?.type)
                    }else{
                        if (!role ||
                            value.system === 0 ||
                            (value.system === 2 && [1, 2, 3, 6].includes(role?.type)) ||
                            (value.system === 1 && [4, 5, 6].includes(role?.type))) {
                            if (value.text) {
                                menuItem["title"] = formatMessage({id: value.text})
                                menuItem["key"] = value.text
                                menuItem["selectable"] = !!value.permissions
                            }
                            if (value.children) {
                                menuItem["children"] = getChildren(value.children,currentRole)
                            }
                            arr.push(menuItem)
                        }
                    }
                }
            }
        );
        return arr
    }
    const roleConfig = (record: any) => {
        allRun({}).then((result:any)=>{
            ctx.setMenu(result.data)
        })
        if(record.name === "Customer-Admin" || record.name === "Sys-Admin"){
            if(record.permissions != null){
                let s = record.permissions.filter((element: any) => element !== "operation.projects.main.setting.permission.export")
                record.permissions = s;
            }
        }
        ctx.setRole(record);
        let tree = getChildren(ctx.menu,record);
        ctx.setTree([...tree]);
        ctx.setPermissionsSet(new Set(record.permissions));
        ctx.setOldData(record.permissions);
        let role = ctx.rolesPool.find((it:any) => it.name === record.name);
        if (!role){
            ctx.setDisableData([])
            ctx.setRoleConfigVisible(true)
            return
        }
        let disableData = permissionsDisable.find((it:any) => it.type === role.type)
        if (disableData){
            let arr = disableData.disables;
            if(record.scope === "site"){
                arr.push("operation.build.site.add");
            }
            ctx.setDisableData(arr)
        }else {
            let arr :any[] = [];
            if(record.scope === "site"){
                arr.push("operation.build.site.add")
            }
            ctx.setDisableData(arr)
        }
        ctx.setRoleConfigVisible(true)
    }

    const edit = (record: any) => {
        ctx.setRole(record)
        ctx.setEditVisible(true)
    }
    const add = () => {
        ctx.setEditVisible(true)
    }


    return (
        <>
            {
                permissions(auth.permissions, "operation.settings.roles.view") &&
                <>
                    <Form form={form} name="role">
                        <Row align="middle" justify="space-between" className="mar-all-15">
                            <Col style={{ marginBottom: '20px' }}>
                                <Input
                                    placeholder={formatMessage({id: "role.name.enter"})}
                                    onChange={onChangeInput}
                                    suffix={<SearchOutlined style={{ color: "#BFBFBF" }} />}
                                    style={{width: 200, marginRight:12}}
                                />
                            </Col>
                            <Col style={{ marginBottom: '20px' }}>
                                <span>
                                    {
                                        permissions(auth.permissions, "operation.settings.roles.export") &&
                                        <Button  onClick={() => leadingOut()} loading={buttonLoading}>{formatMessage({id: "operation.settings.roles.export"})}</Button>
                                    }
                                </span>
                                <span style={{ marginLeft: '12px' }}>
                                    {
                                        permissions(auth.permissions, "operation.settings.roles.add") &&
                                        <Button  type="primary" onClick={add} style={{ marginRight:10}}>{formatMessage({id: "common.addTo"})}</Button>
                                    }
                                </span>
                            </Col>
                        </Row>
                    </Form>
                    <Table
                        loading={listLoading || allLoading}
                        size="small"
                        dataSource={ctx.roles}
                        rowKey={(record: any) => record.name}
                        pagination={false}
                        scroll={{y: tableHeight}}
                    >
                        <Table.Column title={formatMessage({id: "common.serial"})} dataIndex="#" key="#" width={60}
                                      render={(text, record, index) => (index + 1)}/>
                        <Table.Column
                            width={400}

                            title={formatMessage({id: "roles.name"})}
                            key="name"
                            dataIndex="name"
                            ellipsis
                        />
                        <Table.Column
                            width={150}

                            title={formatMessage({id: "common.classification"})}
                            key="scope"
                            dataIndex="scope"
                            ellipsis
                        />
                        <Table.Column
                            width={150}

                            title={formatMessage({id: "common.template"})}
                            key="template"
                            dataIndex="template"
                            ellipsis
                            render={(template, record, index) => {
                                return (
                                    template === 1 ?
                                        formatMessage({id: "common.common"})
                                        :
                                        "DTP"
                                );
                            }}
                        />
                        <Table.Column
                            title={formatMessage({id: "common.description"})}
                            key="description"
                            dataIndex="description"
                            ellipsis
                        />
                        <Table.Column
                            title={formatMessage({id: "common.operation"})}
                            width={200}
                            key="operation"
                            dataIndex="operation"
                            ellipsis
                            render={
                                (value, record, index) => (
                                    <>
                                        {
                                            permissions(auth.permissions, "operation.settings.roles.edit") &&
                                            <Button style={{paddingLeft:0}} size="small" type="link" onClick={() => {
                                                edit(record)
                                            }}>{formatMessage({id: "common.edit"})}</Button>
                                        }
                                        {
                                            permissions(auth.permissions, "operation.settings.roles.config") &&
                                            <Button size="small" type="link" onClick={() => {
                                                roleConfig(record)
                                            }}>{formatMessage({id: "common.role.setting"})}</Button>
                                        }
                                    </>
                                )
                            }
                        />
                    </Table>
                </>
            }
        </>
    )
}