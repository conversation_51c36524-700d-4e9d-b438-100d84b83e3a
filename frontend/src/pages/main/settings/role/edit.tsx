import React, {useEffect} from "react"
import {Form, Input, message, Modal, Radio, Select, Row, Col, Button} from "antd";
import {useRole} from "./context";
import {useFetch} from "../../../../hooks/request";
import {add, listPool, update} from "../../../../api/roles";
import {useIntl} from "react-intl";
import {useGlobal} from "../../../../context/global";
import _ from "lodash";
import { useSafeState } from "ahooks";

export const Edit = () => {

    const g = useGlobal()
    const ctx = useRole()
    const intl = useIntl();
    const {formatMessage} = intl;

    const [send, setSend] = useSafeState<any>(true);
    const [oldData, setOldData] = useSafeState(null);

    const [form] = Form.useForm();
    const hide = () => {
        setSend(true);
        setOldData(null);
        form.resetFields();
        ctx.setRole(null);
        ctx.setEditVisible(false);
    }


    const formChange = () => {
        if (ctx.role) {
            const a = _.cloneDeep(oldData);
            // console.log("1===" + JSON.stringify(a)); 
            let b = _.cloneDeep(form.getFieldsValue());
            // console.log("2===" + JSON.stringify(b)); 
            if (!compareObjects(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }

        }
    };

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1: any, obj2: any) {
        for (let key in obj1) {
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                    if (!arraysAreEqual(obj1[key], obj2[key])) {
                        return false;
                    }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1: any, arr2: any) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }

    const scope = [
        {label:"study", value:"study"},
        {label:"site", value:"site"},
        {label:"depot", value:"depot"},
    ]

    const {runAsync:addRun, loading: addLoading} = useFetch(add, {manual:true});
    const {runAsync:updateRun, loading: updateLoading} = useFetch(update, {manual:true});

    useFetch(listPool, {
        onSuccess: (result:any, params) => {
            ctx.setRolesPool(result.data)
        }
    });

    const save = () => {
        form.validateFields().then(
            (value) => {
                if (ctx.role){
                    // update
                    value.template = ctx.template
                    value.permissions = ctx.role.permissions
                    updateRun({id:ctx.role.id}, {...value}).then(
                        (result:any) => {
                            message.success(result.msg).then(() => {});
                            hide()
                            ctx.setRefresh(ctx.refresh+1)
                        }
                    )
                }else{
                    value.template = ctx.template
                    value.permissions = []
                    addRun({...value}).then(
                        (result:any) => {
                            message.success(result.msg).then(() => {});
                            hide()
                            ctx.setRefresh(ctx.refresh+1)
                        }
                    )
                }
            }
        )
    }
    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 4: 6 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 20: 18 },
        },
    };

    const setInit = () => {
        if(ctx.editVisible){
            form.setFieldsValue({...ctx.role});
            setOldData({...ctx.role});
        }
    }

    useEffect(setInit, [ctx.editVisible])

    return (
        <React.Fragment>
            <Modal
                destroyOnClose={true}
                width={500}
                title={ctx.role? formatMessage({id: "common.edit"}):formatMessage({id: "common.addTo"})}
                visible={ctx.editVisible}
                onOk={save}
                okText={formatMessage({ id: 'common.ok' })}
                onCancel={hide}
                centered
                maskClosable={false}
                okButtonProps={{ disabled: ctx.role ? send : false }}
                confirmLoading = {updateLoading || addLoading}
            >
                <Form
                    form={form}
                    onValuesChange={formChange}
                    {...formItemLayout}

                >
                    <Form.Item label={formatMessage({id: "roles.name"})} name="name" className="mar-ver-5"
                               rules={[{required: true}]}>
                        <Select className="full-width" disabled={ctx.role}>
                            {
                                ctx.rolesPool.map((value: any) =>
                                <Select.Option value={value.name} key={value.key}>{value.name}</Select.Option>
                                )
                            }
                        </Select>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: "common.classification"})} name="scope" className="mar-ver-5"
                               rules={[{required: true}]}>
                        <Select className="full-width" options={scope}>
                        </Select>
                    </Form.Item>
                    <Form.Item  label={formatMessage({id: "common.status"})} name="status" className="mar-ver-5" initialValue={1}>
                        <Radio.Group disabled={true}>
                            <Radio value={1}>{formatMessage({id: "common.effective"})}</Radio>
                            <Radio value={2}>{formatMessage({id: "common.invalid"})}</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: "common.description"})} name="description" className="mar-ver-5">
                        <Input.TextArea showCount allowClear autoSize={{ minRows: 3, maxRows: 10 }} maxLength={500}/>
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    )
}