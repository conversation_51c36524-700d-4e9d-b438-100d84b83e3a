import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, Table, message, Modal, Row, Col, Button, Result} from "antd";
import {useFetch} from "../../../../hooks/request";
import {addBatchUser} from "../../../../api/user";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../../context/global";
import styled from "@emotion/styled";
import { UserAddRoles } from "./add_roles";

export const UserAddTip = (props :any) => {

    const intl = useIntl();
    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState(false);
    const [data, setData] = useSafeState([]);

    const users_roles: any = React.useRef();

    const [form] = Form.useForm();

    const show = (data: any) => {
        setVisible(true);
        setData(data);
    };

    const hide = () => {
        setVisible(false);
        form.resetFields();
        props.refresh();
    };


    const save = () => {
        users_roles.current.show(data);
        hide();

    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Modal
                width={500}
                title={<FormattedMessage id={"common.tips"} />}
                visible={visible}
                onCancel={hide}
                // cancelText={formatMessage({id: 'common.back.revise'})}
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                // okText={emailList.length !== data.length?formatMessage({id: 'common.ignore.continue'}):""}
                // okButtonProps={{loading: addBatchUserLoading}}
                // onOk={save}
                footer={null}
            >
                <CustomResult
                    style={{padding:0}}
                    status="success"
                    title={formatMessage({ id: 'common.user.add.success'})}
                    subTitle={formatMessage({ id: 'common.user.add.configure.permissions.role.tip'})}
                    extra={[
                        <Row style={{ paddingLeft: 24 }}>
                            <Col span={24} style={{ textAlign: 'center' }}>
                                <Button onClick={save}>{formatMessage({ id: "common.user.add.configure.permissions.role" })}</Button>
                            </Col>
                        </Row>
                    ]}
                />
            </Modal>
            <UserAddRoles bind={users_roles} refresh={props.refresh}/>
        </React.Fragment>
    )
};




const CustomResult = styled(Result)`
  .ant-result-icon {
    margin-bottom: 12px;
  }
  .ant-result-icon > .anticon {
    font-size: 44px;
  }
    
`