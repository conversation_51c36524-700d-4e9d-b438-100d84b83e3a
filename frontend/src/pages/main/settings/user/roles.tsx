import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {<PERSON><PERSON>, Col, Drawer, message, Row, Switch, Table} from "antd";
import {useFetch} from "../../../../hooks/request";
import {list} from "../../../../api/roles";
import {setUserRoles as setUserRoleList} from "../../../../api/user";
import {useSafeState} from "ahooks";

export const UserRoles = (props: any) => {

    const intl = useIntl();
    const {formatMessage} = intl;

    const [visible, setVisible] = useSafeState<any>(false);
    const [user, setUser] = useSafeState<any>(null);
    const [userId, setUserId] = useSafeState<any>(null);
    const [roles, setRoles] = useSafeState<any>([]);  // 系统中通用角色
    const [dtpRoles, setDtpRoles] = useSafeState<any>([]);  // 系统中dtp角色
    const [userRoles, setUserRoles] = useSafeState<any>([]); // 用于已有的角色
    const [selectedUserRoles, setSelectedUserRoles] = useSafeState<any>([]); // 新增角色
    const {runAsync:listRun, loading: listLoading} = useFetch(list, {manual:true});
    const {runAsync:listDtpRun, loading: listDtpLoading} = useFetch(list, {manual:true});
    const {runAsync:setUserRolesRun, loading: setUserRolesLoading} = useFetch(setUserRoleList, {manual:true});
    // const [isExistDtpRole, setIsExistDtpRole] = useSafeState<any>([]); // 用户是否存在dtp角色

    const show = (user:any) => {
        listRun({template:1}).then(
            (result:any) => {
                if (result.data){
                    setRoles(result.data.filter((it:any)=>it.name !== "Customer-Admin" && it.name !== "Project-Admin" && it.name !== "Sys-Admin"));
                }
            }
        )
        // listDtpRun({template:2}).then(
        //     (result:any) => {
        //         if (result.data){
        //             setDtpRoles(result.data.filter((it:any)=>it.name !== "Customer-Admin" && it.name !== "Project-Admin" && it.name !== "Sys-Admin"));
        //             let idArr = (user.roles ? user.roles : []).filter((a: any) => (result.data.filter((it:any)=>it.name !== "Customer-Admin" && it.name !== "Project-Admin" && it.name !== "Sys-Admin")).some((b: any) => b.id === a));
        //             if(idArr !== null && idArr !== undefined && idArr.length > 0){
        //                 setIsExistDtpRole(true);
        //             }
        //         }
        //     }
        // )
        setUser(user);
        setUserId(user.id);
        setUserRoles(user.roles ? user.roles : []);

        setVisible(true);
    };

    const hide = () => {
        setVisible(false);
        setUserId(null);
        setUser(null);
        setSelectedUserRoles([]);
        setUserRoles([]);
        setRoles([]);
        // setIsExistDtpRole(false);
    };

    const save = () => {
        setUserRolesRun({id:userId},{ id: userId, roles: selectedUserRoles }).then(
            (result:any) => {
                message.success(result.msg).then(() => {});
                hide();
                props.refresh();
            }
        )
    };

    const setRole = React.useCallback(
        (v:any, id:any) => {
            if (v) {
                setSelectedUserRoles([...selectedUserRoles, id]);
            } else {
                const _roles = [...selectedUserRoles];
                const index = _roles.indexOf(id);
                if (index > -1) {
                    _roles.splice(index, 1);
                    setSelectedUserRoles(_roles);
                }
            }
        },
        [selectedUserRoles]
    );

    React.useImperativeHandle(props.bind, () => ({ show }));

    return (
        <React.Fragment>
            <Drawer
                className="drawer-width-percent"
                title={<div>
                        {formatMessage({ id: "common.user.add.configure.permissions.role" })}
                        <span style={{fontWeight:400}}>{(user ? ` - ${user.info.email}`: '')}</span>
                    </div>}
                visible={visible}
                onClose={hide}
                
                maskClosable={false}
                destroyOnClose={true}
                footer={
                    <Row justify="end">
                        <Col>
                            {
                                !listLoading && !listDtpLoading && roles.length > 0
                                    ?
                                    <Button disabled={!selectedUserRoles || selectedUserRoles.length === 0} onClick={save} type="primary" loading={setUserRolesLoading}>
                                        <span><FormattedMessage id="common.save" /></span>
                                    </Button>
                                    :
                                    null
                            }
                        </Col>
                    </Row>
                }
            >
            {/* <div style={{display: "flex", justifyItems: "center", alignItems: "center", marginTop: "6px", marginBottom: "16px"}}><span className="blue-bar" style={{fontWeight:700}}></span><FormattedMessage id={'common.common'}/></div> */}
            <Table
                style={{marginTop:16}}
                dataSource={roles}
                pagination={false}
                rowKey={(record: any) => (record.id)}
            >
                <Table.Column title={<FormattedMessage id="common.role"/>}
                                dataIndex={'name'} key="name" ellipsis/>
                <Table.Column
                    title={<FormattedMessage id="common.permission"/>}
                    ellipsis
                    width={80}
                    render={
                        (value, record: any) => (
                            <div>
                                <Switch
                                    size='small'
                                    disabled={userRoles && userRoles.findIndex((it:any) => it === record.id) > -1}
                                    onChange={v => setRole(v, record.id)}
                                    checked={userRoles.findIndex((it:any) => it === record.id) > -1 || selectedUserRoles.findIndex((it:any) => it === record.id) > -1}
                                    checkedChildren
                                    unCheckedChildren
                                    // checkedChildren={<CheckOutlined />}
                                    // unCheckedChildren={<CloseOutlined />} 
                                />
                            </div>
                        )
                    }
                />
            </Table>
            {/* {
                isExistDtpRole?
                <>
                    <div style={{display: "flex", justifyItems: "center", alignItems: "center", marginTop: "16px", marginBottom: "16px"}}><span className="blue-bar" style={{fontWeight:700}}></span>{"DTP"}</div>
                    <Table
                        style={{marginTop:16}}
                        dataSource={dtpRoles}
                        pagination={false}
                        rowKey={(record: any) => (record.id)}
                    >
                        <Table.Column title={<FormattedMessage id="common.role"/>}
                                        dataIndex={'name'} key="name" ellipsis/>
                        <Table.Column
                            title={<FormattedMessage id="common.permission"/>}
                            ellipsis
                            width={80}
                            render={
                                (value, record: any) => (
                                    <div>
                                        <Switch
                                            size='small'
                                            disabled={userRoles && userRoles.findIndex((it:any) => it === record.id) > -1}
                                            onChange={v => setRole(v, record.id)}
                                            checked={userRoles.findIndex((it:any) => it === record.id) > -1 || selectedUserRoles.findIndex((it:any) => it === record.id) > -1}
                                            checkedChildren
                                            unCheckedChildren
                                        />
                                    </div>
                                )
                            }
                        />
                    </Table>
                </>:null
            } */}
            </Drawer>
        </React.Fragment>
    );
};
