import React from "react";
import {FormattedMessage, useIntl} from "react-intl";
import {Form, Input, message, Modal, Select, Radio, Space, Col} from "antd";
import {useFetch} from "../../../../hooks/request";
import {addUser, updateUser} from "../../../../api/user";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../../context/global";
import {
    inviteAgainCustomerUser,
} from "../../../../api/user";
import { resendInviteEmail } from "../../../../api/projects";

export const InviteAgain = (props :any) => {

    const intl = useIntl();
    const {formatMessage} = intl;
    const g = useGlobal()

    const [visible, setVisible] = useSafeState(false);
    const [customerId, setCustomerId] = useSafeState(null);
    const [userId, setUserId] = useSafeState(null);
    const [key, setKey] = useSafeState(null);
    const [id, setId] = useSafeState(null);

    const { runAsync: inviteAgainRun, loading: inviteAgainLoading } = useFetch(
        inviteAgainCustomerUser,
        { manual: true }
    );
    const {
        runAsync: resendInviteEmailRun,
        loading: resendInviteEmailLoading,
    } = useFetch(resendInviteEmail, { manual: true });

    const [form] = Form.useForm();

    const show = (customerId:any, userId: any, key: any) => {
        setVisible(true);
        setCustomerId(customerId);
        setUserId(userId);
        setKey(key);
    };

    const hide = () => {
        setCustomerId(null);
        setUserId(null);
        setKey(null);
        setVisible(false);
        form.resetFields();
        setId(null);
    };


    const save = () => {
        form.validateFields()
            .then(values => {
                const emailLanguage = form.getFieldValue("emailLanguage");
                if (key) {
                    if (key === 1) {
                        inviteAgainRun({
                            customerId: customerId,
                            userId: userId,
                            emailLanguage: emailLanguage,
                        }).then((result: any) => {
                            // message.success(result.msg).then(() => {});
                            message.success(formatMessage({id: 'common.success'})).then(() => {});
                            props.refresh();
                            hide();
                        });
                    } else if (key === 2) {
                        resendInviteEmailRun({
                            customerId: customerId,
                            userId: userId,
                            emailLanguage: emailLanguage,
                        }).then((result: any) => {
                            message.success(formatMessage({id: 'common.success'})).then(() => {});
                            // message.success(result.msg).then(() => {});
                            props.refresh();
                            hide();
                        });
                    }
                }
            })
            .catch(error => {
            })

    };

    React.useImperativeHandle(props.bind, () => ({ show }));

    const language = [
        {label:formatMessage({id:"common.email.language.zh"}), value:"zh"},
        {label:formatMessage({id:"common.email.language.en"}), value:"en"},
    ]

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 5: 8 },
        },

    }

    return (
        <React.Fragment>
            <Modal
                width={500}
                title={<FormattedMessage id={"user.settings.invite-again"} />}
                visible={visible}
                onCancel={hide}
                
                maskClosable={false}
                centered
                destroyOnClose
                className='custom-small-modal'
                okText={formatMessage({id: 'common.ok'})}
                okButtonProps={{loading: inviteAgainLoading || resendInviteEmailLoading}}
                onOk={save}
            >
                <Form form={form} {...formItemLayout}>
                    <Form.Item
                        className="mar-ver-5" name="emailLanguage"
                        label={formatMessage({ id: 'common.email.language' })}
                        rules={[{ required: true, message: formatMessage({ id: 'placeholder.select.common.email.language' })}]}
                        style={{marginBottom:24}}
                        // initialValue={"zh"}
                    >
                        {/* <Select 
                            className="full-width" 
                            options={language}
                        >
                        </Select> */}
                        <Radio.Group 
                            className="full-width"
                            disabled={props.disabled}
                        >
                            <Space >
                                <Col
                                    style={{ marginRight: 24 }}
                                >
                                    <Radio value={"zh"}>
                                        {
                                            intl.formatMessage({id: "common.email.language.zh"})
                                        }
                                    </Radio>
                                </Col>
                                <Col
                                    style={{ marginRight: 24 }}
                                >
                                    <Radio value={"en"}>
                                        {
                                            intl.formatMessage({id: "common.email.language.en"})
                                        }
                                    </Radio>
                                </Col>
                            </Space>

                        </Radio.Group>
                    </Form.Item>
                </Form>
            </Modal>
        </React.Fragment>
    )
};