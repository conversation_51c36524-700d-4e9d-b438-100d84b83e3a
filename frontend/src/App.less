@import "~antd/dist/antd.less";
// @import '~antd/dist/antd.compact.less';


@primary-color: #165DFF;
@link-color: #165DFF; // 链接色
@success-color: #41cc82; // 成功色
@warning-color: #ffae00; // 警告色
@error-color: #fe5b5a; // 错误色
@font-size-base: 14px; // 主字号
@heading-color: rgba(0, 0, 0, 0.85); // 标题色
@text-color: #1d2129; // 主文本色
@text-color-secondary: #4e5969; // 次文本色
@disabled-color: #adb2ba; // 失效色
@border-radius-base: 2px; // 组件/浮层圆角
@border-color-base: #d9d9d9; // 边框色
@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05); // 浮层阴影

.link-btn {
    cursor: pointer;
    color: @link-color,
}

// 标题字体
.title-text {
    font-weight: 600;
    font-size: 14px;
    color: #000000;
}

.bg-white {
    background: white !important;
}

.full {
    width: 100% !important;
    height: 100% !important;
}

.full-width {
    width: 100% !important;
}

.custom-tip-full-input {
    width: 260px !important;
}

.custom-tip-full-input-en {
    width: 200px !important;
}

.full-width-disabled {
    width: 100% !important;
    background-color: #f5f5f5 !important;
}

.full-height {
    height: 100% !important;
}

.ver-center {
    display: flex;
    align-items: center;
}

.hor-center {
    display: flex;
    justify-content: center;
}

.all-center {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    vertical-align: middle;
}

.side {
    background: white !important;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100vh;
    position: fixed;
    left: 0;
}

.menu {
    padding-bottom: 60px !important;
}

.right {
    height: 100vh;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
}

.header {
    position: relative;
    top: 0;
    vertical-align: middle;
    z-index: 1;
    width: 100%;
    padding: 0 10px !important;
    height: 56px !important;
    line-height: 56px !important;
    background: white !important;
    border-bottom: #f0f0f0 solid 1px;
}

.toolbar {
    font-size: 16px;
    cursor: pointer !important;
}

.toolbar:hover {
    background-color: #f0f0f0;
}

.footer {
    padding: 0 10px !important;
    height: 50px !important;
    line-height: 50px !important;
    position: relative;
    bottom: 0;
    z-index: 1;
    background: white !important;
    border-top: #f0f0f0 solid 1px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.text-primary {
    color: #009966;
}

.text-white {
    color: #ffffff !important;
}

.brand {
    position: relative;
    top: 0;
    width: 100%;
    height: 50px;
    line-height: 50px;
    font-size: 24px;
    text-align: center;
    vertical-align: middle;
    margin: 0;
    border-bottom: #f0f0f0 solid 1px;
    // border-right: #f0f0f0 solid 1px;
    overflow: hidden;
}

.brand-logo {
    width: 50px;
    float: left;
    text-align: right;
}

.ant-layout-sider-collapsed .brand-logo {
    text-align: center;
}

.brand-title {
    font-weight: 400;
    font-size: 20px;
    text-align: left;
    // color: #333333;
}

.mar-top-0 {
    margin-top: 0 !important;
}

.mar-btm-0 {
    margin-bottom: 0 !important;
}

.mar-lft-0 {
    margin-left: 0 !important;
}

.mar-rgt-0 {
    margin-right: 0 !important;
}

.mar-top-5 {
    margin-top: 5px !important;
}

.mar-btm-5 {
    margin-bottom: 5px !important;
}

.mar-lft-5 {
    margin-left: 5px !important;
}

.mar-rgt-5 {
    margin-right: 5px !important;
}

.mar-top-8 {
    margin-top: 8px !important;
}

.mar-top-10 {
    margin-top: 10px !important;
}

.mar-btm-10 {
    margin-bottom: 10px !important;
}

.mar-lft-10 {
    margin-left: 10px !important;
}

.mar-rgt-10 {
    margin-right: 10px !important;
}

.mar-top-15 {
    margin-top: 15px !important;
}

.mar-btm-15 {
    margin-bottom: 15px !important;
}

.mar-top-16 {
    margin-top: 16px !important;
}

.mar-rgt-12 {
    margin-right: 12px !important;
}

.mar-ver-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.mar-hor-10 {
    margin-left: 10px !important;
    margin-right: 10px !important;
}

.mar-ver-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

.mar-ver-5 {
    margin-top: 5px !important;
    margin-bottom: 5px !important;
}

.mar-ver-8 {
    margin-top: 8px !important;
    margin-bottom: 8px !important;
}

.mar-ver-10 {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
}

.mar-ver-15 {
    margin-top: 15px !important;
    margin-bottom: 15px !important;
}


.mar-all-10 {
    margin: 10px !important;
}

.mar-all-20 {
    margin: 20px !important;
}

.pad-top-10 {
    padding-top: 10px !important;
}

.pad-btm-10 {
    padding-bottom: 10px !important;
}

.pad-lft-10 {
    padding-left: 10px !important;
}

.pad-rgt-10 {
    padding-right: 10px !important;
}

.pad-hor-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.pad-hor-10 {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.pad-hor-15 {
    padding-left: 15px !important;
    padding-right: 15px !important;
}

.pad-top-5 {
    padding-top: 5px !important;
}

.pad-ver-5 {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
}

.pad-ver-10 {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
}

.pad-all-10 {
    padding: 10px !important;
}


.text-right {
    text-align: right
}

.text-center {
    text-align: center
}

.text-middle {
    vertical-align: middle
}

.ver-center {
    display: flex;
    align-items: center;
}

.hor-center {
    display: flex;
    justify-content: center;
}

.all-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.mouse {
    cursor: pointer !important;
}

.history_title {
    font-size: 24px;
}

span.ant-select-selection-item,
span.ant-select-selection-placeholder {
    width: 100%;
}

.ant-table {
    margin: 0 !important;
    //overflow-y: scroll;
    //max-height: calc(100vh - 180px);
}

// 空状态下，不显示边框
.ant-table-empty .ant-table-tbody>tr.ant-table-placeholder>td {
    border-bottom: none;
}



.ant-table-thead {
    height: 48px;
}

// 表头
.ant-table-thead>tr>th {
    color: #677283;
    font-weight: 500 !important;
    font-size: 14px !important;
    background: #F2F3F5;
    vertical-align: middle;
    font-family: "Helvetica Neue", "Helvetica", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", "Arial", "sans-serif";

    line-height: 19.6px;
    letter-spacing: 0px;
    text-align: left;
}

// 表头-edc
.ant-table-thead-edc>tr>th {
    color: #677283;
    font-weight: 500 !important;
    font-size: 14px !important;
    background: #F2F3F5;
    vertical-align: middle;
    font-family: "Helvetica Neue", "Helvetica", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", "Arial", "sans-serif";

    line-height: 19.6px;
    letter-spacing: 0px;
    text-align: left;
    border-right: 1px solid #E3E4E6 !important;
}

.ant-custom-thead .ant-table-thead>tr>th {
    color: #677283;
    font-weight: 500 !important;
    font-size: 14px !important;
    background: #63666F0F;
    vertical-align: middle;
    font-family: "Helvetica Neue", "Helvetica", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", "Arial", "sans-serif";
    line-height: 19.6px;
    letter-spacing: 0px;
    text-align: left;

}

.ant-custom-table .ant-table-thead>tr>th {
    color: #677283;
    font-weight: 500 !important;
    font-size: 14px !important;
    background: #FFFFFF;
    vertical-align: middle;
    font-family: "Helvetica Neue", "Helvetica", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", "Arial", "sans-serif";
    line-height: 19.6px;
    letter-spacing: 0px;
    text-align: left;

}

thead.ant-table-thead>tr>th,
tbody.ant-table-tbody>tr>td {
    line-height: 32px !important;
    padding: 6px 8px 6px 16px !important;

    //表格间距
    &.table-column-padding-left-16-1 {
        padding-left: 16px !important;
        padding-right: 0px !important;
    }

    &.table-column-padding-left-16-2 {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }

    &.table-column-padding-left-32 {
        padding-left: 32px !important;
        padding-right: 0px !important;
    }
}


// 隐藏分割符
.ant-table-thead>tr>th::before {
    display: none;
}

// 垂直居中
.ant-table-tbody>.ant-table-row>.ant-table-cell {
    vertical-align: middle;
}

.custom-table .ant-table-tbody>.ant-table-row>.ant-table-cell {
    vertical-align: baseline;
}


.ant-drawer-body,
.ant-card-body,
.ant-list-item,
.ant-collapse-content-box {
    padding: 10px;
}

.ant-drawer-header-title {
    flex-direction: row-reverse;
}

.ant-menu-inline-collapsed {
    width: 48px !important;
}

.ant-menu-horizontal {
    margin-top: -2px !important;
}

.ant-select-selection-overflow-item {
    margin-right: 4px !important;
}

.ant-tabs-nav {
    margin-bottom: 10px;
}

@media (min-width: 992px) {

    .header-nav-icon,
    .header-link {
        display: none;
    }
}

@media (min-width: 1200px) {
    .drawer-width-percent>.ant-drawer-content-wrapper {
        width: 55% !important;
    }
}

@media (max-width: 1200px) {
    .drawer-width-percent>.ant-drawer-content-wrapper {
        width: 65% !important;
    }
}

@media (max-width: 992px) {
    .drawer-width-percent>.ant-drawer-content-wrapper {
        width: 65% !important;
    }
}

@media (max-width: 768px) {
    .header-link {
        display: none;
    }

    .drawer-width-percent>.ant-drawer-content-wrapper {
        width: 80% !important;
    }
}

@media (max-width: 576px) {
    .header-brand {
        display: none;
    }

    .drawer-width-percent>.ant-drawer-content-wrapper {
        width: 100% !important;
    }
}

.font-size-24 {
    font-size: 24px;
}

.header_check {
    position: relative;
    top: 0;
    vertical-align: middle;
    z-index: 1;
    width: 100%;
    padding: 0 10px !important;
    height: 56px !important;
    line-height: 56px !important;
    background: #3B5FF1 !important;
    border-bottom: #f0f0f0 solid 1px;
}

.ant-divider {
    line-height: 0.9;
    font-weight: bold;
}

.ant-divider-vertical {
    border-left: 2px solid #165dff;
    height: 14px;
}

.ant-modal-header {
    height: 56px;
    background-color: #F9FAFB;
}

.ant-modal-title {
    font-weight: 600;
    color: #1D2129;
}

// modal相关
.custom-tips-modal-with-content {
    width: 420px !important;
    height: 180px !important;
}

.custom-tips-modal-with-content .ant-modal-confirm-body {
    // 加上其他的一些高度，使modal高度刚好是180px
    min-height: 68px;
}

// modal相关
.custom-tips-modal-without-content {
    width: 420px !important;
    height: 160px !important;
}

.custom-tips-modal-without-content .ant-modal-confirm-body {
    // 加上其他的一些高度，使modal高度刚好是160px
    min-height: 48px;
}

.ant-modal-confirm-body .ant-modal-confirm-content {
    margin-top: 16px;
}

.custom-little-modal,
.custom-little-modal .ant-modal-content {
    width: 480px !important;
}

.custom-batch-add-modal-batch,
.custom-batch-add-modal-batch .ant-modal-content {
    width: 1200px !important;
}


.custom-small-modal,
.custom-small-modal .ant-modal-content {
    width: 640px !important;
}

.custom-small-multiple-modal,
.custom-small-multiple-modal .ant-modal-content {
    width: 1000px !important;
}

.custom-small-modal-new,
.custom-small-modal .ant-modal-content {
    width: 640px !important;
}

.custom-medium-modal-new-batch,
.custom-medium-new-batch-modal .ant-modal-content {
    width: 650px !important;
}

.custom-medium-modal-new-setting,
.custom-medium-setting-modal .ant-modal-content {
    width: 1000px !important;
}


.custom-medium-modal,
.custom-medium-modal .ant-modal-content {
    width: 800px !important;
}

.custom-medium-modal-batch-sites-depots,
.custom-medium-modal-batch-sites-depots .ant-modal-content {
    width: 640px !important;
}

.custom-medium-modal-site-add,
.custom-medium-modal .ant-modal-content {
    width: 880px !important;
}

.custom-medium-modal-multiLanguage,
.custom-medium-multiLanguage-modal .ant-modal-content {
    width: 1440px !important;
}

.custom-medium-modal-batch,
.custom-medium-batch-modal .ant-modal-content {
    width: 1200px !important;
}

.custom-visit-setting-modal .ant-modal-content {
    width: 480px !important;
}

.custom-width-modal .ant-modal-content {
    width: 640px !important;
}

.custom-real-dispensing-modal,
.custom-real-dispensing-modal .ant-modal-content {
    width: 990px !important;
}

.custom-large-modal,
.custom-large-modal .ant-modal-content {
    width: 1000px !important;
}

.custom-blarge-modal,
.custom-blarge-modal .ant-modal-content {
    width: 1200px !important;
}

.ant-modal-footer {
    padding: 16px !important;
}

.ant-form-item-label label {
    font-family: "Helvetica Neue", "Helvetica", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", "Arial", "sans-serif";

    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0px;
    text-align: left;
    color: #1D2129;
}

.ant-form-item {
    margin-bottom: 16px;
}

// .ant-form-item:last-of-type {
//     margin-bottom: 0px;
// }

// modal 相关的样式
@modal-footer-height: 65px;
@modal-header-height: 56px;
@modal-body-height: calc(100vh - 140px - @modal-footer-height - @modal-header-height);

.ant-modal-body {
    max-height: @modal-body-height;
    overflow-y: auto;
    padding: 16px 24px;
}

.ant-pagination-item a {
    color: #666666;
}

.ant-pagination-item-active {
    background: #165DFF;
}

.ant-pagination-item-active>a,
.ant-pagination-item-active>a:hover {
    color: white;
}

.ant-checkbox-indeterminate .ant-checkbox-inner::after {
    height: 2.6px;
    width: 12px;
}

.ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {
    height: 2.6px;
    width: 12px;
}

// radio 单选框相关的样式
// 选中状态下的单选框禁用时

.ant-radio-disabled.ant-radio-checked>.ant-radio-inner::after {
    background-color: @primary-color !important;
}

.ant-radio-disabled.ant-radio-checked .ant-radio-inner {
    border-color: @primary-color !important;
    opacity: 0.6;
}

.ant-radio-disabled+span {
    color: #1D2129;
}

// 下拉菜单按钮
.dropdown-button {
    color: @primary-color;
    cursor: pointer;
}

.dropdown-button:hover {
    background-color: rgba(22, 93, 255, 0.1);
}

// input
.ant-input-show-count-suffix {
    color: #C8C9CC;
}

.ant-input-textarea-show-count::after {
    bottom: 4px;
    right: 8px;
    margin-bottom: 0 !important;
    position: absolute;
    color: #C8C9CC;
    z-index: 1;
}

//.ant-spin-nested-loading>div>.ant-spin .ant-spin-dot {
//    position: fixed;
//}
.ant-steps-dot .ant-steps-item-tail::after {
    height: 0.5px;
}

.ant-steps-item-process>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-title {
    color: #1D2129;
}

.ant-steps-item-wait>.ant-steps-item-container>.ant-steps-item-content>.ant-steps-item-title {
    color: #ADB2BA;
}

.ant-steps-step-description {
    color: #4E5969;
    ;
}

tr.ant-table-measure-row {
    visibility: collapse;
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: #E9F1FC;
    color: @primary-color;
}

.custom-popover {
    .ant-popover-title {
        min-width: 294px;
        min-height: 40px;
        margin: 0;
        padding: 8px 8px 8px;
        // color: rgba(0, 0, 0, 0.85);
        color: #1D2129;
        // border-bottom: 1px solid #f0f0f0;
        font-family: "Helvetica Neue", "Helvetica", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", "Arial", "sans-serif";

        font-size: 14px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0px;
        text-align: left;

    }

    .ant-popover-inner-content {
        padding: 8px 8px;
        color: #4E5969;
    }
}

.ant-table-tbody>tr.ant-table-row-selected>td {
    background: white;
}

.ant-tree-treenode.ant-tree-treenode-switcher-open.ant-tree-treenode-selected {
    background-color: #E9F1FC;
    padding-bottom: 0;
}

.ant-tree-switcher .ant-tree-switcher-icon {
    font-size: 14px;
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    // background-color: #fff;
}

::-webkit-scrollbar-thumb {
    display: block;
    min-height: 12px;
    min-width: 8px;
    border-radius: 6px;
    background-color: rgb(217, 217, 217);

    &:hover {
        background-color: rgb(159, 159, 159);
    }
}

@media print {

    html,
    body {
        height: initial !important;
        overflow: initial !important;
    }
}

.reason-status-modal-body {
    height: 340px !important;
    width: 480px !important;
}

.reason-status .ant-modal-confirm-body>.anticon+.ant-modal-confirm-title+.ant-modal-confirm-content {
    margin-left: 0;
    margin-top: 8px;
}

.tree-transfer .ant-transfer-list:first-child {
    flex: none;
    width: 50%;
}

.right-tree .ant-tree-switcher-noop {
    display: none;
}

.right-tree .ant-tree .ant-tree-node-content-wrapper {
    width: 325px;
}

.field-picker .ant-popover-inner-content {
    padding: 8px !important;
}

.right-tree .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: white;
    color: black;
}

.right-tree .ant-tree .ant-tree-node-content-wrapper:hover {
    background-color: white;
}

.right-tree .ant-tree .ant-tree-treenode {
    cursor: row-resize;
}

.right-tree .ant-tree .ant-tree-node-content-wrapper {
    cursor: row-resize;
}

.field-picker .ant-modal-body {
    padding-bottom: 0;
}

.filename-search .ant-btn {
    padding: 4px 10px;
}

// 添加个类名 隐藏掉激活时候下划线、激活时候修改颜色
// .message-tab-ink-bar{
//     .ant-tabs-ink-bar {
//         visibility: hidden;
//     }
//     .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
//         color: #1D2129;
//         font-family: 'PingFang SC';
//         font-size: 12px;
//         font-weight: 400;
//     }
// }

.message-tab-ink-bar .ant-tabs-tab-btn {
    color: #677283;
    font-family: "Helvetica Neue", "Helvetica", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", "Arial", "sans-serif";

    font-size: 12px;
    font-weight: 400;
}

.message-tab-ink-bar .ant-tabs-tab-active .ant-tabs-tab-btn,
.message-tab-ink-bar .ant-tabs-tab-btn:focus,
.message-tab-ink-bar .ant-tabs-tab-btn:active {
    color: #1D2129;
    font-family: "Helvetica Neue", "Helvetica", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", "Arial", "sans-serif";

    font-size: 12px;
    font-weight: 400;
}

.message-tab-ink-bar .ant-tabs-ink-bar {
    display: none;
}

//添加个类名 去掉tabs下的细线
.message-tab-top-ink-bar {

    .ant-tabs-top>.ant-tabs-nav::before,
    .ant-tabs-bottom>.ant-tabs-nav::before,
    .ant-tabs-top>div>.ant-tabs-nav::before,
    .ant-tabs-bottom>div>.ant-tabs-nav::before {
        position: absolute;
        right: 0;
        left: 0;
        border-bottom: 0;
        content: '';
    }
}

.ant-table-cell-scrollbar:not([rowspan]) {
    box-shadow: unset;
}

.ant-input:focus,
.ant-input-focused {
    box-shadow: unset;
}

.download-hover {
    width: 14px;
    height: 14px;
    cursor: pointer;

    &:hover {
        use {
            fill: #165DFF;
        }
    }

    use {
        fill: #ADB2BA;
    }
}

.hover-color {
    &:hover {
        color: #165DFF;
    }
}


.custom-modal-center {
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中 */
    height: calc(100% - 200px);
    /* 设置高度 */
    // width: 100%; /* 设置宽度 */
    width: 800px !important;
    position: relative;
    /* 设置相对定位 */
}

.custom-modal-center .ant-modal-content {
    width: 800px !important;
}


.my-ok-button {
    background-color: #165DFF;
    color: white;
    letter-spacing: -1px;
}


.my-cancel-button {
    background-color: #FFFFFF;
    color: #4B4B4B;
    letter-spacing: -1px;
}

.ant-table-cell-scrollbar {
    box-shadow: 0 0 0 !important;
}

.ant-table .ant-table-thead>tr>th.ant-table-cell-scrollbar {
    border-bottom: 0 !important;
}

thead>tr>th.ant-table-cell-scrollbar {
    border-right: 0 !important;
}


.icon-container {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #fff;
}

.ant-popover-inner>.ant-popover-title {
    border-bottom: none;
    padding: 16px 12px 4px;
}

.ant-popover-inner-content {
    padding: 16px 12px;
    color: #4E5969;
}


// .ant-select:not(.ant-select-customize-input) .ant-select-selector {
//     position: relative;
//     background-color: #fff;
//     border: 1px solid #d9d9d9;
//     border-radius: 2px;
//     transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
// }

.colorSelect {
    width: 68px !important;

    .ant-select-selector {
        background-color: #F5F6F7 !important;
    }
}

.task-select {
    &:hover {

        .ant-select-arrow,
        .ant-select-selection-item div {
            color: #165DFF !important;
        }
    }

    &.ant-select-single.ant-select-open {

        .ant-select-arrow,
        .ant-select-selection-item div {
            color: #165DFF !important;
        }

        .ant-select-arrow {
            transform: rotate(180deg);
        }
    }
}

.task-popup {
    .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
        background: #F6F7F9;

        div {
            font-weight: 600 !important;
            color: #1D2129 !important;
        }
    }
}



.ant-custom-item .ant-collapse-item .ant-collapse-header {
    pointer-events: none;
    background-color: #F1F3F6;
}

.ant-collapse>.ant-collapse-item {
    border: 1px solid #E3E4E6;
    border-radius: 2px;
    margin-bottom: 16px;
}

.custom-collapse .ant-collapse-content-box {
    padding-top: 16px !important;
    background: #FFFFFF;
}

.ant-collapse-borderless {
    background-color: #ffffff;
}

.ant-collapse>.ant-collapse-item:last-child {
    border-bottom: 1px solid #E3E4E6;
}


.blue-bar {
    // position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 14px;
    margin-right: 8px;
    margin-top: 4px;
    background-color: blue;
}

.order-filter {
    .ant-form-item-control {
        max-width: 200px;
    }
}

// 不要删，解决横向滚动条出现两条问题
.ant-table:has(.ant-table-sticky-scroll) {
    ::-webkit-scrollbar {
        height: 0;
    }
}

.area-pop {
    .ant-popover-inner {
        .ant-popover-title {
            padding: 0;
            line-height: 20px;
            min-height: 20px;
            color: #ADB2BA;
            font-size: 12px;
            margin-bottom: 12px;
            padding: 6px 12px;
        }

        .ant-popover-inner-content {
            padding: 0;

            .ant-list {
                max-height: 400px;
                overflow-y: auto;

                .ant-list-item {
                    padding: 0 12px 12px 12px;
                    border: none;
                    display: flex;
                    justify-content: space-between;

                    >div {
                        width: 80%;
                        overflow-wrap: break-word;
                    }

                    .ant-list-item-action {
                        margin-left: 0;

                        li {
                            padding-right: 0;
                            color: #969799;
                            font-size: 10px;
                        }
                    }
                }
            }


            .input-row {
                padding: 6px 12px;
                border-top: 0.5px solid #F0F2F5;

                .ant-form-item {
                    margin-bottom: 0;
                }

                .ant-btn {
                    padding-right: 0;
                }
            }
        }
    }
}

.ant-table-cell-ellipsis:hover {
    white-space: pre-wrap;
    /* 保持换行符和空格 */
    word-wrap: break-word;
    /* 强制换行 */
}

.custom-ant-tab {
    .ant-tabs-nav .ant-tabs-tab-active {
        background-color: inherit !important;
    }

    .ant-tabs-tab {
        padding-left: 0px !important;
    }

    .ant-tabs-ink-bar {
        display: block !important;
    }

    .ant-timeline-item-tail {
        position: absolute;
        top: 10px;
        left: 4px;
        height: calc(100% - 10px);
        border-left: 2px dashed #f0f0f0;
    }



    .popover-title-type::after,
    .popover-content-type::before {
        content: "";
        display: block;
        height: 1px;
        background-color: #D9D9D980;
        width: 264px;
        margin: 10px -12px -8px -12px;
    }


    /* 默认链接样式 */
    a {
        color: #1D2129;
        /* 默认字体颜色为黑色 */
    }

    /* 鼠标悬停时的链接样式 */
    a:hover {
        color: #165DFF;
        /* 鼠标悬停时字体颜色为蓝色 */
    }

    .custom-input-addon-after {
        width: 24px;
        /* 设置宽度为 100 像素 */
    }


    .popover-title-custom-type::after,
    .popover-content-type::before {
        content: "";
        display: block;
        height: 1px;
        background-color: #D9D9D980;
        width: 344px;
        margin: 10px -12px 10px -12px;
    }


    .ant-input-group-addon {
        background-color: #FFFFFF !important;
    }


    .custom-deng-modal-content {
        width: 344px !important;
        // margin: 10px -12px 10px -12px;
    }

    .custom-deng-modal .ant-modal-content {
        box-shadow: none; // 移除阴影效果
        margin: 0px -12px 0px -12px;
    }

    /* 自定义类名 */
    .custom-deng-modal {
        /* 隐藏底部区域 */
        padding-bottom: 0;
        margin-bottom: 0;
        border-bottom: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }


    .custom-input .ant-input-group-addon {
        border-color: #165DFF !important;
    }

    .custom-input .ant-input-group .ant-input {
        border-right-color: #165DFF !important;
    }

    .draggable {
        cursor: move;
    }

    .custom-modal-body {
        margin-left: -20px !important;
    }


    .disabled-link {
        pointer-events: none;
        opacity: 0.5;
        /* 可选，使链接看起来禁用 */
    }


    /* 设置已选禁用状态下的边框颜色为蓝色 */
    .ant-checkbox-group .ant-checkbox-wrapper.ant-checkbox-wrapper-disabled .ant-checkbox-checked .ant-checkbox-inner {
        border-color: #165DFF !important;
    }

    /* 设置已选禁用状态下的选中对号颜色为蓝色 */
    .ant-checkbox-group .ant-checkbox-wrapper.ant-checkbox-wrapper-disabled .ant-checkbox-checked .ant-checkbox-inner::after {
        border-color: #ffffff !important;
    }

    /* 降低已选禁用状态下的不透明度 */
    .ant-checkbox-group .ant-checkbox-wrapper.ant-checkbox-wrapper-disabled .ant-checkbox-checked .ant-checkbox-inner {
        opacity: 0.6;
        background-color: #165DFF !important;
    }

    .ant-carousel .slick-dots li button {
        display: none;
    }

    .ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date-value {
        text-align: left;
    }
}

.block-tab {
    .ant-tabs-nav {
        &::before {
            border-bottom: none;
        }

        .ant-tabs-nav-list {
            background: #f4f6fa;
            padding: 4px 8px;
            border-radius: 2px;

            .ant-tabs-ink-bar {
                display: none;
            }

            .ant-tabs-tab+.ant-tabs-tab {
                margin: 0 0 0 8px;
            }

            .ant-tabs-tab {
                padding: 2px 8px;
                line-height: 22px;
                border-radius: 2px;

                &.ant-tabs-tab-active {
                    background: #fff;
                }
            }
        }
    }
}

.block-radio {
    background: #F4F6FA;
    padding: 4px !important;

    .ant-radio-button-wrapper {
        border: none;
        background: none;
        line-height: 26px;
        height: 26px;
        padding: 0 8px;
        transition: none;

        &:first-child {
            border-left: none;
        }

        &:not(:first-child)::before {
            display: none;
        }

        &:hover {
            color: #165DFF;
        }

        &.ant-radio-button-wrapper-checked {
            border: none;
            color: #165DFF;
            background: #fff;

            &:hover {
                color: #165DFF;
            }

            &:not(.ant-radio-button-wrapper-disabled):focus-within {
                box-shadow: none;
            }
        }
    }
}

.setting-tab {
    >.ant-tabs-nav>.ant-tabs-nav-wrap>.ant-tabs-nav-list {
        >.ant-tabs-tab.ant-tabs-tab-active {
            background: #e8efff;
        }

        >.ant-tabs-ink-bar {
            display: none;
        }
    }
}

.custom-date-cell {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #000;
}

.tagify.tagify--noTags.tagify--empty {
    width: 100% !important;
    /* 添加其他自定义样式 */
}

.label-wrap-style {
    label {
        word-break: break-all;
        white-space: initial;
        height: auto;
    }
}

.ant-table-filter-trigger {
    margin: 0;
    /* 或者使用 margin: auto; */
}

.ant-table-column-title {
    position: relative;
    z-index: 1;
    flex: initial;
}

.ant-table-column-sorters {
    // display: flex;
    justify-content: initial;
}

.ant-table-column-sorter {
    margin-left: 4px;
    margin-top: 4px;
    color: #bfbfbf;
    font-size: 0;
    transition: color .3s;
}

.ant-table-filter-column {
    // display: flex;
    justify-content: initial;
}

.ant-table-filter-trigger {
    position: relative;
    display: flex;
    align-items: center;
    margin: -4px -8px -4px 4px;
    /* padding: 0 4px; */
    color: #bfbfbf;
    font-size: 12px;
    border-radius: 2px;
    cursor: pointer;
    transition: all .3s;
    margin-top: 0px;
    margin-left: 2px;
}




.custom-tooltip {
    .ant-tooltip-arrow {
        display: none;
        /* 隐藏箭头 */
    }

    .ant-tooltip-inner {
        all: unset;
        /* 重置所有默认样式 */
        display: inline-flex;
        align-items: center;
        padding: 0 8px;
        height: 30px !important;
        color: #F9A145;
        background: #FEF6EC;
        border: 1px solid #F9A145;
        border-radius: 30px;
        position: relative;

        &::after {
            position: absolute;
            top: 30px;
            right: 20px;
            border-top: 6px solid #F9A145;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            content: '';
        }

        &::before {
            position: absolute;
            z-index: 2;
            top: 28.5px;
            right: 20px;
            border-top: 6px solid #FEF6EC;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            content: '';
        }


        span {
            // position: relative;
            // bottom: 3px;

            img {
                vertical-align: sub;
                margin-right: 4px;
            }
        }
    }
}

// /* 设置已选禁用状态下的边框颜色为蓝色 */
// .ant-checkbox-group .ant-checkbox-wrapper.ant-checkbox-wrapper-disabled .ant-checkbox-checked .ant-checkbox-inner {
//     border-color: #165DFF !important;
// }

// /* 设置已选禁用状态下的选中对号颜色为蓝色 */
// .ant-checkbox-group .ant-checkbox-wrapper.ant-checkbox-wrapper-disabled .ant-checkbox-checked .ant-checkbox-inner::after {
//     border-color: #165DFF !important;
// }

// /* 降低已选禁用状态下的不透明度 */
// .ant-checkbox-group .ant-checkbox-wrapper.ant-checkbox-wrapper-disabled .ant-checkbox-checked .ant-checkbox-inner {
//     opacity: 0.6;
// }


/* 设置已选禁用状态下的边框颜色为蓝色 */
.ant-checkbox-group .ant-checkbox-wrapper.ant-checkbox-wrapper-disabled .ant-checkbox-checked .ant-checkbox-inner {
    border-color: #165DFF !important;
    background-color: #165DFF !important;
}

/* 降低已选禁用状态下的不透明度 */
.ant-checkbox-group .ant-checkbox-wrapper.ant-checkbox-wrapper-disabled .ant-checkbox-checked .ant-checkbox-inner {
    opacity: 0.6;
}



.custom-tagify {
    width: 100% !important;
    /* 使用!important规则 */
}

.rectangle {
    border: 0.5px solid #8E939F66;
    border-radius: 2px;
    background: linear-gradient(to bottom, #E4ECFE, #F7F9FB);
}

.triangle {
    margin-top: -13px;
    border-width: 7px;
    border-style: solid;
    border-color: transparent transparent #8E939F66;
    padding: 0px;
    width: 0px;
    height: 0px;
    left: 24px;
    position: absolute;
}

.triangle_inner {
    margin: 0px;
    border-width: 10px;
    border-style: solid;
    border-color: transparent transparent #FFF;
    padding: 0px;
    width: 0px;
    height: 0px;
    left: -10px;
    margin-top: -8px;
    position: absolute;
}



.custom-time-picker {
    .ant-picker-panel-container {
        box-shadow: 4px 0 6px 0 rgba(0, 0, 0, 0.1);
        border-radius: 0 2px 2px 0;


        .ant-picker-time-panel {
            border-left: 1px solid #f0f0f0;
        }
    }

    .ant-picker-ranges {
        display: none;
    }

    .ant-picker-footer-extra {
        border-bottom: none;
        padding: 7.5px 8px;
    }
}

.custom-date-picker {
    .ant-picker-panel-container {
        box-shadow: -4px 0 6px 0 rgba(0, 0, 0, 0.1);
        border-radius: 2px 0 0 2px;
    }
}

/* 表单项前显示必选的*号 */
.required-form-item {
    .ant-form-item-label>label::before {
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        /* 红色 */
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
    }
}

.edc-verification-table thead.ant-table-thead>tr>th {
    height: 30px !important;
    line-height: 30px !important;
    padding: 0 !important;
}

.edc-docking-setting-vertical-divider {
    width: 0;
    height: 12px;
    border: 0.5px solid rgba(221, 222, 223, 1)
}

.new-subject-highlight-row {
    background: #E8EFFF;
}

.new-subject-highlight-row .ant-table-cell-fix-left {
    background: #E8EFFF;
    /* 确保固定列背景色也应用 */
}

.new-subject-highlight-row .ant-table-cell-fix-right {
    background: #E8EFFF;
    /* 确保固定列背景色也应用 */
}

/* 基于170px宽度进行布局 */
.order-check-time-picker {
    .ant-picker-panel {
        width: 170px;
        padding-top: 28px;
    }

    .ant-picker-time-panel {
        padding-top: 0 !important;
    }

    .ant-picker-footer-extra {
        padding: 0;
        position: absolute;
        top: 0;
    }

    .order-check-time-header {
        width: 170px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        height: 28px;
        color: rgba(147, 150, 155, 1);

        span {
            width: 84px;
            text-align: center;
        }
    }

    .ant-picker-time-panel-cell-inner {
        padding-left: 34px !important;
    }

    .ant-btn-primary {
        height: 26px;
        width: 46px;
        padding: 2px 8px;
        background-color: rgb(22, 93, 255);
        font-size: 12px;
        border-radius: 2px !important;
        border-color: rgb(22, 93, 255);
    }
}

.colored-disable-checked-checkbox .ant-checkbox-checked.ant-checkbox-disabled .ant-checkbox-inner {
    background-color: fade(@primary-color, 50%); // 背景色半透明
    //border-color: fade(@primary-color, 50%); // 边框色半透明

    // 保持对勾颜色不变
    &::after {
        border-color: @checkbox-check-color; // 使用默认对勾颜色
    }
}
.ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner {
    background-color: fade(@primary-color, 50%);
}
.ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after {

    border-color: fade(#FFFFFF, 50%);
    animation-name: none;
}

.push-scene-checkbox .ant-checkbox-wrapper>span:last-child {
    padding-right: 0;
    /* 移除右侧的 padding */
}

.text-overflow-two-line {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    /* 限制文本为两行 */
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 移除选中标签的默认样式 */
.no-tag-style {
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    //color: inherit !important;
}

.custom-multi-select-with-slash .ant-select-selection-overflow-item {
    margin-right: 0 !important;
}

/* 针对需要顶部左对齐的单元格 */
.custom-left-top-align {
    text-align: left !important;
    vertical-align: top !important;
}

/* 让选择列的宽度 = 复选框宽度 + 左右边距（16px × 2） */
.ant-table-cell-style.ant-table-selection-column {
  width: 20px !important;  /* 16px（复选框） + 16px（左） + 16px（右） */
  min-width: 20px !important;
  padding: 0 16px !important; /* 左右边距 */
  text-align: center; /* 让复选框居中 */
}


.custom-label-form .ant-form-item-label > label {
  white-space: normal;
  word-break: break-all;
  display: inline-block;
}

.ant-form-item-label-wrap {
    align-content: center;
    > label {
        height: auto;
        
    }
}
 
.ant-form-horizontal .ant-form-item-control {
    justify-content: center;
}
