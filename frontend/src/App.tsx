import {FC, useEffect, useMemo} from 'react';
import './App.less';
import {Router} from "components/router";
import {UseRequestProvider} from "context/request";
import {ConfigProvider, Empty} from "antd";
import {IntlProvider} from 'react-intl';
import {useGlobal} from "./context/global";
import {message as zh} from "locales/zh";
import {message as en} from "locales/en";
import {message as ko} from "locales/ko";
import {AuthProvider} from 'context/auth';
import {LockWindow} from "./pages/common/lock";
import "assets/iconfont/iconfont.js";
import "assets/iconfont/iconfont.css";

import EmptyImg from 'images/empty.png';
import { antdMessages, getDefaultLocale } from "./data/data";
export const App: FC = () => {
    useEffect(() => {
        // 页面加载完成后设置translate属性
        document.documentElement.setAttribute('translate', 'no');
    }, []);

    const g = useGlobal();

    const messages = (lang: string | null) => {
        switch (lang) {
            case "en":
                return en;
            case "zh":
                return zh;
            case "ko":
                return ko;
            default:
                return en;
        }
    };

    const defaultLocale = useMemo(() => {
        return getDefaultLocale(g.lang)
    }, [g.lang])

    return (
        <IntlProvider messages={messages(defaultLocale)} locale={defaultLocale} defaultLocale="en">
            <UseRequestProvider>
                <ConfigProvider
                    locale={antdMessages(g.lang)}
                    autoInsertSpaceInButton={false}
                    renderEmpty={
                        () => {
                            return <Empty image={<img alt={""} src={EmptyImg}></img>} />
                        }
                    }
                >
                    <AuthProvider>
                        <Router />
                        <LockWindow />
                    </AuthProvider>
                </ConfigProvider>
            </UseRequestProvider>
        </IntlProvider>
    );
}

