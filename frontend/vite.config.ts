import {defineConfig} from 'vite';
import reactPlugin from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';
import {viteCommonjs} from '@originjs/vite-plugin-commonjs'
import viteCompression from 'vite-plugin-compression'
import path = require("path");

export default defineConfig({
    resolve: {
        alias: [
            {find: /^~/, replacement: ""},
        ],

    },
    build: {
        target: "ES2015",
        assetsDir: "static",
        outDir: "build",
        rollupOptions: {
            output: {
                manualChunks(id: string) {
                    if (id.includes('node_modules/react')) {
                        return 'react';
                    }
                    if (id.includes('node_modules/antd')) {
                        return 'ui';
                    }
                    if (id.includes("node_modules/@yaireo/tagify")) {
                        return "tag";
                    }
                }
            }
        }
    },
    plugins: [
        reactPlugin(),
        tsconfigPaths(),
        viteCommonjs(),
        viteCompression(),
    ],
    css: {
        preprocessorOptions: {
            less: {
                math: "always",
                javascriptEnabled: true
            }
        }
    },
    server: {
        proxy: {
            '/api': {
                target: "http://127.0.0.1:8080",
                changeOrigin: true
            },
        },
        port: 3000,
    },
})