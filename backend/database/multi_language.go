package database

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"context"
	"github.com/duke-git/lancet/v2/slice"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// MultiLanguageTranslateFindByKeys 获取key的翻译
func MultiLanguageTranslateFindByKeys(ctx context.Context, languageId primitive.ObjectID, keys []string) map[string]string {
	result := make(map[string]string)
	// 获得翻译
	filter := bson.M{"language_id": languageId, "key": bson.M{"$in": keys, "$ne": ""}}
	pipeline := mongo.Pipeline{
		{{"$match", filter}},
		{{"$project", bson.M{"key": 1, "name": 1}}},
	}
	cursor, err := tools.Database.Collection("project_multi_language_translate").Aggregate(ctx, pipeline, nil)
	if err != nil {
		return result
	}
	data := make([]models.ProjectMultiLanguageTranslate, 0)
	if err := cursor.All(ctx, &data); err != nil {
		return result
	}
	slice.ForEach(data, func(index int, item models.ProjectMultiLanguageTranslate) {
		result[item.Key] = item.TranslateValue
	})
	return result
}
