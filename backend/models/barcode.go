package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// BarcodeGroup 条形码组_研究产品维度
type BarcodeGroup struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID    primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID      primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	//DrugName      string               `json:"drugName" bson:"drug_name"`           //研究产品名称
	//Spec          string               `json:"spec" bson:"spec"`                    //研究产品规格
	EffectiveTime    time.Duration        `json:"effectiveTime" bson:"effective_time"`        //有效时间
	BatchNumber      string               `json:"batchNumber" bson:"batch_number"`            //批次号
	CreatTime        time.Duration        `json:"creatTime" bson:"creat_time"`                //生成时间
	StorehouseID     primitive.ObjectID   `json:"storehouseId" bson:"storehouse_id"`          //仓库id
	Prefix           string               `json:"prefix" bson:"prefix"`                       //短码前缀
	BarcodeRule      int                  `json:"barcodeRule" bson:"barcode_rule"`            // 研究产品条形码生成顺序
	IsPackageBarcode bool                 `json:"isPackageBarcode" bson:"is_package_barcode"` //是否生成包装条形码
	PackageRule      int                  `json:"packageRule" bson:"package_rule"`            //包装条形码的规则
	MedicineIDs      []primitive.ObjectID `json:"medicineIds" bson:"medicine_ids"`            //生成的研究产品
	PackageNumbers   []string             `json:"packageNumbers" bson:"package_numbers"`      //包装号
	CorrelationID    string               `json:"correlationId" bson:"correlation_id"`        //关联任务ID
}

type EnvBarcodeNumber struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID    primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID `json:"envId" bson:"env_id"`
	ProjectNumber string             `json:"project_number" bson:"project_number"`
	EnvNumber     string             `json:"env_number" bson:"env_number"`
	Number        string             `json:"number" bson:"number"`
}

type BarcodeRule struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID     primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID      primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID  primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID       primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	CodeRule       int                `json:"codeRule" bson:"code_rule"`              //编码规则 0手动上传 1系统自动编码
	CodeConfigInit bool               `json:"codeConfigInit" bson:"code_config_init"` //编码配置是否已经初始化
}

type AppVersion struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	VersionNumber string             `json:"versionNumber" bson:"version_number"`
	UpdateContent string             `json:"updateContent" bson:"update_content"`
	FileID        primitive.ObjectID `json:"fileId" bson:"file_id"`
	FileName      string             `json:"fileName" bson:"file_name"`
}
