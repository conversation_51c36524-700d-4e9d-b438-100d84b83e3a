package models

import (
	"encoding/xml"
	"go.mongodb.org/mongo-driver/bson/primitive"
)


type XmlResponse struct {
	XMLName xml.Name `xml:"Response"`
	ReferenceNumber string `xml:"ReferenceNumber,attr"`
	InboundODMFileOID string `xml:"InboundODMFileOID,attr"`
	IsTransactionSuccessful string `xml:"IsTransactionSuccessful,attr"`
	ReasonCode string `xml:"ReasonCode,attr"`
	ErrorOriginLocation string `xml:"ErrorOriginLocation,attr"`
	SuccessStatistics string `xml:"SuccessStatistics,attr"`
	ErrorClientResponseMessage string `xml:"ErrorClientResponseMessage,attr"`
}


type ODMAdd struct {
	XMLName          xml.Name       `xml:"ODM"`
	Xmlns            string         `xml:"xmlns,attr"`
	ODMVersion       string         `xml:"ODMVersion,attr"`
	FileType         string         `xml:"FileType,attr"`
	FileOID          string         `xml:"FileOID,attr"`
	CreationDateTime string         `xml:"CreationDateTime,attr"`
	ClinicalData     ClinicalDataAdd   `xml:"ClinicalData"`
}

type ClinicalDataAdd struct {
	StudyOID           string       `xml:"StudyOID,attr"`
	MetaDataVersionOID string       `xml:"MetaDataVersionOID,attr"`
	SubjectDataAdd        SubjectDataAdd  `xml:"SubjectData"`
}

type SubjectDataAdd struct {
	SubjectKey      string    `xml:"SubjectKey,attr"`
	TransactionType string    `xml:"TransactionType,attr"`
	SiteRef         SiteRefAdd   `xml:"SiteRef"`
}

type SiteRefAdd struct {
	LocationOID string   `xml:"LocationOID,attr"`
}



type ODM struct {
	XMLName          xml.Name       `xml:"ODM"`
	Xmlns            string         `xml:"xmlns,attr"`
	ODMVersion       string         `xml:"ODMVersion,attr"`
	FileType         string         `xml:"FileType,attr"`
	FileOID          string         `xml:"FileOID,attr"`
	CreationDateTime string         `xml:"CreationDateTime,attr"`
	ClinicalData     ClinicalData   `xml:"ClinicalData"`
}

type ClinicalData struct {
	StudyOID           string       `xml:"StudyOID,attr"`
	MetaDataVersionOID string       `xml:"MetaDataVersionOID,attr"`
	SubjectDataUpdate        SubjectDataUpdate  `xml:"SubjectData"`
}

type SubjectDataUpdate struct {
	SubjectKey      string    `xml:"SubjectKey,attr"`
	TransactionType string    `xml:"TransactionType,attr"`
	SiteRef         SiteRef   `xml:"SiteRef"`
	StudyEventData  StudyEventData `xml:"StudyEventData"`
}

type SiteRef struct {
	LocationOID string   `xml:"LocationOID,attr"`
}


type StudyEventData struct {
	StudyEventOID         string       `xml:"StudyEventOID,attr"`
	//StudyEventRepeatKey   string       `xml:"StudyEventRepeatKey,attr"`
	TransactionType       string       `xml:"TransactionType,attr"`
	FormData              FormData     `xml:"FormData"`
}

type FormData struct {
	FormOID           string       `xml:"FormOID,attr"`
	//FormRepeatKey     string       `xml:"FormRepeatKey,attr"`
	TransactionType   string       `xml:"TransactionType,attr"`
	ItemGroupData     ItemGroupData `xml:"ItemGroupData"`
}

type ItemGroupData struct {
	ItemGroupOID           string       `xml:"ItemGroupOID,attr"`
	//ItemGroupRepeatKey     string       `xml:"ItemGroupRepeatKey,attr"`
	TransactionType        string       `xml:"TransactionType,attr"`
	//ItemData               ItemData     `xml:"ItemData"`
	ItemDataList           []ItemData   `xml:"ItemData"`
}

type ItemData struct {
	XMLName           xml.Name     `xml:"ItemData"`
	ItemOID           string       `xml:"ItemOID,attr"`
	Value             string       `xml:"Value,attr"`
}


type Env struct {
	ID         primitive.ObjectID `json:"id" bson:"id"`
	Name       string             `json:"name" bson:"name"`
	Url        string             `json:"url" bson:"url"`
}


// UrlInfo ...
type UrlInfo struct {
	Url        		string             `json:"url" bson:"url"`
	UserName        string             `json:"userName" bson:"user_name"`
	Password        string             `json:"password" bson:"password"`
}

// VisitRandomization ...
type VisitRandomization struct {
	Visits     			[]Visit                `json:"visits" bson:"visits"`
	Randomizations     	[]Randomization        `json:"randomizations" bson:"randomizations"`
}

// Visit ...
type Visit struct {
	Name     				string             `json:"name" bson:"name"`
	VisitConfigs    		[]VisitConfig      `json:"visitConfigs" bson:"visit_configs"`
}


// VisitConfig ...
type VisitConfig struct {
	VisitCode    			string              `json:"visitCode" bson:"visit_code"`
	FolderOid				string 				`json:"folderOid" bson:"folder_oid"`
	FormOid		    		string 				`json:"formOid" bson:"form_oid"`
	IpNumberOid		    	string 				`json:"ipNumberOid" bson:"ip_number_oid"`
	DispenseTimeOid		    string 				`json:"dispenseTimeOid" bson:"dispense_time_oid"`
}

type Randomization struct {
	Name     					string              `json:"name" bson:"name"`
	RandomizationConfigs    		[]RandomizationConfig      `json:"randomizationConfigs" bson:"randomization_configs"`

	//RandomizationCode	[]FolderFormField	   `json:"randomizationCode" bson:"randomization_code"`
	//RandomizationTime	[]FolderFormField	   `json:"randomizationTime" bson:"randomization_time"`
	//Group				[]FolderFormField	   `json:"group" bson:"group"`
	//Factor				[]FolderFormField	   `json:"factor" bson:"factor"`
	//Cohort				[]FolderFormField	   `json:"cohort" bson:"cohort"`
}

type RandomizationConfig struct {
	RandomizationField    		string              `json:"randomizationField" bson:"randomization_field"`
	FolderOid					string 				`json:"folderOid" bson:"folder_oid"`
	FormOid		    		    string 				`json:"formOid" bson:"form_oid"`
	FieldOid		    		string 				`json:"fieldOid" bson:"field_oid"`
}

type FolderFormField struct {
	FolderOid					string 				`json:"folderOid" bson:"folder_oid"`
	FormOid		    		    string 				`json:"formOid" bson:"form_oid"`
	FieldOid		    		string 				`json:"fieldOid" bson:"field_oid"`
}