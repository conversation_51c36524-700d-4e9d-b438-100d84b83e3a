package models

import "go.mongodb.org/mongo-driver/bson/primitive"

const (
	CustomTemplateTable = "custom_template"
)

type CustomTemplate struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	ProjectID primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvID     primitive.ObjectID `json:"envId" bson:"env_id"`
	UserId    primitive.ObjectID `json:"userId" bson:"user_id"`
	Type      int                `json:"type" bson:"type"`
	Name      string             `json:"name" bson:"name"`
	Fields    []string           `json:"fields" bson:"fields"`
	Meta
}
