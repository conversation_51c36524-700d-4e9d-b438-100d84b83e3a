package models

import "go.mongodb.org/mongo-driver/bson/primitive"

type MultiLanguage struct {
}

// Language ..
type Language struct {
	Code     string `json:"code" bson:"code"`          // 语言code
	Name     string `json:"name" bson:"name"`          // 语言
	BaseCode string `json:"baseCode" bson:"base_code"` // 未翻译时的缺省语言
}

// ProjectMultiLanguage ..
type ProjectMultiLanguage struct {
	ID                  primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID          primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID           primitive.ObjectID `json:"projectId" bson:"project_id"`
	Code                string             `json:"code" bson:"code"`                                 // 语言code
	Language            string             `json:"language" bson:"language"`                         // 语言
	BaseLanguageCode    string             `json:"baseLanguageCode" bson:"base_language_code"`       // 默认展示的语言
	Status              int                `json:"status" bson:"status"`                             // 启用状态 1启用 0禁用
	SharedSystemLibrary int                `json:"sharedSystemLibrary" bson:"shared_system_library"` // 启用系统库 1启用 0禁用
	Meta                Meta               `json:"meta" bson:"meta"`
}

// ProjectMultiLanguageVo ..
type ProjectMultiLanguageVo struct {
	ID                  string `json:"id" bson:"_id"`
	CustomerID          string `json:"customerId" bson:"customer_id"`
	ProjectID           string `json:"projectId" bson:"project_id"`
	Code                string `json:"code" bson:"code"`                                 // 语言code
	Language            string `json:"language" bson:"language"`                         // 语言
	Status              int    `json:"status" bson:"status"`                             // 启用状态 1启用 0禁用
	SharedSystemLibrary int    `json:"sharedSystemLibrary" bson:"shared_system_library"` // 启用系统库 1启用 0禁用
	TranslationQuantity int64  `json:"translationQuantity" bson:"translation_quantity"`  // 已经翻译的数量
	BaseLanguageCode    string `json:"baseLanguageCode" bson:"base_language_code"`       // 默认展示的语言
}

// ProjectMultiLanguageTranslateVo ..
type ProjectMultiLanguageTranslateVo struct {
	ID                     primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID             primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID              primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID          primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID               primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	LanguageID             primitive.ObjectID `json:"languageId" bson:"language_id"`
	LanguageLibrary        int                `json:"languageLibrary" bson:"language_library"` // 语言库: 1 项目库/系统库 2 项目库/项目构建 3 eIRT库
	PagePath               []string           `json:"pagePath" bson:"page_path"`               // 页面路径
	Type                   string             `json:"type" bson:"type"`                        // 类型
	Key                    string             `json:"key" bson:"key"`                          // key
	TranslateValue         string             `json:"translateValue"`                          // 翻译值
	LanguageName           string             `json:"languageName"`                            // 语言名称
	LanguageLibraryValueZh string             `json:"languageLibraryValueZh"`                  // 语言库中文值
	LanguageLibraryValueEn string             `json:"languageLibraryValueEn"`                  // 语言库英文值
	PagePathValueZh        string             `json:"pagePathValueZh"`                         // 页面路径中文值
	PagePathValueEn        string             `json:"pagePathValueEn"`                         // 页面路径英文值
	TypeValueZh            string             `json:"typeValueZh"`                             // 类型中文值
	TypeValueEn            string             `json:"typeValueEn"`                             // 类型英文值
	NameValueZh            string             `json:"nameValueZh"`                             // name中文值
	NameValueEn            string             `json:"nameValueEn"`                             // name英文值

}

// ProjectMultiLanguageTranslate ..
type ProjectMultiLanguageTranslate struct {
	ID              primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID      primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID       primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID   primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID        primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	LanguageID      primitive.ObjectID `json:"languageId" bson:"language_id"`
	LanguageLibrary int                `json:"languageLibrary" bson:"language_library"` // 语言库: 1 项目库/系统库 2 项目库/项目构建 3 eIRT库
	PagePath        []string           `json:"pagePath" bson:"page_path"`               // 页面路径
	Type            string             `json:"type" bson:"type"`                        // 类型
	Key             string             `json:"key" bson:"key"`                          // key
	TranslateValue  string             `json:"translateValue" bson:"translate_value"`   // 翻译值
}

type LanguageTransactionCounts struct {
	Value string `json:"value"`
	Count int64  `json:"count"`
}
