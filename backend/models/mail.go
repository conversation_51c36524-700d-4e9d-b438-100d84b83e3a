package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Mail ..
type Mail struct {
	ID             primitive.ObjectID     `json:"id" bson:"_id"`
	Subject        string                 `json:"subject" bson:"subject"` // 邮件主题（locales/）
	SubjectData    map[string]interface{} `json:"subjectData" bson:"subject_data"`
	Content        string                 `json:"content" bson:"content"` // 邮件内容（locales）
	ContentData    map[string]interface{} `json:"data" bson:"data"`
	BodyContentKey []ContentKey           `json:"bodyContentKey" bson:"body_content_key"`
	ContentKey     []ContentKey           `json:"content_key" bson:"content_key"`
	Lang           string                 `json:"lang" bson:"lang"`
	LangList       []string               `json:"langList" bson:"lang_list"`
	To             []string               `json:"to" bson:"to"`
	Cc             []string               `json:"cc" bson:"cc"`
	Status         int8                   `json:"status" bson:"status"`   // 0.未发送 1.发送成功 2.发送失败 3.发送中
	Failure        string                 `json:"failure" bson:"failure"` //发送失败原因
	Retry          int                    `json:"retry" bson:"retry"`
	CreatedTime    time.Duration          `json:"createdTime" bson:"created_time"` // 创建时间
	ExpectedTime   time.Duration          `json:"time" bson:"time"`                // 期望发送时间
	SendTime       time.Duration          `json:"sendTime" bson:"send_time"`       // 发送时间
	HTML           string                 `json:"html" bson:"html"`
}
type MailEnv struct {
	ID         primitive.ObjectID `json:"id" bson:"_id"`
	MailID     primitive.ObjectID `json:"mailId" bson:"mail_id"`
	CustomerID primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID  primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvID      primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID   primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
}

type ContentKey struct {
	Index int                    `bson:"index"`
	Key   string                 `bson:"key"`
	Map   map[string]interface{} `bson:"map"`
}

// MailHistoryRecord ..已经发送成功的邮件历史记录
type MailHistoryRecord struct {
	ID             primitive.ObjectID     `json:"id" bson:"_id"`
	Subject        string                 `json:"subject" bson:"subject"` // 邮件主题（locales/）
	SubjectData    map[string]interface{} `json:"subjectData" bson:"subject_data"`
	Content        string                 `json:"content" bson:"content"` // 邮件内容（locales）
	ContentData    map[string]interface{} `json:"data" bson:"data"`
	BodyContentKey []ContentKey           `json:"bodyContentKey" bson:"body_content_key"`
	ContentKey     []ContentKey           `json:"content_key" bson:"content_key"`
	Lang           string                 `json:"lang" bson:"lang"`
	LangList       []string               `json:"langList" bson:"lang_list"`
	To             []string               `json:"to" bson:"to"`
	Cc             []string               `json:"cc" bson:"cc"`
	Status         int8                   `json:"status" bson:"status"`   // 0.未发送 1.发送成功 2.发送失败 3.发送中
	Failure        string                 `json:"failure" bson:"failure"` //发送失败原因
	Retry          int                    `json:"retry" bson:"retry"`
	CreatedTime    time.Duration          `json:"createdTime" bson:"created_time"` // 创建时间
	ExpectedTime   time.Duration          `json:"time" bson:"time"`                // 期望发送时间
	SendTime       time.Duration          `json:"sendTime" bson:"send_time"`       // 发送时间
	HTML           string                 `json:"html" bson:"html"`
}
