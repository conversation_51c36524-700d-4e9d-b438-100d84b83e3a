package models

// SimulateRandomReport PDF...模拟随机报告pdf
type SimulateRandomReport struct {
	Sponsor                  string                   `json:"sponsor"`                  //申办方名称
	ProjectNumber            string                   `json:"projectNumber"`            //项目编号
	SimulateRandomReport     string                   `json:"simulateRandomReport"`     //模拟随机报告
	Env                      string                   `json:"env"`                      //项目环境
	Cohort                   string                   `json:"cohort"`                   //cohort
	RunningTime              string                   `json:"runningTime"`              //模拟运行开始时间
	RunningDate              string                   `json:"runningDate"`              //年-月-日  时: 分: 秒
	GenerationTime           string                   `json:"generationTime"`           //模拟报告生成时间
	GenerationDate           string                   `json:"generationDate"`           //年-月-日  时: 分: 秒
	Generator                string                   `json:"generator"`                //生成者
	CreateBy                 string                   `json:"createBy"`                 //XXX
	Directory                string                   `json:"directory"`                //目录
	Summary                  string                   `json:"summary"`                  //概览
	SummaryDetails           string                   `json:"summaryDetails"`           //概览内容
	ConfigureParameterDetail ConfigureParameterDetail `json:"configureParameterDetail"` //随机配置参数详情
	SimulateRandomDetail     SimulateRandomDetail     `json:"simulateRandomDetail"`     //模拟随机详情
}

// ConfigureParameterDetail 随机配置参数详情
type ConfigureParameterDetail struct {
	Title                      string                   `json:"title"`                      //一级标题
	ConfigureParameterInfoList []ConfigureParameterInfo `json:"configureParameterInfoList"` //随机配置参数详情
}

// ConfigureParameterInfo 随机配置参数详情
type ConfigureParameterInfo struct {
	Type                      string                    `json:"type"`                      //随机类型 1,2
	RandomTypeLabel           string                    `json:"randomTypeLabel"`           //随机类型列名
	RandomType                string                    `json:"randomType"`                //随机类型
	RandomTableNameLabel      string                    `json:"randomTableNameLabel"`      //随机表名称列名
	RandomTableName           string                    `json:"randomTableName"`           //随机表名称
	BiasProbabilityLabel      string                    `json:"biasProbabilityLabel"`      //偏倚概率列名
	BiasProbability           string                    `json:"biasProbability"`           //偏倚概率
	RandomIdNumberLabel       string                    `json:"randomIdNumberLabel"`       //随机号数量列名
	RandomIdNumber            string                    `json:"randomIdNumber"`            //随机号数量
	GroupConfigurationDetail  GroupConfigurationDetail  `json:"groupConfigurationDetail"`  //组别配置详情
	FactorConfigurationDetail FactorConfigurationDetail `json:"factorConfigurationDetail"` //分层因素配置详情
	IsCountryLabel            string                    `json:"isCountryLabel"`            //国家是否作为分层因素列名
	IsCountry                 string                    `json:"isCountry"`                 //国家是否作为分层因素
	IsSiteLabel               string                    `json:"isSiteLabel"`               //中心是否作为分层因素列名
	IsSite                    string                    `json:"isSite"`                    //中心是否作为分层因素
	IsRegionLabel             string                    `json:"isRegionLabel"`             //区域是否作为分层因素列名
	IsRegion                  string                    `json:"isRegion"`                  //区域是否作为分层因素
}

// GroupConfigurationDetail 组别配置详情
type GroupConfigurationDetail struct {
	GroupConfigurationLabel string        `json:"groupConfigurationLabel"` //组别配置列名
	GroupNameLabel          string        `json:"groupNameLabel"`          //组别名称列名
	SubGroupNameLabel       string        `json:"subGroupNameLabel"`       //子组别列名
	GroupProportionLabel    string        `json:"groupProportionLabel"`    //组别比例列名
	GroupDetailList         []GroupDetail `json:"groupDetailList"`         //组别配置集合...
}

// GroupDetail 组别配置集合
type GroupDetail struct {
	GroupName       string `json:"groupName"`       //组别名称
	SubGroupName    string `json:"subGroupName"`    //子组别
	GroupProportion string `json:"groupProportion"` //组别比例
}

// FactorConfigurationDetail 分层因素配置详情
type FactorConfigurationDetail struct {
	FactorConfigurationLabel string         `json:"factorConfigurationLabel"` //分层因素列名
	FactorNameLabel          string         `json:"factorNameLabel"`          //分层因素名称列名
	OptionLabel              string         `json:"optionLabel"`              //选项值列名
	WeightRatioLabel         string         `json:"weightRatioLabel"`         //权重比列名
	FactorDetailList         []FactorDetail `json:"factorDetailList"`         //分层因素集合...
}

// FactorDetail 分层因素集合
type FactorDetail struct {
	FactorName  string `json:"factorName"`  //分层因素名称
	Option      string `json:"option"`      //选项值
	WeightRatio string `json:"weightRatio"` //权重比
}

// SimulateRandomDetail 模拟随机详情
type SimulateRandomDetail struct {
	Title                         string                        `json:"title"`                         //一级标题
	ProjectLabel                  string                        `json:"projectLabel"`                  //项目列名
	SerialLabel                   string                        `json:"serialLabel"`                   //序号列名
	SiteLabel                     string                        `json:"siteLabel"`                     //中心列名
	RegionLabel                   string                        `json:"regionLabel"`                   //区域列名
	CountryLabel                  string                        `json:"countryLabel"`                  //国家列名
	FactorLabel                   string                        `json:"factorLabel"`                   //分层列名
	CombinationFactorLabel        string                        `json:"combinationFactorLabel"`        //组合分层列名
	SubjectCountMinLabel          string                        `json:"subjectCountMinLabel"`          //受试者例数最小值列名
	TotalLabel                    string                        `json:"totalLabel"`                    //总计列名
	RunCountLabel                 string                        `json:"runCountLabel"`                 //运行次数标题
	PeopleCountLabel              string                        `json:"peopleCountLabel"`              //人数标签
	UnbalancedLabel               string                        `json:"unbalancedLabel"`               //不平衡标签
	SimulateRandomParameterDetail SimulateRandomParameterDetail `json:"simulateRandomParameterDetail"` //模拟随机参数详情
	OverviewDetail                OverviewDetail                `json:"overviewDetail"`                //总览详情
	DetailedResults               DetailedResults               `json:"detailedResults"`               //详细结果
}

// SimulateRandomParameterDetail 模拟随机参数详情
type SimulateRandomParameterDetail struct {
	Title                   string   `json:"title"`                   //一级标题
	SimulateRandomNameLabel string   `json:"simulateRandomNameLabel"` //模拟随机的名称列名
	SimulateRandomName      string   `json:"simulateRandomName"`      //模拟随机的名称
	RandomListNameLabel     string   `json:"randomListNameLabel"`     //启用的随机列表名称列名
	RandomListName          string   `json:"randomListName"`          //启用的随机列表名称
	SiteNumberLabel         string   `json:"siteNumberLabel"`         //中心数列名
	SiteNumber              string   `json:"siteNumber"`              //中心数
	RegionNumberLabel       string   `json:"regionNumberLabel"`       //区域数列名
	RegionNumber            string   `json:"regionNumber"`            //区域数
	CountryNumberLabel      string   `json:"countryNumberLabel"`      //国家数列名
	CountryNumber           string   `json:"countryNumber"`           //国家数
	RunNumberLabel          string   `json:"runNumberLabel"`          //运行次数列名
	RunNumber               string   `json:"runNumber"`               //运行次数
	SubjectQuantityLabel    string   `json:"subjectQuantityLabel"`    //受试者数列名
	SubjectQuantity         string   `json:"subjectQuantity"`         //受试者数
	FactorRatioLabel        string   `json:"factorRatioLabel"`
	FactorRatio             []string `json:"factorRatio"`
}

// OverviewDetail 总览详情
type OverviewDetail struct {
	Title string `json:"title"` //一级标题
	//AverageStandardDeviationDetail AverageStandardDeviationDetail `json:"averageStandardDeviationDetail"` //均数+-标准差详情
	AvgSDOverviewDetail              AvgSDOverviewDetail              `json:"avgSDOverviewDetail"`              //总览(均数±标准差)
	MinOverviewDetail                MinOverviewDetail                `json:"minOverviewDetail"`                //总览(最小值)
	UnbalancedRunCountOverviewDetail UnbalancedRunCountOverviewDetail `json:"unbalancedRunCountOverviewDetail"` //总览(不均衡运行次数)
	Groups                           []string                         `json:"groups"`
}

// AvgSDOverviewDetail 总览(均数±标准差)
type AvgSDOverviewDetail struct {
	Title         string        `json:"title"`         //标题
	AvgSDOverview AvgSDOverview `json:"avgSDOverview"` //总览(均数±标准差)
}

// MinOverviewDetail 总览(最小值)
type MinOverviewDetail struct {
	Title       string      `json:"title"`       //标题
	MinOverview MinOverview `json:"minOverview"` //总览(最小值)
}

// UnbalancedRunCountOverviewDetail 总览(不均衡运行次数)
type UnbalancedRunCountOverviewDetail struct {
	Title                      string                     `json:"title"`                      //标题
	UnbalancedRunCountOverview UnbalancedRunCountOverview `json:"unbalancedRunCountOverview"` //总览(不均衡运行次数)
}

// DetailedResults 详细结果
type DetailedResults struct {
	Title             string             `json:"title"`             //一级标题
	UnbalancedDetails []UnbalancedDetail `json:"unbalancedDetails"` //详情(不均衡数)
}
