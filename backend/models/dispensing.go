package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Dispensing ...
type Dispensing struct {
	ID                            primitive.ObjectID            `json:"id" bson:"_id"`
	CustomerID                    primitive.ObjectID            `json:"customerId" bson:"customer_id"`
	ProjectID                     primitive.ObjectID            `json:"projectId" bson:"project_id"`
	EnvironmentID                 primitive.ObjectID            `json:"envId" bson:"env_id"`
	CohortID                      primitive.ObjectID            `json:"cohortId" bson:"cohort_id"`
	SubjectID                     primitive.ObjectID            `json:"subjectId" bson:"subject_id"`
	VisitInfo                     VisitInfo                     `json:"visitInfo" bson:"visit_info"`                                         // 访视ID
	SerialNumber                  int                           `json:"serialNumber" bson:"serial_number"`                                   // 序号
	VisitSign                     bool                          `json:"visitSign" bson:"visit_sign"`                                         // 是否计划外访视标记(true是/false不是)
	DispensingMedicines           []DispensingMedicine          `json:"dispensingMedicines" bson:"dispensing_medicines"`                     // 编号研究产品
	OtherDispensingMedicines      []OtherDispensingMedicine     `json:"otherDispensingMedicines" bson:"other_dispensing_medicines"`          // 未编号研究产品
	RealDispensingMedicines       []DispensingMedicine          `json:"realDispensingMedicines" bson:"real_dispensing_medicines"`            // 实际发放药物
	RealOtherDispensingMedicines  []OtherDispensingMedicineInfo `json:"realOtherDispensingMedicines" bson:"real_other_dispensing_medicines"` // 实际发放药物
	Status                        int                           `json:"status"`                                                              // 状态 1待发药 2已发药 3不参加访视
	Reissue                       int                           `json:"reissue"`                                                             // 是否补发 0非补发 1补发
	DispensingTime                time.Duration                 `json:"dispensingTime" bson:"dispensing_time"`                               // 发药时间
	Reasons                       []Reason                      `json:"reasons" bson:"reasons"`                                              // 替换，撤销原因
	ReplaceMedicines              []ReplaceMedicines            `json:"replaceMedicines" bson:"replace_medicines"`                           // 被替换研究产品
	ReplaceOtherMedicines         []OtherDispensingMedicineInfo `json:"replaceOtherMedicines" bson:"replace_other_medicines"`                // 被替换研究产品
	Print                         int                           `bson:"print" json:"print"`                                                  // 是否已经打印
	Label                         string                        `bson:"label" json:"label"`                                                  // 对应发药标签
	Labels                        []string                      `bson:"labels" json:"labels"`                                                // DTP专用字段 对应多个标签
	Remark                        string                        `bson:"remark" json:"remark"`                                                // DTP专用字段 备注
	Order                         string                        `bson:"order" json:"order"`                                                  // 对应订单号
	ReplaceOrder                  []string                      `bson:"replace_order" json:"replaceOrder"`                                   // 具体药物替换对应具体订单号
	CancelMedicinesHistory        []DispensingMedicine          `json:"cancelMedicinesHistory" bson:"cancel_medicines_history"`              // 取回数据记录
	OtherMedicinesHistory         []OtherDispensingMedicineInfo `json:"otherMedicinesHistory" bson:"other_medicines_history"`                // 未编号取回数据记录
	FormulaInfo                   `bson:"formula_info" json:"formulaInfo"`
	ScanStatus                    int                        `json:"scanStatus" bson:"scan_status"` //确认扫码状态 0"未确认" 1“已确认”
	WorkTaskId                    primitive.ObjectID         `json:"workTaskId" bson:"work_task_id"`
	Form                          []CustomerFormula          `json:"form" bson:"form"` // 自定义表单公式计算
	DoseInfo                      *DoseInfo                  `json:"doseInfo" bson:"dose_info"`
	StartVisit                    int                        `json:"startVisit" bson:"start_visit"`                                         // 开启后续阶段访视标记
	InvalidRemark                 string                     `bson:"invalidRemark" json:"invalid_remark"`                                   // 不参加访视备注
	OthersDispensingMedicines     []OthersDispensingMedicine `json:"othersDispensingMedicines" bson:"others_dispensing_medicines"`          // 未编号研究产品
	ReplaceOthersMedicines        []OthersDispensingMedicine `json:"replaceOthersMedicines" bson:"replace_others_medicines"`                // 被替换研究产品
	RealOthersDispensingMedicines []OthersDispensingMedicine `json:"realOthersDispensingMedicines" bson:"real_others_dispensing_medicines"` // 实际发放药物
	Approvals                     []UrgentUnblindingApproval `json:"approvals" bson:"approvals"`                                            //审批流程/短信
}

type DoseInfo struct {
	DoseLevelList     *DoseLevel     `json:"doseLevelList" bson:"dose_level_list"`
	VisitJudgmentList *VisitJudgment `json:"visitJudgmentList" bson:"visit_judgment_list"`
	Form              *FormValue     `json:"form" bson:"form"`
	Frequency         int            `json:"frequency" bson:"frequency"`
}

// VisitInfo ..
type VisitInfo struct {
	VisitCycleInfoID primitive.ObjectID `json:"visitCycleInfoId" bson:"visit_cycle_info_id"` // 访视ID
	Number           string             `json:"number" validate:"required"`                  // 编号
	InstanceRepeatNo string             `json:"instanceRepeatNo" bson:"instance_repeat_no"`  // 二级编号
	BlockRepeatNo    string             `json:"blockRepeatNo" bson:"block_repeat_no"`        // 三级编号
	Name             string             `json:"name" validate:"required"`                    // 名称
	Random           bool               `json:"random"`                                      // 是否随机标记
	Dispensing       bool               `json:"dispensing"`                                  // 是否发药标记
}

// ReplaceMedicines 已经替换编号研究产品信息...
type ReplaceMedicines struct {
	MedicineID     primitive.ObjectID  `json:"medicineId" bson:"medicine_id"`         // 研究产品ID
	Name           string              `json:"name" bson:"name"`                      // 研究产品名称（存贮该字段是为了方便发药页面展示）
	Number         string              `json:"number" bson:"number"`                  // 研究产品号（存贮该字段是为了方便发药页面展示）
	ExpirationDate string              `json:"expirationDate" bson:"expiration_date"` // 有效期（存贮该字段是为了方便发药页面展示）
	BatchNumber    string              `json:"batchNumber" bson:"batch_number"`       // 批次号（存贮该字段是为了方便发药页面展示）
	MedicineNewID  primitive.ObjectID  `json:"medicineNewID" bson:"medicine_new_id"`  // 被替换研究产品ID
	NewNumber      string              `json:"newNumber" bson:"new_number"`           // 被替换研究产品号（存贮该字段是为了方便发药页面展示）
	Time           time.Duration       `json:"time" bson:"time"`                      // 被替换研究产品 初始发药时间
	OperationTime  time.Duration       `json:"operationTime" bson:"operation_time"`   // 操作时间
	Label          string              `json:"label" json:"label"`                    // 对应发药标签
	UseFormulas    *CustomerFormula    `bson:"use_formulas" json:"useFormulas"`
	DoseInfo       *DoseInfo           `bson:"dose_info" json:"doseInfo"`
	OpenSetting    int                 `bson:"open_setting" json:"openSetting"` // 发放类型 分类排序需要
	DTP            *int                `bson:"dtp" json:"dtp,omitempty"`
	OrderOID       *primitive.ObjectID `bson:"order_oid" json:"orderOid,omitempty"`
}

// DispensingMedicine 编号研究产品信息...
type DispensingMedicine struct {
	MedicineID     primitive.ObjectID  `json:"medicineId" bson:"medicine_id"`         // 研究产品ID
	Name           string              `json:"name" bson:"name"`                      // 研究产品名称（存贮该字段是为了方便发药页面展示）
	Number         string              `json:"number" bson:"number"`                  // 研究产品号（存贮该字段是为了方便发药页面展示）
	ShortCode      string              `json:"shortCode" bson:"short_code"`           //app对接新增短码字段
	ExpirationDate string              `json:"expirationDate" bson:"expiration_date"` // 有效期（存贮该字段是为了方便发药页面展示）
	BatchNumber    string              `json:"batchNumber" bson:"batch_number"`       // 批次号（存贮该字段是为了方便发药页面展示）
	PackageNumber  string              `json:"packageNumber" bson:"package_number"`
	Time           time.Duration       `json:"time" bson:"time"`                       // 具体研究产品操作时间
	Type           int                 `json:"type" bson:"type"`                       // 具体研究产品操作类型 1、首次 2、补发 3、替换 4 实际用药 5、访视外发药 6、取回 7 撤销 8 登记后可用 9 登记后作废 注：8、9为被登记的记录
	RealMedicineID primitive.ObjectID  `bson:"real_medicine_id" json:"realMedicineId"` // 实际发放药物
	Label          string              `json:"label" json:"label"`                     // 对应发药标签
	UseFormulas    *CustomerFormula    `bson:"use_formulas" json:"useFormulas"`
	DoseInfo       *DoseInfo           `bson:"dose_info" json:"doseInfo"`
	OpenSetting    int                 `bson:"open_setting" json:"openSetting"` // 发放类型 分类排序需要
	RegisterGroup  *[]string           `bson:"register_group" json:"register_group,omitempty"`
	DTP            *int                `bson:"dtp" json:"dtp,omitempty"`
	OrderOID       *primitive.ObjectID `bson:"order_oid" json:"orderOid,omitempty"`
}

// OthersDispensingMedicine 编号研究产品信息...
type OthersDispensingMedicine struct {
	MedicineID     primitive.ObjectID  `json:"medicineId" bson:"medicine_id"`         // 研究产品ID
	Name           string              `json:"name" bson:"name"`                      // 研究产品名称（存贮该字段是为了方便发药页面展示）
	ExpirationDate string              `json:"expirationDate" bson:"expiration_date"` // 有效期（存贮该字段是为了方便发药页面展示）
	BatchNumber    string              `json:"batchNumber" bson:"batch_number"`       // 批次号（存贮该字段是为了方便发药页面展示）
	PackageNumber  string              `json:"packageNumber" bson:"package_number"`
	Time           time.Duration       `json:"time" bson:"time"`                       // 具体研究产品操作时间
	Type           int                 `json:"type" bson:"type"`                       // 具体研究产品操作类型 1、首次 2、补发 3、替换 4 实际用药 5、访视外发药 6、取回 7 撤销 8 登记后可用 9 登记后作废 注：8、9为被登记的记录
	RealMedicineID primitive.ObjectID  `bson:"real_medicine_id" json:"realMedicineId"` // 实际发放药物
	Label          string              `json:"label" json:"label"`                     // 对应发药标签
	UseFormulas    *CustomerFormula    `bson:"use_formulas" json:"useFormulas"`
	DoseInfo       *DoseInfo           `bson:"dose_info" json:"doseInfo"`
	OpenSetting    int                 `bson:"open_setting" json:"openSetting"` // 发放类型 分类排序需要
	RegisterGroup  *[]string           `bson:"register_group" json:"register_group,omitempty"`
	DTP            *int                `bson:"dtp" json:"dtp,omitempty"`
	OrderOID       *primitive.ObjectID `bson:"order_oid" json:"orderOid,omitempty"`
}

// ReplaceOthersMedicines 已经替换编号研究产品信息...
type ReplaceOthersMedicines struct {
	MedicineID     primitive.ObjectID  `json:"medicineId" bson:"medicine_id"`         // 研究产品ID
	Name           string              `json:"name" bson:"name"`                      // 研究产品名称（存贮该字段是为了方便发药页面展示）
	ExpirationDate string              `json:"expirationDate" bson:"expiration_date"` // 有效期（存贮该字段是为了方便发药页面展示）
	BatchNumber    string              `json:"batchNumber" bson:"batch_number"`       // 批次号（存贮该字段是为了方便发药页面展示）
	MedicineNewID  primitive.ObjectID  `json:"medicineNewID" bson:"medicine_new_id"`  // 被替换研究产品ID
	NewNumber      string              `json:"newNumber" bson:"new_number"`           // 被替换研究产品号（存贮该字段是为了方便发药页面展示）
	Time           time.Duration       `json:"time" bson:"time"`                      // 被替换研究产品 初始发药时间
	OperationTime  time.Duration       `json:"operationTime" bson:"operation_time"`   // 操作时间
	Label          string              `json:"label" json:"label"`                    // 对应发药标签
	UseFormulas    *CustomerFormula    `bson:"use_formulas" json:"useFormulas"`
	DoseInfo       *DoseInfo           `bson:"dose_info" json:"doseInfo"`
	OpenSetting    int                 `bson:"open_setting" json:"openSetting"` // 发放类型 分类排序需要
	MatchID        primitive.ObjectID  `bson:"match_id"`
	DTP            *int                `bson:"dtp" json:"dtp,omitempty"`
	OrderOID       *primitive.ObjectID `bson:"order_oid" json:"orderOid,omitempty"`
}

// OtherDispensingMedicine 未编号研究产品信息
type OtherDispensingMedicine struct {
	ID              primitive.ObjectID  `json:"id" bson:"_id"`
	MedicineOtherID primitive.ObjectID  `json:"medicineOtherID" bson:"medicine_other_id"` // 研究产品ID
	Name            string              `json:"name"`                                     // 研究产品名称
	Count           int                 `json:"count"`                                    // 发药数量
	Batch           string              `json:"batch"`                                    // 批次号
	ExpireDate      string              `json:"expireDate" bson:"expire_date"`            // 有效期
	Time            time.Duration       `json:"time" bson:"time"`                         // 具体研究产品操作时间
	Type            int                 `json:"type" bson:"type"`                         // 具体研究产品操作类型 1、首次 2、补发 3、替换 4、实际发药
	BatchCount      []BatchCount        `json:"batchCount" bson:"batch_count"`            // 区分各批次发药数量，撤销时可用
	Label           string              `json:"label" json:"label"`                       // 对应发药标签
	UseFormulas     *CustomerFormula    `bson:"use_formulas" json:"useFormulas"`
	Level           *string             `bson:"level" json:"level"`
	DoseInfo        *DoseInfo           `bson:"dose_info" json:"doseInfo"`
	ReplaceCount    int                 `json:"replace_count"` // 替换发药数量
	DTP             *int                `bson:"dtp" json:"dtp,omitempty"`
	OrderOID        *primitive.ObjectID `bson:"order_oid" json:"orderOid,omitempty"`
}

// OtherDispensingMedicineInfo 未编号研究产品信息
type OtherDispensingMedicineInfo struct {
	ID              primitive.ObjectID       `json:"id" bson:"_id"`
	MedicineOtherID primitive.ObjectID       `json:"medicineOtherID" bson:"medicine_other_id"` // 研究产品ID
	Name            string                   `json:"name"`                                     // 研究产品名称
	Count           int                      `json:"count"`                                    // 发药数量
	Batch           string                   `json:"batch"`                                    // 批次号
	ExpireDate      string                   `json:"expireDate" bson:"expire_date"`            // 有效期
	Time            time.Duration            `json:"time" bson:"time"`                         // 具体研究产品操作时间
	Type            int                      `json:"type" bson:"type"`                         // 具体研究产品操作类型 1、首次 2、补发 3、替换 4 实际发药 5、访视外发药 6、取回 7登记作废 8 登记后可用
	BeInfo          *OtherDispensingMedicine `json:"beInfo" bson:"be_info"`                    // 被替换/登记其他药物的信息
	Label           string                   `json:"label" json:"label"`                       // 对应发药标签
	UseFormulas     *CustomerFormula         `bson:"use_formulas" json:"useFormulas"`
	RegisterGroup   *[]string                `bson:"register_group" json:"register_group,omitempty"`
	DoseInfo        *DoseInfo                `bson:"dose_info" json:"doseInfo"`
	DTP             *int                     `bson:"dtp" json:"dtp,omitempty"`
	OrderOID        *primitive.ObjectID      `bson:"order_oid" json:"orderOid,omitempty"`
}

// BatchCount 未编号研究产品信息
type BatchCount struct {
	Count int    `json:"count"` // 发药数量
	Batch string `json:"batch"` // 批次号
}

// RemoteSubjectDispensing 和EDC对接发药返回的参数
type RemoteSubjectDispensing struct {
	SubjectNo                string                    `json:"subjectNo"`
	DispensingMedicines      []DispensingMedicine      `json:"dispensingMedicines"`
	OtherDispensingMedicines []OtherDispensingMedicine `json:"otherDispensingMedicines"`
	DispensingTime           string                    `json:"randomTime"`
	TimeZone                 string                    `json:"timeZone"`          // 时区
	Timestamp                time.Duration             `json:"standardTimestamp"` // 标准时间戳
	VisitNo                  string                    `json:"visitNo"`
	InstanceRepeatNo         string                    `json:"instanceRepeatNo"`
	BlockRepeatNo            string                    `json:"blockRepeatNo"`
	Room                     string                    `json:"room"`
	Number                   string                    `json:"number"`
	SiteNo                   string                    `json:"siteNo"`
	StandardSiteName         string                    `json:"standardSiteName"`
}

// Reason ..
type Reason struct {
	ReasonID   primitive.ObjectID `json:"reasonId" bson:"reason_id"`     // 研究产品ID
	ReasonType int                `json:"reasonType" bson:"reason_type"` // 1、替换 2、撤销 3、访视外发药
	ReasonTime time.Duration      `json:"reasonTime" bson:"reason_time"` // 发药时间
	Reason     string             `json:"reason" bson:"reason"`          //原因
}

type ExportDispensing struct {
	Site      string               `bson:"site"`
	Name      string               `bson:"name"`
	Subject   string               `bson:"subject"`
	Visit     string               `bson:"visit"`
	Time      time.Duration        `bson:"time"`
	VisitSign bool                 `bson:"visit_sign"`
	Reissue   int32                `bson:"reissue"`
	medicine  []DispensingMedicine `bson:"medicine"`
}

type DispensingRoomRecord struct {
	ID                 primitive.ObjectID `bson:"_id"`
	CustomerID         primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID          primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID      primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID           primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	ProjectSiteID      primitive.ObjectID `bson:"project_site_id"`
	SubjectID          primitive.ObjectID `bson:"subject_id"`
	DispensingID       primitive.ObjectID `bson:"dispensing_id"`
	RandomNumber       string             `bson:"random_number"`
	Subject            string             `bson:"subject"`
	DispensingMedicine string             `bson:"dispensing_medicine"`
	Room               string             `bson:"room"`
	DispensingTime     string             `bson:"dispensing_time"`
	UserID             primitive.ObjectID `bson:"user_id"`   // 查看人ID
	UserInfo           UserInfo           `bson:"user_info"` // 查看人
	Time               time.Duration      `bson:"time"`
}

// ResDispensingInfo 返回订单信息
type ResDispensingInfo struct {
	Name       string `json:"name"`
	Number     string `json:"number"`
	Order      string `json:"order"`
	ExpireDate string `json:"expireDate"`
}

type ResDispensingListInfo struct {
	VisitOpen        string             `json:"visitOpen"`
	CanOutVisit      bool               `json:"canOutVisit"`
	CanReissue       bool               `json:"canReissue"`
	VisitCycleInfoId primitive.ObjectID `json:"visitCycleInfoId,omitempty"` // 当前补发、计划外用的ID
	ResDispensing    []ResDispensing    `json:"info"`
}

type ResDispensing struct {
	ID                           primitive.ObjectID            `json:"id" bson:"_id"`
	CustomerID                   primitive.ObjectID            `json:"customerId" bson:"customer_id"`
	ProjectID                    primitive.ObjectID            `json:"projectId" bson:"project_id"`
	EnvironmentID                primitive.ObjectID            `json:"envId" bson:"env_id"`
	CohortID                     primitive.ObjectID            `json:"cohortId" bson:"cohort_id"`
	SubjectID                    primitive.ObjectID            `json:"subjectId" bson:"subject_id"`
	VisitInfo                    VisitInfo                     `json:"visitInfo" bson:"visit_info"`                                         // 访视ID
	SerialNumber                 int                           `json:"serialNumber" bson:"serial_number"`                                   // 序号
	VisitSign                    bool                          `json:"visitSign" bson:"visit_sign"`                                         // 是否计划外访视标记(true是/false不是)
	DispensingMedicines          []DispensingMedicineRes       `json:"dispensingMedicines" bson:"dispensing_medicines"`                     // 编号研究产品
	OtherDispensingMedicines     []OtherDispensingMedicineRes  `json:"otherDispensingMedicines" bson:"other_dispensing_medicines"`          // 未编号研究产品
	OthersDispensingMedicines    []OthersDispensingMedicine    `json:"othersDispensingMedicines" bson:"others_dispensing_medicines"`        // 未编号研究产品
	RealDispensingMedicines      []DispensingMedicine          `json:"realDispensingMedicines" bson:"real_dispensing_medicines"`            // 实际发放药物
	Status                       int                           `json:"status"`                                                              // 状态 1待发药 2已发药 3作废
	Reissue                      int                           `json:"reissue"`                                                             // 是否补发 0非补发 1补发
	DispensingTime               time.Duration                 `json:"dispensingTime" bson:"dispensing_time"`                               // 发药时间
	Reasons                      []Reason                      `json:"reasons" bson:"reasons"`                                              // 替换，撤销原因
	ReplaceMedicines             []ReplaceMedicines            `json:"replaceMedicines" bson:"replace_medicines"`                           // 被替换研究产品
	Print                        int                           `bson:"print" json:"print"`                                                  // 是否已经打印
	Label                        string                        `bson:"label" json:"label"`                                                  // 对应发药标签
	Labels                       []string                      `bson:"labels" json:"labels"`                                                // DTP专用字段 对应多个标签
	Remark                       string                        `bson:"remark" json:"remark"`                                                // DTP专用字段 备注
	Order                        string                        `bson:"order" json:"order"`                                                  // 对应订单号
	CanRetrieval                 []DispensingMedicineRes       `json:"canRetrieval"`                                                        // 可以取回操作编号药物
	OtherCanRetrieval            []OtherDispensingMedicineRes  `json:"otherCanRetrieval"`                                                   // 可以取回的未编号药物
	MedicineOrder                []MedicineOrderDispensing     `bson:"medicine_order" json:"medicineOrder"`                                 // 对应订单号
	RealOtherDispensingMedicines []OtherDispensingMedicineInfo `json:"realOtherDispensingMedicines" bson:"real_other_dispensing_medicines"` // 实际发放药物
	CancelMedicinesHistory       []DispensingMedicine          `json:"cancelMedicinesHistory" bson:"cancel_medicines_history"`              // 取回数据记录
	OtherMedicinesHistory        []OtherDispensingMedicineInfo `json:"otherMedicinesHistory" bson:"other_medicines_history"`                // 未编号操作记录 （方便报表展示）
	ReplaceOtherMedicines        []OtherDispensingMedicineInfo `json:"replaceOtherMedicines" bson:"replace_other_medicines"`                // 被替换研究产品
	WorkTaskId                   primitive.ObjectID            `json:"workTaskId" bson:"work_task_id"`
	DoseInfo                     *DoseInfo                     `json:"doseInfo" bson:"dose_info"`
	Period                       `json:"period"`
	DTP                          bool   `json:"dtp"`
	CohortName                   string `json:"cohortName"`                          // cohort名称
	SubjectStatus                int    `json:"subjectStatus"`                       // 受试者状态(在随机项目才有)
	Tz                           string `json:"tz"`                                  // cohort名称
	StartVisit                   int    `json:"startVisit" bson:"start_visit"`       // 开启后续阶段访视标记
	DispensingTrailCount         int    `json:"dispensingTrailCount" bson:"history"` // 统计发药轨迹个数
	CanDispensing                bool   `json:"canDispensing"`                       // 允许发药 不参加
	CanRegister                  bool   `json:"canRegister"`                         // 允许发药
	CanReplace                   bool   `json:"canReplace"`                          // 允许替换
	Retrieval                    bool   `json:"retrieval"`                           // 允许取回
	CanResume                    bool   `json:"canResume"`                           // 允许恢复访视
	CanReissue                   bool   `json:"canReissue"`                          // 允许计划外、补发
	OutVisitStr                  string `json:"outVisitStr"`                         // 自定义计划外名称
	Random                       bool   `json:"random"`
}

// DispensingMedicineRes 编号研究产品信息...
type DispensingMedicineRes struct {
	DispensingMedicine `bson:",inline" json:",inline"`
	DTPs               []int ` json:"dtps,omitempty"`
	UnBlind            int   ` json:"unBlind"`
	UnReplace          bool  ` json:"unReplace"`
}

// OtherDispensingMedicineRes 未编号研究产品信息
type OtherDispensingMedicineRes struct {
	OtherDispensingMedicine `bson:",inline" json:",inline"`
	DTPs                    []int ` json:"dtps,omitempty"`
}

type Period struct {
	OutSize       bool   `json:"outSize"`
	OutSizeWindow bool   `json:"outSizeWindow"`
	MinPeriod     string `json:"minPeriod"`
	MaxPeriod     string `json:"maxPeriod"`
	LineTime      string `json:"lineTime"`
	MaximumTime   int64  `json:"maximumTime"`
}

// FormulaInfo 研究产品订单状态
type FormulaInfo struct {
	Weight        *float64        `json:"weight" bson:"weight"`
	Height        *float64        `json:"height" bson:"height"` // 上次实际体重
	Age           *string         `json:"age" bson:"age"`
	FormulaWeight []FormulaWeight `json:"formulaWeight" bson:"formula_weight"` //上次计算体重  各药物对应计算的实际体重
}

type FormulaWeight struct {
	Name   string   `bson:"name" json:"name"`
	Weight *float64 `bson:"weight" json:"weight"`
}

type ResAppDispensing struct {
	ID                           primitive.ObjectID            `json:"id" bson:"_id"`
	CustomerID                   primitive.ObjectID            `json:"customerId" bson:"customer_id"`
	ProjectID                    primitive.ObjectID            `json:"projectId" bson:"project_id"`
	EnvironmentID                primitive.ObjectID            `json:"envId" bson:"env_id"`
	CohortID                     primitive.ObjectID            `json:"cohortId" bson:"cohort_id"`
	SubjectID                    primitive.ObjectID            `json:"subjectId" bson:"subject_id"`
	VisitInfo                    VisitInfo                     `json:"visitInfo" bson:"visit_info"`                                // 访视ID
	SerialNumber                 int                           `json:"serialNumber" bson:"serial_number"`                          // 序号
	VisitSign                    bool                          `json:"visitSign" bson:"visit_sign"`                                // 是否计划外访视标记(true是/false不是)
	DispensingMedicines          []DispensingMedicine          `json:"dispensingMedicines" bson:"dispensing_medicines"`            // 编号研究产品
	OtherDispensingMedicines     []OtherDispensingMedicine     `json:"otherDispensingMedicines" bson:"other_dispensing_medicines"` // 未编号研究产品
	RealDispensingMedicines      []DispensingMedicine          `json:"realDispensingMedicines" bson:"real_dispensing_medicines"`   // 实际发放药物
	Status                       int                           `json:"status"`                                                     // 状态 1待发药 2已发药 3作废
	Reissue                      int                           `json:"reissue"`                                                    // 是否补发 0非补发 1补发
	DispensingTime               time.Duration                 `json:"dispensingTime" bson:"dispensing_time"`                      // 发药时间
	ApplyTime                    string                        `json:"applyTime" bson:"apply_time"`
	WindowTimeMin                string                        `json:"windowTimeMin" bson:"window_time_min"`
	WindowTimeMax                string                        `json:"windowTimeMax" bson:"window_time_max"`
	Reasons                      []Reason                      `json:"reasons" bson:"reasons"`                                              // 替换，撤销原因
	ReplaceMedicines             []ReplaceMedicines            `json:"replaceMedicines" bson:"replace_medicines"`                           // 被替换研究产品
	Print                        int                           `bson:"print" json:"print"`                                                  // 是否已经打印
	Label                        string                        `bson:"label" json:"label"`                                                  // 对应发药标签
	Labels                       []string                      `bson:"labels" json:"labels"`                                                // DTP专用字段 对应多个标签
	Remark                       string                        `bson:"remark" json:"remark"`                                                // DTP专用字段 备注
	Order                        string                        `bson:"order" json:"order"`                                                  // 对应订单号
	CanRetrieval                 []DispensingMedicine          `json:"canRetrieval"`                                                        // 可以取回操作编号药物
	OtherCanRetrieval            []OtherDispensingMedicine     `json:"otherCanRetrieval"`                                                   // 可以取回的未编号药物
	MedicineOrder                []MedicineOrderDispensing     `bson:"medicine_order" json:"medicineOrder"`                                 // 对应订单号
	RealOtherDispensingMedicines []OtherDispensingMedicineInfo `json:"realOtherDispensingMedicines" bson:"real_other_dispensing_medicines"` // 实际发放药物
	CancelMedicinesHistory       []DispensingMedicine          `json:"cancelMedicinesHistory" bson:"cancel_medicines_history"`              // 取回数据记录
	OtherMedicinesHistory        []OtherDispensingMedicineInfo `json:"otherMedicinesHistory" bson:"other_medicines_history"`                // 未编号操作记录 （方便报表展示）
	ReplaceOtherMedicines        []OtherDispensingMedicineInfo `json:"replaceOtherMedicines" bson:"replace_other_medicines"`
	ScanStatus                   int                           `json:"scanStatus" bson:"scan_status"` //确认扫码状态 0"未确认" 1“已确认”             // 发药时间
	WorkTaskId                   primitive.ObjectID            `json:"workTaskId" bson:"work_task_id"`
	DTP                          []int                         `json:"dtp"`
	CanVisitSign                 bool                          `json:"canVisitSign"`
	CanVisitSignStr              string                        `json:"canVisitSignStr"`
	CanReissue                   bool                          `json:"canReissue"`
	FormulaOperation             bool                          `json:"formulaOperation"`
	CanGrant                     bool                          `json:"canGrant"`                             //app发药是否显示发放按钮
	PlanLeftTime                 string                        `json:"planLeftTime" bson:"plan_left_time"`   // 期望完成时间-前区间
	PlanRightTime                string                        `json:"planRightTime" bson:"plan_right_time"` // 期望完成时间-后区间
	OutSize                      bool                          `json:"outSize" bson:"out_size"`              // 是否超窗
}

// MedicineOrderDispensing 研究产品订单状态
type MedicineOrderDispensing struct {
	ID                primitive.ObjectID   `json:"id" bson:"_id"`
	OrderNumber       string               `json:"orderNumber" bson:"order_number"`
	Medicines         []primitive.ObjectID `json:"medicines" bson:"medicines"`
	OtherMedicines    []OtherMedicineCount `json:"otherMedicines" bson:"other_medicines"`
	OtherMedicinesNew []primitive.ObjectID `json:"otherMedicinesNew" bson:"other_medicines_new"` //未编号重构之后字段
	//6:待确认、1:已请求、2:运送中、3:已收到、4:已丢失、5:已取消  7： 已申请
	Status int `json:"status" bson:"status"`
}

// SaltMedicine GetReissueMedicineName 响应接口
type SaltMedicine struct {
	ID       primitive.ObjectID `json:"id"`
	Key      int                `json:"key"`
	Salt     *string            `json:"salt"`
	SaltName *string            `json:"saltName"`
	Name     string             `json:"name"`
	Spec     string             `json:"spec"`
	IsOther  bool               `json:"is_other"`
	Max      *int               `json:"max"`
	DTP      *[]int             `json:"dtp"`
	DTPS     *[]int             `json:"dtps"` //app字段 后续需兼容
}
type RepMedicine struct {
	VisitName     string         `json:"visit_name"`
	SaltMedicines []SaltMedicine `json:"medicine_name"`
	DeleteDose    string         `json:"delete_dose"`
}

type ParamRealDispensing struct {
	SubjectID  primitive.ObjectID `json:"subject_id" binding:"required"`
	VisitID    primitive.ObjectID `json:"visit_id" binding:"required"`
	Number     primitive.ObjectID `json:"number" binding:"required"`
	RealNumber *string            `json:"real_number"`
	Status     *int               `json:"status" binding:"required"`
	Name       *string            `json:"name"`
	From       *string            `json:"from"`
	Batch      *string            `json:"batch"`
	Count      *int               `json:"count"`
	Expiration *string            `json:"expiration"`
	Remark     string             `json:"remark"`
	BeOther    *BeOther           `json:"be_other"`
}

type BeOther struct {
	Name           string `json:"name"`
	Batch          string `json:"batch"`
	ExpirationDate string `json:"expiration_date"`
}

type OpenDrug struct {
	MedicineInfo []interface{}
	Subject
	ExpireDateKey            map[string]interface{}
	SendType                 int
	OtherMedicineCount       *[]primitive.ObjectID
	OtherDispensingMedicine  *[]OtherDispensingMedicine
	OthersDispensingMedicine *[]OthersDispensingMedicine
	DispensingMedicine       *[]DispensingMedicine
	Attribute
	UserName  string
	VisitName string
	VisitSign bool
	User
	MedicineNumber        *[]string
	SiteOID               primitive.ObjectID
	Histories             *[]History
	Now                   time.Duration
	DispensingType        int
	DispensingAlarmNumber map[string]int32
	DoseInfo              *DoseInfo
	AllDrugMap            map[string]bool
	OldOpenProject        bool
	SiteOrderOID          primitive.ObjectID
	StoreOrderOID         primitive.ObjectID
	StoreOID              primitive.ObjectID
	AlarmCapacityInfoData map[int]map[string]WarnCapacityActual
	NameBatch             map[string]string
	LastDate              string
}

type RepFormula struct {
	Weight        bool           `json:"weight"`
	WeightValue   *float64       `json:"weight_value"`
	Height        bool           `json:"height"`
	HeightValue   *float64       `json:"height_value"`
	Age           bool           `json:"age"`
	AgeValue      *string        `json:"age_value"`
	SaltMedicines []SaltMedicine `json:"medicine_name"`
}

// FormulaMedicineRes ...
type FormulaMedicineRes struct {
	//系统建议 {count} {spec}  (单位研究产品：{specifications}{unit}/{spec})，实际用量 {number}{unit} 。
	//较{comparisonType}，变化> {radio}% ，本次计算体重为{currentComparisonType}。
	Count                 float64  `json:"count"`
	Number                int      `json:"number"`
	ActualWeight          float64  `json:"actualWeight"`
	Radio                 *float64 `json:"radio"`
	ComparisonSymbols     int      `json:"comparisonSymbols"`
	Specifications        float64  `json:"specifications"`
	Unit                  string   `json:"unit"`
	Spec                  string   `json:"spec"`
	ComparisonType        *int     `json:"comparisonType"`
	CurrentComparisonType *int     `json:"currentComparisonType"`
	ShowComparisonType    bool     `json:"showComparisonType"`
	CalculationType       int      `json:"calculationType"`
	IsOther               bool     `json:"is_other"`
	OutSize               bool     `json:"out_size"`
	UseFormula            *string  `json:"useFormula"`
}

type FormulaMedicineInfoRes struct {
	FormulaMedicineRes         []FormulaMedicineRes         `json:"formulaMedicineRes"`
	CustomerFormulaMedicineRes []CustomerFormulaMedicineRes `json:"customerFormulaMedicineRes"`
	//CustomerFormula            *float64                     `json:"customerFormula"`
	//Spec                       []string                     `json:"spec"`
	//Unit                       string                       `json:"unit"`
}

type CustomerFormulaMedicineRes struct {
	CustomerFormula *float64           `json:"customerFormula"`
	UseFormula      string             `json:"useFormula"`
	Spec            []string           `json:"spec"`
	Name            []NameType         `json:"name"`
	Unit            string             `json:"unit"`
	ConfigID        primitive.ObjectID `json:"configId"`
}
type NameType struct {
	Type    int    `json:"type"`
	OutSize bool   `json:"outSize"`
	Number  *int   `json:"number"`
	Name    string `json:"name"`
	ID      string `json:"id"`
}

type DispensingAlarmNumber struct {
}

type FormulaReq struct {
	SubjectID     primitive.ObjectID `json:"subject_id" bson:"subject_id"`
	VisitID       primitive.ObjectID `json:"visit_id" bson:"visit_id"`
	EnvironmentID primitive.ObjectID `json:"env_id" bson:"env_id"`
	CohortID      primitive.ObjectID `json:"cohort_id" bson:"cohort_id"`
	Age           *string            `json:"age"`
	Height        *float64           `json:"height"`
	Weight        *float64           `json:"weight"`
	Form          map[string]float64 `json:"form"`
}

type GetAppAddDispensingOperationDTPREQ struct {
	Operation bool  `json:"operation"`
	DTP       []int `json:"dtp"`
}

type CustomerReportTitle struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID    primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID      primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	Title         []string           `json:"title" bson:"title"` //  自定义表头
}

type ReqDose struct {
	SubjectID primitive.ObjectID `json:"subjectId"`
	VisitID   primitive.ObjectID `json:"visitId"`
	Value     string             `json:"value"`
}

type RepDoseInfo struct {
	Field        *Field       `json:"field,omitempty" `
	NoFrequency  bool         `json:"noFrequency"`
	InheritValue *string      `json:"inheritValue"`         // 继承上一次
	Label        []LabelValue `json:"label"`                //标签
	Name         []LabelValue `json:"name"`                 // 药物名称
	LevelTip     *LevelTip    `json:"levelTip,omitempty"`   // 上一次水平 当前水平
	IsLevel      bool         `json:"isLevel,omitempty"`    // 是否开启水平
	IsPage       bool         `json:"isPage,omitempty"`     // 是否开启包装
	OnlyInit     bool         `json:"onlyInit,omitempty"`   // 是否只开启初始
	InitOption   string       `json:"initOption,omitempty"` // 是否只开启初始
}

type LevelTip struct {
	Last    string `json:"last"`
	Current string `json:"current"`
}

type LabelValue struct {
	ID    primitive.ObjectID `json:"id"`
	Label string             `json:"label"`
	Value int                `json:"value"`
}
type FormValue struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type GroupMedicine struct {
	Name           string         `bson:"name"`
	PackageNumber  string         `bson:"package_number"`
	ExpirationDate string         `bson:"expiration_date"`
	AvailableCount int            `bson:"availableCount"`
	Medicines      []PushMedicine `bson:"medicines"`
}

type PushMedicine struct {
	ID           primitive.ObjectID `bson:"_id"`
	SerialNumber string             `bson:"serial_number"`
	Name         string             `bson:"name"`
	Status       int                `bson:"status"`
	SubjectID    primitive.ObjectID `bson:"subject_id"`
}

type GroupMedicineUnwind struct {
	GroupMedicine `json:",inline" bson:",inline"`
	Medicines     PushMedicine `bson:"medicines"`
}

type ReqDispensingConfirmTable struct {
	EnvironmentID                 primitive.ObjectID              `json:"envId"`
	CohortID                      primitive.ObjectID              `json:"cohortId" `
	RoleID                        primitive.ObjectID              `json:"roleId"`
	SubjectID                     primitive.ObjectID              `json:"subjectId"`
	VisitID                       primitive.ObjectID              `json:"visitId"`
	LevelOption                   string                          `json:"levelOption"`
	DispensingID                  primitive.ObjectID              `json:"dispensingId"`
	ReqDispensingConfirmTableInfo []ReqDispensingConfirmTableInfo `json:"info"`
}

type ReqDispensingConfirmTableInfo struct {
	Count  *int               `json:"count"`
	Label  string             `json:"name"`
	IPName string             `json:"ipName"`
	Spec   string             `json:"spec"`
	Level  string             `json:"level"`
	ID     primitive.ObjectID `json:"id"`
}

type ReportDispensing struct {
	ID                           primitive.ObjectID            `bson:"_id"`
	EnvID                        primitive.ObjectID            `bson:"env_id"`
	CohortID                     primitive.ObjectID            `bson:"cohort_id"`
	Country                      string                        `bson:"country"`
	SerialNumber                 int                           `bson:"serial_number"`
	Site                         string                        `bson:"site"`
	Name                         string                        `bson:"name"`
	TimeZone                     string                        `bson:"time_zone"`
	TZ                           string                        `bson:"tz"`
	Status                       int                           `bson:"status"`
	Subject                      string                        `bson:"subject"`
	Group                        string                        `bson:"group"`
	RegisterGroup                string                        `bson:"register_group"`
	RandomNumber                 string                        `bson:"random_number"`
	Visit                        string                        `bson:"visit"`
	Time                         time.Duration                 `bson:"time"`
	VisitSign                    bool                          `bson:"visit_sign"` // 假设 visit_sign 是布尔值
	Reissue                      int                           `bson:"reissue"`    // 假设 reissue 是布尔值
	Medicine                     []DispensingMedicine          `bson:"medicine"`
	RealMedicine                 []DispensingMedicine          `bson:"realMedicine"`
	ReplaceMedicines             []ReplaceMedicines            ` bson:"replace_medicines"`               // 被替换研究产品
	ReplaceOtherMedicines        []OtherDispensingMedicineInfo ` bson:"replace_other_medicines"`         // 被替换研究产品
	OtherDispensingMedicines     []OtherDispensingMedicine     ` bson:"other_dispensing_medicines"`      // 未编号研究产品
	CancelMedicinesHistory       []DispensingMedicine          ` bson:"cancel_medicines_history"`        // 未编号研究产品
	RealDispensingMedicines      []DispensingMedicine          ` bson:"real_dispensing_medicines"`       // 实际发放药物
	RealOtherDispensingMedicines []OtherDispensingMedicineInfo ` bson:"real_other_dispensing_medicines"` // 实际发放药物
	Form                         []CustomerFormula             `bson:"form"`
	Room                         string                        `bson:"room"`
	Medicines                    DispensingMedicineAndOther    `bson:"medicines"`
	History                      []History                     `bson:"history"`
	Dispensing                   primitive.ObjectID            `bson:"dispensing"`
	Region                       string                        `bson:"region"`
	Label                        string                        `bson:"label"`
	Labels                       []string                      `bson:"labels"`
	FormulaInfo                  *FormulaInfo                  `bson:"formula_info"`
	DoseInfo                     *DoseLevel                    `bson:"dose_info"`
	DoseForm                     *FormValue                    `bson:"dose_form"`
	DispensingID                 primitive.ObjectID            `bson:"dispensing_id"`
	Order                        OrderDispensing               `bson:"order"`
}

type OrderDispensing struct {
	LogisticsInfo `json:"logisticsInfo" bson:"logistics_info"` //物流信息
}

// DispensingMedicineAndOther 编号研究产品信息...
type DispensingMedicineAndOther struct {
	MedicineID      primitive.ObjectID `json:"medicineId" bson:"medicine_id"`         // 研究产品ID
	Name            string             `json:"name" bson:"name"`                      // 研究产品名称（存贮该字段是为了方便发药页面展示）
	Number          string             `json:"number" bson:"number"`                  // 研究产品号（存贮该字段是为了方便发药页面展示）
	ShortCode       string             `json:"shortCode" bson:"short_code"`           //app对接新增短码字段
	ExpirationDate  string             `json:"expirationDate" bson:"expiration_date"` // 有效期（存贮该字段是为了方便发药页面展示）
	BatchNumber     string             `json:"batchNumber" bson:"batch_number"`       // 批次号（存贮该字段是为了方便发药页面展示）
	PackageNumber   string             `json:"packageNumber" bson:"package_number"`
	Time            time.Duration      `json:"time" bson:"time"`                       // 具体研究产品操作时间
	Type            int                `json:"type" bson:"type"`                       // 具体研究产品操作类型 1、首次 2、补发 3、替换 4 实际用药 5、访视外发药 6、取回 7 撤销 8 登记后可用 9 登记后作废 注：8、9为被登记的记录
	RealMedicineID  primitive.ObjectID `bson:"real_medicine_id" json:"realMedicineId"` // 实际发放药物
	Label           string             `json:"label" json:"label"`                     // 对应发药标签
	UseFormulas     *CustomerFormula   `bson:"use_formulas" json:"useFormulas"`
	DoseInfo        *DoseInfo          `bson:"dose_info" json:"doseInfo"`
	ID              primitive.ObjectID `json:"id" bson:"_id"`
	MedicineOtherID primitive.ObjectID `json:"medicineOtherID" bson:"medicine_other_id"` // 研究产品ID
	Count           int                `json:"count"`                                    // 发药数量
	Batch           string             `json:"batch"`                                    // 批次号
	ExpireDate      string             `json:"expireDate" bson:"expire_date"`            // 有效期
	BatchCount      []BatchCount       `json:"batchCount" bson:"batch_count"`            // 区分各批次发药数量，撤销时可用
	Level           *string            `bson:"level" json:"level"`
	// 替换
	ReplaceCount  int                      `json:"replace_count"`                        // 替换发药数量
	BeInfo        *OtherDispensingMedicine `json:"beInfo" bson:"be_info"`                // 被替换/登记其他药物的信息
	MedicineNewID primitive.ObjectID       `json:"medicineNewID" bson:"medicine_new_id"` // 被替换研究产品ID
	NewNumber     string                   `json:"newNumber" bson:"new_number"`          // 被替换研究产品号（存贮该字段是为了方便发药页面展示）
	OperationTime time.Duration            `json:"operationTime" bson:"operation_time"`  // 操作时间
	RegisterGroup []string                 `bson:"register_group" json:"register_group,omitempty"`
	DTP           int                      `bson:"dtp" json:"dtp,omitempty"`
	Key           string                   `json:"key" bson:"key"`
	Data          map[string]interface{}   `json:"data" bson:"data"`
	UID           primitive.ObjectID       `json:"uid" bson:"uid"`
	User          string                   `json:"user" bson:"user"`
	OrderOID      *primitive.ObjectID      `bson:"order_oid" json:"orderOid,omitempty"`
}

type GetWebLabelMedicineRes struct {
	VisitID  primitive.ObjectID    `json:"visitId"`
	Name     string                `json:"visitName"`
	Label    []WebLabelMedicineRes `json:"label"`
	Medicine []WebMedicineRes      `json:"medicine"`
}

type GetLabelMedicineRes struct {
	VisitID  primitive.ObjectID `json:"visitId"`
	Name     string             `json:"visitName"`
	Label    []LabelMedicineRes `json:"label"`
	Medicine []LabelMedicineRes `json:"medicine"`
}

type GetAppLabelMedicineRes struct {
	VisitID   primitive.ObjectID    `json:"visitId"`
	Name      string                `json:"visitName"`
	Labels    []LabelMedicineResult `json:"labels"`
	Medicines []LabelMedicineResult `json:"medicines"`
}

type LabelMedicineRes struct {
	ID          primitive.ObjectID `json:"id,omitempty"`
	Label       string             `json:"label"`
	OpenSetting int                `json:"open_setting,omitempty" `
	Info        SpecCount          `json:"info"`
	DTP         []int              `json:"dtps"`
}

type LabelMedicineResult struct {
	Formkey []string           `json:"formkey"`
	Label   []LabelMedicineRes `json:"label"`
}

type LabelMedicineMiddle struct {
	ID          primitive.ObjectID `json:"id,omitempty"`
	Label       string             `json:"label"`
	OpenSetting int                `json:"open_setting,omitempty" `
	Info        SpecCount          `json:"info"`
	DTP         []int              `json:"dtps"`
	Formkey     []string           `json:"formkey"`
	DefaultShow bool               `json:"defaultShow"`
}

type SpecCount struct {
	Spec                   string  `json:"spec"`
	Count                  []int   `json:"count,omitempty"`                    // app使用  同custom_dispensing_number
	CustomDispensingNumber []int   `json:"custom_dispensing_number,omitempty"` // web使用
	IsOther                bool    `json:"is_other,omitempty"`
	Salt                   *string `json:"salt,omitempty"`
	SaltName               *string `json:"saltName,omitempty"`
	AutomaticRecode        bool    `json:"automaticRecode"`
}

type WebLabelMedicineRes struct {
	ID                  primitive.ObjectID     `json:"id,omitempty"`
	Index               int                    `json:"index"`
	OpenSetting         int                    `json:"openSetting,omitempty"`
	CustomerCalculation string                 `json:"customerCalculation,omitempty"`
	Name                string                 `json:"name,omitempty"`
	DTP                 []int                  `json:"dtp,omitempty"`
	Values              *WebLabelMedicineValue `json:"values,omitempty"`
	DefaultShow         bool                   `json:"defaultShow"`
}
type WebLabelMedicineValue struct { // 标签
	SpecCount `json:",inline"`
	Label     string `json:"label"`
}

type WebMedicineRes struct { // 开放药物
	ID                  primitive.ObjectID `json:"id,omitempty"`
	Key                 int                `json:"key"`
	Name                string             `json:"name"`
	CustomerCalculation string             `json:"customerCalculation"`
	DTP                 []int              `json:"dtp"`
	Max                 int                `json:"max"`
	DefaultShow         bool               `json:"defaultShow"`
	SpecCount           `json:",inline"`
}

//type SpecCount struct {
//	Spec     string  `json:"spec"`
//	Count    []int   `json:"count"`
//	IsOther  bool    `json:"is_other,omitempty"`
//	Salt     *string `json:"salt,omitempty"`
//	SaltName *string `json:"saltName,omitempty"`
//}

type MailDispensingInfo struct {
	ID            primitive.ObjectID `bson:"_id"`
	CustomerID    primitive.ObjectID `bson:"customer_id"`
	ProjectID     primitive.ObjectID `bson:"project_id"`
	EnvironmentID primitive.ObjectID `bson:"env_id"`
	CohortID      primitive.ObjectID `bson:"cohort_id"`
	RandomListID  primitive.ObjectID `bson:"random_list_id"`
	SiteID        primitive.ObjectID `bson:"site_id"`
	Name          string             `bson:"name"`
	NameEn        string             `bson:"nameEn"`
	Number        string             `bson:"number"`
	Subject       string             `bson:"subject"`
	Group         string             `bson:"group"`
	ParGroupName  string             `bson:"par_group_name"`
	SubGroupName  string             `bson:"sub_group_name"`
	RandomNumber  string             `bson:"random_number"`
	SubGroupBlind bool               `bson:"sub_group_blind"`
}
