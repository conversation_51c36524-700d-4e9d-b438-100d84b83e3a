package models

import (
	"clinflash-irt/locales"
	"fmt"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	OperationLogMap = bson.M{
		"operation_type": bson.M{
			"1":  "operation_log.add",
			"2":  "operation_log.edit",
			"3":  "operation_log.delete",
			"4":  "operation_log.run",
			"5":  "operation_log.edit",
			"6":  "operation_log.copy",
			"7":  "operation_log.unbind",
			"8":  "operation_log.reauthorization",
			"9":  "operation_log.export",
			"10": "operation_log.project_copy",
			"11": "operation_log.cohort_copy",
			"12": "operation_log.invite_again",
			"13": "operation_log.close",
			"14": "operation_log.setting",
			"15": "operation_log.cancel",
			"16": "operation_log.activate",
			"17": "operation_log.inactivate",
		}, // 编码规则
		"operation_log.barcode.random": bson.M{
			"0": "operation_log.barcode.manual",
			"1": "operation_log.barcode.auto"}, // 编码规则
		"operation_log.supply_detail.auto_supply_size": bson.M{
			"0": "operation_log.supply_detail.na",
			"1": "operation_log.supply_detail.buffer",
			"2": "operation_log.supply_detail.second_supply",
			"3": "operation_log.supply_detail.forecast",
		}, // 自动配药量
		"operation_log.supply_detail.supply_mode_key": bson.M{
			"0": "",
			"1": "operation_log.supply_detail.supply_mode.all_supply",
			"2": "operation_log.supply_detail.supply_mode.single",
			"3": "operation_log.supply_detail.supply_mode.all_one",
			"4": "operation_log.supply_detail.supply_mode.single_one"}, // 自动配药量
		"operation_log.random_design.type": bson.M{
			"0": "",
			"1": "operation_log.random_design.block",
			"2": "operation_log.random_design.min"}, // 随机类型
		"operation_log.random_design.factor.layer": bson.M{
			"0": "operation_log.random_design.factor.disable",
			"1": "operation_log.random_design.factor.site",
			"2": "operation_log.random_design.factor.country",
			"3": "operation_log.random_design.factor.region"}, // 地区分层
		"operation_log.random_design.factor.list": bson.M{
			"0": "operation_log.random_design.factor.disable",
			"1": "operation_log.random_list.set_site",
			"2": "operation_log.random_list.set_country",
			"3": "operation_log.random_list.set_region"}, // 地区分层
		"operation_log.random_list.status": bson.M{
			"1": "operation_log.random_list.enable",
			"2": "operation_log.random_list.disable",
			"3": "operation_log.random_list.invalid"}, // 随机列表状态
		"operation_log.random_list.set_site": bson.M{
			"0": "operation_log.random_list.dept"}, // 自动配药量
		"operation_log.project_storehouse.supplier": bson.M{
			"shengsheng": "operation_log.supplierType.shengsheng",
			"catalent":   "operation_log.supplierType.catalent",
			"baicheng":   "operation_log.supplierType.baicheng",
			"eDRUG":      "operation_log.supplierType.eDrug"}, //物流供应商
		"operation_log.project_site.status": bson.M{
			"2": "operation_log.projectSiteStatus.valid",
			"1": "operation_log.projectSiteStatus.invalid"}, //中心状态
		"operation_log.project_multi_language.sharedSystemLibrary": bson.M{
			"2": "operation_log.projectSiteActive.open",
			"1": "operation_log.projectSiteActive.close"}, //自动订单供应
		"operation_log.form.type": bson.M{
			"input":       "operation_log.form.input",
			"inputNumber": "operation_log.form.inputNumber",
			"textArea":    "operation_log.form.textArea",
			"select":      "operation_log.form.select",
			"checkbox":    "operation_log.form.checkbox",
			"radio":       "operation_log.form.radio",
			"switch":      "operation_log.form.checkbox",
			"datePicker":  "operation_log.form.datePicker",
			"timePicker":  "operation_log.form.timePicker",
		}, //物流供应商
		"operation_log.attribute.random": bson.M{
			"true":  "operation_log.attribute.random_true",
			"false": "operation_log.attribute.random_false",
		}, //随机化
		"operation_log.attribute.isRandomNumber": bson.M{
			"true":  "operation_log.attribute.isRandomNumber_true",
			"false": "operation_log.attribute.isRandomNumber_false",
		}, //随机号展示
		"operation_log.attribute.isRandomSequenceNumber": bson.M{
			"true":  "operation_log.attribute.isRandomSequenceNumber_true",
			"false": "operation_log.attribute.isRandomSequenceNumber_false",
		}, //随机顺序号号展示
		"operation_log.attribute.dispensing": bson.M{
			"true":  "operation_log.attribute.dispensing_true",
			"false": "operation_log.attribute.dispensing_false",
		}, //发药设计
		"operation_log.attribute.blind": bson.M{
			"true":  "operation_log.attribute.blind_true",
			"false": "operation_log.attribute.blind_false",
		}, //盲法
		"operation_log.attribute.minimize_calc": bson.M{
			"0": "operation_log.attribute.minimize_calc_factor",
			"1": "operation_log.attribute.minimize_calc_actual",
			"2": "operation_log.attribute.notApplicable",
		}, //最小化随机计算
		"operation_log.attribute.prefix": bson.M{
			"true":  "operation_log.attribute.prefix_true",
			"false": "operation_log.attribute.prefix_false",
		}, //受试者前缀
		"operation_log.attribute.subject_number_rule": bson.M{
			"1": "operation_log.attribute.subject_number_rule1",
			"2": "operation_log.attribute.subject_number_rule2",
			"3": "operation_log.attribute.subject_number_rule3",
		}, //受试者号录入规则
		"operation_log.attribute.randomControlRule": bson.M{
			"1": "operation_log.attribute.randomControl1",
			"2": "operation_log.attribute.randomControl2",
			"3": "operation_log.attribute.randomControl3",
		}, //随机研究产品供应核查
		"operation_log.attribute.accuracy": bson.M{
			"1": "operation_log.attribute.accuracy_le",
			"2": "operation_log.attribute.accuracy_eq",
		}, //受试者号位数精确值
		"operation_log.random_design.factor.type": bson.M{
			"radio":      "operation_log.form.radio",
			"select":     "operation_log.form.select",
			"datePicker": "operation_log.form.datePicker",
			"input":      "operation_log.form.input",
		}, // 分层选项
		"operation_log.updateBatch.status": bson.M{
			"0":  "operation_log.updateBatch.toBeWarehoused",
			"1":  "operation_log.updateBatch.available",
			"2":  "operation_log.updateBatch.delivered",
			"3":  "operation_log.updateBatch.transit",
			"4":  "operation_log.updateBatch.quarantine",
			"5":  "operation_log.updateBatch.used",
			"6":  "operation_log.updateBatch.lose",
			"7":  "operation_log.updateBatch.expired",
			"11": "operation_log.updateBatch.InOrder",
			"12": "operation_log.updateBatch.stockPending",
			"13": "operation_log.updateBatch.apply",
			"14": "operation_log.updateBatch.frozen",
			"15": "operation_log.updateBatch.approval",
			"20": "operation_log.updateBatch.lock",
		}, //批次更新状态
		"operation_log.workTask.label": bson.M{
			"add": "operation_log.workTask.add",
		}, //扫码入参
		"operation_log.supply.status": bson.M{
			"1": "operation_log.supply.status_effective",
			"0": "operation_log.supply.status_invalid",
		}, //供应计划状态
		"operation_log.form.status": bson.M{
			"1": "operation_log.form.status_effective",
			"2": "operation_log.form.status_invalid",
		}, //表单配置字段状态
		"operation_log.project.status": bson.M{
			"0": "operation_log.project.progress",
			"1": "operation_log.project.finish",
			"2": "operation_log.project.close",
			"3": "operation_log.project.pause",
			"4": "operation_log.project.terminate",
		}, //项目状态
		"operation_log.project.orderCheck": bson.M{
			"1": "operation_log.project.timing",
			"2": "operation_log.project.realTime",
			"3": "operation_log.project.notApplicable",
		}, //订单核查
		"operation_log.project.pushMode": bson.M{
			"1": "operation_log.project.real",
			"2": "operation_log.project.active",
		}, //同步EDC数据推送方式
		"operation_log.project.pushRules": bson.M{
			"1": "operation_log.project.subjectNumber",
			"2": "operation_log.project.subjectUID",
		}, //同步EDC推送规则
		"operation_log.project.synchronizationMode": bson.M{
			"1": "operation_log.project.stepBy",
			"2": "operation_log.project.timeFull",
		}, //同步EDC同步方式
		"operation_log.project.lockStatus": bson.M{
			"0": "operation_log.project.unlock",
			"1": "operation_log.project.locked",
		}, //项目环境锁定状态
		"operation_log.project.roleStatus": bson.M{
			"1": "operation_log.projectSiteStatus.valid",
			"2": "operation_log.projectSiteStatus.invalid",
		}, //项目角色状态
		"operation_log.projectUser.status": bson.M{
			"0": "operation_log.projectUser.status_invalid",
			"1": "operation_log.projectUser.status_effective",
		}, //人员管理状态
		"operation_log.project.cohortStatus": bson.M{
			"1": "operation_log.project.draft",
			"2": "operation_log.project.enrollment",
			"3": "operation_log.project.complete",
			"4": "operation_log.project.stop",
			"5": "operation_log.project.enrollmentFull",
		}, //cohort入组状态
		"operation_log.random_list.block_rule": bson.M{
			"0": "operation_log.random_list.block_rule_order",
			"1": "operation_log.random_list.block_rule_reverse",
		}, //区组规则 0顺序 1乱序
		"operation_log.random_list.random_number_rule": bson.M{
			"0": "operation_log.random_list.random_number_rule_order",
			"1": "operation_log.random_list.random_number_rule_reverse",
		}, //随机号规则 0顺序 1乱序
		"operation_log.random_design.factor.status": bson.M{
			"1": "operation_log.random_design.factor.status_effective",
			"2": "operation_log.random_design.factor.status_invalid",
		}, //分层因素字段状态
		"operation_log.random_design.factor.calcType": bson.M{
			"0": "operation_log.random_design.factor.calcType_age",
			"1": "operation_log.random_design.factor.calcType_bmi",
		}, //分层因素字段状态
		"operation_log.visitCycle.type": bson.M{
			"0": "operation_log.visitCycle.baseline",
			"1": "operation_log.visitCycle.lastdate",
		}, //访视类型
		"operation_log.visitCycle.sop": bson.M{
			"1": "operation_log.visitCycle.push",
		}, //访视类型
		//  开放配置
		"operation_log.drug_configure.open": bson.M{
			"1": "operation_log.visitCycle.label",
			"2": "operation_log.visitCycle.open_setting",
			"3": "operation_log.visitCycle.formula",
		}, //访视类型
		"operation_log.drug_configure.otherCheck": bson.M{
			"true":  "operation_log.drug_configure.check",
			"false": "operation_log.drug_configure.uncheck",
		}, //未编号研究产品
		"operation_log.drug_configure.openCheck": bson.M{
			"true":  "operation_log.drug_configure.check",
			"false": "operation_log.drug_configure.uncheck",
		}, //开放研究产品
		"operation_log.drug_configure.keepDecimal": bson.M{
			"true":  "operation_log.drug_configure.check",
			"false": "operation_log.drug_configure.uncheck",
		}, //保留小数位
		"operation_log.drug_configure.comparisonSwitch": bson.M{
			"true":  "operation_log.drug_configure.calculationOpen",
			"false": "operation_log.drug_configure.calculationUnOpen",
		}, //体重比较计算
		"operation_log.drug_configure.calculationType": bson.M{
			"1": "operation_log.drug_configure.age",
			"2": "operation_log.drug_configure.weight",
			"3": "operation_log.drug_configure.bsa",
			"4": "operation_log.drug_configure.otherBsa",
		}, //方式
		"operation_log.drug_configure.comparisonType": bson.M{
			"1": "operation_log.drug_configure.weightCalculation",
			"2": "operation_log.drug_configure.weightActual",
			"3": "operation_log.drug_configure.weightRandom",
		}, //上一次体重
		"operation_log.drug_configure.currentComparisonType": bson.M{
			"1": "operation_log.drug_configure.weightCalculation",
			"2": "operation_log.drug_configure.weightActual",
			"3": "operation_log.drug_configure.weightRandom",
		}, //本地计算体重为：
		"operation_log.visitCycle.DTPMode": bson.M{
			"0": "operation_log.visitCycle.send-type-0",
			"1": "operation_log.visitCycle.send-type-1",
			"2": "operation_log.visitCycle.send-type-2",
		}, //表单应用类型：
		"operation_log.form.applicationType": bson.M{
			"1": "operation_log.form.applicationTypeRegister",
			"2": "operation_log.form.applicationTypeFormula",
			"3": "operation_log.form.applicationTypeDoseAdjustment",
			"4": "operation_log.form.applicationTypeFactorCalc",
		},
		"operation_log.attribute.allowReplace": bson.M{
			"1": "operation_log.attribute.allowReplaceUnOpen",
			"2": "operation_log.attribute.allowReplaceOpen",
		},
		"operation_log.random_design.factorLabel": bson.M{
			"1": "operation_log.random_design.sync",
		},
		"operation_log.random_design.status": bson.M{
			"1": "operation_log.random_design.status_effective",
			"2": "operation_log.random_design.status_invalid",
		}, //分层因素字段状态
		"operation_log.attribute.segmentType": bson.M{
			"0": "operation_log.attribute.serialNumber",
			"1": "operation_log.attribute.medicineNumber",
		}, //分层因素字段状态
		"operation_log.random_design.factor.roundingMethod": bson.M{
			"1": "operation_log.random_design.factor.roundingMethod_up",
			"2": "operation_log.random_design.factor.roundingMethod_down",
		}, //保留小数位-取整方式
		"operation_log.drug_configure_setting.InheritanceRule": bson.M{
			"1": "operation_log.drug_configure_setting.groupRuleStop",
			"2": "operation_log.drug_configure_setting.groupRuleMain",
		}, //访视继承发放
		"operation_log.supply.alarm": bson.M{
			"1": "operation_log.supply.blindMedicine",
			"2": "operation_log.supply.openMedicine",
		}, //中心库存警戒
		"operation_log.supply.supply": bson.M{
			"1": "operation_log.supply.blindMedicine",
			"2": "operation_log.supply.openMedicine",
			"3": "operation_log.supply.forecastStop",
		}, //自动供应
		"operation_log.addBarcode.barcodeRule": bson.M{
			"0": "operation_log.addBarcode.rule_order",
			"1": "operation_log.addBarcode.rule_reverse",
		}, // 研究产品条形码规则
		"operation_log.addBarcode.packageRule": bson.M{
			"0": "operation_log.addBarcode.rule_order",
			"1": "operation_log.addBarcode.rule_reverse",
		}, // 包装条形码规则
		"operation_log.addBarcode.isPackageBarcode": bson.M{
			"true":  "operation_log.addBarcode.openPackageBarcode",
			"false": "operation_log.addBarcode.closePackageBarcode",
		}, // 包装条形码规则

		"operation_log.project.orderCheckDay": bson.M{
			"2": "operation_log.project.week.monday",
			"3": "operation_log.project.week.tuesday",
			"4": "operation_log.project.week.wednesday",
			"5": "operation_log.project.week.thursday",
			"6": "operation_log.project.week.friday",
			"7": "operation_log.project.week.saturday",
			"1": "operation_log.project.week.sunday",
		},
	}
)

type OperationLog struct {
	ID         primitive.ObjectID       `json:"id" bson:"_id"`
	Operator   primitive.ObjectID       `json:"operator" bson:"operator"`
	OID        primitive.ObjectID       `bson:"oid" json:"oid"`                // cohortID envID customerID
	OperatorID primitive.ObjectID       `bson:"operatorId" json:"operator_id"` //具体操作数据的ID
	Time       time.Duration            `json:"time" bson:"time"`
	Type       int                      `json:"type" bson:"type"`     //1新增 2编辑 3删除 4运行 6复制 7解绑 8再次授权 16激活 17失活
	Module     string                   `json:"module" bson:"module"` // 模块 eg:项目构建-编码配置
	Mark       []Mark                   `json:"mark" bson:"mark"`     //标识 eg 仓库:XXX，中心编号:xxx， 药物名称:xxx
	Fields     []OperationLogFieldGroup `json:"fields" bson:"fields"`
	CloudType  int                      `json:"cloudType" bson:"cloud_type"` //1用户删除 2用户禁用
}

type OperationLogVo struct {
	ID         primitive.ObjectID       `json:"id" bson:"_id"`
	Operator   primitive.ObjectID       `json:"operator" bson:"operator"`
	OID        primitive.ObjectID       `bson:"oid" json:"oid"`                // cohortID envID customerID
	OperatorID primitive.ObjectID       `bson:"operatorId" json:"operator_id"` //具体操作数据的ID
	Time       time.Duration            `json:"time" bson:"time"`
	Type       int                      `json:"type" bson:"type"`     //1新增 2编辑 3删除 4运行 6复制 7解绑 8再次授权 16激活 17失活
	Module     string                   `json:"module" bson:"module"` // 模块 eg:项目构建-编码配置
	Mark       []Mark                   `json:"mark" bson:"mark"`     //标识 eg 仓库:XXX，中心编号:xxx， 药物名称:xxx
	Fields     []OperationLogFieldGroup `json:"fields" bson:"fields"`
	CloudType  int                      `json:"cloudType" bson:"cloud_type"` //1用户删除 2用户禁用
	User       User                     `json:"user" bson:"user"`
}

type OperationLogFieldGroup struct {
	Key     string            `json:"key" bson:"key"` //user.info.name
	TranKey string            `json:"tranKey" bson:"tran_key"`
	New     OperationLogField `json:"new" bson:"new"`
	Old     OperationLogField `json:"old" bson:"old"`
}

type Mark struct {
	Label string // 标识标签  仓库 中心编号 药物名称
	Value string // 标识值  eg 仓库:XXX，中心编号:xxx， 药物名称:xxx 组别
	Blind bool
}

type OperationLogField struct {
	Type    int         `json:"type" bson:"type"` //1:int 2:string 3:country 4:bool 5:[]interface ArrayMap  6.OperationLogMap 7.checkBlind 8.roomBlind 9.supplyPlanSite 10.unblindingReason 30.根据中英文字段取值
	Value   interface{} `json:"value" bson:"value"`
	ENValue interface{} `json:"enValue" bson:"en_value"` //type：30的时候，分别存中英文string的数据
	Mark    int         `bson:"mark"`                    // 1 默认展示
}

type OperationLogFieldHandler interface {
	handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string
}

type IntHandler struct {
}

func (h *IntHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	return convertor.ToString(value)
}

type CountryHandler struct {
	Countries []Country
}

type OperationLogMapHandler struct {
}

type BoolHandler struct {
}

type ArrayMapHandler struct {
}

type CheckBlindHandler struct {
}

type CheckRoomBlindHandler struct {
}
type SupplyPlanSiteHandler struct {
}
type UnblindingReasonHandler struct {
}

func (u UnblindingReasonHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	logs := value.(primitive.A)
	result := ""
	for _, log := range logs {
		v := log.(primitive.D)
		m := v.Map()
		result = result + fmt.Sprintf("%s/%s;", m["reason"].(string), locales.Tr(ctx, m["allowremarktrankey"].(string)))
	}
	return result
}

func (s SupplyPlanSiteHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	v := value.(primitive.D)
	m := v.Map()
	if m["allsitetrankey"].(string) != "" {
		return locales.Tr(ctx, m["allsitetrankey"].(string))
	}
	return m["sitenames"].(string)
}

func (h *BoolHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	if value == nil {
		return locales.Tr(ctx, "common.no")
	}
	v := value.(bool)
	if !v {
		return locales.Tr(ctx, "common.no")
	}
	return locales.Tr(ctx, "common.yes")
}

func (h *CountryHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	return ""
}

func (h *OperationLogMapHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	v := convertor.ToString(value)
	if OperationLogMap[transKey[0]] != nil && OperationLogMap[transKey[0]].(bson.M)[v] != nil {
		v = OperationLogMap[transKey[0]].(bson.M)[v].(string)
	} else {
		v = ""
	}
	return locales.Tr(ctx, v)
}

func (h *ArrayMapHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	strArr := []string{}
	if value != nil {
		v := value.(primitive.A)
		for _, item := range v {
			if OperationLogMap[transKey[0]] != nil && OperationLogMap[transKey[0]].(bson.M)[convertor.ToString(item)] != nil {
				item = OperationLogMap[transKey[0]].(bson.M)[convertor.ToString(item)].(string)
				strArr = append(strArr, locales.Tr(ctx, item.(string)))
			}
		}
		return strings.Join(strArr, ",")
	} else {
		return ""
	}
}

func (h *CheckBlindHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	isBlindedRole, _ := ctx.Get("isBlindedRole")
	isBlind, _ := ctx.Get("isBlind")
	transKeyNew := transKey[0]
	if transKeyNew == "operation_log.addBarcode.medicineName" ||
		transKeyNew == "operation_log.project_storehouse.medicine_name" ||
		transKeyNew == "operation_log.project_storehouse.research_product_name_validity_reminder" ||
		transKeyNew == "operation_log.supply_detail.name" {
		if isBlindedRole.(bool) && isBlindDrugMap[convertor.ToString(value)] {
			return BlindData
		}
	} else if transKeyNew == "operation_log.project_storehouse.medicine_info" ||
		transKeyNew == "operation_log.drug_configure.drugValue" ||
		transKeyNew == "operation_log.drug_configure.drugNameSpec" ||
		transKeyNew == "operation_log.drug_configure.packageConfig" ||
		transKeyNew == "operation_log.uploadMedicines.medicineName" {
		if isBlindedRole.(bool) {
			newMedicineInfo := ""
			info := strings.Split(convertor.ToString(value), ";")
			for _, medicineInfoSet := range info {
				if medicineInfoSet != "" {
					medicineSet := strings.Split(medicineInfoSet, "/")
					newMedicineSet := ""
					for index, set := range medicineSet {
						if index == 0 && set != "" {
							if isBlindDrugMap[medicineSet[0]] {
								newMedicineSet = newMedicineSet + BlindData + "/"
							} else {
								newMedicineSet = newMedicineSet + set + "/"
							}
						} else {
							newMedicineSet = newMedicineSet + set + "/"
						}

					}
					content := newMedicineSet[0 : len(newMedicineSet)-1]
					newMedicineInfo = newMedicineInfo + fmt.Sprintf("%s;", content)
				}
			}
			return newMedicineInfo
		}
	} else if transKeyNew == "operation_log.drug_configure.packageConfigNew" {
		if isBlindedRole.(bool) {
			newMedicineInfo := ""
			info := strings.Split(convertor.ToString(value), ";")
			for _, medicineInfoSet := range info {
				if medicineInfoSet != "" {
					medicineSet := strings.Split(medicineInfoSet, ",")
					newMedicineSet := ""
					for index, set := range medicineSet {
						drugNames := strings.Split(set, "-")
						if isBlindDrugMap[drugNames[0]] {
							newMedicineSet = newMedicineSet + fmt.Sprintf("%s-%s,", BlindData, drugNames[1])
						} else {
							if index == len(medicineSet)-1 {
								newMedicineSet = newMedicineSet + set
							} else {
								newMedicineSet = newMedicineSet + set + ","
							}
						}
					}
					newMedicineInfo = newMedicineInfo + fmt.Sprintf("%s;", newMedicineSet)
				}
			}
			return newMedicineInfo
		}
	} else if transKeyNew == "operation_log.drug_configure_setting.dtp_ipType" {
		if isBlindedRole.(bool) {
			newMedicineInfo := ""
			info := strings.Split(convertor.ToString(value), ";")
			for _, medicineInfoSet := range info {
				if medicineInfoSet != "" {
					medicineSet := strings.Split(medicineInfoSet, ",")
					newMedicineSet := ""
					for _, set := range medicineSet {
						medicine := strings.Split(set, "/")
						for i, m := range medicine {
							if i == 0 && m != "" {
								if isBlindDrugMap[medicine[0]] {
									newMedicineSet = newMedicineSet + BlindData + "/"
								} else {
									newMedicineSet = newMedicineSet + m + "/"
								}
							} else {
								newMedicineSet = newMedicineSet + m + ","
							}
						}
					}
					content := newMedicineSet[0 : len(newMedicineSet)-1]
					newMedicineInfo = newMedicineInfo + fmt.Sprintf("%s;", content)
				}
			}
			return newMedicineInfo
		}
	} else if transKeyNew == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
		if isBlindedRole.(bool) {
			newMedicineInfo := ""
			info := strings.Split(convertor.ToString(value), ";")
			for _, medicineInfoSet := range info {
				if medicineInfoSet != "" {
					medicineSet := strings.Split(medicineInfoSet, "、")
					newMedicineSetList := make([]string, 0)
					for _, set := range medicineSet {
						medicine := strings.Split(set, "/")
						newMedicineSet := ""
						for i, m := range medicine {
							if i == 0 && m != "" {
								newMedicineSet = newMedicineSet + m + "/"
							} else if i == 1 && m != "" {
								if isBlindDrugMap[m] {
									newMedicineSet = newMedicineSet + BlindData + "/"
								} else {
									newMedicineSet = newMedicineSet + m + "/"
								}
							} else if i == 2 && m != "" {
								newMedicineSet = newMedicineSet + m
								newMedicineSetList = append(newMedicineSetList, newMedicineSet)
							}
						}
					}
					content := strings.Join(newMedicineSetList, "、")
					newMedicineInfo = newMedicineInfo + fmt.Sprintf("%s", content)
				}
			}
			return newMedicineInfo
		}
	} else {
		if isBlind.(bool) {
			return BlindData
		}
	}
	return convertor.ToString(value)
}

func (h *CheckRoomBlindHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	isBlind, _ := ctx.Get("isRoomBlind")
	if isBlind.(bool) {
		return BlindData
	}
	return convertor.ToString(value)
}

type SubGroupHandler struct {
}

func (h *SubGroupHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	subGroupBlind, _ := ctx.Get("subGroupBlind")
	va := value.(primitive.D)
	m := va.Map()
	v := ""
	group := m["group"]
	subName := m["subname"]
	subBlinds := subGroupBlind.(map[primitive.ObjectID][]GroupBlind)
	blinds := subBlinds[OID]
	subP, b := slice.Find(blinds, func(index int, item GroupBlind) bool {
		return item.Group == group.(string)
	})
	if b {
		sub := *subP
		isBlindedRole, _ := ctx.Get("isBlindedRole")
		if sub.SubBlind && isBlindedRole.(bool) {
			v = BlindData
		} else {
			v = subName.(string)
		}
	} else {
		v = subName.(string)
	}
	return v
}

type visitSubGroupHandler struct {
}

func (h *visitSubGroupHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	isBlindedRole, _ := ctx.Get("isBlindedRole")
	subGroupBlind, ok := ctx.Get("subGroupBlind")
	subBlinds := subGroupBlind.(map[primitive.ObjectID][]GroupBlind)
	blinds := subBlinds[OID]
	isBlind, _ := ctx.Get("isBlind")
	if ok {
		group := strings.Split(value.(string), ",")
		if isBlindedRole.(bool) {
			ss := make([]string, 0)
			for _, g := range group {
				gbP, b := slice.Find(blinds, func(index int, item GroupBlind) bool {
					return item.Group == g
				})
				if b {
					gb := *gbP
					if isBlindedRole.(bool) {
						if isBlind.(bool) && gb.SubBlind {
							ss = append(ss, BlindData+" "+BlindData)
						} else if !isBlind.(bool) && gb.SubBlind {
							ss = append(ss, gb.ParName+" "+BlindData)
						} else if isBlind.(bool) && !gb.SubBlind {
							ss = append(ss, BlindData+" "+gb.SubName)
						} else {
							ss = append(ss, g)
						}
					} else {
						ss = append(ss, g)
					}
				} else {
					ss = append(ss, BlindData)
				}
			}
			v := strings.Join(ss, ",")
			return v
		} else {
			return value.(string)
		}
	} else {
		if isBlind.(bool) && isBlindedRole.(bool) {
			return BlindData
		} else {
			return value.(string)
		}
	}
}

type groupConfigHandler struct {
}

func (h *groupConfigHandler) handle(ctx *gin.Context, value interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	isBlindedRole, _ := ctx.Get("isBlindedRole")
	subGroupBlind, ok := ctx.Get("subGroupBlind")
	blinds := make([]GroupBlind, 0)
	if ok {
		subBlinds := subGroupBlind.(map[primitive.ObjectID][]GroupBlind)
		blinds = subBlinds[OID]
	}
	isBlind, _ := ctx.Get("isBlind")
	if ok {
		split := strings.Split(value.(string), "@@")
		parName := split[0]
		subStr := split[1]
		subs := strings.Split(subStr, "$$")
		if isBlindedRole.(bool) {
			v := ""
			if isBlind.(bool) {
				v += BlindData + ":"
			} else {
				v += parName + ":"
			}
			afterSubs := slice.Map(subs, func(index int, item string) string {
				s, b := slice.Find(blinds, func(index int, it GroupBlind) bool {
					return it.ParName == parName && it.SubName == item
				})
				if b {
					if s.SubBlind {
						return BlindData
					} else {
						return item
					}
				} else {
					return BlindData
				}
			})
			v += strings.Join(afterSubs, "/")
			return v
		} else {
			v := strings.ReplaceAll(value.(string), "@@", ":")
			v = strings.ReplaceAll(v, "$$", "/")
			return v
		}
	} else {
		if isBlind.(bool) && isBlindedRole.(bool) {
			return BlindData
		} else {
			v := strings.ReplaceAll(value.(string), "@@", ":")
			v = strings.ReplaceAll(v, "$$", "/")
			return v
		}
	}
}
func TypeHandle(ctx *gin.Context, t int, v interface{}, isBlindDrugMap map[string]bool, OID primitive.ObjectID, transKey ...string) string {
	var o OperationLogFieldHandler
	if t == 1 || t == 2 || t == 30 {
		o = &IntHandler{}
	} else if t == 3 {
		o = &CountryHandler{}
	} else if t == 4 {
		o = &BoolHandler{}
	} else if t == 5 {
		o = &ArrayMapHandler{}
	} else if t == 6 {
		o = &OperationLogMapHandler{}
	} else if t == 7 {
		o = &CheckBlindHandler{}
	} else if t == 8 {
		o = &CheckRoomBlindHandler{}
	} else if t == 9 {
		o = &SupplyPlanSiteHandler{}
	} else if t == 10 {
		o = &UnblindingReasonHandler{}
	} else if t == 11 {
		o = &SubGroupHandler{}
	} else if t == 12 {
		o = &visitSubGroupHandler{}
	} else if t == 13 {
		o = &groupConfigHandler{}
	}
	if len(transKey) == 0 {
		return o.handle(ctx, v, isBlindDrugMap, OID)
	}
	return o.handle(ctx, v, isBlindDrugMap, OID, transKey[0])

}

type RetOperationLog struct {
	ID            primitive.ObjectID `json:"id"`
	UserEmail     string             `json:"user_email"`
	UserName      string             `json:"user_name"`
	Unicode       int32              `json:"unicode"`
	OperationType string             `json:"operation_type"`
	Time          time.Duration      `json:"time" bson:"time"`
	TimeStr       string             `json:"time_str"`
	Context       []string           `json:"fields" bson:"fields"`
}

type ReturnOperationLog struct {
	ID              primitive.ObjectID `json:"id"`
	UserEmail       string             `json:"user_email"`
	UserName        string             `json:"user_name"`
	Unicode         int32              `json:"unicode"`
	OperationType   string             `json:"operation_type"`
	Time            string             `json:"time" bson:"time"`
	Context         []string           `json:"fields" bson:"fields"`
	OperatorContext string             `json:"operatorContext" `
}
