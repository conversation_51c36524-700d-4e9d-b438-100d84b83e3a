package models

// ConfigureReport PDF...配置报告pdf
type ConfigureReport struct {
	Sponsor                          string            `json:"sponsor"`                          //申办方名称
	ProjectNumber                    string            `json:"projectNumber"`                    //项目编号
	ConfigureReport                  string            `json:"configureReport"`                  //配置报告
	Env                              string            `json:"env"`                              //项目环境
	GenerationTime                   string            `json:"generationTime"`                   //生成时间
	CreateDate                       string            `json:"createDate"`                       //年-月-日  时: 分: 秒
	Generator                        string            `json:"generator"`                        //生成者
	CreateBy                         string            `json:"createBy"`                         //XXX
	Directory                        string            `json:"directory"`                        //目录
	Summary                          string            `json:"summary"`                          //概览
	SummaryDetails                   string            `json:"summaryDetails"`                   //概览内容
	BasicInformation                 string            `json:"basicInformation"`                 //基本信息
	ProjectTimeZoneLabel             string            `json:"projectTimeZoneLabel"`             //项目时区列名
	ProjectTimeZone                  string            `json:"projectTimeZone"`                  //项目时区
	ProjectTypeLabel                 string            `json:"projectTypeLabel"`                 //项目类型列名
	ProjectType                      string            `json:"projectType"`                      //项目类型
	ProjectOrderCheckLabel           string            `json:"projectOrderCheckLabel"`           //中心研究产品库存核查列名
	ProjectOrderCheck                string            `json:"projectOrderCheck"`                //中心研究产品库存核查
	ProjectOrderConfirmationLabel    string            `json:"projectOrderConfirmationLabel"`    //中心回收订单确认列名
	ProjectOrderConfirmation         string            `json:"projectOrderConfirmation"`         //中心回收订单确认
	ProjectDeIsolationApprovalLabel  string            `json:"projectDeIsolationApprovalLabel"`  //解隔离审批列名
	ProjectDeIsolationApproval       string            `json:"projectDeIsolationApproval"`       //解隔离审批
	ProjectUnblindingControlLabel    string            `json:"projectUnblindingControlLabel"`    //揭盲控制列名
	ProjectUnblindingControl         string            `json:"projectUnblindingControl"`         //揭盲控制
	ProjectOrderApprovalControlLabel string            `json:"projectOrderApprovalControlLabel"` //研究中心订单申请列名
	ProjectOrderApprovalControl      string            `json:"projectOrderApprovalControl"`      //研究中心订单申请
	ProjectNoticeLabel               string            `json:"projectNoticeLabel"`               //项目通知列名
	ProjectNotice                    string            `json:"projectNotice"`                    //项目通知
	ProjectConnectEdcLabel           string            `json:"projectConnectEdcLabel"`           //对接EDC列名
	ProjectConnectEdc                string            `json:"projectConnectEdc"`                //对接EDC ---- 如对接，显示EDC供应商值，否则
	ProjectPushModeLabel             string            `json:"projectPushModeLabel"`             //数据推送方式列名
	ProjectPushMode                  string            `json:"projectPushMode"`                  //数据推送方式
	ProjectSynchronizationModeLabel  string            `json:"projectSynchronizationModeLabel"`  //同步方式列名
	ProjectSynchronizationMode       string            `json:"projectSynchronizationMode"`       //同步方式
	IsGroupStage                     bool              `json:"isGroupStage"`                     //是否群组、再随机项目 true是false不是
	CohortOrStage                    string            `json:"cohortOrStage"`                    //群组/阶段
	GeneralSituation                 string            `json:"generalSituation"`                 //概况
	AllOfThem                        string            `json:"allOfThem"`                        //的所有
	Name                             string            `json:"name"`                             //的所有
	CohortNameList                   []string          `json:"cohortNameList"`                   //cohort名称集合
	ConfigureDetailList              []ConfigureDetail `json:"configureDetailList"`              //项目详情
}

// 项目详情
type ConfigureDetail struct {
	Index              string                 `json:"index"`              //次数
	Title              string                 `json:"title"`              //一级标题
	AttributeConfigure AttributeConfigureInfo `json:"attributeConfigure"` //属性配置
	RandomConfigure    RandomConfigureInfo    `json:"randomConfigure"`    //随机配置
	FormConfigure      FormConfigureInfo      `json:"formConfigure"`      //表单配置
	IpManagement       IpManagementInfo       `json:"ipManagement"`       //研究产品管理
}

// 属性配置
type AttributeConfigureInfo struct {
	Title           string              `json:"title"`           //二级标题
	SystemConfigure SystemConfigureInfo `json:"systemConfigure"` //系统配置
	SubjectIDRules  SubjectIDRulesInfo  `json:"subjectIDRules"`  //受试者号规则
	OtherRules      OtherRulesInfo      `json:"otherRules"`      //其他规则
}

// 系统配置
type SystemConfigureInfo struct {
	Title                               string `json:"title"`                               //三级标题
	RandomizeLabel                      string `json:"randomizeLabel"`                      //随机化列名
	Randomize                           string `json:"randomize"`                           //随机化
	DisplayRandomizationIDLabel         string `json:"displayRandomizationIDLabel"`         //随机号展示列名
	DisplayRandomizationID              string `json:"displayRandomizationID"`              //随机号展示
	DisplayRandomSequenceNumberShowFlag bool   `json:"displayRandomSequenceNumberShowFlag"` //随机顺序号展示标记
	DisplayRandomSequenceNumberLabel    string `json:"displayRandomSequenceNumberLabel"`    //随机顺序号展示列名
	DisplayRandomSequenceNumber         string `json:"displayRandomSequenceNumber"`         //随机顺序号展示
	RandomSequenceNumberPrefixLabel     string `json:"randomSequenceNumberPrefixLabel"`     //随机顺序号前缀列名
	RandomSequenceNumberPrefix          string `json:"randomSequenceNumberPrefix"`          //随机顺序号前缀
	RandomSequenceNumberDigitLabel      string `json:"randomSequenceNumberDigitLabel"`      //随机顺序号位数列名
	RandomSequenceNumberDigit           string `json:"randomSequenceNumberDigit"`           //随机顺序号位数
	RandomSequenceNumberStartLabel      string `json:"randomSequenceNumberStartLabel"`      //随机顺序号起始数列名
	RandomSequenceNumberStart           string `json:"randomSequenceNumberStart"`           //随机顺序号起始数
	TreatmentDesignLabel                string `json:"treatmentDesignLabel"`                //发药设计列名
	TreatmentDesign                     string `json:"treatmentDesign"`                     //发药设计
	DTPRuleLabel                        string `json:"dtpRuleLabel"`                        //DTP规则列名
	DTPRule                             string `json:"dtpRule"`                             //DTP规则
	RandomizationSupplyCheckLabel       string `json:"randomizationSupplyCheckLabel"`       //随机研究产品供应核查列名
	RandomizationSupplyCheck            string `json:"randomizationSupplyCheck"`            //随机研究产品供应核查
	BlindDesignLabel                    string `json:"blindDesignLabel"`                    //盲法列名
	BlindDesign                         string `json:"blindDesign"`                         //盲法
	SubjectScreeningProcessLabel        string `json:"subjectScreeningProcessLabel"`        //受试者筛选流程列名
	SubjectScreeningProcess             string `json:"subjectScreeningProcess"`             //受试者筛选流程

}

// 受试者号规则
type SubjectIDRulesInfo struct {
	Title                              string `json:"title"`                              //三级标题
	SubjectNumberInputRuleLabel        string `json:"subjectNumberInputRuleLabel"`        //受试者号录入规则列名
	SubjectNumberInputRule             string `json:"subjectNumberInputRule"`             //受试者号录入规则
	SubjectPrefixLabel                 string `json:"subjectPrefixLabel"`                 //受试者前缀列名
	SubjectPrefix                      string `json:"subjectPrefix"`                      //受试者前缀
	SubjectIDPrefixLabel               string `json:"subjectIDPrefixLabel"`               //受试者号前缀列名
	SubjectIDPrefix                    string `json:"subjectIDPrefix"`                    //受试者号前缀
	ReplacementTextForSubjectIDLabel   string `json:"replacementTextForSubjectIDLabel"`   //受试者号替换文本列名
	ReplacementTextForSubjectID        string `json:"replacementTextForSubjectID"`        //受试者号替换文本
	ReplacementTextEnForSubjectIDLabel string `json:"replacementTextEnForSubjectIDLabel"` //受试者号替换文本(英文)列名
	ReplacementTextEnForSubjectID      string `json:"replacementTextEnForSubjectID"`      //受试者号替换文本(英文)
	SubjectIDDigitLabel                string `json:"subjectIDDigitLabel"`                //受试者号位数列名
	SubjectIDDigit                     string `json:"subjectIDDigit"`                     //受试者号位数
	SubjectReplacementLabel            string `json:"subjectReplacementLabel"`            //受试者替换列名
	SubjectReplacement                 string `json:"subjectReplacement"`                 //受试者替换
	TakeCare                           string `json:"takeCare"`                           //注：若受试者号前缀设置为{siteNO}，则实际受试者编号为09001，09为中心编号，001为受试者顺序号。
}

// 其他规则
type OtherRulesInfo struct {
	Title                            string `json:"title"`                            //三级标题
	StopUnblindedSubjectsLabel       string `json:"stopUnblindedSubjectsLabel"`       //是否停用非盲受试者列名
	StopUnblindedSubjects            string `json:"stopUnblindedSubjects"`            //是否停用非盲受试者
	QuarantinedIPCountingRuleLabel   string `json:"quarantinedIPCountingRuleLabel"`   //是否使用隔离单品计算规则列名
	QuarantinedIPCountingRule        string `json:"quarantinedIPCountingRule"`        //是否使用隔离单品计算规则
	TransportAccordingPackagingLabel string `json:"transportAccordingPackagingLabel"` //是否按包装运输列名
	TransportAccordingPackaging      string `json:"transportAccordingPackaging"`      //是否按包装运输
	Deactivate                       string `json:"deactivate"`                       //【停用非盲受试者】：开启后，紧急揭盲的受试者，不再允许继续发放。
	Quarantine                       string `json:"quarantine"`                       //【隔离单品计算规则】：开启后，运行运送算法时，将隔离单品默认计算为研究中心的可用库存。
	Packing                          string `json:"packing"`                          //【是否按包装运输】：开启后，订单新增等会按照包装运输。
}

// 随机配置
type RandomConfigureInfo struct {
	Level                     string                        `json:"level"`                     //标题级别
	Title                     string                        `json:"title"`                     //二级标题
	RandomDesign              RandomInfo                    `json:"randomDesign"`              //随机设计
	StratificationCalculation StratificationCalculationInfo `json:"stratificationCalculation"` //分层计算
}

// 随机设计
type RandomInfo struct {
	Level             string       `json:"level"`             //标题级别
	Title             string       `json:"title"`             //三级标题
	RandomTypeLabel   string       `json:"randomTypeLabel"`   //随机类型列名
	RandomType        string       `json:"randomType"`        //随机类型
	GroupLabel        string       `json:"groupLabel"`        //组别名称列名
	Group             []string     `json:"group"`             //组别名称
	RegionFactorLabel string       `json:"regionFactorLabel"` //地区分层列名
	RegionFactor      string       `json:"regionFactor"`      //地区分层
	FactorOptionLabel string       `json:"factorOptionLabel"` //分层因素(选项值)列名
	FactorLabel       string       `json:"factorLabel"`       //分层因素列名
	FieldNumberLabel  string       `json:"fieldNumberLabel"`  //字段编号列名
	FieldNameLabel    string       `json:"fieldNameLabel"`    //字段名称列名
	VariableLabel     string       `json:"variableLabel"`     //变量名称列名
	ControlTypeLabel  string       `json:"controlTypeLabel"`  //控件类型列名
	OptionLabel       string       `json:"optionLabel"`       //选项值列名
	StatusLabel       string       `json:"statusLabel"`       //状态列名
	FactorList        []FactorInfo `json:"factorList"`        //分层因素1...
	Length            string       `json:"length"`            //长度
}

// 分层因素
type FactorInfo struct {
	FieldNumber string   `json:"fieldNumber"` //字段编号
	FieldName   string   `json:"fieldName"`   //字段名称
	Variable    string   `json:"variable"`    //变量名称
	ControlType string   `json:"controlType"` //控件类型
	Option      []string `json:"option"`      //选项值
	Status      string   `json:"status"`      //状态
}

// 分层计算
type StratificationCalculationInfo struct {
	Level string `json:"level"` //标题级别
	Title string `json:"title"` //三级标题
	//Bmi                 BmiInfo                          `json:"bmi"`                 //BMI
	//Age                 AgeInfo                          `json:"age"`                 //年龄
	FieldNumberLabel    string                           `json:"fieldNumberLabel"`    //字段编号列名
	FormulaTypeLabel    string                           `json:"formulaTypeLabel"`    //公式类型列名
	CustomFormulaLabel  string                           `json:"customFormulaLabel"`  //自定义公式列名
	RetainDecimalsLabel string                           `json:"retainDecimalsLabel"` //保留小数位列名
	LayeredNameLabel    string                           `json:"layeredNameLabel"`    //分层名称列名
	LayeredOptionLabel  string                           `json:"layeredOptionLabel"`  //分层选项映射列名
	StatusLabel         string                           `json:"statusLabel"`         //状态列名
	FieldList           []StratificationCalculationField `json:"fieldList"`           //分层计算字段数据
}

type StratificationCalculationField struct {
	FieldNumber    string   `json:"fieldNumber"`    //字段编号
	FormulaType    string   `json:"formulaType"`    //公式类型
	CustomFormula  string   `json:"customFormula"`  //自定义公式
	RetainDecimals string   `json:"retainDecimals"` //保留小数位
	LayeredName    string   `json:"layeredName"`    //分层名称
	LayeredOption  []string `json:"layeredOption"`  //分层选项映射
	Status         string   `json:"status"`         //状态
}

// BMI
type BmiInfo struct {
	Level              string     `json:"level"`              //标题级别
	ParagraphNameLabel string     `json:"paragraphNameLabel"` //1.分层计算—BMI
	FieldList          []BimField `json:"fieldList"`          //BIM
	FieldNumberLabel   string     `json:"fieldNumberLabel"`   //字段编号列名
	FormulaLabel       string     `json:"formulaLabel"`       //计算公式列名
	WeightNameLabel    string     `json:"weightNameLabel"`    //录入体重字段名称列名
	HeightNameLabel    string     `json:"heightNameLabel"`    //录入身高字段名称列名
	ControlTypeLabel   string     `json:"controlTypeLabel"`   //控件类型列名
	LayeredNameLabel   string     `json:"layeredNameLabel"`   //分层名称列名
	LayeredOptionLabel string     `json:"layeredOptionLabel"` //分层选项映射列名
	StatusLabel        string     `json:"statusLabel"`        //状态列名
	TakeCare           string     `json:"takeCare"`           //注：(1) BMI计算公式：(体重[kg]÷(身高[m])²。
}
type BimField struct {
	FieldNumber   string   `json:"fieldNumber"`   //字段编号
	Formula       string   `json:"formula"`       //计算公式
	WeightName    string   `json:"weightName"`    //录入体重字段名称
	HeightName    string   `json:"heightName"`    //录入身高字段名称
	ControlType   string   `json:"controlType"`   //控件类型
	LayeredName   string   `json:"layeredName"`   //分层名称
	LayeredOption []string `json:"layeredOption"` //分层选项映射
	Status        string   `json:"status"`        //状态
}

// 年龄
type AgeInfo struct {
	Level               string     `json:"level"`               //标题级别
	ParagraphNameLabel  string     `json:"paragraphNameLabel"`  //2.分层计算—年龄
	FieldList           []AgeField `json:"fieldList"`           //年龄
	FieldNumberLabel    string     `json:"fieldNumberLabel"`    //字段编号列名
	FormulaLabel        string     `json:"formulaLabel"`        //计算公式列名
	FieldNameLabel      string     `json:"fieldNameLabel"`      //录入字段名称列名
	RetainDecimalsLabel string     `json:"retainDecimalsLabel"` //保留小数位列名
	ControlTypeLabel    string     `json:"controlTypeLabel"`    //控件类型列名
	FormatTypeLabel     string     `json:"formatTypeLabel"`     //格式类型列名
	LayeredNameLabel    string     `json:"layeredNameLabel"`    //分层名称列名
	LayeredOptionLabel  string     `json:"layeredOptionLabel"`  //分层选项映射列名
	StatusLabel         string     `json:"statusLabel"`         //状态列名
	TakeCare            string     `json:"takeCare"`            //注：(1) 年龄：按365.25天/年进行计算。
}
type AgeField struct {
	FieldNumber    string   `json:"fieldNumber"`    //字段编号
	Formula        string   `json:"formula"`        //计算公式
	FieldName      string   `json:"fieldName"`      //录入字段名称
	RetainDecimals string   `json:"retainDecimals"` //保留小数位
	ControlType    string   `json:"controlType"`    //控件类型
	FormatType     string   `json:"formatType"`     //格式类型
	LayeredName    string   `json:"layeredName"`    //分层名称
	LayeredOption  []string `json:"layeredOption"`  //分层选项映射
	Status         string   `json:"status"`         //状态
}

// 表单配置
type FormConfigureInfo struct {
	Level               string                  `json:"level"`               //标题级别
	Title               string                  `json:"title"`               //二级标题
	SubjectRegistration SubjectRegistrationInfo `json:"subjectRegistration"` //受试者登记
	CustomFormula       CustomFormulaInfo       `json:"customFormula"`       //公式计算
	DoseAdjustment      DoseAdjustmentInfo      `json:"doseAdjustment"`      //剂量调整
	LayeredCalculation  LayeredCalculationInfo  `json:"layeredCalculation"`  //分层计算
}

// 受试者登记
type SubjectRegistrationInfo struct {
	Level               string              `json:"level"`               //标题级别
	Title               string              `json:"title"`               //三级标题
	FieldNameLabel      string              `json:"fieldNameLabel"`      //字段名称列名
	IsEditableLabel     string              `json:"isEditableLabel"`     //是否可修改列名
	RequiredLabel       string              `json:"requiredLabel"`       //必填列名
	VariableIdLabel     string              `json:"variableIdLabel"`     //变量ID列名
	ControlTypeLabel    string              `json:"controlTypeLabel"`    //控件类型列名
	OptionLabel         string              `json:"optionLabel"`         //选项值列名
	FormatTypeLabel     string              `json:"formatTypeLabel"`     //格式类型列名
	VariableFormatLabel string              `json:"variableFormatLabel"` //变量格式列名
	VariableRangeLabel  string              `json:"variableRangeLabel"`  //变量范围列名
	StatusLabel         string              `json:"statusLabel"`         //状态列名
	FieldList           []RegistrationField `json:"fieldList"`           //受试者登记
}
type RegistrationField struct {
	FieldName      string   `json:"fieldName"`      //字段名称
	IsEditable     string   `json:"isEditable"`     //是否可修改
	Required       string   `json:"required"`       //必填
	VariableId     string   `json:"variableId"`     //变量ID
	ControlType    string   `json:"controlType"`    //控件类型
	Option         []string `json:"option"`         //选项值
	FormatType     string   `json:"formatType"`     //格式类型
	VariableFormat string   `json:"variableFormat"` //变量格式
	VariableRange  string   `json:"variableRange"`  //变量范围
	Status         string   `json:"status"`         //状态
}

// 公式计算
type CustomFormulaInfo struct {
	Level               string         `json:"level"`               //标题级别
	Title               string         `json:"title"`               //三级标题
	FieldNameLabel      string         `json:"fieldNameLabel"`      //字段名称列名
	RequiredLabel       string         `json:"requiredLabel"`       //必填列名
	VariableIdLabel     string         `json:"variableIdLabel"`     //变量ID列名
	ControlTypeLabel    string         `json:"controlTypeLabel"`    //控件类型列名
	FormatTypeLabel     string         `json:"formatTypeLabel"`     //格式类型列名
	VariableFormatLabel string         `json:"variableFormatLabel"` //变量格式列名
	VariableRangeLabel  string         `json:"variableRangeLabel"`  //变量范围列名
	StatusLabel         string         `json:"statusLabel"`         //状态列名
	FieldList           []FormulaField `json:"fieldList"`           //公式计算
}
type FormulaField struct {
	FieldName      string `json:"fieldName"`      //字段名称
	Required       string `json:"required"`       //必填
	VariableId     string `json:"variableId"`     //变量ID
	ControlType    string `json:"controlType"`    //控件类型
	FormatType     string `json:"formatType"`     //格式类型
	VariableFormat string `json:"variableFormat"` //变量格式
	VariableRange  string `json:"variableRange"`  //变量范围
	Status         string `json:"status"`         //状态
}

// 剂量调整
type DoseAdjustmentInfo struct {
	Level            string      `json:"level"`            //标题级别
	Title            string      `json:"title"`            //三级标题
	FieldNameLabel   string      `json:"fieldNameLabel"`   //字段名称列名
	RequiredLabel    string      `json:"requiredLabel"`    //必填列名
	VariableIdLabel  string      `json:"variableIdLabel"`  //变量ID列名
	ControlTypeLabel string      `json:"controlTypeLabel"` //控件类型列名
	OptionLabel      string      `json:"optionLabel"`      //选项值列名
	StatusLabel      string      `json:"statusLabel"`      //状态列名
	FieldList        []DoseField `json:"fieldList"`        //剂量调整
}
type DoseField struct {
	FieldName   string   `json:"fieldName"`   //字段名称
	Required    string   `json:"required"`    //必填
	VariableId  string   `json:"variableId"`  //变量ID
	ControlType string   `json:"controlType"` //控件类型
	Option      []string `json:"option"`      //选项值
	Status      string   `json:"status"`      //状态
}

// 分层计算
type LayeredCalculationInfo struct {
	Level               string         `json:"level"`               //标题级别
	Title               string         `json:"title"`               //三级标题
	FieldNameLabel      string         `json:"fieldNameLabel"`      //字段名称列名
	IsEditableLabel     string         `json:"isEditableLabel"`     //是否可修改列名
	RequiredLabel       string         `json:"requiredLabel"`       //必填列名
	VariableIdLabel     string         `json:"variableIdLabel"`     //变量ID列名
	ControlTypeLabel    string         `json:"controlTypeLabel"`    //控件类型列名
	OptionLabel         string         `json:"optionLabel"`         //选项值列名
	FormatTypeLabel     string         `json:"formatTypeLabel"`     //格式类型列名
	VariableFormatLabel string         `json:"variableFormatLabel"` //变量格式列名
	VariableRangeLabel  string         `json:"variableRangeLabel"`  //变量范围列名
	StatusLabel         string         `json:"statusLabel"`         //状态列名
	FieldList           []LayeredField `json:"fieldList"`           //分层计算数据
}
type LayeredField struct {
	FieldName      string   `json:"fieldName"`      //字段名称
	IsEditable     string   `json:"isEditable"`     //是否可修改
	Required       string   `json:"required"`       //必填
	VariableId     string   `json:"variableId"`     //变量ID
	ControlType    string   `json:"controlType"`    //控件类型
	Option         []string `json:"option"`         //选项值
	FormatType     string   `json:"formatType"`     //格式类型
	VariableFormat string   `json:"variableFormat"` //变量格式
	VariableRange  string   `json:"variableRange"`  //变量范围
	Status         string   `json:"status"`         //状态
}

// 研究产品管理
type IpManagementInfo struct {
	Level           string              `json:"level"`           //标题级别
	Title           string              `json:"title"`           //二级标题
	VisitManagement VisitManagementInfo `json:"visitManagement"` //访视管理
	TreatmentDesign TreatmentDesignInfo `json:"treatmentDesign"` //研究产品配置
}

// 访视管理
type VisitManagementInfo struct {
	Level                 string         `json:"level"`                 //标题级别
	Title                 string         `json:"title"`                 //三级标题
	CycleVersionLabel     string         `json:"cycleVersionLabel"`     //访视版本列名
	CycleVersion          string         `json:"cycleVersion"`          //访视版本
	VisitOffsetTypeLabel  string         `json:"visitOffsetTypeLabel"`  //访视偏倚类型列名
	VisitOffsetType       string         `json:"visitOffsetType"`       //访视偏倚类型
	VisitNumberLabel      string         `json:"visitNumberLabel"`      //访视编号列名
	VisitNameLabel        string         `json:"visitNameLabel"`        //访视名称列名
	GroupLabel            string         `json:"groupLabel"`            //组别列名
	IntervalDurationLabel string         `json:"intervalDurationLabel"` //间隔时长列名
	WindowLabel           string         `json:"windowLabel"`           //窗口期列名
	IsDispenseLabel       string         `json:"isDispenseLabel"`       //允许发放列名
	IsRandomizeLabel      string         `json:"isRandomizeLabel"`      //允许随机列名
	IsDTPLabel            string         `json:"isDTPLabel"`            //允许DTP列名
	IsSubjectReplaceLabel string         `json:"isSubjectReplaceLabel"` //允许受试者替换列名
	IsDoseAdjustmentLabel string         `json:"isDoseAdjustmentLabel"` //剂量调整列名
	VisitList             []VisitDetails `json:"visitList"`             //访视列表
}

// 访视列表
type VisitDetails struct {
	VisitNumber      string `json:"visitNumber"`      //访视编号
	VisitName        string `json:"visitName"`        //访视名称
	Group            string `json:"group"`            //组别
	IntervalDuration string `json:"intervalDuration"` //间隔时长
	Window           string `json:"window"`           //窗口期
	IsDispense       string `json:"isDispense"`       //允许发放
	IsRandomize      string `json:"isRandomize"`      //允许随机
	IsDTP            string `json:"isDTP"`            //允许DTP
	IsSubjectReplace string `json:"isSubjectReplace"` //允许受试者替换
	IsDoseAdjustment string `json:"isDoseAdjustment"` //剂量调整
}

// 研究产品配置
type TreatmentDesignInfo struct {
	Level         string            `json:"level"`         //标题级别
	Title         string            `json:"title"`         //三级标题
	DTPIp         DTPIpInfo         `json:"DTPIp"`         //DTP研究产品
	LabelOpen     LabelOpenInfo     `json:"labelOpen"`     //按标签/开放配置
	FormulaConfig FormulaConfigInfo `json:"formulaConfig"` //按公式计算
}

// DTP研究产品
type DTPIpInfo struct {
	Level          string       `json:"level"`              //标题级别
	Title          string       `json:"title"`              //标题
	IpLabel        string       `json:"ipLabel"`            //研究产品列名
	DTPModeLabel   string       `json:"dtpModeLabel"`       // DTP方式列名
	DTPIpFieldList []DTPIpField `json:"labelOpenFieldList"` //DTP研究产品
	TakeCare       string       `json:"takeCare"`           //注：黄色字体为未编号研究产品。
}

type DTPIpField struct {
	IpName      string   `json:"ipName"`      //研究产品名称
	DTPTypeList []string `json:"dtpTypeList"` // DTP类型
	IsOtherDrug bool     `json:"isOtherDrug"` // 是否是未编号药物
}

// 按标签/开放配置
type LabelOpenInfo struct {
	Level                     string           `json:"level"`                     //标题级别
	Title                     string           `json:"title"`                     //标题
	GroupLabel                string           `json:"groupLabel"`                //组别列名
	VisitNameLabel            string           `json:"visitNameLabel"`            //访视名称列名
	IpNameLabel               string           `json:"ipNameLabel"`               //研究产品名称列名
	DispensationQuantityLabel string           `json:"dispensationQuantityLabel"` //发放数量列名
	CustomFormulaLabel        string           `json:"customFormulaLabel"`        //自定义公式列名
	CombinedDispensationLabel string           `json:"combinedDispensationLabel"` //（组合）发放标签列名
	IpSpecificationLabel      string           `json:"ipSpecificationLabel"`      //研究产品规格列名
	SpecificationLabel        string           `json:"specificationLabel"`        //规格列名
	AutomaticAssignmentLabel  string           `json:"automaticAssignmentLabel"`  //是否自动赋值列名
	CalculationUnitLabel      string           `json:"calculationUnitLabel"`      //自动赋值计算单位列名
	LabelOpenFieldList        []LabelOpenField `json:"labelOpenFieldList"`        //按标签/开放配置
	TakeCare                  string           `json:"takeCare"`                  //注：黄色字体为未编号研究产品。
}
type LabelOpenField struct {
	Group                string `json:"group"`                //组别
	VisitName            string `json:"visitName"`            //访视名称
	IpName               string `json:"ipName"`               //研究产品名称
	DispensationQuantity string `json:"dispensationQuantity"` //发放数量
	CustomFormula        string `json:"customFormula"`        //自定义公式
	CombinedDispensation string `json:"combinedDispensation"` //（组合）发放标签
	IpSpecification      string `json:"ipSpecification"`      //研究产品规格
	Specification        string `json:"specification"`        //规格
	AutomaticAssignment  string `json:"automaticAssignment"`  //是否自动赋值
	CalculationUnit      string `json:"calculationUnit"`      //自动赋值计算单位
	IsOtherDrug          bool   `json:"isOtherDrug"`          // 是否是未编号药物
}

// 按公式计算
type FormulaConfigInfo struct {
	Level            string               `json:"level"`            //标题级别
	Title            string               `json:"title"`            //标题
	FormulaAge       FormulaAgeInfo       `json:"formulaAge"`       //按公式计算-年龄
	FormulaWeight    FormulaWeightInfo    `json:"formulaWeight"`    //按公式计算-体重
	FormulaSimpleBSA FormulaSimpleBSAInfo `json:"formulaSimpleBSA"` //按公式计算-简易体表面积BSA
	FormulaOtherBSA  FormulaOtherBSAInfo  `json:"formulaOtherBSA"`  //按公式计算-其他体表面积(按自定义公式)
}

// 按公式计算-年龄
type FormulaAgeInfo struct {
	Title                     string            `json:"title"`                     //标题-年龄
	GroupLabel                string            `json:"groupLabel"`                //组别列名
	VisitNameLabel            string            `json:"visitNameLabel"`            //访视名称列名
	IpNameLabel               string            `json:"ipNameLabel"`               //研究产品名称列名
	IpSpecificationLabel      string            `json:"ipSpecificationLabel"`      //研究产品规格列名
	AgeRangeLabel             string            `json:"ageRangeLabel"`             //年龄范围列名
	DispensationQuantityLabel string            `json:"dispensationQuantityLabel"` //发放数量列名
	IsOpenIpLabel             string            `json:"isOpenIpLabel"`             //是否为开放研究产品列名
	KeepDecimalPlacesLabel    string            `json:"keepDecimalPlacesLabel"`    //录入数据计算时保留小数位列名
	FormulaAgeFieldList       []FormulaAgeField `json:"formulaAgeFieldList"`       //年龄
	TakeCare                  string            `json:"takeCare"`                  //注： （1）年龄：按365.25天/年进行计算； （2）黄色字体为未编号研究产品。
}
type FormulaAgeField struct {
	Group                string   `json:"group"`                //组别
	VisitName            string   `json:"visitName"`            //访视名称
	IpName               string   `json:"ipName"`               //研究产品名称
	IpSpecification      string   `json:"ipSpecification"`      //研究产品规格
	AgeRange             []string `json:"ageRange"`             //年龄范围
	DispensationQuantity []string `json:"dispensationQuantity"` //发放数量
	IsOpenIp             string   `json:"isOpenIp"`             //是否为开放研究产品
	KeepDecimalPlaces    string   `json:"keepDecimalPlaces"`    //录入数据计算时保留小数位
	IsOtherDrug          bool     `json:"isOtherDrug"`          // 是否是未编号药物
}

// 按公式计算-体重
type FormulaWeightInfo struct {
	Title                            string               `json:"title"`                            //标题-体重
	GroupLabel                       string               `json:"groupLabel"`                       //组别列名
	VisitNameLabel                   string               `json:"visitNameLabel"`                   //访视名称列名
	IpNameLabel                      string               `json:"ipNameLabel"`                      //研究产品名称列名
	IpSpecificationLabel             string               `json:"ipSpecificationLabel"`             //研究产品规格列名
	WeightRangeLabel                 string               `json:"weightRangeLabel"`                 //体重范围列名
	DispensationQuantityLabel        string               `json:"dispensationQuantityLabel"`        //发放数量列名
	IsOpenIpLabel                    string               `json:"isOpenIpLabel"`                    //是否为开放研究产品列名
	KeepDecimalPlacesLabel           string               `json:"keepDecimalPlacesLabel"`           //录入数据计算时保留小数位列名
	WeightComparisonCalculationLabel string               `json:"weightComparisonCalculationLabel"` //体重比较计算列名
	ComparedWithLabel                string               `json:"comparedWithLabel"`                //相较于列名
	ChangeLabel                      string               `json:"changeLabel"`                      //变化的百分比（%）列名
	CalculationLabel                 string               `json:"calculationLabel"`                 //本次计算体重为列名
	FormulaWeightFieldList           []FormulaWeightField `json:"formulaWeightFieldList"`           //体重
	TakeCare                         string               `json:"takeCare"`                         //注：黄色字体为未编号研究产品。
}
type FormulaWeightField struct {
	Group                string   `json:"group"`                //组别
	VisitName            string   `json:"visitName"`            //访视名称
	IpName               string   `json:"ipName"`               //研究产品名称
	IpSpecification      string   `json:"ipSpecification"`      //研究产品规格
	WeightRange          []string `json:"weightRange"`          //体重范围
	DispensationQuantity []string `json:"dispensationQuantity"` //发放数量
	IsOpenIp             string   `json:"isOpenIp"`             //是否为开放研究产品
	KeepDecimalPlaces    string   `json:"keepDecimalPlaces"`    //录入数据计算时保留小数位
	ComparedWith         string   `json:"comparedWith"`         //相较于
	Change               string   `json:"change"`               //变化的百分比（%）
	Calculation          string   `json:"calculation"`          //本次计算体重为
	IsOtherDrug          bool     `json:"isOtherDrug"`          // 是否是未编号药物
}

// 按公式计算-简易体表面积BSA
type FormulaSimpleBSAInfo struct {
	Title                            string                  `json:"title"`                            //标题-体重
	GroupLabel                       string                  `json:"groupLabel"`                       //组别列名
	VisitNameLabel                   string                  `json:"visitNameLabel"`                   //访视名称列名
	IpNameLabel                      string                  `json:"ipNameLabel"`                      //研究产品名称列名
	IpSpecificationLabel             string                  `json:"ipSpecificationLabel"`             //研究产品规格列名
	UnitCapacityLabel                string                  `json:"unitCapacityLabel"`                //单位容量列名
	UnitCalculationStandardLabel     string                  `json:"unitCalculationStandardLabel"`     //单位计算标准列名
	IsOpenIpLabel                    string                  `json:"isOpenIpLabel"`                    //是否为开放研究产品列名
	WeightComparisonCalculationLabel string                  `json:"weightComparisonCalculationLabel"` //体重比较计算列名
	ComparedWithLabel                string                  `json:"comparedWithLabel"`                //相较于列名
	ChangeLabel                      string                  `json:"changeLabel"`                      //变化的百分比（%）列名
	CalculationLabel                 string                  `json:"calculationLabel"`                 //本次计算体重为列名
	FormulaSimpleBSAFieldList        []FormulaSimpleBSAField `json:"formulaSimpleBSAFieldList"`        //简易体表面积BSA
	TakeCare                         string                  `json:"takeCare"`                         //注： （1）简易体表面积BSA公式: [体重 (kg) x 身高 (cm)/3600]1/2；（2）黄色字体为未编号研究产品。
}
type FormulaSimpleBSAField struct {
	Group                   string `json:"group"`                   //组别
	VisitName               string `json:"visitName"`               //访视名称
	IpName                  string `json:"ipName"`                  //研究产品名称
	IpSpecification         string `json:"ipSpecification"`         //研究产品规格
	UnitCapacity            string `json:"unitCapacity"`            //单位容量
	UnitCalculationStandard string `json:"unitCalculationStandard"` //单位计算标准
	IsOpenIp                string `json:"isOpenIp"`                //是否为开放研究产品
	ComparedWith            string `json:"comparedWith"`            //相较于
	Change                  string `json:"change"`                  //变化的百分比（%）
	Calculation             string `json:"calculation"`             //本次计算体重为
	IsOtherDrug             bool   `json:"isOtherDrug"`             // 是否是未编号药物
}

// 按公式计算-其他体表面积(按自定义公式)
type FormulaOtherBSAInfo struct {
	Title                            string                 `json:"title"`                            //标题-体重
	GroupLabel                       string                 `json:"groupLabel"`                       //组别列名
	VisitNameLabel                   string                 `json:"visitNameLabel"`                   //访视名称列名
	IpNameLabel                      string                 `json:"ipNameLabel"`                      //研究产品名称列名
	IpSpecificationLabel             string                 `json:"ipSpecificationLabel"`             //研究产品规格列名
	UnitCapacityLabel                string                 `json:"unitCapacityLabel"`                //单位容量列名
	UnitCalculationStandardLabel     string                 `json:"unitCalculationStandardLabel"`     //单位计算标准列名
	IsOpenIpLabel                    string                 `json:"isOpenIpLabel"`                    //是否为开放研究产品列名
	WeightComparisonCalculationLabel string                 `json:"weightComparisonCalculationLabel"` //体重比较计算列名
	ComparedWithLabel                string                 `json:"comparedWithLabel"`                //相较于列名
	ChangeLabel                      string                 `json:"changeLabel"`                      //变化的百分比（%）列名
	CalculationLabel                 string                 `json:"calculationLabel"`                 //本次计算体重为列名
	FormulaOtherBSAFieldList         []FormulaOtherBSAField `json:"formulaOtherBSAFieldList"`         //其他体表面积(按自定义公式)
	TakeCare                         string                 `json:"takeCare"`                         //注： （1）自定义公式: xxxxxxxxxx；（2）黄色字体为未编号研究产品。
}
type FormulaOtherBSAField struct {
	Group                   string `json:"group"`                   //组别
	VisitName               string `json:"visitName"`               //访视名称
	IpName                  string `json:"ipName"`                  //研究产品名称
	IpSpecification         string `json:"ipSpecification"`         //研究产品规格
	UnitCapacity            string `json:"unitCapacity"`            //单位容量
	UnitCalculationStandard string `json:"unitCalculationStandard"` //单位计算标准
	IsOpenIp                string `json:"isOpenIp"`                //是否为开放研究产品
	ComparedWith            string `json:"comparedWith"`            //相较于
	Change                  string `json:"change"`                  //变化的百分比（%）
	Calculation             string `json:"calculation"`             //本次计算体重为
	IsOtherDrug             bool   `json:"isOtherDrug"`             // 是否是未编号药物
}
