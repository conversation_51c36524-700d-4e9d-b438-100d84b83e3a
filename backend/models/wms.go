package models

// WmsMediciens ...
type WmsMediciens struct {
	ProjectNo     string       `json:"projectNo"`
	WarehouseCode string       `json:"warehouseCode"`
	CompanyCode   string       `json:"companyCode"`
	Type          int          `json:"type"`
	Products      []WmsProduct `json:"products"`
}

type WmsProduct struct {
	Number         string `json:"number"`         // 研究产品号
	Name           string `json:"name"`           // 研究产品名称
	Spec           string `json:"spec"`           // 研究产品规格
	BatchNumber    string `json:"batchNumber"`    // 批次号
	ExpirationDate string `json:"expirationDate"` // 有效期 yyyy-MM-dd
	//packList array-string // 包装号列
}

type WmsCancelOrder struct {
	ProjectNo   string `json:"projectNo"`
	OrderNumber string `json:"orderNumber"`
	CompanyCode string `json:"companyCode"`
}

type WmsOrder struct {
	ProjectNo           string       `json:"projectNo"`
	OrderNumber         string       `json:"orderNumber"`
	CompanyCode         string       `json:"companyCode"`
	WarehouseCode       string       `json:"warehouseCode"`
	Send                WmsInstitute `json:"send"`
	Receive             WmsInstitute `json:"receive"`
	ExpectedArrivalTime string       `json:"expectedArrivalTime"` //期望送达时间
	Products            []WmsProduct `json:"products"`
}

type WmsInstitute struct {
	Number  string `json:"number"`  // 发送地
	Name    string `json:"name"`    // 名称
	Contact string `json:"contact"` // 联系人
	Phone   string `json:"phone"`   // 手机号
	Email   string `json:"email"`   // 邮箱
	Address string `json:"address"` // 地址
	Country string `json:"county"`  // 国家 alpha3
	State   string `json:"state"`   // 省份
	City    string `json:"city"`    // 城市
}

type DrugApply struct {
	AppId             string            `json:"appId"`
	Nonce             string            `json:"nonce"`
	TimeStamp         string            `json:"timeStamp"`
	Sign              string            `json:"sign"`
	DrugApplyAPIParam DrugApplyAPIParam `json:"drugApplyAPIParam"`
}

type DrugApplyAPIParam struct {
	OpenApiOrderId  string           `json:"openApiOrderId"`  //外部订单 id 对接外部的唯一id
	ProjectCode     string           `json:"projectCode"`     //项目编号
	ReceiptType     string           `json:"receiptType"`     // 订单类型 固定传 0
	DrugInfoList    []DrugInfoDTO    `json:"drugInfoList"`    // 药品信息 不可为空集合
	ReceiveInfoList []ReceiveInfoDTO `json:"receiveInfoList"` // 收件人信息 不可为空集合
	CenterName      string           `json:"centerName"`      // 中心名称
	CenterCode      string           `json:"centerCode"`      // 中心 code
	CreateUserPhone string           `json:"createUserPhone"` // 创建人手机号
	CreateUserEmail string           `json:"createUserEmail"` // 创建人邮箱
	CreateUserName  string           `json:"createUserName"`  // 创建人姓名
	Note            string           `json:"note"`            // 备注
	ApplyTime       string           `json:"applyTime"`       // 申请时间,时间戳
	ApproveTime     string           `json:"approveTime"`     // 审批完成时间,时间
}

type DrugInfoDTO struct {
	MedicineName           string   `json:"medicineName"`           //Y 药品名称
	DrugNumber             []string `json:"drugNumber"`             ///N 药品编号 编盲号段
	MedicineCount          int      `json:"medicineCount"`          // N 申请药品数量
	Standard               string   `json:"standard"`               //Y 药物规格
	BatchNumber            string   `json:"batchNumber"`            //Y 批号
	MedicineExpirationDate int64    `json:"medicineExpirationDate"` //Y 效期，时间戳
	StorageConditions      string   `json:"storageConditions"`      //存储条件
}

type ReceiveInfoDTO struct {
	ReceiveName string `json:"receiveName"` //N 收件人姓名
	LandLine    string `json:"landLine"`    //N 项目编号 landline 和
	Phone       string `json:"phone"`       //N 订单类型 landline 和
	Addressee   string `json:"addressee"`   //N 药品信息
	CityName    string `json:"cityName"`    // N 城市
}
