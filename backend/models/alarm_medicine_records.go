package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// AlarmMedicineRecords ...
type AlarmMedicineRecords struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	EnvID         primitive.ObjectID `json:"env_id" bson:"env_id"`
	LastMailID    primitive.ObjectID `json:"last_mail_id" bson:"last_mail_id"` // 上一封邮件ID
	ProjectSiteID primitive.ObjectID `json:"project_site_id" bson:"project_site_id"`
	Subject       string             `json:"subject" bson:"subject"`           // 邮件类型
	CreatedTime   time.Duration      `json:"created_time" bson:"created_time"` // 创建时间
}
