package models

import "go.mongodb.org/mongo-driver/bson/primitive"

type ProjectRolePermission struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	RoleId      primitive.ObjectID `json:"roleId" bson:"role_id"`
	CustomerID  primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID   primitive.ObjectID `json:"projectId" bson:"project_id"`
	Name        string             `json:"name"`
	Scope       string             `json:"scope"`
	Description string             `json:"description"`
	Template    int                `json:"template"` // 1 通用模板 2、DTP
	Status      int                `json:"status"`   // 2 无效  1、 有效
	Permissions []string           `json:"permissions" bson:"permissions"`
}

type RolePermission struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	Name        string             `json:"name"`
	Scope       string             `json:"scope"`
	Description string             `json:"description"`
	Template    int                `json:"template"` // 1 通用模板 2、DTP
	Status      int                `json:"status"`   // 2 无效  1、 有效
	Permissions []string           `json:"permissions" bson:"permissions"`
}
type RolePermissionWithType struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	Type        int                `json:"type"`
	Name        string             `json:"name"`
	Scope       string             `json:"scope"`
	Description string             `json:"description"`
	Template    int                `json:"template"` // 1 通用模板 2、DTP
	Status      int                `json:"status"`   // 2 无效  1、 有效
	Permissions []string           `json:"permissions" bson:"permissions"`
}

type RolePool struct {
	Key  int    `json:"key"`
	Name string `json:"name"`
	Type int    `json:"type"`
}

type ScopeCount struct {
	Scope string `json:"scope"`
	Count int    `json:"count"`
}

type ProjectRolePermissionUser struct {
	ID       primitive.ObjectID `json:"id" bson:"_id"`
	RoleName string             `json:"roleName" bson:"role_name"`
	User     []UserInfos        `json:"user" bson:"user"`
}

type UserInfos struct {
	ID       primitive.ObjectID `json:"id" bson:"_id"`
	UserInfo `json:"info" bson:"info"`
}
