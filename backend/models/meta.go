package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Meta Data
type Meta struct {
	CreatedAt time.Duration      `json:"createdAt" bson:"created_at"`
	CreatedBy primitive.ObjectID `json:"createdBy" bson:"created_by"`
	UpdatedAt time.Duration      `json:"updatedAt" bson:"updated_at"`
	UpdatedBy primitive.ObjectID `json:"updatedBy" bson:"updated_by"`
	DeletedAt time.Duration      `json:"deletedAt" bson:"deleted_at"`
	DeletedBy primitive.ObjectID `json:"deletedBy" bson:"deleted_by"`
}
