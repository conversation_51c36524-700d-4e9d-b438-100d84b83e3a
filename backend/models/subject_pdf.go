package models

import "go.mongodb.org/mongo-driver/bson/primitive"

type SubjectPdf struct {
	ID                   primitive.ObjectID
	ProjectNumber        string
	ProjectName          string
	CohortName           string
	Sponsor              string
	EnvName              string
	Creator              string
	SiteNumber           string
	SiteName             string
	SubjectNumber        string
	RegisterTimeStr      string
	RegisterInfo         []FormInfo
	IsScreening          bool
	ScreenTimeStr        string
	ICFSignedTimeStr     string
	ModifyInfo           []ModifyInfo
	Group                string
	RandomNumber         string
	RandomTimeStr        string
	FactorInfo           []FactorDetailInfo
	DispensingDetailInfo []DispensingDetailInfo
	CancelDetailInfo     []DispensingDetailInfo
	ReissueDetailInfo    []DispensingDetailInfo
	OutVisitDetailInfo   []DispensingDetailInfo
	RetrieveDetailInfo   []DispensingDetailInfo
	ReplaceDetailInfo    []DispensingDetailInfo
	RegisterDetailInfo   []DispensingDetailInfo
	StopTimeStr          string
	StopReason           string
	FinishTimeStr        string
	FinishRemark         string
}

type FormInfo struct {
	Value interface{}
	Label string
}
type ModifyInfo struct {
	Label    string
	OldValue interface{}
	NewValue interface{}
}

type FactorDetailInfo struct {
	Label       string
	Value       string
	ActualValue string
}

type DispensingDetailInfo struct {
	VisitCycleName                string //VisitInfo.Name
	OutSize                       bool   //Period.OutSize
	DoseLevelName                 string //发放水平 DoseLevel.Name
	MedicineDetailInfo            []MedicineDetailInfo
	OperationTimeStr              string
	Operator                      string
	ReDispensationRemark          string //补发
	UnscheduledDispensationRemark string //方式外
	Remark                        string
}

type MedicineDetailInfo struct {
	Label          string
	Number         string
	OldNumber      string //替换对应被替换研究产品，登记对应系统发放研究产品
	BatchNumber    string
	ExpirationDate string
	DTP            *int //发放方式 0 中心（中心库存） 1中心（直接寄送受试者） 2库房（直接寄送受试者）
	DoseForm       string
	UseFormulas    string
	OrderNumber    string
}
