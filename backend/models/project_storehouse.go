package models

import "go.mongodb.org/mongo-driver/bson/primitive"

// ProjectStorehouse ..
type ProjectStorehouse struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID    primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID `json:"envId" bson:"env_id"`
	StorehouseID  primitive.ObjectID `json:"storehouseId" bson:"storehouse_id"`
	Alert         []Alert            `json:"alert" bson:"alert"`
	MedicineInfos []MedicineInfo     `json:"medicineInfos" bson:"medicine_infos"`
	Contacts      string             `json:"contacts"`
	Phone         string             `json:"phone"`
	Email         string             `json:"email"`
	Address       string             `json:"address"`
	Deleted       int                `json:"deleted" bson:"deleted"`          //1:已删除, 2: 已启用
	Connected     bool               `json:"connected" bson:"connected"`      //是否对接物流仓库 false:否 true:是
	Supplier      string             `json:"supplier" bson:"supplier"`        //shengsheng 生生物流;catalent catalent;baicheng 百诚物流
	NotIncluded   bool               `json:"notIncluded" bson:"not_included"` //订单中不包含隔离药物
	Country       []string           `json:"country" bson:"country"`
	IsUsed        bool               `json:"isUsed" bson:"is_used"` //prod改库复制的是否已使用
	Meta          `json:"meta"`
}
type MedicineInfo struct {
	MedicineName     string `json:"medicineName" bson:"medicine_name"`         //研究产品名称
	ValidityReminder int    `json:"validityReminder" bson:"validity_reminder"` //有效期提醒
	Alert            int    `json:"alert" bson:"alert"`                        //警戒值
}

// ProjectDepotSetting ..
type ProjectDepotSetting struct {
	ID            primitive.ObjectID         `json:"id" bson:"_id"`
	CustomerID    primitive.ObjectID         `json:"customerId" bson:"customer_id"`
	ProjectID     primitive.ObjectID         `json:"projectId" bson:"project_id"`
	EnvironmentID primitive.ObjectID         `json:"envId" bson:"env_id"`
	Medicines     []MedicineValidityReminder `json:"medicines" bson:"medicines"`
	Meta          `json:"meta"`
}

// MedicineValidityReminder ..
type MedicineValidityReminder struct {
	MedicineName     string `json:"medicineName" bson:"medicine_name"`         //研究产品名称
	ValidityReminder int    `json:"validityReminder" bson:"validity_reminder"` //有效期提醒
}

// Alert ..
type Alert struct {
	MedicineName string `json:"medicineName" bson:"medicine_name"` //研究产品名称
	Value        int    `json:"value"`                             //警戒值
}

// 参数
type ProjectStorehouseParameter struct {
	ID            primitive.ObjectID `json:"id"`
	CustomerID    primitive.ObjectID `json:"customerId"`
	ProjectID     primitive.ObjectID `json:"projectId"`
	EnvironmentID primitive.ObjectID `json:"envId"`
	CohortID      primitive.ObjectID `json:"cohortId"`
	StorehouseID  primitive.ObjectID `json:"storehouseId"`
	Alert         []Alert            `json:"alert"`
	Contacts      string             `json:"contacts"`
	Phone         string             `json:"phone"`
	Email         string             `json:"email"`
	Address       string             `json:"address"`
	Deleted       int                `json:"deleted"`     //1:已删除, 2: 已启用
	Connected     bool               `json:"connected"`   //是否对接物流仓库 false:否 true:是
	Supplier      string             `json:"supplier"`    //shengsheng 生生物流;catalent catalent;baicheng 百诚物流
	NotIncluded   bool               `json:"notIncluded"` //订单中不包含隔离药物
	Country       []string           `json:"country"`
	Meta          `json:"meta"`
}

// 参数
type ProjectDepotSettingParameter struct {
	ID            primitive.ObjectID         `json:"id"`
	CustomerID    primitive.ObjectID         `json:"customerId"`
	ProjectID     primitive.ObjectID         `json:"projectId"`
	EnvironmentID primitive.ObjectID         `json:"envId"`
	CohortID      primitive.ObjectID         `json:"cohortId"`
	Medicines     []MedicineValidityReminder `json:"medicines"`
	Meta          `json:"meta"`
}

// IdMapping ..
type IdMapping struct {
	OldID primitive.ObjectID `json:"oldId" bson:"old_id"`
	NewID primitive.ObjectID `json:"newId" bson:"new_id"`
}

type DepotBatchGroupAlarm struct {
	Depot primitive.ObjectID `json:"depot" bson:"depot"`
	Info  []BatchGroupAlarm  `bson:"batch_group_alarm" json:"batchGroupAlarm" `
}

type BatchGroupAlarm struct {
	Batch string       `json:"batch" bson:"batch"`
	Info  []GroupAlarm `json:"info" bson:"info"`
}

type GroupAlarm struct {
	Group    string `json:"group" bson:"group"`       //
	Warn     int    `json:"warn" bson:"warn"`         //警戒人数
	Capacity int    `json:"estimate" bson:"estimate"` // 上限人数
}

type DepotBatchGroup struct {
	ID              primitive.ObjectID     `json:"id" bson:"_id"`
	CustomerID      primitive.ObjectID     `json:"customerId" bson:"customer_id"`
	ProjectID       primitive.ObjectID     `json:"projectId" bson:"project_id"`
	EnvironmentID   primitive.ObjectID     `json:"envId" bson:"env_id"`
	BatchGroupAlarm []DepotBatchGroupAlarm `json:"info" bson:"info"`
}

type WarnCapacityActual struct {
	Warn     int //警戒人数
	Capacity int // 上限人数
	Actual   int // 上限人数
}

type ProjectStorehouseInfo struct {
	ProjectStorehouse `bson:",inline"`
	Storehouse        Storehouse `bson:"storehouse"`
}
