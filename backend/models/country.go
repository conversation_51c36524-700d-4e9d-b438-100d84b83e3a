package models

type Country struct {
	Cn    string  `json:"cn"`
	En    string  `json:"en"`
	Ko    string  `json:"ko"`
	Code  string  `json:"code"`
	State []State `json:"state"`
}
type State struct {
	Cn   string `json:"cn"`
	En   string `json:"en"`
	Ko   string `json:"ko"`
	Code string `json:"code"`
	City []City `json:"city"`
}

type City struct {
	Cn   string `json:"cn"`
	En   string `json:"en"`
	Ko   string `json:"ko"`
	Code string `json:"code"`
}
