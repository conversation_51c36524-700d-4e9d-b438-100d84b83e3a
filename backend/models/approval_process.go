package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ApprovalProcess ...
type ApprovalProcess struct {
	Number                  string             `json:"number" bson:"number"`                                     //审批编号
	Name                    string             `json:"name" bson:"name"`                                         //任务标题名称
	Type                    int                `json:"type" bson:"type"`                                         //审批任务类型：1研究中心订单申请 2:紧急揭盲审批申请 3：Pv揭盲审批申请 4研究产品揭盲申请， 5发药申请
	Status                  int                `json:"status" bson:"status"`                                     //任务状态 0 未开始 1已完成
	EstimatedCompletionTime time.Duration      `json:"estimatedCompletionTime" bson:"estimated_completion_time"` //预期完成时间
	ApplicationTime         time.Duration      `json:"applicationTime" bson:"application_time"`                  //申请时间
	ApplicationBy           primitive.ObjectID `json:"applicationBy" bson:"application_by"`                      //申请人
	ApplicationByEmail      string             `json:"applicationByEmail" bson:"application_by_email"`           //申请人邮箱
	ApplicationRoleID       primitive.ObjectID `json:"applicationRoleId" bson:"application_role_id"`             //申请人角色
	ApprovalTime            time.Duration      `json:"approvalTime" bson:"approval_time"`                        //审批时间(实际完成时间)
	ApprovalBy              primitive.ObjectID `json:"approvalBy" bson:"approval_by"`                            //审批人
	ApprovalByEmail         string             `json:"approvalByEmail" bson:"approval_by_email"`                 //审批人邮箱
	ApprovalStatus          int                `json:"approvalStatus" bson:"approval_status"`                    //审批状态 0提交申请 1已通过 2已拒绝
	Reason                  string             `json:"reason" bson:"reason"`                                     //拒绝原因
	// Msg                     string             `json:"msg" bson:"msg"`                                           //发送的短信内容
	// SmsUsers                []SmsUser          `json:"smsUsers" bson:"sms_users"`                                //发送短信的手机号
	// Lang                    string             `json:"lang" bson:"lang"`                                         //记录申请时候使用的语言， 发邮件需要根据申请的语言发邮件
}

type OrderAddTask struct {
	ID              primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID      primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID       primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID   primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID        primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	ProjectSiteID   primitive.ObjectID `json:"projectSiteID" bson:"project_site_id"`
	App             int                `json:"app" bson:"app"` //创建方式：0:web 1:app
	ApprovalProcess `json:",inline" bson:",inline"`
	Data            OrderAddInfo       `json:"data" bson:"data"`
	UnblindingData  UnblindingDataInfo `json:"unblindingData" bson:"unblinding_data"`
	RoleID          primitive.ObjectID `json:"roleId" bson:"role_id"`
}

type UnblindingDataInfo struct {
	SubjectID           primitive.ObjectID `json:"subjectId" bson:"subject_id"`
	SubjectNumber       string             `json:"subjectNumber" bson:"subject_number"`              //受试者编号
	SubjectRandomNumber string             `json:"subjectRandomNumber" bson:"subject_random_number"` //受试者随机号
	Number              string             `json:"number" bson:"number"`                             //审批编号
	MedicineID          primitive.ObjectID `json:"medicineId" bson:"medicine_id"`                    //研究产品编号
	DispensingID        primitive.ObjectID `json:"dispensingId" bson:"dispensing_id"`                //访视ID
	MedicineNumber      string             `json:"medicineNumber" bson:"medicine_number"`            //研究产品编号
	Status              int                `json:"status" bson:"status"`                             //审批状态 0提交申请 1已通过 2已拒绝
	ApprovalType        int                `json:"approvalType" bson:"approval_type"`                //审批类型 1审批确认（短信、审批流程） 2揭盲码
	ApplicationTime     time.Duration      `json:"applicationTime" bson:"application_time"`          //申请时间
	ApplicationBy       primitive.ObjectID `json:"applicationBy" bson:"application_by"`              //申请人
	ApprovalTime        time.Duration      `json:"approvalTime" bson:"approval_time"`                //审批时间
	ApprovalBy          primitive.ObjectID `json:"approvalBy" bson:"approval_by"`                    //审批人
	ApplicationByEmail  string             `json:"applicationByEmail" bson:"application_by_email"`   //申请人
	ApprovalByEmail     string             `json:"approvalByEmail" bson:"approval_by_email"`         //审批人
	ReasonStr           string             `json:"reasonStr" bson:"reason_str"`                      //紧急揭盲原因
	Remark              string             `json:"remark" bson:"remark"`                             //备注
	RejectReason        string             `json:"rejectReason" bson:"reject_reason"`                //拒绝原因
}

type UnblindingDataInfoShow struct {
	SubjectID           primitive.ObjectID `json:"subjectId" bson:"subject_id"`
	SubjectNumber       string             `json:"subjectNumber" bson:"subject_number"`              //受试者编号
	SubjectRandomNumber string             `json:"subjectRandomNumber" bson:"subject_random_number"` //受试者随机号
	Number              string             `json:"number" bson:"number"`                             //审批编号
	MedicineID          primitive.ObjectID `json:"medicineId" bson:"medicine_id"`                    //研究产品ID
	DispensingID        primitive.ObjectID `json:"dispensingId" bson:"dispensing_id"`                //访视ID
	MedicineNumber      string             `json:"medicineNumber" bson:"medicine_number"`            //研究产品编号
	Status              int                `json:"status" bson:"status"`                             //审批状态 0提交申请 1已通过 2已拒绝
	ApprovalType        int                `json:"approvalType" bson:"approval_type"`                //审批类型 1审批确认（短信、审批流程） 2揭盲码
	ApplicationTime     time.Duration      `json:"applicationTime" bson:"application_time"`          //申请时间
	ApplicationBy       primitive.ObjectID `json:"applicationBy" bson:"application_by"`              //申请人
	ApprovalTime        time.Duration      `json:"approvalTime" bson:"approval_time"`                //审批时间
	ApprovalBy          primitive.ObjectID `json:"approvalBy" bson:"approval_by"`                    //审批人
	ApplicationByEmail  string             `json:"applicationByEmail" bson:"application_by_email"`   //申请人
	ApprovalByEmail     string             `json:"approvalByEmail" bson:"approval_by_email"`         //审批人
	ReasonStr           string             `json:"reasonStr" bson:"reason_str"`                      //紧急揭盲原因
	Remark              string             `json:"remark" bson:"remark"`                             //备注
	RejectReason        string             `json:"rejectReason" bson:"reject_reason"`                //拒绝原因
	ApprovalUser        []ApprovalUser     `json:"approvalUser" `
}

type OrderAddInfo struct {
	SendID              primitive.ObjectID   `json:"sendId" bson:"send_id"`                            //起运地
	ReceiveID           primitive.ObjectID   `json:"receiveId" bson:"receive_id"`                      //目的地
	Mode                int                  `json:"mode" bson:"mode"`                                 //补充方式
	DrugNames           []string             `json:"drugNames" bson:"drugNames"`                       //研究产品名称
	SupplyID            primitive.ObjectID   `json:"supplyId" bson:"supply_id"`                        //供应计划
	Contacts            string               `json:"contacts"`                                         // 联系人
	Phone               string               `json:"phone"`                                            // 联系方式
	Email               string               `json:"email"`                                            // 邮箱
	Address             string               `json:"address"`                                          // 地址
	SupplyCount         int                  `json:"supplyCount" bson:"supply_count"`                  // 供应量总和
	BlindCount          int                  `json:"blindCount" bson:"blind_count"`                    //供应单品总数（仅盲态研究产品）                                    //盲态单品总数
	OpenDrugCount       []OrderDrugNames     `json:"openDrugCount" bson:"open_drug_count"`             //开放药物
	DetailData          []DetailOrderAddInfo `json:"detailData" bson:"detail_data"`                    //研究产品
	OtherDetailData     []DetailOrderAddInfo `json:"otherDetailData" bson:"other_detail_data"`         //研究产品
	ExpectedArrivalTime string               `json:"expectedArrivalTime" bson:"expected_arrival_time"` //期望送达时间
	MedicinesPackage    []MedicinePackage    `json:"medicinesPackage" bson:"medicines_package"`        //研究产品名称运输方式
}

type OrderAddTaskShow struct {
	ID              primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID      primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID       primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID   primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID        primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	ApprovalProcess `json:",inline" bson:",inline"`
	Data            OrderAddInfoShow       `json:"data" bson:"data"`
	UnblindingData  UnblindingDataInfoShow `json:"unblindingData" bson:"unblinding_data"`
	AttributeInfo   AttributeInfo          `json:"attributeInfo" bson:"attribute_info"`
	Cohort          Cohort                 `json:"cohort"`
}

type OrderAddInfoShow struct {
	SendID              primitive.ObjectID       `json:"sendId" bson:"send_id"`                            //起运地
	ReceiveID           primitive.ObjectID       `json:"receiveId" bson:"receive_id"`                      //目的地
	Mode                int                      `json:"mode" bson:"mode"`                                 //补充方式
	DrugNames           []string                 `json:"drugNames" bson:"drugNames"`                       //研究产品名称
	SupplyID            primitive.ObjectID       `json:"supplyId" bson:"supply_id"`                        //供应计划
	SupplyCount         int                      `json:"supplyCount" bson:"supplyCount"`                   //供应量总和
	Contacts            string                   `json:"contacts"`                                         // 联系人
	Phone               string                   `json:"phone"`                                            // 联系方式
	Email               string                   `json:"email"`                                            // 邮箱
	Address             string                   `json:"address"`                                          // 地址
	BlindCount          int                      `json:"blindCount" bson:"blind_count"`                    //供应单品总数（仅盲态研究产品）                            //盲态单品总数
	OpenDrugCount       []OrderDrugNames         `json:"openDrugCount" bson:"open_drug_count"`             //开放药物
	DetailData          []DetailOrderAddInfoShow `json:"detailData" bson:"detail_data"`                    //研究产品
	OtherDetailData     []DetailOrderAddInfoShow `json:"otherDetailData" bson:"other_detail_data"`         //研究产品
	ExpectedArrivalTime string                   `json:"expectedArrivalTime" bson:"expected_arrival_time"` //期望送达时间
}

type DetailOrderAddInfoShow struct {
	Name           string `json:"name"`                      //研究产品名称
	ExpirationDate string `json:"expirationDate"`            //有效期
	BatchNumber    string `json:"batchNumber"`               //批次号
	PackageMethod  bool   `json:"packageMethod"`             //包装方式
	Count          string `json:"count"`                     //数量
	UseCount       string `json:"useCount" bson:"use_count"` //库存数量
}

type DetailOrderAddInfo struct {
	Name           string `json:"name"`                                //研究产品名称
	ExpirationDate string `json:"expirationDate"`                      //有效期
	BatchNumber    string `json:"batchNumber"`                         //批次号
	Count          int    `json:"count"`                               //数量
	UseCount       int    `json:"useCount" bson:"use_count"`           //库存数量
	PackageMethod  bool   `json:"packageMethod" bson:"package_method"` //包装方式
}

type ApprovalNumber struct {
	ID         primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID  primitive.ObjectID `json:"projectId" bson:"project_id"`
	Number     string             `json:"number" bson:"number"`
}
