package models

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Customer ...
type Customer struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	Name        string             `json:"name" validate:"required"`
	Email       string             `json:"email" validate:"required,email"`
	Phone       string             `json:"phone"`
	Description string             `json:"description"`
	Meta        `json:"meta"`
}

// SelectCustomer 用于客户下拉框
type SelectCustomer struct {
	ID    string   `json:"id"`
	Name  string   `json:"name"`
	Types []string `json:"types"` // type: admin 客户管理员  project 项目管理员
}
