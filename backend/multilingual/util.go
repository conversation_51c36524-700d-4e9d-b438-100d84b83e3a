package multilingual

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"context"
	"github.com/duke-git/lancet/v2/slice"
)

func OperationLogTransKeyGather(ctx context.Context, operationLogs []models.OperationLogVo) []interface{} {
	transKeys := make([]interface{}, 0)
	slice.ForEach(operationLogs, func(index int, operationLog models.OperationLogVo) {
		slice.ForEach(operationLog.Fields, func(index int, item models.OperationLogFieldGroup) {
			transKeys = append(transKeys, locales.TrStash(ctx, item.TranKey))
		})
	})
	return transKeys
}
