package multilingual

import (
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"context"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"reflect"
)

func LanguageId(c context.Context) (primitive.ObjectID, error) {
	switch ctx := c.(type) {
	case *gin.Context:
		languageId, err := primitive.ObjectIDFromHex(ctx.GetHeader("languageId"))
		if err != nil {
			return primitive.NilObjectID, err
		}
		return languageId, nil
	default:
		return primitive.NilObjectID, errors.New("not languageId")
	}
}

func Tr(ctx context.Context, key string, data ...interface{}) string {
	// 加载数据库翻译
	translations := loadTranslationsFromDB(ctx, []string{key})
	return translation(ctx, &locales.TrData{Key: key, Data: data}, translations)
}

func TrBatch(ctx context.Context, data []interface{}) []interface{} {
	// 加载数据库翻译
	translations := loadTranslationsFromDB(ctx, getTrKeys(data))
	for i, item := range data {
		if isTrData(item) {
			trData := item.(locales.TrData)
			data[i] = translation(ctx, &trData, translations)
		}
	}
	return data
}

func TrBatchMap(c context.Context, data ...[]interface{}) map[string]string {
	result := make(map[string]string)
	allData := slice.Union(data...)
	// 加载数据库翻译
	translations := loadTranslationsFromDB(c, getTrKeys(allData))
	for _, item := range allData {
		if isTrData(item) {
			trData := item.(locales.TrData)
			result[trData.Key] = translation(c, &trData, translations)
		}
	}
	return result
}

func TrDoubleBatch(ctx context.Context, data [][]interface{}) [][]interface{} {
	// 加载数据库翻译
	fields := make([]interface{}, 0)
	slice.ForEach(data, func(index int, item []interface{}) {
		fields = append(fields, item...)
	})
	translations := loadTranslationsFromDB(ctx, getTrKeys(fields))
	for i, fieldItems := range data {
		for i2, item := range fieldItems {
			if isTrData(item) {
				trData := item.(locales.TrData)
				data[i][i2] = translation(ctx, &trData, translations)
			}
		}
	}
	return data
}

func isTrData(item interface{}) bool {
	return reflect.TypeOf(item).Name() == reflect.TypeOf(locales.TrData{}).Name()
}

func getTrKeys(data []interface{}) []string {
	keys := slice.Map(data, func(index int, item interface{}) string {
		if isTrData(item) {
			return item.(locales.TrData).Key
		}
		return ""
	})
	return slice.Unique(slice.Filter(keys, func(index int, item string) bool {
		return item != ""
	}))
}

func translation(ctx context.Context, item *locales.TrData, translations map[string]string) string {
	translationText := ""
	if content, ok := translations[item.Key]; ok && content != "" {
		translationText = locales.TrMessage(ctx, item.Key, content, item.Data...)
	} else {
		translationText = locales.Tr(ctx, item.Key, item.Data...)
	}
	return locales.ReplaceTemplate(translationText, translations)
}

// 加载翻译
func loadTranslationsFromDB(ctx context.Context, keys []string) map[string]string {
	// 抽取模板占位符，非动态变量
	placeholders := locales.ExtractPlaceholders(slice.Map(keys, func(index int, key string) string {
		return locales.Tr(ctx, key)
	}))
	// 需要翻译的key，包括key本身及key内容中包含的翻译占位符
	translationKeys := slice.Union(keys, placeholders)
	// 翻译结果
	translations := make(map[string]string)
	languageId, err := LanguageId(ctx)
	// languageId, err := primitive.ObjectIDFromHex("67ed4defc73817c202175fc6")
	if err == nil {
		translations = database.MultiLanguageTranslateFindByKeys(ctx, languageId, translationKeys)
	}
	// 占位符未翻译则使用系统默认值
	slice.ForEach(placeholders, func(index int, key string) {
		if content, ok := translations[key]; !ok || content == "" {
			translations[key] = locales.Tr(ctx, key)
		}
	})
	return translations
}
