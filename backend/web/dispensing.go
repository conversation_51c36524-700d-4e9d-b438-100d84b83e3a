package web

import (
	"clinflash-irt/models"
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// DispensingController struct
type DispensingController struct {
	s service.DispensingService
}

// GetVisit 查询访视周期列表
func (c *DispensingController) GetVisit(ctx *gin.Context) {
	customerID := ctx.Query("customerId")
	projectID := ctx.Query("projectId")
	envID := ctx.Query("envId")
	cohortID := ctx.Query("cohortId")
	subjectId := ctx.Query("subjectId")
	roleId := ctx.Query("roleId")
	status := ctx.Query("status")
	data, err := c.s.GetVisit(ctx, customerID, projectID, envID, cohortID, subjectId, status, roleId)
	tools.Response(ctx, err, data)
}

// AddDispensingVisit 发药
func (c *DispensingController) AddDispensingVisit(ctx *gin.Context) {
	var data map[string]interface{}
	response := map[string]interface{}{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	res, order, err := c.s.AddDispensingVisit(ctx, data, 0, "")
	response["dispensing_info"] = res
	response["order_info"] = order
	tools.Response(ctx, err, response)
}

// GetDispensing 发药记录
func (c *DispensingController) GetDispensing(ctx *gin.Context) {
	subjectID := ctx.Query("id")
	roleId := ctx.Query("roleId")
	data, err := c.s.GetDispensing(ctx, subjectID, roleId)
	tools.Response(ctx, err, data)
}

//// PatchAppDispenseTask app发放任务
//func (c *DispensingController) PatchAppDispenseTask(ctx *gin.Context) {
//	subjectID := ctx.Query("id")
//	roleId := ctx.Query("roleId")
//	data, err := c.s.PatchAppDispenseTask(ctx, subjectID, roleId)
//	tools.Response(ctx, err, data)
//}

// PatchAppDispenseTaskFinish 完成发药-修改app发药任务的完成状态
func (c *DispensingController) PatchAppDispenseTaskFinish(ctx *gin.Context) {
	ID := ctx.Query("id")
	err := c.s.PatchAppDispenseTaskFinish(ctx, ID)
	tools.Response(ctx, err)
}

// PatchAppDispenseTaskVoided 不参加访视-删除已经产生的app发药任务
func (c *DispensingController) PatchAppDispenseTaskVoided(ctx *gin.Context) {
	ID := ctx.Query("id")
	err := c.s.PatchAppDispenseTaskVoided(ctx, ID)
	tools.Response(ctx, err)
}

//// PatchAppDispenseTaskAgainPush 研究产品管理发布-更新app发放任务
//func (c *DispensingController) PatchAppDispenseTaskAgainPush(ctx *gin.Context) {
//	customerID := ctx.Query("customerId")
//	projectID := ctx.Query("projectId")
//	envID := ctx.Query("envId")
//	cohortID := ctx.Query("cohortId")
//	roleID := ctx.Query("roleId")
//	err := c.s.PatchAppDispenseTaskAgainPush(ctx, customerID, projectID, envID, cohortID, roleID)
//	tools.Response(ctx, err)
//}

// GetAppDispensing 发药记录
func (c *DispensingController) GetAppDispensing(ctx *gin.Context) {
	subjectID := ctx.Query("id")
	roleId := ctx.Query("roleId")
	data, err := c.s.GetAppDispensing(ctx, subjectID, roleId)
	tools.Response(ctx, err, data)
}

// GetNonBlindDispensing 发药记录(非盲)
func (c *DispensingController) GetNonBlindDispensing(ctx *gin.Context) {
	subjectID := ctx.Query("id")
	data, err := c.s.GetNonBlindDispensing(ctx, subjectID)
	tools.Response(ctx, err, data)
}

// ReplaceDrug 替换研究产品
func (c *DispensingController) ReplaceDrug(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	res, err := c.s.ReplaceDrug(ctx, data, "")
	tools.Response(ctx, err, res)
}

// ReissueDispensing 单药品补发
func (c *DispensingController) ReissueDispensing(ctx *gin.Context) {
	var data map[string]interface{}
	response := map[string]interface{}{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	res, app, err := c.s.ReissueDispensing(ctx, data, 0, "")
	response["resOrderInfo"] = res
	response["app"] = app
	tools.Response(ctx, err, response)
}

// Cancel 单药品补发
func (c *DispensingController) Cancel(ctx *gin.Context) {
	id := ctx.Query("id")
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.Cancel(ctx, id, data["reason"].(string))
	tools.Response(ctx, err)
}

// ExportDispensing 发药记录导出
func (c *DispensingController) ExportDispensing(ctx *gin.Context) {
	customerId := ctx.Query("customerId")
	projectId := ctx.Query("projectId")
	envId := ctx.Query("envId")
	err := c.s.ExportDispensing(ctx, customerId, projectId, envId)
	if err != nil {
		tools.Response(ctx, err)
	}

}

// UpdatePrintStatus 发药记录导出
func (c *DispensingController) UpdatePrintStatus(ctx *gin.Context) {
	ID := ctx.Query("id")
	err := c.s.UpdatePrintStatus(ctx, ID)
	tools.Response(ctx, err)
}

// RetrievalDrug 研究产品取回
func (c *DispensingController) RetrievalDrug(ctx *gin.Context) {
	ID := ctx.Query("id")
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.RetrievalDrug(ctx, ID, data)
	tools.Response(ctx, err)
}

// InvalidDispensing 不参加访视
func (c *DispensingController) InvalidDispensing(ctx *gin.Context) {
	ID := ctx.Query("id")
	roleId := ctx.Query("roleId")
	remark := ctx.Query("remark")
	err := c.s.InvalidDispensing(ctx, ID, roleId, remark)
	tools.Response(ctx, err)
}

// RegisterDispensing 登记实际药物
func (c *DispensingController) RegisterDispensing(ctx *gin.Context) {
	var data models.ParamRealDispensing
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	res, err := c.s.RegisterDispensing(ctx, &data)
	tools.Response(ctx, err, res)
}

// GetSubjectStatusAndRoom 发药页面实时获取受试者状态和房间号
func (c *DispensingController) GetSubjectStatusAndRoom(ctx *gin.Context) {
	ID := ctx.Query("id")
	roleID := ctx.Query("role_id")
	data, err := c.s.GetSubjectStatusAndRoom(ctx, ID, roleID)
	tools.Response(ctx, err, data)
}

// GetDispensingRoomInfo  房间号页面实时获取房间号药物
func (c *DispensingController) GetDispensingRoomInfo(ctx *gin.Context) {
	ID := ctx.Query("id")
	roleId := ctx.Query("roleId")
	data, err := c.s.GetDispensingRoomInfo(ctx, ID, roleId)
	tools.Response(ctx, err, data)
}

// PostRecordRoomInfo 发药页面实时获取受试者状态和房间号
func (c *DispensingController) PostRecordRoomInfo(ctx *gin.Context) {
	ID := ctx.Query("id")
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.PostRecordRoomInfo(ctx, ID, data)
	tools.Response(ctx, err)
}

// ExportRoomRecord 房间号查看记录报表
func (c *DispensingController) ExportRoomRecord(ctx *gin.Context) {
	envID := ctx.Query("envId")
	projectSiteID := ctx.DefaultQuery("projectSiteId", "")
	cohortID := ctx.DefaultQuery("cohortId", "")
	roleID := ctx.Query("roleId")
	err := c.s.ExportRoomRecord(ctx, envID, cohortID, projectSiteID, roleID)
	if err != nil {
		tools.Response(ctx, err)
	}
}

// UpdateDispensingVisitWithDTP DTP访视发药
func (c *DispensingController) UpdateDispensingVisitWithDTP(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	dispensingID := data["dispensing_id"].(string)
	visitID := data["visit_id"].(string)
	visitLabelIDs := data["visit_label_id"].([]interface{})
	roleID := data["role_id"].(string)

	remark := ""
	if data["remark"] != nil {
		remark = data["remark"].(string)
	}

	res, err := c.s.UpdateDispensingVisitWithDTP(ctx, dispensingID, visitID, visitLabelIDs, remark, roleID)
	if err != nil {
		tools.Response(ctx, err)
		return
	}
	tools.Response(ctx, err, res)

}

// AddDispensingVisitWithDTP DTP访视外发药
func (c *DispensingController) AddDispensingVisitWithDTP(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	subject := data["subject_id"].(string)
	visitID := data["visit_id"].(string)
	visitLabelIDs := data["visit_label_id"].([]interface{})
	reason := data["reason"].(string)
	roleID := data["role_id"].(string)
	remark := ""
	if data["remark"] != nil {
		remark = data["remark"].(string)
	}
	res, err := c.s.AddDispensingVisitWithDTP(ctx, subject, visitID, visitLabelIDs, reason, remark, roleID)
	if err != nil {
		tools.Response(ctx, err)
		return
	}
	tools.Response(ctx, err, res)

}

// ReissueDispensingWithDTP DTP补发
func (c *DispensingController) ReissueDispensingWithDTP(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	res, err := c.s.ReissueDispensingWithDTP(ctx, data)
	if err != nil {
		tools.Response(ctx, err)
		return
	}
	tools.Response(ctx, err, res)

}

// GetReissueMedicineName 获取补发的药物名称
func (c *DispensingController) GetReissueMedicineName(ctx *gin.Context) {
	subjectID := ctx.Query("subject_id")
	dispensingID := ctx.Query("visit_id")
	roleID := ctx.Query("role_id")
	res, err := c.s.GetReissueMedicineName(ctx, subjectID, dispensingID, roleID)
	tools.Response(ctx, err, res)
}

// GetFormula 获取公式计算药物配置
func (c *DispensingController) GetFormula(ctx *gin.Context) {
	envID := ctx.Query("env_id")
	cohortID := ctx.Query("cohort_id")
	subjectID := ctx.Query("subject_id")
	visitOID := ctx.Query("visit_id")
	roleID := ctx.Query("role_id")
	dose := ctx.DefaultQuery("dose", "")
	res, err := c.s.GetFormula(ctx, envID, cohortID, subjectID, visitOID, roleID, dose)
	tools.Response(ctx, err, res)
}

// GetFormulaMedicine ...
func (c *DispensingController) GetFormulaMedicine(ctx *gin.Context) {
	var data models.FormulaReq
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	res, err := c.s.GetFormulaRes(ctx, data)
	tools.Response(ctx, err, res)
}

// GetLabelMedicine ...
func (c *DispensingController) GetLabelMedicine(ctx *gin.Context) {
	subjectID := ctx.Query("subject_id")
	visitOID := ctx.Query("visit_id")
	outSize := ctx.Query("out_size")
	res, err := c.s.GetLabelMedicine(ctx, subjectID, visitOID, outSize)
	tools.Response(ctx, err, res)
}

// GetWebLabelMedicines ...
func (c *DispensingController) GetWebLabelMedicines(ctx *gin.Context) {
	subjectID := ctx.Query("subjectId")
	visitOID := ctx.Query("visitId")
	dispensingID := ctx.Query("id")
	res, err := c.s.GetWebLabelMedicines(ctx, subjectID, visitOID, dispensingID)
	tools.Response(ctx, err, res)
}

// GetAppAddDispensingOperationDTP ...
func (c *DispensingController) GetAppAddDispensingOperationDTP(ctx *gin.Context) {
	subjectID := ctx.Query("subjectId")
	visitOID := ctx.Query("visitId")
	roleID := ctx.Query("roleId")
	res, err := c.s.GetAppAddDispensingOperationDTP(ctx, subjectID, visitOID, roleID)
	tools.Response(ctx, err, res)
}

// DispensingResume ...
func (c *DispensingController) DispensingResume(ctx *gin.Context) {
	dispensingID := ctx.Query("dispensingId")
	roleId := ctx.Query("roleId")
	_ = c.s.DispensingResume(ctx, dispensingID, roleId)
	tools.Response(ctx, nil)
}

// DoseInfo ...
func (c *DispensingController) DoseInfo(ctx *gin.Context) {
	visitID := ctx.Query("visitId")
	subjectID := ctx.Query("subjectId")
	data, err := c.s.DoseInfo(ctx, visitID, subjectID)
	tools.Response(ctx, err, data)
}

// DoseInfoRes ...
func (c *DispensingController) DoseInfoRes(ctx *gin.Context) {
	var data models.ReqDose
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	res, err := c.s.DoseInfoRes(ctx, data)
	tools.Response(ctx, err, res)
}

// StartFollowUpVisits
func (c *DispensingController) StartFollowUpVisits(ctx *gin.Context) {
	var data map[string]interface{}
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	err := c.s.StartFollowUpVisits(ctx, data)
	tools.Response(ctx, err)
}

// DispensingConfirmTable ...
func (c *DispensingController) DispensingConfirmTable(ctx *gin.Context) {
	var data models.ReqDispensingConfirmTable
	_ = ctx.ShouldBindBodyWith(&data, binding.JSON)
	res, err := c.s.DispensingConfirmTable(ctx, data)
	tools.Response(ctx, err, res)
}

// GetMedicineUnBlind ...
func (c *DispensingController) GetMedicineUnBlind(ctx *gin.Context) {
	ID := ctx.Query("id")
	subjectID := ctx.Query("subjectId")
	data, err := c.s.GetMedicineUnBlind(ctx, subjectID, ID)
	tools.Response(ctx, err, data)
}

// IPUnblindingApplication ...
func (c *DispensingController) IPUnblindingApplication(ctx *gin.Context) {
	data, err := c.s.IPUnblindingApplication(ctx)
	tools.Response(ctx, err, data)
}

// UnblindingApproval ...
func (c *DispensingController) UnblindingApproval(ctx *gin.Context) {
	data, err := c.s.UnblindingApproval(ctx)
	tools.Response(ctx, err, data)
}
