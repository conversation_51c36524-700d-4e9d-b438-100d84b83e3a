package task

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"strconv"
	"strings"
	"time"
)

// LayerCheckTask 分层检查
func LayerCheckTask(needMail bool) (map[string]interface{}, error) {
	defer tools.DeferReturn("LayerCheckTask")
	var projects []map[string]interface{}
	cursor, _ := tools.Database.Collection("project").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"status": bson.M{"$ne": 2}}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.name": "PROD"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$envs.cohorts", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$project", Value: bson.M{
			"_id":      1,
			"info":     1,
			"envs":     1,
			"match_id": bson.M{"$ifNull": bson.A{"$envs.cohorts.id", "$envs.id"}},
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "attribute",
			"let": bson.M{
				"match_id": "$match_id",
			},
			"pipeline": mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"$or": bson.A{
					bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$match_id"}}, "$or": bson.A{bson.M{"cohort_id": primitive.NilObjectID}, bson.M{"cohort_id": bson.M{"$exists": 0}}}},
					bson.M{"$expr": bson.M{"$eq": bson.A{"$cohort_id", "$$match_id"}}},
				}}}},
			},
			"as": "attribute",
		}}},
		{{Key: "$project", Value: bson.M{
			"_id":               1,
			"number":            "$info.number",
			"name":              "$info.name",
			"env_id":            "$envs.id",
			"env":               "$envs.name",
			"cohort_id":         "$envs.cohorts.id",
			"cohort":            "$envs.cohorts.name",
			"institute_layered": bson.M{"$first": "$attribute.info.institute_layered"},
			"country_layered":   bson.M{"$first": "$attribute.info.country_layered"},
		}}},
	})
	err := cursor.All(nil, &projects)
	if err != nil {
		return map[string]interface{}{}, err
	}

	//var mails []models.Mail
	var strArr []string
	for _, project := range projects {
		cohort := ""
		if project["cohort"] != nil {
			cohort = fmt.Sprintf(" cohort/阶段[%s]", project["cohort"])
		}
		// 中心分层
		if project["institute_layered"] != nil && project["institute_layered"].(bool) {
			data, err := checkSite(project)
			if err != nil {
				strArr = append(strArr, fmt.Sprintf("中心分层项目%s [%s] %s,检查失败 %v", project["number"], project["env"], cohort, err))
			}
			if data != nil && len(data) > 0 {
				var blocks []string
				for _, item := range data {
					blocks = append(blocks, strconv.Itoa(int(item["block"].(int32))))
				}
				strArr = append(strArr, fmt.Sprintf("中心分层项目%s [%s] %s,存在区组【%s】不同中心", project["number"], project["env"], cohort, strings.Join(blocks, ",")))

			}
		}

		// 国家分层
		if project["country_layered"] != nil && project["country_layered"].(bool) {
			data, err := checkCountry(project)
			if err != nil {
				strArr = append(strArr, fmt.Sprintf("中心分层项目%s [%s] %s,检查失败 %v", project["number"], project["env"], cohort, err))
			}
			if data != nil && len(data) > 0 {
				var blocks []string
				for _, item := range data {
					blocks = append(blocks, strconv.Itoa(int(item["block"].(int32))))
				}
				strArr = append(strArr, fmt.Sprintf("国家分层项目%s [%s] %s，存在区组[%s]不同国家", project["number"], project["env"], cohort, strings.Join(blocks, ",")))
			}
		}
	}

	if len(strArr) > 0 && needMail {
		var settingConfig map[string]interface{}
		tools.Database.Collection("setting_config").FindOne(nil, bson.M{"key": "corssCheck"}).Decode(&settingConfig)
		var mailArr []string
		for _, item := range settingConfig["data"].(map[string]interface{})["mail"].(primitive.A) {
			mailArr = append(mailArr, item.(string))
		}
		mails := models.Mail{
			ID:           primitive.NewObjectID(),
			Subject:      "cross.check.error",
			ContentData:  map[string]interface{}{"results": strArr},
			To:           mailArr,
			Lang:         "en-US",
			Status:       0,
			CreatedTime:  time.Duration(time.Now().Unix()),
			ExpectedTime: time.Duration(time.Now().Unix()),
			SendTime:     time.Duration(time.Now().Unix()),
			HTML:         "layer_check_en-US_new.html",
		}
		_, err = tools.Database.Collection("mail").InsertOne(nil, mails)
		if err != nil {
			return map[string]interface{}{}, err
		}
	}
	data := map[string]interface{}{}
	data["info"] = strArr
	return data, nil
}

func checkSite(project map[string]interface{}) ([]map[string]interface{}, error) {
	var data []map[string]interface{}
	match := bson.M{"env_id": project["env_id"], "status": bson.M{"$ne": 3}}
	if project["cohort_id"] != nil && project["cohort_id"] != primitive.NilObjectID {
		match["cohort_id"] = project["cohort_id"]
	}
	cursor, err := tools.Database.Collection("random_number").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{"block": "$block", "project_site": "$project_site_id"},
		}}},
		{{Key: "$group", Value: bson.M{
			"_id":   "$_id.block",
			"count": bson.M{"$sum": 1},
		}}},
		{{Key: "$match", Value: bson.M{"count": bson.M{"$gt": 1}}}},
		{{Key: "$project", Value: bson.M{
			"_id":   0,
			"block": "$_id",
			"count": 1,
		}}},
	})
	if err != nil {
		return nil, err
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func checkCountry(project map[string]interface{}) ([]map[string]interface{}, error) {
	var data []map[string]interface{}
	match := bson.M{"env_id": project["env_id"], "status": bson.M{"$ne": 3}}
	if project["cohort_id"] != nil && project["cohort_id"] != primitive.NilObjectID {
		match["cohort_id"] = project["cohort_id"]
	}
	cursor, err := tools.Database.Collection("random_number").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{"block": "$block", "project_site_id": "$project_site_id"},
		}}},
		{{Key: "$group", Value: bson.M{
			"_id":             "$_id.block",
			"count":           bson.M{"$sum": 1},
			"project_site_id": bson.M{"$push": "$_id.project_site_id"},
		}}},
		// 匹配同区组 不同中心
		{{Key: "$match", Value: bson.M{"count": bson.M{"$gt": 1}}}},
		//	关联中心判断 不同中心的国家是否相同
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{Key: "$project", Value: bson.M{
			"block":   "$_id",
			"country": bson.M{"$first": bson.M{"$ifNull": bson.A{"$site.country", bson.A{"NA"}}}},
		}}},
		// 国家条件去重
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{"block": "$block", "country": "$country"},
			//"count":           bson.M{"$sum": 1},
		}}},
		{{Key: "$group", Value: bson.M{
			"_id":   bson.M{"block": "$_id.block"},
			"count": bson.M{"$sum": 1},
			//"country": "",
		}}},
		{{Key: "$match", Value: bson.M{"count": bson.M{"$gt": 1}}}},
		{{Key: "$project", Value: bson.M{
			"_id":   0,
			"block": "$_id.block",
			"count": 1,
		}}},
	})
	if err != nil {
		return nil, err
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, err
	}
	return data, nil
}
