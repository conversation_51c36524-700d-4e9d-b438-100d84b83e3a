alarm:
    storehouse:
        content: <p>The number of available IP is lower than the warning value, please re-supply in time.IP information ([warehouse name / IP name / Alert quantity / remaining quantity]):{{.drugInfo}}</p>
        content_dual: <p>项目编号:{{.projectNumber}}</p> <p>项目名称:{{.projectName}}</p> <p>项目环境:{{.envName}}</p> <p>库房名称:{{.deportName}}</p> <p>研究产品名称:{{.medicineName}}</p> <p>警戒值:{{.alertValue}}</p> <p>库存研究产品低于警戒值,请及时补充.</p> <p></p> <p>Project number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Depot Name:{{.deportName}}</p> <p>IP Name:{{.medicineName}}</p> <p>Alert Value:{{.alertValue}}</p> <p> The IP quantity in stock is below the alert value, please replenish in time. </p>
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Depot Inventory Alert {{.deportName}}
app:
    unauthorized: App Unauthorized
auth:
    fail: Authentication failed
cohort:
    status:
        complete: Complete
        draft: Draft
        enrollment: Enrollment
        stop: Stop
shipmentMode:
    status:
        set: Quantity
        reSupply: Re-supply
        max: Maximum Buffer
        supplyRatio: Supply Ratio
common:
    operator: Operation
    required: Please enter
    error:
        default: An error occurred processing the request.
        request: Bad request
        not-logged-in: Not logged in
        unauthorized: Unauthorized
        not-found: Unknown request
    checkbox: checkbox
    date: date
    dateTime: Time Selection Box
    delete:
        fail: Deletion failed.
        success: Delete succeed
    duplicated:
        factors: Duplicated factors
        names: Duplicated names
    no: No
    input: input
    inputNumber: inputNumber
    load:
        fail: Loaded failed
        success: Loaded successfully
    nil: "null"
    operation:
        edc:
            dsp:
                fail: IP has dispensed at current visit. Information is returned a second time.
            fail: The subject is randomized
        fail: Operation failed
        success: Operation Succeeded
    radio: radio
    remark: Remark
    save:
        fail: Save failed
        success: Save succeed
    select: select
    switch: switch
    textArea: Multiline text box
    yes: Yes
    update:
        fail: Update failed
        success: Update succeed
    wrong:
        parameters: Parameter error
    country: country
cross:
    check:
        error: Clinflash IRT Exception Notification
customer:
    not:
        exist: The customer does not exist
customers:
    delete:
        admin:
            The current operator does not have admin permission under the customer
            and has no right to operate
        message: There is a user under the customer and cannot be deleted
    duplicated:
        names: The customer name is duplicated
dispense_list_download_name: dispensation report
dispensing:
    approval:
        pending: Dispensation application under approval, please wait
        name:
            main-visit: IP Dispense Application
            out-visit: IP Dispense application Unscheduled Visit
            reissue: Re-dispensation Application
    plan: "<p>{{.labelEn}}:{{.subjectNumber}}</p><p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Dispensation Time:{{.dispensingDate}}</p>  <p>Remark:{{.remark}}</p> "
    unscheduled-plan: "<p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Dispensation Time:{{.dispensingDate}}</p>  <p>Unscheduled dispensation reason:{{.reason}}</p> "
    plan-logistics: "<p>{{.labelEn}}:{{.subjectNumber}}</p><p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Dispensation Time:{{.dispensingDate}}</p>  <p>Remark:{{.remark}}</p> "
    unscheduled-plan-logistics: "<p>{{.labelEn}}:{{.subjectNumber}}</p>  <p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Dispensation Time:{{.dispensingDate}}</p>  <p>Unscheduled dispensation reason:{{.reason}}</p> "
    plan-title: Clinflash IRT  {{.projectNumber}} {{.envName}}   IP Dispensation notificaiton {{.siteNumber}} {{.siteNameEn}}
    unscheduled-plan-title: Clinflash IRT  {{.projectNumber}} {{.envName}}  IP Dispensation {{.unscheduledEn}}  {{.siteNumber}} {{.siteNameEn}}
    reissue: "<p>{{.labelEn}}:{{.subjectNumber}}</p>  <p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Re-dispensation reason:{{.remark}}</p> <p>Dispensation Time:{{.dispensingDate}}</p>"
    reissue-logistics: <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.drugNumber}}</p> <p>Visit cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Dispensation Time:{{.dispensingDate}}</p> <p>Re-dispensation reason:{{.remark}}</p>
    reissue-title: Clinflash IRT {{.projectNumber}} {{.envName}}  IP Re-dispensation {{.siteNumber}} {{.siteNameEn}}
    replace: <p>{{.labelEn}}:{{.subjectNumber}}</p>
        <p>IP Number:{{.replaceNumber}}</p>
        <p>IP Number(Replaced):{{.drugNumber}}</p>
        <p>Replace Time:{{.dispensingDate}}</p>
        <p>Reason for Replacement:{{.reason}}</p>
    replace-title: Clinflash IRT {{.projectNumber}} {{.envName}}  IP Replacement {{.siteNumber}} {{.siteNameEn}}
    apply: <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.drugNumber}}</p> <p>Visit Cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Remark:{{.remark}}</p>  <p>Dispense Application Time:{{.dispensingDate}}</p>
    apply-title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Dispense application {{.siteNumber}} {{.siteNameEn}}
    reissue-dtp: <p>{{.labelEn}}:{{.subjectNumber}}</p>  <p>IP Number:{{.drugNumber}}</p> <p>Visit Cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Re-dispensation Reason:{{.remark}}</p>  <p>Re-Dispensation Application Time:{{.dispensingDate}}</p>
    reissue-dtp-title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Re-dispensation application {{.siteNumber}} {{.siteNameEn}}
    unscheduled-apply: <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.drugNumber}}</p> <p>Visit Cycle:{{.visitName}}</p> <p>Shipment Number:{{.orderNumber}}</p> <p>Reason:{{.reason}}</p>  <p>Re-Dispensation Application Time:{{.dispensingDate}}</p>
    unscheduled-apply-title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Dispense application Unscheduled Visit {{.siteNumber}} {{.siteNameEn}}
    replace-dtp: "<p>{{.labelEn}}:{{.subjectNumber}}</p> <p>IP Number:{{.replaceNumber}}</p> <p>IP Number(Replaced):{{.drugNumber}}</p>  <p>Shipment Number: {{.orderNumber}}</p>   <p>Replace Time:{{.dispensingDate}}</p> <p>Reason for Replacement:{{.reason}}</p>"
    replace-dtp-title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Replacement {{.siteNumber}} {{.siteNameEn}}
    register-title: Clinflash IRT {{.projectNumber}} {{.envName}} Registration of Actually Used IP Notification {{.siteNumber}} {{.siteNameEn}}
    register: "<p>{{.labelEn}}:{{.subjectNumber}}</p>
        <p>IP Number:{{.drugNumber}}</p>
        <p>Visit Cycle:{{.visitName}}</p>
        <p>Dispensation Time:{{.dispensingTime}}</p>
        <p>Remark:{{.remark}}</p>
        <p>Actual IP:{{.registerNumber}}</p>
        <p>Operation Time:{{.dispensingDate}}</p>"
    retrieval-title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Retrieval {{.siteNumber}} {{.siteNameEn}}
    retrieval: "<p>{{.labelEn}}:{{.subjectNumber}}</p>
        <p>IP Number:{{.drugNumber}}</p>
        <p>Visit Cycle:{{.visitName}}</p>
        <p>Dispensation Time:{{.dispensingTime}}</p>
        <p>Remark:{{.remark}}</p>
        <p>Retrieve IP:{{.retrievalNumber}}</p>
        <p>Operation Time:{{.dispensingDate}}</p>"
    #  发药邮件字段拼接
    group: "<p>Group:{{.group}}</p>"
    subGroup: "<p>Sub Group:{{.subGroup}}</p>"
    number: "<p>Randomization ID :{{.random_number}}</p>"
    not-attend-title: Clinflash IRT {{.projectNumber}} {{.envName}} Do Not Attend Visit {{.siteNumber}} {{.siteNameEn}}
    single:
        subject: "<p>{{.labelEn}}:{{.subjectNumber}}</p>"
        drugNumber: "<p>IP Number:{{.drugNumber}}</p>"
        dispensingDate: "<p>Dispensation Time:{{.dispensingDate}}</p>"
        replaceTime: "<p>Replace Time:{{.replaceTime}}</p>"
        orderNumber: "<p>Order Number:{{.orderNumber}}</p>"
        visitCycle: "<p>Visit Cycle:{{.visitCycleEn}}</p>"
        remark: "<p>Remark:{{.remark}}</p>"
        registerNumber: "<p>Actual IP:{{.registerNumber}}</p>"
        retrievalNumber: "<p>Retrieve IP:{{.retrievalNumber}}</p>"
        dispensingTime: "<p>Operation Time:{{.dispensingTime}}</p>"
        replaceNumber: "<p>IP Number:{{.replaceNumber}}</p>"
        beReplaceNumber: "<p>IP Number(Replaced):{{.beReplaceNumber}}</p>"
        unscheduled-dispensing-reason: "<p>Unscheduled dispensation reason:{{.reason}}</p>"
        unscheduled-dispensing-reason-customer: "<p>{{.dispensingTypeEn}} Reason:{{.reason}}</p>"
        re-dispensing-reason: "<p>Re-dispensation reason:{{.reason}}</p>"
        replace-reason: "<p>Reason for Replacement:{{.reason}}</p>"
        unblindingTime: "<p>IP Unblinding Time:{{.unblindingTime}}</p>"
        unblindingReason: "<p>IP Unblinding Reason:{{.unblindingReason}}</p>"
        commonReason: "<p>Reason:{{.reason}}</p>"
        approvalCode: "<p>Approval Code:{{.approvalCode}}</p>"
        approvalResult: "<p>Approval Result:{{.approvalResultEn}}</p>"
        randomNumber: "<p>Random Number:{{.randomNumber}}</p>"
batch-group:
    alert-site-title: Clinflash IRT  {{.projectNumber}} {{.envName}} Subject Site Alert {{.siteNumber}} {{.siteNameEn}}
    alert-depot-title: Clinflash IRT  {{.projectNumber}} {{.envName}} Subject Depot Alert {{.depotName}}
    limit-site-title: Clinflash IRT {{.projectNumber}} {{.envName}} Subject Limit Reminder  {{.siteNumber}} {{.siteNameEn}}
    limit-depot-title: Clinflash IRT {{.projectNumber}} {{.envName}} Subject Limit Reminder {{.depotName}}
    depotName: "Deport:{{.depotName}}"
    siteName: "Site Name:{{.siteNameEn}}"
    alarm: "<p>The number of subjects enrolled in different batches reached the alert value.</p><p>Batch/Alert Value/Actual Number:  {{.alarm}}</p>"
    limit: "<p>The number of subjects enrolled in different batches reached the alert value.</p><p>Batch/Alert Value/Actual Number:  {{.limit}}</p>"
edc:
    error: Request failed, the date push mode is active push from IRT.
    version: The EDC interface version is incorrect. please contact EDC personnel.
    add:
        relation:
            site:
                error: Site association failed, please associate site again.
    block:
        is:
            not:
                nil:
                    error: blockRepeatNo parameter is empty, dispensation failed, please confirm configuration again.
    check:
        matching:
            value:
                error: Value matching failed, please re confirm the value of the check box
    configure:
        drug:
            error: Treatment design is empty, please confirm treatment design in EDC again.
        visit:
            error: Visit cycle is empty, please confirm configuration of visit cycle in EDC again.
    drug:
        number:
            error: IP Number search failed. Please search again.
            mismatch: IP number search result does not match, please confirm replacing IP information.
        reissue:
            error: Re-dispensation failed, please contact unblinded personnel to complete operation.
        replace:
            error: Replacement failed, unable to replace the IP for the last completed visit, please re select.
            nil:
                nil: IP number is empty, please operate after confirm again.
                error: Failed to search the Replaced IP , please contact IRT engineer.
        type:
            error: type parameter is empty, dispensation failed, please confirm configuration again.
    env:
        error: Project environment information does not exist, please contact IRT engineer to check.
    factor:
        error: Cohort factor is empty, please confirm again.
    instance:
        is:
            not:
                nil:
                    error: instanceRepeatNo parameter is empty, dispensation failed, please confirm configuration again.
    matching:
        value:
            error: Value matching failed, please confirm the value of radio button or drop-down box again.
    multiple:
        subject: Subject ID search result duplicated, please confirm subject number information
    no:
        subject: Edit failed, please contact IRT engineer to deal with.
    parameter:
        error: Project number/environment/site number/site name/Subject ID is empty, please confirm EDC configuration.
    project:
        env:
            number:
                error: Project number/project environment is empty, synchronization failed, please confirm configuration again.
    query:
        factor:
            error: Cohort search failed, please confirm cohort factor configuration again.
        project:
            error: Project search failed, please contact IRT engineer to check.
            number:
                error: Project search failed, please contact IRT engineer to check.
            dtp:
                error: DTP project does not support docking with EDC
        site:
            number:
                error: This site is not unique, please check if multiple sites exist in IRT system
                relation:
                    site:
                        error: This site is not unique, please check if multiple sites exist in IRT system
    register:
        synchronization:
            error: Current interface does not support one-time full synchronization, interface call failed. Please confirm EDC synchronization function configuration.
    relation:
        site:
            error: Site association failed, please contact IRT engineer to check.
    site:
        error: Site search failed, please contact IRT engineer to check.
    standard:
        lost:
            site:
                error: Site information incomplete, please contact IRT engineer to edit.
        site:
            error: Site information lost, please contact IRT engineer to add.
    start:
        site:
            error: Site enable/activate failed, please operate again.
    subject:
        after:
            dispensing:
                error: Subject has not been randomized, dispensation failed, please confirm again.
        dispensing:
            error: Subject has not been randomized, dispensation failed, please confirm again.
        existence: Subject already exists, please do not add repeatedly.
        random:
            number:
                error: Subject ID is empty, please confirm EDC configuration.
        register:
            error: Subject is not registered, please confirm again.
        status:
            dispensing:
                error: Subject has been randomized, dispensation before randomization failed, please confirm again.
        site:
            error: The site information returned by IRT does not match the site where the subject is located. Please perform the subject transfer in IRT before dispensation.
    unable:
        at:
            random: Current fuction is not applicable in randomized project, please confirm randomization design.
    visit:
        error: Visit cycle is empty, please confirm configuration between visit number and visit cycle in EDC again.
        no:
            dispensing:
                error: Dispensation failed at current visit, please confirm property settings before/after randomization.
        number:
            drug:
                configure:
                    error: Treatment design search duplicated, please confirm configuration between visit number and treatment design in EDC again.
            error: Visit number is empty. The sync failed. Please reconfirm the configuration.
environment:
    duplicated:
        names: Duplicated environment name
    alertThresholds:
        limitError: Dispensation projects only, threshold control conditions only allow configuration registration/screening or enrollment, please reconfirm.
        attributeError: The project attribute is “dispensation only”, the current threshold limit condition configuration is invalid, please reconfigure it.
export:
    project: Project
    projects:
        number: Project Number
        name: Project Name
        cohort: Cohort Name
        stage: Stage
    barcode: Barcode
    random:
        register: Register
        random: Randomize
        exit: Stop
        unBlind: Emergency unblinded
        pv: PV unblinded
        screenSuccess: Screening successful
        screenFail: Screening failed
        finish: Complete Study
        toBeRandom: To be randomized
        number: Randomization Number
    dispensing:
        auto: auto
        first: first
        medicine: IP number
        realMedicine: Actually Used IP Number
        medicineName: IP Name
        otherMedicineCount: quantity of unnumbered IP
        outVisit: Unscheduled
        reissue: Re-dispense
        replace: Replace
        retrieve: Retrieve
        register: Register
        cancel: Withdraw
        invalid: Do Not Attend the Visit
        recover: Recover Dispensation
        replaceMedicine: Replaced IP number
        room: room number
        siteName: SiteName
        siteNumber: SiteNumber
        subject: subject
        time: operation time
        type: operation type
        visit: Visit name
        visit_number: Visit number
        visitSign: Unscheduled Dispense
        visit_apply: Visit Application
        out_visit_apply: Unscheduled Dispensation Application
        reissue_apply: Re-dispensation application
        replace_apply: Replacement application
        is_replace: Is it comfirmed to replace
        is_real: Whether the IP is actually dispensed
        operate_time: Dispensation Operation Time
        realMedicineNumber: Actually Used IP Number
        weight: Weight
        height: Height
        age: Age
    random_config:
        SubjectReplaceText: Label (replacement text)：
        accuracy: Exact number of digits：
        attribute: Project properties
        blind: Blind or not：
        countryLayered: Set country as stratification factor or not：
        regionLayered: Set Regional as stratification factor or not：
        createBy: Producer：
        createDate: Generation time：
        runningTime: Start time of simulation run：
        generationTime: Simulation report generation time：
        digit: Digit：
        dispensing: Dispensing or not：
        export: Configuration Report
        simulation_pdf: Randomization Simulation Report
        factor: Stratification factors：
        no: No
        group: Group：
        instituteLayered: Set site as stratification factor：
        isFreeze: When running the delivery algorithm, count quarantined items as part of the available inventory in the sites
        isRandom: The center has not assigned a random number and cannot be enrolled：
        list: Randomization List：
        prefix: Use prefix：
        random: Randomization or not：
        random_design: Random configuration
        ratio: Group ratio：
        report: "Clinflash IRT is a random and trial IP supply management system, which can be used for randomization, dispensation and trial research products supply management.This configuration report includes project properties, randomization design and treatment  design, which can be used to quickly review and approve project settings,and archive project files."
        configure_report: "Clinflash IRT is a random and trial IP supply management system, which can be used f|or random, dispensing and trial research products supply management.This configurat|ion report includes project properties, random design and treatment  design, which ca|n be used to quickly review and approve project settings,and archive project files."
        report_pdf: "Clinflash IRT is a random and trial IP supply management system, which can be used for randomization, dispensation and trial research products supply management.This configuration report includes project properties, randomization design and treatment  design, which can be used to quickly review and approve project settings,and archive project files."
        directory: Directory
        summary: Summary
        unitCapacity: "Unit capacity:"
        unitStandard: "Unit Calculation Standard:"
        ageType: Age
        weightType: Weight
        bsaType: Simple body surface area BSA
        customerBsaType: Other body surface area BSA
        table:
            code: Group code
            count: The number of IP dispensed
            countMax: Dispensation quantity
            formulas: Dispensation quantity range
            label: (Combined) Dispensation label
            formula: Custom formula
            medicine: IP Name
            spec: IP Specification
            formulasType: Formula
        total: Total：
        treatment_design: Treatment design
        yes: Yes
        type: Random type：
    room:
        history:
            room: the history of viewing room number
            time: time
            user: user
        project: project
    unblinding:
        remark: Unblinding Remark
        reason: Unblinding Reason
        reason_mark: Unblinding Reason Remark
        operator: Unblinding Operator
        operate_time: Unblinding Operation Time
    user:
        name: Name
        email: Email
        role: Role
        create_time: Created Time
        status_effective: Valid
        status_invalid: Invalid
    medicine:
        serial_number: Sequence Number
        ip_name: IP Name
        ip_number: IP Number
        expiration_date: Expiration
        batch_number: Batch Number
        package_number: Package Number
        package_number_serialNumber: Package Sequence Number
    subject:
        number: Subject Number
file_emtpy: The file list is empty
col_empty_error: The uploaded randomization list contains empty columns (including headers). Please correct them before re-uploading.
footer: This email is delivered by system automatically. Please do not reply to the email since the mailbox is not monitored..<br>
history:
    dispensing:
        updateBatch: IP【Update】,Expiration:{{.expirationDate}},batch No.:{{.batchNumber}},status:{{.status}},Update Count:{{.count}}
        cancel: 【Dispensing Withdrawed】{{.label}}:{{.subject}}     Withdrawed reason:{{.reason}}
        dispensing: 【Dispensation】{{.label}}:{{.subject}}    IP number:{{.medicine}}
        dispensing-other: 【Dispensation】 {{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}}
        dispensing-with-other: 【Dispensation】 {{.label}}:{{.subject}}    IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}}
        dispensing-with-other-reason: 【Dispensation】 {{.label}}:{{.subject}}     IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}},Unscheduled dispensation reason:{{.reason}}
        dispensingVisit: 【Unscheduled Dispensation】{{.label}}:{{.subject}}    IP number:{{.medicine}},Unscheduled Dispensation reason:{{.reason}}
        dispensingVisit-other: 【Unscheduled Dispensation】{{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}},Unscheduled dispensation reason:{{.reason}}

        dispensing-new: 【Dispensation】{{.label}}:{{.subject}} {{.form}}  {{.formula}} IP number:{{.medicine}},  remark:{{.remark}}
        dispensing-other-new: 【Dispensation】 {{.label}}:{{.subject}}  {{.form}} {{.formula}} unnumbered IP name /quantity date:{{.medicine}},  remark:{{.remark}}
        dispensing-with-other-new: 【Dispensation】 {{.label}}:{{.subject}}  {{.form}} {{.formula}} IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}}, remark:{{.remark}}
        dispensing-with-other-reason-new: 【Unscheduled Dispensation】 {{.label}}:{{.subject}}  {{.form}} {{.formula}}  IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}},Unscheduled dispensation reason:{{.reason}}
        dispensingVisit-new: 【Unscheduled Dispensation】{{.label}}:{{.subject}}  {{.form}} {{.formula}} IP number:{{.medicine}},  Unscheduled dispensation reason:{{.reason}}
        dispensingVisit-other-new: 【Unscheduled Dispensation】{{.label}}:{{.subject}}  {{.form}} {{.formula}} unnumbered IP name /quantity date:{{.medicine}}, Unscheduled dispensation reason:{{.reason}}

        dtp-dispensing: 【Application】{{.label}}:{{.subject}}    IP number:{{.medicine}} remark:{{.remark}}
        dtp-dispensing-other: 【Dispensation】 {{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}} remark:{{.remark}}
        dtp-dispensing-with-other: 【Dispensation】 {{.label}}:{{.subject}}    IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}} remark:{{.remark}}
        dtp-dispensing-with-other-reason: 【Unscheduled Dispensation】 {{.label}}:{{.subject}}     IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}},Unscheduled dispensation reason:{{.reason}}
        dtp-dispensingVisit: 【Unscheduled Dispensation】{{.label}}:{{.subject}}    IP number:{{.medicine}},Unscheduled Dispensation reason:{{.reason}}
        dtp-dispensingVisit-other: 【Unscheduled Dispensation】{{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}},Unscheduled dispensation reason:{{.reason}}
        dtp-reissue: 【Re-dispensation Application】 {{.label}}:{{.subject}}    IP number:{{.medicine}} reason:{{.remark}}
        dtp-reissue-other: 【Re-dispensation】{{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}} reason:{{.remark}}
        dtp-reissue-with-other: 【Re-dispensation】{{.label}}:{{.subject}}    IP:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}} reason:{{.remark}}

        replace-logistics: 【Replacement】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}},IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Remark:{{.remark}}.
        dispensing-logistics: 【Dispensation】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}}, {{.form}} IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Remark:{{.remark}}.
        dispensing-logistics-noRandomNumber: 【Dispensation】{{.label}}:{{.subject}},Visit Cycle:{{.visit}}, {{.form}}IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Remark:{{.remark}}.
        dispensingVisit-logistics: 【Unscheduled Dispensation】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}},  {{.form}} IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Unscheduled dispensation reason:{{.reason}}.
        dispensingVisit-logistics-noRandomNumber: 【Unscheduled Dispensation】{{.label}}:{{.subject}},Visit Cycle:{{.visit}}, {{.form}} IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Unscheduled dispensation reason:{{.reason}}.
        reissue-with-logistics: 【Re-dispensation】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}},IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Re-dispensation Reason:{{.remark}}.
        reissue-with-logistics-noRandomNumber: 【Re-dispensation】{{.label}}:{{.subject}},Visit Cycle:{{.visit}},IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Re-dispensation Reason:{{.remark}}.

        invalid: 【Do Not Attend the Visit】{{.label}}:{{.subject}}, remark:{{.remark}}.
        register: 【Registration of Actually Used IP】  {{.label}}:{{.subject}},IPs dispensed by the system:{{.medicine}}, Actual dispensed IPs:{{.real_medicine}}
        reissue: 【Re-dispensation】 {{.label}}:{{.subject}}    IP number:{{.medicine}}, Re-dispensation Reason:{{.remark}}
        reissue-other: 【Re-dispensation】{{.label}}:{{.subject}}    unnumbered IP name /quantity date:{{.medicine}}, Re-dispensation Reason:{{.remark}}
        reissue-with-other: 【Re-dispensation】{{.label}}:{{.subject}}    IP:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}},Re-dispensation Reason:{{.remark}}
        replace: 【Replacement IP 】{{.label}}:{{.subject}}    Reason for Replacement:{{.reason}} ,replace IP number:{{.medicine}}, be replace IP number:{{.beReplaceMedicine}}
        retrieval: 【Retrieval IP】{{.label}}:{{.subject}}    Retrieved IP:{{.medicine}}
        retrieval-order: 【Retrieval IP】{{.label}}:{{.subject}}    Retrieved IP:{{.medicine}} reason:order close or cancel
        send-type-0: Site(Site Inventory)
        send-type-1: Site(Direct-to-Patient Shipment)
        send-type-2: Deport(Direct-to-Patient Shipment)
        scanConfrim: 【Scan Confirm】{{.label}}:{{.subject}},IP number:{{.medicine}},shortCode:{{.shortCode}}
        dispensing-new-formula: 【Dispensation】{{.label}}:{{.subject}} {{.formula}} {{.form}}   IP number:{{.medicine}}, remark:{{.remark}}
        dispensing-other-new-formula: 【Dispensation】 {{.label}}:{{.subject}} {{.formula}}{{.form}}   unnumbered IP name /quantity date:{{.medicine}},  remark:{{.remark}}
        dispensing-with-other-new-formula: 【Dispensation】 {{.label}}:{{.subject}}  {{.formula}}{{.form}}  IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}}, remark:{{.remark}}
        dispensing-with-other-reason-new-formula: 【Unscheduled Dispensation】 {{.label}}:{{.subject}}   {{.formula}}{{.form}}  IP number:{{.medicine}}, unnumbered IP name /quantity date:{{.other_medicine}},Unscheduled dispensation reason:{{.reason}}
        dispensingVisit-new-formula: 【Unscheduled Dispensation】{{.label}}:{{.subject}}  {{.formula}}{{.form}}  IP number:{{.medicine}}, Unscheduled dispensation reason:{{.reason}}
        dispensingVisit-other-new-formula: 【Unscheduled Dispensation】{{.label}}:{{.subject}}  {{.formula}}{{.form}}  unnumbered IP name /quantity date:{{.medicine}}, Unscheduled dispensation reason:{{.reason}}
        dispensing-logistics-formula: 【Dispensation】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}}, {{.formula}}{{.form}} IP number:{{.medicine}},  Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Remark:{{.remark}}.
        dispensing-logistics-noRandomNumber-formula: 【Dispensation】{{.label}}:{{.subject}},Visit Cycle:{{.visit}},{{.formula}}{{.form}} IP number:{{.medicine}},  Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Remark:{{.remark}}.
        dispensingVisit-logistics-formula: 【Unscheduled Dispensation】{{.label}}:{{.subject}},Randomization ID:{{.randomNumber}}, Visit Cycle:{{.visit}},  {{.formula}}{{.form}}  IP number:{{.medicine}},Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Unscheduled dispensation reason:{{.reason}}.
        dispensingVisit-logistics-noRandomNumber-formula: 【Unscheduled Dispensation】{{.label}}:{{.subject}},Visit Cycle:{{.visit}}, {{.formula}}{{.form}} IP number:{{.medicine}},  Shipment No.:{{.order}},Dispensation Method:{{.sendType}},Logistics:{{.vendor}}/{{.number}}, Unscheduled dispensation reason:{{.reason}}.
        resume: 【Recover Dispensation】{{.label}}:{{.subject}}
        dispensingCustomer-dispensing: 【Dispensation】 {{.label}}:{{.subject}}, {{.data}}
        dispensingCustomer-dispensingVisit: 【Unscheduled Dispensation】 {{.label}}:{{.subject}}, {{.data}}
        dispensingCustomer-dispensingVisitCustomer: "【{{.dispensingType}}】 {{.label}}：{{.subject}}， {{.data}}"
        dispensingCustomer-reissue: 【Re-dispensation】 {{.label}}:{{.subject}}, {{.data}}
        dispensingCustomer-replace: 【Replacement IP】 {{.label}}:{{.subject}}, {{.data}}
        dispensingCustomer-retrieval: 【Retrieval IP】{{.label}}:{{.subject}}, {{.data}}
        dispensingCustomer-register: 【Registration of Actually Dispensed IP】 {{.label}}:{{.subject}}, {{.data}}
        dispensingCustomer-not-attend: 【Do Not Attend the Visit】 {{.label}}：{{.subject}}， {{.data}}
        dispensingCustomer-resume: 【Recover Dispensation】 {{.label}}：{{.subject}}， {{.data}}
        dispensingCustomer-unblinding-application: 【IP UnBlind】 {{.data}}
        single:
            comma: ","
            period: "."
            colon: ": "
            dispensingVisit: "Unscheduled Dispensation"
            dispensingApply: " Apply"
            randomNumber: Randomization ID:{{.randomNumber}}
            visit: Visit Cycle:{{.visit}}
            form: "{{.form}}"
            date: Date Of Birth:{{.date}}
            weight: Weight:{{.weight}}
            height: Height:{{.height}}
            dose: "{{.dose}}"
            level: Dispensation Level:{{.level}}
            medicine: IP number:{{.medicine}}
            otherMedicine: unnumbered IP name /quantity date:{{.medicine}}
            order: Shipment No.:{{.order}}
            sendType: Dispensation Method
            vendor: Logistics:{{.vendor}}/{{.number}}
            outSize: "Is it overdue:Yes"
            remark: Remark:{{.remark}}
            reason: Reason:{{.reason}}
            reasonDispensingVisit: Unscheduled Dispensation reason:{{.reasonDispensingVisit}}
            reasonDispensingVisitCustomer: "{{.dispensingType}} reason：{{.reasonDispensingVisit}}"
            reasonReissue: Re-dispensation Reason:{{.reasonDispensingVisit}}
            reasonReplace: Reason for Replacement:{{.reasonDispensingVisit}}
            replaceNumber: replace IP number:{{.replaceNumber}}
            beReplaceMedicine: be replace IP number:{{.beReplaceMedicine}}
            retrievalMedicine: IP:{{.retrievalMedicine }}
            systemMedicine: IP delivery system:{{.systemMedicine}}
            realMedicine: Actual dispensed IPs:{{.realMedicine}}
            notAttendRemark: "Remark：Open follow-up visit stage,current stage:{{.random}},the follow-up stage:{{.atRandom}}"
            resumeRemark: "Remark：Close follow-up visit stage,current stage:{{.currentStage}},the follow-up stage:{{.nextStage}}"
            noKey: "{{ .data }}"
            unBlindMedicine: "Unblinding IP:{{ .number }}"
            unBlindStatus: "Status"
            unBlindApplication: "pending approval"
            unBlindReject: "rejected"
            unBlindSuccess: "passed"
            unBlindApprovalNumber: " approval number:{{ .approvalNumber }}"
    medicine:
        update-batch-expireDate: 【Edit】 Related shipment:{{.orderNumber}},Status:{{.status}},IP Number:{{.ipNumber}},Expiration Date:{{.expirationDate}},Batch NO.:{{.batchNumber}}.
        other-update-batch-expireDate: 【Edit】 Related shipment:{{.orderNumber}},Status:{{.status}},IP :{{.ip}},Quantity :{{.count}},Expiration Date:{{.expirationDate}},Batch NO.:{{.batchNumber}}.
        updateBatch: IP【Update】,Expiration:{{.expirationDate}},batch No.:{{.batchNumber}},status:{{.status}}.
        updateOtherBatch: IP【Update】,Expiration:{{.expirationDate}},batch No.:{{.batchNumber}},status:{{.status}},Update Count:{{.count}}.
        expired: 【Expired IP】 expired reason:Automatically expired. Current expired IP number:{{.packNumber}}.
        freeze: Quarantine IP, quarantine reason:{{.reason}}, current quarantine IP number:{{.packNumber}}.
        freeze-new: IP 【Quarantined】,quarantine number:{{.freezeNumber}},IP Number:{{.packNumber}},reason:{{.reason}}.
        freeze-package-new: IP 【Quarantined】,Quarantine number:{{.freezeNumber}},IP Number:{{.packNumber}},Package Number:{{.packageNumber}},reason:{{.reason}}.
        otherFreeze: IP 【Quarantined】, quarantine No.:{{.freezeNumber}}, reason:{{.freezeReason}}, name of current quarantined IP:{{.name}}, batch No.:{{.batch}}, expiry date:{{.expireDate}}, quarantine:{{.freezeCount}}.
        new-freeze: IP 【Quarantined】,{{.data}}
        new-freeze-label:
            freezeNumber: quarantine number:{{.freezeNumber}}
            position: Quarantine Location:{{.position}}
            ipNumber: IP Number:{{.packNumber}}
            reason: reason:{{.reason}}
            packageNumber: Package Number:{{{.packageNumber}}
            ipName: current quarantine IP number:{{.name}}
            batch: batch No.{{.batch}}
            expireDate: expiry date:{{.expireDate}}
            freezeCount: quarantine:{{.freezeCount}}
            orderNumber: Related shipment:{{.orderNumber}}
            changeReason: Replacement Reason:{{.reason}}
            changeOtherMedicine: IP name/batch No/Expiration/count:{{.changeOtherMedicine}}
        otherMedicineLost: IP【Lost / Wasted】, reason:{{.freezeReason}}, name of current quarantined IP:{{.name}}, batch No.:{{.batch}}, Expiration:{{.expireDate}}, Lost / invalid:{{.freezeCount}}.
        otherMedicineLost-new: IP【Lost/Wasted IP】,IP:{{.name}}, batch No.:{{.batch}}, expiry date:{{.expireDate}}, quarantine:{{.freezeCount}},reason:{{.freezeReason}}.
        lost: Lost / Wasted reason:{{.reason}}, current lost / wasted IP number:{{.packNumber}}.
        lost-new: IP【Lost/Wasted】, IP:{{.packNumber}},reason:{{.reason}}.
        lost-package: Lost / Wasted reason:{{.reason}}, current lost / wasted IP number:{{.packNumber}},Package Number:{{.packageNumber}}.
        new-lost:
            lost: IP【Lost/Wasted】, {{.data}}
            position: 位置：{{.position}}
        release-new:
            release: IP【Release】, {{.data}}
            packNumber: current IP number:{{.packNumber}}
        release: IP【Release】, current IP number:{{.packNumber}},reason:{{.reason}}.
        release-package: IP【Lifting Quarantine】, currently lifted IP number:{{.packNumber}},package number:{{.packageNumber}},reason:{{.reason}}.
        quarantine-no: IP【Lifting Quarantine Approval】,IP Number:{{.packNumber}},Lifting Confirmation:Reject,Deny Reason:{{.reason}}.
        approval: IP【Release Request】,reason:{{.reason}},IP Number:{{.packNumber}}.
        locked: IP【Locked】.
        otherLocked: IP【Locked】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
        otherUse: IP【Dispensed】,current IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
        otherCanUse: IP【Available】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
        otherLost: IP【Lost/Wasted】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
        otherToBeConfirm: IP【To be confirmed】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
        otherExpired: IP【Expired】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
        toBeConfirm: IP【To be confirmed】.
        toBeConfirmNew: Shipment Number:{{.order}},Status:To be confirmed.
        toFrozenNew: "Shipment Number:{{.orderNumber}},Status:Frozen."
        toBeConfirmNewUpdate: Shipment Number:{{.orderNumber}},Status:To be confirmed.
        confirmed: IP【Confirmed】.
        confirmedNew: Shipment Number:{{.orderNumber}},Status:Confirmed.
        otherConfirmed: IP【Confirmed】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
        releaseLost-new:
            release: 【Lifting lost/wasted IP】, :{{.data}}
            reason: reason for lost/ wasted :{{.reason}}
            packNumber: current IP number:{{.packNumber}}
        releaseLost: 【Lifting lost/wasted IP】 lost / wasted IP is:{{.reason}}, current IP number:{{.packNumber}}.
        releaseLost-package: 【Lifted  lost/wasted IP】reason for lost/ wasted :{{.reason}}, current IP number:{{.packNumber}},Package Number:{{.packageNumber}}.
        otherRelease: 【Lifting IP】The reason for Lifting Quarantine is:{{.reason}},name of current quarantine IP:{{.name}}, batch No.:{{.batch}}, Expiration date:{{.expireDate}}, quarantine:{{.count}}.
        otherReleaseLost: 【Lost/Wasted IP】 lost / wasted IP is:{{.reason}},name of current lost / wasted IP:{{.name}}, batch No.:{{.batch}}, expiry date:{{.expireDate}}, quarantine:{{.count}}.
        otherRelease-new:
            release: 【Lifting IP】, {{.data}}
            reason: The reason for Lifting Quarantine is:{{.reason}}
            name: name of current quarantine IP:{{.name}}
            count: quarantine:{{.count}}
        otherReleaseLost-new:
            release: 【Lost/Wasted IP】, {{.data}}
            reason: lost / wasted IP is:{{.reason}}
            name: name of current lost / wasted IP:{{.name}}
            count: quarantine:{{.count}}
        other-quarantine-no: IP【Lifting Approval】,Lifting Confirmation:Reject, Deny Reason:{{.reason}},IP name:{{.name}}, Batch Number.:{{.batch}}, expiry date:{{.expireDate}}, Quantity:{{.count}}.
        other-approval: IP【Release Request】 reason:{{.reason}},IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
        replace: "【IP Replacement】Wasted reason: {{.reason}}, current wasted IP number: {{.packNumber}}."
        use: 【Set as available IP】 set as available reason:{{.reason}}, current available IP number:{{.packNumber}}.
        use-package: Set as available IP,set as available reason:{{.reason}}, current available IP number:{{.packNumber}},Package Number:{{.packageNumber}}.
        use-new:
            use: 【Set as available IP】, {{.data}}
            packNumber: current available IP number:{{.packNumber}}
            position: 位置：{{.position}}
            reason: set as available reason:{{.reason}}
        replace-dtp: "IP replaced, before replacement: {{.beReplace}}, after replacement: {{.replace}}."
        replace-with-dtp: 【IP Replacement】,IP【Lost/Wasted】,Reason:IP has been replaced.
        cancel: IP【Withdrew】,Subject:{{.subject}},Visit Cycle:{{.visit}},Operation:IP Withdrew.
        register: IP【Dispensed】 Reason:Registration of Actually Used IP, IP delivery system:{{.medicine}}, Actually dispensed IP Number:{{.real_medicine}}.
        apply: IP has been applied.
        shipped: Shipment Number:{{.orderNumber}},Status:In delivery.
        otherShipped: IP 【Delivered】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
        receive: IP 【received】.
        receiveNew: Shipment Number:{{.orderNumber}},Status:Received.
        otherReceive: IP 【received】,IP name:{{.name}}, Batch Number.:{{.batch}}, Expiration date:{{.expireDate}}, Quantity:{{.count}}.
        used: IP dispensed.
        canUse: IP【Available】.
        uploadCanUse: 【IP Upload】Status:To be Warehoused.
        scanCanUse: 【Scan for Warehousing】Status:To be approved.
        examinePassThrough: 【Approved】Status:To be Warehoused
        examineRefuse: 【Reject】Status:Approval failed
        updateName: 【Update】Status:To be approved
        updateNameScanCan: 【Update】Status：To be scanned
        release-usable: 【Release】Status:Available.
        register-use: IP【Available】,reason:IP has been registered to be "available".
        register-frozen: IP【Frozen】,reason:IP has been registered to be "frozen".
        register-lost: IP【Lost/Wasted】,reason:IP has been registered as "Lost/Wasted".
        in-order: IP to be confirmed, Subject No.:{{.subject}},IP:{{.medicine}}.
        confrim: IP is confirmed.
        dispensing-used: IP dispensed,Subject No.:{{.subject}},IP number:{{.medicine}}.
        use-dtp: Shipment Number:{{.orderNumber}},Status:available.
        use-order-cancel: IP Status Recover,reason:Shipment Cancelled.
        use-order-close: IP Status Recover,reason:Close Shipment.
        use-order-termination: The status of the IP is rescovered,reason:Terminate Shipment.
        use-available-order-cancel: IP【Available】,reason:Shipment Cancelled,Status Recover.
        use-available-order-close: IP【Available】,reason:Shipment is closed,Status Recover.
        use-available-order-termination: IP【Available】,reason:Terminate Shipment,Status Recover.
        use-available-order-confrim: Shipment Number:{{.orderNumber}} ,Status:available,Reason:The IP was not confirmed during order confirmation,Status Recover.
        use-available-order-frozen: Shipment Number:{{.orderNumber}} ,Status:frozen,Reason:The IP was not confirmed during order confirmation,Status Recover.
        use-frozen-order-cancel: IP【Frozen】,reason:Shipment Cancelled,Status Recover.
        use-frozen-order-close: IP【Frozen】,reason:Shipment is closed,Status Recover.
        use-frozen-order-termination: IP【Frozen】,reason:Terminate Shipment,Status Recover.
        use-lose-order-cancel: IP【Lost/Wasted】,reason:Shipment Cancelled,Status Recover.
        use-lose-order-close: IP【Lost/Wasted】,reason:Shipment is closed,Status Recover.
        use-lose-order-termination: IP【Lost/Wasted】,reason:Terminate Shipment,Status Recover.
        use-expired-order-cancel: IP【Expired】,reason:Cancel Shipment,Status Recover.
        use-expired-order-close: IP【Expired】,reason:Shipment is closed,Status Recover.
        use-expired-order-termination: IP【Expired】,reason:Terminate Shipment,Status Recover.
        sku-freeze: IP 【Frozen】.
        sku-freeze-reason: 【Set as frozen IP】 set as frozen reason:{{.reason}}, current frozen IP number:{{.packNumber}}.
        sku-freeze-subject: IP【Frozen】, Subject:{{.subject}}, Visit Cycle:{{.visit}},Operation:{{.operation}}.
        sku-used-subject: IP【Dispensed】, Subject:{{.subject}}, Visit Cycle:{{.visit}},Operation:{{.operation}}.
        sku-use-subject: IP【Available】, Assigned Subject:{{.subject}}, Visit Cycle:{{.visit}},Operation:{{.operation}}.
        sku-in-order-subject: IP 【To be confirmed】, Subject:{{.subject}}, Visit Cycle:{{.visit}},Operation:{{.operation}}.
        sku-lost-subject: IP 【Lost/Wasted】, Subject:{{.subject}}, Visit Cycle:{{.visit}},Operation:{{.operation}}.
        rest-receive: IP【Received】,Subject:{{.subject}},Visit Cycle:{{.visit}},Operation:{{.operation}}.
        rest-return: IP【Returned】,Subject:{{.subject}},Visit Cycle:{{.visit}},Operation:{{.operation}}.
        rest-destroy: IP【Destroyed】,Subject:{{.subject}},Visit Cycle:{{.visit}},Operation:{{.operation}}.
        rest-return-retrieve: IP【Returned】,Operation:{{.operation}}.
        rest-destroy-retrieve: IP【Destroyed】,Operation:{{.operation}}.
        operation:
            dispensing: Dispense
            retrieval: Retrieve
            replace: IP Replace
            unscheduled: Unscheduled dispense
            reissue: Re-dispense
            register: Registration of Actually Used IP
        sku-lose: Shipment Number:{{.orderNumber}},Status:【Lost】.
        cancel-dtp: IP has been terminated, termination reason:{{.reason}}.
        drugFreeze-new:
            freeze: IP 【Quarantined】, {{.data}}
            lost: IP 【lost/Wasted】, {{.data}}
            approval: IP【Lifting Application】,{{.data}}
            release: IP 【Lifting Quarantine】,{{.data}}
            freezeNumber: quarantine number:{{.freezeNumber}}
            position: Quarantine Location:{{.position}}
            medicines: IP Number:{{.medicines}}
            medicinesPackage: IP Number/Package Number:{{.medicines}}
            freezeOtherMedicine: name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}}
            lostOtherMedicine: name of current lost/Wasted IP/batch No/Expiration/count:{{.otherMedicines}}
            releaseOtherMedicine: name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}}
            reason: reason:{{.reason}}
            quarantine-no: IP【Lifting Approval】,{{.data}} Lifting Confirmation:Reject,Deny Reason:{{.freezeReason}}.
            quarantine-yes: IP【Lifting Approval】,{{.data}} Lifting Confirmation:Pass.
        drugFreeze:
            drugFreeze: IP 【Quarantined】,quarantine number:{{.freezeNumber}}, IP Number:{{.medicines}}, reason:{{.freezeReason}}.
            drugFreeze-package: IP 【quarantined】,quarantine number:{{.freezeNumber}}, IP Number/Package Number:{{.medicines}}, reason:{{.freezeReason}}.
            otherDrugFreeze: IP 【Quarantined】,quarantine number:{{.freezeNumber}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}},reason:{{.freezeReason}}.
            allDrugFreeze: IP 【Quarantined】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}},IP Number:{{.medicines}}.
            allDrugFreeze-package: IP 【Quarantined】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}.
            lost: IP 【lost/Wasted】,quarantine number:{{.freezeNumber}}, IP Number:{{.medicines}} , reason:{{.freezeReason}}.
            lost-package: IP 【lost/Wasted】,quarantine number:{{.freezeNumber}}, IP Number/Package Number:{{.medicines}} , reason:{{.freezeReason}}.
            approval: IP【Lifting Application】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},IP Number:{{.medicines}}.
            approval-package: IP【Lifting  Application】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},IP Number/Package Number:{{.medicines}}.
            release: IP 【Lifting Quarantine】,quarantine number:{{.freezeNumber}}, IP Number:{{.medicines}} , reason:{{.freezeReason}}.
            release-package: IP 【Lifting Quarantine】,quarantine number:{{.freezeNumber}}, IP Number/Package Number:{{.medicines}} , reason:{{.freezeReason}}.
            quarantine-no: IP【Lifting Approval】,quarantine number:{{.freezeNumber}},IP Number:{{.medicines}},reason:{{.untieReason}},Lifting Confirmation:Reject,Deny Reason:{{.freezeReason}}.
            quarantine-no-package: IP【Lifting Approval】,quarantine number:{{.freezeNumber}},IP Number/Package Number:{{.medicines}},reason:{{.untieReason}},Lifting Confirmation:Reject,Deny Reason:{{.freezeReason}}.
            quarantine-yes: IP【Lifting Approval】,quarantine number:{{.freezeNumber}},IP Number:{{.medicines}},reason:{{.untieReason}},Lifting Confirmation:Pass.
            quarantine-yes-package: IP【Lifting Approval】,quarantine number:{{.freezeNumber}},IP Number/Package Number:{{.medicines}},reason:{{.untieReason}},Lifting Confirmation:Pass.
            otherLost: IP 【lost/Wasted】,quarantine number:{{.freezeNumber}},name of current lost/Wasted IP/batch No/Expiration/count:{{.otherMedicines}},reason:{{.freezeReason}}.
            other-approval: IP【Lifting  Application】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}}.
            otherRelease: IP 【Lifting Quarantine】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},reason:{{.freezeReason}}.
            other-quarantine-no: IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},reason:{{.untieReason}},Lifting Confirmation:Reject,Deny Reason:{{.freezeReason}}.
            other-quarantine-yes: IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},reason:{{.untieReason}},Lifting Confirmation:Pass.
            allLost: IP 【lost/Wasted】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current lost/Wasted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number:{{.medicines}}.
            allLost-package: IP 【lost/Wasted】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current lost/Wasted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}.
            all-approval: IP【Lifting  Application】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}},IP Number:{{.medicines}}.
            all-approval-package: IP【Lifting  Application】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current quarantined IP/batch No/Expiration/quarantine:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}.
            allRelease: IP 【Lifting Quarantine】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number:{{.medicines}}.
            allRelease-package: IP 【Lifting Quarantine】,quarantine number:{{.freezeNumber}},reason:{{.freezeReason}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}.
            all-quarantine-no: IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number:{{.medicines}}, reason:{{.untieReason}},Lifting Confirmation:Reject,Deny Reason:{{.freezeReason}}.
            all-quarantine-no-package: IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}, reason:{{.untieReason}},Lifting Confirmation:Reject,Deny Reason:{{.freezeReason}}.
            all-quarantine-yes: IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number:{{.medicines}}, reason:{{.untieReason}},Lifting Confirmation:Pass.
            all-quarantine-yes-package: IP【Lifting Approval】,quarantine number:{{.freezeNumber}},name of current batch lifted IP/batch No/Expiration/count:{{.otherMedicines}},IP Number/Package Number:{{.medicines}}, reason:{{.untieReason}},Lifting Confirmation:Pass.
        change:
            newToConfirm: Related shipment:{{.orderNumber}},Status:To be confirmed,Original IP:{{.oldMedicine}},Post-replacement IP:Post-replacement IP:{{.newMedicine}},Replacement Reason:{{.reason}}。
            newConfirmed: Related shipment:{{.orderNumber}},Status:Confirmed,Original IP:{{.oldMedicine}},Replacement IP name/batch number/expiration date/count:{{.newMedicine}},Replacement Reason:{{.reason}}。
            old: IP 【Quarantined】, quarantine number:{{.freezeNumber}},IP Number:{{.oldMedicine}},Related shipment:{{.orderNumber}},Replacement Reason:{{.reason}}。
            otherNewToConfirm: Related shipment:{{.orderNumber}},To be confirmed,Original IP name/batch number/expiration date/count:{{.oldMedicine}},Replacement IP name/batch number/expiration date/count:{{.newMedicine}},Replacement Reason:{{.reason}}。
            otherNewConfirmed: Related shipment:{{.orderNumber}},Status:Confirmed,Original IP name/batch number/expiration date/count:{{.oldMedicine}},Replacement IP name/batch number/expiration date/count:{{.newMedicine}},Replacement Reason:{{.reason}}。
            otherOld: IP 【Quarantined】,quarantine number:{{.freezeNumber}},IP name/batch No/Expiration/count:{{.oldMedicine}},Related shipment:{{.orderNumber}},Replacement Reason:{{.reason}}。
    order:
        cancel-new: Shipment No.:{{.orderNumber}},shipment【Canceled】, reason for cancellation:{{.reason}}.
        close-new: Shipment No.:{{.orderNumber}},order【Closed】, reason for closed:{{.reason}}.
        confrim-new: Shipment No.:{{.orderNumber}},order【Confirmed】.
        confrim-expectedArrivalTime-new: Shipment No.:{{.orderNumber}},order【Confirmed】,Expected Arrival Time:{{.expectedArrivalTime}}.
        create-new: Shipment No.:{{.orderNumber}},order【To be confirmed】.
        create-expectedArrivalTime-new: Shipment No.:{{.orderNumber}},order【To be confirmed】,Expected Arrival Time:{{.expectedArrivalTime}}.
        lost-new: Shipment No.:{{.orderNumber}},shipment【Lost】, reason for loss:{{.reason}}.
        receive-new: Shipment No.:{{.orderNumber}},order【Received】.
        receive-actualTime-new: Shipment No.:{{.orderNumber}},order【Received】,Actual Receipt Time:{{.actualReceiptTime}}.
        send-new: Shipment No.:{{.orderNumber}},shipment【In delivery】.
        send-expectedArrivalTime-new: Shipment No.:{{.orderNumber}},shipment【In delivery】,Expected Arrival Time:{{.expectedArrivalTime}}.
        logistics—other: "【Logistics information update】Logistics Vendor: {{.logistics}}, Other logistics: {{.other}}, logistics Number: {{.number}}."
        logistics: "【Logistics information update】Logistics Vendor: {{.logistics}}, Tracking Number: {{.number}}."
        logistics—other-actualTime: 【Logistics information update】Shipment No.:{{.orderNumber}},Actual Receipt Time:{{.actualReceiptTime}}.
        logistics-actualTime: 【Logistics information update】Shipment No.:{{.orderNumber}},Actual Receipt Time:{{.actualReceiptTime}}.
        logistics—other-actualTime-all: 【Logistics information update】Logistics Vendor:{{.logistics}}, Other logistics:{{.other}},logistics Number:{{.number}},Actual Receipt Time:{{.actualReceiptTime}}.
        logistics-actualTime-all: 【Logistics information update】Logistics Vendor:{{.logistics}},logistics Number:{{.number}},Actual Receipt Time:{{.actualReceiptTime}}.
        send-with-logistics: Shipment No.:{{.orderNumber}},shipment【In delivery】,Logistics Vendor:{{.logistics}},Logistics Number:{{.number}}.
        send-with-logistics-expectedTime: Shipment No.:{{.orderNumber}},shipment【In delivery】,Logistics Vendor:{{.logistics}},Logistics Number:{{.number}},,Expected Arrival Time:{{.expectedArrivalTime}}.
        send-with-other-logistics: Shipment No.:{{.orderNumber}},shipment【In delivery】,Logistics Vendor:{{.logistics}},Other Vendor:{{.other}},Logistics Number:{{.number}}.
        send-with-other-logistics-expectedTime: Shipment No.:{{.orderNumber}},shipment【In delivery】,Logistics Vendor:{{.logistics}},Other Vendor:{{.other}},Logistics Number:{{.number}},,Expected Arrival Time:{{.expectedArrivalTime}}.
        cancel-dtp: Shipment No.:{{.orderNumber}}, order 【Terminated】, termination reason:{{.reason}}.
        cancel: Cancel shipment, shipment has been cancelled, reason for cancellation:{{.reason}}.
        close: Close shipment, shipment closed, reason for closing:{{.reason}}.
        confirm-task: Send shipment confirmation task, shipment requested.
        confirm-task-finish: The shipment confirmation task has been completed and the shipment is in delivery.
        confrim: Confirm shipment, shipment requested.
        create: Create shipment, shipment to be confirmed.
        close-with-dtp: Shipment No.:{{.orderNumber}}, order 【closed】,Reason:All IP are replaced.
        close-with-register: Shipment No.:{{.orderNumber}}, order 【closed】,Reason:IP has been registered to be "available/frozen".
        close-with-register-lost: Shipment No.:{{.orderNumber}}, order 【closed】,Reason:IP has been registered to be "lost/Wasted".
        lost: Shipment lost, Shipment lost, reason for loss:{{.reason}}.
        receive: Shipment received.
        receive-task-confrim: Send IP receive task, shipment requested.
        receive-task-finish: The task of receiving IP has been completed, and the shipment has been received.
        receive-task-send: Send IPreceiving task, Order Shipping.
        send: Deliver the shipment. The shipment is in delivery.
        apply: Create shipment, shipment requested. remark:{{.remark}}.
        approval: Shipment application【approved】,order【To be confirmed】,Shipment No.:{{.orderNumber}}.
        change: 【Replacement】,Shipment No.:{{.orderNumber}},Original IP details:{{.oldMedicines}},Replacement IP details:{{.newMedicines}}, Reason for Replacement:{{.reason}}.
        batch: 【Edit】,Shipment No.:{{.orderNumber}}，IP-Expiration Date-Batch Number:{{.ipExpireDateBatch}}.
        expireDate: 【Edit】， {{.data}}
        expireDateBatch:
            orderNumber: Shipment No.：{{.orderNumber}}
            ipExpireDateBatch: IP-Expiration Date-Batch Number：{{.ipExpireDateBatch}}
    project:
        cohort:
            add: 【Add Cohort/Re-randomization】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},alert threshold:{{.reminderThresholds}}%
            delete: 【Cohort/Re-randomization Deleted】 name:{{.cohortName}}
            edcAdd: 【Add Cohort/Re-randomization】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},factor:{{.factor}},alert threshold:{{.reminderThresholds}}%
            edcEdit: 【Cohort/Re-randomization Edit】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},factor:{{.factor}},alert threshold:{{.reminderThresholds}}%
            edcRAdd: 【Add Cohort/Re-randomization】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},factor:{{.factor}},Previous Stage :{{.lastCohort}},alert threshold:{{.reminderThresholds}}%
            edcREdit: 【Cohort/Re-randomization Edit】 name:{{.cohortName}},capacity:{{.capacity}},status:{{.state}},factor:{{.factor}},Previous Stage :{{.lastCohort}},alert threshold:{{.reminderThresholds}}%
            edit: 【Cohort/Re-randomization Edit】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},alert threshold:{{.reminderThresholds}}%
            rAdd: 【Add Cohort/Re-randomization】 name:{{.cohortName}},capping:{{.capacity}},status:{{.state}},Previous Stage :{{.lastCohort}},alert threshold:{{.reminderThresholds}}%
            rEdit: 【Cohort/Re-randomization Edit】 name:{{.cohortName}},capacity:{{.capacity}},status:{{.state}},Previous Stage :{{.lastCohort}},alert threshold:{{.reminderThresholds}}%
        drugConfigur:
            add: Add IP configuration,group:{{.group}},IP configuration:{{.drugConfigure}}
            delete: Delete IP configuration,group:{{.group}},IP configuration:{{.drugConfigure}}
            edit: Edit IP configuration,group:{{.group}},IP configuration:{{.drugConfigure}}
        env:
            add: 【Adding Environment】Create {{.env}}environment
        info:
            add: 【Adding project】 project number:{{.projectNum}},project name:{{.projectName}},sponsor:{{.biddingUnit}},project description:{{.projectDesc}}
            edit: Edit project, project number:{{.projectNum}},project name:{{.projectName}},sponsor:{{.biddingUnit}},project description:{{.projectDesc}}
        medicine:
            batch: 【Batch Management】 The inventory batch number is updated:{{.batch}},expiry date:{{.expireDate}},status:{{.status}},update batch number:{{.batchUpdate}},update expiry date:{{.expireDateUpdate}}
            upload: 【Uploading IP】 IP name:{{.drugName}},quantity:{{.count}}
    randomization:
        attribute:
            properties:Randomization or not:{{.random}}, Display Randomization
            ID:{{.isRandomNumber}}, Dispensing or not:{{.dispensing}}, Blind or open
            label:{{.blind}}, Use prefix:{{.prefix}}, Subject No. prifix:{{.prefixExpression}}, Prefix text:{{.subjectReplaceText}},
            Exact number of digits (1:less than or equal to / 2:equal to):{{.accuracy}},
            Exact digit:{{.digit}}, When running the delivery algorithm, the quarantined items
            are calculated as part of the re usable inventory of the research institution:{{.isFreeze}},
            EDC docking IP Configuration Label:{{.edcDrugConfigLabel}}, Is
            the segment random:{{.segment}}{{.segmentLength}}
        block:
            distributionFactor: 【Random segmentation】 【{{.name}}】 Block:{{.block}} Distribution factor:{{.valueSite}}
            distributionSite: 【Random segmentation】 【{{.name}}】 Block:{{.block}}  Distribution site:{{.valueSite}}
            generate: 【Block randomization】【 Randomization ID generation】 Name:{{.name}},Initial number:{{.initialValue}},Group configuration group (weight ratio):{{.groupRatio}},Block configuration block length (number of blocks):{{.blockNumber}},Factor:{{.factors}},ID length:{{.numberLength}},Random seed:{{.seed}},Number prefix:{{.numberPrefix}},Generated quantity:{{.numberText}}
            upload: 【Block Randomization】【Randomization ID Upload】 Name:{{.name}},Factor:{{.factors}},Acceptable block size:{{.blockSize}},Number of uploads:{{.numberText}}
        config:
            block:
                distributionFactor: 【Random segmentation】 【{{.name}}】 Block:{{.block}} Distribution factor:{{.valueSite}}
                distributionSite: 【Random segmentation】 【{{.name}}】 Block:{{.block}}  Distribution site:{{.valueSite}}
                generate: 【Block randomization】【 Randomization ID generation】 Name:{{.name}},Initial number:{{.initialValue}},Group configuration group (weight ratio):{{.groupRatio}},Block configuration block length (number of blocks):{{.blockNumber}},Factor:{{.factors}},ID length:{{.numberLength}},Random seed:{{.seed}},Number prefix:{{.numberPrefix}},Generated quantity:{{.numberText}}
                upload: 【Block Randomization】【Randomization ID Upload】 Name:{{.name}},Factor:{{.factors}},Acceptable block size:{{.blockSize}},Number of uploads:{{.numberText}}
            factor:
                add: 【Factor Add】 Field Code:{{.number}}, Name:{{.label}} , Control type:{{ .type}}, Options:{{.options}}
                addEDC: 【Factor Add】 Field Code:{{.number}}, Name:{{.label}} ,Variable name:{{ .name}}, Control type:{{ .type}}, Options:{{.options}}
                clean: 【Empty other layers】 【{{ .name}}】 Block[{{ .block}}] Empty other layers
                countryEnable: 【Set country as stratification factor】 Enable
                delete: 【Factor Deleted】 Field Code:{{.oldNumber}}, Name:{{.oldLabel}} , Control type:{{ .oldType}}, Options {{.oldOptions}}
                deleteEDC: 【Factor Deleted】 Field Code:{{.oldNumber}}, Name:{{.oldLabel}} ,Variable name:{{ .oldName}}, Control type:{{ .oldType}}, Options {{.oldOptions}}
                disableCountryLayer: 【Set country as stratification factor】 Disable
                disableLayer: 【Set stratification factor】 Disable
                disableSiteLayer: 【Set site as stratification factor】 Disable
                edit: 【Stratification Factor Edit】 Replacement Field Code:{{.oldNumber}}, Name:{{.oldLabel}} , Control type:{{ .oldType}}, Options {{.oldOptions}} with Field Code:{{.number}}, Name:{{.label}} ,Variable name:{{ .name}}, Control type:{{ .type}}, Options:{{.options}}
                editEDC: 【Stratification Factor Edit】 Replacement Field Code:{{.oldNumber}}, Name:{{.oldLabel}} ,Variable name:{{ .oldName}}, Control type:{{ .oldType}}, Options {{.oldOptions}} with Field Code:{{.number}}, Name:{{.label}} ,Variable name:{{ .name}}, Control type:{{ .type}}, Options:{{.options}}
                number: 【Setting Stratification Factor number】 【{{ .name}}】  Stratification Factor:{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
                siteEnable: 【Stratification factor Edit】 Set site as stratification factor
            form:
                add: 【Adding Form】 Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}}
                addEDC: 【Adding Form】 Field name:{{.name}} Variable name:{{.label}}, Editable or Not:{{ .modifiable}}, Control type:{{.type}}
                addOption: 【Adding Form】 Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}}  Options:{{.options}}
                addOptionEDC: 【Adding Form】 Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}}  Options:{{.options}}
                clean: 【Clean other factor】 【{{ .name}}】 Block {{ .block}} clean other factor
                delete: 【Form Deletion】 Variable name:{{.label}}, Editable or Not:{{ .modifiable}}, Control type:{{.type}}
                deleteEDC: 【Form Deletion】 Field name:{{.name}} Variable name:{{.label}}, Editable or Not:{{ .modifiable}}, Control type:{{.type}}
                deleteOption: 【Form Deletion】Variable name:{{.label}}, Can be modified or not:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}}
                deleteOptionEDC: 【Form Deletion】Field name:{{.name}} Variable name:{{.label}}, Editable or Not:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}}
                edit: 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} ) replace (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} )
                editEDC: 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} ) replace  (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} )
                editOption: 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} )    Modify as (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}})
                editOptionEDC: 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}})    replace (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}})
                editOptionend: 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} )  Modify as (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
                editOptionendEDC: 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Editable or Not:{{ .oldModifiable}}, Control type:{{.oldType}} )    replace (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.oldOptions}})
                editOptionstart: 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}})    replaced by (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}})
                editOptionstartEDC: 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}})    replaced by (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}})
                editOptionstartend: 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}})    replaced by ( Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
                editOptionstartendEDC: 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}})    replaced by (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
                editend: 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} ) Modify as  (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
                editendEDC: 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} )  (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
                editstart: 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}}) replace  (Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} )
                editstartEDC: 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}}) replace  (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} )
                editstartend: 【Form Edit】 (Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}}) replace  ( Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
                editstartendEDC: 【Form Edit】 (Field name:{{.oldName}} Variable name:{{.oldLabel}}, Enable modification:{{ .oldModifiable}}, Control type:{{.oldType}} Options:{{.oldOptions}}) Modify as  (Field name:{{.name}} Variable name:{{.label}}, Enable modification:{{ .modifiable}}, Control type:{{.type}} Options:{{.options}})
            group:
                add: 【Adding Treatment Group】 Group Code:{{.number}},Group Name:{{.name}}
                delete: 【Treatment Group Deletion】  Group Code:{{.number}}, Group Name:{{.name}}
                edit: 【Treatment Group Edit】 Replace   Group Code:{{.oldNumber}}, Group Name:{{.oldName}} with Group Code:{{.number}}, Group Name:{{.name}}
            list:
                disableStatus: 【{{.name}}】 State adjustment Disable randomization Status
                enableStatus: 【status adjustment】 【{{.name}}】 Enable random state
                invalid: 【Wasted】Randomization list【{{.name}}】has been voided
            minimize: Minimizing random Name:{{.name}},Initial number:{{.initialValue}},Group configuration group (weight ratio):{{.groupRatio}}, Stratification factor stratification (weight ratio):{{.factors}},Bias probability:{{.probability}},Total cases:{{.total}},Number length:{{.numberLength}},Random seed:{{.seed}},Number prefix:{{.numberPrefix}}
            typeBlock: 【Randomization type adjustment】 Enable Block randomization
            typeMin: 【Randomization Type Adjustment】 Enable Minimized Randomization
        factor:
            clean: 【Empty other layers】 【{{ .name}}】 Block[{{ .block}}] Empty other layers
            addNumber: 【Add layers number】 【{{ .name}}】 layers:{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
            editNumber: 【Edit layers number】 【{{ .name}}】 layers:{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
            delNumber: 【Delete layers number】 【{{ .name}}】 layers:{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
            number: 【Setting Stratification Factor number】 【{{ .name}}】  Stratification Factor:{{.factor}}, Expected Number:{{.estimateNumber}}, Alert number:{{.warnNumber}}
        list:
            disableStatus: 【Status adjustment】 Disable randomization Status
            enableStatus: 【Invalidation】Randomization list has been invalidated
            invalid: 【Invalidation】Randomization list has been invalidated
            site: 【Edit】 Binding Center:{{.siteName}}
        minimize: Minimizing random Name:{{.name}},Initial number:{{.initialValue}},Group configuration group (weight ratio):{{.groupRatio}}, Stratification factor stratification (weight ratio):{{.factors}},Bias probability:{{.probability}},Total cases:{{.total}},Number length:{{.numberLength}},Random seed:{{.seed}},Number prefix:{{.numberPrefix}}
    subject:
        add: 【Add】 {{.content}}
        at-random-add: 【Add】 Stage:{{.stage}},{{.content}}
        delete: 【Delete】 {{.label}}：{{.name}}
        beReplaced: 【Be replaced The current】 subject is already a subject:{{.name}} replace
        replaced-new: 【Subject Replacement】original subjects:{{.name}},original Randomization ID:{{.beReplaceRandomNumber}}, replacement subjects:{{.replaceName}}, replacement subject Randomization ID:{{.replaceRandomNumber}}
        pvUnblinding: 【PV Unblinding]】 subject:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}}
        remark-pvUnblinding: 【PV Unblinding]】 subject:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}},Remark:{{.remark}}
        random: 【Randomization】 subject:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}}
        randomSub: 【Randomization】 subject:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}},Sub Group:{{.subGroup}}
        randomNoNumber: 【Randomization】 subject:{{.name}} It's already randomized,Group:{{.group}}
        randomNoNumberSub: 【Randomization】 subject:{{.name}} It's already randomized,Group:{{.group}},Sub Group:{{.subGroup}}
        replaced: 【Replacement】The current subject is derived from subject replacement,Replaced subjects:{{.name}} Randomization ID:{{.randomNumber}}
        signOut: 【Stop】 subject:{{.name}},Reason for Stop:{{.reason}}
        unblinding: "【Unblinding】 subject:{{.name}} Unblinding,Reasons for uncovering blindness:{{.reasonStr}},Remark:{{.reason}},Whether the Sponsor has been notified or not:{{.isSponsor}},Remark:{{.remark}}"
        unblinding-success: Unblinding (urgent) success
        at-random-unblinding-success: Stage:{{.stage}},Unblinding (urgent) success
        unblinding-application: "【Applied for unblinding (urgent)】approval number: {{.approvalNumber}}, status pending approval"
        at-random-unblinding-application: "【Applied for unblinding (urgent)】 Stage:{{.stage}},approval number: {{.approvalNumber}}, status pending approval"
        unblinding-application-pv: "【Applied for unblinding (pv)】approval number: {{.approvalNumber}}, status pending approval"
        at-random-unblinding-application-pv: "【Applied for unblinding (pv)】 Stage:{{.stage}},approval number: {{.approvalNumber}}, status pending approval"
        unblinding-approval-agree: "【Unblinding (urgent) approved】approval number: {{.approvalNumber}}, status passed"
        unblinding-approval-agree-pv: "【Unblinding (pv) approved】approval number: {{.approvalNumber}}, status passed"
        unblinding-approval-reject: "【Unblinding (Urgent) Approved】approval Number: {{.approvalNumber}}, Status Rejected, Reason: {{.reason}}"
        unblinding-approval-reject-pv: "【Unblinding (pv) Approved】approval Number: {{.approvalNumber}}, Status Rejected, Reason: {{.reason}}"
        update: 【Modify】 {{.content}}
        at-random-update: 【Modify】 Stage:{{.stage}},{{.content}}
        at-random-random: 【Randomization】 Stage:{{.stage}},{{.content}}
        transfer: 【Transfer】 {{.label}}:{{.subjectNo}},Original site:{{.oldSite}},New site:{{.newSite}}.
        switch-cohort: 【Switch Cohort】 {{.label}}：{{.subjectNo}}，Original cohort：{{.oldCohort}}，New cohort：{{.newCohort}}.
        at-random-transfer: 【Transfer】 Stage:{{.stage}},{{.label}}:{{.subjectNo}},Original site:{{.oldSite}},New site:{{.newSite}}.
        updateSubjectNo: 【Edit】 Subject:{{.oldSubjectNo}},Modify as:{{.shortname}}
        joinTime: 【Edit】 {{.label}}:{{.subjectNo}},Enrollment Time:{{.joinTime}}.
        at-random-joinTime: 【Edit】 Stage:{{.stage}},{{.label}}:{{.subjectNo}},Enrollment Time:{{.joinTime}}.
        start-follow-up-visits: 【Open follow-up visit stage】 Stage:{{.currentStage}},{{.label}}:{{.subjectNo}},current stage:{{.currentStage}},the follow-up stage:{{.nextStage}}
        close-follow-up-visits: 【Close follow-up visit stage】 Stage:{{.currentStage}},{{.label}}:{{.subjectNo}},current stage:{{.currentStage}},the follow-up stage:{{.nextStage}}
        label:
            common-key-value: "{{.name}}:{{.value}}"
            common-key-value1: "{{.name}}：{{.value}}"
            replaced-new: 【Subject Replacement】original {{.label}}:{{.name}},original Randomization ID:{{.beReplaceRandomNumber}}, replacement {{.label}}:{{.replaceName}}, replacement subject Randomization ID:{{.replaceRandomNumber}}
            at-random-replaced-new-a: 【Subject Replacement】 original{{.label}}:{{.name}},replacement{{.label}}:{{.replaceName}},Stage:{{.stage}},original Randomization ID:{{.beReplaceRandomNumber}},replacement subject Randomization ID:{{.replaceRandomNumber}}
            at-random-replaced-new-b: 【Subject Replacement】 original{{.label}}:{{.name}},replacement{{.label}}:{{.replaceName}},Stage:{{.stage}},original Randomization ID:{{.beReplaceRandomNumber}},replacement subject Randomization ID:{{.replaceRandomNumber}}；Stage:{{.stage2}},original Randomization ID:{{.beReplaceRandomNumber2}},replacement subject Randomization ID:{{.replaceRandomNumber2}}
            pvUnblinding: 【PV Unblinding]】 {{.label}}:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}}
            at-random-pvUnblinding: 【PV Unblinding]】 Stage:{{.stage}},{{.label}}:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}}
            remark-pvUnblinding: 【PV Unblinding]】 {{.label}}:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}},Remark:{{.remark}}
            at-random-remark-pvUnblinding: 【PV Unblinding]】 Stage:{{.stage}},{{.label}}:{{.name}} PV Unblinding,Reasons for unblinding:{{.reason}},Remark:{{.remark}}
            random: 【Randomization】 {{.label}}:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}}
            at-random-random: 【Randomization】 Stage:{{.stage}},{{.label}}:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}}
            randomSub: 【Randomization】 {{.label}}:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}},Sub Group:{{.subGroup}}
            at-random-randomSub: 【Randomization】 Stage:{{.stage}},{{.label}}:{{.name}} It's already random,Randomization ID:{{.randomNumber}},Group:{{.group}},Sub Group:{{.subGroup}}
            randomNoNumber: 【Randomization】 {{.label}}:{{.name}} It's already random,Group:{{.group}}
            at-random-randomNoNumber: 【Randomization】 Stage:{{.stage}},{{.label}}:{{.name}} It's already random,Group:{{.group}}
            randomNoNumberSub: 【Randomization】 {{.label}}:{{.name}} It's already random,Group:{{.group}},Sub Group:{{.subGroup}}
            at-random-randomNoNumberSub: 【Randomization】 Stage:{{.stage}},{{.label}}:{{.name}} It's already random,Group:{{.group}},Sub Group:{{.subGroup}}
            signOut: 【Stop】 {{.label}}:{{.name}},Reason for Stop:{{.reason}}
            at-random-signOut: 【Stop】 Stage:{{.stage}},{{.label}}:{{.name}},Reason for Stop:{{.reason}}
            signOutReal: 【Stop】 {{.label}}:{{.name}} It's already stop,Reason for Stop:{{.reason}},Actual Stopped Date:{{.signOutRealTime}}
            at-random-signOutReal: 【Stop】 Stage:{{.stage}},{{.label}}:{{.name}} It's already stop,Reason for Stop:{{.reason}},Actual Stopped Date:{{.signOutRealTime}}
            unblinding: "【Unblinding】 {{.label}}:{{.name}} Unblinding,Reasons for uncovering blindness:{{.reasonStr}},Remark:{{.reason}},Whether the Sponsor has been notified or not:{{.isSponsor}},Remark:{{.remark}}"
            at-random-unblinding: "【Unblinding】 Stage:{{.stage}},{{.label}}:{{.name}} Unblinding,Reasons for uncovering blindness:{{.reasonStr}},Remark:{{.reason}},Whether the Sponsor has been notified or not:{{.isSponsor}},Remark:{{.remark}}"
            updateSubjectNo: 【Edit】 {{.label}}:{{.oldSubjectNo}},Modify as:{{.shortname}}
            screen: 【Screening】{{.label}}:{{.name}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}},ICF Signed Date{{.icfTime}}
            at-random-screen: 【Screening】 Stage:{{.stage}},{{.label}}:{{.name}} It's already screening,Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}},ICF Signed Date{{.icfTime}}
            screenScreenFail: 【Screening】{{.label}}:{{.name}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}}
            update: 【Modify】{{.label}}:{{.name}},{{.content}}
            at-random-update: 【Modify】 Stage:{{.stage}},{{.label}}:{{.name}},{{.content}}
            updateCustomize: 【Modify】{{.label}}:{{.name}},{{.content}},{{.updateFields}}
            update2Customize: 【Edit】 {{.updateFields}}
            updateCustomizeConnectingSymbol: ","
            updateCustomizeLastSymbolKey: "."
            updateCustomizeJoinTime: Enrollment Time:{{.joinTime}}
            updateCustomizeStage: Stage:{{.stage}}
            updateCustomizeSignOutRealTime: Actual Stopped Date:{{.signOutRealTime}}
            updateCustomizeIsScreen: Screening successful or not:{{.isScreen}}
            updateCustomizeScreenTime: Screening Date:{{.screenTime}}
            updateCustomizeIcfTime: ICF Signed Date:{{.icfTime}}
            updateCustomizeReason: Reason for Stop:{{.reason}}
            updateCustomizeRemark: Complete study-remark：{{.remark}}
            updateSignOutTime: 【Modify】{{.label}}:{{.name}},{{.content}},Actual Stopped Date:{{.signOutRealTime}}
            updateScreen: 【Modify】{{.label}}:{{.name}},{{.content}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}},ICF Signed Date:{{.icfTime}}
            updateScreenSignOutTime: 【Modify】{{.label}}:{{.name}},{{.content}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}},ICF Signed Date:{{.icfTime}},Actual Stopped Date:{{.signOutRealTime}}
            updateScreenFail: 【Modify】{{.label}}:{{.name}},{{.content}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}}
            updateScreenFailSignOutTime: 【Modify】{{.label}}:{{.name}},{{.content}},Screening successful or not:{{.isScreen}},Screening Date:{{.screenTime}},Actual Stopped Date:{{.signOutRealTime}}
            finish: 【Complete Study】{{.label}}:{{.name}},Randomization ID:{{.randomNumber}},It's already complete study,Remark:{{.remark}}
            at-random-finish: 【Complete Study】 Stage:{{.stage}},{{.label}}:{{.name}},Randomization ID:{{.randomNumber}},It's already complete study,Remark:{{.remark}}
    supply-plan:
        add: 【new supply plan】 Supply Plan Name:{{.name}},plan description:{{.description}}
        update: 【edit supply plan】 Supply Plan Name:{{.name}},plan description:{{.description}}
    supply-plan-medicine:
        add: 【Adding IP configuration in supply plan】 IP:{{.medicineName}},Quantity of Initial Shipment:{{.initSupply}} , value:{{.warning}}, Maximum buffer:{{.buffer}},  Re-supply:{{.secondSupply}},Do not deliver days:{{.unDistributionDate}},Do not dispensation date:{{.unProvideDate}},   Expiration Reminder:{{.validityReminder}},    Automatic re-supply:{{.autoSupply}},  Automatic re-supply quantity:{{.autoSupplySize}},    Supplyment Mode:{{.supplyMode}}.
        update: 【edit IP configuration in supply plan】 IP:{{.medicineName}},Quantity of Initial Shipment:{{.initSupply}} , value:{{.warning}}, Maximum buffer:{{.buffer}},  Re-supply:{{.secondSupply}},Do not deliver days:{{.unDistributionDate}},Do not dispensation date:{{.unProvideDate}},   Expiration Reminder:{{.validityReminder}},   Automatic re-supply:{{.autoSupply}},  Automatic re-supply quantity:{{.autoSupplySize}},   Supplyment Mode:{{.supplyMode}}.
mail:
    test: Test
    text: Test {{.Name}}
medicine:
    autoSupplySize1: Maximum buffer
    autoSupplySize2: Re-supply
    errorSupplyMode: The way of supplement is inconsistent, Commit failed, Please modify again.
    errorAutoSupplyMode: Wrong! The Auto-Ration Mode is inconsistent.
    errorSupplyModeHint: The way of supplement is inconsistent.
    errorAutoSupplyModeHint: The Auto-Ration Mode is inconsistent.
    expire_title: Clinflash IRT  {{.projectNumber}} {{.envName}} IP Expiry Alert {{.instituteNumber}} {{.instituteNameEn}}
    freeze:
        title: Clinflash IRT  {{.projectNumber}} ({{.envName}}) IP Quarantine Notification {{.instituteInfoEn}} {{.freezeNumber}}
    release:
        title: Clinflash IRT  {{.projectNumber}} ({{.envName}}) IP Release Notification {{.instituteInfoEn}} {{.freezeNumber}}
    un_provide_date:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} IP reaches Do Not Dispense days reminder {{.siteNumber}} {{.siteNameEn}}
    status:
        available: Available
        delivered: Confirmed
        destroy: Destroy
        expired: Expired
        inStorage: In the warehouse process
        lose: Lose/Wasted
        quarantine: Quarantine
        receive: Received
        return: Returned
        sending: In Delivery
        stockPending: To be warehoused
        toBeConfirmed: To be confirmed
        toBeWarehoused: To be warehoused
        transit: In Delivery
        used: Used
        apply: Applied
        frozen: Frozen
        locked: Locked
    supplyMode1: Total IP supplementation
    supplyMode2: Single-product Supplement
    supplyMode3: Full-product Supplement Plus One Random IP
    supplyMode4: Single-product Supplement plus one random IP
medicineOrder_download_packageMethod: Deliver Mode
medicineOrder_download_packageMethodSingle: Item
medicineOrder_download_packageMethodPackage: Package
medicineOrder_download_packageNumber: Package Number
medicineOrder_download_batchNumber: Batch Number
medicineOrder_download_cancelDate: Cancellation Time
medicineOrder_download_cancelUser: Canceller
medicineOrder_download_count: IP Quantity
medicineOrder_download_createDate: Created Time
medicineOrder_download_createUser: Created By
medicineOrder_download_expiredDate: Expiration
medicineOrder_download_fileName: Order report
medicineOrder_download_medicineNumber: IP Number
medicineOrder_download_medicine: IP
medicineOrder_download_number: Shipment Number
medicineOrder_download_orderInfo: Shipment details
medicineOrder_download_other: Number
medicineOrder_download_receiveDate: Receiver Time
medicineOrder_download_expectedArrivalTime: Expected Arrival Time
medicineOrder_download_actualReceiptTime: Actual Receipt Time
medicineOrder_download_receiveInstitute: Destination
medicineOrder_download_receiveUser: Receiver
medicineOrder_download_sendInstitute: Origin
medicineOrder_download_status: Shipment Status
medicineOrder_download_cancelReason: Cancellation Reanson
medicineOrder_download_confirmUser: Confirmed By
medicineOrder_download_confirmDate: Confirm Time
medicineOrder_download_closeUser: Closed By
medicineOrder_download_closeDate: Closed Time
medicineOrder_download_closeReason: Reason for Closure
medicineOrder_download_sendUser: Delivered By
medicineOrder_download_sendDate: Delivery Time
medicineOrder_download_lostUser: Lost By
medicineOrder_download_lostDate: Lost/Wasted Operation Time
medicineOrder_download_lostReason: Reason for Loss
medicineOrder_download_endUser: Terminated By
medicineOrder_download_endDate: Terminated Time
medicineOrder_download_endReason: Reason for Termination
medicineOrder_download_supplier: Logistics Vendor
medicineOrder_download_supplierOther: Other Vendor
medicineOrder_download_supplierNumber: Tracking Number
medicine_batch_number: Batch Number
medicine_download_batch: Batch Number
medicine_download_spec: Specification
medicine_download_packageNumber: Package Number
medicine_download_depot_name: Depot Statistics Report
medicine_download_expiredDate: Expiration
medicine_download_location: Location
medicine_download_name: IP name
medicine_download_number: IP Number
medicine_download_orderNumber: Shipment Number
medicine_download_site: Site Name
medicine_download_site_name: Site Item Report
medicine_download_dtp_sku: IP item report
medicine_download_status: Status
medicine_download_storehouse: Depots
medicine_download_reason: Reason
medicine_download_operFree: Quarantine
medicine_download_operRelease: Lift
medicine_download_operLost: Lost/Wasted
medicine_download_operUse: Make Available
medicine_download_operator: Operator
medicine_download_time: Time
medicine_download_country: Country (Stratification Property)
medicine_download_region: Region (Stratification Property)
medicine_download_site_country: Country
medicine_download_site_region: Region
medicine_download_freeze_reason: Freeze Reason
medicine_download_freeze_operator: Quarantine Operator
medicine_download_freeze_time: Quarantine Operation Time
medicine_download_release_reason: Lifting Quarantine Reason
medicine_download_release_operator: Lifting Quarantine Operator
medicine_download_release_time: Lifting Quarantine Time
medicine_download_lost_reason: Lost/Wasted Reason
medicine_download_lost_operator: Lost/Wasted Operator
medicine_download_lost_time: Lost/Wasted Time
medicine_download_use_reason: Setting as available reason
medicine_download_use_operator: setting as available operator
medicine_download_use_time: Setting as available time
medicine_download_site_number: Site Number
medicine_duplicated: The IP already exists
medicine_duplicated_number: The IP number already exists
medicine_duplicated_package_number: The package number already exists
medicine_duplicated_serial_package_number: The package sequence number already exists
medicine_upload_package_number: The packaging number has not been uploaded, please upload it again.
medicine_upload_package_serial_number: The package sequence number has not been uploaded, please upload it again.
medicine_upload_package_count: Error. Please re-upload.
medicine_upload_package_serial_count: The quantity of package sequence number is incorrect, please re-upload.
medicine_duplicated_serial_number: The sequence number already exists
medicine_expiration_date: Expiration
medicine_list_download_name: IP list
medicine_name: IP name
medicine_not_exist: The IP does not exist
medicine_number: IP number
medicine_package: Package Number
medicine_package_barcode: Package Number-Barcode
medicine_package_barcode_short: Package Number & Barcode
medicine_code: Short Code
medicine_examine_uccess: Approved
medicine_examine_fail: Approval failed
medicine_examine_update: Modify
medicine_examine_release: Release
medicine_barcode_code: IP-Barcode
medicine_barcode_code_short: Barcode & Short Code
medicine_serial_number: Sequence Number
medicine_packlist_upload_check: IP number does not exist. Please upload IP number first
medicine_packlist_upload_firstPack: The first layer of packaging cannot be empty
medicine_status: Status
minimize_bias_probability_tips: Bias probability cannot be empty
minimize_layered_tips: Group stratification bias not captured, please confirm again.
project_edc_irt_return: Subject number prefix rules is inconsistent with EDC configuration, please reconfirm.
variable_duplicated: Variable ID is duplicate, please confirm again.
form_name_duplicated: Form name is duplicate, please confirm again.
option_label_duplicated: Add duplicate, please confirm again.
combined_dispensation_label_duplicated: The IP within the combined dispensation label needs to be needs to be consistent, please reconfirm.
form_invalid_error: Invalid Failed, field has been applied in the IP configuration.
update_medicine_status_error: The status of some IP products has changed, please select again.
is_screen: Screening successful or not
screen_time: Screening Date
icf_time: ICF Signed Date
operator:
    people: Operator
    reason: Reason
    time: Operation Time
    content: Operate Content
order:
    automatic_error: <p>Project number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Origin:{{.startEn}}</p> <p>Destination:{{.destinationEn}}</p> <p>Failed to create automatic shipment. Please check whether the automatic IP Dispensation Quantity matches the stock IP quantity.</p>
    automatic_error_dual: <p>Project number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Origin:{{.startEn}}</p> <p>Destination:{{.destinationEn}}</p> <p>Failed to generate an automatic IP shipment. Please check if the depot inventory is sufficient to cover the re-supply value.</p> <p></p>
    automatic_error_title: Clinflash IRT {{.projectNumber}} {{.envName}} Automatic shipment creation failed alert {{.destinationEn}}
    automatic_alarm_title: Clinflash IRT {{.projectNumber}}({{.envName}}) Automatic Shipment Alert
    automatic_alarm: "
        <p>中心编号:{{.siteNumber}}</p>
        <p>中心名称:{{.siteName}}</p>
        <p>According to the supply plan, do not need to create automatic IP shipment. Please check whether the supply plan values need to be adjusted or whether the IP inventory meets the current site IP needs.</p>
        <p>Site Number:{{.siteNumber}}</p>
        <p>Site Name:{{.siteNameEn}}</p>
        <p>According to the supply plan, do not need to create automatic IP shipment. Please check whether the supply plan values need to be adjusted or whether the IP inventory meets the current site IP needs.</p>"

    automatic_success_title: Clinflash IRT {{.projectNumber}}({{.envName}}) Automatic Shipment Notification {{.destinationEn}} {{.orderNumber}}
    cancel: " <p>Origin:{{.startEn}}</p>
        <p>Destination:{{.destinationEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Cancellation Reanson:{{.reason}}</p>
        <p>This shipment has been cancelled.</p>"
    cancel-logistics: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Cancellation Reason:{{.reason}}</p>
        <p>This shipment has been cancelled.</p>"
    close: "<p>Origin:{{.startEn}}</p>
        <p>Destination:{{.destinationEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Close Reason:{{.reason}}</p>
        <p>This shipment has been closed.</p>"
    close-logistics: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Close Reason:{{.reason}}</p>
        <p>This shipment has been closed.</p>"
    end: "<p>Origin:{{.startEn}}</p>
        <p>Destination:{{.destinationEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Terminate Reason:{{.reason}}</p>
        <p>This shipment has been terminated.</p>"
    end-logistics: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Express Logistics:{{.logistics}}</p>
        <p>Terminate Reason:{{.reason}}</p>
        <p>This shipment has been terminated.</p>"
    cancel_dtp: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Cancellation Reanson:{{.reason}}</p>
        <p>This shipment has been cancelled.</p>"
    cancel_dtp_sub: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Sub Group:{{.subGroup}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Cancellation Reanson:{{.reason}}</p>
        <p>This shipment has been cancelled.</p>"
    close_dtp: "<p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Close Reason:{{.reason}}</p>
        <p>This shipment has been closed.</p>"
    close_dtp_sub: "<p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Sub Group:{{.subGroup}}</p>
        <p>Visit Name:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Close Reason:{{.reason}}</p>
        <p>This shipment has been closed.</p>"
    lost_dtp: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Order Number:{{.orderNumber}}</p>
        <p>Loss Reason:{{.reason}}</p>
        <p>{{.userName}} confirmed this shipment was lost.</p>"
    lost_dtp_sub: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Sub Group:{{.subGroup}}</p
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Order Number:{{.orderNumber}}</p>
        <p>Loss Reason:{{.reason}}</p>
        <p>{{.userName}} confirmed this shipment was lost.</p>"
    cancel_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Cancellation Notification {{.destinationEn}} {{.orderNumber}}
    close_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Closure Notification {{.destinationEn}} {{.orderNumber}}
    end_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Terminated Notification {{.destinationEn}} {{.orderNumber}}
    cancel_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} Shipment Cancellation Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
    close_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Closure Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
    change_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP  Update  {{.destinationEn}} {{.orderNumber}}
    change_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} IP  Update  {{.subject}} {{.visit}} {{.orderNumber}}
    batch_expiration_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} IP Expiry Update {{.orderNumber}}
    lost: "<p>Origin:{{.startEn}}</p>
        <p>Destination:{{.destinationEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Loss Reason:{{.reason}}</p>
        <p>{{.userName}} confirmed this shipment was lost.</p>"
    lost_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Lost Notification {{.destinationEn}} {{.orderNumber}}
    lost_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Lost Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
    no_automatic: <p>Project number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Site number:{{.siteNumber}}</p> <p>Site name:{{.siteNameEn}}</p> <p>The IP in stock are lower than the warning value, please replenish them in time</p>
    no_automatic_dual: "<p>项目编号:{{.projectNumber}}</p>
        <p>项目名称:{{.projectName}}</p>
        <p>项目环境:{{.envName}}</p>
        <p>中心编号:{{.siteNumber}}</p>
        <p>中心名称:{{.siteName}}</p>
        <p>库存研究产品低于警戒值,请及时补充.</p>
        <p></p>
        <p>Project Number:{{.projectNumber}}</p>
        <p>Project Name:{{.projectName}}</p>
        <p>Project Environment:{{.envName}}</p>
        <p>Site Number:{{.siteNumber}}</p>
        <p>Site Name:{{.siteNameEn}}</p>
        <p>The IP quantity in stock is below the alert value, please replenish in time.</p>"
    no_automatic_success_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Confirmation Notification {{.destinationEn}} {{.orderNumber}}
    medicine_order_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Creation Notification {{.destinationEn}} {{.orderNumber}}
    medicine_order_dtp_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Creation Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
    forecast_title: Clinflash IRT {{.projectNumber}} {{.envName}} Site Inventory Usage Time Forecast
    no_automatic_title: Clinflash IRT {{.projectNumber}} {{.envName}} Site Inventory Alert Notification {{.siteNumber}} {{.siteNameEn}}
    over_title_depot: Clinflash IRT {{.projectNumber}} {{.envName}}  {{.siteNameEn}} Shipment Timeout Reminder {{.origination}} {{.orderNumber}}
    over_title_site: Clinflash IRT {{.projectNumber}} {{.envName}} Shipment Timeout Reminder {{.destinationEn}} {{.orderNumber}}
    overtime_depot: <p>Project number:{{.projectNumber}}</p> <p>Project name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Receiving Site No:{{.siteNameEn}}</p> <p>Shipment Number :{{.orderNumber}}</p> <p>There is an Shipment that has not been confirmed to be received, and the status is {{.statusItem}},Shipment generation time {{.generateDate}},Please confirm.</p>
    overtime_depot_dual: "<p>项目编号:{{.projectNumber}}</p>
        <p>项目名称:{{.projectName}}</p>
        <p>项目环境:{{.envName}}</p>
        <p>发送机构名称:{{.origination}}</p>
        <p>接收机构名称:{{.siteName}}</p>
        <p>订单编号:{{.orderNumber}}</p>
        <p>订单生成时间:{{.generateDate}}</p>
        <p>订单状态:{{.statusItem}}</p>
        <p>请确认.</p> <p></p>
        <p>Project number:{{.projectNumber}}</p>
        <p>Project name:{{.projectName}}</p>
        <p>Project Environment:{{.envName}}</p>
        <p>Origin:{{.origination}}</p>
        <p>Destination:{{.siteNameEn}}</p>
        <p>Shipment Number :{{.orderNumber}}</p>
        <p>Order generation Time: {{.generateDate}}</p>
        <p>Shipment Status: {{.statusItem}}</p>
        <p>This shipment has not been received, please confirm.</p>"
    overtime_site: <p>Project number:{{.projectNumber}}</p> <p>Project name:{{.projectName}}</p> <p>Project Environment:{{.envName}}</p> <p>Receiving Site No:{{.siteNumber}}</p> <p>Name of Receiving Site:{{.siteNameEn}}</p> <p>Shipment Number :{{.orderNumber}}</p> <p>There is an Shipment that has not been confirmed to be received, and the status is {{.statusItem}},Shipment generation time {{.generateDate}},Please confirm.</p>
    overtime_site_dual: "<p>项目编号:{{.projectNumber}}</p>
        <p>项目名称:{{.projectName}}</p>
        <p>项目环境:{{.envName}}</p>
        <p>起运地名称:{{.origination}}</p>
        <p>接收机构名称:{{.destinationEn}}</p>
        <p>订单编号:{{.orderNumber}}</p>
        <p>订单生成时间:{{.generateDate}}</p>
        <p>订单状态:{{.statusItem}}</p>
        <p>请确认</p>
        <p></p>
        <p>Project Number:{{.projectNumber}}</p>
        <p>Project Name:{{.projectName}}</p>
        <p>Project Environment:{{.envName}}</p>
        <p>Origin:{{.origination}}</p>
        <p>Destination: {{.destinationEn}}</p>
        <p>Shipment Number :{{.orderNumber}}</p>
        <p>Order generation Time: {{.generateDate}}</p>
        <p>Shipment Status: {{.statusItem}}</p>
        <p>This shipment has not been received, please confirm.</p>"
    receive: "<p>Origin:{{.startEn}}</p>
        <p>Destination:{{.destinationEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>{{.userName}} confirmed that this shipment had been received at the destination.</p>"
    receive-logistics: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Express Logistics:{{.logistics}}</p>
        <p>{{.userName}} confirmed that this shipment had been received at the destination.</p>"
    receive_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment  Receipt Notification {{.destinationEn}} {{.orderNumber}}
    receive_dtp: "<p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Visit Name:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Express Logistics:{{.logistics}}</p>
        <p>{{.userName}} confirmed that this shipment had been received at the destination.</p>"
    receive_dtp_sub: "<p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Sub Group:{{.subGroup}}</p>
        <p>Visit Name:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Express Logistics:{{.logistics}}</p>
        <p>{{.userName}} confirmed that this shipment had been received at the destination.</p>"
    receive_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment  Receipt Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
    recovery_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Return Shipment Creation Notification {{.destinationEn}} {{.orderNumber}}
    recovery_confirm_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Return Shipment Confirmation Notification {{.destinationEn}} {{.orderNumber}}
    send: "<p>Origin:{{.startEn}}</p>
        <p>Destination:{{.destinationEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Shipment is in delivery.</p>"
    send_dtp: "<p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Visit Name:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Express logistics:{{.logistics}}</p>
        <p>Shipment is in delivery.</p>"
    send_dtp_sub: "<p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Sub Group:{{.subGroup}}</p>
        <p>Visit Name:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Express logistics:{{.logistics}}</p>
        <p>Shipment is in delivery.</p>"
    send-logistics: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Express logistics:{{.logistics}}</p>
        <p>Shipment is in delivery.</p>"
    send_title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Delivery Notification {{.destinationEn}} {{.orderNumber}}
    send_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Delivery Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
    create_title_logistics: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Confirmation Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
    create_logistics: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>IP shipment is created.</p>"
    create_logistics_dtp: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>IP shipment is confirmed.</p>"
    confrim_logistics: "<p>Origin:{{.startEn}}</p>
        <p>{{.labelEn}}:{{.subject}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>IP shipment is confirmed.</p>"
    end_title_dtp: Clinflash IRT {{.projectNumber}} {{.envName}} IP Shipment Terminated Notification {{.subject}} {{.visitEn}} {{.orderNumber}}
    end_dtp: "<p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Express Logistics:{{.logistics}}</p>
        <p>Terminate Reason:{{.reason}}</p>
        <p>This shipment has been terminated.</p>"
    end_dtp_sub: "<p>{{.labelEn}}:{{.subject}}</p>
        <p>Randomization ID:{{.randomNumber}}</p>
        <p>Group:{{.group}}</p>
        <p>Sub Group:{{.subGroup}}</p>
        <p>Visit Cycle:{{.visitEn}}</p>
        <p>Shipment Number:{{.orderNumber}}</p>
        <p>Express Logistics:{{.logistics}}</p>
        <p>Terminate Reason:{{.reason}}</p>
        <p>This shipment has been terminated.</p>"
    approval:
        add-title: Clinflash IRT {{.projectNumber}} {{.envName}} Site Shipment Application
        failed-title: Clinflash IRT {{.projectNumber}} {{.envName}} Site Shipment Application Approval Failed
    single:
        start: "<p>Origin:{{.startEn}}</p>"
        subject: "<p>{{.labelEn}}:{{.subject}}</p>"
        visit: "<p>Visit Cycle:{{.visitEn}}</p>"
        orderNumber: "<p>Order Number:{{.orderNumber}}</p>"
        expectedArrivalTime: "<p>Expected Arrival Time:{{.expectedArrivalTime}}</p>"
        logistics: "<p>Express Logistics:{{.logistics}}</p>"
        lostReason: "<p>Loss Reason:{{.reason}}</p>"
        userName: "<p>{{.userName}} confirmed that this order was lost.</p>"
order_status_cancelled: Cancelled
order_status_not_cancelled: Current shipment status cannot be terminated, please refresh the page
order_status_lose: Lost
order_status_received: Received
order_status_requested: Confirmed
order_status_toBeConfirmed: To be Confirmed
order_status_transit: In Delivery
order_status_apply: Applied
order_status_terminated: Terminated
order_status_close: Closed
order_logistics_1: "SF Express"
order_logistics_2: "EMS"
order_logistics_3: "JD"
order_logistics_4: "YTO Express"
order_logistics_5: "UDA Express"
order_logistics_6: "ZTO"
order_logistics_7: "STO Express"
order_logistics_8: "J&T Express"
order_logistics_9: "Others"
page_notice:
    mail:
        send_fail: Fail in send
        send_success: Sent successfully
    system_update:
        email_error: Email address error
        others: Other
        quota_exceeded: The daily send quota of the main account has been exceeded
        timeout: Mail server timed out
project:
    env: "<p>Project Environment:{{.envName}}</p>"
    name: "<p>Project Name:{{.projectName}}</p>"
    number: "<p>Project Number:{{.projectNumber}}</p>"
    cohort: "<p>Name:{{.cohortName}}</p>"
    order:
        expectedArrivalTime: "<p>Expected Arrival Time:{{.expectedArrivalTime}}</p>"
    site:
        number: "<p>Site Number:{{.siteNumber}}</p>"
        name: "<p>Site Name:{{.siteNameEn}}</p>"
        delete: The site has been used and cannot be disabled
    storehouse:
        delete: The warehouse has been used and cannot be deleted
        had:
            add: The depot already exists.
        unConnected: The depot is not connected to the logistics. Please do not push the data
    user:
        join: <p>{{.customerName}}invites you to join the randomized experimental study</p> <p>Project number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p>
        title: Clinflash IRT Project invitation
projects:
    duplicated:
        names: The project name is already on this system
        numbers: Duplicated number, please re-enter
random:
    number: Randomization ID
randomList:
    export:
        available: available
        block: block
        center: site
        delete: Wasted
        inactivate: Invalid
        group: group
        number: randomization ID
        status: status
        subject: subject number
        used: used
randomNumber:
    export:
        status:
            invalid: Invalid
            used: Used
            unused: Available
            unavailable: Unavailable
    exist:
        used: Error, there is a used randomization ID in this block group, and other layers cannot be assigned, please confirm again.
        used.clean: Error, there is a used randomization ID in this block , and the stratification factor cannot be removed, please confirm again.
random_filed_error: Duplicated field, please confirm again.
random_attribute_error: Minimized randomization has been configured as "Not Applicable", please modify and re-create.
random_length_error:
    The generated randomization ID length has exceeded the set number length
    set number length
random_list_download_name: Source Randomization List Report
random_list_name_duplicated: Name already exists
random_number_block_error: Block must be unique, please confirm again.
random_number_error: Error, duplicated randomization ID, please confirm again.
random_number_format_error: Upload list format error, please confirm again.
random_number_format_error_trim: There are spaces in the uploaded data, please upload again
random_total_error: Error, The total number of Randomization IDs must be divisible by the sum of the group ratios, please confirm again.
random_duplicated_factor: The stratification factor already exists, please do not repeat the configuration
randomNumber_status_error: Random sequence number status has been changed. Please reconfirm.
randomization:
    accuracy:
        ones: Less than or equal to
        twos: equal to
    type:
        ones: Block randomization
        twos: Minimization random
    upload:
        blockSize: The block length in the randomization list is inconsistent with the acceptable block size configured in the system, please confirm again.
        group: Error, the group of the list is inconsistent with the configured treatment group
        group_error: Upload failed, the upload group does not match the system configuration, please reconfirm.
        group_tip_error: The number of source blinded groups uploaded does not match the system configuration, please confirm.
roles:
    delete:
        message: Role has been used, cannot be deleted
    duplicated:
        names: Added repeatedly, please modify
        sys_check: The menu permissions of the system administrator role cannot be modified
        project_admin_check: It is not allowed to modify the project administrator's project viewing permission
shipment_order_add_info:
    Because it is a blind project, please select 2 groups of
    IP names
shipment_order_buffer_info:
    The inventory quantity of the current site is greater
    than the maximum buffer quantity
shipment_order_cancel_not_exist: This outbound tracking number does not exist at the logistics site.
shipment_order_cancel_not_status:
    Logistics has delivered goods, cannot cancel the
    waybill
shipment_order_initial_info: Current Site Status does not allow repeated creation of  innitial shipment
shipment_order_create_error: No IP need to be created, please re-select
shipment_order_mode_info: When the Origin is the site, the supplement
    mode can only be the quantity supplement mode
shipment_order_over_warning_info: The available inventory of the current site is higher than the warning value
shipment_order_sendAndReceive_info: The origin and destination cannot be duplicated
shipment_order_dtp_info: DTP shipments do not allow partial confirmation or receipt
shipment_order_check_packageNumber: The packing quantity of the whole box is inconsistent with the configuration, please confirm again.
shipment_order_supply_info: The destination is not bound to a supply plan, please contact the project administrator to configure.
shipment_order_supply_page_error: 无法创建订单,“{{.currentName}}”为包装运输,同包装研究产品“{{.otherName}}”所属群组状态为”已完成/草稿/已终止“,请重新确认。
shipment_order_create_modeMax_info: The destination available inventory has exceeded the maximum buffer, and IP shipment is not created.
shipment_order_no_supply: Please configure the supply plan first.
shipment_order_ratio_err: 目的地中心未开启供应比例配置。
shipment_out_of_stock: Shipment create unsuccessfully due to Insufficient IP inventory, please contact administrator for supplyment.
shipment_change_out_of_stock: Shipment change unsuccessfully due to Insufficient IP inventory, please contact administrator for supplyment.
shipment_order_supplyRatio_create_fail: Shipment generated failed, IP quantity is not match with ratio, please reconfirm.
simulated:
    random:
        list:
            factor: Stratification Factor
            group: Group
            name: SimulationResult
            number: Randomization ID
            only: Only one random list is allowed to start
            runCount: Number of runs
            site: Site
            subject: Subject
        number:
            not:
                enough: The number of subjects must be less than the number of randomization ID
site:
    disabled: This site is disabled
    had:
        bind: Started site cannot delete
    name: Site name
    number: Site number
sites:
    duplicated:
        number: Duplicate number, please re-enter
storehouse:
    had:
        bind: The depot had been activated and can not be deleted.
storehouses:
    duplicated:
        number: Duplicated depot number
subject:
    alarm:
        content: <p>The number of subjects enrolled in different layers reached the alert value</p> <p>Layer/Alert Value/Actual Number:{{.info}}</p>
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Subject Alert
    alert_threshold:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Subject Limit Setting Reminder
        cohort_title: Clinflash IRT {{.projectNumber}} {{.envName}} Subject Limit Setting Reminder
    group: group
    number: Subject ID
    replace: Replace
    replace_subject: Replace subject ID
    replace_number: replace subject Randomization ID
    pvUnblinding:
        content:
            <p>Project number:{{.projectNumber}}</p> <p>Project Name:{{.projectName}}</p>
            <p>Project Environment:{{.envName}}</p> <p>Site number:{{.siteNumber}}</p> <p>Site
            name:{{.siteNameEn}}</p> <p>Subjects {{.subjectNumber}} Uncover the blind of PV,time:{{.time}}.reason：
            {{.reason}}</p>
        title: Clinflash IRT Subjects PvUnblinding
    random:
        content_no_group: <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Randomization Time:{{.time}}</p>
        content: <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Group:{{.group}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Randomization Time:{{.time}}</p>
        content_sub: <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p> <p>Randomization ID:{{.randomNumber}}</p> <p>Randomization Time:{{.time}}</p>
        content_no_random_number_no_group: <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Randomization Time:{{.time}}</p>
        content_no_random_number: <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Group:{{.group}}</p> <p>Randomization Time:{{.time}}</p>
        content_no_random_number_sub: <p>{{.labelEn}}:{{.subjectNumber}}</p> <p>Group:{{.group}}</p> <p>Sub Group:{{.subGroup}}</p> <p>Randomization Time:{{.time}}</p>
        fileName: Randomization Report
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Subject Randomization {{.siteNumber}} {{.siteNameEn}}
    add:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Subject Registration {{.siteNumber}} {{.siteNameEn}}
    status: Status
    status.registered: Registered
    status.random: Randomized
    status.sign.out: Deactivated
    signOut:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Subject Stop {{.siteNumber}} {{.siteNameEn}}
        content: <p>{{.labelEn}}:{{.subjectNumber}}</p>
            <p>Random Number:{{.randomNumber}}</p>
            <p>Group:{{.group}}</p>
            <p>Stop Time:{{.stopTime}}</p>
            <p>Reason:{{.reason}}</p>
        content_no_group: <p>{{.labelEn}}:{{.subjectNumber}}</p>
            <p>Random Number:{{.randomNumber}}</p>
            <p>Stop Time:{{.stopTime}}</p>
            <p>Reason:{{.reason}}</p>
        content_sub: <p>{{.labelEn}}:{{.subjectNumber}}</p>
            <p>Random Number:{{.randomNumber}}</p>
            <p>Group:{{.group}}</p>
            <p>Sub Group:{{.subGroup}}</p>
            <p>Stop Time:{{.stopTime}}</p>
            <p>Reason:{{.reason}}</p>
        content_no_random_number_no_group:
            <p>{{.labelEn}}:{{.subjectNumber}}</p>
            <p>Stop Time:{{.stopTime}}</p>
            <p>Reason:{{.reason}}</p>
        content_no_random_number: <p>{{.labelEn}}:{{.subjectNumber}}</p>
            <p>Group:{{.group}}</p>
            <p>Stop Time:{{.stopTime}}</p>
            <p>Reason:{{.reason}}</p>
        content_no_random_number_sub: <p>{{.labelEn}}:{{.subjectNumber}}</p>
            <p>Group:{{.group}}</p>
            <p>Sub Group:{{.subGroup}}</p>
            <p>Stop Time:{{.stopTime}}</p>
            <p>Reason:{{.reason}}</p>
    replacement:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Subject Replacement {{.siteNumber}} {{.siteNameEn}}
    modify:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Subject Modification {{.siteNumber}} {{.siteNameEn}}
    screen:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Subject Screening {{.siteNumber}} {{.siteNameEn}}
    status.unblinding: Emergency Unblinded
    status.screen.success: Screening successful
    status.screen.fail: Screening failed
    status.finish: Complete Study
    status.to.be.random: To be randomized
    status.join: Enrollment
    unblinding-approval:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Emergency Unblinding Approval Result {{.siteNumber}} {{.siteNameEn}}
        content: "<p>{{.labelEn}}:{{.subjectNumber}}</p>
            <p>Randomization ID:{{.randomNumber}}</p>
            <p>Emergency Unblinding Time:{{.time}} </p>
            <p>Emergency Unblinding Reason:{{.reason}}</p>
            <p>Remark:{{.remark}}</p>
            <p>Approval Code:{{.approvalNumber}}</p>
            <p>Approval Result:{{.approvalResultEn}}</p>
            <p>Reason:{{.rejectReason}}</p>"
    ordinary-unblinding-approval:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Emergency Unblinding Succeed {{.siteNumber}} {{.siteNameEn}}
        content: "<p>Project Number:{{.projectNumber}}</p>
            <p>Project Name:{{.projectName}}</p>
            <p>Project Environment:{{.envName}}</p>
            <p>Site number:{{.siteNumber}}</p>
            <p>Site name:{{.siteNameEn}}</p>
            <p>{{.labelEn}}:{{.subjectNumber}}</p>
            <p>Randomization ID:{{.randomNumber}}</p>
            <p>Emergency Unblinding Time:{{.time}} </p>
            <p>Emergency Unblinding Reason:{{.reason}}</p>"
    pv-unblinding-approval:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} PV Unblinding Approval Result {{.siteNumber}} {{.siteNameEn}}
        content: "<p>{{.labelEn}}:{{.subjectNumber}}</p>
            <p>Random Number:{{.randomNumber}}</p>
            <p>Pv Unblinding Time:{{.time}} </p>
            <p>Pv Unblinding Reason:{{.reason}}</p>
            <p>Remark:{{.remark}}</p>
            <p>Approval Code:{{.approvalNumber}}</p>
            <p>Approval Result:{{.approvalResultEn}}</p>
            <p>Reason:{{.rejectReason}}</p>"
    ip-unblinding-approval:
        title: Clinflash IRT {{.projectNumber}} {{.envName}} IP Unblinding Approval Result {{.siteNumber}} {{.siteName}}
        content: <p>{{.label}}:{{.subjectNumber}}</p>
            <p>随机号:{{.randomNumber}}</p>
            <p>研究产品揭盲时间:{{.time}} </p>
            <p>研究产品揭盲原因:{{.reason}}</p>
            <p>备注:{{.remark}}</p>
            <p>审批编号:{{.approvalNumber}}</p>
            <p>审批结果:{{.approvalResult}}</p>
            <p>原因:{{.rejectReason}}</p>
    unblinding:
        controlContent: <p>{{.labelEn}}:{{.subjectNumber}}</p>
            <p>Randomization ID:{{.randomNumber}}</p>
            <p>Emergency Unblinding Time:{{.time}}</p>
            <p>Emergency Unblinding Reason:{{.reason}}</p>
        content: "<p>{{.labelEn}}:{{.subjectNumber}}</p>
            <p>Randomization ID:{{.randomNumber}}</p>
            <p>Emergency Unblinding Time:{{.time}}</p>
            <p>Emergency Unblinding Reason:{{.reason}}</p>
            <p>Whether the Sponsor has been notified or not:{{.isSponsor}}</p>
            <p>Remark:{{.remark}}</p>"
        fileName: Unblinding Report
        sponsor: Whether the Sponsor has been notified or not
        title: Clinflash IRT {{.projectNumber}} {{.envName}} Emergency Unblinding Succeed {{.siteNumber}} {{.siteNameEn}}
subject_cohort_check: Randomization is not allowed when not enrolled at current stage
subject_cohort_check_register: Register failed,draft status is not allowed to register.
subject_register_number_fail: Registration failed, subject number has reached the max bit length.
subject_cohort_last_group: At the upper stage, the subject was not randomly enrolled
subject_factor_check_error: Randomization ID has exceeded the upper limit of the project or the project is not in the group status, please contact the system configuration personnel in time to make adjustments before operating the random number.
simulate_random_site_cont_error: Save failed, number of regions/countries must be smaller than or equal to number of sites.
subject_factor_no_null: Stratification factor cannot be empty
subject_medicine_count: Dispensation failed, the inventory of site is insufficient.
subject_medicine_batch_count: 发放失败，无可用有效期内发放的研究产品，请确认。
subject_medicine_count_store: Dispensation failed, the inventory of depot is insufficient.
subject_medicine_alarm_count: Dispensation failed, the inventory of site is insufficient.
subject_medicine_dose_error: Dispensation failed, unable to match the visit to the corresponding dose.
subject_medicine_dtp_error: The IP within the combined dispensation label needs to be needs to be consistent, please reconfirm.
subject_medicine_label_select: Please select IP label or IP
subject_medicine_label_select_outsize: Dispensation failed, no independent unscheduled visit dispensation rules configured, please confirm.
subject_medicine_count_real: There is no IP in stock, please re-enter
subject_medicine_count_real_same: The same batch of registered IP is not allowed, please re-enter
subject_medicine_other: Unnumbered IP
subject_no_random: There is no matched randomization ID, please confirm again.
subject_site_no_depot: Unable to randomize/dispense, site is unbound to the depot, please contact the administrator to configure.
subject_no_drug_configure: Current inventory is empty or ip is not configured,please contact system configurator to revise it and then operate again.
subject_no_enough_drug: Randomization failed, IP inventory is insufficient, please confirmed.
subject_no_register: Subjects were not registered
subject_replace_no_register_screenFail: Replacement failed, replacement subject was not registered/screening failed, please enter again.
subject_replace_no_site_fail: Replacement failed, replace subject number is not in current site, please confirm again.
subject_replace_no_site_fail_cohort: The replacement subject ID is not in the current group, please reconfirm.
subject_replace_no_site_fail_stage: The replacement subject ID is not in the current Stage, please reconfirm.
subject_replace_no_cohort_site_fail: Replacement failed, replace subject number is not in current cohort site, please confirm again.
subject_replace_no_stage_site_fail: Replacement failed, replace subject number is not in current stage site, please confirm again.
subject_replace_random: Replacement failed, replacement subject has been randomized, please enter again.
subject_replace_sign_out: Replacement failed, replacement subject has been stopped, please enter again.
subject_replace_register: Replacement failed, replacement subject has been registered, please enter again.
subject_no_replace:
    The subject number you entered has been randomly selected and
    cannot be replaced
subject_no_visit: Please perform registration after visit cycle configuration
subject_number_repeat: Subject already exists, please confirm again.
subject_transfer_fail: Transfer failed, new site subject number:{{.subjectNo}} is repeated, please confirm again.
subject_switch_cohort_fail1: Cohort switching is only supported for subjects whose status is pre-randomization.
subject_switch_cohort_fail2: The current project has established data integration with the EDC for pre-randomization modifications and does not support cohort switching
subject_switch_cohort_fail3: Cohort switching is not supported for subjects who have already had medication dispensed
subject_switch_cohort_fail4: The stratification configuration of the switched cohort differs from that of the current cohort's form. Please confirm
subject_random_error: The IP dispensation before randomization is not completed, can not randomize.
subject_random_number_existence:
    The Randomization ID already exists in the system
    and cannot be used
subject_status_no_cancel: Current subject status cannot be cancelled
subject_status_no_delete: Current subject status does not allow deleting.
subject_status_no_dispensing: Current subject status does not allow IP dispensation.
subject_status_no_join: Current subject status does not allow non-attending visit, please confirm again.
subject_status_no_random: Current subject status does not allow randomization
subject_status_no_reissue: Current subject status does not allow IP re-dispensation.
subject_status_no_replace: Current subject status does not allow subject replacement.
subject_status_no_replace_dispensing: Current subject status does not allow IP replacement.
subject_status_no_retrieval: Current subject status does not allow IP retrieving.
subject_status_no_sign_out: Current subject status does not allow stop.
subject_status_no_unblinding: Current subject status cannot be unblinded
subject_status_no_update: Current subject status does not allow modification.
subject_visit_dispensing: IP has been dispensed at current visit
subject_visit_dispensing_no_join: Current visit set as non-dispensation.
subject_visit_dispensing_no_order: Please dispense the IP in the order of visit
subject_visit_dispensing_no_order_confrim: Last visit shipment confirmation in pending.
subject_visit_dispensing_no_order_dtp: Current visit configuration cannot use Site  / Depo t(Send the IP directly to the patient), please check the configuration
subject_visit_dispensing_store: Site has not be assigned to the depot
subject_visit_dispensing_set_no_join: Current visit does not allow to perform 'Do Not Attend the Visit', please refresh the page.
subject_visit_dispensing_no_reissue: "Re-dispensation failed. Re-dispensation cannot be made for current unscheduled visit. Please select again."
subject_visit_dispensing_no_reissue_dose: "Re-dispensation failed, the option corresponding to visit judgment/dose level has been deleted, please reconfirm."
subject_visit_dispensing_no_site: "The site has not been assigned a depot yet"
subject_visit_dispensing_order_status: "Current shipment status does not allow registration of actually dispensed IP."
subject_visit_dispensing_order_status_last: "The shipment status of the last visit was not delivered, and the current visit cannot be applied"
subject_visit_cannot_cancel: "Current project property does not allow IP retrieval and withdrawal"
subject_visit_cannot_replace: "The current IP status does not allow replacement"
medicine_approval_err: "Current task has not been approved for release, Please contact the approver to complete the approval before applying for release."
medicine_release_err: Select at least one item or input quantity
upload_medicines_cell: Please delete the blank line data of Excel file
upload_medicines_drugName: IP names do not match
upload_medicines_info: Do not upload empty template data
upload_translate_info: Do not upload empty template data
random_type_error: The random type of the randomization list is inconsistent with the system configuration
random_group_error: The group of randomization list is inconsistent with system configuration
random_factor_error: The stratification factors/options for random list  is inconsistent with the system configuration
medicine_delete_error: The IP has been used by the shipment and cannot be deleted
factor_calc_not_match: The calculation result does not match any corresponding stratification, please reconfirm.
factor_not_match: There is no matched Stratification Factor, please confirm again
subject_visit_err: Template does not match, the current date has exceeded the minimum window period date, please confirm again.
subject.register.enrollment.full: Registration failed and the current status is enrollment full.
subject.dispensing.enrollment.full: Dispensation failed, the enrollment is full, please contact the administrator to confirm.
subject.register.screen.fail.enrollment.full: Screening failed and the current status is enrollment full.
user:
    accept: The user{{.userName}}, email:{{.email}} you invited has accepted the invitation
    createDate: Creation time
    customer:
        authorization:
            success: Authorization succeeded
        bind:
            error:
                There is no such user in the customer group. Please contact the administrator
                to add the user
            error1: There is no such user in the customer group
            success: Binding succeeded
    depot: Depots
    email: email
    exist:
        env: This user already exists in the environment
    invite: <p> You have been invited to join the Clinflash randomized trial study,<a href={{.url}}>System link</a>,Your initial password is:{{.password}},</p> <p>{{.url}}</p> <p>If the page does not open after clicking the link, Please copy the link to the browser address bar to access.</p>
    join: . <p> {{customerName}} {{.userName}} to invite you to join the randomization study. <a href = {{.url}} > accept the invitation</a> </p> <p> {{.url}} </p> <p> this link is valid for two days, please complete the operation ASAP. If the page does not open properly after clicking the link, manually copy the link and paste it into the address bar of the browser.</p>
    locking: Your account{{.email}}enter the wrong password 5 times,the account has been locked,please contact the administrator or click forget password.
    name: name
    no:
        exist: User does not exist
        exist.customer: User does not belong to the current customer
    notice:
        customer:
            bind:
                title: 「Customer Authorization Notification」
        project:
            bind:
                title: 「Project authorization notification」
        return:
            login: Return to login
        title: Project authorization notification
        customer-title: Customer Authorization Notification
    notice_customer: Your account {{.email}} has been successfully authorized to [{{.customer}}] customers, please log in to the system in time to check.
    notice_project: Your account {{.email}} is assigned to the new clinical trial project [{{.project}}], please log in to the system in time to check.
    password:
        error: Password error
    phone:
        exist: The mobile phone number is bound to the user
    resend:
        email:
            info: User status is active,don't need to send him/her an activation email
    resend_invite: Clinflash Cloud has created an account for you, please visit this link {{.link}} in 7 days to activate your account.If not operated by yourself, please ignore！
    resend_invite_title: Clinflash Cloud - Activate your account
    reset: <p> your password has been reset by the administrator, the new password is {{.Password}}. <a href = {.{url}} > system link </a> </p > <p> {{.Url}} < / p > <p> If the page does not open properly after clicking on the link, please manually copy the link and paste it into the address bar of your browser to visit the website.</p>
    retrieve: <p> You apply to reset your password via email, please ignore it unless it was operated by yourself. <a href = {{.url}} > reset password </a> </p> <p> {{.url}} </p> <p> this link is valid for 7 days, please complete the operation ASAP. If the page does not open properly after clicking the link, manually copy the link and paste it into the address bar of the browser.</p>
    roles: roles
    site: sites
    status: status
    status.activited: Activated
    status.not.active: Inactived
    status.open: Enabled
    status.unauthorized: Unauthorized
    status.enable: "Activate"
    status.disable: "Disabled"
users:
    authentication:
        failed: Authentication failed, please login again
    duplicated:
        customer: User already exists
        emails: Duplicated user emails
        cloud-customer: The user is disabled in the Cloud and cannot be added.
        cloud-disable: Disabled in the cloud
        cloud-delete: User has been deleted, please contact Cloud Admin to confirm.
        cloud-exist: The email already exists
    identify:
        code:
            incorrect: The verification code is incorrect
    missing:
        phone:
            The mobile phone number is incomplete. Please guide the user to log in
            to cloud and modify it in personal information
    phone:
        not:
            exist:
                The account does not exist or has no permission. Please contact the
                project administrator to add it
visit_cycle_duplicated_number: Duplicated visit cycle number
visit_cycle_dtp_required: Please select DTP mode
visit_cycle_duplicated_random1: Random visit already exists, please do not repeat the configuration
visit_cycle_duplicated_random2: For random items, only 2 random visits can be configured
visit_cycle_duplicated_random3: The visit has randomization and dispensation data, please do not modify the configuration
visit_cycle_duplicated_version: Duplicated visit cycle version number
visit_cycle_formula_error: The formula cannot be recognized, please re-enter it.
visit_cycle_formula_visit_error: The weight at the random visit comparison condition has been enabled. Please configure the random visit to allow dispense.
visit_cycle_visit_formula_error: The weight at the random visit is enabled to calculate the weight for this time. Please configure the random visit to allow dispense.
visit_cycle_formula_customer_error: The formula cannot be recognized, please re-enter it.
system_suggest_value: System suggest value
work:
    task:
        error: Task has been completed and cannot be repeated
        deleted: The task no longer exists. Please refresh and try again
        scan:
            error: This task does not require scanning code confirmation operation
        packageScan:
            error: This task does not require package scanning operation
        package_number:
            error: Scanning failed, the scanned IP is not a system-generated IP in the current project/environment.
        exist: A single operation has not been completed and cannot be repeated
        medicine:
            error: Scan list has been updated. Please refresh and try again
medicine_other_repeat: Add duplicate, please add again
medicine_drug_configure_check: Deletion failed,IP was used or produced inventory.
medicine_drug_configure_other_check2: 'Modification failed,"'
medicine_drug_configure_other_check: '" has inventory number '
medicine_drug_configure_other_check1: ", and can not re-calculate the inventory automatically and accurately, please reconfirm the data before operation."
medicine_other_repeat_formula: Formula add duplicate, please add again
medicine_open_setting_repeat: Configured "Open Configuration/Non-Open Configuration", it need to be consistent, please re-configuration
medicine_open_setting_repeat1: " Configured "
medicine_open_setting_openTrue: " Open Configuration "
medicine_open_setting_openFlase: " Non-Open Configuration "
medicine_open_setting_repeat2: " , it need to be consistent , please re-configuration. "
medicine_open_setting_repeat3: Dispensation method is inconsistent with existing configuration, please confirm configuration again.
medicine_open_setting_approval: Saving failed, it is not allow the addition of only a single blinded IP control.
site_not_delete: Site cannot set as invalid after subject registration, randomization/IP dispensation.
order_status_error: Shipment status abnormal, please return to the list and try again.
subject_status_error: The subject status is abnormal, please refresh the page and try again.
approval_task_error: The task status is abnormal, please refresh the page and try again.
urgentUnblinding_approval_task_error: The task status is abnormal, please refresh the page and try again.
wms:
    cancel_order_fail: Bioquick shipment cancellation failed
unblinding_code_error: Unbliding code error
unblinding_password_error: Wrong password
common_configuration_error: configuration error
subject_urgentUnblindingApproval_reason_other: Other
subject_urgentUnblindingApproval_reason_sae: SAE
subject_urgentUnblindingApproval_reason_pregnancy: Pregnancy
subject_urgentUnblindingApproval_reason_policy: Policy Requirements
subject_urgentUnblindingApproval_agree: Passed
subject_urgentUnblindingApproval_reject: Refused
subject_urgentUnblindingApproval_applicationed: The subject unblinding (urgent) application has been submitted, please wait for the approval of the approver.
subject_urgentUnblindingApproval_pv_applicationed: The subject unblinding (pv) application has been submitted, please wait for the approval of the approver.
subject_urgentUnblindingApproval_ip_applicationed: The subject unblinding (ip) application has been submitted, please wait for the approval of the approver.
supply_plan_duplicated_name: Duplicated name, please enter again.
edc_push_subject_number: Subject Number
edc_push_randomization_number: Randomization number
edc_push_group: Group
edc_push_randomization_time: Randomization Time
edc_push_visit_number: Visit Number
edc_push_dispense: Dispense
edc_push_dispense_time: Dispensing Time
edc_push_drug: drug
edc_push_drug_level: Dose Level
edc_push_drug_label: Label
edc_push_cohort: Cohort/Stage
edc_push_edc_return: Return from EDC

edc_push_error_code_200: Succeeded
edc_push_error_code_201: Timestamp, signature and base parameters are wrong, please contact IRT engineer to confirm.
edc_push_error_code_202: Project number is wrong, please contact IRT configuration administrator to confirm.
edc_push_error_code_203: Environment number is wrong, please contact IRT configuration administrator to confirm.
edc_push_error_code_204: Site number is error, please contact IRT configuration administrator to confirm.
edc_push_error_code_205: Subject duplicate, please contact EDC configuration administrator for manual processing.
edc_push_error_code_206: Subject number prefix rule is inconsistent, please contact EDC and IRT configuration administrator to confirm.
edc_push_error_code_207: Modification failed, the target subject number already exists, please reconfirm.
edc_push_error_code_208: Subject creation is in progress, please operate later.
edc_push_error_code_209: Creation failed, EDC site version not pushed, please contact EDC configuration administrator to confirm.
edc_push_error_code_210: Creation failed, abbreviations are missing, please reconfirm.
edc_push_error_code_211: Failed to create, please contact EDC engineer for confirmation.
edc_push_error_code_212: EDC data saving resolution error, please contact EDC engineer to confirm.
edc_push_error_code_213: EDC data request parsing error, please contact EDC engineer to confirm.
edc_push_error_code_299: Other undefined errors, please contact EDC engineer to confirm.

project_dynamics_scene_personnel: Personnel
project_dynamics_scene_order: Order
project_dynamics_scene_unblinding: Unblinding
project_dynamics_scene_forecast: 库存
project_dynamics_scene_visit: 访视
project_dynamics_type_enter_site: Enter the site
project_dynamics_type_bind_storehouse: Assign depot
project_dynamics_type_role_assignment: Role Assignment
project_dynamics_type_overtime: Overtime
project_dynamics_type_emergency_unblinding: Emergency unblinding
project_dynamics_type_emergency_unblinding_pv: Pv unblinding
project_dynamics_type_alert_storehouse: Depot alert
project_dynamics_type_forecast: Inventory Prediction Time
project_dynamics_type_visit: 访视超窗
project_dynamics_content_enter_site: 【{{.siteName}}】<a>{{.email}}</a>Entered the site
project_dynamics_content_bind_storehouse: 【{{.storehouseName}}】<a>{{.email}}</a>Storehouse bound
project_dynamics_content_role_assignment: 【Role Assignment】<a>{{.email}}</a>Assigned{{.roles}}Role
project_dynamics_content_overtime: 【Shipment timeout】<a>{{.orderNumber}}</a>Time out
project_dynamics_content_emergency_unblinding: 【{{.siteName}}】<a>{{.subjectName}}</a>Unblinded
project_dynamics_content_emergency_unblinding_emergency: 【{{.siteName}}】<a>{{.subjectName}}</a>Unblinded(Emergency)
project_dynamics_content_emergency_unblinding_pv: 【{{.siteName}}】<a>{{.subjectName}}</a>Unblinded(PV)
project_dynamics_content_forecast: 【<a>{{.siteName}}</a>】inventory available time forecast reminder.
project_dynamics_content_alert_storehouse: 【<a>{{.storehouseName}}</a>】The IP in the storehouse has reached the alert value
project_dynamics_content_visit: 【{{.siteName}}】【{{.subject}}】{{.visit}} overdue.
barcode_error: Saving failed, please close the packaging number function first
barcode_rule: IP barcode generation rules must be consistent. Please confirm.
barcode_package_rule: Packaging barcode generation rules must be consistent. Please confirm.
form_field_used: The field has been used and cannot be deleted
planned_case_error: Current number of randomizad subjects has exceeded the enrollment capping of project, please contact the system configuration personnel in time to adjust and then operate randomization
planned_case_random_error: Randomization failed and the project has reached the enrollment threshold upper limit.
planned_case_register_error: Registration failed and the project has reached the registration upper limit.
planned_case_screen_error: Screening failed and the project has reached the screening success upper limit.
plan_number: Planned Randomization Number
subject_replace_auto_error: Replacement failed. The replacement random ID conflicts with the currently active randomization list. Please modify the rules before proceeding.
random_list_upload_error: The stratification factors are inconsistent, please synchronize before proceeding.
subject_random_factor_error: Randomization failed, please update the latest stratification results before proceeding.
simulate_random_factor_error: The system detects that the factors of different randomization list are not consistent, and it can't  randomization simulation at the same time. Please modify and operate after consistency.
simulate_random_factor_ratio_error: Randomization Simulation stratification configuration differs from actual stratification configuration. Please edit before running.
simulate_random_factor_ratio_list_error: System has detected an inconsistency between the random configuration and the randomization list. Please modify to ensure consistency before proceeding.
simulate_random_factor_ratio_total_error: Total stratification case numbers do not match the randomization count. Please adjust.
simulate_random_factor_ratio_lack_error: Stratification case numbers are missing. Please complete them.
subject_random_sequence_number_start_max_error: The start value of the sequence number exceeds the digit limit. Please check the random number configuration.
edc_register_error: Registration failed, the draft state does not allow registration, please contact the IRT project administrator, modify the cohort status to "Enrolling" before operation.
factor_calc_mapping_converge: Mapping relations converge, please confirm again.
randomization_config_factor_not_calc_form: The form field corresponding to the custom formula variable ID is missing. Please check the configuration.
randomization_config_factor_not_calc: Variable ID configuration calculation conflict, please confirm again.
randomization_config_factor_not_calc_type: Variable ID configuration control conflict, please confirm again.
mail_security: This email contains a secure link. Please do not share this email, link, or access code with others.
mail_noreply: This email is sent automatically by the system, please do not reply directly.
mail_copyright: Copyright ©2020 Clinflash Healthcare Technology (Jiaxing) Co.,Ltd. All Rights Reserved
mail_upper_half: '<!DOCTYPE html>
    <html lang="en">
    <head>
    <meta charset="utf-8"/>
    <title>Clinflash IRT</title>
    <style>
    .text-body {
    font-size: 14px;
    color: dimgrey;
    }
    </style>
    </head>
    <body>
    <table align="center" border="0" cellspacing="0" cellpadding="0" width="600"
    style="font-size: 14px; background-color: #fff; table-layout: fixed; border-top: #0A47ED solid 6px;">
    <tbody>
    <tr>
    <td colspan="12" style="padding: 40px 0 40px 50px;">
    <img style="width: 238px; height: 40px;"
    src="{{.irt_url}}/api/img/mail"/>
    </td>
    </tr>
    <tr>
    <td colspan="12" style="padding: 5px 50px;">
    <div class="text-body">'
mail_lower_half: ' </div>
    </td>
    </tr>
    </tbody>
    <tfoot>
    <tr>
    <td colspan="12"
    style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3">
    <div>This email contains a secure link. Please do not share this email, link, or access code with others.</div>
    <div>This email is sent automatically by the system, please do not reply directly.</div>
    <div>Copyright ©2020 Clinflash Healthcare Technology (Jiaxing) Co.,Ltd. All Rights Reserved</div>
    </td>
    </tr>
    </tfoot>
    </table>
    </body>'
operation_log:
    label:
        medicine: IP Name
        site: Site Number
        dept: Depot Name
        supply: Supply Plan Name
        group: Group Code
        factor: Stratification Factor
        list: Randomization List Name
        invalidList: Invalidate Randomization List
        visitCycle: Visit Cycle
        drugConfigure: Treatment Design
        packageConfigure: Package Configuration
        medicinesList: Packlist
        updateBatch: Batch Management
        otherMedicines: Unnumbered IP
        simulate_random_name: Name
        name: Field Name
        attribute: Properties
        uploadMedicine: Upload IP
        barcode_add: Generate Barcode
        uploadPacklist: Upload Packlist
        deleteMedicine: Mass Delete IP
        projectUser: User
        project: Project settings
        barcode_label: Label ID
    module:
        barcode: Project Design-Coding Configuration
        supply: Project Design-Supply Plan
        supply_detail: Project Design-Supply Plan-Supply Plan Detail
        random_design: Project Design-Randomization Design-Randomization Design
        project_site: Project Design-Site Management
        project_storehouse: Project Design-Depot Management
        project_storehouse_medicine_alert: Project Design-Depot Management-IP Alert
        visitCycle: Project Design-Treatment Design-Visit Cycle
        visitSetting: Project Design-Treatment Design-Visit Cycle-Setting
        attribute: Project Design-Properties
        drug_configure: Project Design-Treatment Design-Treatment Design
        drug_configure_setting: Project Design-Treatment Design-Treatment Design-Setting
        package_configure: Project Design-Packlist- Configuration
        examine: 项目构建-研究产品列表-审核
        update: 项目构建-研究产品列表-修改
        release: 项目构建-研究产品列表-放行
        barcode_add: Project Design-Treatment Design-Barcode List-Generate Barcode
        barcode_label: Project Design-Treatment Design-Label Management
        work_task_add: Project Design-Treatment Design-Scan for Warehousing
        medicinesList_uploadMedicine: Project Design-Treatment Design-Packlist-Upload
        medicinesList_uploadPacklist: Project Design-Treatment Design-Packlist-Upload Packlist
        medicinesList_delete: Project Design-Treatment Design-Packlist-Mass Delete
        updateBatch: Project Design-Treatment Design-Batch Management
        otherMedicines: Project Design-Treatment Design-Unnumbered IP
        form: Project Design-Randomization Design-Region configuration
        simulate_random: Project Design-Randomization Simulation
        push: Project Design-Statistics-Send
        project: Project settings
        projectUser_role: Other settings-User Management-Role
        projectUser_site: Other settings-User Management-Site
        projectUser_depot: Other Settings - User Management - Depot
        projectUser: Other settings-User Management
        user: Settings-Users
        project_information: Project settings-Basic Information
        project_env: Project settings-Project Environments
        project_function: Project settings-Business Functions
        project_docking: Project settings-External Sync
        project_custom: Project settings-Custom Process
        project_permission: Project settings-Project Permissions
        notifications: Other Settings-Notifications
        configure_export: Other Settings-Download Configuration
        project_basic_information: Project settings-Basic Information
        subjects: Subjects
        project_notice: Project Notification
        project_multi_language: Multilingual
        project_multi_language_translate: Multilingual-
        project_multi_language_batch_upload: Multilingual-
    add: Add
    edit: Edit
    delete: Delete
    copy: Copy
    run: Run
    unbind: Unbind
    close: Close
    setting: Setting
    cancel: Cancel
    reauthorization: Reauthorization
    invite_again: Invite Again
    export: Export
    project_copy: Project Copy
    cohort_copy: Copy
    activate: Activate
    inactivate: Inactivate
    barcode:
        env_name: Environment
        random: Coding Rules
        manual: Manual Coding Upload
        auto: Automatic Coding
    supply:
        env_name: Environment
        name: Supply Plan Name
        status: Plan Status
        status_effective: valid
        status_invalid: invalid
        site: Plan Applicable Site
        all_site: All Site
        desc: Plan Description
        control: Supply Plan Control
        alarm: Site Inventory Alert
        supply: Automatic Supply
        blindMedicine: Blind IP
        openMedicine: Open IP
        forecastStop: Blind IP minimum predicted automatic supply shutdown
    supply_detail:
        env_name: Environment
        name: IP
        init_supply: Quantity of Initial Shipment
        warning: Site Inventory Alert Value
        dispensing_alert: Subject dispensation alert value
        na: NA
        buffer: Maximum Buffer
        second_supply: Re-supply
        forecast: Minimum prediction
        un_distribution_date: Do Not Deliver
        un_provide_date: Do Not Dispense Setting
        validity_reminder: Expiration Reminder
        auto_supply_size: automatic re-supply quantity
        supply_mode_key: Supply Mode
        forecastPeriod: Forecast Window
        supply_mode:
            all_supply: Full-IP Supplement
            single: Single-product Supplement
            all_one: Full-IP Supplement Plus One Random IP
            single_one: Single-product Supplement Plus One Random IP
    region:
        name: name
        env_name: Environment
    random_design:
        env_name: Environment
        factorLabel: Stratification Factor
        sync: Synchronization
        inactivate: Synchronization
        list: Randomization List Name
        type: Randomization Type
        block: Block Randomization
        min: Minimized Randomization
        group_name: Group Name
        group_code: Group Code
        status: Status
        status_effective: valid
        status_invalid: Invalid
        factor:
            layer: Set region as stratification factor
            list: Enrollment Capping
            number: Field Code
            name: System Field
            calcType: Calculation Formula
            formula: Custom formula
            keepDecimal: Keep Decimal Places
            roundingMethod: Keep Decimal Places - Rounding Method
            roundingMethod_up: Ceiling rounding
            roundingMethod_down: Floor rounding
            calcType_age: Age
            calcType_bmi: BMI
            inputLabel: Enter Field Name
            inputWeightLabel: Enter Weight Field Name
            inputHeightLabel: Enter Height Field Name
            label: Field Name
            type: Control Type
            options: Option
            options_label_value: Option Label
            folder_oid: Folder OID
            form_oid: Form OID
            field_oid: Field OID
            disable: Set regions as stratification factors is prohbited
            country: Set Country as stratification factor
            site: Set Site as stratification factor
            region: Set Regions as stratification factors
            status: Status
            precision: Keep Decimal Places
            status_effective: valid
            status_invalid: Invalid
        mapping:
            random:
                folder_oid: Randomization ID-Folder OID
                form_oid: Randomization ID-Form OID
                field_oid: Randomization ID-Field OID
            group:
                folder_oid: Group-Folder OID
                form_oid: Group-Form OID
                field_oid: Group-Field OID
            time:
                folder_oid: Randomization Time-Folder OID
                form_oid: Randomization Time-Form OID
                field_oid: Randomization Time-Field OID
    random_list:
        env_name: Environment
        onlyID: Form ID
        name: Name
        site: Site
        initial_number: Initial Number
        end_number: Termination Number
        block_rule: Block Rules
        random_number_rule: Randomization ID Rule
        block_rule_order: Order
        block_rule_reverse: Disorder
        random_number_rule_order: Order
        random_number_rule_reverse: Disorder
        weight_ratio: Group Configuration(weight ratio)
        block_configuration: Block Configuration
        factor_ratio: Stratification Factor(weight ratio)
        number_length: Number Length
        seed: Randomization Seed
        prefix: Number Prefix
        size: Acceptable Block Size(Use ‘,’ to separate multiple blocks)
        status: Status
        disable: Disable
        enable: Enable
        invalid: Invalidate
        isRandom: Site cannot enroll subjects without assigned randomization IDs.
        isCountryRandom: Site cannot enroll subjects if random IDs are not assigned to  countries
        isRegionRandom: Site cannot enroll subjects if the randomization ID has not been assigned to the region.
        set_site: Assign Block to Site
        set_region: Assign Block to Region
        set_country: Assign Block to Country
        set_factor: Assign Block to Stratification Factor
        clean_factor: Clear Other Stratification factors
        set_count: Set Quantity
        file_name: Upload
        factor: Stratification Factor
        estimateNumber: Estimated Number
        warnNumber: Subject Alert Number
        block: Block
        randomNumber: Randomization Number
        randomNumberStatus: Status
    visitCycle:
        env_name: Environment
        type: Visit Offset Type
        baseCohort: baseline基准
        sort: Sort
        number: Visit Number
        name: Visit Name
        random: Randomize or Not
        dispensing: Dispense or Not
        replace: Allowed To Subject Replace
        doseAdjustment: Dose Adjustment
        startDays: Start Day
        endDays: End Day
        folder_oid: Folder OID
        form_oid: Form OID
        dispensing_ip_oid: Dispensation OID
        dispensing_time_oid: Dispensation time OID
        interval: Interval Duration
        period: Window
        group: Group
        baseline: Baseline
        lastdate: Lastdate
        sop: Visit Process
        version: Visit Cycle Version Number
        push: Preview and release
        label: Label
        open_setting: Open Configuration
        formula: Formula
        DTPMode: DTP mode
        dtp: DTP or Not
        send-type-0: Site(Site Inventory)
        send-type-1: Site(Direct-to-Patient Shipment)
        send-type-2: Deport(Direct-to-Patient Shipment)
    visitSetting:
        env_name: Environment
        unscheduled_visit: Unscheduled Visit
        name_zh: Chinese Name
        name_en: English Name
    drug_configure:
        env_name: Environment
        onlyID: Form ID
        group: Group
        preGroup: 主组别
        subGroup: Sub Group
        drugValue: IP Name/Dispensation Quantity/IP Specification
        open: Open Configuration
        formula: According to the calculation formula
        spec: Spec
        calculationType: Formula
        age: Age
        weight: Weight
        bsa: Simple body surface area BSA
        otherBsa: Other body surface area BSA
        visitCycles: Visit Name
        label: (Combined) Dispensation label
        drugLabel: Dispensation label
        room: Room Number
        isDispense: Not Dispense Setting
        notDispenseConfig: IP Name-Do Not Dispense Setting
        isOpenPackage: Shipped by Package
        isOpenApplication: Site Shipment Application
        supplyRatio: Supply Ratio
        orderApplictionConfig: IP Name-Ratio
        packageConfig: Mixed Packages
        packageConfigNew: Mixed Packages
        drugNameSpec: IP Name/IP Specification
        visitDrugNameDispeningNumber: Visit Name/IP Name/Dispensation Quantity
        weightDispeningNumber: Weight Range/Dispensation Quantity
        ageDispeningNumber: Age Range/Dispensation Quantity
        dispensingNumber: Dispensation Quantity
        specifications: Unit Capacity
        standard: Unit Calculation Standard
        comparisonSwitch: Weight Comparison Calculation
        comparisonType: Comparison Conditions
        comparisonRatio: The change is
        currentComparisonType: Is used for this calculatio
        otherCheck: Unnumbered IP
        openCheck: Open IP
        keepDecimal: Keep Decimal Places
        precision: Number of decimal places
        automatic_recode: Automatic Assignment
        automatic_recode_spec: Calculation Unit
        check: Check
        uncheck: Unchecked
        calculationOpen: Open
        calculationUnOpen: Unopened
        weightCalculation: The calculated weight at the last visit
        weightActual: The actual weight at the last visit
        weightRandom: The weight at the random visit
        customerCalculation: Custom formula
    drug_configure_setting:
        env_name: Environment
        dtp_ipType: IP/Dispensation Method
        dtp_ipType_site: Site(Site Inventory)
        dtp_ipType_siteSubject: Site(Direct-to-Patient Shipment)
        dtp_ipType_depotSubject: Depot(Direct-to-Patient Shipment)
        doseAdjustment: Dose Adjustment
        selectType: Dose Selection
        doseForm: Dose Form
        isFirstInitial: Initial dose enabled at the first visit
        isDoseReduction: Allows the subject to reduce the dose a certain number of times
        frequency: Frequency
        doseLevel: Dose Level
        doseLevelID: ID
        doseLevelName: Name
        doseLevelGroup: Group
        doseLevelDoseDistribution: Dispensation Dose
        doseLevelInitialDose: Initial Dose
        visitJudgment: Visit Judgment
        visitJudgmentID: ID
        visitJudgmentName: Name/Value
        visitJudgmentGroup: Group
        visitJudgmentDoseDistribution: Dispensation Dose
        visitJudgmentVisitInheritance: Subsequent visits inherit
        visitJudgmentVisitInheritanceCount: Number of Subsequent visits inherit
        InheritanceGroup: Group of Subsequent visits inherit
        InheritanceRule: Rule of Subsequent visits inherit
        groupRuleMain: Main Visit
        groupRuleStop: Stop Dispensation
    drug_examine:
        nameDateNumberCount: 研究产品名称/有效期/批次号/数量
        examineConfig: 审核确认
        examineNotes: 备注
    drug_update:
        nameDateNumberCount: 研究产品名称/有效期/批次号/数量
    addBarcode:
        env_name: Environment
        medicineName: IP Name
        medicineSpec: IP Specification
        storehouse: Depot
        expireDate: Expiration
        batchNumber: Batch Number
        count: Barcode Quantity
        prefix: Short Code prefix
        barcodeRule: 研究产品条形码规则
        isPackageBarcode: 包装条形码
        packageRule: 包装条形码规则
        rule_order: Order
        rule_reverse: Disorder
        openPackageBarcode: Open
        closePackageBarcode: Close
    uploadMedicines:
        env_name: Environment
        onlyID: Form ID
        medicineName: IP Name
        medicineSpec: IP Specification
        storehouse: Depot
        expireDate: Expiration
        batchNumber: Batch Number
        count: Quantity
        singleCount: Item Quantity
        packageCount: Packages Quantity
        deleteMedicines: Mass Delete IP
        fileName: Upload File Name
    updateBatch:
        batch: Batch Number
        expirationDate: Expiration
        status: the status of IP
        name: IP Name
        updateCount: Update Count
        updateBatch: Update Batch Number
        updateExpirationDate: Update Expiration
        toBeWarehoused: To be Warehoused
        available: Available
        delivered: Confirmed
        transit: In Delivery
        quarantine: Quarantined
        used: Used
        lose: Lost/Wasted
        expired: Expired
        InOrder: To be Confirmed
        stockPending: To be warehoused
        apply: Applied
        frozen: Freeze
        approval: To be approved
        lock: Locked
        depot: Depots
        group: Group
        warn: Subject Alert
        capacity: Subject Limit
        batchNo: Batch Number
    workTask:
        label: Scan the code and enter the depot task
        add: send
    form:
        env_name: Environment
        name: Field Name
        editable: Editable or Not
        required: Required
        type: Control Type
        format: format type
        options: Option
        input: Input Box
        inputNumber: Numeric Input Box
        textArea: Multiline Text Box
        select: Drop-down Box
        checkbox: Checkbox
        radio: Radio Box
        switch: Switch
        datePicker: Date Selection Box
        timePicker: TimePickerDialog
        length: Length
        max: Max
        min: Minimum value
        variableFormat: Variable Format
        status: Status
        status_effective: valid
        status_invalid: Invalid
        applicationType: Application Type
        applicationTypeRegister: Subject Registration
        applicationTypeFormula: Custom formula
        applicationTypeDoseAdjustment: Dose Adjustment
        applicationTypeFactorCalc: Stratification Calculation
        variable: Variable ID
        currentTime: Current Time
    simulateRandom:
        env_name: Environment
        onlyID: Form ID
        name: Name
        randomList: Randomization List
        siteQuantity: Site Quantity
        countryQuantity: Country Quantity
        regionQuantity: Region Quantity
        RunQuantity: Number of Runs
        SubjectQuantity: Subject Quantity
        FactorRatio: Stratification Case Numbers
        run: Randomization Simulation:Run
    project_storehouse:
        env_name: Environment
        onlyID: Form ID
        name: Depots
        country: Country and Region
        contacts: Contacts
        phone: TEL
        email: Email
        address: Address
        connected: Sync or Not
        supplier: Logistics Vendor
        notIncluded: Shipment does not include quarantined IP
        research_product_name_validity_reminder: IP Name(Expiration Reminder)
        medicine_name: IP Name
        medicine_alert: Alert Value
        medicine_info: IP Name/Alert Value/Expiration Date Reminder
    supplierType:
        shengsheng: SHENGSHENG LOGISTICS
        catalent: catalent
        baicheng: Bioquick
        eDRUG: eDRUG
    projectSiteStatus:
        valid: Valid
        invalid: Invalid
    projectSiteActive:
        open: Open
        close: Close
    project_site:
        env_name: Environment
        number: Site Number
        name: Site Standard Name
        shortName: Site abbreviation
        country: Country/Region
        region: Region
        status: Status
        active: Automatic Re-supply
        supplyPlan: Supply Plan
        storehouse: Depot Name
        contacts: Contacts
        phone: Contact Information
        email: Email
        address: Address
        order_trail: "Initial Automatic Shipment: ON, Shipment Number"
    attribute:
        env_name: Environment
        random: Randomize
        random_true: Randomize
        random_false: non-randomized
        isRandomNumber: Display Randomization ID
        isRandomNumber_true: yes
        isRandomNumber_false: no
        isRandomSequenceNumber: Random Sequence Number Display
        isRandomSequenceNumber_true: yes
        isRandomSequenceNumber_false: no
        randomSequenceNumberPrefix: Random Sequence Number Prefix
        randomSequenceNumberDigit: Random Sequence Number Digits
        randomSequenceNumberStart: Random Sequence Number Start Number
        dispensing: Treatment Design
        dispensing_true: Dispense
        dispensing_false: Do not dispense
        dtpRule: DTP Rules
        dtpRule_ip: IP
        dtpRule_visitFlow: Visit Flow
        dtpRule_notApplicable: Not Applicable
        overdueVisitApproval: 超窗访视发放审批
        overdueVisitProcess: 超窗访视发放审批流程操作
        overdueVisitSms: 超窗访视发放审批短信
        overdueVisitMainVisit: 主访视
        overdueVisitReDispensation: 补发
        overdueVisitUnscheduled: 计划外发放/独立计划外
        randomControl: Randomization Control
        randomControlRule: Randomization Control Rule
        randomControl1: All groups, can randomize after with sufficient inventory
        randomControl2: Allocated groups, can randomize after with sufficient in
        randomControl3: Force random to available inventory group
        randomControl3_info: How much groups need to be supplied at least
        allowRegisterGroup: Actually used IP group
        blind: Blind Design
        blind_true: Blind
        blind_false: Open
        minimize_calc: Minimized Randomization Calculation
        minimize_calc_factor: Randomization Stratification
        minimize_calc_actual: Actual Stratification
        notApplicable: Not Applicable
        subject_number_rule: Subject number input rule
        subject_number_rule1: Customize
        subject_number_rule2: Auto-incrementing and unique within the project
        subject_number_rule3: Auto-incrementing and unique within the site
        screen: Subject Screening Process
        screen_true: Open
        screen_false: Close
        prefix: Subject Prefix
        prefix_number: Subject No. Prefix
        prefix_true: Have
        prefix_false: Not Found
        sitePrefix: Use Site Number as Prefix or Not
        prefixConnector: Prefix Connector
        otherPrefix: Other Prefixes of Subject ID
        otherPrefixText: Prefix Text
        subjectReplaceText: Replacement text for Subject ID
        subjectReplaceTextEn: Replacement text for Subject ID (English)
        accuracy: Exact value for Subject ID
        accuracy_le: Less than or Equal to
        accuracy_eq: Equal to
        digit: Subject No. Digit
        isFreeze: Quarantined-IP Counting Rule
        edcDrugConfigLabel: EDC Docking Treatment Design Label
        segmentType: Calculation Rules
        serialNumber: Sequence Number
        medicineNumber: IP Number
        segment: Segmented Dispensation
        segmentLength: Segmented Dispensation Length
        unblindingReason: Unblinding Reason
        freezeReason: Quarantine Reason
        unblindingAllowTrue: Allowed to remark
        unblindingAllowFalse: Not allowed to remark
        allowReplace: Subject Replacement
        allowReplaceOpen: Open
        allowReplaceUnOpen: Close
        replaceRule: Replacing randomization ID
        ReplaceRuleNumber: 替换受试者随机号规则
        blindingRestrictions: Stop Unblinded Subjects
        pvBlindingRestrictions: Include PV unblinded subjects
        IPInheritance: IP Inheritance
        RemainingVisit: Remaining Visit Cycles
    push:
        registered: Register
        update: Modify
        random: Randomize
        dispense: Dispense
        out_visit_dispensing: Unscheduled Dispensation
        replace: Replace IP
        reissue: Re-dispensation
        cancel: Withdraw IP
        retrieval: Retrieve IP
        realDispensing: Actually Dispensed IP
        subjectReplace: Subject Replacement
        unknown: Unknown
    projectUser:
        emailLanguage: Email Language
        email: Email
        roles: Role
        addRoles: Assign roles
        cancelRoles: Cancel Roles
        unblindingCode: Generate unblinding codes
        sites: Site
        addSites: Assign Site
        cancelSites: Distribute site
        depots: Depot
        addDepots: Assign Depots
        cancelDepots: Cancel Depots
        App: APP Account
        status: Status
        status_effective: Valid
        status_invalid: Invalid
    project_notice:
        envName: Environments
        notice_rule: Notification Rules
        notice_targets: Notification targets
    project:
        sponsor: Sponsor
        name: Project Name
        startTime: Project Cycle(Start Date)
        endTime: Project Cycle(end Date)
        plannedCases: Planned Enrollment Number
        phone: Contact Information
        descriptions: Remark
        timeZone: TimeZone
        status: Status
        progress: In Progress
        finish: Completed
        close: Closed
        pause: Paused
        terminate: Terminate
        orderCheck: Run Shipping Algorithm
        orderCheckDay: Custom Period
        checkTime: Check Time
        timing: Fixed Time(including manual run)
        realTime: Real Time
        notApplicable: Not Applicable
        orderConfirmation: Return Shipments Confirmation
        deIsolationApproval: Lifting Quarantine Approval
        administrators: Administrator
        connectEdc: Sync EDC
        pushMode: Data Push Mode
        real: Real-time request by EDC
        active: Active push from IRT
        pushRules: Push Rules
        subjectNumber: Subject ID
        subjectUID: Subject UID
        pushScenario: Push Scenarios
        registerPush: Register
        updateRandomFrontPush: Subject modify(Before Randomization)
        updateRandomAfterPush: Subject modify(After Randomization)
        randomPush: Randomize
        randomBlockPush: Stratification checks inconsistency, and randomization blocking is performed
        formRandomBlockPush: Form checks inconsistency, and randomization blocking is performed
        cohortRandomBlockPush: Cohort Name checks inconsistency, and randomization blocking is performed
        stageRandomBlockPush: Stage Name checks inconsistency, and randomization blocking is performed
        dispensingPush: Dispense
        screenPush: Screening
        synchronizationMode: Synchronization Mode
        edcUrl: URL
        edcSupplier: EDC Supplier
        edcMappingRules: EDC Mapping Rules
        folderOid: Folder OID
        formOid: Form OID
        fieldOid: Field OID
        ipNumberOid: IP Number OID
        dispenseTimeOid: Dispensation Time OID
        randomizationCode: Randomization Number
        randomizationTime: Randomization Time
        group: group
        factor: factor
        cohor: Cohort/Re-randomization
        stepBy: Data synchronization when subject screening
        timeFull: Data synchronization when subjects randomizing
        connectLearning: Sync eLearning
        needLearning: Must complete course
        needLearningEnv: Environment
        unblindingControl: Unblinding control
        unblindingSms: SMS
        unblindingProcess: Process operation
        unblindingCode: Unblinding code

        pvUnblinding: Unblinding(pv)
        pvUnblindingSms: SMS(pv)
        pvUnblindingProcess: Process operation(pv)

        ipUnblinding: Unblinding(IP)
        ipUnblindingSms: SMS(IP)
        ipUnblindingProcess: Process operation(IP)

        orderApprovalControl: Site Shipment Approval Control
        envName: Environment Name
        envCapacity: Capping
        alertThresholds: Status/Capping/Alert Threshold
        envReminderThresholds: Alert Threshold
        newEnvName: New Environment
        lockStatus: Status
        unlock: Unlock
        locked: Lock
        roleName: Role Name
        scope: Scope
        roleStatus: Status
        roleDescription: Description
        cancelRolePermissionSelect: Cancel Tick
        addRolePermissionSelect: Tick
        cohort: Cohort Name
        stage: Stage Name
        complete: Completed
        draft: Draft
        enrollment: Enrolling
        stop: Stopped
        enrollmentFull: Enrollment Full
        capacity: Capping
        lastStage: Previous Stage
        cohortStatus: Status
        properties: Project Properties
        type: Project Type
        region:
            name: Name
        week:
            monday: Monday
            tuesday: Tuesday
            wednesday: Wednesday
            thursday: Thursday
            friday: Friday
            saturday: Saturday
            sunday: Sunday
    project_multi_language:
        projectName: Project
        language: Language
        status: Enabled Status
        sharedSystemLibrary: Shared System Library
    project_multi_language_translate:
        projectName: Project
        languageLibrary: Language Library
        pagePath: Page Path
        envName: Environment
        cohortName: Cohort
        stageName: Stage
        type: Type
        key: Key
        name: Name
        label: label
    project_multi_language_batch_upload:
        projectName: Project
        filename: "[[batch_upload.filename]]"
    barcode_label:
        correlation_name: Associated Barcode Task
        send_success: Lable Management：Send
        code_method: Coding Method
        product_count: Print Quantity
        template_size: Print Paper Size
        label_size: Lable Size
        base:
            Component: Basic Components
            text: Text
            position: Position
            font: Font
            background: Background
            height: Height
            marginTop: Top Margin
            marginLeft: Left Margin
report:
    template:
        name_exist: Template Name Already Exists
        name_invalid: Template Name Invalid

source_ip_upload_history_name: Name
source_ip_upload_history_rows: Rows
source_ip_upload_history_rows_succeeded: Succeeded
source_ip_upload_history_rows_failed: Failed

app_scan_package_notification_title: Package scan Notification
app_scan_package_notification_content: Package scan
app_scan_notification_title: Scan to Enter Depot Notification
app_scan_notification_content: Scan to Enter Depot
app_shipment_confirmed_notification_title: Shipment to be Confirmed Notification
app_shipment_confirmed_notification_content: Shipment to be confirmed
app_shipment_received_notification_title: Shipment to be Received Notification
app_shipment_received_notification_content: Shipment to be Received
app_recovery_shipment_confirmed_notification_title: Return Shipment to be Confirmed Notification
app_recovery_shipment_confirmed_notification_content: Return Shipment to be confirmed
app_recovery_shipment_received_notification_title: Return Shipment to be Received Notification
app_recovery_shipment_received_notification_content: Return Shipment to be Received
app_recovery_shipment_delivered_notification_title: Return Shipment to be Delivered Notification
app_recovery_shipment_delivered_notification_content: Return Shipment to be Delivered
app_dispensation_confirmed_notification_title: IP Dispensation Confirmation Notification
app_dispensation_confirmed_notification_content: IP Dispensation Confirmation
app_re_dispensation_confirmed_notification_title: Re-dispensation Confirmation Notification
app_re_dispensation_confirmed_notification_content: Re-dispensation Confirmation
app_shipment_delivered_notification_title: Shipment to be Delivered Notification
app_shipment_delivered_notification_content: Shipment to be Delivered
app_shipment_delivered_application_title: Site Shipment Application Notification
app_shipment_delivered_application_content: Site Shipment Application
app_unblinding_urgent_notification_title: Unblinding (Urgent) Approval Notification
app_unblinding_urgent_notification_content: Unblinding (Urgent) Approval
app_unschedualed_dispensation_confirmation_notification_title: Unschedualed Dispensation Confirmation Notication
app_unschedualed_dispensation_confirmation_notification_content: Unschedualed Dispensation Confirmation
app_unblinding_pv_notification_title: Unblinding(PV) Approval Notification
app_unblinding_pv_notification_content: Unblinding(PV)
app_ip_dispensation_notification_title: IP Dispensation Notification
app_ip_dispensation_notification_content: IP Dispensation
app_site_alert_notification_title: Site Alert Notification
app_site_alert_notification_content: IP has reached alert value
app_depot_alert_notification_title: Depot Alert Notification
app_depot_alert_notification_content: IP has reached alert value
app_shipment_timeout_notification_title: Shipment Timeout Notification
app_shipment_timeout_notification_content_1: Shipment
app_shipment_timeout_notification_content_2: is timeout by
app_shipment_timeout_notification_content_day: day
app_shipment_timeout_notification_content_days: days
app_visit_reminder_title: Visit Reminder
app_visit_reminder_content_a: "{{.days}} days later you have a visit task, please remember to arrange it in advance!"
app_visit_reminder_content_b: "You have a visit task tomorrow, please remember to arrange it!"
app_visit_reminder_content_c: "You have a visit task today, please remember to go for a check-up!"
app_visit_reminder_content_d: "Did you forget to arrange the visit today? Come in and take a look!"
project_at_random_error: The re-randomization project only supports adding two stages.

medicine_package_err: Modification failed, package IP name is not match with setting, please reconfirm.
