package locales

import (
	"clinflash-irt/ini"
	"context"
	"embed"
	"github.com/duke-git/lancet/v2/slice"
	"io/fs"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"
	"gopkg.in/yaml.v3"
)

var (
	//go:embed *
	LocalesFS embed.FS

	bundle *i18n.Bundle
)

// LoadMessageFileFS is like LoadMessageFile but instead of reading from the
// hosts operating system's file system it reads from the fs file system.
func loadMessageFileFS(b *i18n.Bundle, fsys fs.FS, path string) (*i18n.MessageFile, error) {
	buf, err := fs.ReadFile(fsys, path)
	if err != nil {
		panic(err)
	}
	return b.ParseMessageFileBytes(buf, path)
}

func Init() {
	bundle = i18n.NewBundle(language.English)
	bundle.RegisterUnmarshalFunc("yml", yaml.Unmarshal)
	_, err := loadMessageFileFS(bundle, LocalesFS, "en.yml")
	if err != nil {
		panic(err)
	}
	_, err = loadMessageFileFS(bundle, LocalesFS, "zh.yml")
	if err != nil {
		panic(err)
	}
	_, err = loadMessageFileFS(bundle, LocalesFS, "ko.yml")
	if err != nil {
		panic(err)
	}
	bundle.RegisterUnmarshalFunc("ini", ini.Unmarshal)
	_, err = loadMessageFileFS(bundle, LocalesFS, "en.ini")
	if err != nil {
		panic(err)
	}
	_, err = loadMessageFileFS(bundle, LocalesFS, "transition.en.ini")
	if err != nil {
		panic(err)
	}
	_, err = loadMessageFileFS(bundle, LocalesFS, "zh.ini")
	if err != nil {
		panic(err)
	}
	_, err = loadMessageFileFS(bundle, LocalesFS, "transition.zh.ini")
	if err != nil {
		panic(err)
	}
	_, err = loadMessageFileFS(bundle, LocalesFS, "ko.ini")
	if err != nil {
		panic(err)
	}
}

func Lang(c context.Context) string {
	var lang = "en"
	switch ctx := c.(type) {
	case *gin.Context:
		lang = ctx.Request.Header.Get("Accept-Language")
	default:
		if v, ok := ctx.Value("lang").(string); ok {
			lang = v
		}
		// grpc metadata中key对应的value为[]string类型
		if v, ok := ctx.Value("lang").([]string); ok {
			if len(v) > 0 {
				lang = v[0]
			}
		}
	}
	return lang
}

func Tr(c context.Context, key string, data ...interface{}) string {
	return TrWithLang(Lang(c), key, data...)
}

func TrWithLang(lang string, key string, data ...interface{}) string {
	var templateData interface{}
	if len(data) > 0 {
		templateData = data[0]
	}
	localizeConfig := &i18n.LocalizeConfig{MessageID: key, TemplateData: templateData}
	msg := trWithConfig(lang, localizeConfig, bundle)
	// 自定义模板替换
	vars := ExtractPlaceholders([]string{msg})
	if len(vars) > 0 {
		varNames := make(map[string]string, len(vars))
		slice.ForEach(vars, func(index int, item string) {
			varNames[item] = trWithConfig(lang, &i18n.LocalizeConfig{MessageID: item}, bundle)
		})
		msg = ReplaceTemplate(msg, varNames)
	}
	return msg
}

func TrMessage(c context.Context, key string, content string, data ...interface{}) string {
	return trWithMessage(Lang(c), &i18n.Message{ID: key, Other: content}, data...)
}

func trWithMessage(lang string, message *i18n.Message, data ...interface{}) string {
	var templateData interface{}
	if len(data) > 0 {
		templateData = data[0]
	}
	localizeConfig := &i18n.LocalizeConfig{DefaultMessage: message, TemplateData: templateData}
	// 临时bundle
	tempBundle := i18n.NewBundle(language.English)
	tempBundle.AddMessages(language.English, message)
	return trWithConfig(lang, localizeConfig, tempBundle)
}

func trWithConfig(lang string, config *i18n.LocalizeConfig, bundle *i18n.Bundle) string {
	msg := ""
	defer func() string {
		if err := recover(); err != nil {
			return msg
		}
		return msg
	}()
	localizer := i18n.NewLocalizer(bundle, lang)
	msg = localizer.MustLocalize(config)
	return msg
}

type TrData struct {
	Key  string
	Data []interface{}
}

func TrStash(c context.Context, key string, data ...interface{}) TrData {
	return TrData{Key: key, Data: data}
}

// ExtractPlaceholders 匹配[[var]]格式，并提取变量名
func ExtractPlaceholders(tpls []string) []string {
	re := regexp.MustCompile(`\[\[([^\[\]]+)\]\]`)
	placeholders := make([]string, 0)
	slice.ForEach(tpls, func(index int, tpl string) {
		vars := slice.Map(re.FindAllString(tpl, -1), func(index int, item string) string {
			return strings.Trim(item, "[]")
		})
		placeholders = append(placeholders, vars...)
	})
	return slice.Unique(placeholders)
}

// ReplaceTemplate 替换模板变量
func ReplaceTemplate(tpl string, vars map[string]string) string {
	re := regexp.MustCompile(`\[\[([^\[\]]+)\]\]`)
	return re.ReplaceAllStringFunc(tpl, func(match string) string {
		// 提取变量名（去除[[]]）
		varName := strings.Trim(match, "[]")
		if val, exists := vars[varName]; exists {
			return val
		}
		return match // 未找到变量保持原样
	})
}
