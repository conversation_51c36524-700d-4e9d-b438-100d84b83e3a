package catalent

import (
	"bytes"
	"clinflash-irt/config"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/xml"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Log struct {
	ID          primitive.ObjectID `bson:"_id"`
	OrderId     primitive.ObjectID `bson:"order_id"`
	Type        int                `bson:"type"` //0 irt->catalent  1 catalent->irt
	FileName    string             `bson:"file_name"`
	XmlContent  string             `bson:"xml_content"`
	MailContent string             `bson:"mail_content"`
	FtpError    string             `bson:"ftp_error"`
	FtpStatus   int                `bson:"ftp_status"` //0 irt->catalent未发送 catalent->irt未处理  1 irt->catalent已发送 catalent->irt已处理
	MailError   string             `bson:"mail_error"`
	Time        string             `bson:"time"`
}

type Name struct {
	Protocol      string
	PerformCode   string
	RequestorCode string
	ShipmentID    string
	Type          string
	Time          time.Time
}

func GetCatalentXml(n Name) string {
	return fmt.Sprint(GetCatalentName(n), ".xml")
}
func GetCatalentName(n Name) string {
	format := n.Time.Format("2006-01-02_150405.000")
	replace := strings.Replace(format, ".", "", -1)
	return fmt.Sprint(n.Protocol, "_", n.PerformCode, "_", n.RequestorCode, "_", n.Type, n.ShipmentID, "_", replace)
}

func GetXmlns(t time.Time) string {
	format := t.Format("2006_01_02")
	return fmt.Sprint("http://www.catalent.com/irti/clinicalShipmentOrders_", format)
}

type ClinicalShipmentReceipt struct {
	XMLName                xml.Name               `xml:"ClinicalShipmentReceipt"`
	Xmlns                  string                 `xml:"xmlns,attr"`
	ClinicalShipmentHeader ClinicalShipmentHeader `xml:"ClinicalShipmentHeader"`
	ReceiptStatus          string
	ReceiptStatusDate      string
}

type ClinicalShipmentOrder struct {
	XMLName                xml.Name               `xml:"ClinicalShipmentOrder"`
	Xmlns                  string                 `xml:"xmlns,attr"`
	ClinicalShipmentHeader ClinicalShipmentHeader `xml:"ClinicalShipmentHeader"`
	ShipAddress            ShipAddress            `xml:"ShipAddress"`
	OrderItems             OrderItems             `xml:"OrderItems"`
}

type ClinicalShipmentDispatch struct {
	XMLName                xml.Name               `xml:"ClinicalShipmentDispatch"`
	Xmlns                  string                 `xml:"xmlns,attr"`
	ClinicalShipmentHeader ClinicalShipmentHeader `xml:"ClinicalShipmentHeader"`
	AirwayBill             AirwayBill             `xml:"AirwayBill"`
	DispatchStatus         string
	DispatchUpdateDate     string
	DeliveredDate          string
}

type ClinicalShipmentHeader struct {
	XMLName              xml.Name `xml:"ClinicalShipmentHeader"`
	Protocol             string
	PerformCode          string
	RequestorCode        string
	ShipmentID           string
	ShipmentDate         string
	ShipmentType         string
	ShipSite             string
	ShipmentInstructions string
	EstArrivalDate       string
	FromID               string
	FromCountry          string `xml:"FromCountry>Country"`
	DestinationID        string
	DestinationCountry   string `xml:"DestinationCountry>Country"`
}

type ShipAddress struct {
	XMLName     xml.Name `xml:"ShipAddress"`
	PiName      string
	AttentionOf string
	Department  string
	Address1    string
	Address2    string
	Address3    string
	City        string
	State       string
	Country     string `xml:"Country>Country"`
	PostalCode  string
	Telephone   string `xml:"Telephone>PhoneNumber"`
	Fax         string `xml:"Fax>PhoneNumber"`
	Email       string
}

type OrderItems struct {
	XMLName         xml.Name        `xml:"OrderItems"`
	PatientKitBatch PatientKitBatch `xml:"PatientKitBatch"`
}

type PatientKitBatch struct {
	XMLName             xml.Name `xml:"PatientKitBatch"`
	Quantity            string   `xml:"Quantity,attr"`
	MaterialType        string
	MaterialDescription string
	ExpiryDate          string
	LotNo               string
	BulkLotNo           string
	PatientKits         []PatientKit `xml:"PatientKit"`
}

type PatientKit struct {
	XMLName     xml.Name `xml:"PatientKit"`
	MedID       string
	MaterialSeq string
}

type AncillaryItem struct {
	XMLName             xml.Name `xml:"AncillaryItem"`
	Quantity            string   `xml:"Quantity,attr"`
	MaterialType        string
	MaterialDescription string
	ExpiryDate          string
	LotNo               string
	BulkLotNo           string
}

type AirwayBill struct {
	XMLName          xml.Name `xml:"AirwayBill"`
	QtyShippers      int      `xml:"QtyShippers,attr"`
	AirwayBillNo     string
	Courier          string
	AirwayBillStatus string
	INCOTERM         string
	Signatory        string
	DeliveredDate    string
	Shipper          Shipper `xml:"Shipper"`
}

type Shipper struct {
	XMLName                  xml.Name    `xml:"Shipper"`
	TemperatureControlDevice string      `xml:"TemperatureControlDevice"`
	ShippedItem              ShippedItem `xml:"ShippedItem"`
}

type ShippedItem struct {
	XMLName         xml.Name        `xml:"ShippedItem"`
	PatientKitBatch PatientKitBatch `xml:"PatientKitBatch"`
}

type DockingConfig struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	Supplier       string             `json:"supplier" bson:"supplier"`
	DockingMapping []DockingMapping   `json:"dockingMapping" bson:"docking_mapping"`
}
type DockingMapping struct {
	SiteName             string `json:"siteName" bson:"site_name"`
	SiteNameForInterface string `json:"SiteNameForInterface" bson:"site_name_for_interface"`
	Email                string `json:"email" bson:"email"`
}

func GetAllKeyValue(obj interface{}, buffer *bytes.Buffer) {
	refType := reflect.TypeOf(obj)
	if refType.Kind() == reflect.Struct {
		refValue := reflect.ValueOf(obj)
		buffer.WriteString(refType.Name())
		buffer.WriteString("\r\n")
		for i := 0; i < refType.NumField(); i++ {
			field := refType.Field(i)
			if field.Name == "XMLName" {
				continue
			}
			value := refValue.FieldByName(field.Name)
			if value.Kind() == reflect.Slice {
				for j := 0; j < value.Len(); j++ {
					v := value.Index(j)
					if v.Kind() == reflect.Struct {
						GetAllKeyValue(v.Interface(), buffer)
					} else {
						buffer.WriteString("    ")
						buffer.WriteString(field.Name)
						buffer.WriteString(":")
						buffer.WriteString(value.String())
						buffer.WriteString("\r\n")
					}
				}
				continue
			}
			if value.Kind() == reflect.Struct {
				GetAllKeyValue(value.Interface(), buffer)
			} else {
				buffer.WriteString("    ")
				buffer.WriteString(field.Name)
				buffer.WriteString(":")
				buffer.WriteString(value.String())
				buffer.WriteString("\r\n")
			}
		}
	}
}

func SendToCatalent(orderId string) error {
	var order models.MedicineOrder
	orderObjId, _ := primitive.ObjectIDFromHex(orderId)
	_ = tools.Database.Collection("medicine_order").FindOne(nil, bson.M{"_id": orderObjId}).Decode(&order)
	var sendProjectStorehouse models.ProjectStorehouse
	_ = tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": order.SendID}).Decode(&sendProjectStorehouse)
	//只需要同步仓库->中心、仓库->仓库，仓库->第三方仓库
	if order.Status == 1 && !sendProjectStorehouse.ID.IsZero() && sendProjectStorehouse.Connected && sendProjectStorehouse.Supplier == "catalent" {
		var sendStorehouse models.Storehouse
		var shipSite string
		var destination string
		var fromCountry string
		var destinationCountry string
		var sendEmail string
		_ = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": sendProjectStorehouse.StorehouseID}).Decode(&sendStorehouse)
		shipSite, sendEmail = getSiteNameAndEmailForInterface("catalent", sendStorehouse.Name)
		fromCountry = sendProjectStorehouse.Country[0]

		var receiveProjectStorehouse models.ProjectStorehouse
		var receiveStorehouse models.Storehouse
		var receiveProjectSite models.ProjectSite
		var department = ""
		var address1 = ""
		var address2 = ""
		var address3 = ""
		var state = ""
		var city = ""
		var piName = ""
		var telephone = ""
		var email = ""
		var address = ""
		_ = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": order.ReceiveID}).Decode(&receiveProjectSite)
		if !receiveProjectSite.ID.IsZero() {
			department = receiveProjectSite.Name

			if receiveProjectSite.ShortName != "" {
				department = receiveProjectSite.ShortName
			}
			if len(receiveProjectSite.ContactGroup) > 0 {
				groupP, ok := slice.Find(receiveProjectSite.ContactGroup, func(index int, item models.ContactInfo) bool {
					return item.IsDefault == 1
				})
				if ok {
					group := *groupP
					piName = group.Contacts
					address = group.Address
					telephone = group.Phone
					email = group.Email
				} else {
					piName = receiveProjectSite.ContactGroup[0].Contacts
					address = receiveProjectSite.ContactGroup[0].Address
					telephone = receiveProjectSite.ContactGroup[0].Phone
					email = receiveProjectSite.ContactGroup[0].Email
				}
			}
			//piName = receiveProjectSite.Contacts
			//address = receiveProjectSite.Address
			//telephone = receiveProjectSite.Phone
			//email = receiveProjectSite.Email
			destination = receiveProjectSite.Name
			destinationCountry = receiveProjectSite.Country[0]
			if len(receiveProjectSite.Country) == 2 {
				pipeline := mongo.Pipeline{
					{{Key: "$unwind", Value: "$state"}},
					{{Key: "$unwind", Value: "$state.city"}},
					{{Key: "$match", Value: bson.M{"code": receiveProjectSite.Country[0], "state.code": receiveProjectSite.Country[1]}}},
				}
				var data []map[string]interface{}
				cursor, _ := tools.Database.Collection("country").Aggregate(nil, pipeline, nil)
				_ = cursor.All(nil, &data)
				city = data[0]["state"].(map[string]interface{})["en"].(string)
				if len(data) == 0 {
					pipeline = mongo.Pipeline{
						{{Key: "$unwind", Value: "$state"}},
						{{Key: "$unwind", Value: "$state.city"}},
						{{Key: "$match", Value: bson.M{"code": receiveProjectSite.Country[0], "state.city.code": receiveProjectSite.Country[1]}}},
					}
					cursor, _ = tools.Database.Collection("country").Aggregate(nil, pipeline, nil)
					_ = cursor.All(nil, &data)
					city = data[0]["state"].(map[string]interface{})["city"].(map[string]interface{})["en"].(string)
				}
			} else if len(receiveProjectSite.Country) == 3 {
				pipeline := mongo.Pipeline{
					{{Key: "$unwind", Value: "$state"}},
					{{Key: "$unwind", Value: "$state.city"}},
					{{Key: "$match", Value: bson.M{"code": receiveProjectSite.Country[0], "state.code": receiveProjectSite.Country[1], "state.city.code": receiveProjectSite.Country[2]}}},
				}
				var data []map[string]interface{}
				cursor, _ := tools.Database.Collection("country").Aggregate(nil, pipeline, nil)
				_ = cursor.All(nil, &data)
				state = data[0]["state"].(map[string]interface{})["en"].(string)
				city = data[0]["state"].(map[string]interface{})["city"].(map[string]interface{})["en"].(string)
			}
		} else {
			_ = tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": order.ReceiveID}).Decode(&receiveProjectStorehouse)
			_ = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": receiveProjectStorehouse.StorehouseID}).Decode(&receiveStorehouse)
			piName = receiveProjectStorehouse.Contacts
			department = receiveStorehouse.Name
			address = receiveProjectStorehouse.Address
			destination = receiveStorehouse.Name
			destinationCountry = receiveProjectStorehouse.Country[0]
			if len(receiveProjectStorehouse.Country) == 2 {
				pipeline := mongo.Pipeline{
					{{Key: "$unwind", Value: "$state"}},
					{{Key: "$unwind", Value: "$state.city"}},
					{{Key: "$match", Value: bson.M{"code": receiveProjectStorehouse.Country[0], "state.code": receiveProjectStorehouse.Country[1]}}},
				}
				var data []map[string]interface{}
				cursor, _ := tools.Database.Collection("country").Aggregate(nil, pipeline, nil)
				_ = cursor.All(nil, &data)
				city = data[0]["state"].(map[string]interface{})["en"].(string)
				if len(data) == 0 {
					pipeline = mongo.Pipeline{
						{{Key: "$unwind", Value: "$state"}},
						{{Key: "$unwind", Value: "$state.city"}},
						{{Key: "$match", Value: bson.M{"code": receiveProjectStorehouse.Country[0], "state.city.code": receiveProjectStorehouse.Country[1]}}},
					}
					cursor, _ = tools.Database.Collection("country").Aggregate(nil, pipeline, nil)
					_ = cursor.All(nil, &data)
					city = data[0]["state"].(map[string]interface{})["city"].(map[string]interface{})["en"].(string)
				}
			} else if len(receiveProjectStorehouse.Country) == 3 {
				pipeline := mongo.Pipeline{
					{{Key: "$unwind", Value: "$state"}},
					{{Key: "$unwind", Value: "$state.city"}},
					{{Key: "$match", Value: bson.M{"code": receiveProjectStorehouse.Country[0], "state.code": receiveProjectStorehouse.Country[1], "state.city.code": receiveProjectStorehouse.Country[2]}}},
				}
				var data []map[string]interface{}
				cursor, _ := tools.Database.Collection("country").Aggregate(nil, pipeline, nil)
				_ = cursor.All(nil, &data)
				state = data[0]["state"].(map[string]interface{})["en"].(string)
				city = data[0]["state"].(map[string]interface{})["city"].(map[string]interface{})["en"].(string)
			}
			telephone = receiveProjectStorehouse.Phone
			email = receiveProjectStorehouse.Email
		}

		if len(address) > 0 && len(address) <= 30 {
			address1 = address[0:]
		} else if len(address) > 30 && len(address) <= 60 {
			address1 = address[0:30]
			address2 = address[30:]
		} else if len(address) > 60 && len(address) <= 90 {
			address1 = address[0:30]
			address2 = address[30:60]
			address3 = address[60:]
		} else if len(address) > 90 {
			address1 = address[0:30]
			address2 = address[30:60]
			address3 = address[60:90]
		}

		xmlns := GetXmlns(time.Now())
		medicines := make([]models.Medicine, len(order.Medicines))
		cursor, _ := tools.Database.Collection("medicine").Find(nil, bson.M{"_id": bson.M{"$in": order.Medicines}})
		_ = cursor.All(nil, &medicines)
		patientKits := make([]PatientKit, len(order.Medicines))
		for i, medicine := range medicines {
			patientKits[i] = PatientKit{
				MedID:       medicine.Number,
				MaterialSeq: "1",
			}
		}
		expDates := make([]string, len(order.Medicines))
		for i, medicine := range medicines {
			expDates[i] = medicine.ExpirationDate
		}
		expTime := expDates[0]
		var project models.Project
		_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": order.ProjectID}).Decode(&project)
		shipmentOrder := ClinicalShipmentOrder{
			Xmlns: xmlns,
			ClinicalShipmentHeader: ClinicalShipmentHeader{
				Protocol:             project.Number,
				PerformCode:          "000000",
				RequestorCode:        "0",
				ShipmentID:           order.OrderNumber,
				ShipmentDate:         time.Now().Format("2006-01-02"),
				ShipmentType:         "Initial", //"Initial","Reshipment"
				ShipSite:             shipSite,
				ShipmentInstructions: "",
				EstArrivalDate:       time.Now().AddDate(0, 0, 3).Format("2006-01-02"),
				FromID:               "ClinFlash",
				FromCountry:          fromCountry,
				DestinationID:        destination,
				DestinationCountry:   destinationCountry,
			},
			ShipAddress: ShipAddress{
				PiName:      piName,
				AttentionOf: "",
				Department:  department,
				Address1:    address1,
				Address2:    address2,
				Address3:    address3,
				City:        city,
				State:       state,
				Country:     destinationCountry,
				PostalCode:  "",
				Telephone:   telephone,
				Fax:         "",
				Email:       email,
			},
			OrderItems: OrderItems{
				PatientKitBatch: PatientKitBatch{
					Quantity:            strconv.Itoa(len(order.Medicines)),
					MaterialType:        "",
					MaterialDescription: "N/A",
					ExpiryDate:          expTime,
					LotNo:               "",
					BulkLotNo:           "",
					PatientKits:         patientKits,
				},
			},
		}
		headerBytes := []byte(xml.Header)
		xmlOrder := shipmentOrder
		destination = xmlOrder.ClinicalShipmentHeader.DestinationID
		shortID := ""
		if len(destination) < 25 {
			shortID = destination[:]
		} else {
			shortID = destination[:25]
		}
		xmlOrder.ClinicalShipmentHeader.DestinationID = shortID
		xmlByte, _ := xml.Marshal(xmlOrder)
		headerBytes = append(headerBytes, xmlByte...)
		catalentName := Name{
			Protocol:      project.Number,
			PerformCode:   "000000",
			RequestorCode: "0",
			ShipmentID:    order.OrderNumber,
			Type:          "SRQ",
			Time:          time.Now(),
		}
		fileName := GetCatalentXml(catalentName)
		mailError := ""
		var buffer bytes.Buffer
		if sendEmail != "" {
			GetAllKeyValue(shipmentOrder, &buffer)
			receivers := []tools.Recevier{{Address: sendEmail}}
			err := tools.SendText(GetCatalentName(catalentName), buffer.String(), receivers)
			if err != nil {
				mailError = err.Error()
			}
		}
		catalentLog := Log{
			ID:          primitive.NewObjectID(),
			Type:        0,
			OrderId:     orderObjId,
			FileName:    fileName,
			XmlContent:  string(headerBytes),
			MailContent: buffer.String(),
			FtpError:    "",
			FtpStatus:   0,
			MailError:   mailError,
			Time:        time.Now().UTC().Add(time.Hour * time.Duration(8)).Format("2006-01-02 15:04:05"),
		}
		_, err := tools.Database.Collection("catalent_log").InsertOne(nil, catalentLog)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}
func SendToSFTP() error {
	defer tools.DeferReturn("SendToSFTP")
	logs := make([]Log, 0)
	cursor, err := tools.Database.Collection("catalent_log").Find(nil, bson.M{"type": 0, "ftp_status": 0})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &logs)
	if err != nil {
		return errors.WithStack(err)
	}
	if len(logs) > 0 {
		port, _ := strconv.Atoi(config.CatalentPort)
		ftpError := ""
		sftpClient, err := tools.Connect(config.CatalentUser, config.CatalentPwd, config.CatalentURL, port)
		if err != nil {
			ftpError = err.Error()
		}
		defer sftpClient.Close()
		for _, log := range logs {
			// 用来测试的远程文件路径 和 本地文件夹
			var remoteFilePath = "/in/" + log.FileName

			dstFile, err := sftpClient.Create(remoteFilePath)
			if err != nil {
				ftpError = err.Error()
			}
			_, err = dstFile.Write([]byte(log.XmlContent))
			if err != nil {
				ftpError = err.Error()
			}

			ftpStatus := 0
			if ftpError == "" {
				ftpStatus = 1
			}
			now := time.Now().UTC().Add(time.Hour * time.Duration(8)).Format("2006-01-02 15:04:05")
			tools.Database.Collection("catalent_log").UpdateOne(nil, bson.M{"_id": log.ID}, bson.M{"$set": bson.M{"ftp_error": ftpError, "ftp_status": ftpStatus, "time": now}})
			dstFile.Close()
		}

	}
	return nil
}

func getSiteNameAndEmailForInterface(supplier string, siteName string) (interfaceName string, email string) {
	var config DockingConfig
	tools.Database.Collection("docking_config").FindOne(nil, bson.M{"supplier": supplier}).Decode(&config)
	if config.ID.IsZero() {
		return siteName, ""
	}
	interfaceName = ""
	email = ""
	for _, mapping := range config.DockingMapping {
		if mapping.SiteName == siteName {
			interfaceName = mapping.SiteNameForInterface
			email = mapping.Email
			break
		}
	}
	if interfaceName == "" {
		return siteName, email
	}
	return interfaceName, email
}

// ReadFtpFile 连接到ftp查看文件
func ReadFtpFile() error {
	defer tools.DeferReturn("ReadFtpFile")
	port, _ := strconv.Atoi(config.CatalentPort)
	sftpClient, err := tools.Connect(config.CatalentUser, config.CatalentPwd, config.CatalentURL, port)
	if err != nil {
		return errors.WithStack(err)
	}
	defer sftpClient.Close()

	// 用来测试的远程文件路径 和 本地文件夹
	var remoteFilePath = "/out"
	root, err := sftpClient.ReadDir(remoteFilePath)
	if err != nil {
		return errors.WithStack(err)
	}
	for _, info := range root {
		file, openErr := sftpClient.Open(remoteFilePath + "/" + info.Name())
		if openErr != nil {
			continue
		}
		stats, _ := file.Stat()

		// []byte
		data := make([]byte, stats.Size())
		_, err = file.Read(data)
		if err != nil {
			return errors.WithStack(err)
		}
		id := primitive.NewObjectID()
		calalentLog := Log{
			ID:         id,
			Type:       1,
			FileName:   file.Name(),
			XmlContent: string(data),
			FtpError:   "",
			FtpStatus:  0,
			Time:       time.Now().UTC().Add(time.Hour * time.Duration(8)).Format("2006-01-02 15:04:05"),
		}
		_, err = tools.Database.Collection("catalent_log").InsertOne(nil, calalentLog)
		if err != nil {
			return errors.WithStack(err)
		}
		if strings.Contains(file.Name(), "DIS") { //|| strings.Contains(file.Name(), "DEL")
			var dispatch ClinicalShipmentDispatch
			xml.Unmarshal(data, &dispatch)
			protocol := dispatch.ClinicalShipmentHeader.Protocol
			shipmentID := dispatch.ClinicalShipmentHeader.ShipmentID
			var project models.Project
			err = tools.Database.Collection("project").FindOne(nil, bson.M{"info.number": protocol}).Decode(&project)
			if err != nil {
				return errors.WithStack(err)
			}
			var order models.MedicineOrder
			err = tools.Database.Collection("medicine_order").FindOne(nil, bson.M{"project_id": project.ID, "order_number": shipmentID}).Decode(&order)
			if err != nil {
				return errors.WithStack(err)
			}
			var medicines []models.Medicine
			cursor, err := tools.Database.Collection("medicine").Find(nil, bson.M{"_id": bson.M{"$in": order.Medicines}})
			if err != nil {
				return errors.WithStack(err)
			}
			err = cursor.All(nil, &medicines)
			if err != nil {
				return errors.WithStack(err)
			}
			if dispatch.DispatchStatus == "InTransit" && order.Status == 1 {
				setUpdate := bson.M{
					"status":     2,
					"sort_index": 5,
					"meta": models.Meta{
						CreatedAt: order.CreatedAt,
						CreatedBy: order.CreatedBy,
						UpdatedAt: time.Duration(time.Now().Unix()),
					},
				}
				update := bson.M{
					"$set": setUpdate,
				}
				_, err := tools.Database.Collection("medicine_order").UpdateOne(nil, bson.M{"_id": order.ID}, update)
				if err != nil {
					return errors.WithStack(err)
				}
				//已运送 更新研究产品状态
				kits := dispatch.AirwayBill.Shipper.ShippedItem.PatientKitBatch.PatientKits
				kitMedicineIds := make([]primitive.ObjectID, len(kits))
				for i, kit := range kits {
					for _, medicine := range medicines {
						if medicine.Number == kit.MedID {
							kitMedicineIds[i] = medicine.ID
							break
						}
					}
				}
				medicineFilter := bson.M{"_id": bson.M{"$in": kitMedicineIds}}
				medicineUpdate := bson.M{
					"$set": bson.M{
						"status": 3,
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(nil, medicineFilter, medicineUpdate); err != nil {
					return errors.WithStack(err)
				}
				//未发货的编码研究产品 更新研究产品状态为已隔离，并生成隔离记录
				frozenMedicineIds := make([]primitive.ObjectID, len(order.Medicines)-len(kitMedicineIds))
				for _, medicineID := range order.Medicines {
					in := false
					for _, kID := range kitMedicineIds {
						if kID == medicineID {
							in = true
							break
						}
					}
					if !in {
						frozenMedicineIds = append(frozenMedicineIds, medicineID)
					}
				}

				frozenMedicineFilter := bson.M{"_id": bson.M{"$in": frozenMedicineIds}}
				frozenMedicineUpdate := bson.M{
					"$set": bson.M{
						"status": 4,
					},
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(nil, frozenMedicineFilter, frozenMedicineUpdate); err != nil {
					return errors.WithStack(err)
				}
			}
		}
		err = sftpClient.Remove(remoteFilePath + "/" + info.Name())
		if err != nil {
			return errors.WithStack(err)

		}
		now := time.Now().UTC().Add(time.Hour * time.Duration(8)).Format("2006-01-02 15:04:05")
		_, err = tools.Database.Collection("catalent_log").UpdateOne(nil, bson.M{"_id": id}, bson.M{"$set": bson.M{"ftp_status": 1, "time": now}})
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}
