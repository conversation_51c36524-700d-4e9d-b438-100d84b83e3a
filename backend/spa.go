package main

import (
	"embed"
	"io/fs"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

//go:embed build
var build embed.FS

//go:embed images
var images embed.FS

// reference --> https://github.com/gin-gonic/gin/issues/2654
func spa(app *gin.Engine, embedded bool) {
	imagesFS := http.FS(images)
	app.GET("/api/img/mail", func(c *gin.Context) {
		c.FileFromFS("images/mail_logo.png", imagesFS)
	})

	if embedded {
		buildFS := http.FS(build)
		staticFS, _ := fs.Sub(build, "build/static")
		app.StaticFS("build/logo32.svg", buildFS)
		app.StaticFS("/static", http.FS(staticFS))
		app.GET("/", func(c *gin.Context) {
			c.FileFromFS("build/index.htm", buildFS)
		})
		app.Use(func(c *gin.Context) {
			c.Next()
			if c.Writer.Status() == 404 {
				c.File<PERSON>rom<PERSON>("build/index.htm", buildFS)
			}
		})
	} else {
		app.StaticFile("/logo32.svg", "./build/logo32.svg")
		app.Static("/static", "./build/static")
		app.GET("/", func(c *gin.Context) {
			c.File("./build/index.html")
		})
		app.Use(func(c *gin.Context) {
			c.Next()
			if c.Writer.Status() == 404 {
				if !strings.HasPrefix(c.Request.URL.Path, "/api") && !strings.HasPrefix(c.Request.URL.Path, "/open-api") {
					c.File("./build/index.html")
				}
			}
		})
	}
}
