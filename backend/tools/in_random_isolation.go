package tools

import (
	"clinflash-irt/models"
	"github.com/duke-git/lancet/v2/slice"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// InRandomIsolation 在随机项目隔离
func InRandomIsolation(projectNum string) bool {
	// "BG001" UAT平平的项目 "ZSJ003" 开发乔春峰的项目 其它线上的项目
	projectNumber := [...]string{"ZSJ 项目编号01", "BG001", "BG2109-AB-301", "JSLHC2023-10-01", "ZGJAK025&ZGJAK026"}
	for _, num := range projectNumber {
		if num == projectNum {
			return true
		}
	}
	return false
}

// IsolationProject 访视计算 特殊项目处理
func IsolationProject(project models.Project, envOID primitive.ObjectID) (bool, primitive.ObjectID) {

	if project.ID.IsZero() {
		Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	}
	isolationProject := project.Number == "JSLHC2023-10-01"

	//isolationProject := project.Number == "FSJS"
	if isolationProject {
		envP, ok := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return envOID == item.ID
		})
		if ok {
			env := *envP
			cohortP, ok := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				//return item.Name == "第一阶段"
				return item.Name == "核心治疗期"
			})
			if ok {
				cohort := *cohortP
				return true, cohort.ID
			}
		}
	}
	return false, primitive.ObjectID{}
}
