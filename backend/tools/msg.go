package tools

import (
	"clinflash-irt/config"
	"fmt"
	"github.com/pkg/errors"

	openapi "github.com/alibabacloud-go/darabonba-openapi/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v2/client"
	"github.com/alibabacloud-go/tea/tea"
)

func createClient() (_result *dysmsapi20170525.Client, _err error) {
	config := &openapi.Config{
		// 您的AccessKey ID
		AccessKeyId: tea.String("LTAI4GC16fv7F6CCGwsD17at"),
		// 您的AccessKey Secret
		AccessKeySecret:  tea.String("******************************"),
		Endpoint:         tea.String("dysmsapi.aliyuncs.com"),
		SignatureVersion: tea.String("2017-05-25"),
		RegionId:         tea.String("cn-hangzhou"),
	}
	// 访问的域名
	_result, _err = dysmsapi20170525.NewClient(config)
	return _result, _err
}

func SendVerificationCode(code string, phone string) (_err error) {
	client, _err := createClient()
	if _err != nil {
		return _err
	}
	sendSmsRequest := &dysmsapi20170525.SendSmsRequest{}
	sendSmsRequest.SetTemplateCode("SMS_199580163")
	sendSmsRequest.SetSignName("Clinflash")
	sendSmsRequest.SetTemplateParam("{\"code\":\"" + code + "\"}")
	sendSmsRequest.SetPhoneNumbers(phone)
	// 复制代码运行请自行打印 API 的返回值
	_, err := client.SendSms(sendSmsRequest)
	if err != nil {
		return err
	}
	return _err
}

func SendUrgentUnblindingApproval(templateParam string, phones []string) (_err error) {
	client, _err := createClient()
	if _err != nil {
		return _err
	}
	for _, phone := range phones {
		sendSmsRequest := &dysmsapi20170525.SendSmsRequest{}
		sendSmsRequest.SetTemplateCode(config.IRT_UNBLINDING_SMS)
		sendSmsRequest.SetSignName("ClinflashSMS")
		sendSmsRequest.SetTemplateParam(templateParam)
		sendSmsRequest.SetPhoneNumbers(phone)
		// 复制代码运行请自行打印 API 的返回值
		resp, err := client.SendSms(sendSmsRequest)
		fmt.Println(resp)
		if err != nil {
			SaveErrorLog("SendUrgentUnblindingApproval", err)
		}

	}
	return _err
}
func SendMedicineUnblindingApproval(templateParam string, phones []string) (_err error) {
	client, _err := createClient()
	if _err != nil {
		return _err
	}
	for _, phone := range phones {
		sendSmsRequest := &dysmsapi20170525.SendSmsRequest{}
		sendSmsRequest.SetTemplateCode(config.IRT_MEDICINE_UNBLINDING_SMS)
		sendSmsRequest.SetSignName("ClinflashSMS")
		sendSmsRequest.SetTemplateParam(templateParam)
		sendSmsRequest.SetPhoneNumbers(phone)
		// 复制代码运行请自行打印 API 的返回值
		resp, err := client.SendSms(sendSmsRequest)
		fmt.Println(resp)
		if err != nil {
			SaveErrorLog("SendUrgentUnblindingApproval", err)
		}
	}
	return _err
}

func SendDispensingApproval(templateParam string, phones []string) (_err error) {
	client, _err := createClient()
	if _err != nil {
		return _err
	}
	for _, phone := range phones {
		sendSmsRequest := &dysmsapi20170525.SendSmsRequest{}
		sendSmsRequest.SetTemplateCode(config.IRT_DISPENSING_APPROVAL_SMS)
		sendSmsRequest.SetSignName("ClinflashSMS")
		sendSmsRequest.SetTemplateParam(templateParam)
		sendSmsRequest.SetPhoneNumbers(phone)
		// 复制代码运行请自行打印 API 的返回值
		resp, err := client.SendSms(sendSmsRequest)
		fmt.Println(resp)
		if err != nil {
			SaveErrorLog("SendDispensingApproval", err)
		}
	}
	return _err
}

func SendDispensingApprovalResult(templateParam string, phones []string) (_err error) {
	client, _err := createClient()
	if _err != nil {
		return _err
	}
	for _, phone := range phones {
		sendSmsRequest := &dysmsapi20170525.SendSmsRequest{}
		sendSmsRequest.SetTemplateCode(config.IRT_DISPENSING_APPROVAL_RESULT_SMS)
		sendSmsRequest.SetSignName("ClinflashSMS")
		sendSmsRequest.SetTemplateParam(templateParam)
		sendSmsRequest.SetPhoneNumbers(phone)
		// 复制代码运行请自行打印 API 的返回值
		resp, err := client.SendSms(sendSmsRequest)
		fmt.Println(resp)
		if err != nil {
			SaveErrorLog("SendDispensingApprovalResult", err)
		}
	}
	return _err
}

func SendVisitNotice(templateParam string, phones []string, configSMS string) (_err error) {
	client, _err := createClient()
	if _err != nil {
		return _err
	}
	for _, phone := range phones {
		sendSmsRequest := &dysmsapi20170525.SendSmsRequest{}
		sendSmsRequest.SetTemplateCode(configSMS)
		sendSmsRequest.SetSignName("ClinflashSMS")
		sendSmsRequest.SetTemplateParam(templateParam)
		sendSmsRequest.SetPhoneNumbers(phone)
		// 复制代码运行请自行打印 API 的返回值
		respP, err := client.SendSms(sendSmsRequest)
		fmt.Println(respP)
		if err != nil {
			SaveErrorLog("SendVisitNotice", err)
		}
		resp := *respP
		if resp.Body.Code != nil && *resp.Body.Code != "OK" {
			_err = errors.New(*resp.Body.Message)
		}

	}
	return _err
}
