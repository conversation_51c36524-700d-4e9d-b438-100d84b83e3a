package tools

import (
	"clinflash-irt/data"
	"clinflash-irt/models"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type RoleTypeEmail struct {
	Email   string
	IsBlind bool //是否仅有盲态角色
}

// GetRoleUsersMail ..
func GetRoleUsersMail(projectOID primitive.ObjectID, envOID primitive.ObjectID, key string, siteOrStoreID ...primitive.ObjectID) ([]string, error) {
	var emails []string
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"project_id": projectOID, "key": key, "env_id": envOID}}},
		// 获取 项目环境下的 所有用户对应的角色
		{{Key: "$lookup", Value: bson.M{
			"from": "user_project_environment",
			"let": bson.M{
				"roles":  "$roles",
				"env_id": "$env_id",
			},
			"pipeline": mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}}}},
				{{Key: "$match", Value: bson.M{"$expr": bson.M{"$ne": bson.A{"$unbind", true}}}}},
				{{Key: "$unwind", Value: "$roles"}},
				{{Key: "$match", Value: bson.M{"$expr": bson.M{"$in": bson.A{"$roles", "$$roles"}}}}},
			},
			"as": "user",
		}}},
		{{Key: "$unwind", Value: "$user"}},
		// 关联 用户信息获取邮箱
		{{Key: "$lookup", Value: bson.M{
			"from":         "user",
			"localField":   "user.user_id",
			"foreignField": "_id",
			"as":           "userinfo",
		}}},
		{{Key: "$match", Value: bson.M{"userinfo.info.status": bson.M{"$in": []int8{0, 1}}, "userinfo.deleted": bson.M{"$ne": true}}}},
	}

	d := []string{
		"notice.subject.add",             // 受试者登记
		"notice.medicine.order",          // 研究产品订单
		"notice.medicine.alarm",          // 研究产品警戒
		"notice.subject.dispensing",      //发药
		"notice.subject.random",          //随机
		"notice.subject.signOut",         //停用
		"notice.subject.replace",         //替換
		"notice.subject.update",          //修改
		"notice.medicine.isolation",      //研究产品隔离
		"notice.order.timeout",           //订单超时提醒
		"notice.subject.alarm",           //受试者警戒邮件
		"notice.subject.unblinding",      //紧急揭盲邮件
		"notice.storehouse.alarm",        //仓库研究产品警戒邮件
		"notice.subject.alert.threshold", //受试者上限设置提醒
	}

	hasKey := false
	for _, element := range d {
		if key == element {
			hasKey = true
		}
	}
	// 是否区分过滤中心
	if hasKey {
		pipeline = append(pipeline,
			// 当前环境用户ID关联 用户仓库
			bson.D{{Key: "$lookup", Value: bson.M{
				"from": "user_depot",
				"let": bson.M{
					"env_id":  "$env_id",
					"user_id": "$user.user_id",
				},
				"pipeline": mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}}}},
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$user_id", "$$user_id"}}}}},
				},
				"as": "depot",
			}}},
			// 当前环境用户ID关联 用户中心
			bson.D{{Key: "$lookup", Value: bson.M{
				"from": "user_site",
				"let": bson.M{
					"env_id":  "$env_id",
					"user_id": "$user.user_id",
				},
				"pipeline": mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}}}},
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$user_id", "$$user_id"}}}}},
				},
				"as": "site",
			}}},
			// 当前环境用户角色关联 角色表获取  角色类型 (study,site,deport)
			bson.D{{Key: "$lookup", Value: bson.M{
				"from":         "project_role_permission",
				"localField":   "user.roles",
				"foreignField": "_id",
				"as":           "role",
			}}},
			bson.D{{Key: "$match", Value: bson.M{
				"$or": bson.A{
					bson.M{"role.scope": "study"},
					bson.M{"$or": bson.A{
						bson.M{"site.site_id": bson.M{"$in": siteOrStoreID}},
						bson.M{"depot.depot_id": bson.M{"$in": siteOrStoreID}},
					}},
				}}}},
		)
	}
	// 用户名单去重
	pipeline = append(pipeline, bson.D{{Key: "$group", Value: bson.M{"_id": "$userinfo.info.email"}}})

	pipeline = append(pipeline, bson.D{
		{Key: "$project", Value: bson.M{
			"_id":   0,
			"email": "$_id",
		}}})

	var emailsMap []map[string]interface{}
	cursor, err := Database.Collection("notice_config").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &emailsMap)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var noticeConfig models.NoticeConfig
	err = Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": key}).Decode(&noticeConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, err
	}

	for _, item := range emailsMap {
		isExists := false
		if noticeConfig.ExcludeRecipientList != nil && len(noticeConfig.ExcludeRecipientList) > 0 {
			excludeRecipientMap := getArrMap(noticeConfig.ExcludeRecipientList)
			if _, is := excludeRecipientMap[item["email"].(primitive.A)[0].(string)]; is {
				isExists = true
			}
		}
		if !isExists {
			emails = append(emails, item["email"].(primitive.A)[0].(string))
		}
	}
	return emails, nil
}

func GetRoleUsersMailWithRole(projectOID primitive.ObjectID, envOID primitive.ObjectID, key string, siteOrStoreID ...primitive.ObjectID) ([]RoleTypeEmail, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"project_id": projectOID, "key": key, "env_id": envOID}}},
		// 获取 项目环境下的 所有用户对应的角色
		{{Key: "$lookup", Value: bson.M{
			"from": "user_project_environment",
			"let": bson.M{
				"roles":  "$roles",
				"env_id": "$env_id",
			},
			"pipeline": mongo.Pipeline{
				{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}}}},
				{{Key: "$match", Value: bson.M{"$expr": bson.M{"$ne": bson.A{"$unbind", true}}}}},
				{{Key: "$unwind", Value: "$roles"}},
				{{Key: "$match", Value: bson.M{"$expr": bson.M{"$in": bson.A{"$roles", "$$roles"}}}}},
			},
			"as": "user",
		}}},
		{{Key: "$unwind", Value: "$user"}},
		// 关联 用户信息获取邮箱
		{{Key: "$lookup", Value: bson.M{
			"from":         "user",
			"localField":   "user.user_id",
			"foreignField": "_id",
			"as":           "userinfo",
		}}},
		{{Key: "$match", Value: bson.M{"userinfo.info.status": bson.M{"$in": []int8{0, 1}}, "userinfo.deleted": bson.M{"$ne": true}}}},
	}

	d := []string{
		"notice.subject.add",             // 受试者登记
		"notice.medicine.order",          // 研究产品订单
		"notice.medicine.alarm",          // 研究产品警戒
		"notice.subject.dispensing",      //发药
		"notice.subject.random",          //随机
		"notice.subject.signOut",         //停用
		"notice.subject.replace",         //替換
		"notice.subject.update",          //修改
		"notice.subject.screen",          //筛选
		"notice.medicine.isolation",      //研究产品隔离
		"notice.order.timeout",           //订单超时提醒
		"notice.subject.alarm",           //受试者警戒邮件
		"notice.subject.unblinding",      //紧急揭盲邮件
		"notice.storehouse.alarm",        //仓库研究产品警戒邮件
		"notice.subject.alert.threshold", //受试者上限设置提醒
	}

	hasKey := false
	for _, element := range d {
		if key == element {
			hasKey = true
		}
	}
	// 是否区分过滤中心
	if hasKey {
		pipeline = append(pipeline,
			// 当前环境用户ID关联 用户仓库
			bson.D{{Key: "$lookup", Value: bson.M{
				"from": "user_depot",
				"let": bson.M{
					"env_id":  "$env_id",
					"user_id": "$user.user_id",
				},
				"pipeline": mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}}}},
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$user_id", "$$user_id"}}}}},
				},
				"as": "depot",
			}}},
			// 当前环境用户ID关联 用户中心
			bson.D{{Key: "$lookup", Value: bson.M{
				"from": "user_site",
				"let": bson.M{
					"env_id":  "$env_id",
					"user_id": "$user.user_id",
				},
				"pipeline": mongo.Pipeline{
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}}}},
					{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$user_id", "$$user_id"}}}}},
				},
				"as": "site",
			}}},
			// 当前环境用户角色关联 角色表获取  角色类型 (study,site,deport)
			bson.D{{Key: "$lookup", Value: bson.M{
				"from":         "project_role_permission",
				"localField":   "user.roles",
				"foreignField": "_id",
				"as":           "role",
			}}},
			bson.D{{Key: "$match", Value: bson.M{
				"$or": bson.A{
					bson.M{"role.scope": "study"},
					bson.M{"$or": bson.A{
						bson.M{"site.site_id": bson.M{"$in": siteOrStoreID}},
						bson.M{"depot.depot_id": bson.M{"$in": siteOrStoreID}},
					}},
				}}}},
		)
	} else {
		// 当前环境用户角色关联 角色表获取  角色类型 (study,site,deport)
		pipeline = append(pipeline, bson.D{{Key: "$lookup", Value: bson.M{
			"from":         "project_role_permission",
			"localField":   "user.roles",
			"foreignField": "_id",
			"as":           "role",
		}}})
	}
	// 用户名单去重

	pipeline = append(pipeline, bson.D{
		{Key: "$project", Value: bson.M{
			"_id":      0,
			"email":    bson.M{"$first": "$userinfo.info.email"},
			"roleName": bson.M{"$first": "$role.name"},
		}}})

	var emails []map[string]interface{}
	cursor, err := Database.Collection("notice_config").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &emails)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var noticeConfig models.NoticeConfig
	err = Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": key}).Decode(&noticeConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, err
	}

	var roleTypeEmails []RoleTypeEmail
	emailMap := make(map[string]bool)
	for _, item := range emails {
		email := item["email"].(string)

		isExists := false
		if noticeConfig.ExcludeRecipientList != nil && len(noticeConfig.ExcludeRecipientList) > 0 {
			excludeRecipientMap := getArrMap(noticeConfig.ExcludeRecipientList)
			if _, is := excludeRecipientMap[email]; is {
				isExists = true
			}
		}

		if !isExists {
			roleName := item["roleName"].(string)
			isBlind := data.RolePoolMap[roleName].Type == 2 || data.RolePoolMap[roleName].Type == 3
			_, ok := emailMap[email]
			if !ok {
				//若map没这个邮箱，就设置是否盲态
				emailMap[email] = isBlind
			} else {
				//若map有这个邮箱
				if !emailMap[email] {
					//若该邮箱已经是非盲就跳过
					continue
				} else {
					//若该邮箱是盲态，则继续赋值
					emailMap[email] = isBlind
				}
			}
		}

	}
	for k, v := range emailMap {
		roleTypeEmails = append(roleTypeEmails, RoleTypeEmail{
			Email:   k,
			IsBlind: v,
		})
	}
	return roleTypeEmails, nil
}

func getArrMap(arr []string) map[string]bool {
	// 使用一个 map 来提高查找效率
	exists := make(map[string]bool)
	// 将 array2 的元素存入 map
	for _, value := range arr {
		exists[value] = true
	}
	return exists
}

// 查询固定权限的用户
func GetPermissionUserIds(sctx mongo.SessionContext, permission []string, projectID primitive.ObjectID, envID primitive.ObjectID, siteOrStoreID ...primitive.ObjectID) ([]primitive.ObjectID, error) {

	// 接口原因，需要区分是否查询app是否开启的用户

	var (
		appNotOpen = map[string]bool{
			"operation.subject.unblinding-ip-approval": true,
			"operation.subject.dispensing-approval":    true,
		}
	)

	// 返回数据
	var userIds []primitive.ObjectID
	// 查询项目权限角色
	projectRoles := make([]models.ProjectRolePermission, 0)
	cursor, err := Database.Collection("project_role_permission").Find(sctx, bson.M{"project_id": projectID, "permissions": bson.M{"$in": permission}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(sctx, &projectRoles)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询有该角色的用户
	var roleIds []primitive.ObjectID
	for _, pr := range projectRoles {
		roleIds = append(roleIds, pr.ID)
	}
	if len(roleIds) > 0 {
		match := bson.M{"project_id": projectID, "env_id": envID, "roles": bson.M{"$in": roleIds}, "app": true}
		if appNotOpen[permission[0]] {
			match = bson.M{"project_id": projectID, "env_id": envID, "roles": bson.M{"$in": roleIds}}
		}
		if len(siteOrStoreID) > 0 {
			depotPipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{
					"$and": bson.A{
						bson.M{"$expr": bson.M{"$eq": bson.A{"$user_id", "$$user_id"}}},
						bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}},
					},
				}}},
			}
			sitePipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.M{
					"$and": bson.A{
						bson.M{"$expr": bson.M{"$eq": bson.A{"$user_id", "$$user_id"}}},
						bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}},
					},
				}}},
				{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "site_id", "foreignField": "_id", "as": "project_site"}}},
				{{Key: "$match", Value: bson.M{"project_site.deleted": 2}}},
			}
			// 连接查询
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: match}},
				{{Key: "$unwind", Value: "$roles"}},
				{{Key: "$lookup", Value: bson.M{"from": "user_site", "let": bson.M{"env_id": "$env_id", "user_id": "$user_id"}, "pipeline": sitePipeline, "as": "site"}}},
				{{Key: "$lookup", Value: bson.M{"from": "user_depot", "let": bson.M{"env_id": "$env_id", "user_id": "$user_id"}, "pipeline": depotPipeline, "as": "depot"}}},
				{{Key: "$lookup", Value: bson.M{
					"from":         "project_role_permission",
					"localField":   "roles",
					"foreignField": "_id",
					"as":           "role",
				}}},
				{{Key: "$match", Value: bson.M{"$and": bson.A{bson.M{"role._id": bson.M{"$in": roleIds}}, bson.M{
					"$or": bson.A{
						bson.M{"role.scope": "study"},
						bson.M{"$or": bson.A{
							bson.M{"site.site_id": bson.M{"$in": siteOrStoreID}},
							bson.M{"depot.depot_id": bson.M{"$in": siteOrStoreID}},
						}},
					}},
				}}}},
				{{Key: "$group", Value: bson.M{"_id": "$user_id"}}},
			}
			var data []map[string]interface{}
			cursor, err = Database.Collection("user_project_environment").Aggregate(nil, pipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &data)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, item := range data {
				userIds = append(userIds, item["_id"].(primitive.ObjectID))
			}
		} else {
			userProjectEnvironments := make([]models.UserProjectEnvironment, 0)
			cursor, err = Database.Collection("user_project_environment").Find(sctx, match)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(sctx, &userProjectEnvironments)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, upe := range userProjectEnvironments {
				userIds = append(userIds, upe.UserID)
			}
		}

	}
	return userIds, nil
}

// app数据权限判断(指中心仓库的数据权限)
func HaveAppDataPermission(
	userID primitive.ObjectID,
	envID primitive.ObjectID,
	userProjectEnvironments []models.UserProjectEnvironment,
	projectRolePermissions []models.ProjectRolePermission,
	userSites []models.UserSite,
	userDepots []models.UserDepot,
	siteIds []primitive.ObjectID,
	depotIds []primitive.ObjectID) bool {
	rolesP, b := slice.Find(userProjectEnvironments, func(index int, item models.UserProjectEnvironment) bool {
		return item.UserID == userID && item.EnvID == envID
	})
	if b {
		roles := *rolesP
		rolePermissions := slice.Filter(projectRolePermissions, func(index int, item models.ProjectRolePermission) bool {
			return slice.Contain(roles.Roles, item.ID)
		})
		_, studyB := slice.Find(rolePermissions, func(index int, item models.ProjectRolePermission) bool {
			return item.Scope == "study"
		})
		//如果角色就是study直接通过
		if studyB {
			return true
		} else {
			siteOrDepotB := false
			if len(siteIds) > 0 {
				_, siteB := slice.Find(rolePermissions, func(index int, item models.ProjectRolePermission) bool {
					return item.Scope == "site"
				})
				if siteB {
					sites := slice.Filter(userSites, func(index int, item models.UserSite) bool {
						return item.EnvID == envID
					})
					sitePIds := slice.Map(sites, func(index int, item models.UserSite) primitive.ObjectID {
						return item.SiteID
					})
					for _, siteId := range sitePIds {
						if slice.Contain(siteIds, siteId) {
							siteOrDepotB = true
						}
					}
				}

			}
			if len(depotIds) > 0 {
				_, depotB := slice.Find(rolePermissions, func(index int, item models.ProjectRolePermission) bool {
					return item.Scope == "depot"
				})
				if depotB {
					depots := slice.Filter(userDepots, func(index int, item models.UserDepot) bool {
						return item.EnvID == envID
					})
					depotPIds := slice.Map(depots, func(index int, item models.UserDepot) primitive.ObjectID {
						return item.DepotID
					})
					for _, depotId := range depotPIds {
						if slice.Contain(depotIds, depotId) {
							siteOrDepotB = true
						}
					}

				}
			}
			return siteOrDepotB
		}
	}
	return false
}

// app操作权限判断
func HaveAppOperaPermission(
	userID primitive.ObjectID,
	envID primitive.ObjectID,
	userProjectEnvironments []models.UserProjectEnvironment,
	projectRolePermissions []models.ProjectRolePermission,
	ps []string) bool {
	rolesP, b := slice.Find(userProjectEnvironments, func(index int, item models.UserProjectEnvironment) bool {
		return item.UserID == userID && item.EnvID == envID
	})
	if b {
		roles := *rolesP
		rolePermissions := slice.Filter(projectRolePermissions, func(index int, item models.ProjectRolePermission) bool {
			return slice.Contain(roles.Roles, item.ID)
		})
		permissions := make([]string, 0)
		for _, rolePermission := range rolePermissions {
			permissions = append(permissions, rolePermission.Permissions...)
		}
		permissions = slice.Unique(permissions)
		for _, p := range ps {
			if slice.Contain(permissions, p) {
				return true
			}
		}

	}
	return false
}

// app任务列表根据数据权限和操作权限过滤用户
func FilterUsers(
	permission []string,
	status int,
	envID primitive.ObjectID,
	userID primitive.ObjectID,
	userProjectEnvironments []models.UserProjectEnvironment,
	projectRolePermissions []models.ProjectRolePermission,
	userSites []models.UserSite,
	userDepots []models.UserDepot,
	siteOrStoreID ...primitive.ObjectID) (bool, error) {

	// 逻辑：数据权限没有，看不到；操作权限没有，待办任务隐藏；已完成任务只有查看操作了，允许查看。
	// 根据环境和用户ID筛选用户角色
	var roleIds []primitive.ObjectID
	for _, upe := range userProjectEnvironments {
		if upe.EnvID == envID && upe.UserID == userID {
			roleIds = upe.Roles
			break
		}
	}

	// 循环角色ID
	for _, r := range roleIds {
		// 筛选角色
		for _, prp := range projectRolePermissions {
			if r == prp.ID {
				// 循环判断角色(study、site、depot)
				if prp.Scope == "study" {
					// 判断任务是否完成
					if status == 1 {
						// 已完成返回true
						return true, nil
					} else {
						// 未完成 判断是否有操作权限（有操作权限返回true,没有返回false）
						for _, p := range permission {
							for _, prp_p := range prp.Permissions {
								if p == prp_p {
									return true, nil
								}
							}
						}
					}
				} else if prp.Scope == "site" {
					// 判断是否有数据权限
					for _, us := range userSites {
						for _, sosID := range siteOrStoreID {
							if us.UserID == userID && us.SiteID == sosID {
								// 有：判断任务是否完成(已完成返回true 未完成继续判断操作权限)
								if status == 1 {
									return true, nil
								} else {
									// 未完成 判断是否有操作权限（有操作权限返回true,没有返回false）
									for _, p := range permission {
										for _, prp_p := range prp.Permissions {
											if p == prp_p {
												return true, nil
											}
										}
									}
								}
							}
						}
					}
				} else if prp.Scope == "depot" {
					// 判断是否有数据权限
					for _, ud := range userDepots {
						for _, sosID := range siteOrStoreID {
							if ud.UserID == userID && ud.DepotID == sosID {
								// 有：判断任务是否完成(已完成返回true 未完成继续判断操作权限)
								if status == 1 {
									return true, nil
								} else {
									// 未完成 判断是否有操作权限（有操作权限返回true,没有返回false）
									for _, p := range permission {
										for _, prp_p := range prp.Permissions {
											if p == prp_p {
												return true, nil
											}
										}
									}
								}
							}
						}
					}
				} else {
					return false, nil
				}
			}
		}
	}
	return false, nil
}
