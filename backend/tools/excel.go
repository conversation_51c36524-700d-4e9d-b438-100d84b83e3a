package tools

import "fmt"

// SheetCellName 单元格名称标识，{[0, 0]: A1, [0, 1]: B1, [1, 0]: A2}
func SheetCellName(row int, col int) string {
	column := ""
	for col >= 0 {
		column = upperCaseLetter(col%26) + column
		col = col/26 - 1
	}
	return fmt.Sprintf("%s%d", column, row+1)
}

// upperCaseLetter 大写英文字母A到Z
func upperCaseLetter(index int) string {
	if index < 0 || index > 25 {
		return ""
	}
	return string('A' + byte(index))
}
