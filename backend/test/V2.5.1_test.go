package test

import (
	"clinflash-irt/models"
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"testing"
	"time"
)

func initMessageCenter(t *testing.T) {
	var data []models.User
	o := &options.FindOptions{Projection: bson.M{"_id": 1}}
	cursor, err := DB.Collection("user").Find(nil, bson.M{}, o)
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		panic(err)
	}
	var readTo []models.Receive
	var unReadTo []models.Receive
	for _, d := range data {
		readTo = append(readTo, models.Receive{
			UserId: d.ID,
			Read:   true,
		})
		unReadTo = append(unReadTo, models.Receive{
			UserId: d.ID,
			Read:   false,
		})
	}
	msgs := make([]interface{}, 0)
	msgs = append(msgs, models.MessageCenter{
		ID:         primitive.NewObjectID(),
		NoticeType: 1,
		Title:      "Clinflash IRT V2.3.1",
		Time:       time.Duration(1648968751),
		SystemUpdate: models.SystemMessage{
			Title:     "V2.3.1 发布说明",
			TitleEn:   "V2.3.1 Release Description",
			Content:   "1.IRT接入Clinflash Cloud平台，支持用户多产品下的统一登录；\n2.增加订单实时触发控制；\n3.EDC系统上受试者号更新后，IRT实时更新，同时不影响随机及发药逻辑，并增加相关的轨迹记录；\n4.IRT对于补发场景的药物，进行计算后完成补发。\n5.账号开通时，支持中心多选；\n6.发药时支持实际用药的登记，揭盲时并给与提醒；\n7.支持与elearning系统对接，提供用户学习与考试流程；\n8.相关报表导出支持国家信息显示；\n9.支持项目房间号导出（在某些权限下）；",
			ContentEn: "1. IRT access to Clinflash Cloud platform to support unified login under multiple products for users.\n2. Adding real-time trigger control for orders.\n3. IRT is updated in real time after the subject number is updated on the EDC system, without affecting the \nrandomization and dispensing logic, and adding related track records.\n4. IRT support dispense in the redispensing scenario, after the calculation to complete the redispensing.\n5. Support for site multi-selection when the account is opened.\n6. Support the registration of the actual use of IP when issuing, and give a reminder when uncovering the blind.\n7. Support docking with elearning system to provide user learning and examination process.\n8. Export of relevant reports to support the display of country information.\n9. Support for project room number export (under certain permissions).",
		},
		To: readTo,
	})
	msgs = append(msgs, models.MessageCenter{
		ID:         primitive.NewObjectID(),
		NoticeType: 1,
		Title:      "Clinflash IRT V2.4.0",
		Time:       time.Duration(**********),
		SystemUpdate: models.SystemMessage{
			Title:     "V2.4.0 发布说明",
			TitleEn:   "V2.4.0 Release Description",
			Content:   "1.新的DTP模型，支持研究产品在患者访视维度的应用，串联研究产品订单的生产及运输过程，跟踪订单状态直到订单关闭；\n跳过中心的研究产品库存管理，减少中心的库存压力和研究产品分配过程；\n2.IRT支持基于项目维度独立维护中心数据，避免客户组下的交叉使用问题；\n3.IRT使用SITE时区记录随机化和分发的时间戳，并显示在报告和轨迹页面上（在一定权限下）；\n4.基于底层RBAC模型更新角色权限模型；\n5.系统级菜单功能优化，可更好地使用IRT。",
			ContentEn: "1. A new DTP model that supports the application of research products in the subject visit dimension, \ncascading the production and shipping process of research product orders and tracking the status of orders until they are closed.\nSkipping IP inventory management at the center, reducing inventory pressure and the research product distribution process at the site.\n2. IRT supports independent maintenance of center data based on project dimensions to avoid cross-use issues under customer groups.\n3. IRT uses SITE time zones to record randomization and distribution timestamps and display them on reports and track pages (under certain permissions).\n4. Updating the role permission model based on the underlying RBAC model.\n5. System level menu function optimization for better use of IRT.",
		},
		To: readTo,
	})
	msgs = append(msgs, models.MessageCenter{
		ID:         primitive.NewObjectID(),
		NoticeType: 1,
		Title:      "Clinflash IRT V2.4.1",
		Time:       time.Duration(1661407735),
		SystemUpdate: models.SystemMessage{
			Title:     "V2.4.1 发布说明",
			TitleEn:   "V2.4.1 Release Description",
			Content:   "1.增加项目构建初始化过程中的轨迹记录；\n2.紧急揭盲，支持审批流追踪（短信或操作）及揭盲码控制；\n3.规范系统术语，优化系统英文界面及提示语；\n4.优化订单流程，及订单轨迹说明；\n5.增加国家等项目信息的报表导出；\n6.UI及UE提升及优化；\n7.优化随机和发药等场景二次确认流程；\n8.数据存储与部署，迁移至阿里云；",
			ContentEn: "1.Increase the audit trail during the project construction initialization process;\n2. Emergency unblinding can support approval flow tracking (message or operation), and unblinding code control;\n3. Standardize the terminology of the system, optimize the English  web interface and prompts of the system;\n4. Optimize the ip order process and order audit trail description;\n5. Increase the report export of project information such as countries and others;\n6. Big UI and UE improvement and optimization can lead you a good experience;\n7. Optimize the secondary confirmation process for randomization and dispensing scenarios;\n8. Data storage and deployment  are migrated to Ali-Cloud.",
		},
		To: readTo,
	})
	msgs = append(msgs, models.MessageCenter{
		ID:         primitive.NewObjectID(),
		NoticeType: 1,
		Title:      "Clinflash IRT V2.5.0",
		Time:       time.Duration(1666969197),
		SystemUpdate: models.SystemMessage{
			Title:     "V2.5.0 发布说明",
			TitleEn:   "V2.5.0 Release Description",
			Content:   "1.支持DTP模式下的发药、订单运送场景，支持追踪物流信息状态；\n2.订单终止/关闭等完整状态流程补充；\n3.盲态项目中盲态角色可申请订单，订单支持审批流程管控；\n4.项目维度控制角色权限，支持中心人员对订单的管理等；\n5.在项目及环境维度对接eLearning的配置；\n6.在订单入库、出库及库存管理上，完成与佰诚物流的对接;\n7.中心标准库接口切换DMP接口；\n8.表单创建及其他场景的部分功能优化提升。",
			ContentEn: "1.Support IP delivery and order delivery scenarios in DTP mode, and support tracking the \nstatus of logistics information；\n2.Supplement to the complete status process such as order termination/closure, etc.；\n3.Blinded roles in blinded projects can apply for orders, and orders support approval process control；\n4.Support role permissions can be controled by every project, and also supports the management of \norders by central personnel, etc.；\n5.Connect the configuration of eLearning system in the project and environment dimensions；\n6.Complete the docking with Baicheng Logistics in terms of order entry, exit and inventory management;\n7.Central standard library interface switch to DMP interface；\n8.Optimization and improvement of some functions of form creation and other scenarios.",
		},
		To: readTo,
	})
	msgs = append(msgs, models.MessageCenter{
		ID:         primitive.NewObjectID(),
		NoticeType: 1,
		Title:      "Clinflash IRT V2.5.1",
		Time:       time.Duration(time.Now().Unix()),
		SystemUpdate: models.SystemMessage{
			Title:     "V2.5.1 发布说明",
			TitleEn:   "V2.5.1 Release Description",
			Content:   "1.支持项目概览，增加项目维度的数据统计；\n2.支持项目的自定义报表；\n3.揭盲后支持是否用药控制逻辑；\n4.开放药物支持研究产品名称的直接配置发放；\n5.优化EDC与IRT的对接重构方案；\n6.支持消息中心体系，包括系统版本内容发布及异常消息同步；\n7.对邮件通知内容的支持自定义；\n8.对表单配置逻辑进行优化；\n9.其他揭盲、登记等场景功能优化。\n10.客户和项目用户的绑定、关闭等场景优化。",
			ContentEn: "1. System support for project overviews and increased data statistics for project dimensions.\n2. System support for custom reports for projects.\n3. System support for whether dispensing control logic is used after unblinding.\n4. Open IP support for direct configuration of study product names for distribution.\n5. Optimization of the EDC and IRT docking reconfiguration scheme.\n6. System support for the message center system, including system version content release and abnormal message synchronization.\n7. System support for customization of the content of email notifications.\n8. Optimization of the form configuration logic.\n9. Unblinding, registration and other scenarios functional optimization.\n10. Customer and project user binding, closing and other scenario optimization.",
		},
		To: unReadTo,
	})
	_, err = DB.Collection("message_center").InsertMany(nil, msgs)
	if err != nil {
		panic(err)
	}
}

func createDispensingIndex(t *testing.T) {
	DB.Collection("dispensing").Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{Keys: bson.D{{"dispensing_medicines.medicine_id", 1}}},
		{Keys: bson.D{{"real_dispensing_medicines.medicine_id", 1}}},
	})
}

func updateNoticeConfig(t *testing.T) {
	DB.Collection("notice_config").UpdateMany(nil, bson.M{"key": "notice.subject.dispensing"},
		bson.M{
			"$set": bson.M{
				"state": bson.A{
					"dispensing.plan-title",
					"dispensing.unscheduled-plan-title",
					"dispensing.reissue-title",
					"dispensing.replace-title",
					//"retrieval",
					//"register",
				},
				"fields_config": bson.A{"random_number"},
			},
		})
	DB.Collection("notice_config").UpdateMany(nil, bson.M{"key": "notice.medicine.order"},
		bson.M{
			"$set": bson.M{
				"state": bson.A{
					"order.no_automatic_success_title",
					"order.close_title",
					"order.send_title",
					"order.receive_title",
					"order.end_title",
					"order.lost_title",
					"order.automatic_success_title",
					"order.automatic_error_title",
				},
			},
		})
	DB.Collection("notice_config").UpdateMany(nil, bson.M{"key": "notice.medicine.isolation"},
		bson.M{
			"$set": bson.M{
				"state": bson.A{
					"medicine.freeze.title",
					"medicine.freeze.release",
				},
			},
		})
}
func updateAttribute(t *testing.T) {
	DB.Collection("attribute").UpdateMany(nil, bson.M{},
		bson.M{
			"$set": bson.M{
				"info.blinding_restrictions":      true,
				"info.pv_unblinding_restrictions": false,
			},
		})

}

func initOperation(t *testing.T) {
	DB.Collection("role_permission").UpdateMany(nil, bson.M{"name": bson.M{"$nin": []string{"Customer-Admin", "Sys-Admin"}}}, bson.M{"$addToSet": bson.M{"permissions": "operation.project.status.view"}})
	DB.Collection("role_permission").UpdateMany(nil, bson.M{"name": bson.M{"$nin": []string{"Customer-Admin", "Sys-Admin"}}}, bson.M{"$addToSet": bson.M{"permissions": "operation.project.task.view"}})
	DB.Collection("role_permission").UpdateMany(nil, bson.M{"name": bson.M{"$nin": []string{"Customer-Admin", "Sys-Admin"}}}, bson.M{"$addToSet": bson.M{"permissions": "operation.project.dynamics.view"}})
	DB.Collection("project_role_permission").UpdateMany(nil, bson.M{"name": bson.M{"$nin": []string{"Customer-Admin", "Sys-Admin"}}}, bson.M{"$addToSet": bson.M{"permissions": "operation.project.status.view"}})
	DB.Collection("project_role_permission").UpdateMany(nil, bson.M{"name": bson.M{"$nin": []string{"Customer-Admin", "Sys-Admin"}}}, bson.M{"$addToSet": bson.M{"permissions": "operation.project.task.view"}})
	DB.Collection("project_role_permission").UpdateMany(nil, bson.M{"name": bson.M{"$nin": []string{"Customer-Admin", "Sys-Admin"}}}, bson.M{"$addToSet": bson.M{"permissions": "operation.project.dynamics.view"}})

}

func attributePrefixPrefixExpression(t *testing.T) {
	type attribute struct {
		ID              primitive.ObjectID `bson:"_id"`
		SitePrefix      bool               `bson:"site_prefix"`       // 废弃 是否将中心作为前缀
		PrefixConnector string             `bson:"prefix_connector"`  // 废弃 前缀连接符
		OtherPrefix     bool               `bson:"other_prefix"`      // 废弃 受试者号的其他前缀
		OtherPrefixText string             `bson:"other_prefix_text"` // 废弃 其他前缀文本
	}
	attributes := make([]attribute, 0)
	opts := &options.FindOptions{Projection: bson.M{
		"_id":               1,
		"site_prefix":       "$info.site_prefix",
		"prefix_connector":  "$info.prefix_connector",
		"other_prefix":      "$info.other_prefix",
		"other_prefix_text": "$info.other_prefix_text",
	}}
	cursor, err := DB.Collection("attribute").Find(nil, bson.M{
		"info.prefix": true,
		"$or": bson.A{
			bson.M{"info.site_prefix": true},
			bson.M{"info.other_prefix": true}},
	}, opts)
	if err != nil {
		return
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return
	}
	for _, a := range attributes {
		sitePrefix := ""
		other := ""
		if a.SitePrefix {
			sitePrefix = fmt.Sprintf("%s%s", "{siteNO}", a.PrefixConnector)
		}
		if a.OtherPrefix {
			other = a.OtherPrefixText
		}

		prefixExpression := fmt.Sprintf("%s%s", sitePrefix, other)
		DB.Collection("attribute").UpdateOne(nil, bson.M{"_id": a.ID}, bson.M{"$set": bson.M{"info.prefix_expression": prefixExpression}})
	}

}

//func fixDynamicsOrder(t *testing.T) {
//	dynamics := make([]models.ProjectDynamics, 0)
//	cursor, _ := DB.Collection("project_dynamics").Find(nil, bson.M{"type_tran": "project_dynamics_type_overtime"})
//	cursor.All(nil, &dynamics)
//	orderNumbers := slice.Map(dynamics, func(index int, item models.ProjectDynamics) string {
//		return item.ContentData["orderNumber"].(string)
//	})
//	orderNumbers = slice.Unique(orderNumbers)
//	orderOpt := &options.FindOptions{Projection: bson.M{
//		"_id":          1,
//		"order_number": 1,
//		"type":         1,
//	}}
//	orders := make([]models.MedicineOrder, 0)
//	cursor, err := DB.Collection("medicine_order").Find(nil, bson.M{"order_number": bson.M{"$in": orderNumbers}}, orderOpt)
//	if err != nil {
//		return
//	}
//	cursor.All(nil, &orders)
//	dyIds := make([]primitive.ObjectID, 0)
//	for _, dynamic := range dynamics {
//		number := dynamic.ContentData["orderNumber"].(string)
//		oP, ok := slice.Find(orders, func(index int, item models.MedicineOrder) bool {
//			return item.OrderNumber == number
//		})
//		if ok {
//			order := *oP
//			if order.Type == 3 || order.Type == 4 {
//				dyIds = append(dyIds, dynamic.ID)
//			}
//		}
//	}
//	many, err := DB.Collection("project_dynamics").UpdateMany(nil, bson.M{"_id": bson.M{"$in": dyIds}}, bson.M{"$set": bson.M{"type_tran": "project_dynamics_type_overtime_recovery"}})
//	if err != nil {
//		return
//	}
//	fmt.Println(many)
//}
