package test

import (
	"clinflash-irt/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"testing"
)

// 更新通知设置-基本设置邮件语言配置-US节点，统一全部英文
func InsertUSEmailLanguageHistory(t *testing.T) {

	projectList := make([]models.Project, 0)
	cursor, err := DB.Collection("project").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projectList)
	if err != nil {
		panic(err)
	}

	roles := []primitive.ObjectID{}
	fieldsConfig := []string{}
	state := []string{}

	noticeConfigList := make([]interface{}, 0)
	if projectList != nil && len(projectList) > 0 {
		for _, project := range projectList {
			if project.Environments != nil && len(project.Environments) > 0 {
				for _, environment := range project.Environments {
					noticeConfigList = append(noticeConfigList, models.NoticeConfig{
						ID:            primitive.NewObjectID(),
						CustomerID:    project.CustomerID,
						ProjectID:     project.ID,
						EnvironmentID: environment.ID,
						Roles:         roles,
						FieldsConfig:  fieldsConfig,
						State:         state,
						Key:           "notice.basic.settings",
						Automatic:     2,
						Manual:        2,
					})
				}
			}
		}
	}

	if noticeConfigList != nil && len(noticeConfigList) > 0 {
		if _, err := DB.Collection("notice_config").InsertMany(nil, noticeConfigList); err != nil {
			panic(err)
		}
	}

}
