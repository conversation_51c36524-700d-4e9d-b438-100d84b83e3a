package test

import (
	"clinflash-irt/models"
	"context"
	"fmt"
	"log"
	"testing"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UpdateDispensingVisitCycleInfoID 更新dispensing的VisitInfo.VisitCycleInfoID
// 根据subject关联的dispensings，查询另一个cohort的visit_cycle，
// 通过dispensing.VisitInfo.Number关联visitCycle.Infos.Number，
// 将关联到的visitCycle.Infos.ID更新到dispensing.VisitInfo.VisitCycleInfoID
func TestUpdateDispensingVisitCycleInfoID(t *testing.T) {
	// 配置参数 - 请根据实际情况修改这些参数
	var (
		envID          = "67d3e2782df90baf862d2b1b" // 替换为实际的环境ID
		targetCohortID = "685378e81b4d86c4eab8ef70" // 要关联的visit_cycle所在的cohort_id
		subjectID      = "6878ce8854f6b073d07f1c20" // 可选：指定特定的受试者ID，为空则处理所有受试者
		dryRun         = true                       // 是否为试运行模式，true时只打印不实际更新
	)

	err := updateDispensingVisitCycleInfoID(envID, targetCohortID, subjectID, dryRun)
	if err != nil {
		t.Fatalf("更新失败: %v", err)
	}
}

func updateDispensingVisitCycleInfoID(envID, targetCohortID, subjectID string, dryRun bool) error {
	ctx := context.Background()
	envOID, err := primitive.ObjectIDFromHex(envID)
	if err != nil {
		return fmt.Errorf("无效的环境ID: %v", err)
	}
	targetCohortOID, err := primitive.ObjectIDFromHex(targetCohortID)
	if err != nil {
		return fmt.Errorf("无效的目标cohort_id: %v", err)
	}

	// 1. 查询目标cohort的visit_cycle
	log.Printf("正在查询目标cohort (%s) 的visit_cycle...", targetCohortID)
	var targetVisitCycle models.VisitCycle
	err = DB.Collection("visit_cycle").FindOne(ctx, bson.M{
		"env_id":    envOID,
		"cohort_id": targetCohortOID,
	}).Decode(&targetVisitCycle)
	if err != nil {
		return fmt.Errorf("查询目标visit_cycle失败: %v", err)
	}

	// 创建visit number到visit cycle info ID的映射
	visitNumberToInfoID := make(map[string]primitive.ObjectID)
	for _, info := range targetVisitCycle.Infos {
		visitNumberToInfoID[info.Number] = info.ID
		log.Printf("目标visit_cycle中找到访视: Number=%s, ID=%s, Name=%s", info.Number, info.ID.Hex(), info.Name)
	}
	subjectOID, err := primitive.ObjectIDFromHex(subjectID)
	// 2. 构建查询条件
	dispensingFilter := bson.M{"subject_id": subjectOID}

	// 3. 查询当前cohort下的所有dispensing记录
	cursor, err := DB.Collection("dispensing").Find(ctx, dispensingFilter)
	if err != nil {
		return fmt.Errorf("查询dispensing记录失败: %v", err)
	}
	defer cursor.Close(ctx)

	var dispensings []models.Dispensing
	err = cursor.All(ctx, &dispensings)
	if err != nil {
		return fmt.Errorf("读取dispensing记录失败: %v", err)
	}

	log.Printf("找到 %d 条dispensing记录", len(dispensings))

	// 4. 处理每条dispensing记录
	updateCount := 0
	skipCount := 0
	errorCount := 0

	for _, dispensing := range dispensings {
		visitNumber := dispensing.VisitInfo.Number
		currentVisitCycleInfoID := dispensing.VisitInfo.VisitCycleInfoID

		// 查找对应的visit cycle info ID
		targetVisitCycleInfoID, exists := visitNumberToInfoID[visitNumber]
		if !exists {
			log.Printf("警告: dispensing ID=%s, 访视编号=%s 在目标visit_cycle中未找到对应的访视",
				dispensing.ID.Hex(), visitNumber)
			skipCount++
			continue
		}

		// 检查是否需要更新
		if currentVisitCycleInfoID == targetVisitCycleInfoID {
			log.Printf("跳过: dispensing ID=%s, 访视编号=%s 的VisitCycleInfoID已经是正确的值",
				dispensing.ID.Hex(), visitNumber)
			skipCount++
			continue
		}

		log.Printf("需要更新: dispensing ID=%s, 访视编号=%s, 当前VisitCycleInfoID=%s, 目标VisitCycleInfoID=%s",
			dispensing.ID.Hex(), visitNumber, currentVisitCycleInfoID.Hex(), targetVisitCycleInfoID.Hex())

		if !dryRun {
			// 执行更新
			updateResult, err := DB.Collection("dispensing").UpdateOne(ctx,
				bson.M{"_id": dispensing.ID},
				bson.M{"$set": bson.M{"visit_info.visit_cycle_info_id": targetVisitCycleInfoID}})

			if err != nil {
				log.Printf("错误: 更新dispensing ID=%s 失败: %v", dispensing.ID.Hex(), err)
				errorCount++
				continue
			}

			if updateResult.ModifiedCount > 0 {
				log.Printf("成功更新: dispensing ID=%s", dispensing.ID.Hex())
				updateCount++
			} else {
				log.Printf("警告: dispensing ID=%s 更新操作未修改任何记录", dispensing.ID.Hex())
				skipCount++
			}
		} else {
			log.Printf("试运行模式: 将会更新dispensing ID=%s", dispensing.ID.Hex())
			updateCount++
		}
	}

	// 5. 输出统计结果
	log.Printf("\n=== 处理完成 ===")
	log.Printf("总记录数: %d", len(dispensings))
	log.Printf("需要更新: %d", updateCount)
	log.Printf("跳过记录: %d", skipCount)
	log.Printf("错误记录: %d", errorCount)

	if dryRun {
		log.Printf("注意: 这是试运行模式，没有实际执行更新操作")
		log.Printf("要执行实际更新，请将 dryRun 参数设置为 false")
	}

	return nil
}
