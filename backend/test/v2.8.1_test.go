package test

import (
	"clinflash-irt/models"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"testing"
)

// 添加app版本号
func AddAppVersion(t *testing.T) {
	//FID, err := primitive.ObjectIDFromHex("") //dev
	//FID, err := primitive.ObjectIDFromHex("65fd3d0948bd67019c0c033d") //test
	//FID, err := primitive.ObjectIDFromHex("") //uat
	FID, err := primitive.ObjectIDFromHex("65fd58fd47260000fa006161") //生产
	if err != nil {
		panic(err)
	}
	appVersion := models.AppVersion{
		ID:            primitive.NewObjectID(),
		VersionNumber: "2.8.0",
		UpdateContent: "1、发药、推送等功能的加入;2、已知bug的修复",
		FileID:        FID,
		FileName:      "Clinflash eIRT",
	}
	_, err = DB.Collection("app_version").InsertOne(nil, appVersion)
	if err != nil {
		panic(err)
	}
}
