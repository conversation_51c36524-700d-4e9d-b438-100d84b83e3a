package test

import (
	"clinflash-irt/config"
	"clinflash-irt/pb"
	"clinflash-irt/tools"
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/emptypb"
)

func TestDMP(t *testing.T) {
	// init
	config.Init()
	tools.InitDmpGrpcClientConn()
	defer tools.CloseDmpGrpcConn()

	// grpc init
	grpcCtx := tools.DmpGrpcContext()
	cli := pb.NewHospitalClient(tools.DmpGrpcClientConn)

	// action
	start := time.Now()
	// _, err := cli.SearchHospitalNames(grpcCtx, &pb.SearchHospitalNamesRequest{HospitalName: "南开"})
	out, err := cli.ListAllHospitals(grpcCtx, &emptypb.Empty{})
	duration := time.Since(start)
	t.Log("duartion", duration)
	if err != nil {
		t.<PERSON>rror(err)
	}
	t.Log("out", out)
}
