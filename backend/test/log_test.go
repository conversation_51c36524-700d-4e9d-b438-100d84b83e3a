package test

import (
	"testing"
	"time"

	graylog "github.com/gemnasium/logrus-graylog-hook/v3"
	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"
)

type NullFormatter struct{}

// Don't spend time formatting logs
func (NullFormatter) Format(e *log.Entry) ([]byte, error) {
	return []byte{}, nil
}

func TestLog(t *testing.T) {
	hook := graylog.NewAsyncGraylogHook("172.18.20.9:12201", map[string]interface{}{"tag": "clinflash-irt"})
	log.AddHook(hook)
	log.SetFormatter(&log.JSONFormatter{PrettyPrint: true})
	log.SetReportCaller(true)
	// log.SetFormatter(&NullFormatter{})
	log.WithFields(log.Fields{"env": "dev"}).Info("some logging message ---- 6")
	// err := testx()
	// log.Error(fmt.Sprintf("%+v", err))
	time.Sleep(time.Second * 2)
}

func testx() error {
	if err := test2(); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func test2() error {
	err := errors.New("bbbbbb")
	return errors.WithStack(err)
}
