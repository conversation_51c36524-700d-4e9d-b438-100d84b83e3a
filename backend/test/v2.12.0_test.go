package test

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"testing"
)

// UpdateOperationHistoryPushSetting 历史数据推送设置权限
func UpdateOperationHistoryPushSetting(t *testing.T) {
	_, err := DB.Collection("project_role_permission").UpdateMany(nil,
		bson.M{
			"$or": []bson.M{
				{"name": "IP Officer"},
				{"name": "IRT Designer"},
			},
		},
		bson.M{
			"$addToSet": bson.M{
				"permissions": "operation.build.push.history",
			},
		},
	)
	if err != nil {
		panic(err)
	}

	_, err = DB.Collection("role_permission").UpdateMany(nil,
		bson.M{
			"$or": []bson.M{
				{"name": "IP Officer"},
				{"name": "IRT Designer"},
			},
		}, bson.M{
			"$addToSet": bson.M{
				"permissions": "operation.build.push.history",
			},
		},
	)
	if err != nil {
		panic(err)
	}
}

// UpdateInvalidListSetting 查看已删除受试者的权限
func UpdateInvalidListSetting(t *testing.T) {
	_, err := DB.Collection("project_role_permission").UpdateMany(nil,
		bson.M{"name": "IP Officer"},
		bson.M{
			"$addToSet": bson.M{
				"permissions": "operation.subject.invalid-list",
			},
		},
	)
	if err != nil {
		panic(err)
	}

	_, err = DB.Collection("role_permission").UpdateMany(nil,
		bson.M{"name": "IP Officer"},
		bson.M{
			"$addToSet": bson.M{
				"permissions": "operation.subject.invalid-list",
			},
		},
	)
	if err != nil {
		panic(err)
	}
}

func ExportCodeAttribute(T *testing.T) {
	type ProjectPermission struct {
		models.ProjectRolePermission ` bson:",inline"`
		Project                      models.Project `bson:"project"`
		User                         []models.User  `bson:"user"`
	}
	var projectPermissions []ProjectPermission
	cursor, err := DB.Collection("project_role_permission").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{}}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "project.administrators", "foreignField": "_id", "as": "user"}}},
		{{"$unwind", "$project"}},
	})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projectPermissions)
	if err != nil {
		panic(err)

	}
	content := [][]interface{}{}
	for _, projectPermission := range projectPermissions {
		attrView := slice.Contain(projectPermission.Permissions, "operation.build.attribute.view")
		codeView := slice.Contain(projectPermission.Permissions, "operation.build.code-rule.view")

		attrEdit := slice.Contain(projectPermission.Permissions, "operation.build.attribute.edit")
		codeEdit := slice.Contain(projectPermission.Permissions, "operation.build.code-rule.edit")
		tmp := []interface{}{}
		if attrView != codeView || attrEdit != codeEdit {
			tmp = append(tmp, projectPermission.Project.Number)
			tmp = append(tmp, projectPermission.Name)
			tmp = append(tmp, attrView)
			tmp = append(tmp, codeView)
			tmp = append(tmp, attrEdit)
			tmp = append(tmp, codeEdit)
			users := slice.Map(projectPermission.User, func(index int, item models.User) string {
				return item.Email
			})
			tmp = append(tmp, users)

			content = append(content, tmp)
		}
	}
	f := excelize.NewFile()
	title := []interface{}{"项目编号", "角色名称", "属性配置查看权限", "编码配置查看权限", "属性配置编辑权限", "编码配置编辑权限", "项目管理员"}
	tools.ExportSheet(f, "Sheet1", title, content)
	err = f.SaveAs("attribute_code.xlsx")
	if err != nil {
		panic(err)
	}
	err = f.Close()
	if err != nil {
		panic(err)

	}
}

//多语言添加语言下拉配置
//db.getCollection("setting_config").insert( {
//	key: "multiLanguage",
//	data: [
//		{
//			"code": "zh",
//			"name": "简体中文",
//		},
//		{
//			"code": "ft",
//			"name": "繁体中文",
//		},
//		{
//			"code": "en",
//			"name": "English",
//		},
//		{
//			"code": "ja",
//			"name": "日本語",
//		},
//		{
//			"code": "ko",
//			"name": "한국어",
//		},
//		{
//			"code": "th",
//			"name": "Thai",
//		},
//	]
//} )
