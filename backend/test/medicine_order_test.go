package test

import (
	"clinflash-irt/models"
	"sync"
	"testing"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func handlerCancelOrder(t *testing.T) {
	cancelOrders := make([]models.MedicineOrder, 0)
	cursor, err := DB.Collection("medicine_order").Find(nil, bson.M{"status": 5})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &cancelOrders)
	if err != nil {
		panic(err)
	}
	type SimpleHistory struct {
		ID  primitive.ObjectID `json:"id" bson:"_id"`
		Key string             `json:"key" bson:"key"`
	}

	var wg sync.WaitGroup
	wg.Add(len(cancelOrders))
	for _, order := range cancelOrders {
		go func(o models.MedicineOrder) {
			defer wg.Done()
			history := SimpleHistory{}
			err := DB.Collection("history").FindOne(nil, bson.M{"oid": o.ID,
				"key": bson.M{"$in": bson.A{
					"history.order.confrim",
					"history.order.confrim-new",
				}}}).Decode(&history)
			if err != nil && err != mongo.ErrNoDocuments {
				panic(err)
			}
			if history.Key == "history.order.confrim" || history.Key == "history.order.confrim-new" {
				DB.Collection("medicine_order").UpdateOne(nil, bson.M{"_id": o.ID}, bson.M{"$set": bson.M{"status": 9}})
				cancelHistory := SimpleHistory{}
				err := DB.Collection("history").FindOne(nil, bson.M{"oid": o.ID,
					"key": bson.M{"$in": bson.A{
						"history.order.cancel",
						"history.order.cancel-new",
					}}}).Decode(&cancelHistory)
				if err != nil && err != mongo.ErrNoDocuments {
					panic(err)
				}
				if cancelHistory.Key == "history.order.cancel" {
					DB.Collection("history").UpdateOne(nil, bson.M{"_id": cancelHistory.ID}, bson.M{"$set": bson.M{"key": "history.order.close"}})
				} else if cancelHistory.Key == "history.order.cancel-new" {
					DB.Collection("history").UpdateOne(nil, bson.M{"_id": cancelHistory.ID}, bson.M{"$set": bson.M{"key": "history.order.close-new"}})
				}
			}
		}(order)

	}
	wg.Wait()
}
