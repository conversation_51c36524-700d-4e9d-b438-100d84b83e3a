package test

import (
	"clinflash-irt/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"testing"
)

// UpdateAttributeField 属性配置未更新Field字段label、labelEn问题修复
func UpdateAttributeField(t *testing.T) {

	projectList := make([]models.Project, 0)
	pCursor, err := DB.Collection("project").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = pCursor.All(nil, &projectList)
	if err != nil {
		panic(err)
	}

	if projectList != nil && len(projectList) > 0 {
		for _, project := range projectList {
			if project.Environments != nil && len(project.Environments) > 0 {
				for _, environment := range project.Environments {
					cohortIdList := make([]primitive.ObjectID, 0)
					if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
						for _, cohort := range environment.Cohorts {
							cohortIdList = append(cohortIdList, cohort.ID)
						}

						if cohortIdList != nil && len(cohortIdList) > 0 {
							var attribute models.Attribute
							err = DB.Collection("attribute").FindOne(nil, bson.M{"project_id": project.ID, "customer_id": project.CustomerID, "env_id": environment.ID, "cohort_id": cohortIdList[0]}).Decode(&attribute)
							if err != nil && err != mongo.ErrNoDocuments {
								panic(err)
							}

							fiter := bson.M{
								"project_id":  project.ID,
								"env_id":      environment.ID,
								"customer_id": project.CustomerID,
								"cohort_id":   bson.M{"$in": cohortIdList},
							}
							aliUpdate := bson.M{"$set": bson.M{"info.field.label": attribute.AttributeInfo.SubjectReplaceText, "info.field.label_en": attribute.AttributeInfo.SubjectReplaceTextEn}}
							_, rderr := DB.Collection("attribute").UpdateMany(nil, fiter, aliUpdate)
							if rderr != nil {
								panic(err)
							}

						}

					}
				}
			}

		}
	}

}
