package test

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"github.com/wxnacy/wgo/arrays"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"reflect"
	"strconv"
	"strings"
	"testing"
	"time"
)

// CopyEnvironmentToProd -- PROD复制出来的UAT，然后UAT新增一个cohort1,再把UAT复制回PROD
func CopyEnvironmentToProd(t *testing.T) {

	oldEnvId := "68087f91e1cc848d5badf3ee"
	newEnvId := "680d9c9acf29beb8bb3ddaab"
	oldCohortId := "685cb1c60726e154bd94eba5" //Part B-45mg
	newCohortId := "686614f511c017c226ee1837" //Part B-45mg
	//oldCohortId := "6861f18911c017c226e5cbab" //Part B-75mg
	//newCohortId := "6866150911c017c226ee18ad" //Part B-75mg
	//oldCohortId := "6861f1a211c017c226e5cc69" //Part B-105mg
	//newCohortId := "6866152411c017c226ee1930" //Part B-105mg
	oldEnvName := "UAT1"
	newCopyEnv := "PROD"
	projectNumber := "UA026-CN101"
	userID := "622ea2fc1a98a2bfd77b58f1"

	oldEnvID, _ := primitive.ObjectIDFromHex(oldEnvId)
	newEnvID, _ := primitive.ObjectIDFromHex(newEnvId)
	oldCohortID, _ := primitive.ObjectIDFromHex(oldCohortId)
	newCohortID, _ := primitive.ObjectIDFromHex(newCohortId)
	userOID, _ := primitive.ObjectIDFromHex(userID)

	var project models.Project
	err := DB.Collection("project").FindOne(nil, bson.M{"info.number": projectNumber}).Decode(&project)
	if err != nil {
		panic(err)
	}

	copyEnv := oldEnvName + "->" + newCopyEnv
	//复制通知配置
	err = copyNoticeConfig(userOID, project, oldEnvID, newEnvID, copyEnv, oldEnvName, newCopyEnv)
	if err != nil {
		panic(err)
	}

	// 复制项目属性
	err = copyAttribute(userOID, project, oldEnvID, oldCohortID, newEnvID, newCohortID, copyEnv)
	if err != nil {
		panic(err)
	}
	// 复制表单
	form, err := copyForm(userOID, project, oldEnvID, oldCohortID, newEnvID, newCohortID, copyEnv)
	if err != nil {
		panic(err)
	}
	// 复制随机配置
	err = copyRandomDesign(userOID, project, oldEnvID, oldCohortID, newEnvID, newCohortID, copyEnv)
	if err != nil {
		panic(err)
	}
	// 复制访视周期
	visitCycleInfo, err := copyVisitCycle(userOID, project, oldEnvID, oldCohortID, newEnvID, newCohortID, copyEnv)
	if err != nil {
		panic(err)
	}
	// 复制研究产品配置
	drugConfigure, err := copyDrugConfig(userOID, project, oldEnvID, oldCohortID, newEnvID, newCohortID, copyEnv, visitCycleInfo)
	if err != nil {
		panic(err)
	}

	// 复制包装配置
	err = copyDrugPackageConfigure(userOID, project, oldEnvID, newEnvID)
	if err != nil {
		panic(err)
	}

	// 复制研究产品配置-设置
	err = copyDrugConfigSetting(userOID, project, oldEnvID, oldCohortID, newEnvID, newCohortID, form, drugConfigure, copyEnv)
	if err != nil {
		panic(err)
	}
	//复制编码配置
	err = copyCodeRule(userOID, project, oldEnvID, oldCohortID, newEnvID, newCohortID, copyEnv)
	if err != nil {
		panic(err)
	}

}

// copyNoticeConfig 复制通知配置
func copyNoticeConfig(userOID primitive.ObjectID, project models.Project, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID, copyEnv string, oldEnvName string, newCopyEnv string) error {
	noticeConfig := make([]models.NoticeConfig, 0)
	cursor, err := DB.Collection("notice_config").Find(nil, bson.M{"env_id": oldEnvID})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &noticeConfig)
	if err != nil {
		return errors.WithStack(err)
	}

	insertData := make([]interface{}, 0)
	newNoticeConfig := make([]models.NoticeConfig, 0)
	for _, m := range noticeConfig {
		nc := m
		nc.ID = primitive.NewObjectID()
		nc.EnvironmentID = newEnvID
		insertData = append(insertData, nc)
		newNoticeConfig = append(newNoticeConfig, nc)
	}
	if len(insertData) > 0 {

		if oldEnvName != "PROD" || newCopyEnv == "PROD" {
			//先清空
			// 删除多个文档
			_, err = DB.Collection("notice_config").DeleteMany(nil, bson.M{"env_id": newEnvID})
			if err != nil && err != mongo.ErrNoDocuments {
				return errors.WithStack(err)
			}
		}

		_, err = DB.Collection("notice_config").InsertMany(nil, insertData)
		if err != nil {
			return errors.WithStack(err)
		}
		OID := newEnvID
		for _, nc := range newNoticeConfig {
			err = insertNotificationsLog(userOID, nc.Key, OID, 10, models.NoticeConfig{}, nc, copyEnv)
		}
	}

	return nil

}

// copyAttribute 复制项目属性
func copyAttribute(userOID primitive.ObjectID, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string) error {
	filter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		filter["cohort_id"] = oldCohortID
	}
	var attribute models.Attribute
	_ = DB.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if attribute.ID != primitive.NilObjectID {

		oldInfo := models.AttributeInfo{}

		attribute.ID = primitive.NewObjectID()
		attribute.EnvironmentID = newEnvID
		if project.ProjectInfo.Type == 1 {
			attribute.AttributeInfo.ConnectAli = false
			attribute.AttributeInfo.AliProjectNO = ""
		} else {
			attribute.CohortID = newCohortID
		}
		attribute.AttributeInfo.Field.ID = primitive.NewObjectID()

		condition := bson.M{"env_id": newEnvID}
		if project.ProjectInfo.Type != 1 {
			condition["cohort_id"] = newCohortID
		}
		if count, _ := DB.Collection("attribute").CountDocuments(nil, condition); count > 0 {
			var ab models.Attribute
			err := DB.Collection("attribute").FindOne(nil, condition).Decode(&ab)
			if err != nil {
				return errors.WithStack(err)
			}

			ud := bson.M{"$set": bson.M{"info": attribute.AttributeInfo}}
			_, err = DB.Collection("attribute").UpdateOne(nil, condition, ud)
			if err != nil {
				return errors.WithStack(err)
			}

			//oldInfo = ab.AttributeInfo

		} else {
			_, err := DB.Collection("attribute").InsertOne(nil, attribute)
			if err != nil {
				return errors.WithStack(err)
			}

			//oldInfo = models.AttributeInfo{}
		}

		OID := newEnvID
		if !newCohortID.IsZero() {
			OID = newCohortID
		}
		err := insertAttributeLog(userOID, OID, 10, oldInfo, attribute.AttributeInfo, "", "", copyEnv)
		if err != nil {
			return errors.WithStack(err)
		}

	}
	return nil
}

// copyForm 复制表单
func copyForm(userOID primitive.ObjectID, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string) (models.Form, error) {
	filter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		filter["cohort_id"] = oldCohortID
	}
	var form models.Form
	_ = DB.Collection("form").FindOne(nil, filter).Decode(&form)
	if form.ID != primitive.NilObjectID {
		form.ID = primitive.NewObjectID()
		form.EnvironmentID = newEnvID
		if project.ProjectInfo.Type != 1 {
			form.CohortID = newCohortID
		}
		OID := newEnvID
		if !newCohortID.IsZero() {
			OID = newCohortID
		}
		var fields []models.Field
		for _, field := range form.Fields {
			field.ID = primitive.NewObjectID()

			err := insertFormLog(userOID, OID, 10, models.Field{}, field, form.ID, copyEnv)
			if err != nil {
				return form, errors.WithStack(err)
			}
			fields = append(fields, field)
		}
		form.Fields = fields

		condition := bson.M{"env_id": newEnvID}
		if project.ProjectInfo.Type != 1 {
			condition["cohort_id"] = newCohortID
		}
		if count, _ := DB.Collection("form").CountDocuments(nil, condition); count > 0 {

			ud := bson.M{"$set": bson.M{"fields": form.Fields}}
			_, err := DB.Collection("form").UpdateOne(nil, condition, ud)
			if err != nil {
				return form, errors.WithStack(err)
			}

		} else {
			_, err := DB.Collection("form").InsertOne(nil, form)
			if err != nil {
				return form, errors.WithStack(err)
			}
		}

	}
	return form, nil
}

// copyRandomDesign 复制随机设置
func copyRandomDesign(userOID primitive.ObjectID, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string) error {
	filter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		filter["cohort_id"] = oldCohortID
	}
	var randomDesign models.RandomDesign
	_ = DB.Collection("random_design").FindOne(nil, filter).Decode(&randomDesign)

	isCopyData := false

	oldEnv, b := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == oldEnvID
	})
	if b {
		if oldEnv.Name == "PROD" {
			//判断是否有受试者
			if count, _ := DB.Collection("subject").CountDocuments(nil, filter); count > 0 {
				isCopyData = true
			}

			//判断是否有订单
			if count, _ := DB.Collection("medicine_order").CountDocuments(nil, filter); count > 0 {
				isCopyData = true
			}
		}
	}

	if randomDesign.ID != primitive.NilObjectID {
		randomDesign.ID = primitive.NewObjectID()
		randomDesign.EnvironmentID = newEnvID
		if project.ProjectInfo.Type != 1 {
			randomDesign.CohortID = newCohortID
		}

		// 组别
		groups := make([]models.RandomGroup, 0)
		for _, group := range randomDesign.Info.Groups {
			group.ID = primitive.NewObjectID()
			group.IsCopyData = isCopyData
			subGroupList := make([]models.SubGroup, 0)
			if group.SubGroup != nil && len(group.SubGroup) > 0 {
				for _, sub := range group.SubGroup {
					sub.IsCopyData = isCopyData
					subGroupList = append(subGroupList, sub)
				}
			}
			group.SubGroup = subGroupList
			groups = append(groups, group)
		}
		randomDesign.Info.Groups = groups

		// 分层因素
		randomFactors := make([]models.RandomFactor, 0)
		for _, factor := range randomDesign.Info.Factors {
			factor.ID = primitive.NewObjectID()
			optionList := make([]models.Option, 0)
			if factor.Options != nil && len(factor.Options) > 0 {
				for _, option := range factor.Options {
					option.IsCopyData = isCopyData
					optionList = append(optionList, option)
				}
			}
			factor.IsCopyData = isCopyData
			factor.Options = optionList
			randomFactors = append(randomFactors, factor)
		}
		randomDesign.Info.Factors = randomFactors

		// 排列组合
		randomDesign.Info.BlockNumber = 1 // 返回从1开始
		randomDesign.Info.Combination = []models.FactorsCombination{}

		oldInfo := models.RandomDesignInfo{}

		condition := bson.M{"env_id": newEnvID}
		if project.ProjectInfo.Type != 1 {
			condition["cohort_id"] = newCohortID
		}
		if count, _ := DB.Collection("random_design").CountDocuments(nil, condition); count > 0 {
			var ab models.RandomDesign
			err := DB.Collection("random_design").FindOne(nil, condition).Decode(&ab)
			if err != nil {
				return errors.WithStack(err)
			}

			//组别
			if randomDesign.Info.Groups != nil && len(randomDesign.Info.Groups) > 0 {
				groupList := make([]models.RandomGroup, 0)
				for _, group := range randomDesign.Info.Groups {
					if ab.Info.Groups != nil && len(ab.Info.Groups) > 0 {
						rg, ba := slice.Find(ab.Info.Groups, func(index int, item models.RandomGroup) bool {
							return item.Code == group.Code && item.Status == group.Status
						})
						if ba {
							group.ID = rg.ID
						}
					}
					group.IsCopyData = false
					subGroupList := make([]models.SubGroup, 0)
					if group.SubGroup != nil && len(group.SubGroup) > 0 {
						for _, sub := range group.SubGroup {
							sub.IsCopyData = false
							subGroupList = append(subGroupList, sub)
						}
					}
					group.SubGroup = subGroupList
					groupList = append(groupList, group)
				}
				randomDesign.Info.Groups = groups
			}

			//分层因素
			if randomDesign.Info.Factors != nil && len(randomDesign.Info.Factors) > 0 {
				randomFactorList := make([]models.RandomFactor, 0)
				for _, factor := range randomDesign.Info.Factors {
					if ab.Info.Factors != nil && len(ab.Info.Factors) > 0 {
						rg, ba := slice.Find(ab.Info.Factors, func(index int, item models.RandomFactor) bool {
							return item.Number == factor.Number && item.Name == factor.Name && item.Status == factor.Status
						})
						if ba {
							factor.ID = rg.ID
						}
					}
					optionList := make([]models.Option, 0)
					if factor.Options != nil && len(factor.Options) > 0 {
						for _, option := range factor.Options {
							option.IsCopyData = false
							optionList = append(optionList, option)
						}
					}
					factor.IsCopyData = false
					factor.Options = optionList
					randomFactorList = append(randomFactorList, factor)
				}
				randomDesign.Info.Factors = randomFactorList
			}

			ud := bson.M{"$set": bson.M{"info": randomDesign.Info}}
			_, err = DB.Collection("random_design").UpdateOne(nil, condition, ud)
			if err != nil {
				return errors.WithStack(err)
			}

			//oldInfo = ab.Info

		} else {
			_, err := DB.Collection("random_design").InsertOne(nil, randomDesign)
			if err != nil {
				return errors.WithStack(err)
			}

			//oldInfo = models.RandomDesignInfo{}
		}

		OID := newEnvID
		if !newCohortID.IsZero() {
			OID = newCohortID
		}
		_ = insertRandomDesignLog(userOID, OID, 10, oldInfo, randomDesign.Info, randomDesign.ID, "", project, copyEnv)
	}
	return nil
}

// copyVisitCycle 复制访视周期
func copyVisitCycle(userOID primitive.ObjectID, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string) (models.VisitCycle, error) {

	isOldEnvNameProd := false
	oldEnv, b := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == oldEnvID
	})
	if b {
		if oldEnv.Name == "PROD" {
			isOldEnvNameProd = true
		}
	}

	filter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		filter["cohort_id"] = oldCohortID
	}
	var visitCycle models.VisitCycle
	err := DB.Collection("visit_cycle").FindOne(nil, filter).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return visitCycle, errors.WithStack(err)
	}

	if visitCycle.ID != primitive.NilObjectID {
		visitCycle.ID = primitive.NewObjectID()
		visitCycle.EnvironmentID = newEnvID
		if project.ProjectInfo.Type != 1 {
			visitCycle.CohortID = newCohortID
		}
		OID := newEnvID
		if !newCohortID.IsZero() {
			OID = newCohortID
		}
		visitCycle.SetInfo.Id = primitive.NewObjectID()

		err = insertVisitSettingLog(userOID, OID, 10, models.VisitSetting{}, visitCycle.SetInfo, visitCycle.ID, copyEnv)
		if err != nil {
			return visitCycle, errors.WithStack(err)
		}
		visitCycleInfos := make([]models.VisitCycleInfo, 0)
		for _, visitCycleInfo := range visitCycle.ConfigInfo.Infos {
			visitOID := visitCycleInfo.ID
			visitCycleInfo.ID = primitive.NewObjectID()
			err = insertVisitCycleLog(userOID, OID, 10, models.VisitCycleInfo{}, visitCycleInfo, visitCycle.ID, project, copyEnv)
			if err != nil && err != mongo.ErrNoDocuments {
				return visitCycle, errors.WithStack(err)
			}
			isUsed := false
			if visitCycleInfo.Random {
				// 查询是否已经有随机的受试者 已经有随机的不给删除修改
				documents, err := DB.Collection("subject").CountDocuments(nil, bson.M{"group": bson.M{"$ne": ""}, "env_id": oldEnvID, "status": bson.M{"$in": bson.A{3, 4, 5, 6}}})
				if project.ProjectInfo.Type != 1 {
					documents, err = DB.Collection("subject").CountDocuments(nil, bson.M{"group": bson.M{"$ne": ""}, "env_id": oldEnvID, "cohort_id": oldCohortID, "status": bson.M{"$in": bson.A{3, 4, 5, 6}}})
				}
				if err != nil && err != mongo.ErrNoDocuments {
					return visitCycle, errors.WithStack(err)
				}
				if documents > 0 && isOldEnvNameProd {
					isUsed = true
				}
			}
			if visitCycleInfo.Dispensing {
				// 查询是否已经有发药的访视 已经有发药的不给删除修改
				dispensingFilter := bson.M{
					"env_id":                         oldEnvID,
					"visit_info.visit_cycle_info_id": visitOID,
					"status":                         bson.M{"$ne": 1},
					"$or": []bson.M{
						{"dispensing_medicines": bson.M{"$exists": true, "$ne": bson.A{}}},
						{"other_dispensing_medicines": bson.M{"$exists": true, "$ne": bson.A{}}},
					},
				}

				if project.ProjectInfo.Type != 1 {
					dispensingFilter["cohort_id"] = oldCohortID
				}
				documents, err := DB.Collection("dispensing").CountDocuments(nil, dispensingFilter)
				if err != nil && err != mongo.ErrNoDocuments {
					return visitCycle, errors.WithStack(err)
				}
				if documents > 0 && isOldEnvNameProd {
					isUsed = true
					visitCycleInfo.IsCopyEditDelete = true
				}
			}
			visitCycleInfo.IsUsed = isUsed
			visitCycleInfos = append(visitCycleInfos, visitCycleInfo)
		}
		visitCycle.ConfigInfo.Infos = visitCycleInfos
		visitCycle.Infos = visitCycleInfos
		visitCycle.HistoryInfo = nil

		condition := bson.M{"env_id": newEnvID}
		if project.ProjectInfo.Type != 1 {
			condition["cohort_id"] = newCohortID
		}
		if count, _ := DB.Collection("visit_cycle").CountDocuments(nil, condition); count > 0 {

			var newVisitCycle models.VisitCycle
			e := DB.Collection("visit_cycle").FindOne(nil, condition).Decode(&newVisitCycle)
			if e != nil && e != mongo.ErrNoDocuments {
				return visitCycle, errors.WithStack(e)
			}

			visitCycleInfoList := make([]models.VisitCycleInfo, 0)
			if visitCycle.ConfigInfo.Infos != nil && len(visitCycle.ConfigInfo.Infos) > 0 {
				for _, info := range visitCycle.ConfigInfo.Infos {
					find, b := slice.Find(newVisitCycle.ConfigInfo.Infos, func(index int, item models.VisitCycleInfo) bool {
						return item.Number == info.Number
					})
					if b {
						info.ID = find.ID
					}
					visitCycleInfoList = append(visitCycleInfoList, info)
				}
			}
			visitCycle.Infos = visitCycleInfoList
			ud := bson.M{"$set": bson.M{"infos": visitCycleInfoList, "update_infos.infos": visitCycleInfoList}}
			_, err := DB.Collection("visit_cycle").UpdateOne(nil, condition, ud)
			if err != nil && err != mongo.ErrNoDocuments {
				return visitCycle, errors.WithStack(err)
			}

		} else {

			_, err := DB.Collection("visit_cycle").InsertOne(nil, visitCycle)
			if err != nil && err != mongo.ErrNoDocuments {
				return visitCycle, errors.WithStack(err)
			}

		}

	}
	return visitCycle, nil
}

// copyDrugConfig 复制研究产品配置
func copyDrugConfig(userOID primitive.ObjectID, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string, visitCycleInfo models.VisitCycle) (models.DrugConfigure, error) {

	isOldEnvNameProd := false

	oldEnv, b := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == oldEnvID
	})
	if b {
		if oldEnv.Name == "PROD" {
			isOldEnvNameProd = true
		}
	}

	medicineNameList, otherMedicineNameList, err := findOrderMedicine(oldEnvID)
	if err != nil {
		return models.DrugConfigure{}, errors.WithStack(err)
	}

	oldFilter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		oldFilter["cohort_id"] = oldCohortID
	}

	newFilter := bson.M{"env_id": newEnvID}
	if project.ProjectInfo.Type != 1 {
		newFilter["cohort_id"] = newCohortID
	}

	// 查询旧的访视周期
	var oldVisitCycle models.VisitCycle
	_ = DB.Collection("visit_cycle").FindOne(nil, oldFilter).Decode(&oldVisitCycle)

	// 查询新的访视周期
	var newVisitCycle models.VisitCycle
	_ = DB.Collection("visit_cycle").FindOne(nil, newFilter).Decode(&newVisitCycle)

	// 查询旧的研究产品配置
	var drugConfigure models.DrugConfigure
	_ = DB.Collection("drug_configure").FindOne(nil, oldFilter).Decode(&drugConfigure)

	if drugConfigure.ID != primitive.NilObjectID {
		drugConfigure.ID = primitive.NewObjectID()
		drugConfigure.EnvironmentID = newEnvID
		if project.ProjectInfo.Type != 1 {
			drugConfigure.CohortID = newCohortID
		}
		OID := newEnvID
		if !newCohortID.IsZero() {
			OID = newCohortID
		}
		drugConfigureInfos := make([]models.DrugConfigureInfo, 0)
		for _, configureInfo := range drugConfigure.Configures {
			visitCyclesIds := make([]primitive.ObjectID, 0)
			// 根据访视编号匹配访视配置里面的访视周期ID

			for _, oldVisitId := range configureInfo.VisitCycles {
				var oldVisitNumber string
				for _, oldVisit := range oldVisitCycle.Infos {
					if oldVisit.ID == oldVisitId {
						oldVisitNumber = oldVisit.Number
						break
					}
				}

				if visitCycleInfo.Infos != nil && len(visitCycleInfo.Infos) > 0 {
					for _, newVisit := range visitCycleInfo.Infos {
						if newVisit.Number == oldVisitNumber {
							visitCyclesIds = append(visitCyclesIds, newVisit.ID)
							break
						}
					}
				} else {
					for _, newVisit := range newVisitCycle.Infos {
						if newVisit.Number == oldVisitNumber {
							visitCyclesIds = append(visitCyclesIds, newVisit.ID)
							break
						}
					}
				}

				var oldVisitNameZh string
				var oldVisitNameEn string
				if oldVisitCycle.SetInfo.IsOpen {
					if oldVisitCycle.SetInfo.Id == oldVisitId {
						oldVisitNameZh = oldVisitCycle.SetInfo.NameZh
						oldVisitNameEn = oldVisitCycle.SetInfo.NameEn
					}
				}

				if newVisitCycle.SetInfo.IsOpen {
					if newVisitCycle.SetInfo.NameZh == oldVisitNameZh && newVisitCycle.SetInfo.NameEn == oldVisitNameEn {
						visitCyclesIds = append(visitCyclesIds, newVisitCycle.SetInfo.Id)
					}
				}

			}
			if configureInfo.RoutineVisitMappingList != nil && len(configureInfo.RoutineVisitMappingList) > 0 {
				routineVisitMappingList := make([]models.RoutineVisitMapping, 0)
				for _, mapping := range configureInfo.RoutineVisitMappingList {
					mapping.ID = primitive.NewObjectID()
					if mapping.VisitList != nil && len(mapping.VisitList) > 0 {
						mappinfVisitList := make([]primitive.ObjectID, 0)
						// 根据访视编号匹配访视配置里面的访视周期ID
						for _, mappingVisitId := range mapping.VisitList {
							var oldVisitNumber string
							for _, oldVisit := range oldVisitCycle.Infos {
								if oldVisit.ID == mappingVisitId {
									oldVisitNumber = oldVisit.Number
									break
								}
							}

							if visitCycleInfo.Infos != nil && len(visitCycleInfo.Infos) > 0 {
								for _, newVisit := range visitCycleInfo.Infos {
									if newVisit.Number == oldVisitNumber {
										mappinfVisitList = append(mappinfVisitList, newVisit.ID)
										break
									}
								}
							}
						}
						mapping.VisitList = mappinfVisitList
					}
					mappingDrugList := make([]models.Drug, 0)
					if mapping.DrugList != nil && len(mapping.DrugList) > 0 {
						for _, drug := range mapping.DrugList {
							drug.ID = primitive.NewObjectID()
							mappingDrugList = append(mappingDrugList, drug)
						}
					}
					mapping.DrugList = mappingDrugList
					routineVisitMappingList = append(routineVisitMappingList, mapping)
				}
				configureInfo.RoutineVisitMappingList = routineVisitMappingList
			}
			configureInfo.ID = primitive.NewObjectID()
			configureInfo.VisitCycles = visitCyclesIds
			err := insertDrugConfigureLog(userOID, OID, 10, models.DrugConfigureInfo{}, configureInfo, OID, drugConfigure.EnvironmentID, drugConfigure.CohortID, copyEnv, visitCycleInfo)
			if err != nil {
				return drugConfigure, errors.WithStack(err)
			}
			drugConfigureInfos = append(drugConfigureInfos, configureInfo)
		}

		if drugConfigureInfos != nil && len(drugConfigureInfos) > 0 {
			drugConfigureInfoList := make([]models.DrugConfigureInfo, 0)
			for _, info := range drugConfigureInfos {
				isCopyEditDelete := false
				drugConfigureInfo := info
				if info.Values != nil && len(info.Values) > 0 {
					drugValueList := make([]models.DrugValue, 0)
					for _, value := range info.Values {
						drugValue := value
						if value.IsOther {
							//未编号研究产品
							index := arrays.ContainsString(otherMedicineNameList, value.DrugName)
							if index != -1 && isOldEnvNameProd {
								//-1说明不存在
								drugValue.IsCopyEditDelete = true
								isCopyEditDelete = true
							} else {
								drugValue.IsCopyEditDelete = false
							}
						} else {
							//研究产品
							index := arrays.ContainsString(medicineNameList, value.DrugName)
							if index != -1 && isOldEnvNameProd {
								//-1说明不存在
								drugValue.IsCopyEditDelete = true
								isCopyEditDelete = true
							} else {
								drugValue.IsCopyEditDelete = false
							}
						}
						drugValueList = append(drugValueList, drugValue)
					}
					drugConfigureInfo.Values = drugValueList
					drugConfigureInfo.IsCopyEditDelete = isCopyEditDelete
				}
				drugConfigureInfoList = append(drugConfigureInfoList, drugConfigureInfo)
			}
			drugConfigureInfos = drugConfigureInfoList
		}

		drugConfigure.Configures = drugConfigureInfos

		condition := bson.M{"env_id": newEnvID}
		if project.ProjectInfo.Type != 1 {
			condition["cohort_id"] = newCohortID
		}
		if count, _ := DB.Collection("drug_configure").CountDocuments(nil, condition); count > 0 {
			ud := bson.M{"$set": bson.M{"configures": drugConfigure.Configures}}
			_, err := DB.Collection("drug_configure").UpdateOne(nil, condition, ud)
			if err != nil {
				return drugConfigure, errors.WithStack(err)
			}

		} else {

			_, err := DB.Collection("drug_configure").InsertOne(nil, drugConfigure)
			if err != nil {
				return drugConfigure, errors.WithStack(err)
			}

		}

	}
	return drugConfigure, nil
}

// copyDrugPackageConfigure 复制研究产品包装配置
func copyDrugPackageConfigure(userOID primitive.ObjectID, project models.Project, oldEnvID primitive.ObjectID, newEnvID primitive.ObjectID) error {
	filter := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": oldEnvID}
	var oldDrugPackageConfigure models.DrugPackageConfigure
	e := DB.Collection("drug_package_configure").FindOne(nil, filter).Decode(&oldDrugPackageConfigure)
	if e != nil && e != mongo.ErrNoDocuments {
		return errors.WithStack(e)
	}

	drugPackageConfigure := models.DrugPackageConfigure{
		ID:                     primitive.NewObjectID(),
		CustomerID:             oldDrugPackageConfigure.CustomerID,
		ProjectID:              oldDrugPackageConfigure.ProjectID,
		EnvironmentID:          newEnvID,
		IsOpen:                 oldDrugPackageConfigure.IsOpen,
		MixedPackage:           oldDrugPackageConfigure.MixedPackage,
		IsOpenUnProvideDate:    oldDrugPackageConfigure.IsOpenUnProvideDate,
		UnProvideDateConfig:    oldDrugPackageConfigure.UnProvideDateConfig,
		OrderApprovalName:      oldDrugPackageConfigure.OrderApprovalName,
		IsOpenApplication:      oldDrugPackageConfigure.IsOpenApplication,
		SupplyRatio:            oldDrugPackageConfigure.SupplyRatio,
		OrderApplicationConfig: oldDrugPackageConfigure.OrderApplicationConfig,
	}

	condition := bson.M{"env_id": newEnvID}

	if count, _ := DB.Collection("drug_package_configure").CountDocuments(nil, condition); count > 0 {
		updatePackageConfig := bson.M{"$set": bson.M{"is_open": oldDrugPackageConfigure.IsOpen, "mixed_package": oldDrugPackageConfigure.MixedPackage, "is_open_un_provide_date": oldDrugPackageConfigure.IsOpenUnProvideDate,
			"un_provide_date_config": oldDrugPackageConfigure.UnProvideDateConfig, "order_approval_name": oldDrugPackageConfigure.OrderApprovalName,
			"is_open_application": oldDrugPackageConfigure.IsOpenApplication, "is_supply_ratio": oldDrugPackageConfigure.SupplyRatio, "order_application_config": oldDrugPackageConfigure.OrderApplicationConfig}}
		_, err := DB.Collection("drug_package_configure").UpdateOne(nil, condition, updatePackageConfig)
		if err != nil {
			return errors.WithStack(err)
		}

	} else {
		_, err := DB.Collection("drug_package_configure").InsertOne(nil, drugPackageConfigure)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil

}

// copyDrugConfigSetting 复制研究产品配置-设置
func copyDrugConfigSetting(userOID primitive.ObjectID, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, targetForm models.Form, targetDrugConfigure models.DrugConfigure, copyEnv string) error {
	oldFilter := bson.M{"env_id": oldEnvID}
	if project.ProjectInfo.Type != 1 {
		oldFilter["cohort_id"] = oldCohortID
	}

	OID := newEnvID
	if project.ProjectInfo.Type != 1 {
		OID = newCohortID
	}

	// 查询旧的研究产品配置
	var drugConfigureSetting models.DrugConfigureSetting
	err := DB.Collection("drug_configure_setting").FindOne(nil, oldFilter).Decode(&drugConfigureSetting)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	if count, _ := DB.Collection("drug_configure_setting").CountDocuments(nil, oldFilter); count == 0 {
		return nil
	}

	var newDrugConfigureSetting models.DrugConfigureSetting
	newDrugConfigureSetting.ID = primitive.NewObjectID()
	newDrugConfigureSetting.CustomerID = drugConfigureSetting.CustomerID
	newDrugConfigureSetting.ProjectID = drugConfigureSetting.ProjectID
	newDrugConfigureSetting.EnvironmentID = newEnvID
	newDrugConfigureSetting.CohortID = newCohortID
	newDrugConfigureSetting.IsOpen = drugConfigureSetting.IsOpen
	newDrugConfigureSetting.SelectType = drugConfigureSetting.SelectType
	newDrugConfigureSetting.DtpIpList = drugConfigureSetting.DtpIpList

	match := bson.M{
		"project_id":  drugConfigureSetting.ProjectID,
		"env_id":      oldEnvID,
		"customer_id": drugConfigureSetting.CustomerID,
	}
	if project.ProjectInfo.Type != 1 {
		match["cohort_id"] = oldCohortID
	}

	var form models.Form
	if err := DB.Collection("form").FindOne(nil, match).Decode(&form); err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	doseFormName := ""
	if form.Fields != nil && len(form.Fields) > 0 {
		for _, field := range form.Fields {
			if field.ID.Hex() == drugConfigureSetting.DoseFormId {
				doseFormName = field.Variable
			}
		}
	}

	targetDoseFormId := ""
	filter := bson.M{
		"project_id":  drugConfigureSetting.ProjectID,
		"env_id":      newEnvID,
		"customer_id": drugConfigureSetting.CustomerID,
	}
	if project.ProjectInfo.Type != 1 {
		filter["cohort_id"] = newCohortID
	}
	//var targetForm models.Form
	//if err := DB.Collection("form").FindOne(ctx, filter).Decode(&targetForm); err != nil && err != mongo.ErrNoDocuments {
	//	return errors.WithStack(err)
	//}
	if targetForm.Fields != nil && len(targetForm.Fields) > 0 {
		for _, field := range targetForm.Fields {
			if field.Variable == doseFormName {
				targetDoseFormId = field.ID.Hex()
			}
		}
	}
	newDrugConfigureSetting.DoseFormId = targetDoseFormId

	newDrugConfigureSetting.IsFirstInitial = drugConfigureSetting.IsFirstInitial
	newDrugConfigureSetting.IsDoseReduction = drugConfigureSetting.IsDoseReduction
	newDrugConfigureSetting.Frequency = drugConfigureSetting.Frequency

	//var targetDrugConfigure models.DrugConfigure
	//err := DB.Collection("drug_configure").FindOne(nil, filter).Decode(&targetDrugConfigure)
	//if err != nil && err != mongo.ErrNoDocuments {
	//	return errors.WithStack(err)
	//}

	targetDoseLevelList := make([]models.DoseLevel, 0)
	if drugConfigureSetting.DoseLevelList != nil && len(drugConfigureSetting.DoseLevelList) > 0 {
		for _, doseLevel := range drugConfigureSetting.DoseLevelList {
			var targetDoseLevel models.DoseLevel
			targetDoseLevel.ID = primitive.NewObjectID()
			targetDoseLevel.Name = doseLevel.Name
			targetDoseLevel.Group = doseLevel.Group
			targetDoseDistribution := make([]string, 0)
			if doseLevel.DoseDistribution != nil && len(doseLevel.DoseDistribution) > 0 {
				for _, doseDistribution := range doseLevel.DoseDistribution {
					var jsonObject map[string]interface{}
					json.Unmarshal([]byte(doseDistribution), &jsonObject)
					//fmt.Println(jsonObject)
					name := jsonObject["name"].(string)
					if targetDrugConfigure.Configures != nil && len(targetDrugConfigure.Configures) > 0 {
						for _, configure := range targetDrugConfigure.Configures {
							if doseLevel.Group != nil && len(doseLevel.Group) > 0 {
								index := arrays.ContainsString(doseLevel.Group, configure.Group)
								if index != -1 {
									if configure.OpenSetting == 1 {
										//按标签配置
										if len(configure.Label) > 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label
												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													if len(drugValue.Label) != 0 {
														if name == drugValue.Label {
															var doseLabel models.DoseLabel
															doseLabel.ID = configure.ID
															doseLabel.Name = drugValue.Label
															jsonString, e := json.Marshal(doseLabel)
															if e != nil {
																return errors.WithStack(e)
															}
															whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
															if whether == -1 {
																targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
															}
														}
													}
												}
											}
										}
									} else if configure.OpenSetting == 2 {
										//开放配置
										if len(configure.Label) != 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label

												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													drug := ""
													if len(drugValue.DrugSpec) != 0 {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/" + drugValue.DrugSpec
													} else {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/"
													}

													if name == drug {
														var doseLabel models.DoseLabel
														doseLabel.ID = configure.ID
														doseLabel.Name = drug

														jsonString, e := json.Marshal(doseLabel)
														if e != nil {
															return errors.WithStack(e)
														}
														whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
														if whether == -1 {
															targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
														}
													}
												}
											}
										}

									}
								}
							}
						}
					}
				}
			}
			targetDoseLevel.DoseDistribution = targetDoseDistribution
			targetDoseLevel.InitialDose = doseLevel.InitialDose
			targetDoseLevel.IsUsed = false
			targetDoseLevelList = append(targetDoseLevelList, targetDoseLevel)
		}
	}
	newDrugConfigureSetting.DoseLevelList = targetDoseLevelList

	targetVisitJudgmentList := make([]models.VisitJudgment, 0)
	if drugConfigureSetting.VisitJudgmentList != nil && len(drugConfigureSetting.VisitJudgmentList) > 0 {
		for _, visitJudgment := range drugConfigureSetting.VisitJudgmentList {
			var targetVisitJudgment models.VisitJudgment
			targetVisitJudgment.ID = primitive.NewObjectID()
			targetVisitJudgment.Name = visitJudgment.Name
			targetVisitJudgment.Group = visitJudgment.Group
			targetDoseDistribution := make([]string, 0)
			if visitJudgment.DoseDistribution != nil && len(visitJudgment.DoseDistribution) > 0 {
				for _, doseDistribution := range visitJudgment.DoseDistribution {
					var jsonObject map[string]interface{}
					json.Unmarshal([]byte(doseDistribution), &jsonObject)
					//fmt.Println(jsonObject)
					name := jsonObject["name"].(string)
					if targetDrugConfigure.Configures != nil && len(targetDrugConfigure.Configures) > 0 {
						for _, configure := range targetDrugConfigure.Configures {
							if visitJudgment.Group != nil && len(visitJudgment.Group) > 0 {
								index := arrays.ContainsString(visitJudgment.Group, configure.Group)
								if index != -1 {
									if configure.OpenSetting == 1 {
										//按标签配置
										if len(configure.Label) > 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label
												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													if len(drugValue.Label) != 0 {
														if name == drugValue.Label {
															var doseLabel models.DoseLabel
															doseLabel.ID = configure.ID
															doseLabel.Name = drugValue.Label
															jsonString, e := json.Marshal(doseLabel)
															if e != nil {
																return errors.WithStack(e)
															}
															whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
															if whether == -1 {
																targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
															}
														}
													}
												}
											}
										}
									} else if configure.OpenSetting == 2 {
										//开放配置
										if len(configure.Label) != 0 {
											if name == configure.Label {
												var doseLabel models.DoseLabel
												doseLabel.ID = configure.ID
												doseLabel.Name = configure.Label

												jsonString, e := json.Marshal(doseLabel)
												if e != nil {
													return errors.WithStack(e)
												}
												whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
												if whether == -1 {
													targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
												}
											}
										} else {
											if configure.Values != nil && len(configure.Values) != 0 {
												for _, drugValue := range configure.Values {
													drug := ""
													if len(drugValue.DrugSpec) != 0 {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/" + drugValue.DrugSpec
													} else {
														drug = drugValue.DrugName + "/" + drugValue.CustomDispensingNumber + "/"
													}

													if name == drug {
														var doseLabel models.DoseLabel
														doseLabel.ID = configure.ID
														doseLabel.Name = drug

														jsonString, e := json.Marshal(doseLabel)
														if e != nil {
															return errors.WithStack(e)
														}
														whether := arrays.ContainsString(targetDoseDistribution, string(jsonString))
														if whether == -1 {
															targetDoseDistribution = append(targetDoseDistribution, string(jsonString))
														}
													}
												}
											}
										}

									}
								}
							}
						}
					}
				}
			}
			targetVisitJudgment.DoseDistribution = targetDoseDistribution
			targetVisitJudgment.VisitInheritance = visitJudgment.VisitInheritance
			targetVisitJudgment.IsUsed = false
			targetVisitJudgmentList = append(targetVisitJudgmentList, targetVisitJudgment)
		}
	}
	newDrugConfigureSetting.VisitJudgmentList = targetVisitJudgmentList

	logOID := newDrugConfigureSetting.EnvironmentID
	if newDrugConfigureSetting.CohortID != primitive.NilObjectID {
		logOID = newDrugConfigureSetting.CohortID
	}

	condition := bson.M{"env_id": newEnvID}
	if project.ProjectInfo.Type != 1 {
		condition["cohort_id"] = newCohortID
	}
	if count, _ := DB.Collection("drug_configure_setting").CountDocuments(nil, condition); count > 0 {

		edit := bson.M{"is_open": newDrugConfigureSetting.IsOpen, "select_type": newDrugConfigureSetting.SelectType}

		edit["dose_form_id"] = newDrugConfigureSetting.DoseFormId
		edit["is_first_initial"] = newDrugConfigureSetting.IsFirstInitial
		edit["is_dose_reduction"] = newDrugConfigureSetting.IsDoseReduction
		edit["frequency"] = newDrugConfigureSetting.Frequency
		edit["dose_level_list"] = newDrugConfigureSetting.DoseLevelList
		edit["visit_judgment_list"] = newDrugConfigureSetting.VisitJudgmentList
		edit["form_record_list"] = newDrugConfigureSetting.FormRecordList

		change := bson.M{"$set": edit}
		_, err := DB.Collection("drug_configure_setting").UpdateOne(nil, condition, change)
		if err != nil {
			return errors.WithStack(err)
		}
		err = insertDrugConfigureSettingLog(userOID, logOID, 10, models.DrugConfigureSetting{}, newDrugConfigureSetting, OID, newEnvID, newCohortID, copyEnv)
		if err != nil {
			return errors.WithStack(err)
		}

	} else {

		_, err := DB.Collection("drug_configure_setting").InsertOne(nil, newDrugConfigureSetting)
		if err != nil {
			return errors.WithStack(err)
		}
		err = insertDrugConfigureSettingLog(userOID, logOID, 10, models.DrugConfigureSetting{}, newDrugConfigureSetting, OID, newEnvID, newCohortID, copyEnv)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

// copyCodeRule 复制编码配置
func copyCodeRule(userOID primitive.ObjectID, project models.Project, oldEnvID primitive.ObjectID, oldCohortID primitive.ObjectID, newEnvID primitive.ObjectID, newCohortID primitive.ObjectID, copyEnv string) error {
	filter := bson.M{"customer_id": project.CustomerID, "project_id": project.ID, "env_id": oldEnvID}
	if oldCohortID != primitive.NilObjectID {
		filter["cohort_id"] = oldCohortID
	}
	var oldBarcodeRule models.BarcodeRule
	e := DB.Collection("barcode_rule").FindOne(nil, filter).Decode(&oldBarcodeRule)
	if e != nil && e != mongo.ErrNoDocuments {
		return errors.WithStack(e)
	}

	barcodeRule := models.BarcodeRule{
		ID:             primitive.NewObjectID(),
		CustomerID:     project.CustomerID,
		ProjectID:      project.ID,
		EnvironmentID:  newEnvID,
		CohortID:       newCohortID,
		CodeRule:       oldBarcodeRule.CodeRule,
		CodeConfigInit: oldBarcodeRule.CodeConfigInit,
	}

	condition := bson.M{"env_id": newEnvID}
	if project.ProjectInfo.Type != 1 {
		condition["cohort_id"] = newCohortID
	}
	if count, _ := DB.Collection("barcode_rule").CountDocuments(nil, condition); count > 0 {

		ud := bson.M{"$set": bson.M{"code_rule": barcodeRule.CodeRule, "code_config_init": barcodeRule.CodeConfigInit}}
		_, err := DB.Collection("barcode_rule").UpdateOne(nil, condition, ud)
		if err != nil {
			return errors.WithStack(err)
		}

	} else {
		_, err := DB.Collection("barcode_rule").InsertOne(nil, barcodeRule)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	if barcodeRule.CodeConfigInit {
		// 保存项目日志
		oldField := models.OperationLogField{
			Type:  6,
			Value: "",
		}
		var OperationLogFieldGroups []models.OperationLogFieldGroup
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "barcode.edit",
				TranKey: "operation_log.barcode.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}
		tranKey := "operation_log.barcode.random"
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "barcode.edit",
			TranKey: tranKey,
			Old:     oldField,
			New: models.OperationLogField{
				Type:  6,
				Value: barcodeRule.CodeRule,
			},
		})
		OID := barcodeRule.EnvironmentID
		if !barcodeRule.CohortID.IsZero() {
			OID = barcodeRule.CohortID
		}
		err := SaveOperation(userOID, "operation_log.module.barcode", OID, 10, OperationLogFieldGroups, []models.Mark{}, barcodeRule.ID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil

}

// insertNotificationsLog 项目通知日志
func insertNotificationsLog(userOID primitive.ObjectID, key string, OID primitive.ObjectID, types int, old models.NoticeConfig, new models.NoticeConfig, copyEnv string) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {
		//基本设置
		if key == "notice.basic.settings" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.basic.settings",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//邮件语言-自动任务
			oldAutomaticZh := ""
			oldAutomaticEn := ""
			if &old.Automatic != nil && old.Automatic != 0 {
				if old.Automatic == 1 {
					oldAutomaticZh = "中文"
					oldAutomaticEn = "Chinese"
				} else if old.Automatic == 2 {
					oldAutomaticZh = "英文"
					oldAutomaticEn = "English"
				} else if old.Automatic == 3 {
					oldAutomaticZh = "中英文"
					oldAutomaticEn = "Chinese And English"
				}
			}
			newAutomaticZh := ""
			newAutomaticEn := ""
			if &new.Automatic != nil && old.Automatic != 0 {
				if new.Automatic == 1 {
					newAutomaticZh = "中文"
					newAutomaticEn = "Chinese"
				} else if new.Automatic == 2 {
					newAutomaticZh = "英文"
					newAutomaticEn = "English"
				} else if new.Automatic == 3 {
					newAutomaticZh = "中英文"
					newAutomaticEn = "Chinese And English"
				}
			}
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.basic.settings",
				TranKey: "notifications.notice.basic.settings.emailLanguage_automatedTasks",
				Old: models.OperationLogField{
					Type:    30,
					Value:   oldAutomaticZh,
					ENValue: oldAutomaticEn,
				},
				New: models.OperationLogField{
					Type:    30,
					Value:   newAutomaticZh,
					ENValue: newAutomaticEn,
				},
			})
			//邮件语言-手动任务
			oldManualZh := ""
			oldManualEn := ""
			if &old.Manual != nil && old.Manual != 0 {
				if old.Manual == 1 {
					oldManualZh = "中文"
					oldManualEn = "Chinese"
				} else if old.Manual == 2 {
					oldManualZh = "英文"
					oldManualEn = "English"
				} else if old.Manual == 3 {
					oldManualZh = "中英文"
					oldManualEn = "Chinese And English"
				}
			}
			newManualZh := ""
			newManualEn := ""
			if &new.Manual != nil && new.Manual != 0 {
				if new.Manual == 1 {
					newManualZh = "中文"
					newManualEn = "Chinese"
				} else if new.Manual == 2 {
					newManualZh = "英文"
					newManualEn = "English"
				} else if new.Manual == 3 {
					newManualZh = "中英文"
					newManualEn = "Chinese And English"
				}
			}
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.basic.settings",
				TranKey: "notifications.notice.basic.settings.emailLanguage_manualTasks",
				Old: models.OperationLogField{
					Type:    30,
					Value:   oldManualZh,
					ENValue: oldManualEn,
				},
				New: models.OperationLogField{
					Type:    30,
					Value:   newManualZh,
					ENValue: newManualEn,
				},
			})
		}
		//受试者登记
		if key == "notice.subject.add" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.add",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.add",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.add",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//受试者随机
		if key == "notice.subject.random" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.random",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.random",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.random",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//受试者停用
		if key == "notice.subject.signOut" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.signOut",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.signOut",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.signOut",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//受试者替换
		if key == "notice.subject.replace" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.replace",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.replace",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.replace",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//受试者修改
		if key == "notice.subject.update" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.update",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.update",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//场景
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.update",
				TranKey: "notifications.dispensing.scene",
				Old: models.OperationLogField{
					Type:  25,
					Value: old.State,
				},
				New: models.OperationLogField{
					Type:  25,
					Value: new.State,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.update",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//受试者筛选
		if key == "notice.subject.screen" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.screen",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.screen",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//场景
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.screen",
				TranKey: "notifications.dispensing.scene",
				Old: models.OperationLogField{
					Type:  24,
					Value: old.State,
				},
				New: models.OperationLogField{
					Type:  24,
					Value: new.State,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.screen",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//受试者发放
		if key == "notice.subject.dispensing" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.dispensing",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.dispensing",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//场景
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.dispensing",
				TranKey: "notifications.dispensing.scene",
				Old: models.OperationLogField{
					Type:  18,
					Value: old.State,
				},
				New: models.OperationLogField{
					Type:  18,
					Value: new.State,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.dispensing",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//受试者警戒
		if key == "notice.subject.alarm" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.alarm",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.alarm",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.alarm",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//紧急揭盲
		if key == "notice.subject.unblinding" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.unblinding",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.unblinding",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.unblinding",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//研究产品隔离
		if key == "notice.medicine.isolation" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.isolation",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.medicine.isolation",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//场景
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.medicine.isolation",
				TranKey: "notifications.isolation.scene",
				Old: models.OperationLogField{
					Type:  19,
					Value: old.State,
				},
				New: models.OperationLogField{
					Type:  19,
					Value: new.State,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.medicine.isolation",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//研究产品订单
		if key == "notice.medicine.order" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.order",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.medicine.order",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//场景
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.medicine.order",
				TranKey: "notifications.order.scene",
				Old: models.OperationLogField{
					Type:  20,
					Value: old.State,
				},
				New: models.OperationLogField{
					Type:  20,
					Value: new.State,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.medicine.order",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//研究产品有效期
		if key == "notice.medicine.reminder" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.reminder",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.medicine.reminder",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//研究产品警戒
		if key == "notice.medicine.alarm" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.medicine.alarm",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.medicine.alarm",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.medicine.alarm",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//库房警戒
		if key == "notice.storehouse.alarm" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.storehouse.alarm",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.storehouse.alarm",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.storehouse.alarm",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//受试者上限设置提醒
		if key == "notice.subject.alert.threshold" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.subject.alert.threshold",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.alert.threshold",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.subject.alert.threshold",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
		}
		//订单超时
		if key == "notice.order.timeout" {
			if copyEnv != "" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "notifications.notice.order.timeout",
					TranKey: "notifications.notice.env_name",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: copyEnv,
					},
				})
			}
			//内容配置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.order.timeout",
				TranKey: "notifications.dispensing.contentConfiguration",
				Old: models.OperationLogField{
					Type:  17,
					Value: old.FieldsConfig,
				},
				New: models.OperationLogField{
					Type:  17,
					Value: new.FieldsConfig,
				},
			})
			//角色
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.order.timeout",
				TranKey: "notifications.roles",
				Old: models.OperationLogField{
					Type:  16,
					Value: old.Roles,
				},
				New: models.OperationLogField{
					Type:  16,
					Value: new.Roles,
				},
			})
			//订单超时天数设置
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.order.timeout",
				TranKey: "notifications.notice.order.timeout.lateShipmentAlertSetting",
				Old: models.OperationLogField{
					Type:  21,
					Value: old.TimeoutDays,
				},
				New: models.OperationLogField{
					Type:  21,
					Value: new.TimeoutDays,
				},
			})
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "notifications.notice.order.timeout",
				TranKey: "notifications.notice.order.timeout.lateShipmentSendAlertSetting",
				Old: models.OperationLogField{
					Type:  21,
					Value: old.SendDays,
				},
				New: models.OperationLogField{
					Type:  21,
					Value: new.SendDays,
				},
			})
		}
	}
	marks := []models.Mark{}
	err := SaveOperation(userOID, "operation_log.module.notifications", OID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// insertAttributeLog 属性配置日志
func insertAttributeLog(userOID primitive.ObjectID, OID primitive.ObjectID, types int, old models.AttributeInfo, new models.AttributeInfo, oldSegmentLength string, segmentLength string, copyEnv string) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.attribute",
				TranKey: "operation_log.attribute.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.random",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Random,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.Random,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.isRandomNumber",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.IsRandomNumber,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.IsRandomNumber,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.dispensing",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Dispensing,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.Dispensing,
			},
		})
		oldDtpRule := ""
		oldDtpRuleEn := ""
		newDtpRule := ""
		newDtpRuleEn := ""
		if new.Dispensing {
			if old.DtpRule != 0 {
				if old.DtpRule == 1 {
					oldDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_ip")
					oldDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_ip")
				} else if old.DtpRule == 2 {
					oldDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_visitFlow")
					oldDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_visitFlow")
				} else if old.DtpRule == 3 {
					oldDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_notApplicable")
					oldDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_notApplicable")
				}
			}
			if new.DtpRule != 0 {
				if new.DtpRule == 1 {
					newDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_ip")
					newDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_ip")
				} else if new.DtpRule == 2 {
					newDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_visitFlow")
					newDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_visitFlow")
				} else if new.DtpRule == 3 {
					newDtpRule = locales.TrWithLang("zh", "operation_log.attribute.dtpRule_notApplicable")
					newDtpRuleEn = locales.TrWithLang("en", "operation_log.attribute.dtpRule_notApplicable")
				}
			}
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.dtpRule",
			Old: models.OperationLogField{
				Type:    30,
				Value:   oldDtpRule,
				ENValue: oldDtpRuleEn,
			},
			New: models.OperationLogField{
				Type:    30,
				Value:   newDtpRule,
				ENValue: newDtpRuleEn,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.randomControl",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.RandomControl,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.RandomControl,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.randomControlRule",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.RandomControlRule,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.RandomControlRule,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.segmentType",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.SegmentType,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.SegmentType,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.allowRegisterGroup",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.AllowRegisterGroup,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.AllowRegisterGroup,
			},
		})

		if new.RandomControl && new.RandomControlRule == 3 { //强制随机到有供应的分组
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.attribute",
				TranKey: "operation_log.attribute.randomControl3_info",
				Old: models.OperationLogField{
					Type:  1,
					Value: old.RandomControlGroup,
				},
				New: models.OperationLogField{
					Type:  1,
					Value: new.RandomControlGroup,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.blind",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Blind,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.Blind,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.subject_number_rule",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.SubjectNumberRule,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.SubjectNumberRule,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.screen",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.IsScreen,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.IsScreen,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.minimize_calc",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.MinimizeCalc,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.MinimizeCalc,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.prefix",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Prefix,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.Prefix,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.prefix_number",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.PrefixExpression,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.PrefixExpression,
			},
		})
		oldAllowReplace := "1"
		if old.AllowReplace {
			oldAllowReplace = "2"
		}
		newAllowReplace := "1"
		if new.AllowReplace {
			newAllowReplace = "2"
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.allowReplace",
			Old: models.OperationLogField{
				Type:  6,
				Value: oldAllowReplace,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: newAllowReplace,
			},
		})
		//OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		//	Key:     "operation_log.attribute",
		//	TranKey: "operation_log.attribute.sitePrefix",
		//	Old: models.OperationLogField{
		//		Type:  4,
		//		Value: old.SitePrefix,
		//	},
		//	New: models.OperationLogField{
		//		Type:  4,
		//		Value: new.SitePrefix,
		//	},
		//})
		//OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		//	Key:     "operation_log.attribute",
		//	TranKey: "operation_log.attribute.prefixConnector",
		//	Old: models.OperationLogField{
		//		Type:  2,
		//		Value: old.PrefixConnector,
		//	},
		//	New: models.OperationLogField{
		//		Type:  2,
		//		Value: new.PrefixConnector,
		//	},
		//})
		//OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		//	Key:     "operation_log.attribute",
		//	TranKey: "operation_log.attribute.otherPrefix",
		//	Old: models.OperationLogField{
		//		Type:  4,
		//		Value: old.OtherPrefix,
		//	},
		//	New: models.OperationLogField{
		//		Type:  4,
		//		Value: new.OtherPrefix,
		//	},
		//})
		//OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		//	Key:     "operation_log.attribute",
		//	TranKey: "operation_log.attribute.otherPrefixText",
		//	Old: models.OperationLogField{
		//		Type:  2,
		//		Value: old.OtherPrefixText,
		//	},
		//	New: models.OperationLogField{
		//		Type:  2,
		//		Value: new.OtherPrefixText,
		//	},
		//})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.subjectReplaceText",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.SubjectReplaceText,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.SubjectReplaceText,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.subjectReplaceTextEn",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.SubjectReplaceTextEn,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.SubjectReplaceTextEn,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.accuracy",
			Old: models.OperationLogField{
				Type:  6,
				Value: old.Accuracy,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new.Accuracy,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.digit",
			Old: models.OperationLogField{
				Type:  1,
				Value: old.Digit,
			},
			New: models.OperationLogField{
				Type:  1,
				Value: new.Digit,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.isFreeze",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.IsFreeze,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.IsFreeze,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.replaceRule",
			Old: models.OperationLogField{
				Type:  6,
				Value: convertor.ToString(old.ReplaceRule),
			},
			New: models.OperationLogField{
				Type:  6,
				Value: convertor.ToString(new.ReplaceRule),
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.ReplaceRuleNumber",
			Old: models.OperationLogField{
				Type:  1,
				Value: old.ReplaceRuleNumber,
			},
			New: models.OperationLogField{
				Type:  1,
				Value: new.ReplaceRuleNumber,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.blindingRestrictions",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.UnBlindingRestrictions,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.UnBlindingRestrictions,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.pvBlindingRestrictions",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.PvUnBlindingRestrictions,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.PvUnBlindingRestrictions,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.edcDrugConfigLabel",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.EdcDrugConfigLabel,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.EdcDrugConfigLabel,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.segment",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.Segment,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.Segment,
			},
		})
		if len(segmentLength) > 0 {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.attribute",
				TranKey: "operation_log.attribute.segmentLength",
				Old: models.OperationLogField{
					Type:  7,
					Value: oldSegmentLength,
				},
				New: models.OperationLogField{
					Type:  7,
					Value: segmentLength,
				},
			})
		}
		oldUnblindingReason := make([]models.UnblindingReasonConfigOperationLog, 0)
		for _, config := range old.UnblindingReasonConfig {
			key := ""
			if config.AllowRemark {
				key = "operation_log.attribute.unblindingAllowTrue"
			} else {
				key = "operation_log.attribute.unblindingAllowFalse"
			}
			oldUnblindingReason = append(oldUnblindingReason, models.UnblindingReasonConfigOperationLog{
				Reason:             config.Reason,
				AllowRemarkTranKey: key,
			})
		}
		newUnblindingReason := make([]models.UnblindingReasonConfigOperationLog, 0)
		for _, config := range new.UnblindingReasonConfig {
			key := ""
			if config.AllowRemark {
				key = "operation_log.attribute.unblindingAllowTrue"
			} else {
				key = "operation_log.attribute.unblindingAllowFalse"
			}
			newUnblindingReason = append(newUnblindingReason, models.UnblindingReasonConfigOperationLog{
				Reason:             config.Reason,
				AllowRemarkTranKey: key,
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.attribute",
			TranKey: "operation_log.attribute.unblindingReason",
			Old: models.OperationLogField{
				Type:  10,
				Value: oldUnblindingReason,
			},
			New: models.OperationLogField{
				Type:  10,
				Value: newUnblindingReason,
			},
		})

	}
	marks := []models.Mark{}
	err := SaveOperation(userOID, "operation_log.module.attribute", OID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// insertFormLog 表单日志
func insertFormLog(userOID primitive.ObjectID, envOID primitive.ObjectID, types int, oldField models.Field, newField models.Field, OID primitive.ObjectID, copyEnv string) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	if types != 3 {
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design.form",
				TranKey: "operation_log.form.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design.form",
			TranKey: "operation_log.form.applicationType",
			Old: models.OperationLogField{
				Type:  6,
				Value: oldField.ApplicationType,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: newField.ApplicationType,
			},
		})
		if types == 1 || types == 10 {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design.form",
				TranKey: "operation_log.form.name",
				Old: models.OperationLogField{
					Type:  1,
					Value: oldField.Label,
				},
				New: models.OperationLogField{
					Type:  1,
					Value: newField.Label,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design.form",
			TranKey: "operation_log.form.variable",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldField.Variable,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newField.Variable,
			},
		})
		// 公式计算 不显示
		if newField.ApplicationType != nil && *newField.ApplicationType != 2 {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design.form",
				TranKey: "operation_log.form.editable",
				Old: models.OperationLogField{
					Type:  4,
					Value: oldField.Modifiable,
				},
				New: models.OperationLogField{
					Type:  4,
					Value: newField.Modifiable,
				},
			})
		}

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design.form",
			TranKey: "operation_log.form.required",
			Old: models.OperationLogField{
				Type:  4,
				Value: oldField.Required,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: newField.Required,
			},
		})

		//if types == 2 {
		//	if newField.Type == "radio" || newField.Type == "checkbox" || newField.Type == "select" {
		//		oldOption := []string{}
		//		for _, option := range oldField.Options {
		//			oldOption = append(oldOption, option.Label)
		//		}
		//		newOption := []string{}
		//		for _, option := range newField.Options {
		//			newOption = append(newOption, option.Label)
		//		}
		//		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		//			Key:     "operation_log.random_design.form",
		//			TranKey: "operation_log.form.options",
		//			Old: models.OperationLogField{
		//				Type:  1,
		//				Value: strings.Join(oldOption, ","),
		//			},
		//			New: models.OperationLogField{
		//				Type:  1,
		//				Value: strings.Join(newOption, ","),
		//			},
		//		})
		//	}
		//}

		if types == 1 || types == 2 || types == 10 || types == 11 {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design.form",
				TranKey: "operation_log.form.type",
				Old: models.OperationLogField{
					Type:  6,
					Value: oldField.Type,
				},
				New: models.OperationLogField{
					Type:  6,
					Value: newField.Type,
				},
			})

			if newField.Type == "input" || newField.Type == "textArea" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.format",
					Old: models.OperationLogField{
						Type:  1,
						Value: locales.TrWithLang("zh", "operation_log.form.length"),
					},
					New: models.OperationLogField{
						Type:  1,
						Value: locales.TrWithLang("zh", "operation_log.form.length"),
					},
				})

				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.variableFormat",
					Old: models.OperationLogField{
						Type:  1,
						Value: oldField.Length,
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newField.Length,
					},
				})

			} else if newField.Type == "inputNumber" {
				oldFormatTypeName := "form.control.type.format.numberLength"
				newFormatTypeName := "form.control.type.format.numberLength"
				if newField.FormatType == "decimalLength" {
					oldFormatTypeName = "form.control.type.format.decimalLength"
					newFormatTypeName = "form.control.type.format.decimalLength"
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.format",
					Old: models.OperationLogField{
						Type:  1,
						Value: oldFormatTypeName,
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newFormatTypeName,
					},
				})

				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.variableFormat",
					Old: models.OperationLogField{
						Type:  1,
						Value: oldField.Length,
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newField.Length,
					},
				})

				max := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.max",
				}
				min := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.min",
				}
				if newField.Range != nil {
					max.New = models.OperationLogField{
						Type:  1,
						Value: newField.Range.Max,
					}
					min.New = models.OperationLogField{
						Type:  1,
						Value: newField.Range.Min,
					}
				} else {
					max.New = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
					min.New = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
				}
				if oldField.Range != nil {
					max.Old = models.OperationLogField{
						Type:  1,
						Value: oldField.Range.Max,
					}
					min.Old = models.OperationLogField{
						Type:  1,
						Value: oldField.Range.Min,
					}
				} else {
					max.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
					min.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, max)
				OperationLogFieldGroups = append(OperationLogFieldGroups, min)
			} else if newField.Type == "datePicker" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.format",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldField.DateFormat,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newField.DateFormat,
					},
				})

				max := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.max",
				}
				min := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.min",
				}
				if newField.DateRange != nil {
					maxStr := newField.DateRange.Max
					maxStrEn := newField.DateRange.Max
					if newField.DateRange.Max == "currentTime" {
						maxStr = locales.TrWithLang("zh", "operation_log.form.currentTime")
						maxStrEn = locales.TrWithLang("en", "operation_log.form.currentTime")
					}
					max.New = models.OperationLogField{
						Type:    30,
						Value:   maxStr,
						ENValue: maxStrEn,
					}
					min.New = models.OperationLogField{
						Type:  2,
						Value: newField.DateRange.Min,
					}
				} else {
					max.New = models.OperationLogField{
						Type:  2,
						Value: nil,
					}
					min.New = models.OperationLogField{
						Type:  2,
						Value: nil,
					}
				}
				if oldField.DateRange != nil {
					oldMaxStr := oldField.DateRange.Max
					oldMaxStrEn := oldField.DateRange.Max
					if oldField.DateRange.Max == "currentTime" {
						oldMaxStr = locales.TrWithLang("zh", "operation_log.form.currentTime")
						oldMaxStrEn = locales.TrWithLang("en", "operation_log.form.currentTime")
					}
					max.Old = models.OperationLogField{
						Type:    30,
						Value:   oldMaxStr,
						ENValue: oldMaxStrEn,
					}
					min.Old = models.OperationLogField{
						Type:  2,
						Value: oldField.DateRange.Min,
					}
				} else {
					max.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
					min.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, max)
				OperationLogFieldGroups = append(OperationLogFieldGroups, min)
			} else if newField.Type == "timePicker" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.format",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldField.TimeFormat,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newField.TimeFormat,
					},
				})
				max := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.max",
				}
				min := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.min",
					New: models.OperationLogField{
						Type:  2,
						Value: newField.DateRange.Min,
					},
				}
				if newField.DateRange != nil {
					maxStr := newField.DateRange.Max
					maxStrEn := newField.DateRange.Max
					if newField.DateRange.Max == "currentTime" {
						maxStr = locales.TrWithLang("zh", "operation_log.form.currentTime")
						maxStrEn = locales.TrWithLang("en", "operation_log.form.currentTime")
					}
					max.New = models.OperationLogField{
						Type:    30,
						Value:   maxStr,
						ENValue: maxStrEn,
					}
					min.New = models.OperationLogField{
						Type:  2,
						Value: newField.DateRange.Min,
					}
				} else {
					max.New = models.OperationLogField{
						Type:  2,
						Value: nil,
					}
					min.New = models.OperationLogField{
						Type:  2,
						Value: nil,
					}
				}
				if oldField.DateRange != nil {
					oldMaxStr := oldField.DateRange.Max
					oldMaxStrEn := oldField.DateRange.Max
					if oldField.DateRange.Max == "currentTime" {
						oldMaxStr = locales.TrWithLang("zh", "operation_log.form.currentTime")
						oldMaxStrEn = locales.TrWithLang("en", "operation_log.form.currentTime")
					}
					max.Old = models.OperationLogField{
						Type:    30,
						Value:   oldMaxStr,
						ENValue: oldMaxStrEn,
					}
					min.Old = models.OperationLogField{
						Type:  2,
						Value: oldField.DateRange.Min,
					}
				} else {
					max.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
					min.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, max)
				OperationLogFieldGroups = append(OperationLogFieldGroups, min)
			}

			if newField.Type == "radio" || newField.Type == "checkbox" || newField.Type == "select" {
				oldOption := []string{}
				for _, option := range oldField.Options {
					oldOption = append(oldOption, option.Label)
				}
				newOption := []string{}
				for _, option := range newField.Options {
					newOption = append(newOption, option.Label)
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.options",
					Old: models.OperationLogField{
						Type:  1,
						Value: strings.Join(oldOption, ","),
					},
					New: models.OperationLogField{
						Type:  1,
						Value: strings.Join(newOption, ","),
					},
				})
			}
		}
		if oldField.Status == nil {
			status := 1
			oldField.Status = &status
		}
		if newField.Status == nil {
			status := 1
			newField.Status = &status
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design.form",
			TranKey: "operation_log.form.status",
			Old: models.OperationLogField{
				Type:  6,
				Value: oldField.Status,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: newField.Status,
			},
		})
	}
	marks := []models.Mark{}
	if types == 1 || types == 10 {
		marks = append(marks, models.Mark{
			Label: "operation_log.label.name",
			Value: newField.Label,
			Blind: false,
		})
	} else {
		marks = append(marks, models.Mark{
			Label: "operation_log.label.name",
			Value: oldField.Label,
			Blind: false,
		})
	}

	if types == 3 || len(OperationLogFieldGroups) != 0 {
		err := SaveOperation(userOID, "operation_log.module.form", envOID, types, OperationLogFieldGroups, marks, OID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

// insertRandomDesignLog 随机类型， 区组， 分层日志写入
func insertRandomDesignLog(userOID primitive.ObjectID, OID primitive.ObjectID, types int, oldRandomDesign models.RandomDesignInfo, newRandomDesign models.RandomDesignInfo, OperID primitive.ObjectID, updateType string, project models.Project, copyEnv string) error {
	// TODO保存项目日志
	groupLabelShow := ""
	factorLabelShow := ""
	OperationLogFieldGroups := []models.OperationLogFieldGroup{}
	if types != 3 {
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design",
				TranKey: "operation_log.random_design.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}
		if oldRandomDesign.Type != newRandomDesign.Type && newRandomDesign.Type != 0 {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design",
				TranKey: "operation_log.random_design.type",
				Old: models.OperationLogField{
					Type:  6,
					Value: oldRandomDesign.Type,
				},
				New: models.OperationLogField{
					Type:  6,
					Value: newRandomDesign.Type,
				},
			})
		}
		for _, newGroup := range newRandomDesign.Groups {
			add := true
			for _, oldGroup := range oldRandomDesign.Groups {
				if newGroup.ID == oldGroup.ID {
					add = false
					haveSub := false
					if (oldGroup.SubGroup != nil && len(oldGroup.SubGroup) > 0) || (newGroup.SubGroup != nil && len(newGroup.SubGroup) > 0) {
						haveSub = true
					}
					if !haveSub { // 编辑的话 修改当前数据
						if newGroup.Name == oldGroup.Name {
							groupLabelShow = oldGroup.Name
						}
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.group_name",
							Old: models.OperationLogField{
								Type:  7,
								Value: oldGroup.Name,
							},
							New: models.OperationLogField{
								Type:  7,
								Value: newGroup.Name,
							},
						})

						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.group_code",
							Old: models.OperationLogField{
								Type:  7,
								Value: oldGroup.Code,
							},
							New: models.OperationLogField{
								Type:  7,
								Value: newGroup.Code,
							},
						})
					} else {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.group_code",
							Old: models.OperationLogField{
								Type:  7,
								Value: oldGroup.Code,
							},
							New: models.OperationLogField{
								Type:  7,
								Value: newGroup.Code,
							},
						})
						oldType := 7
						oldGroupName := oldGroup.Name
						if oldGroup.SubGroup != nil && len(oldGroup.SubGroup) > 0 {
							oldType = 13
							subNames := slice.Map(oldGroup.SubGroup, func(index int, item models.SubGroup) string {
								return item.Name
							})
							oldGroupName = fmt.Sprintf("%s%s%s", oldGroup.Name, "@@", strings.Join(subNames, "$$"))
						}
						newType := 7
						newGroupName := newGroup.Name
						if newGroup.SubGroup != nil && len(newGroup.SubGroup) > 0 {
							newType = 13
							subNames := slice.Map(newGroup.SubGroup, func(index int, item models.SubGroup) string {
								return item.Name
							})
							newGroupName = fmt.Sprintf("%s%s%s", newGroup.Name, "@@", strings.Join(subNames, "$$"))
						}
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.group_name",
							Old: models.OperationLogField{
								Type:  oldType,
								Value: oldGroupName,
							},
							New: models.OperationLogField{
								Type:  newType,
								Value: newGroupName,
							},
						})
					}
					if oldGroup.Status != newGroup.Status {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.status",
							Old: models.OperationLogField{
								Type:  6,
								Value: oldGroup.Status,
							},
							New: models.OperationLogField{
								Type:  6,
								Value: newGroup.Status,
							},
						})
					}
				}

			}
			if add {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.group_code",
					Old: models.OperationLogField{
						Type:  7,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  7,
						Value: newGroup.Code,
					},
				})
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.status",
					Old: models.OperationLogField{
						Type:  6,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  6,
						Value: newGroup.Status,
					},
				})
				haveSub := false
				if newGroup.SubGroup != nil && len(newGroup.SubGroup) > 0 {
					haveSub = true
				}
				if !haveSub {
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.group_name",
						Old: models.OperationLogField{
							Type:  7,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  7,
							Value: newGroup.Name,
						},
					})
				} else {
					newType := 7
					newGroupName := newGroup.Name
					if newGroup.SubGroup != nil && len(newGroup.SubGroup) > 0 {
						newType = 13
						subNames := slice.Map(newGroup.SubGroup, func(index int, item models.SubGroup) string {
							return item.Name
						})
						newGroupName = fmt.Sprintf("%s%s%s", newGroup.Name, "@@", strings.Join(subNames, "$$"))
					}
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.group_name",
						Old: models.OperationLogField{
							Type:  7,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  newType,
							Value: newGroupName,
						},
					})
				}
			}
		}
		for _, newFactor := range newRandomDesign.Factors {
			add := true
			for _, oldFactor := range oldRandomDesign.Factors {
				if newFactor.ID == oldFactor.ID {
					add = false
					if newFactor.Label == oldFactor.Label {
						factorLabelShow = oldFactor.Label
					}
					if newFactor.Name != oldFactor.Name {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.name",
							Old: models.OperationLogField{
								Type:  1,
								Value: oldFactor.Name,
							},
							New: models.OperationLogField{
								Type:  1,
								Value: newFactor.Name,
							},
						})
					}
					if newFactor.Number != oldFactor.Number {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.number",
							Old: models.OperationLogField{
								Type:  1,
								Value: oldFactor.Number,
							},
							New: models.OperationLogField{
								Type:  1,
								Value: newFactor.Number,
							},
						})
					}
					if newFactor.Label != oldFactor.Label {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.label",
							Old: models.OperationLogField{
								Type:  1,
								Value: oldFactor.Label,
							},
							New: models.OperationLogField{
								Type:  1,
								Value: newFactor.Label,
							},
						})
					}
					if newFactor.Type != oldFactor.Type && newFactor.IsCalc == false {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.type",
							Old: models.OperationLogField{
								Type:  6,
								Value: oldFactor.Type,
							},
							New: models.OperationLogField{
								Type:  6,
								Value: newFactor.Type,
							},
						})
					}

					if newFactor.Status != oldFactor.Status {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.status",
							Old: models.OperationLogField{
								Type:  6,
								Value: oldFactor.Status,
							},
							New: models.OperationLogField{
								Type:  6,
								Value: newFactor.Status,
							},
						})
					}
					if oldFactor.DateFormat == nil {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.form.format",
							Old: models.OperationLogField{
								Type:  2,
								Value: nil,
							},
							New: models.OperationLogField{
								Type:  2,
								Value: newFactor.DateFormat,
							},
						})
					}
					if oldFactor.Precision == nil {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.precision",
							Old: models.OperationLogField{
								Type:  1,
								Value: nil,
							},
							New: models.OperationLogField{
								Type:  1,
								Value: newFactor.Precision,
							},
						})
					} else {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.precision",
							Old: models.OperationLogField{
								Type:  1,
								Value: *oldFactor.Precision,
							},
							New: models.OperationLogField{
								Type:  1,
								Value: *newFactor.Precision,
							},
						})
					}
					oldLabel := []string{}
					newLabel := []string{}
					for _, oldOption := range oldFactor.Options {
						if oldOption.Formula != nil {
							oldLabel = append(oldLabel, oldOption.Label+" - "+*oldOption.Formula)
						} else {
							oldLabel = append(oldLabel, oldOption.Label)
						}
					}
					for _, newOption := range newFactor.Options {
						if newOption.Formula != nil {
							newLabel = append(newLabel, newOption.Label+" - "+*newOption.Formula)
						} else {
							newLabel = append(newLabel, newOption.Label)
						}
					}
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.factor.options_label_value",
						Old: models.OperationLogField{
							Type:  1,
							Value: strings.Join(oldLabel, "/"),
						},
						New: models.OperationLogField{
							Type:  1,
							Value: strings.Join(newLabel, "/"),
						},
					})
				}
			}
			if add {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.factor.name",
					Old: models.OperationLogField{
						Type:  1,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newFactor.Name,
					},
				})
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.factor.number",
					Old: models.OperationLogField{
						Type:  1,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newFactor.Number,
					},
				})
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.factor.label",
					Old: models.OperationLogField{
						Type:  1,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newFactor.Label,
					},
				})
				if newFactor.IsCalc == true {
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.factor.calcType",
						Old: models.OperationLogField{
							Type:  1,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  6,
							Value: newFactor.CalcType,
						},
					})
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.factor.formula",
						Old: models.OperationLogField{
							Type:  2,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  2,
							Value: newFactor.CustomFormulas,
						},
					})
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.factor.keepDecimal",
						Old: models.OperationLogField{
							Type:  1,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  1,
							Value: newFactor.Precision,
						},
					})
					if newFactor.Precision != nil && *newFactor.Precision == 0 {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.roundingMethod",
							Old: models.OperationLogField{
								Type:  6,
								Value: "",
							},
							New: models.OperationLogField{
								Type:  6,
								Value: newFactor.Round,
							},
						})
					}

				}
				if newFactor.IsCalc == false {
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.factor.type",
						Old: models.OperationLogField{
							Type:  1,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  6,
							Value: newFactor.Type,
						},
					})
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.factor.status",
					Old: models.OperationLogField{
						Type:  1,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  6,
						Value: newFactor.Status,
					},
				})
				newLabel := []string{}
				for _, newOption := range newFactor.Options {
					if newOption.Formula != nil {
						newLabel = append(newLabel, newOption.Label+" - "+*newOption.Formula)
					} else {
						newLabel = append(newLabel, newOption.Label)
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.factor.options_label_value",
					Old: models.OperationLogField{
						Type:  1,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  1,
						Value: strings.Join(newLabel, "/"),
					},
				})
			}
		}
		marks := []models.Mark{}
		if types == 2 {
			if updateType == "group" && groupLabelShow != "" {
				marks = append(marks, models.Mark{
					Label: "operation_log.random_design.group_name",
					Value: groupLabelShow,
					Blind: false,
				})
			}
			if updateType == "factor" && factorLabelShow != "" {
				marks = append(marks, models.Mark{
					Label: "operation_log.random_design.factor.label",
					Value: factorLabelShow,
					Blind: false,
				})
			}
		}
		if len(OperationLogFieldGroups) != 0 {
			err := SaveOperation(userOID, "operation_log.module.random_design", OID, types, OperationLogFieldGroups, marks, OperID)
			if err != nil {
				return errors.WithStack(err)
			}
			return nil
		}

	}
	return nil
}

// insertVisitSettingLog 访视设置
func insertVisitSettingLog(userOID primitive.ObjectID, OID primitive.ObjectID, types int, old models.VisitSetting, new models.VisitSetting, operID primitive.ObjectID, copyEnv string) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.visitSetting",
				TranKey: "operation_log.visitSetting.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitSetting",
			TranKey: "operation_log.visitSetting.unscheduled_visit",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.IsOpen,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.IsOpen,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitSetting",
			TranKey: "operation_log.visitSetting.name_zh",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.NameZh,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.NameZh,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitSetting",
			TranKey: "operation_log.visitSetting.name_en",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.NameEn,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.NameEn,
			},
		})

	}
	marks := []models.Mark{}

	err := SaveOperation(userOID, "operation_log.module.visitSetting", OID, types, OperationLogFieldGroups, marks, operID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// insertVisitCycleLog 访视日志
func insertVisitCycleLog(userOID primitive.ObjectID, OID primitive.ObjectID, types int, old models.VisitCycleInfo, new models.VisitCycleInfo, operID primitive.ObjectID, project models.Project, copyEnv string) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.visitCycle",
				TranKey: "operation_log.visitCycle.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.number",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.Number,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.Number,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.name",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.Name,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.Name,
			},
		})
		oldGroup := []string{}
		for _, item := range old.Group {
			oldGroup = append(oldGroup, item.(string))
		}
		newGroup := []string{}
		for _, item := range new.Group {
			newGroup = append(newGroup, item.(string))
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.group",
			Old: models.OperationLogField{
				Type:  12,
				Value: strings.Join(oldGroup, ","),
			},
			New: models.OperationLogField{
				Type:  12,
				Value: strings.Join(newGroup, ","),
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.interval",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.Interval,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.Interval,
			},
		})
		oldPeriodMin := ""
		oldPeriodMax := ""
		newPeriodMin := ""
		newPeriodMax := ""
		if old.PeriodMin != nil {
			oldPeriodMin = convertor.ToString(*old.PeriodMin)
		}
		if old.PeriodMax != nil {
			oldPeriodMax = convertor.ToString(*old.PeriodMax)
		}
		if new.PeriodMin != nil {
			newPeriodMin = convertor.ToString(*new.PeriodMin)
		}
		if new.PeriodMax != nil {
			newPeriodMax = convertor.ToString(*new.PeriodMax)
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.period",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldPeriodMin + "~" + oldPeriodMax,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newPeriodMin + "~" + newPeriodMax,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.random",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.Random,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.Random,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.dispensing",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.Dispensing,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.Dispensing,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.dtp",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.DTP,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.DTP,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.replace",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.Replace,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.Replace,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.DTPMode",
			Old: models.OperationLogField{
				Type:  5,
				Value: old.DTPType,
			},
			New: models.OperationLogField{
				Type:  5,
				Value: new.DTPType,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.visitCycle",
			TranKey: "operation_log.visitCycle.doseAdjustment",
			Old: models.OperationLogField{
				Type:  4,
				Value: old.DoseAdjustment,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.DoseAdjustment,
			},
		})

	}
	marks := []models.Mark{}
	if types != 1 {
		marks = append(marks,
			models.Mark{
				Label: "operation_log.label.visitCycle",
				Value: new.Name,
				Blind: false,
			})
	}

	err := SaveOperation(userOID, "operation_log.module.visitCycle", OID, types, OperationLogFieldGroups, marks, operID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// insertDrugConfigureLog 研究产品配置日志
func insertDrugConfigureLog(userOID primitive.ObjectID, OID primitive.ObjectID, types int, old models.DrugConfigureInfo, new models.DrugConfigureInfo, operID primitive.ObjectID, envOID primitive.ObjectID, cohortOID primitive.ObjectID, copyEnv string, visitCycleInfo models.VisitCycle) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {

		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  7,
					Value: copyEnv,
				},
			})
		}

		//组别
		oldGroup := old.Group
		if old.ParName != "" {
			oldGroup = old.ParName
		}
		if new.ParName != oldGroup {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.group",
				Old: models.OperationLogField{
					Type:  7,
					Value: oldGroup,
				},
				New: models.OperationLogField{
					Type:  7,
					Value: new.Group,
				},
			})
		}
		//子组别
		if new.SubName != "" {
			oldSubGroup := models.Group{
				Group:   old.Group,
				ParName: old.ParName,
				SubName: old.SubName,
			}
			newSubGroup := models.Group{
				Group:   new.Group,
				ParName: new.ParName,
				SubName: new.SubName,
			}
			if newSubGroup != oldSubGroup {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure",
					TranKey: "operation_log.drug_configure.subGroup",
					Old: models.OperationLogField{
						Type:  11,
						Value: oldSubGroup,
					},
					New: models.OperationLogField{
						Type:  11,
						Value: newSubGroup,
					},
				})
			}
		}
		//发放方式
		if new.OpenSetting != old.OpenSetting {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.open",
				Old: models.OperationLogField{
					Type:  6,
					Value: old.OpenSetting,
				},
				New: models.OperationLogField{
					Type:  6,
					Value: new.OpenSetting,
				},
			})
		}

		if new.IsFormula != old.IsFormula {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.formula",
				Old: models.OperationLogField{
					Type:  4,
					Value: old.IsFormula,
				},
				New: models.OperationLogField{
					Type:  4,
					Value: new.IsFormula,
				},
			})
		}

		if new.IsFormula && new.CustomerCalculation != old.CustomerCalculation {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.customerCalculation",
				Old: models.OperationLogField{
					Type:  2,
					Value: old.CustomerCalculation,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: new.CustomerCalculation,
				},
			})
		}

		if new.IsFormula && new.CustomerCalculationSpec != old.CustomerCalculationSpec {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.spec",
				Old: models.OperationLogField{
					Type:  2,
					Value: old.CustomerCalculationSpec,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: new.CustomerCalculationSpec,
				},
			})
		}

		//研究产品配置
		if new.OpenSetting != 3 {
			var oldDrugValues string
			var newDrugValues string
			for _, v := range old.Values {
				// 兼容旧数据
				dispensingNumber := ""
				if v.CustomDispensingNumber != "" {
					dispensingNumber = v.CustomDispensingNumber
				} else {
					dispensingNumber = convertor.ToString(v.DispensingNumber)
				}

				oldDrugValues = oldDrugValues + fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s；", convertor.ToString(v.AutomaticRecode), convertor.ToString(v.AutomaticRecodeSpec), v.DrugName, dispensingNumber, v.DrugSpec, strconv.FormatBool(v.IsOther), v.Label)
			}
			for _, v := range new.Values {
				// 兼容旧数据
				dispensingNumber := ""
				if v.CustomDispensingNumber != "" {
					dispensingNumber = v.CustomDispensingNumber
				} else {
					dispensingNumber = convertor.ToString(v.DispensingNumber)
				}
				newDrugValues = newDrugValues + fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s；", convertor.ToString(v.AutomaticRecode), convertor.ToString(v.AutomaticRecodeSpec), v.DrugName, dispensingNumber, v.DrugSpec, strconv.FormatBool(v.IsOther), v.Label)
			}
			if newDrugValues != oldDrugValues {
				if len(new.Values) > 0 {
					for i, v := range new.Values {
						var oldValue models.DrugValue
						if len(old.Values) > i {
							oldValue = old.Values[i]
						}

						oDispensingNumber := ""
						if oldValue.CustomDispensingNumber != "" {
							oDispensingNumber = oldValue.CustomDispensingNumber
						} else {
							oDispensingNumber = convertor.ToString(oldValue.DispensingNumber)
						}

						vDispensingNumber := ""
						if v.CustomDispensingNumber != "" {
							vDispensingNumber = v.CustomDispensingNumber
						} else {
							vDispensingNumber = convertor.ToString(v.DispensingNumber)
						}

						olds := fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s；", convertor.ToString(oldValue.AutomaticRecode), convertor.ToString(oldValue.AutomaticRecodeSpec), oldValue.DrugName, oDispensingNumber, oldValue.DrugSpec, strconv.FormatBool(oldValue.IsOther), oldValue.Label)
						news := fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s；", convertor.ToString(v.AutomaticRecode), convertor.ToString(v.AutomaticRecodeSpec), v.DrugName, vDispensingNumber, v.DrugSpec, strconv.FormatBool(v.IsOther), v.Label)
						//研究产品名称/规格
						if v.DrugName != "" && olds != news {
							oldDrugNameSpec := ""
							if oldValue.DrugName != v.DrugName || oldValue.DrugSpec != v.DrugSpec {
								oldDrugNameSpec = oldValue.DrugName + "/" + oldValue.DrugSpec
							}
							OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
								Key:     "operation_log.drug_configure",
								TranKey: "operation_log.drug_configure.drugNameSpec",
								Old: models.OperationLogField{
									Type:  7,
									Value: oldDrugNameSpec,
								},
								New: models.OperationLogField{
									Type:  7,
									Value: v.DrugName + "/" + v.DrugSpec,
								},
							})
							//发放数量
							if vDispensingNumber != oDispensingNumber {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.dispensingNumber",
									Old: models.OperationLogField{
										Type:  1,
										Value: oldValue.CustomDispensingNumber,
									},
									New: models.OperationLogField{
										Type:  1,
										Value: v.CustomDispensingNumber,
									},
								})
							}
							if new.OpenSetting == 1 {
								//发放标签
								if v.Label != oldValue.Label {
									OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
										Key:     "operation_log.drug_configure",
										TranKey: "operation_log.drug_configure.drugLabel",
										Old: models.OperationLogField{
											Type:  1,
											Value: oldValue.Label,
										},
										New: models.OperationLogField{
											Type:  1,
											Value: v.Label,
										},
									})
								}
							}

							//未编号研究产品
							if v.IsOther != oldValue.IsOther {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.otherCheck",
									Old: models.OperationLogField{
										Type:  6,
										Value: oldValue.IsOther,
									},
									New: models.OperationLogField{
										Type:  6,
										Value: v.IsOther,
									},
								})
							}
							//自动赋值
							if v.AutomaticRecode != oldValue.AutomaticRecode {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.automatic_recode",
									Old: models.OperationLogField{
										Type:  4,
										Value: oldValue.AutomaticRecode,
									},
									New: models.OperationLogField{
										Type:  4,
										Value: v.AutomaticRecode,
									},
								})
							}
							//计算单位
							if v.AutomaticRecodeSpec != oldValue.AutomaticRecodeSpec {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.automatic_recode_spec",
									Old: models.OperationLogField{
										Type:  1,
										Value: oldValue.AutomaticRecodeSpec,
									},
									New: models.OperationLogField{
										Type:  1,
										Value: v.AutomaticRecodeSpec,
									},
								})
							}
						}

					}
				}
			}
		}

		//查询访视
		oldVisitCycleZh := make([]string, 0)
		oldVisitCycleEn := make([]string, 0)
		newVisitCycleZh := make([]string, 0)
		newVisitCycleEn := make([]string, 0)
		//var oldVisitCycle string
		//var newVisitCycle string
		var visitCycle models.VisitCycle
		match := bson.M{"env_id": envOID}
		if cohortOID != primitive.NilObjectID {
			match = bson.M{"env_id": envOID, "cohort_id": cohortOID}
		}
		DB.Collection("visit_cycle").FindOne(nil, match).Decode(&visitCycle)
		if copyEnv != "" {
			visitCycle = visitCycleInfo
		}
		if visitCycle.Infos != nil {
			for _, v := range visitCycle.Infos {
				for _, oldV := range old.VisitCycles {
					if v.ID == oldV {
						oldVisitCycleZh = append(oldVisitCycleZh, v.Name)
						oldVisitCycleEn = append(oldVisitCycleEn, v.Name)
						//oldVisitCycle = oldVisitCycle + v.Name + ";"
					}
				}
				for _, newV := range new.VisitCycles {
					if v.ID == newV {
						newVisitCycleZh = append(newVisitCycleZh, v.Name)
						newVisitCycleEn = append(newVisitCycleEn, v.Name)
						//newVisitCycle = newVisitCycle + v.Name + ";"
					}
				}
			}
		}

		if visitCycle.SetInfo.IsOpen {
			for _, oldV := range old.VisitCycles {
				if visitCycle.SetInfo.Id == oldV {
					oldVisitCycleZh = append(oldVisitCycleZh, visitCycle.SetInfo.NameZh)
					oldVisitCycleEn = append(oldVisitCycleEn, visitCycle.SetInfo.NameEn)
					//oldVisitCycle = oldVisitCycle + v.Name + ";"
				}
			}
			for _, newV := range new.VisitCycles {
				if visitCycle.SetInfo.Id == newV {
					newVisitCycleZh = append(newVisitCycleZh, visitCycle.SetInfo.NameZh)
					newVisitCycleEn = append(newVisitCycleEn, visitCycle.SetInfo.NameEn)
				}
			}
		}

		//访视名称
		if strings.Join(newVisitCycleZh, ";") != strings.Join(oldVisitCycleZh, ";") ||
			strings.Join(newVisitCycleEn, ";") != strings.Join(oldVisitCycleEn, ";") {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.visitCycles",
				Old: models.OperationLogField{
					Type:    30,
					Value:   strings.Join(oldVisitCycleZh, ";"),
					ENValue: strings.Join(oldVisitCycleEn, ";"),
				},
				New: models.OperationLogField{
					Type:    30,
					Value:   strings.Join(newVisitCycleZh, ";"),
					ENValue: strings.Join(newVisitCycleEn, ";"),
				},
			})
		}
		//发放标签
		if new.OpenSetting == 1 || new.OpenSetting == 2 {
			if new.Label != old.Label {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure",
					TranKey: "operation_log.drug_configure.label",
					Old: models.OperationLogField{
						Type:  2,
						Value: old.Label,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: new.Label,
					},
				})
			}
		}

		//公式发放
		//研究产品名称打码显示
		if new.OpenSetting == 3 {
			//方式
			if new.CalculationType != old.CalculationType {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure",
					TranKey: "operation_log.drug_configure.calculationType",
					Old: models.OperationLogField{
						Type:  6,
						Value: old.CalculationType,
					},
					New: models.OperationLogField{
						Type:  6,
						Value: new.CalculationType,
					},
				})
			}
			//自定义公式 其他体表面积BSA
			if new.CalculationType == 4 {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure",
					TranKey: "operation_log.drug_configure.customerCalculation",
					Old: models.OperationLogField{
						Type:  2,
						Value: old.CustomerCalculation,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: new.CustomerCalculation,
					},
				})
			}

			if len(new.Values) > 0 {
				for i, v := range new.Values {
					var oldValue models.DrugValue
					if len(old.Values) > i {
						oldValue = old.Values[i]
					}

					//范围/发放数量
					oldF := ""
					newF := ""
					for _, v := range oldValue.Formulas {
						oldF = oldF + v.Expression + "/" + strconv.Itoa(v.Value) + ";"
					}
					for _, v := range v.Formulas {
						newF = newF + v.Expression + "/" + strconv.Itoa(v.Value) + ";"
					}

					oldV := fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s", oldValue.DrugName, oldValue.DrugSpec, strconv.FormatBool(oldValue.IsOther), strconv.FormatBool(oldValue.IsOther), strconv.FormatBool(oldValue.CalculationInfo.KeepDecimal), convertor.ToString(oldValue.CalculationInfo.Precision), oldF)
					newV := fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s", v.DrugName, v.DrugSpec, strconv.FormatBool(v.IsOther), strconv.FormatBool(v.IsOther), strconv.FormatBool(v.CalculationInfo.KeepDecimal), convertor.ToString(v.CalculationInfo.Precision), newF)

					oldSpecifiaction := ""
					if new.CalculationType == 3 || new.CalculationType == 4 {
						if types == 2 {
							if oldValue.CalculationInfo.Specifications.Value != nil && oldValue.CalculationInfo.Specifications.Unit != nil {
								oldSpecifiaction = convertor.ToString(*oldValue.CalculationInfo.Specifications.Value) + convertor.ToString(*oldValue.CalculationInfo.Specifications.Unit)
							}
						}
						oldV = oldV + oldSpecifiaction + convertor.ToString(oldValue.UnitCalculationStandard)
						newV = newV + convertor.ToString(*v.CalculationInfo.Specifications.Value) + convertor.ToString(*v.CalculationInfo.Specifications.Unit) + convertor.ToString(v.UnitCalculationStandard)
					}

					//研究产品名称/规格
					if v.DrugName != "" && oldV != newV {
						oldDrugNameSpec := ""
						if oldValue.DrugName != v.DrugName || oldValue.DrugSpec != v.DrugSpec {
							oldDrugNameSpec = oldValue.DrugName + "/" + oldValue.DrugSpec
						}
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.drug_configure",
							TranKey: "operation_log.drug_configure.drugNameSpec",
							Old: models.OperationLogField{
								Type:  7,
								Value: oldDrugNameSpec,
							},
							New: models.OperationLogField{
								Type:  7,
								Value: v.DrugName + "/" + v.DrugSpec,
							},
						})

						//体重范围/发放数量
						if new.CalculationType == 2 || new.CalculationType == 3 || new.CalculationType == 4 {
							if newF != oldF {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.weightDispeningNumber",
									Old: models.OperationLogField{
										Type:  2,
										Value: oldF,
									},
									New: models.OperationLogField{
										Type:  2,
										Value: newF,
									},
								})
							}
							//体重计算比较
							if v.ComparisonSwitch != oldValue.ComparisonSwitch {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.comparisonSwitch",
									Old: models.OperationLogField{
										Type:  6,
										Value: oldValue.ComparisonSwitch,
									},
									New: models.OperationLogField{
										Type:  6,
										Value: v.ComparisonSwitch,
									},
								})
							}
							//比较条件
							if v.ComparisonType != oldValue.ComparisonType {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.comparisonType",
									Old: models.OperationLogField{
										Type:  6,
										Value: oldValue.ComparisonType,
									},
									New: models.OperationLogField{
										Type:  6,
										Value: v.ComparisonType,
									},
								})
							}
							if v.ComparisonRatio != oldValue.ComparisonRatio {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.comparisonRatio",
									Old: models.OperationLogField{
										Type:  2,
										Value: oldValue.ComparisonRatio,
									},
									New: models.OperationLogField{
										Type:  2,
										Value: v.ComparisonRatio,
									},
								})
							}
							if v.CurrentComparisonType != oldValue.CurrentComparisonType {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.currentComparisonType",
									Old: models.OperationLogField{
										Type:  6,
										Value: oldValue.CurrentComparisonType,
									},
									New: models.OperationLogField{
										Type:  6,
										Value: v.CurrentComparisonType,
									},
								})
							}
						}
						//年龄范围/发放数量
						if new.CalculationType == 1 {
							if newF != oldF {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.ageDispeningNumber",
									Old: models.OperationLogField{
										Type:  2,
										Value: oldF,
									},
									New: models.OperationLogField{
										Type:  2,
										Value: newF,
									},
								})
							}
						}
						//单位计算标准
						if new.CalculationType == 3 || new.CalculationType == 4 {
							OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
								Key:     "operation_log.drug_configure",
								TranKey: "operation_log.drug_configure.specifications",
								Old: models.OperationLogField{
									Type:  2,
									Value: oldSpecifiaction,
								},
								New: models.OperationLogField{
									Type:  2,
									Value: convertor.ToString(*v.CalculationInfo.Specifications.Value) + convertor.ToString(*v.CalculationInfo.Specifications.Unit),
								},
							})
							if v.UnitCalculationStandard != oldValue.UnitCalculationStandard {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.standard",
									Old: models.OperationLogField{
										Type:  2,
										Value: oldValue.UnitCalculationStandard,
									},
									New: models.OperationLogField{
										Type:  2,
										Value: v.UnitCalculationStandard,
									},
								})
							}
						}

						//未编号研究产品
						if v.IsOther != oldValue.IsOther {
							OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
								Key:     "operation_log.drug_configure",
								TranKey: "operation_log.drug_configure.otherCheck",
								Old: models.OperationLogField{
									Type:  6,
									Value: oldValue.IsOther,
								},
								New: models.OperationLogField{
									Type:  6,
									Value: v.IsOther,
								},
							})
						}
						//开放研究产品
						if v.IsOpen != oldValue.IsOpen {
							OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
								Key:     "operation_log.drug_configure",
								TranKey: "operation_log.drug_configure.openCheck",
								Old: models.OperationLogField{
									Type:  6,
									Value: oldValue.IsOpen,
								},
								New: models.OperationLogField{
									Type:  6,
									Value: v.IsOpen,
								},
							})
						}
						//保留小数位
						if v.CalculationInfo.KeepDecimal != oldValue.CalculationInfo.KeepDecimal {
							OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
								Key:     "operation_log.drug_configure",
								TranKey: "operation_log.drug_configure.keepDecimal",
								Old: models.OperationLogField{
									Type:  6,
									Value: oldValue.CalculationInfo.KeepDecimal,
								},
								New: models.OperationLogField{
									Type:  6,
									Value: v.CalculationInfo.KeepDecimal,
								},
							})
						}
						if v.CalculationInfo.KeepDecimal {
							//保留位数
							if v.CalculationInfo.Precision != oldValue.CalculationInfo.Precision {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.precision",
									Old: models.OperationLogField{
										Type:  1,
										Value: oldValue.CalculationInfo.Precision,
									},
									New: models.OperationLogField{
										Type:  1,
										Value: v.CalculationInfo.Precision,
									},
								})
							}
						}
					}
				}
			}
		}

		//常规访视映射
		//oldRoutineVisitMappingList := old.RoutineVisitMappingList
		newRoutineVisitMappingList := new.RoutineVisitMappingList

		visitDrugNameDispeningNumberList := make([]string, 0)
		if newRoutineVisitMappingList != nil && len(newRoutineVisitMappingList) > 0 {
			for _, newRoutineVisitMapping := range newRoutineVisitMappingList {
				visitNameList := make([]string, 0)
				if visitCycle.Infos != nil {
					for _, v := range visitCycle.Infos {
						if newRoutineVisitMapping.VisitList != nil && len(newRoutineVisitMapping.VisitList) > 0 {
							for _, visitId := range newRoutineVisitMapping.VisitList {
								if v.ID == visitId {
									visitNameList = append(visitNameList, v.Name)
								}
							}
						}
					}
				}
				strings.Join(visitNameList, "、")
				if newRoutineVisitMapping.DrugList != nil && len(newRoutineVisitMapping.DrugList) > 0 {
					for _, drug := range newRoutineVisitMapping.DrugList {
						visitDrugNameDispeningNumberList = append(visitDrugNameDispeningNumberList, strings.Join(visitNameList, "、")+"/"+drug.DrugName+"/"+drug.CustomDispensingNumber)
					}
				}
			}
		}

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.drug_configure",
			TranKey: "operation_log.drug_configure.visitDrugNameDispeningNumber",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: strings.Join(visitDrugNameDispeningNumberList, "、"),
			},
		})

	}

	if (OperationLogFieldGroups != nil && len(OperationLogFieldGroups) > 0) && (types == 1 || types == 2 || types == 10) {
		newOperationLogFieldGroup := models.OperationLogFieldGroup{
			Key:     "operation_log.drug_configure",
			TranKey: "operation_log.drug_configure.onlyID",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.ID,
			},
		}
		// 添加新的 OperationLogFieldGroup 到数组的开头
		OperationLogFieldGroups = append([]models.OperationLogFieldGroup{newOperationLogFieldGroup}, OperationLogFieldGroups...)
	}
	marks := []models.Mark{}
	err := SaveOperation(userOID, "operation_log.module.drug_configure", OID, types, OperationLogFieldGroups, marks, operID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// insertDrugConfigureSettingLog 研究产品配置-设置日志
func insertDrugConfigureSettingLog(userOID primitive.ObjectID, OID primitive.ObjectID, types int, old models.DrugConfigureSetting, new models.DrugConfigureSetting, operID primitive.ObjectID, envOID primitive.ObjectID, cohortOID primitive.ObjectID, copyEnv string) error {
	// 保存项目日志
	OperationLogFieldGroups := make([]models.OperationLogFieldGroup, 0)
	if types != 3 {

		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}

		if new.DtpIpList != nil && len(new.DtpIpList) > 0 {
			dtpIpTypeListZh := make([]string, 0)
			dtpIpTypeListEn := make([]string, 0)
			for _, dtpIp := range new.DtpIpList {
				dtpIpType := dtpIp.IP + "/"
				if dtpIp.DtpTypeList != nil && len(dtpIp.DtpTypeList) > 0 {
					dtpTypeListZh := make([]string, 0)
					dtpTypeListEn := make([]string, 0)
					for _, dtpType := range dtpIp.DtpTypeList {
						dtpTypeZh := ""
						dtpTypeEn := ""
						if dtpType != 0 {
							if dtpType == 1 {
								dtpTypeZh = locales.TrWithLang("zh", "operation_log.drug_configure_setting.dtp_ipType_site")
								dtpTypeEn = locales.TrWithLang("en", "operation_log.drug_configure_setting.dtp_ipType_site")
							} else if dtpType == 2 {
								dtpTypeZh = locales.TrWithLang("zh", "operation_log.drug_configure_setting.dtp_ipType_siteSubject")
								dtpTypeEn = locales.TrWithLang("en", "operation_log.drug_configure_setting.dtp_ipType_siteSubject")
							} else if dtpType == 3 {
								dtpTypeZh = locales.TrWithLang("zh", "operation_log.drug_configure_setting.dtp_ipType_depotSubject")
								dtpTypeEn = locales.TrWithLang("en", "operation_log.drug_configure_setting.dtp_ipType_depotSubject")
							}
							dtpTypeListZh = append(dtpTypeListZh, dtpTypeZh)
							dtpTypeListEn = append(dtpTypeListEn, dtpTypeEn)
						}
					}
					dtpIpTypeListZh = append(dtpIpTypeListZh, dtpIpType+strings.Join(dtpTypeListZh, "、"))
					dtpIpTypeListEn = append(dtpIpTypeListEn, dtpIpType+strings.Join(dtpTypeListEn, "、"))
				}
			}

			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.dtp_ipType",
				Old: models.OperationLogField{
					Type:    30,
					Value:   nil,
					ENValue: nil,
				},
				New: models.OperationLogField{
					Type:    30,
					Value:   strings.Join(dtpIpTypeListZh, ","),
					ENValue: strings.Join(dtpIpTypeListEn, ","),
				},
			})
		}

		oldIsOpen := ""
		if old.IsOpen {
			oldIsOpen = locales.TrWithLang("zh", "drug.configure.setting.doseAdjustment.open")
		} else {
			oldIsOpen = locales.TrWithLang("zh", "drug.configure.setting.doseAdjustment.close")
		}
		newIsOpen := ""
		if old.IsOpen {
			newIsOpen = locales.TrWithLang("zh", "drug.configure.setting.doseAdjustment.open")
		} else {
			newIsOpen = locales.TrWithLang("zh", "drug.configure.setting.doseAdjustment.close")
		}

		//剂量调整开关
		if new.SelectType != old.SelectType {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.doseAdjustment",
				Old: models.OperationLogField{
					Type:  1,
					Value: oldIsOpen,
				},
				New: models.OperationLogField{
					Type:  1,
					Value: newIsOpen,
				},
			})
		}

		oldSelectTypeName := ""
		if old.SelectType == 1 {
			oldSelectTypeName = locales.TrWithLang("zh", "drug.configure.setting.dose.level")
		} else if old.SelectType == 2 {
			oldSelectTypeName = locales.TrWithLang("zh", "drug.configure.setting.dose.visit.judgment")
		}
		newSelectTypeName := ""
		if old.SelectType == 1 {
			newSelectTypeName = locales.TrWithLang("zh", "drug.configure.setting.dose.level")
		} else if old.SelectType == 2 {
			newSelectTypeName = locales.TrWithLang("zh", "drug.configure.setting.dose.visit.judgment")
		}

		//类型
		if new.SelectType != old.SelectType {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.selectType",
				Old: models.OperationLogField{
					Type:  1,
					Value: oldSelectTypeName,
				},
				New: models.OperationLogField{
					Type:  1,
					Value: newSelectTypeName,
				},
			})
		}
		oldDoseFormName := ""
		oldFieldName := ""
		oldOptionList := make([]models.Option, 0)
		if len(old.DoseFormId) != 0 {
			oldDoseForm, err := GetFormType(new.CustomerID.Hex(), new.EnvironmentID.Hex(), new.CohortID.Hex(), 3, old.DoseFormId)
			if err != nil {
				return errors.WithStack(err)
			}
			oldDoseFormName = oldDoseForm.Variable
			oldFieldName = oldDoseForm.Label
			oldOptionList = oldDoseForm.Options
		}
		newDoseFormName := ""
		newFieldName := ""
		newOptionList := make([]models.Option, 0)
		if len(new.DoseFormId) != 0 {
			newDoseForm, err := GetFormType(new.CustomerID.Hex(), new.EnvironmentID.Hex(), new.CohortID.Hex(), 3, new.DoseFormId)
			if err != nil {
				return errors.WithStack(err)
			}
			newDoseFormName = newDoseForm.Variable
			newFieldName = newDoseForm.Label
			newOptionList = newDoseForm.Options
		}
		//剂量表单
		if new.DoseFormId != old.DoseFormId {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.doseForm",
				Old: models.OperationLogField{
					Type:  2,
					Value: oldDoseFormName,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: newDoseFormName,
				},
			})
		}

		//首次访视启用初始剂量
		if new.IsFirstInitial != old.IsFirstInitial {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.isFirstInitial",
				Old: models.OperationLogField{
					Type:  4,
					Value: old.IsFirstInitial,
				},
				New: models.OperationLogField{
					Type:  4,
					Value: new.IsFirstInitial,
				},
			})
		}

		//允许受试者剂量下调的次数
		if new.IsDoseReduction != old.IsDoseReduction {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.isDoseReduction",
				Old: models.OperationLogField{
					Type:  4,
					Value: old.IsDoseReduction,
				},
				New: models.OperationLogField{
					Type:  4,
					Value: new.IsDoseReduction,
				},
			})
		}

		//次数
		if new.Frequency != old.Frequency {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.frequency",
				Old: models.OperationLogField{
					Type:  1,
					Value: old.Frequency,
				},
				New: models.OperationLogField{
					Type:  1,
					Value: new.Frequency,
				},
			})
		}

		//剂量水平集
		// 创建两个map，用于快速查找
		oldDoseLevelMap := make(map[primitive.ObjectID]models.DoseLevel)
		for _, obj := range old.DoseLevelList {
			oldDoseLevelMap[obj.ID] = obj
		}
		newDoseLevelMap := make(map[primitive.ObjectID]models.DoseLevel)
		for _, obj := range new.DoseLevelList {
			newDoseLevelMap[obj.ID] = obj
		}
		// 找出新增的元素
		for id, newObj := range newDoseLevelMap {
			if _, ok := oldDoseLevelMap[id]; !ok {
				//fmt.Printf("新增对象: %v\n", newObj)
				//剂量水平
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevel",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newObj.Name,
					},
				})
				//名称
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelName",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newObj.Name,
					},
				})
				//组别
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelGroup",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(newObj.Group, ", "),
					},
				})

				//发放剂量
				newDoseDistributionList := make([]string, 0)
				if newObj.DoseDistribution != nil && len(newObj.DoseDistribution) > 0 {
					for _, doseDistribution := range newObj.DoseDistribution {
						var jsonObject map[string]interface{}
						json.Unmarshal([]byte(doseDistribution), &jsonObject)
						fmt.Println(jsonObject)
						name := jsonObject["name"].(string)
						newDoseDistributionList = append(newDoseDistributionList, name)
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelDoseDistribution",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(newDoseDistributionList, ", "),
					},
				})
				//初始剂量
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelInitialDose",
					Old: models.OperationLogField{
						Type:  30,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:    30,
						Value:   FormatBoolZh(newObj.InitialDose),
						ENValue: strconv.FormatBool(newObj.InitialDose),
					},
				})
			}
		}
		// 找出删除的元素
		for id, oldObj := range oldDoseLevelMap {
			if _, ok := newDoseLevelMap[id]; !ok {
				fmt.Printf("删除对象: %v\n", oldObj)
				//ID
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelID",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldObj.ID.Hex(),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//剂量水平
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevel",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldObj.Name,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//名称
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelName",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldObj.Name,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//组别
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelGroup",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(oldObj.Group, ", "),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//发放剂量
				oldDoseDistributionList := make([]string, 0)
				if oldObj.DoseDistribution != nil && len(oldObj.DoseDistribution) > 0 {
					for _, doseDistribution := range oldObj.DoseDistribution {
						var jsonObject map[string]interface{}
						json.Unmarshal([]byte(doseDistribution), &jsonObject)
						fmt.Println(jsonObject)
						name := jsonObject["name"].(string)
						oldDoseDistributionList = append(oldDoseDistributionList, name)
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelDoseDistribution",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(oldDoseDistributionList, ", "),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//初始剂量
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelInitialDose",
					Old: models.OperationLogField{
						Type:    30,
						Value:   FormatBoolZh(oldObj.InitialDose),
						ENValue: strconv.FormatBool(oldObj.InitialDose),
					},
					New: models.OperationLogField{
						Type:  30,
						Value: nil,
					},
				})
			}
		}
		// 找出修改的元素
		for id, newObj := range newDoseLevelMap {
			if oldObj, ok := oldDoseLevelMap[id]; ok {

				if newObj.Name != oldObj.Name || !reflect.DeepEqual(newObj.Group, oldObj.Group) ||
					!reflect.DeepEqual(newObj.DoseDistribution, oldObj.DoseDistribution) ||
					newObj.InitialDose != oldObj.InitialDose {
					//剂量水平
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.doseLevel",
						Old: models.OperationLogField{
							Type:  2,
							Value: nil,
						},
						New: models.OperationLogField{
							Type:  2,
							Value: newObj.Name,
						},
					})
				}

				if newObj.Name != oldObj.Name {
					//名称
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.doseLevelName",
						Old: models.OperationLogField{
							Type:  2,
							Value: oldObj.Name,
						},
						New: models.OperationLogField{
							Type:  2,
							Value: newObj.Name,
						},
					})
				}

				// 使用reflect.DeepEqual函数比较两个字符串数组
				if !reflect.DeepEqual(newObj.Group, oldObj.Group) {
					//组别
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.doseLevelGroup",
						Old: models.OperationLogField{
							Type:  2,
							Value: strings.Join(oldObj.Group, ", "),
						},
						New: models.OperationLogField{
							Type:  2,
							Value: strings.Join(newObj.Group, ", "),
						},
					})
				}
				if !reflect.DeepEqual(newObj.DoseDistribution, oldObj.DoseDistribution) {
					//发放剂量
					oldDoseDistributionList := make([]string, 0)
					if oldObj.DoseDistribution != nil && len(oldObj.DoseDistribution) > 0 {
						for _, doseDistribution := range oldObj.DoseDistribution {
							var jsonObject map[string]interface{}
							json.Unmarshal([]byte(doseDistribution), &jsonObject)
							fmt.Println(jsonObject)
							name := jsonObject["name"].(string)
							oldDoseDistributionList = append(oldDoseDistributionList, name)
						}
					}
					newDoseDistributionList := make([]string, 0)
					if newObj.DoseDistribution != nil && len(newObj.DoseDistribution) > 0 {
						for _, doseDistribution := range newObj.DoseDistribution {
							var jsonObject map[string]interface{}
							json.Unmarshal([]byte(doseDistribution), &jsonObject)
							fmt.Println(jsonObject)
							name := jsonObject["name"].(string)
							newDoseDistributionList = append(newDoseDistributionList, name)
						}
					}
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.doseLevelDoseDistribution",
						Old: models.OperationLogField{
							Type:  2,
							Value: strings.Join(oldDoseDistributionList, ", "),
						},
						New: models.OperationLogField{
							Type:  2,
							Value: strings.Join(newDoseDistributionList, ", "),
						},
					})
				}
				if newObj.InitialDose != oldObj.InitialDose {
					//初始剂量
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.doseLevelInitialDose",
						Old: models.OperationLogField{
							Type:    30,
							Value:   FormatBoolZh(oldObj.InitialDose),
							ENValue: strconv.FormatBool(oldObj.InitialDose),
						},
						New: models.OperationLogField{
							Type:    30,
							Value:   FormatBoolZh(newObj.InitialDose),
							ENValue: strconv.FormatBool(newObj.InitialDose),
						},
					})
				}

			}
		}

		//访视判断
		// 创建两个map，用于快速查找
		oldVisitJudgmentMap := make(map[primitive.ObjectID]models.VisitJudgment)
		for _, obj := range old.VisitJudgmentList {
			oldVisitJudgmentMap[obj.ID] = obj
		}
		newVisitJudgmentMap := make(map[primitive.ObjectID]models.VisitJudgment)
		for _, obj := range new.VisitJudgmentList {
			newVisitJudgmentMap[obj.ID] = obj
		}

		// 找出新增的元素
		for id, newObj := range newVisitJudgmentMap {
			if _, ok := oldVisitJudgmentMap[id]; !ok {
				fmt.Printf("新增对象: %v\n", newObj)

				newName := ""
				if newOptionList != nil && len(newOptionList) > 0 {
					for _, option := range newOptionList {
						if option.Value == newObj.Name {
							newName = newFieldName + " : " + option.Label
						}
					}
				}

				//访视判断
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgment",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newName,
					},
				})
				//名称
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentName",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newName,
					},
				})
				//组别
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentGroup",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(newObj.Group, ", "),
					},
				})
				//发放剂量
				newDoseDistributionList := make([]string, 0)
				if newObj.DoseDistribution != nil && len(newObj.DoseDistribution) > 0 {
					for _, doseDistribution := range newObj.DoseDistribution {
						var jsonObject map[string]interface{}
						json.Unmarshal([]byte(doseDistribution), &jsonObject)
						fmt.Println(jsonObject)
						name := jsonObject["name"].(string)
						newDoseDistributionList = append(newDoseDistributionList, name)
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentDoseDistribution",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(newDoseDistributionList, ", "),
					},
				})
				//后续访视继承
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentVisitInheritance",
					Old: models.OperationLogField{
						Type:  30,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:    30,
						Value:   FormatBoolZh(newObj.VisitInheritance),
						ENValue: strconv.FormatBool(newObj.VisitInheritance),
					},
				})
			}
		}

		// 找出删除的元素
		for id, oldObj := range oldVisitJudgmentMap {
			if _, ok := newVisitJudgmentMap[id]; !ok {
				fmt.Printf("删除对象: %v\n", oldObj)

				oldName := ""
				if oldOptionList != nil && len(oldOptionList) > 0 {
					for _, option := range oldOptionList {
						if option.Value == oldObj.Name {
							oldName = oldFieldName + " : " + option.Label
						}
					}
				}

				//ID
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentID",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldObj.ID,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//访视判断
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgment",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldName,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//名称
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentName",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldName,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//组别
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentGroup",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(oldObj.Group, ", "),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//发放剂量
				oldDoseDistributionList := make([]string, 0)
				if oldObj.DoseDistribution != nil && len(oldObj.DoseDistribution) > 0 {
					for _, doseDistribution := range oldObj.DoseDistribution {
						var jsonObject map[string]interface{}
						json.Unmarshal([]byte(doseDistribution), &jsonObject)
						fmt.Println(jsonObject)
						name := jsonObject["name"].(string)
						oldDoseDistributionList = append(oldDoseDistributionList, name)
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentDoseDistribution",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(oldDoseDistributionList, ", "),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//后续访视继承
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentVisitInheritance",
					Old: models.OperationLogField{
						Type:    30,
						Value:   FormatBoolZh(oldObj.VisitInheritance),
						ENValue: strconv.FormatBool(oldObj.VisitInheritance),
					},
					New: models.OperationLogField{
						Type:  30,
						Value: nil,
					},
				})
			}
		}

		// 找出修改的元素
		for id, newObj := range newVisitJudgmentMap {
			if oldObj, ok := oldVisitJudgmentMap[id]; ok {

				oldName := ""
				if oldOptionList != nil && len(oldOptionList) > 0 {
					for _, option := range oldOptionList {
						if option.Value == oldObj.Name {
							oldName = oldFieldName + " : " + option.Label
						}
					}
				}

				newName := ""
				if newOptionList != nil && len(newOptionList) > 0 {
					for _, option := range newOptionList {
						if option.Value == newObj.Name {
							newName = newFieldName + " : " + option.Label
						}
					}
				}

				if newObj.Name != oldObj.Name || !reflect.DeepEqual(newObj.Group, oldObj.Group) ||
					!reflect.DeepEqual(newObj.DoseDistribution, oldObj.DoseDistribution) ||
					newObj.VisitInheritance != oldObj.VisitInheritance {
					//访视判断
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.visitJudgment",
						Old: models.OperationLogField{
							Type:  2,
							Value: nil,
						},
						New: models.OperationLogField{
							Type:  2,
							Value: newName,
						},
					})
				}

				if newObj.Name != oldObj.Name {
					//名称
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.visitJudgmentName",
						Old: models.OperationLogField{
							Type:  2,
							Value: oldName,
						},
						New: models.OperationLogField{
							Type:  2,
							Value: newName,
						},
					})
				}

				// 使用reflect.DeepEqual函数比较两个字符串数组
				if !reflect.DeepEqual(newObj.Group, oldObj.Group) {
					//组别
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.visitJudgmentGroup",
						Old: models.OperationLogField{
							Type:  2,
							Value: strings.Join(oldObj.Group, ", "),
						},
						New: models.OperationLogField{
							Type:  2,
							Value: strings.Join(newObj.Group, ", "),
						},
					})
				}
				if !reflect.DeepEqual(newObj.DoseDistribution, oldObj.DoseDistribution) {
					//发放剂量
					oldDoseDistributionList := make([]string, 0)
					if oldObj.DoseDistribution != nil && len(oldObj.DoseDistribution) > 0 {
						for _, doseDistribution := range oldObj.DoseDistribution {
							var jsonObject map[string]interface{}
							json.Unmarshal([]byte(doseDistribution), &jsonObject)
							fmt.Println(jsonObject)
							name := jsonObject["name"].(string)
							oldDoseDistributionList = append(oldDoseDistributionList, name)
						}
					}
					newDoseDistributionList := make([]string, 0)
					if newObj.DoseDistribution != nil && len(newObj.DoseDistribution) > 0 {
						for _, doseDistribution := range newObj.DoseDistribution {
							var jsonObject map[string]interface{}
							json.Unmarshal([]byte(doseDistribution), &jsonObject)
							fmt.Println(jsonObject)
							name := jsonObject["name"].(string)
							newDoseDistributionList = append(newDoseDistributionList, name)
						}
					}
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.visitJudgmentDoseDistribution",
						Old: models.OperationLogField{
							Type:  2,
							Value: strings.Join(oldDoseDistributionList, ", "),
						},
						New: models.OperationLogField{
							Type:  2,
							Value: strings.Join(newDoseDistributionList, ", "),
						},
					})
				}
				if newObj.VisitInheritance != oldObj.VisitInheritance {
					//初始剂量
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.visitJudgmentVisitInheritance",
						Old: models.OperationLogField{
							Type:    30,
							Value:   FormatBoolZh(oldObj.VisitInheritance),
							ENValue: strconv.FormatBool(oldObj.VisitInheritance),
						},
						New: models.OperationLogField{
							Type:    30,
							Value:   FormatBoolZh(newObj.VisitInheritance),
							ENValue: strconv.FormatBool(newObj.VisitInheritance),
						},
					})
				}
			}
		}

	}
	marks := []models.Mark{}
	if copyEnv != "" && len(OperationLogFieldGroups) == 2 {
		return nil
	} else {
		err := SaveOperation(userOID, "operation_log.module.drug_configure_setting", OID, types, OperationLogFieldGroups, marks, operID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func SaveOperation(userOID primitive.ObjectID, module string, OID primitive.ObjectID, types int, OperationLogFieldGroups []models.OperationLogFieldGroup, mark []models.Mark, operatorID primitive.ObjectID) error {
	user := models.User{}
	e := DB.Collection("user").FindOne(nil, bson.M{"_id": userOID}).Decode(&user)
	if e != nil {
		panic(e)
	}
	var operationLog models.OperationLog
	operationLog.ID = primitive.NewObjectID()
	operationLog.OID = OID
	operationLog.Operator = user.ID
	operationLog.Module = module
	operationLog.Mark = mark
	operationLog.Time = time.Duration(time.Now().Unix())
	operationLog.Type = types
	operationLog.OperatorID = operatorID
	operationLog.Fields = OperationLogFieldGroups
	_, err := DB.Collection("operation_log").InsertOne(nil, operationLog)
	if err != nil {
		panic(err)
	}
	return nil
}

// 查询订单中的研究产品、未编号研究产品
func findOrderMedicine(oldEnvID primitive.ObjectID) ([]string, []string, error) {

	medicineNameList := make([]string, 0)
	otherMedicineNameList := make([]string, 0)

	//判断是否有订单
	condition := bson.M{"env_id": oldEnvID}
	medicineOrderList := make([]models.MedicineOrder, 0)
	medicineOrderCursor, err := DB.Collection("medicine_order").Find(nil, condition)
	if err != nil {
		return medicineNameList, otherMedicineNameList, errors.WithStack(err)
	}
	err = medicineOrderCursor.All(nil, &medicineOrderList)
	if err != nil {
		return medicineNameList, otherMedicineNameList, errors.WithStack(err)
	}

	medicineIdList := make([]primitive.ObjectID, 0)
	if medicineOrderList != nil && len(medicineOrderList) > 0 {
		for _, medicineOrder := range medicineOrderList {
			if medicineOrder.Medicines != nil && len(medicineOrder.Medicines) > 0 {
				for _, medicineId := range medicineOrder.Medicines {
					medicineIdList = append(medicineIdList, medicineId)
				}
			}
			if medicineOrder.OtherMedicines != nil && len(medicineOrder.OtherMedicines) > 0 {
				for _, otherMedicine := range medicineOrder.OtherMedicines {
					index := arrays.ContainsString(otherMedicineNameList, otherMedicine.Name)
					if index == -1 {
						//-1说明不存在
						otherMedicineNameList = append(otherMedicineNameList, otherMedicine.Name)
					}
				}
			}
		}
	}

	medicineList := make([]models.Medicine, 0)
	cursor, err := DB.Collection("medicine").Find(nil, bson.M{"_id": bson.M{"$in": medicineIdList}})
	if err != nil {
		return medicineNameList, otherMedicineNameList, errors.WithStack(err)
	}
	err = cursor.All(nil, &medicineList)
	if err != nil {
		return medicineNameList, otherMedicineNameList, errors.WithStack(err)
	}

	if medicineList != nil && len(medicineList) > 0 {
		for _, medicine := range medicineList {
			index := arrays.ContainsString(medicineNameList, medicine.Name)
			if index == -1 {
				//-1说明不存在
				medicineNameList = append(medicineNameList, medicine.Name)
			}
		}
	}

	return medicineNameList, otherMedicineNameList, nil
}

func FormatBoolZh(b bool) string {
	if b {
		return "是"
	}
	return "否"
}

func GetFormType(customerID string, envID string, cohortID string, applicationType int, id string) (models.Field, error) {
	var field models.Field
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		//"fields":      bson.M{"$elemMatch": bson.M{"application_type": applicationType}},
	}

	if cohortID != "" {
		filter["cohort_id"] = cohortOID
	}
	var form models.Form
	if err := DB.Collection("form").FindOne(nil, filter).Decode(&form); err != nil && err != mongo.ErrNoDocuments {
		return field, errors.WithStack(err)
	}
	opts := options.Find().SetProjection(bson.M{
		"info": 1,
	})
	type subjectResp struct {
		ID   primitive.ObjectID `json:"id" bson:"_id"`
		Info []models.Info      `json:"info"`
	}
	subjects := make([]*subjectResp, 0)
	cursor, err := DB.Collection("subject").Find(nil, filter, opts)
	if err != nil {
		return field, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjects)
	if err != nil {
		return field, errors.WithStack(err)
	}
	for i := range form.Fields {
		//状态默认值
		if form.Fields[i].Status == nil {
			status := 1
			form.Fields[i].Status = &status
		}
		// 应用类型 默认值
		if form.Fields[i].ApplicationType == nil {
			at := 1
			form.Fields[i].ApplicationType = &at
		}

		//判断是否有subject引用
		if form.Fields[i].Type == "select" || form.Fields[i].Type == "radio" || form.Fields[i].Type == "checkbox" || form.Fields[i].IsCalc == true {
			for _, subject := range subjects {
				for _, info := range subject.Info {
					if info.Name == form.Fields[i].Name {
						for j := range form.Fields[i].Options {
							if form.Fields[i].Type == "checkbox" && info.Value != nil {
								for _, v := range info.Value.(primitive.A) {
									if form.Fields[i].Options[j].Value == v {
										form.Fields[i].Options[j].Disable = true
									}
								}
							} else if form.Fields[i].Options[j].Value == info.Value {
								form.Fields[i].Options[j].Disable = true
							}
						}
					}
				}
			}
		}
		apt := *form.Fields[i].ApplicationType
		status := *form.Fields[i].Status
		if apt == applicationType && status != 2 {
			if id == form.Fields[i].ID.Hex() {
				field = form.Fields[i]
			}
		}
	}

	return field, nil
}
