package test

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"strings"
	"testing"
)

func GetProjectInfo(t *testing.T) {
	type ProjectUser struct {
		models.Project `json:",inline" bson:",inline"`
		AdminUser      []models.User `bson:"administrators_user"`
		IPUser         []models.User `bson:"ip_user"`
	}
	var projects []ProjectUser
	cursor, err := DB.Collection("project").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{}}},
		{{"$lookup", bson.M{
			"from":         "user",
			"localField":   "administrators",
			"foreignField": "_id",
			"as":           "administrators_user",
		}}},
		{{"$lookup", bson.M{
			"from": "project_role_permission",
			"let": bson.M{
				"id": "$_id",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"name": "IP Officer"}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$project_id", "$$id"}}}},
			},
			"as": "project_role_permission",
		}}},
		{{"$lookup", bson.M{
			"from":         "user_project_environment",
			"localField":   "project_role_permission._id",
			"foreignField": "roles",
			"as":           "user_project_environment",
		}}},
		{{"$lookup", bson.M{
			"from":         "user",
			"localField":   "user_project_environment.user_id",
			"foreignField": "_id",
			"as":           "ip_user",
		}}},
	})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		panic(err)
	}
	fmt.Println(len(projects))
	content := [][]interface{}{}
	for _, project := range projects {
		if project.CustomerID.Hex() == "60c02efc1f287c54a328053b" || project.CustomerID.Hex() == "60b9e295dab195431d802eb3" {
			continue
		}
		item := []interface{}{}
		adminUser := slice.Map(project.AdminUser, func(index int, item models.User) string {
			return item.Email
		})
		ipUser := slice.Map(project.IPUser, func(index int, item models.User) string {
			return item.Email
		})
		item = append(item, project.Name)
		item = append(item, project.Number)
		item = append(item, strings.Join(adminUser, ","))
		item = append(item, strings.Join(ipUser, ","))
		content = append(content, item)
	}
	f := excelize.NewFile()
	title := []interface{}{"项目名称", "项目编号", "项目管理员", "IP Officer"}
	tools.ExportSheet(f, "Sheet1", title, content)
	f.SaveAs("xxxxx.xlsx")
}
