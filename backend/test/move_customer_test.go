package test

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"testing"
)

// UpdateProjectCustomerId 更新customer_id字段
func UpdateProjectCustomerId(t *testing.T) {

	projectId := "67f36f2d6d72036d92252817"
	newCustomerId := "68075a7fe1cc848d5bac657a"

	projectOID, _ := primitive.ObjectIDFromHex(projectId)
	newCustomerOID, _ := primitive.ObjectIDFromHex(newCustomerId)

	update := bson.M{
		"$set": bson.M{
			"customer_id": newCustomerOID,
		},
	}

	//ali_request 集合
	filter := bson.M{"local_project_id": projectOID}
	if _, err := DB.Collection("ali_request").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//app_task_notice 集合
	filter = bson.M{"project_id": projectOID}
	if _, err := DB.Collection("app_task_notice").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//approval_number 集合
	if _, err := DB.Collection("approval_number").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//approval_process 集合
	if _, err := DB.Collection("approval_process").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//attribute 集合
	if _, err := DB.Collection("attribute").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//barcode_group 集合
	if _, err := DB.Collection("barcode_group").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//barcode_label 集合
	if _, err := DB.Collection("barcode_label").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//barcode_rule 集合
	if _, err := DB.Collection("barcode_rule").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//customer_report_title 集合
	if _, err := DB.Collection("customer_report_title").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//depot_batch_group 集合
	if _, err := DB.Collection("depot_batch_group").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//dispensing 集合
	if _, err := DB.Collection("dispensing").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//dispensing_room_record 集合
	if _, err := DB.Collection("dispensing_room_record").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//drug_configure 集合
	if _, err := DB.Collection("drug_configure").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//drug_configure_setting 集合
	if _, err := DB.Collection("drug_configure_setting").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//drug_package_configure 集合
	if _, err := DB.Collection("drug_package_configure").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//drug_track 集合
	if _, err := DB.Collection("drug_track").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//edc_push 集合
	if _, err := DB.Collection("edc_push").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//edc_push_log 集合
	if _, err := DB.Collection("edc_push_log").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//env_barcode_number 集合
	if _, err := DB.Collection("env_barcode_number").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//form 集合
	if _, err := DB.Collection("form").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//mail_env 集合
	if _, err := DB.Collection("mail_env").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//medicine 集合
	if _, err := DB.Collection("medicine").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//medicine_change_record 集合
	if _, err := DB.Collection("medicine_change_record").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//medicine_freeze 集合
	if _, err := DB.Collection("medicine_freeze").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//medicine_order 集合
	if _, err := DB.Collection("medicine_order").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//medicine_other_institute 集合
	if _, err := DB.Collection("medicine_other_institute").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//medicine_other_key 集合
	if _, err := DB.Collection("medicine_other_key").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//medicine_others 集合
	if _, err := DB.Collection("medicine_others").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//medicine_upload_record 集合
	if _, err := DB.Collection("medicine_upload_record").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//notice_config 集合
	if _, err := DB.Collection("notice_config").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//other_package_number 集合
	if _, err := DB.Collection("other_package_number").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//project 集合
	filter = bson.M{"_id": projectOID}
	if _, err := DB.Collection("project").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//project_multi_language 集合
	filter = bson.M{"project_id": projectOID}
	if _, err := DB.Collection("project_multi_language").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//project_multi_language_translate 集合
	if _, err := DB.Collection("project_multi_language_translate").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//project_notice 集合
	if _, err := DB.Collection("project_notice").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//project_role_permission 集合
	if _, err := DB.Collection("project_role_permission").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//project_site 集合
	if _, err := DB.Collection("project_site").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//project_storehouse 集合
	if _, err := DB.Collection("project_storehouse").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//project_storehouse_validity 集合
	if _, err := DB.Collection("project_storehouse_validity").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//random_design 集合
	if _, err := DB.Collection("random_design").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//random_list 集合
	if _, err := DB.Collection("random_list").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//random_number 集合
	if _, err := DB.Collection("random_number").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//region 集合
	if _, err := DB.Collection("region").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//simulate_random 集合
	if _, err := DB.Collection("simulate_random").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//subject 集合
	if _, err := DB.Collection("subject").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//supply_plan 集合
	if _, err := DB.Collection("supply_plan").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//supply_plan_medicine 集合
	if _, err := DB.Collection("supply_plan_medicine").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//user_depot 集合
	if _, err := DB.Collection("user_depot").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//user_focus_project 集合
	if _, err := DB.Collection("user_focus_project").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//user_project_environment 集合
	if _, err := DB.Collection("user_project_environment").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//user_site 集合
	if _, err := DB.Collection("user_site").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//visit_cycle 集合
	if _, err := DB.Collection("visit_cycle").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//visit_notice 集合
	if _, err := DB.Collection("visit_notice").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	//work_task 集合
	if _, err := DB.Collection("work_task").UpdateMany(nil, filter, update); err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

}
