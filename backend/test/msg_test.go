package test

import (
	"clinflash-irt/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"testing"
	"time"
)

func updateSysMsg(t *testing.T) {
	var data []models.User
	o := &options.FindOptions{Projection: bson.M{"_id": 1}}
	cursor, err := DB.Collection("user").Find(nil, bson.M{}, o)
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		panic(err)
	}
	var readTo []models.Receive
	var unReadTo []models.Receive
	for _, d := range data {
		readTo = append(readTo, models.Receive{
			UserId: d.ID,
			Read:   true,
		})
		unReadTo = append(unReadTo, models.Receive{
			UserId: d.ID,
			Read:   false,
		})
	}
	msg := models.MessageCenter{
		ID:         primitive.NewObjectID(),
		NoticeType: 1,
		Title:      "Clinflash IRT V2.13.0",
		Time:       time.Duration(time.Now().Unix()),
		SystemUpdate: models.SystemMessage{
			Title:     "V2.13.0 发布说明",
			TitleEn:   "V2.13.0 Release Description",
			Content:   "1.支持项目半时区的配置计算，应用于项目内订单等任务的计算；\n2.支持群组研究下，自定义扩展设置再随机研究的组合项目，适用于在同一个项目中，可以完成基本研究和再随机研究属性的复合研究；\n3.基于受试者揭盲的基础上，支持单个研究产品的揭盲，受揭盲权限和盲态管理逻辑控制；\n4.系统增加支持按序列号范围触发订单，满足部分中心可以创建连续编号订单，便于中心研究产品的便捷接收；\n5.对于中心研究产品的不发放天数限制，增加可配置的邮件提醒，帮助相关人员更灵活的管控中心库存；\n6.群组研究中，针对随机前受试者，可根据实际情况变更所属群组关系，同时保留轨迹追溯；\n7.与EDC对接中，支持自定义配置，是否群组/阶段名称不一致，可以阻断随机的流程；\n8.发药配置中，针对公式计算场景中，支持自定义设置公式计算结果的小数位设定；\n9.对项目环境维度的锁定与解锁场景，功能菜单数据操作权限的优化；\n10.支持对客户或项目中的用户，进行批量关闭或解绑；\n11.支持隔离原因的自定义配置；\n12.增加受试者随机顺序号逻辑，支持在列表等页面中添加显示；\n13.系统支持研究产品标签的设计管理，并可与外部打印机链接，进行传输打印；\n14.支持冻结的研究产品编号，允许被登记实际使用；\n15.其他功能内容优化和一些已知问题修复。",
			ContentEn: "1.Support project half-timezone configuration calculation, applied to calculations for orders and tasks within the project.\n2.Support customizable extension settings for Re-randomization studies under group sequential design. Enables composite studies within a single project, allowing completion of both the base study and a re-randomization study phase.\n3.Support for single IP unblinding based on subject unblinding, controlled by unblinding permissions and blinding management logic.\n4.System support for triggering orders by serial number range. Meets requirements where some sites need to create consecutively numbered orders for easier receipt of IP.\n5.Configurable email alerts for site IP Non-Dispensing day limits, and helps relevant personnel manage site inventory more flexibly.\n6.Group assignment change for Pre-Randomized subjects in group sequential studies. Allows changing a subject's group affiliation based on actual circumstances while maintaining audit trail.\n7.In EDC Integration, can customizable blocking of randomization and configurable option to block randomization if group/stage names between systems are inconsistent.\n8.In dispensing configuration, can customizable decimal precision for formula calculation results. Supports setting the number of decimal places for calculated values in formulas.\n9.Related to locking and unlocking scenarios at the project environment level, optimization of data operation permissions for function menus.\n10.Batch deactivation or unbinding of users, supports bulk actions for users within a client or project.\n11.Customizable configuration of quarantine reasons.\n12.Addition of subject randomization sequence number logic, and supports display in lists and other pages.\n13.System supports designing study product labels and transmitting them to external printers to print.\n14.Allow Registration of actual use for frozen IP.\n15.Other functional optimizations and fixes for known issues.",
		},
		To: unReadTo,
	}
	_, err = DB.Collection("message_center").InsertOne(nil, msg)
	if err != nil {
		panic(err)
	}
}
