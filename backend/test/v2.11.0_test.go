package test

import (
	"clinflash-irt/models"
	"fmt"
	"testing"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// 未编号数据清洗 和 未编号轨迹表数据
func HandleMedicineOtherInstitute(t *testing.T) {
	//envID, _ := primitive.ObjectIDFromHex("62187fa0803b7ab90cb018bc")
	medicineOtherInstitutes := make([]models.MedicineOtherInstitute, 0)
	cursor, err := DB.Collection("medicine_other_institute").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &medicineOtherInstitutes)
	if err != nil {
		panic(err)
	}
	for _, medicineOtherInstitute := range medicineOtherInstitutes {
		//轨迹key表数据
		var medicineOtherKey models.MedicineOtherKey
		medicineOtherKey.ID = medicineOtherInstitute.ID
		medicineOtherKey.ProjectID = medicineOtherInstitute.ProjectID
		medicineOtherKey.EnvironmentID = medicineOtherInstitute.EnvironmentID
		medicineOtherKey.CustomerID = medicineOtherInstitute.CustomerID
		medicineOtherKey.SiteID = medicineOtherInstitute.InstituteID
		medicineOtherKey.StorehouseID = medicineOtherInstitute.StorehouseID
		medicineOtherKey.Name = medicineOtherInstitute.Info.Name
		medicineOtherKey.Batch = medicineOtherInstitute.Info.Batch
		medicineOtherKey.ExpireDate = medicineOtherInstitute.Info.ExpireDate
		medicineOtherKey.Spec = medicineOtherInstitute.Info.Spec
		DB.Collection("medicine_other_key").InsertOne(nil, medicineOtherKey)

		var addOtherMedicines []interface{}
		if medicineOtherInstitute.Info.Count > 0 { //可用
			var otherMedicine models.OtherMedicine
			otherMedicine.ProjectID = medicineOtherInstitute.ProjectID
			otherMedicine.EnvironmentID = medicineOtherInstitute.EnvironmentID
			otherMedicine.CustomerID = medicineOtherInstitute.CustomerID
			otherMedicine.SiteID = medicineOtherInstitute.InstituteID
			otherMedicine.StorehouseID = medicineOtherInstitute.StorehouseID
			otherMedicine.SubjectID = medicineOtherInstitute.SubjectID
			otherMedicine.Name = medicineOtherInstitute.Info.Name
			otherMedicine.BatchNumber = medicineOtherInstitute.Info.Batch
			otherMedicine.ExpirationDate = medicineOtherInstitute.Info.ExpireDate
			otherMedicine.Spec = medicineOtherInstitute.Info.Spec
			otherMedicine.Status = 1
			//otherMedicine.Used = 0
			for i := 0; i < medicineOtherInstitute.Info.Count; i++ {
				otherMedicine.ID = primitive.NewObjectID()
				addOtherMedicines = append(addOtherMedicines, otherMedicine)
			}
		}
		if medicineOtherInstitute.Info.ToBeConfirmCount > 0 { //待确认
			var otherMedicine models.OtherMedicine
			otherMedicine.ProjectID = medicineOtherInstitute.ProjectID
			otherMedicine.EnvironmentID = medicineOtherInstitute.EnvironmentID
			otherMedicine.CustomerID = medicineOtherInstitute.CustomerID
			otherMedicine.SiteID = medicineOtherInstitute.InstituteID
			otherMedicine.StorehouseID = medicineOtherInstitute.StorehouseID
			otherMedicine.SubjectID = medicineOtherInstitute.SubjectID
			otherMedicine.Name = medicineOtherInstitute.Info.Name
			otherMedicine.BatchNumber = medicineOtherInstitute.Info.Batch
			otherMedicine.ExpirationDate = medicineOtherInstitute.Info.ExpireDate
			otherMedicine.Spec = medicineOtherInstitute.Info.Spec
			otherMedicine.Status = 11
			//otherMedicine.Used = 0
			for i := 0; i < medicineOtherInstitute.Info.ToBeConfirmCount; i++ {
				otherMedicine.ID = primitive.NewObjectID()
				addOtherMedicines = append(addOtherMedicines, otherMedicine)
			}
		}
		if medicineOtherInstitute.Info.ToBeSendCount > 0 { //已确认
			var otherMedicine models.OtherMedicine
			otherMedicine.ProjectID = medicineOtherInstitute.ProjectID
			otherMedicine.EnvironmentID = medicineOtherInstitute.EnvironmentID
			otherMedicine.CustomerID = medicineOtherInstitute.CustomerID
			otherMedicine.SiteID = medicineOtherInstitute.InstituteID
			otherMedicine.StorehouseID = medicineOtherInstitute.StorehouseID
			otherMedicine.SubjectID = medicineOtherInstitute.SubjectID
			otherMedicine.Name = medicineOtherInstitute.Info.Name
			otherMedicine.BatchNumber = medicineOtherInstitute.Info.Batch
			otherMedicine.ExpirationDate = medicineOtherInstitute.Info.ExpireDate
			otherMedicine.Spec = medicineOtherInstitute.Info.Spec
			otherMedicine.Status = 2
			//otherMedicine.Used = 0
			for i := 0; i < medicineOtherInstitute.Info.ToBeSendCount; i++ {
				otherMedicine.ID = primitive.NewObjectID()
				addOtherMedicines = append(addOtherMedicines, otherMedicine)
			}
		}
		if medicineOtherInstitute.Info.InTransitCount > 0 { //已运送
			var otherMedicine models.OtherMedicine
			otherMedicine.ProjectID = medicineOtherInstitute.ProjectID
			otherMedicine.EnvironmentID = medicineOtherInstitute.EnvironmentID
			otherMedicine.CustomerID = medicineOtherInstitute.CustomerID
			otherMedicine.SiteID = medicineOtherInstitute.InstituteID
			otherMedicine.StorehouseID = medicineOtherInstitute.StorehouseID
			otherMedicine.SubjectID = medicineOtherInstitute.SubjectID
			otherMedicine.Name = medicineOtherInstitute.Info.Name
			otherMedicine.BatchNumber = medicineOtherInstitute.Info.Batch
			otherMedicine.ExpirationDate = medicineOtherInstitute.Info.ExpireDate
			otherMedicine.Spec = medicineOtherInstitute.Info.Spec
			otherMedicine.Status = 3
			//otherMedicine.Used = 0
			for i := 0; i < medicineOtherInstitute.Info.InTransitCount; i++ {
				otherMedicine.ID = primitive.NewObjectID()
				addOtherMedicines = append(addOtherMedicines, otherMedicine)
			}
		}
		if medicineOtherInstitute.Info.QuarantinedCount > 0 { //已隔离
			var otherMedicine models.OtherMedicine
			otherMedicine.ProjectID = medicineOtherInstitute.ProjectID
			otherMedicine.EnvironmentID = medicineOtherInstitute.EnvironmentID
			otherMedicine.CustomerID = medicineOtherInstitute.CustomerID
			otherMedicine.SiteID = medicineOtherInstitute.InstituteID
			otherMedicine.StorehouseID = medicineOtherInstitute.StorehouseID
			otherMedicine.SubjectID = medicineOtherInstitute.SubjectID
			otherMedicine.Name = medicineOtherInstitute.Info.Name
			otherMedicine.BatchNumber = medicineOtherInstitute.Info.Batch
			otherMedicine.ExpirationDate = medicineOtherInstitute.Info.ExpireDate
			otherMedicine.Spec = medicineOtherInstitute.Info.Spec
			otherMedicine.Status = 4
			//otherMedicine.Used = 0
			for i := 0; i < medicineOtherInstitute.Info.QuarantinedCount; i++ {
				otherMedicine.ID = primitive.NewObjectID()
				addOtherMedicines = append(addOtherMedicines, otherMedicine)
			}
		}
		if medicineOtherInstitute.Info.UsedCount > 0 { //已使用
			var otherMedicine models.OtherMedicine
			otherMedicine.ProjectID = medicineOtherInstitute.ProjectID
			otherMedicine.EnvironmentID = medicineOtherInstitute.EnvironmentID
			otherMedicine.CustomerID = medicineOtherInstitute.CustomerID
			otherMedicine.SiteID = medicineOtherInstitute.InstituteID
			otherMedicine.StorehouseID = medicineOtherInstitute.StorehouseID
			otherMedicine.SubjectID = medicineOtherInstitute.SubjectID
			otherMedicine.Name = medicineOtherInstitute.Info.Name
			otherMedicine.BatchNumber = medicineOtherInstitute.Info.Batch
			otherMedicine.ExpirationDate = medicineOtherInstitute.Info.ExpireDate
			otherMedicine.Spec = medicineOtherInstitute.Info.Spec
			otherMedicine.Status = 5
			//otherMedicine.Used = 0
			for i := 0; i < medicineOtherInstitute.Info.UsedCount; i++ {
				otherMedicine.ID = primitive.NewObjectID()
				addOtherMedicines = append(addOtherMedicines, otherMedicine)
			}
		}
		if medicineOtherInstitute.Info.LostCount > 0 { //丢失/作废
			var otherMedicine models.OtherMedicine
			otherMedicine.ProjectID = medicineOtherInstitute.ProjectID
			otherMedicine.EnvironmentID = medicineOtherInstitute.EnvironmentID
			otherMedicine.CustomerID = medicineOtherInstitute.CustomerID
			otherMedicine.SiteID = medicineOtherInstitute.InstituteID
			otherMedicine.StorehouseID = medicineOtherInstitute.StorehouseID
			otherMedicine.SubjectID = medicineOtherInstitute.SubjectID
			otherMedicine.Name = medicineOtherInstitute.Info.Name
			otherMedicine.BatchNumber = medicineOtherInstitute.Info.Batch
			otherMedicine.ExpirationDate = medicineOtherInstitute.Info.ExpireDate
			otherMedicine.Spec = medicineOtherInstitute.Info.Spec
			otherMedicine.Status = 6
			//otherMedicine.Used = 0
			for i := 0; i < medicineOtherInstitute.Info.LostCount; i++ {
				otherMedicine.ID = primitive.NewObjectID()
				addOtherMedicines = append(addOtherMedicines, otherMedicine)
			}
		}
		if medicineOtherInstitute.Info.ApplyCount > 0 { //申请
			var otherMedicine models.OtherMedicine
			otherMedicine.ProjectID = medicineOtherInstitute.ProjectID
			otherMedicine.EnvironmentID = medicineOtherInstitute.EnvironmentID
			otherMedicine.CustomerID = medicineOtherInstitute.CustomerID
			otherMedicine.SiteID = medicineOtherInstitute.InstituteID
			otherMedicine.StorehouseID = medicineOtherInstitute.StorehouseID
			otherMedicine.SubjectID = medicineOtherInstitute.SubjectID
			otherMedicine.Name = medicineOtherInstitute.Info.Name
			otherMedicine.BatchNumber = medicineOtherInstitute.Info.Batch
			otherMedicine.ExpirationDate = medicineOtherInstitute.Info.ExpireDate
			otherMedicine.Spec = medicineOtherInstitute.Info.Spec
			otherMedicine.Status = 13
			//otherMedicine.Used = 0
			for i := 0; i < medicineOtherInstitute.Info.ApplyCount; i++ {
				otherMedicine.ID = primitive.NewObjectID()
				addOtherMedicines = append(addOtherMedicines, otherMedicine)
			}
		}
		if medicineOtherInstitute.Info.ExpiredCount > 0 { //过期
			var otherMedicine models.OtherMedicine
			otherMedicine.ProjectID = medicineOtherInstitute.ProjectID
			otherMedicine.EnvironmentID = medicineOtherInstitute.EnvironmentID
			otherMedicine.CustomerID = medicineOtherInstitute.CustomerID
			otherMedicine.SiteID = medicineOtherInstitute.InstituteID
			otherMedicine.StorehouseID = medicineOtherInstitute.StorehouseID
			otherMedicine.SubjectID = medicineOtherInstitute.SubjectID
			otherMedicine.Name = medicineOtherInstitute.Info.Name
			otherMedicine.BatchNumber = medicineOtherInstitute.Info.Batch
			otherMedicine.ExpirationDate = medicineOtherInstitute.Info.ExpireDate
			otherMedicine.Spec = medicineOtherInstitute.Info.Spec
			otherMedicine.Status = 7
			//otherMedicine.Used = 0
			for i := 0; i < medicineOtherInstitute.Info.ExpiredCount; i++ {
				otherMedicine.ID = primitive.NewObjectID()
				addOtherMedicines = append(addOtherMedicines, otherMedicine)
			}
		}
		if medicineOtherInstitute.Info.FrozenCount > 0 { //冻结
			var otherMedicine models.OtherMedicine
			otherMedicine.ProjectID = medicineOtherInstitute.ProjectID
			otherMedicine.EnvironmentID = medicineOtherInstitute.EnvironmentID
			otherMedicine.CustomerID = medicineOtherInstitute.CustomerID
			otherMedicine.SiteID = medicineOtherInstitute.InstituteID
			otherMedicine.StorehouseID = medicineOtherInstitute.StorehouseID
			otherMedicine.SubjectID = medicineOtherInstitute.SubjectID
			otherMedicine.Name = medicineOtherInstitute.Info.Name
			otherMedicine.BatchNumber = medicineOtherInstitute.Info.Batch
			otherMedicine.ExpirationDate = medicineOtherInstitute.Info.ExpireDate
			otherMedicine.Spec = medicineOtherInstitute.Info.Spec
			otherMedicine.Status = 14
			//otherMedicine.Used = 0
			for i := 0; i < medicineOtherInstitute.Info.FrozenCount; i++ {
				otherMedicine.ID = primitive.NewObjectID()
				addOtherMedicines = append(addOtherMedicines, otherMedicine)
			}
		}
		if medicineOtherInstitute.Info.LockedCount > 0 { //锁定
			var otherMedicine models.OtherMedicine
			otherMedicine.ProjectID = medicineOtherInstitute.ProjectID
			otherMedicine.EnvironmentID = medicineOtherInstitute.EnvironmentID
			otherMedicine.CustomerID = medicineOtherInstitute.CustomerID
			otherMedicine.SiteID = medicineOtherInstitute.InstituteID
			otherMedicine.StorehouseID = medicineOtherInstitute.StorehouseID
			otherMedicine.SubjectID = medicineOtherInstitute.SubjectID
			otherMedicine.Name = medicineOtherInstitute.Info.Name
			otherMedicine.BatchNumber = medicineOtherInstitute.Info.Batch
			otherMedicine.ExpirationDate = medicineOtherInstitute.Info.ExpireDate
			otherMedicine.Spec = medicineOtherInstitute.Info.Spec
			otherMedicine.Status = 20
			//otherMedicine.Used = 0
			for i := 0; i < medicineOtherInstitute.Info.LockedCount; i++ {
				otherMedicine.ID = primitive.NewObjectID()
				addOtherMedicines = append(addOtherMedicines, otherMedicine)
			}
		}
		DB.Collection("medicine_others").InsertMany(nil, addOtherMedicines)
	}
}

// 清洗订单中未编号数据
func HandleMedicineOrder(t *testing.T) {
	//envID, _ := primitive.ObjectIDFromHex("62187fa0803b7ab90cb018bc")
	medicineOrders := make([]models.MedicineOrder, 0)
	cursor, err := DB.Collection("medicine_order").Find(nil, bson.M{"other_medicines.0": bson.M{"$exists": true}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &medicineOrders)
	if err != nil {
		panic(err)
	}
	for _, medicineOrder := range medicineOrders {
		orderType := medicineOrder.Type
		orderStatus := medicineOrder.Status
		match := bson.M{}
		//updateFlag := true
		if orderType == 1 || orderType == 3 || orderType == 5 { // 订单起运地“仓库” -> 1仓库->中心，3仓库->仓库，5仓库->受试者
			if orderStatus == 5 || orderStatus == 8 || orderStatus == 9 { //5已取消订单 || 8已终止订单 || 9已关闭订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "storehouse_id": medicineOrder.SendID, "status": 1}
				//updateFlag = false
			} else if orderStatus == 6 { //6待确认订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "storehouse_id": medicineOrder.SendID, "status": 11, "used": 0}
			} else if orderStatus == 4 { //4已丢失/作废订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "storehouse_id": medicineOrder.SendID, "status": 6, "used": 0}
			}
		} else { //  订单起运地“中心”
			if orderStatus == 5 || orderStatus == 8 || orderStatus == 9 { //5已取消订单 || 8已终止订单 || 9已关闭订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "site_id": medicineOrder.SendID, "status": 1}
				//updateFlag = false
			} else if orderStatus == 6 { //6待确认订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "site_id": medicineOrder.SendID, "status": 11, "used": 0}
			} else if orderStatus == 4 { //4已丢失/作废订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "site_id": medicineOrder.SendID, "status": 6, "used": 0}
			}
		}

		if orderType == 1 || orderType == 2 { // 订单目的地“中心” -> 1仓库->中心，2中心->中心
			if orderStatus == 1 { //1已确认订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "site_id": medicineOrder.ReceiveID, "status": 2, "used": 0}
			} else if orderStatus == 2 { //2已运送订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "site_id": medicineOrder.ReceiveID, "status": 3, "used": 0}
			} else if orderStatus == 3 { //3已接收
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "site_id": medicineOrder.ReceiveID, "status": 1}
			}
		} else if orderType == 3 || orderType == 4 { //  订单目的地“仓库”
			if orderStatus == 1 { //1已确认订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "storehouse_id": medicineOrder.ReceiveID, "status": 2, "used": 0}
			} else if orderStatus == 2 { //2已运送订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "storehouse_id": medicineOrder.ReceiveID, "status": 3, "used": 0}
			} else if orderStatus == 3 { //3已接收
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "storehouse_id": medicineOrder.ReceiveID, "status": 1}
			}
		} else if orderType == 5 { //  仓库——>“受试者”
			if orderStatus == 1 { //1已确认订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "storehouse_id": medicineOrder.SendID, "status": 2, "used": 0}
			} else if orderStatus == 2 { //2已运送订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "storehouse_id": medicineOrder.SendID, "status": 3, "used": 0}
			} else if orderStatus == 3 { //3已接收
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "storehouse_id": medicineOrder.SendID, "status": 5, "used": 0}
			}
		} else if orderType == 6 { //  中心——>“受试者”
			if orderStatus == 1 { //1已确认订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "site_id": medicineOrder.SendID, "status": 2, "used": 0}
			} else if orderStatus == 2 { //2已运送订单
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "site_id": medicineOrder.SendID, "status": 3, "used": 0}
			} else if orderStatus == 3 { //3已接收
				match = bson.M{"env_id": medicineOrder.EnvironmentID, "site_id": medicineOrder.SendID, "status": 5}
			}
		}

		//usedOtherIDs := make([]primitive.ObjectID, 0)
		otherIDs := make([]primitive.ObjectID, 0)
		for _, otherMedicine := range medicineOrder.OtherMedicines {
			count := otherMedicine.UseCount
			if orderStatus == 3 {
				count = otherMedicine.ReceiveCount
			}
			if count > 0 {
				match["name"] = otherMedicine.Name
				match["expiration_date"] = otherMedicine.ExpireDate
				match["batch_number"] = otherMedicine.Batch
				var medicineOthers []models.MedicineOther
				pipeline := mongo.Pipeline{
					{{Key: "$match", Value: match}},
					{{Key: "$limit", Value: count}},
				}
				cursor, err := DB.Collection("medicine_others").Aggregate(nil, pipeline)
				if err != nil {
					panic(err)
				}
				err = cursor.All(nil, &medicineOthers)
				if err != nil {
					panic(err)
				}

				if len(medicineOthers) != count {
					fmt.Println("订单号", medicineOrder.OrderNumber)
				}

				for _, other := range medicineOthers {
					otherIDs = append(otherIDs, other.ID)
				}
			}

			// if orderStatus == 3 && otherMedicine.UseCount != otherMedicine.ReceiveCount {
			// 	frozenCount := otherMedicine.UseCount - otherMedicine.ReceiveCount
			// 	match["status"] = 4
			// 	var medicineOthers []models.MedicineOther
			// 	pipeline := mongo.Pipeline{
			// 		{{Key: "$match", Value: match}},
			// 		{{Key: "$limit", Value: frozenCount}},
			// 	}
			// 	cursor, err := DB.Collection("medicine_others").Aggregate(nil, pipeline)
			// 	if err != nil {
			// 		panic(err)
			// 	}
			// 	err = cursor.All(nil, &medicineOthers)
			// 	if err != nil {
			// 		panic(err)
			// 	}

			// 	if len(medicineOthers) != frozenCount {
			// 		panic(err)
			// 	}

			// 	for _, other := range medicineOthers {
			// 		otherIDs = append(otherIDs, other.ID)
			// 	}
			// }
		}

		//更新订单上的other_medicines_new 字段
		_, err := DB.Collection("medicine_order").UpdateMany(nil, bson.M{"_id": medicineOrder.ID}, bson.M{"$set": bson.M{"other_medicines_new": otherIDs, "other_medicines_history_new": otherIDs}})
		if err != nil {
			panic(err)
		}

		//更新未编号研究产品上的orderID
		_, err = DB.Collection("medicine_others").UpdateMany(nil, bson.M{"_id": bson.M{"$in": otherIDs}}, bson.M{"$set": bson.M{"order_id": medicineOrder.ID}})
		if err != nil {
			panic(err)
		}
		//
		_, err = DB.Collection("medicine_others").UpdateMany(nil, bson.M{"_id": bson.M{"$in": otherIDs, "status": bson.M{"$ne": bson.A{1, 5}}}}, bson.M{"$set": bson.M{"used": 1}})
		if err != nil {
			panic(err)
		}
	}
}

// 清洗隔离管理中得数据
func HandldMedicineFreeze(t *testing.T) {
	//envID, _ := primitive.ObjectIDFromHex("62187fa0803b7ab90cb018bc")
	medicineFreezes := make([]models.MedicineFreeze, 0)
	cursor, err := DB.Collection("medicine_freeze").Find(nil, bson.M{"other_history.0": bson.M{"$exists": true}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &medicineFreezes)
	if err != nil {
		panic(err)
	}
	for _, medicineFreeze := range medicineFreezes {
		updateOrderIDs := make([]primitive.ObjectID, 0)
		otherIDs := make([]primitive.ObjectID, 0)
		quarantinedIDs := make([]primitive.ObjectID, 0)
		for _, otherHistory := range medicineFreeze.OtherHistory {
			match := bson.M{"env_id": medicineFreeze.EnvironmentID, "name": otherHistory.Name, "expiration_date": otherHistory.ExpireDate, "batch_number": otherHistory.Batch}
			var flag = false
			var otherMedicineInfo models.FreezeOtherMedicine
			for _, otherMedicine := range medicineFreeze.OtherMedicines {
				if otherHistory.Name == otherMedicine.Name && otherHistory.ExpireDate == otherMedicine.ExpireDate && otherHistory.Batch == otherMedicine.Batch {
					flag = true
					otherMedicineInfo = otherMedicine
					break
				}
			}
			if medicineFreeze.InstituteType == 1 { //中心
				match["site_id"] = medicineFreeze.InstituteID
			} else { //仓库
				match["storehouse_id"] = medicineFreeze.InstituteID
			}

			if flag { //没有全部解隔离
				if otherMedicineInfo.QuarantinedCount > 0 {
					match["status"] = 4
					match["used"] = 0
					var medicineOthers []models.MedicineOther
					pipeline := mongo.Pipeline{
						{{Key: "$match", Value: match}},
						{{Key: "$limit", Value: otherMedicineInfo.QuarantinedCount}},
					}
					cursor, err := DB.Collection("medicine_others").Aggregate(nil, pipeline)
					if err != nil {
						panic(err)
					}
					err = cursor.All(nil, &medicineOthers)
					if err != nil {
						panic(err)
					}

					for _, other := range medicineOthers {
						quarantinedIDs = append(quarantinedIDs, other.ID)
						updateOrderIDs = append(updateOrderIDs, other.ID)
						otherIDs = append(otherIDs, other.ID)
					}

					//更新未编号研究产品上的orderID
					_, err = DB.Collection("medicine_others").UpdateMany(nil, bson.M{"_id": bson.M{"$in": quarantinedIDs}}, bson.M{"$set": bson.M{"used": 1}})
					if err != nil {
						panic(err)
					}
				}
				if otherMedicineInfo.Count > 0 {
					match["status"] = 1
					var medicineOthers []models.MedicineOther
					pipeline := mongo.Pipeline{
						{{Key: "$match", Value: match}},
						{{Key: "$limit", Value: otherMedicineInfo.Count}},
					}
					cursor, err := DB.Collection("medicine_others").Aggregate(nil, pipeline)
					if err != nil {
						panic(err)
					}
					err = cursor.All(nil, &medicineOthers)
					if err != nil {
						panic(err)
					}

					for _, other := range medicineOthers {
						otherIDs = append(otherIDs, other.ID)
					}
				}
				if otherMedicineInfo.LostCount > 0 {
					match["status"] = 6
					match["used"] = 0
					var medicineOthers []models.MedicineOther
					pipeline := mongo.Pipeline{
						{{Key: "$match", Value: match}},
						{{Key: "$limit", Value: otherMedicineInfo.LostCount}},
					}
					cursor, err := DB.Collection("medicine_others").Aggregate(nil, pipeline)
					if err != nil {
						panic(err)
					}
					err = cursor.All(nil, &medicineOthers)
					if err != nil {
						panic(err)
					}

					for _, other := range medicineOthers {
						otherIDs = append(otherIDs, other.ID)
						updateOrderIDs = append(updateOrderIDs, other.ID)
					}
				}
			} else { //已全部解隔离
				var medicineOthers []models.MedicineOther
				pipeline := mongo.Pipeline{
					{{Key: "$match", Value: match}},
					{{Key: "$limit", Value: otherHistory.QuarantinedCount}},
				}
				cursor, err := DB.Collection("medicine_others").Aggregate(nil, pipeline)
				if err != nil {
					panic(err)
				}
				err = cursor.All(nil, &medicineOthers)
				if err != nil {
					panic(err)
				}

				for _, other := range medicineOthers {
					otherIDs = append(otherIDs, other.ID)
				}
			}
		}
		//更新
		_, err := DB.Collection("medicine_freeze").UpdateMany(nil, bson.M{"_id": medicineFreeze.ID}, bson.M{"$set": bson.M{"other_medicines_new": quarantinedIDs, "other_history_new": otherIDs}})
		if err != nil {
			panic(err)
		}
		//更新未编号研究产品上的orderID
		_, err = DB.Collection("medicine_others").UpdateMany(nil, bson.M{"_id": bson.M{"$in": updateOrderIDs}}, bson.M{"$set": bson.M{"used": 1}})
		if err != nil {
			panic(err)
		}
	}
}

// 清洗发药中的数据
func HandleDispensing(t *testing.T) {
	//envID1, _ := primitive.ObjectIDFromHex("66a08bddaa0da958f1cf7256")
	filter := bson.M{
		"$or": bson.A{
			bson.M{"other_dispensing_medicines.0": bson.M{"$exists": true}},
			bson.M{"real_other_dispensing_medicines.0": bson.M{"$exists": true}},
			bson.M{"replace_other_medicines.0": bson.M{"$exists": true}},
		},
		//"env_id": envID1,
	}
	dispensings := make([]models.Dispensing, 0)
	cursor, err := DB.Collection("dispensing").Find(nil, filter)
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &dispensings)
	if err != nil {
		panic(err)
	}

	othersMap := make(map[primitive.ObjectID]models.MedicineOtherInstitute)
	var medicineOthers []models.MedicineOtherInstitute
	// 按过期时间，获取不同批次的非编号药物。
	cursor, err = DB.Collection("medicine_other_institute").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &medicineOthers)
	if err != nil {
		panic(err)
	}
	for _, other := range medicineOthers {
		othersMap[other.ID] = other
	}

	for _, dispensing := range dispensings {
		//判断发药信息有没有
		var othersDispensingMedicines []models.OthersDispensingMedicine
		updateOrderIDs := make([]primitive.ObjectID, 0)
		if dispensing.OtherDispensingMedicines != nil && len(dispensing.OtherDispensingMedicines) > 0 {
			for _, other := range dispensing.OtherDispensingMedicines {
				otherMatch := bson.M{"env_id": dispensing.EnvironmentID, "name": other.Name, "expiration_date": other.ExpireDate, "batch_number": other.Batch, "status": 5, "used": 0}
				medicineOtherInstitute := othersMap[other.MedicineOtherID]
				if medicineOtherInstitute.StorehouseID != primitive.NilObjectID {
					otherMatch["storehouse_id"] = medicineOtherInstitute.StorehouseID
				} else {
					otherMatch["site_id"] = medicineOtherInstitute.InstituteID
				}
				var groupOtherMedicineData []map[string]interface{}
				//判断药物列表是否有数据
				groupPipeline := mongo.Pipeline{
					{{Key: "$match", Value: otherMatch}},
					{{Key: "$limit", Value: other.Count}},
					{{Key: "$project", Value: bson.M{"_id": 1}}},
				}
				cursor, err := DB.Collection("medicine_others").Aggregate(nil, groupPipeline)
				err = cursor.All(nil, &groupOtherMedicineData)
				if err != nil {
					fmt.Println(errors.WithStack(err))
				}
				//取对应数量数据
				if other.Count == len(groupOtherMedicineData) {
					for i := 0; i < other.Count; i++ {
						othersDispensingMedicine := models.OthersDispensingMedicine{
							MedicineID:     groupOtherMedicineData[i]["_id"].(primitive.ObjectID),
							Name:           other.Name,
							ExpirationDate: other.ExpireDate,
							BatchNumber:    other.Batch,
							Time:           other.Time,
							Type:           other.Type,
							Label:          other.Label,
							UseFormulas:    other.UseFormulas,
							DoseInfo:       other.DoseInfo,
							OpenSetting:    2,
							DTP:            other.DTP,
						}
						othersDispensingMedicines = append(othersDispensingMedicines, othersDispensingMedicine)
						updateOrderIDs = append(updateOrderIDs, groupOtherMedicineData[i]["_id"].(primitive.ObjectID))
					}
				}

			}
		}
		var realOthersDispensingMedicines []models.OthersDispensingMedicine
		if dispensing.RealOtherDispensingMedicines != nil && len(dispensing.RealOtherDispensingMedicines) > 0 {
			for _, other := range dispensing.RealOtherDispensingMedicines {
				otherMatch := bson.M{"_id": bson.M{"$nin": updateOrderIDs}, "env_id": dispensing.EnvironmentID, "name": other.Name, "expiration_date": other.ExpireDate, "batch_number": other.Batch, "status": 5, "used": 0}
				medicineOtherInstitute := othersMap[other.MedicineOtherID]
				if medicineOtherInstitute.StorehouseID != primitive.NilObjectID {
					otherMatch["storehouse_id"] = medicineOtherInstitute.StorehouseID
				} else {
					otherMatch["site_id"] = medicineOtherInstitute.InstituteID
				}
				var groupOtherMedicineData []map[string]interface{}
				//判断药物列表是否有数据
				groupPipeline := mongo.Pipeline{
					{{Key: "$match", Value: otherMatch}},
					{{Key: "$limit", Value: other.Count}},
					{{Key: "$project", Value: bson.M{"_id": 1}}},
				}
				cursor, err := DB.Collection("medicine_others").Aggregate(nil, groupPipeline)
				err = cursor.All(nil, &groupOtherMedicineData)
				if err != nil {
					fmt.Println(errors.WithStack(err))
				}
				//取对应数量数据
				if other.Count == len(groupOtherMedicineData) {
					for i := 0; i < other.Count; i++ {
						othersDispensingMedicine := models.OthersDispensingMedicine{
							MedicineID:     groupOtherMedicineData[i]["_id"].(primitive.ObjectID),
							Name:           other.Name,
							ExpirationDate: other.ExpireDate,
							BatchNumber:    other.Batch,
							Time:           other.Time,
							Type:           other.Type,
							Label:          other.Label,
							UseFormulas:    other.UseFormulas,
							DoseInfo:       other.DoseInfo,
							OpenSetting:    2,
							DTP:            other.DTP,
							OrderOID:       other.OrderOID,
						}
						realOthersDispensingMedicines = append(realOthersDispensingMedicines, othersDispensingMedicine)
						updateOrderIDs = append(updateOrderIDs, groupOtherMedicineData[i]["_id"].(primitive.ObjectID))
					}
				}

			}
		}
		var replaceOthersMedicines []models.OthersDispensingMedicine
		if dispensing.ReplaceOtherMedicines != nil && len(dispensing.ReplaceOtherMedicines) > 0 {
			for _, other := range dispensing.ReplaceOtherMedicines {
				otherMatch := bson.M{"_id": bson.M{"$nin": updateOrderIDs}, "env_id": dispensing.EnvironmentID, "name": other.Name, "expiration_date": other.ExpireDate, "batch_number": other.Batch, "status": 6, "used": 0}
				medicineOtherInstitute := othersMap[other.MedicineOtherID]
				if medicineOtherInstitute.StorehouseID != primitive.NilObjectID {
					otherMatch["storehouse_id"] = medicineOtherInstitute.StorehouseID
				} else {
					otherMatch["site_id"] = medicineOtherInstitute.InstituteID
				}
				var groupOtherMedicineData []map[string]interface{}
				//判断药物列表是否有数据
				groupPipeline := mongo.Pipeline{
					{{Key: "$match", Value: otherMatch}},
					{{Key: "$limit", Value: other.Count}},
					{{Key: "$project", Value: bson.M{"_id": 1}}},
				}
				cursor, err := DB.Collection("medicine_others").Aggregate(nil, groupPipeline)
				err = cursor.All(nil, &groupOtherMedicineData)
				if err != nil {
					fmt.Println(errors.WithStack(err))
				}
				//取对应数量数据
				if other.Count == len(groupOtherMedicineData) {
					for i := 0; i < other.Count; i++ {
						othersDispensingMedicine := models.OthersDispensingMedicine{
							MedicineID:     groupOtherMedicineData[i]["_id"].(primitive.ObjectID),
							Name:           other.Name,
							ExpirationDate: other.ExpireDate,
							BatchNumber:    other.Batch,
							Time:           other.Time,
							Type:           other.Type,
							Label:          other.Label,
							UseFormulas:    other.UseFormulas,
							DoseInfo:       other.DoseInfo,
							OpenSetting:    2,
							DTP:            other.DTP,
							OrderOID:       other.OrderOID,
						}
						replaceOthersMedicines = append(replaceOthersMedicines, othersDispensingMedicine)
						updateOrderIDs = append(updateOrderIDs, groupOtherMedicineData[i]["_id"].(primitive.ObjectID))
					}
				}

			}
		}
		update := bson.M{
			"$set": bson.M{"others_dispensing_medicines": othersDispensingMedicines, "replace_others_medicines": replaceOthersMedicines, "real_others_dispensing_medicines": realOthersDispensingMedicines},
		}
		DB.Collection("dispensing").UpdateOne(nil, bson.M{"_id": dispensing.ID}, update)
		_, err = DB.Collection("medicine_others").UpdateMany(nil, bson.M{"_id": bson.M{"$in": updateOrderIDs}}, bson.M{"$set": bson.M{"used": 1}})
		if err != nil {
			panic(err)
		}
	}
}

// UpdateOperationVisitSetting 访视管理设置权限
func UpdateOperationVisitSetting(t *testing.T) {
	_, err := DB.Collection("project_role_permission").UpdateMany(nil,
		bson.M{
			"$or": []bson.M{
				bson.M{"name": "IP Officer"},
				bson.M{"name": "IRT Designer"},
			},
		},
		bson.M{
			"$addToSet": bson.M{
				"permissions": bson.M{
					"$each": []string{
						"operation.build.medicine.visit.setting.edit",
						"operation.build.medicine.visit.setting.list",
					},
				},
			},
		},
	)
	if err != nil {
		panic(err)
	}
}

// UpdateOperationDtpRules 历史数据：现有已开通DTP项目应用的配置，清洗到访视流程中。
func UpdateOperationDtpRules(t *testing.T) {

	visitCycleList := make([]models.VisitCycle, 0)
	cursor, err := DB.Collection("visit_cycle").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &visitCycleList)
	if err != nil {
		panic(err)
	}

	if visitCycleList != nil && len(visitCycleList) > 0 {
		for _, visitCycle := range visitCycleList {
			b := false
			if visitCycle.Infos != nil && len(visitCycle.Infos) > 0 {
				for _, info := range visitCycle.Infos {
					if info.DTP {
						b = true
					}
				}
			}
			if visitCycle.ConfigInfo.Infos != nil && len(visitCycle.ConfigInfo.Infos) > 0 {
				for _, info := range visitCycle.ConfigInfo.Infos {
					if info.DTP {
						b = true
					}
				}
			}

			if b {
				filter := bson.M{"customer_id": visitCycle.CustomerID, "project_id": visitCycle.ProjectID, "env_id": visitCycle.EnvironmentID}
				if visitCycle.CohortID != primitive.NilObjectID {
					filter["cohort_id"] = visitCycle.CohortID
				}
				update := bson.M{"$set": bson.M{"info.dtp_rule": 2}}
				_, err = DB.Collection("attribute").UpdateOne(nil, filter, update)
				if err != nil {
					panic(err)
				}
			}
		}
	}
}

// InsertCNEmailLanguageHistory 更新通知设置-基本设置邮件语言配置-
func InsertCNEmailLanguageHistory(t *testing.T) {

	type EmailLanguage struct {
		ProjectNumber string `json:"projectNumber"`
		EnvName       string `json:"envName"`
		Automatic     int    `json:"automatic"` //邮件语言-自动任务 1中文2英文3中英文
		Manual        int    `json:"manual"`    //邮件语言-手动任务 1中文2英文3中英文
	}

	emailLanguageList := []EmailLanguage{
		{ProjectNumber: "GMA102-T2DM-301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "GMA102-T2DM-301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "GMA102-T2DM-301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "PAT-CHINA-303", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "PAT-CHINA-303", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "PAT-CHINA-303", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "A20-202", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "A20-202", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "A20-202", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "COVID-19-PRO-003", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "COVID-19-PRO-003", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "COVID-19-PRO-003", EnvName: "UAT2", Automatic: 2, Manual: 2},
		{ProjectNumber: "COVID-19-PRO-003", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "JY-R105-101", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JY-R105-101", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JY-R105-101", EnvName: "TEST", Automatic: 1, Manual: 1},
		{ProjectNumber: "JY-R105-101", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGTSH003", EnvName: "TEST", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGTSH003", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGTSH003", EnvName: "DEV2", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGTSH003", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGTSH003", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGTSH003", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "201701-IOL-939", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "201701-IOL-939", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "201701-IOL-939", EnvName: "UAT-随机测试", Automatic: 1, Manual: 1},
		{ProjectNumber: "201701-IOL-939", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "201701-IOL-939", EnvName: "PRO", Automatic: 1, Manual: 1},
		{ProjectNumber: "201701-IOL-939", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "GB491-004", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "GB491-004", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "GB491-004", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "GB491-008", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "GB491-008", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "GB491-008", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "PG-011-PN-201", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "PG-011-PN-201", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "PG-011-PN-201", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "QS-JLLMA-202108/QS-JLLMA-LTFU-202108", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "QS-JLLMA-202108/QS-JLLMA-LTFU-202108", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "QS-JLLMA-202108/QS-JLLMA-LTFU-202108", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TG2105FLU", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "FGCL-4592-858", EnvName: "TEST", Automatic: 1, Manual: 1},
		{ProjectNumber: "FGCL-4592-858", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "FGCL-4592-858", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "YT-YLZFL-202012", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "YT-YLZFL-202012", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "YT-YLZFL-202012", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT103", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT103", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT103", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT102", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT102", EnvName: "TEST", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT102", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT102", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JL20001-P001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JL20001-P001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "MH004-CP002CN", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "MH004-CP002CN", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "MH004-CP002CN", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "MH004-CP002CN", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK020", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK020", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK020", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HS627-TBE", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HS627-TBE", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HS627-TBE", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "REGEND001-COPD-201", EnvName: "TEST", Automatic: 1, Manual: 1},
		{ProjectNumber: "REGEND001-COPD-201", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "REGEND001-COPD-201", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "REGEND001-COPD-201", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "REGEND001-COPD-201", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSPL-PL-18-101", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "JSPL-PL-18-101", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "JSPL-PL-18-101", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "ZGJAK025-NA", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "KOR-CHINA-301", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "KOR-CHINA-301", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "KOR-CHINA-301", EnvName: "UAT-2", Automatic: 2, Manual: 2},
		{ProjectNumber: "KOR-CHINA-301", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "3D-197-CN-001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "3D-197-CN-001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "3D-197-CN-001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "NM-22-A-001-YJ-001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "NM-22-A-001-YJ-001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "NM-22-A-001-YJ-001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "4042", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "4042", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "4042", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "ABO1009-CoV.617.2-101-Indonesia", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "ABO1009-CoV.617.2-101-Indonesia", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "ABO1009-CoV.617.2-101-Indonesia", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "ABO1009-CoV.617.2-201-Indonesia", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "ABO1009-CoV.617.2-201-Indonesia", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "ABO1009-CoV.617.2-201-Indonesia", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "BN102-101-Ⅰ期", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "BN102-101-Ⅰ期", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "BN102-101-Ⅰ期", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "32336", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "32336", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "32336", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "BR55-113", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "BR55-113", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "BR55-113", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "21052-BXV", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "21052-BXV", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "21052-BXV", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "EOC202A2102", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "EOC202A2102", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "EOC202A2102", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JX202202-EVT201-QT", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JX202202-EVT201-QT", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JX202202-EVT201-QT", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ABO1015-DP-101-Indonesia", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ABO1015-DP-101-Indonesia", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ABO1015-DP-101-Indonesia", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "64672", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "64672", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "64672", EnvName: "UAT2", Automatic: 2, Manual: 2},
		{ProjectNumber: "64672", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "SNG2111-ICR-1", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SNG2111-ICR-1", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SNG2111-ICR-1", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "BR55-114", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "BR55-114", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "BR55-114", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "RFSAV-PIII", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "RFSAV-PIII", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "RFSAV-PIII", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "2022-ALPL-BE-001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "2022-ALPL-BE-001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "2022-ALPL-BE-001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "QR060127-1-1", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "QR060127-1-1", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "QR060127-1-1", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "QR060127-1-1", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "INS018-055-002", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "INS018-055-002", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "INS018-055-002", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "INS018-055-002", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "INS018-055-002", EnvName: "UAT3", Automatic: 1, Manual: 1},
		{ProjectNumber: "INS018-055-002", EnvName: "UAT4", Automatic: 1, Manual: 1},
		{ProjectNumber: "BAHEAL2022312", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "BAHEAL2022312", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "BAHEAL2022312", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK025", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK025&ZGJAK026", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK025&ZGJAK026", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "A-1062-301", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "A-1062-301", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "A-1062-301", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "CPRO-2201-001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPRO-2201-001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPRO-2201-001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "AMS-H-03-104", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "AMS-H-03-104", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "AMS-H-03-104", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CM310_IIS_SAR05", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CM310_IIS_SAR05", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CM310_IIS_SAR05", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ESCORT-AF", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ESCORT-AF", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ESCORT-AF", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ABO1020-301", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "ABO1020-301", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "ABO1020-301", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "OMN-TB-301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "OMN-TB-301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "OMN-TB-301", EnvName: "PRDO", Automatic: 1, Manual: 1},
		{ProjectNumber: "OMN-TB-301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HLX10IIT10", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HLX10IIT10", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HLX10IIT10", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "BN301-101", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "BN301-101", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "BN301-101", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "YHGT-UC-01", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "YHGT-UC-01", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "YHGT-UC-01", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "YHGT-UC-01", EnvName: "UAT2", Automatic: 2, Manual: 2},
		{ProjectNumber: "QR12000-GXY-2-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "QR12000-GXY-2-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "QR12000-GXY-2-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AB-301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AB-301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AB-301", EnvName: "UAT 2", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AB-301", EnvName: "UAT 3", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AB-301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AB-301", EnvName: "UAT 4", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AB-301", EnvName: "UAT 5", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AB-301", EnvName: "UAT6", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AC-201", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AC-201", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AC-201", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AC-201", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AC-201", EnvName: "UAT3", Automatic: 1, Manual: 1},
		{ProjectNumber: "BG2109-AC-201", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "GMA102-T2DM-302", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "GMA102-T2DM-302", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "GMA102-T2DM-302", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "QY201-Ⅰ-2", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "QY201-Ⅰ-2", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "QY201-Ⅰ-2", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "SNS812CLCT01", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "SNS812CLCT01", EnvName: "DEV2", Automatic: 2, Manual: 2},
		{ProjectNumber: "SNS812CLCT01", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "SNS812CLCT01", EnvName: "UAT2", Automatic: 2, Manual: 2},
		{ProjectNumber: "SNS812CLCT01", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "SD/LC-0001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SD/LC-0001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SD/LC-0001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT104", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT104", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT104", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "LWY21101C", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "LWY21101C", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "GOCS-H101-E02C01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "GOCS-H101-E02C01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "KL120-BE-01-CTP", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "KL120-BE-01-CTP", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "KL120-BE-01-CTP", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "SHRC-CX2101-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SHRC-CX2101-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SHRC-CX2101-01", EnvName: "DEV1", Automatic: 1, Manual: 1},
		{ProjectNumber: "SHRC-CX2101-01", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "SHRC-CX2101-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HX008-III-CRC-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HX008-III-CRC-01", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "HX008-III-CRC-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HX008-III-CRC-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "A210801.CSP", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "A210801.CSP", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "A210801.CSP", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "KFD-TRA16-002", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "KFD-TRA16-002", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "KFD-TRA16-002", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "GST-HG171-II/III-01", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "GST-HG171-II/III-01", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "GST-HG171-II/III-01", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "AK0707-2001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "AK0707-2001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "AK0707-2001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ANG601-1001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ANG601-1001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ANG601-1001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TCXY202201-I", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TCXY202201-I", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TCXY202201-I", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "KXZY-SHEN26-101", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "KXZY-SHEN26-101", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "KXZY-SHEN26-101", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "THDBH130-02-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "THDBH130-02-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "THDBH130-02-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "SYJC-MLSTN-R01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SYJC-MLSTN-R01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SYJC-MLSTN-R01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "GOCS-H101-E02", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "GOCS-H101-E02", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "GOCS-H101-E02", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "AQAL", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "AQAL", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "AQAL", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CNBG-BIBP-VZV-07", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CNBG-BIBP-VZV-07", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CNBG-BIBP-VZV-07", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK031", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK031", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK031", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "NRFR-001-301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "NRFR-001-301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "VPS-HA-CIP", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "VPS-HA-CIP", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "VPS-HA-CIP", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ADC189-2022-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ADC189-2022-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ADC189-2022-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202204WMX", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202204WMX", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202204WMX", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202206LJY", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202206LJY", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202206LJY", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK029", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK029", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK029", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "RBFI1101", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "RBFI1101", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "RBFI1101", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "RBFI1101", EnvName: "TEST", Automatic: 2, Manual: 2},
		{ProjectNumber: "CPMDD0150", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPMDD0150", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPMDD0150", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPASD0001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPASD0001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPASD0001", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPASD0001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "VG081821AC-CN-003", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "VG081821AC-CN-003", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "VG081821AC-CN-003", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "XK-22-A-002-YJ-002", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "XK-22-A-002-YJ-002", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "XK-22-A-002-YJ-002", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "XK-22-A-002-YJ-002", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HPP737-Psoriasis-301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HPP737-Psoriasis-301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HPP737-Psoriasis-301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPSA0290", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPSA0290", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPSA0290", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPSM0360", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPSM0360", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPSM0360", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "KH109-CS01-CRP", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "KH109-CS01-CRP", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "KH109-CS01-CRP", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "KH109-CS01-CRP", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "KXZY-SHEN26-301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "KXZY-SHEN26-301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "KXZY-SHEN26-301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSPL-PL-18-102", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSPL-PL-18-102", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSPL-PL-18-102", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "2021-MATE-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "2021-MATE-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "2021-MATE-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202203LJY", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202203LJY", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202203LJY", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPMDD0100", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPMDD0100", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPMDD0100", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPTRD0060", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPTRD0060", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPTRD0060", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPMDD0050", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPMDD0050", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPMDD0050", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ESR-21-21293", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ESR-21-21293", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ESR-21-21293", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "AVERT", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "AVERT", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "AVERT", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "NX-MFNT-2022-001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "NX-MFNT-2022-001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "NX-MFNT-2022-001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CNBG-BIBP-VZV-08", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CNBG-BIBP-VZV-08", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CNBG-BIBP-VZV-08", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "CNBG-BIBP-VZV-08", EnvName: "UAT3", Automatic: 1, Manual: 1},
		{ProjectNumber: "CNBG-BIBP-VZV-08", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK032 V2.0", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZGJAK032 V2.0", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSPL-PL-5-302", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSPL-PL-5-302", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TCXY202201", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TCXY202201", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TCXY202201", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "SCLN-INF-V-0822", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SCLN-INF-V-0822", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SCLN-INF-V-0822", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "SCLN-INF-V-0822", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "ART-2021-005", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ART-2021-005", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ART-2021-005", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "Z-HLJD-WA-Ⅲ", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "Z-HLJD-WA-Ⅲ", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "Z-HLJD-WA-Ⅲ", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "Z-HLJD-WA-Ⅲ", EnvName: "TEST", Automatic: 1, Manual: 1},
		{ProjectNumber: "CBP-201-CN003", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CBP-201-CN003", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CBP-201-CN003", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "PB718101", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "PB718101", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "PB718101", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CBP-201-CN004", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "CBP-201-CN004", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "CBP-201-CN004", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "CT20230228", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CT20230228", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CT20230228", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPASD0002", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPASD0002", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPASD0002", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPASD0002", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TD-SWYM-2023-CTP", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TD-SWYM-2023-CTP", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TD-SWYM-2023-CTP", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM01-CT301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM01-CT301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "MY008211A-PNH-2-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "MY008211A-PNH-2-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "MY008211A-PNH-2-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "RBHB1203", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "RBHB1203", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "RBHB1203", EnvName: "UAT2", Automatic: 2, Manual: 2},
		{ProjectNumber: "RBHB1203", EnvName: "UAT3", Automatic: 2, Manual: 2},
		{ProjectNumber: "RBHB1203", EnvName: "UAT4", Automatic: 2, Manual: 2},
		{ProjectNumber: "RBHB1203", EnvName: "UAT5", Automatic: 2, Manual: 2},
		{ProjectNumber: "RBHB1203", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "RBHB1203", EnvName: "TEST", Automatic: 2, Manual: 2},
		{ProjectNumber: "RBHB1203", EnvName: "UAT6", Automatic: 2, Manual: 2},
		{ProjectNumber: "SCLN-INF-V-0922", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SCLN-INF-V-0922", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SCLN-INF-V-0922", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "WPV01-CP-23", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "WPV01-CP-23", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "WPV01-CP-23", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "WPV01-CP-23", EnvName: "TEST", Automatic: 1, Manual: 1},
		{ProjectNumber: "WPV01-CP-23", EnvName: "HCTEST", Automatic: 1, Manual: 1},
		{ProjectNumber: "QY201-I-2 (II)", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "QY201-I-2 (II)", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "QY201-I-2 (II)", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JY-R105-202", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JY-R105-202", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JY-R105-202", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JY-R105-202", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "LIANBANG-demo01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "LIANBANG-demo01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT105", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT105", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT105", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT106", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT106", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HTD1801.PCT106", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CBT-102", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CBT-102", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CBT-102", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TTYP01-II-AIS", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TTYP01-II-AIS", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TTYP01-II-AIS", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZBKB-XBYZXFZ-20220426", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZBKB-XBYZXFZ-20220426", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZBKB-XBYZXFZ-20220426", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CBM-MPC-007", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CBM-MPC-007", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CBM-MPC-007", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "CBM-MPC-007", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "BY-D-2110", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "BY-D-2110", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "BY-D-2110", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "BY-D-2110", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "VPS-VA-CIP", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "VPS-VA-CIP", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "BAT-8006-001-CR", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "BAT-8006-001-CR", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "BAT-8006-001-CR", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "0035-N-301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "0035-N-301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "0035-N-301", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "0035-N-301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JZB05DME301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JZB05DME301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JZB05DME301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "SCTV01E-2-CHN-1", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SCTV01E-2-CHN-1", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SCTV01E-2-CHN-1", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "RP902-102", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "RP902-102", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "RP902-102", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CU-20401-203", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CU-20401-203", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CU-20401-203", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CU-20401-203", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSPL-PL-18-103", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSPL-PL-18-103", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TISA-818-23201", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TISA-818-23201", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TISA-818-23201", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "TISA-818-23201", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ACE-106-001", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "ACE-106-001", EnvName: "95", Automatic: 2, Manual: 2},
		{ProjectNumber: "ACE-106-001", EnvName: "TEST1017", Automatic: 2, Manual: 2},
		{ProjectNumber: "ACE-106-001", EnvName: "TEST1027", Automatic: 2, Manual: 2},
		{ProjectNumber: "ACE-106-001", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "ACE-106-001", EnvName: "UAT1", Automatic: 2, Manual: 2},
		{ProjectNumber: "ACE-106-001", EnvName: "UAT2", Automatic: 2, Manual: 2},
		{ProjectNumber: "ACE-106-001", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "ACE-106-001", EnvName: "ForPQ", Automatic: 2, Manual: 2},
		{ProjectNumber: "ACE-106-001", EnvName: "UAT改库", Automatic: 2, Manual: 2},
		{ProjectNumber: "JT202307WX", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202307WX", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202307WX", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ACT22-001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ACT22-001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ACT22-001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "Arthrobot-UKA", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "Arthrobot-UKA", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "Arthrobot-UKA", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HMM0601", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HMM0601", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HMM0601", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "GST-HG131-II-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "GST-HG131-II-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "GST-HG131-II-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "GST-HG131-II-01", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "TXF202301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TXF202301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TXF202301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "LZ901-300", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "LZ901-300", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "LZ901-300", EnvName: "DEV2", Automatic: 1, Manual: 1},
		{ProjectNumber: "LZ901-300", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "LZ901-300", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "VG290131-AU-001", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "VG290131-AU-001", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "VG290131-AU-001", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "VG290131-AU-001", EnvName: "UAT2", Automatic: 2, Manual: 2},
		{ProjectNumber: "BC-U001-ARDS2023", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "BC-U001-ARDS2023", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "BC-U001-ARDS2023", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "BC-U001-ARDS2023", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "BL-CJJY-201", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "BL-CJJY-201", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "BL-CJJY-201", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "093", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "093", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "093", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "093", EnvName: "PROD", Automatic: 1, Manual: 0},
		{ProjectNumber: "JSKN003-302", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSKN003-302", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSKN003-302", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSKN003-302", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "MG-K10-AD-003", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "MG-K10-AD-003", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "MG-K10-AD-003", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "SFKE-TTF-GBM-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SFKE-TTF-GBM-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SFKE-TTF-GBM-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "INI101-PLN-CO-003", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "INI101-PLN-CO-003", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TGRX-326-3001-NSCLC-CN", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TGRX-326-3001-NSCLC-CN", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TGRX-326-3001-NSCLC-CN", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "KN035-CN-017", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "KN035-CN-017", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "KN035-CN-017", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "KN035-CN-017", EnvName: "PROD", Automatic: 1, Manual: 0},
		{ProjectNumber: "ADC189-2022-01-III", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ADC189-2022-01-III", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ADC189-2022-01-III", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "YN001-002", EnvName: "TEST", Automatic: 1, Manual: 1},
		{ProjectNumber: "YN001-002", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "YN001-002", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "YN001-002", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "YN001-002", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "YN001-002", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "NX-PLMWXR-2023-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "NX-PLMWXR-2023-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "NX-PLMWXR-2023-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "SNT-I-VAM-024", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SNT-I-VAM-024", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SNT-I-VAM-024", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "LZ901-330", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "LZ901-330", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "LZ901-330", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "KN-BCG-III", EnvName: "DEV2", Automatic: 1, Manual: 1},
		{ProjectNumber: "KN-BCG-III", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "KN-BCG-III", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JY-R105-202-V2.0", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JY-R105-202-V2.0", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JY-R105-202-V2.0", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "JY-R105-202-V2.0", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202316ZH", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202316ZH", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202316ZH", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSLHC2023-10-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSLHC2023-10-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSLHC2023-10-01", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSLHC2023-10-01", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSLHC2023-10-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TCR1672-II-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TCR1672-II-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TCR1672-II-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "V1.2", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "V1.2", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "V1.2", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT101_1", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT101_1", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT101_1", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT101_2", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT101_2", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT101_2", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "KD6005CT01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "KD6005CT01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "KD6005CT01", EnvName: "PROD", Automatic: 1, Manual: 0},
		{ProjectNumber: "MH004-E-201", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "MH004-E-201", EnvName: "TEST", Automatic: 1, Manual: 1},
		{ProjectNumber: "MH004-E-201", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "MH004-E-201", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "IG3018-23-02-01", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "IG3018-23-02-01", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "IG3018-23-02-01", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "AK0901-2001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "AK0901-2001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "AK0901-2001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSLHC2023-10-02", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSLHC2023-10-02", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JSLHC2023-10-02", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "M70104", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "M70104", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "M70104", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "M70104", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "M70104", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "AMMPSV4-Ⅳ-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "AMMPSV4-Ⅳ-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "AMMPSV4-Ⅳ-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPASD005", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPASD005", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CPASD005", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "SZ-BE-P-008", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SZ-BE-P-008", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SZ-BE-P-008", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "SZ-BE-P-008", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "MHB039A-A-101", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "MHB039A-A-101", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "MHB039A-A-101", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "Kawin-KW053-1", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "Kawin-KW053-1", EnvName: "UAT 1", Automatic: 1, Manual: 1},
		{ProjectNumber: "Kawin-KW053-1", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "Kawin-KW053-1", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "LM302-03-101", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "LM302-03-101", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "LM302-03-101", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "PCiVAC—002", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "PCiVAC—002", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "PCiVAC—002", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HengLi009-Ⅱ", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HengLi009-Ⅱ", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HengLi009-Ⅱ", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CU-20101-305", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CU-20101-305", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CU-20101-305", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "LWY23090C", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "LWY23090C", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "LWY23090C", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "KYHY-DC042-Ⅱ-202402", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "KYHY-DC042-Ⅱ-202402", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "KYHY-DC042-Ⅱ-202402", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "PB119110", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "PB119110", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "PB119110", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HL[HL08]-Ⅱ-2024", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HL[HL08]-Ⅱ-2024", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HL[HL08]-Ⅱ-2024", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "YR001-A03", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "YR001-A03", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "YR001-A03", EnvName: "UAT3", Automatic: 2, Manual: 2},
		{ProjectNumber: "YR001-A03", EnvName: "TEST", Automatic: 2, Manual: 2},
		{ProjectNumber: "YR001-A03", EnvName: "TEST1", Automatic: 2, Manual: 2},
		{ProjectNumber: "YR001-A03", EnvName: "UAT2", Automatic: 2, Manual: 2},
		{ProjectNumber: "YR001-A03", EnvName: "UAT4", Automatic: 2, Manual: 2},
		{ProjectNumber: "YR001-A03", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "JT202317WX", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202317WX", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202317WX", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HZ-H08905-301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HZ-H08905-301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HZ-H08905-301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ESG401-301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ESG401-301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ESG401-301", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "ESG401-301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "GS-MN001-IIT01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "GS-MN001-IIT01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "GS-MN001-IIT01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CU-10101-103", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CU-10101-103", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CU-10101-103", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "113-CSZ01P", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "113-CSZ01P", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "113-CSZ01P", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "NJHD-2023-002", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "NJHD-2023-002", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "NJHD-2023-002", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "NJHD-2023-002", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202405WX", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202405WX", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202405WX", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "DTP test", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "MG-K10-AD-004", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "MG-K10-AD-004", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "MG-K10-AD-004", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "2312INF", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "2312INF", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "2312INF", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZX-7101A-210", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZX-7101A-210", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZX-7101A-210", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG006-002", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG006-002", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG006-002", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "康方DEMO-001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SR750-202", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SR750-202", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "SR1375-204", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "SR1375-204", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "SR1375-204", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "SR1375-204", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG006-003", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG006-003", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG006-003", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG006-003", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "RS-C1001-C-1-1-001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "RS-C1001-C-1-1-001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "RS-C1001-C-1-1-001", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "RS-C1001-C-1-1-001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-003", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-003", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-003", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "XZYY-20240424", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "XZYY-20240424", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "XZYY-20240424", EnvName: "UAT-new", Automatic: 1, Manual: 1},
		{ProjectNumber: "XZYY-20240424", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "RJK-RC1416-102", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "RJK-RC1416-102", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "RJK-RC1416-102", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "2024-760-00CH1", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "2024-760-00CH1", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "2024-760-00CH1", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202409WX", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202409WX", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JT202409WX", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZTJT-ZL-2024-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZTJT-ZL-2024-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZTJT-ZL-2024-01", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZTJT-ZL-2024-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HY310-002", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "HY310-002", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HY310-002", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "VGR-R01-301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "VGR-R01-301", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "VGR-R01-301", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT102-1b", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT102-1b", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT102-1b", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "KY2024-754", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "KY2024-754", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "KY2024-754", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "HY310-004", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "HY310-004", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TJJS01-201", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TJJS01-201", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TJJS01-201", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TISA-818-23102", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TISA-818-23102", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TISA-818-23102", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "KACM001-CTP-IP", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "KACM001-CTP-IP", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "KACM001-CTP-IP", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "H889A-2024-02", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "H889A-2024-02", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "H889A-2024-02", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ONZ-NeoLUNG", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ONZ-NeoLUNG", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ONZ-NeoLUNG", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "2024-013-00CH1", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "2024-013-00CH1", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "2024-013-00CH1", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "43CH2401", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "43CH2401", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "43CH2401", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "TQB2102-II-04", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TQB2102-II-04", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TQB2102-II-04", EnvName: "DEV1", Automatic: 1, Manual: 1},
		{ProjectNumber: "TQB2102-II-04", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-002", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-002", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-002", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "YN001-002R", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "YN001-002R", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "YN001-002R", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "YN001-002R", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "OEBC3", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "OEBC3", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "OEBC3", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "QB-CN-003", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "QB-CN-003", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "QB-CN-003", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-005", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-005", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-005", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TQA3605-II-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TQA3605-II-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TQA3605-II-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-004", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-004", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-004", EnvName: "DEV1", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-004", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZG005-004", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "NX-PLMWXR-2024-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "NX-PLMWXR-2024-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "NX-PLMWXR-2024-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "B240102.CSP", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "B240102.CSP", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "B240102.CSP", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "B240102.CSP", EnvName: "UAT1", Automatic: 1, Manual: 1},
		{ProjectNumber: "B240102.CSP", EnvName: "DEV1", Automatic: 1, Manual: 1},
		{ProjectNumber: "B240102.CSP", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "TR0471101", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "TR0471101", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "TR0471101", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "VSA003-3001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "VSA003-3001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "VSA003-3001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "BGM0504-III-T2DM-02-IDN", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "BGM0504-III-T2DM-02-IDN", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "BGM0504-III-T2DM-02-IDN", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "YZZB-LSSX-Z02", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "YZZB-LSSX-Z02", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "YZZB-LSSX-Z02", EnvName: "UAT2", Automatic: 1, Manual: 1},
		{ProjectNumber: "YZZB-LSSX-Z02", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "CG2001-C-2", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "CG2001-C-2", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "CG2001-C-2", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT102-2", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT102-2", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "JHM03-CT102-2", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZTJT-TNB-2024-01", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZTJT-TNB-2024-01", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "ZTJT-TNB-2024-01", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "YN001-004", EnvName: "DEV", Automatic: 2, Manual: 2},
		{ProjectNumber: "YN001-004", EnvName: "UAT", Automatic: 2, Manual: 2},
		{ProjectNumber: "YN001-004", EnvName: "PROD", Automatic: 2, Manual: 2},
		{ProjectNumber: "MG-K10-PN-001", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "MG-K10-PN-001", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "MG-K10-PN-001", EnvName: "PROD", Automatic: 1, Manual: 1},
		{ProjectNumber: "QY201-301", EnvName: "DEV", Automatic: 1, Manual: 1},
		{ProjectNumber: "NCT06516978", EnvName: "UAT", Automatic: 1, Manual: 1},
		{ProjectNumber: "NCT06516978", EnvName: "PROD", Automatic: 1, Manual: 1},
	}

	projectList := make([]models.Project, 0)
	cursor, err := DB.Collection("project").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projectList)
	if err != nil {
		panic(err)
	}

	roles := []primitive.ObjectID{}
	fieldsConfig := []string{}
	state := []string{}

	noticeConfigList := make([]interface{}, 0)
	if projectList != nil && len(projectList) > 0 {
		for _, project := range projectList {
			if project.Environments != nil && len(project.Environments) > 0 {
				for _, environment := range project.Environments {
					for _, language := range emailLanguageList {
						if language.ProjectNumber == project.ProjectInfo.Number && language.EnvName == environment.Name {
							noticeConfigList = append(noticeConfigList, models.NoticeConfig{
								ID:            primitive.NewObjectID(),
								CustomerID:    project.CustomerID,
								ProjectID:     project.ID,
								EnvironmentID: environment.ID,
								Roles:         roles,
								FieldsConfig:  fieldsConfig,
								State:         state,
								Key:           "notice.basic.settings",
								Automatic:     language.Automatic,
								Manual:        language.Manual,
							})
						}
					}

				}
			}
		}
	}

	if noticeConfigList != nil && len(noticeConfigList) > 0 {
		if _, err := DB.Collection("notice_config").InsertMany(nil, noticeConfigList); err != nil {
			panic(err)
		}
	}

}
