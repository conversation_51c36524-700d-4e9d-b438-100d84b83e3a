package test

import (
	"clinflash-irt/models"
	"go.mongodb.org/mongo-driver/bson"
	"testing"
)

// 添加app版本号
func UpdateNoticeSubjectUpdateAndScreen(t *testing.T) {
	// 受试者修改默认数据
	updateNoticeConfig := make([]models.NoticeConfig, 0)
	cursor, err := DB.Collection("notice_config").Find(nil, bson.M{"key": "notice.subject.update"})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &updateNoticeConfig)
	if err != nil {
		return
	}
	for _, c := range updateNoticeConfig {
		//if c.FieldsConfig == nil {
		//	_, err = DB.Collection("notice_config").UpdateOne(nil,
		//		bson.M{"_id": c.ID},
		//		bson.M{"$set": bson.M{"fields_config": bson.A{"projectName", "projectNumber", "envName", "siteName", "siteNumber", "random_number"}}})
		//	if err != nil {
		//		panic(err)
		//	}
		//} else {
		//	_, err = DB.Collection("notice_config").UpdateOne(nil,
		//		bson.M{"_id": c.ID},
		//		bson.M{"$addToSet": bson.M{"fields_config": bson.M{"$each":bson.A{"projectName", "projectNumber", "envName", "siteName", "siteNumber", "random_number"}}})
		//	if err != nil {
		//		panic(err)
		//	}
		//}
		if c.State == nil {
			_, err = DB.Collection("notice_config").UpdateOne(nil,
				bson.M{"_id": c.ID},
				bson.M{"$set": bson.M{"state": bson.A{"subject.update.form_factor"}}})
			if err != nil {
				panic(err)
			}
		} else {
			_, err = DB.Collection("notice_config").UpdateOne(nil,
				bson.M{"_id": c.ID},
				bson.M{"$addToSet": bson.M{"state": "subject.update.form_factor"}},
			)
			if err != nil {
				panic(err)
			}
		}
	}
	updateNoticeScreen := make([]models.NoticeConfig, 0)
	cursor, err = DB.Collection("notice_config").Find(nil, bson.M{"key": "notice.subject.screen"})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &updateNoticeScreen)
	if err != nil {
		return
	}

	for _, c := range updateNoticeScreen {
		if c.State == nil {
			_, err = DB.Collection("notice_config").UpdateOne(nil,
				bson.M{"_id": c.ID},
				bson.M{"$set": bson.M{"state": bson.A{"subject.screen.success", "subject.screen.fail"}}})
			if err != nil {
				panic(err)
			}
		} else {
			_, err = DB.Collection("notice_config").UpdateOne(nil,
				bson.M{"_id": c.ID},
				bson.M{"$addToSet": bson.M{"state": bson.M{"$each": bson.A{"subject.screen.success", "subject.screen.fail"}}}})
			if err != nil {
				panic(err)
			}
		}
	}

}
