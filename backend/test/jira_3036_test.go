package test

import (
	"clinflash-irt/models"
	"testing"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func UpdateDrugConfigure(t *testing.T) {
	//, "SHRC-CX2101-01" 有盲态药物
	projectNumbers := [13]string{"2021P0004", "20220530", "202296", "3D-197-CN-001", "A20-202", "ANG601-1001", "BN102-101-Ⅰ期", "BN301-101", "HR19042-202", "TestProject_DrBai_2", "ZGJAK020", "ZGJAK031"}
	//projectNumbers := [1]string{"ANG601-1001"}
	for _, projectNumber := range projectNumbers {
		var project models.Project
		DB.Collection("project").FindOne(nil, bson.M{"info.number": projectNumber}).Decode(&project)
		for _, envs := range project.Environments {
			//envs.Name == "UAT" &&
			if project.Type != 1 {
				for _, cohort := range envs.Cohorts {
					match := bson.M{"cohort_id": cohort.ID, "env_id": envs.ID}
					pipeline := mongo.Pipeline{
						{{Key: "$match", Value: match}},
						{{Key: "$project", Value: bson.M{
							"_id":        0,
							"id":         "$_id",
							"configures": 1,
						}}},
					}
					var data []map[string]interface{}
					cursor, err := DB.Collection("drug_configure").Aggregate(nil, pipeline)
					if err != nil {
						panic(err)
					}
					err = cursor.All(nil, &data)
					if err != nil {
						panic(err)
					}
					if data != nil && len(data) > 0 {
						drugConfigure := data[0]
						configures := drugConfigure["configures"]
						if configures != nil {
							newConfigures := configures.(primitive.A)
							for index, configure := range newConfigures {
								turnConfigure := configure.(map[string]interface{})
								// if turnConfigure["open_setting"] == nil || turnConfigure["open_setting"].(bool) != false {
								// 	turnConfigure["open_setting"] = true
								// }
								if turnConfigure["open_setting"] != nil && turnConfigure["open_setting"].(bool) {
									delete(turnConfigure, "open_setting")
								}
								newConfigures[index] = turnConfigure
							}
							update := bson.M{
								"$set": bson.M{
									"configures": newConfigures,
								},
							}
							_, updateErr := DB.Collection("drug_configure").UpdateOne(nil, bson.M{"_id": drugConfigure["id"]}, update)
							if updateErr != nil {
								panic(err)
							}
						}
					}
				}
			} else {
				var attribute models.Attribute
				err := DB.Collection("attribute").FindOne(nil, bson.M{"env_id": envs.ID}).Decode(&attribute)
				if err != nil {
					panic(err)
				}
				if !attribute.AttributeInfo.Blind { //开放项目
					match := bson.M{"env_id": envs.ID}
					pipeline := mongo.Pipeline{
						{{Key: "$match", Value: match}},
						{{Key: "$project", Value: bson.M{
							"_id":        0,
							"id":         "$_id",
							"configures": 1,
						}}},
					}
					var data []map[string]interface{}
					cursor, err := DB.Collection("drug_configure").Aggregate(nil, pipeline)
					if err != nil {
						panic(err)
					}
					err = cursor.All(nil, &data)
					if err != nil {
						panic(err)
					}
					if data != nil && len(data) > 0 {
						drugConfigure := data[0]
						configures := drugConfigure["configures"]
						if configures != nil {
							newConfigures := configures.(primitive.A)
							for index, configure := range newConfigures {
								turnConfigure := configure.(map[string]interface{})
								// if turnConfigure["open_setting"] == nil || turnConfigure["open_setting"].(bool) != false {
								// 	turnConfigure["open_setting"] = true
								// }
								if turnConfigure["open_setting"] != nil && turnConfigure["open_setting"].(bool) {
									delete(turnConfigure, "open_setting")
								}
								newConfigures[index] = turnConfigure
							}
							update := bson.M{
								"$set": bson.M{
									"configures": newConfigures,
								},
							}
							_, updateErr := DB.Collection("drug_configure").UpdateOne(nil, bson.M{"_id": drugConfigure["id"]}, update)
							if updateErr != nil {
								panic(err)
							}
						}
					}
				}

			}
		}
	}

}
