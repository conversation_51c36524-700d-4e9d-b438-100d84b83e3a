package test

import (
	"clinflash-irt/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"testing"
)

type Hospital struct {
	ID              primitive.ObjectID `json:"id" bson:"_id"`
	HospitalName    string             `json:"hospitalName" bson:"hospitalName"`
	HospitalNameEN  string             `json:"hospitalNameEN" bson:"hospitalNameEN"`
	HospitalAliases []string           `json:"hospitalAliases" bson:"hospitalAliases"`
}

func updateSiteName(t *testing.T) {
	var projectSites []models.ProjectSite
	var Hospitals []Hospital
	cursor, err := DB.Collection("project_site").Find(nil, bson.M{
		"$or": bson.A{
			bson.M{"dmp_id": bson.M{"$ne": primitive.NilObjectID}},
			bson.M{"dmp_id": bson.M{"$exists": 0}},
		}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projectSites)
	if err != nil {
		panic(err)
	}
	var DmpIDs bson.A
	for _, site := range projectSites {
		DmpIDs = append(DmpIDs, site.DmpID)
	}

	cursor, err = DB2.Collection("hospital").Find(nil, bson.M{"_id": bson.M{"$in": DmpIDs}}, &options.FindOptions{
		Projection: bson.M{
			"_id":             1,
			"hospitalName":    1,
			"hospitalNameEN":  1,
			"hospitalAliases": 1,
		},
	})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &Hospitals)
	if err != nil {
		panic(err)
	}
	HospitalsMap := map[string]Hospital{}
	for _, hospital := range Hospitals {
		HospitalsMap[hospital.ID.Hex()] = hospital
	}

	for _, site := range projectSites {
		tmpDMP := HospitalsMap[site.DmpID.Hex()]
		if tmpDMP.ID == primitive.NilObjectID {
			continue
		}
		if tmpDMP.HospitalNameEN == "" {
			continue
		}
		// 默认update英文名称
		update := bson.M{"name_en": tmpDMP.HospitalNameEN}
		//tmpDMP.HospitalNameEN
		//fmt.Println(site.Name + "~~~~" + tmpDMP.HospitalNameEN)
		//
		//// 之前名称使用英文名称的情况 名称需改回中文名称
		if site.Name == tmpDMP.HospitalNameEN && tmpDMP.HospitalName != tmpDMP.HospitalNameEN {
			//fmt.Println("中文名称使用英文~~~~~~~~~" + site.Name + "~~~~" + tmpDMP.HospitalName + "~~~~" + tmpDMP.HospitalNameEN)
			update["name"] = tmpDMP.HospitalName
		}

		//if !(site.Name == tmpDMP.HospitalName || site.Name == tmpDMP.HospitalNameEN) {
		//	// dmp修改过名称
		//	fmt.Println("名称修改过的：" + site.ID.Hex() + "~~~~" + site.Name + "~~~~" + tmpDMP.HospitalName)
		//}

		_, err := DB.Collection("project_site").UpdateOne(nil, bson.M{"_id": site.ID}, bson.M{"$set": update})
		if err != nil {
			panic(err)
		}

	}
}
