package test

import (
	"clinflash-irt/config"
	"clinflash-irt/models"
	"clinflash-irt/pb"
	"clinflash-irt/tools"
	"context"
	"fmt"
	"sync"
	"testing"

	"github.com/wxnacy/wgo/arrays"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func CreateBarcodeNumberIndex(t *testing.T) {
	DB.Collection("barcode_number").Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{Keys: bson.D{{"number", 1}}},
	})
}

func UpdateOldOpenProjectDrugConfig(t *testing.T) {
	// 基础研究项目
	var projects []models.Project
	basicProject := []string{
		"A20-202",
		"ZGJAK020",
		"2021P0004",
		"HR19042-202",
		"KYZY-SHHS-COVID-19-2301",
	}
	cursor, err := DB.Collection("project").Find(nil, bson.M{"info.number": bson.M{"$in": basicProject}})
	err = cursor.All(nil, &projects)
	if err != nil {
		panic(err)
	}

	for _, project := range projects {
		for _, environment := range project.Environments {
			_, err := DB.Collection("drug_configure").UpdateOne(nil, bson.M{
				"env_id": environment.ID,
			}, bson.M{
				"$set": bson.M{
					"configures.$[].open_setting": 2,
				},
			})
			if err != nil {
				return
			}
		}
	}

	// 开放盲态项目
	blindProject := []string{
		"3D-197-CN-001",
		"BN102-101-Ⅰ期",
		"BN301-101",
		"ANG601-1001",
		"QY201-Ⅰ-2",
		"SHRC-CX2101-01",
		"DEMO_002群组",
	}

	var blindProjects []models.Project
	cursor, err = DB.Collection("project").Find(nil, bson.M{"info.number": bson.M{"$in": blindProject}})
	if err != nil && err != mongo.ErrNoDocuments {
		panic(err)
	}

	err = cursor.All(nil, &blindProjects)
	if err != nil {
		panic(err)
	}
	for _, project := range blindProjects {
		for _, environment := range project.Environments {
			for _, cohort := range environment.Cohorts {
				count, err := DB.Collection("attribute").CountDocuments(nil, bson.M{"cohort_id": cohort.ID, "info.blind": false})
				if err != nil {
					panic(err)
				}
				if count > 0 {
					_, err := DB.Collection("drug_configure").UpdateOne(nil, bson.M{
						"cohort_id": cohort.ID,
					}, bson.M{
						"$set": bson.M{
							"configures.$[].open_setting": 2,
						},
					})
					if err != nil {
						panic(err)
					}
				}

			}
		}

	}
}

// UpdateOperationConfigurationSetting 剂量调整权限
func UpdateOperationConfigurationSetting(t *testing.T) {
	_, err := DB.Collection("project_role_permission").UpdateMany(nil,
		bson.M{
			"$or": []bson.M{
				bson.M{"name": "IP Officer"},
				bson.M{"name": "IRT Designer"},
			},
		},
		bson.M{
			"$addToSet": bson.M{
				"permissions": bson.M{
					"$each": []string{
						"operation.build.medicine.configuration.setting.add",
						"operation.build.medicine.configuration.setting.delete",
						"operation.build.medicine.configuration.setting.edit",
						"operation.build.medicine.configuration.setting.list",
					},
				},
			},
		},
	)
	if err != nil {
		panic(err)
	}
}

// UpdateProjectSiteTz 初始化更新tz
func UpdateProjectSiteTz(t *testing.T) {

	//dmp初始化
	config.Init()
	tools.InitDmpGrpcClientConn()

	tzList := make([]string, 0)

	sitesList := make([]models.Site, 0)
	grpcCtx := tools.DmpGrpcContext()
	cli := pb.NewHospitalV2Client(tools.DmpGrpcClientConn)
	request := &pb.ListAllHospitalsV2Request{
		CombineNames: true, // 设置 CombineNames 字段为 true
	}
	out, err := cli.ListAllHospitalsV2(grpcCtx, request)
	if err != nil {
		panic(err)
	}
	for _, site := range out.Data {
		tz := "Asia/Shanghai"
		if len(site.HospitalAddresses[0].Tz) > 0 {
			tz = site.HospitalAddresses[0].Tz
		}
		siteConfig := models.Site{
			ID: site.Id,
			Tz: tz,
		}
		sitesList = append(sitesList, siteConfig)
		index := arrays.ContainsString(tzList, tz)
		if index == -1 {
			//-1说明不存在
			tzList = append(tzList, tz)
		}
	}

	projectSiteList := make([]models.ProjectSite, 0)
	cursor, err := DB.Collection("project_site").Find(nil, bson.M{"dmp_id": bson.M{"$exists": true}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projectSiteList)
	if err != nil {
		panic(err)
	}

	if projectSiteList != nil && len(projectSiteList) > 0 {
		for _, projectSite := range projectSiteList {
			if projectSite.DmpID != primitive.NilObjectID && sitesList != nil && len(sitesList) > 0 {
				for _, site := range sitesList {
					if site.ID == projectSite.DmpID.Hex() {
						//oldTimeZone := projectSite.TimeZone
						offsetString, err := tools.GetUTCOffsetString(site.Tz)
						if err != nil {
							panic(err)
						}
						//更新project_site中心时区time_zone和tz字段
						if _, err := DB.Collection("project_site").UpdateOne(nil, bson.M{"_id": projectSite.ID}, bson.M{"$set": bson.M{"time_zone": offsetString, "tz": site.Tz}}); err != nil {
							panic(err)
						}
						//更新visit_notice

						//if oldTimeZone != offsetString {
						//	err = task.UpdateNotice(nil, 3, projectSite.EnvironmentID, primitive.NilObjectID, projectSite.ID, primitive.NilObjectID)
						//	if err != nil {
						//		panic(err)
						//	}
						//}

						if _, err := DB.Collection("visit_notice").UpdateMany(nil, bson.M{"project_site_id": projectSite.ID}, bson.M{"$set": bson.M{"tz": site.Tz}}); err != nil {
							panic(err)
						}
					}
				}
			}
		}
	}

	//添加tz和time_zone字段表
	if tzList != nil && len(tzList) > 0 {
		for _, tz := range tzList {
			offset, err := tools.GetLocationTimeZone(tz)
			if err != nil {
				panic(err)
			}
			if count, _ := DB.Collection("time_zone").CountDocuments(nil, bson.M{"tz": tz}); count > 0 {
				if _, err := DB.Collection("time_zone").UpdateMany(nil, bson.M{"tz": tz}, bson.M{"$set": bson.M{"time_zone": offset}}); err != nil {
					panic(err)
				}
			} else {
				timeZone := models.TimeZone{
					ID:       primitive.NewObjectID(),
					TimeZone: offset,
					Tz:       tz,
				}
				_, err = DB.Collection("time_zone").InsertOne(nil, timeZone)
				if err != nil {
					panic(err)
				}
			}

		}
	}

}

func CompareLocation(t *testing.T) {
	_, err := tools.GetLocationUtc("America/Chicago", 1704508553)
	if err != nil {
		panic(err)
	}
}

func UpdateEnvCohortCapacity(t *testing.T) {
	projects := make([]models.Project, 0)
	find, err := DB.Collection("project").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = find.All(nil, &projects)
	if err != nil {
		panic(err)
	}
	for _, project := range projects {
		for _, environment := range project.Environments {
			if project.Type == 1 && environment.Capacity != nil {
				alertThresholds := []models.AlertThreshold{
					{
						Type:       3,
						Capacity:   *environment.Capacity,
						Thresholds: environment.ReminderThresholds,
					},
				}

				update := bson.M{
					"$set": bson.M{
						"envs.$[env].alert_thresholds": alertThresholds,
					},
				}
				opts := &options.UpdateOptions{
					ArrayFilters: &options.ArrayFilters{
						Filters: bson.A{bson.M{"env.id": environment.ID}},
					},
				}
				if _, err := DB.Collection("project").UpdateOne(nil, bson.M{"_id": project.ID}, update, opts); err != nil {
					fmt.Println("失败", environment.ID.Hex())
					panic(err)
				}
				fmt.Println("成功", environment.ID.Hex())
			}
			if project.Type != 1 {
				for _, cohort := range environment.Cohorts {
					if cohort.Capacity > 0 {
						alertThresholds := []models.AlertThreshold{
							{
								Type:       3,
								Capacity:   cohort.Capacity,
								Thresholds: environment.ReminderThresholds,
							},
						}

						update := bson.M{
							"$set": bson.M{
								"envs.$[env].cohorts.$[cohort].alert_thresholds": alertThresholds,
							},
						}
						opts := &options.UpdateOptions{
							ArrayFilters: &options.ArrayFilters{
								Filters: bson.A{bson.M{"env.id": environment.ID}, bson.M{"cohort.id": cohort.ID}},
							},
						}
						if _, err := DB.Collection("project").UpdateOne(nil, bson.M{"_id": project.ID}, update, opts); err != nil {
							fmt.Println("失败", environment.ID.Hex())
							fmt.Println("失败", cohort.ID.Hex())
							panic(err)
						}
						fmt.Println("成功", environment.ID.Hex())
						fmt.Println("成功", cohort.ID.Hex())
					}
				}
			}
		}
	}
}

// 更新包装配置的数据
func UpdatePackageConfig(t *testing.T) {
	type TempDrugPackageConfigure struct {
		ID            primitive.ObjectID     `json:"id" bson:"_id"`
		CustomerID    primitive.ObjectID     `json:"customerId" bson:"customer_id"`
		ProjectID     primitive.ObjectID     `json:"projectId" bson:"project_id"`
		EnvironmentID primitive.ObjectID     `json:"envId" bson:"env_id"`
		IsOpen        bool                   `json:"isOpen" bson:"is_open"`
		PackageConfig []models.PackageConfig `json:"packageConfig" bson:"package_config"` //是否打开包装运输
		MixedPackage  []models.MixedPackage  `json:"mixedPackage" bson:"mixed_package"`   //包装信息配置
	}
	drugPackageConfigures := make([]TempDrugPackageConfigure, 0)
	cursor, err := DB.Collection("drug_package_configure").Find(nil, bson.M{"is_open": true})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &drugPackageConfigures)
	if err != nil {
		panic(err)
	}

	var wg sync.WaitGroup
	wg.Add(len(drugPackageConfigures))
	for i, h := range drugPackageConfigures {
		go func(in int, drugPackageConfigure TempDrugPackageConfigure) {
			defer wg.Done()
			if len(drugPackageConfigure.PackageConfig) > 0 {
				var mixedPackages []models.MixedPackage
				for _, v := range drugPackageConfigure.PackageConfig {
					var packageConfig []models.PackageConfig
					packageConfig = append(packageConfig, models.PackageConfig{
						Name:   v.Name,
						Number: v.Number,
					})
					mixedPackage := models.MixedPackage{
						IsMixed:       false,
						PackageConfig: packageConfig,
					}
					mixedPackages = append(mixedPackages, mixedPackage)
				}
				DB.Collection("drug_package_configure").UpdateOne(nil,
					bson.M{"_id": drugPackageConfigure.ID},
					bson.M{"$set": bson.M{"mixed_package": mixedPackages}})
			}

		}(i, h)
	}
	wg.Wait()
	fmt.Println("done")
}
