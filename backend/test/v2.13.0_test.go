package test

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"strconv"
	"strings"
	"testing"
	"time"
)

func ExportProjectTz(T *testing.T) {

	projects := make([]models.Project, 0)
	cursor, err := DB.Collection("project").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &projects)
	if err != nil {
		panic(err)
	}
	//fmt.Println(projects)
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	content := make([][]interface{}, 0)
	for _, item := range projects {
		tmp := []interface{}{}
		tmp = append(tmp, item.ProjectInfo.Number)
		tmp = append(tmp, item.ProjectInfo.TimeZoneStr)

		content = append(content, tmp)
		fmt.Println(item)
	}
	title := []interface{}{"项目编号", "时区"}
	tools.ExportSheet(f, "Sheet1", title, content)
	f.SaveAs("project_timeZone.xlsx")
	f.Close()
}

func ExportTz(T *testing.T) {

	type TimeZoneInfo struct {
		ID       string `json:"id"`
		Name     string `json:"name"`
		Location string `json:"location"`
		Zh       string `json:"zh"`
		En       string `json:"en"`
	}

	// 你的 JSON 数据
	jsonData := `[
		{
			id: "Dateline Standard Time",
			name: "国际日期变更线标准时间",
			location: "Etc/GMT+12",
			zh: "国际日期变更线西",
			en: "International Date Line West"
		},
		{
			id: "UTC-11",
			name: "UTC-11",
			location: "Etc/GMT+11",
			zh: "协调世界时-11",
			en: "Coordinated Universal Time-11"
		},
		{
			id: "Aleutian Standard Time",
			name: "阿留申群岛标准时间",
			location: "America/Adak",
			zh: "阿留申群岛",
			en: "Aleutian Islands"
		},
		{
			id: "Hawaiian Standard Time",
			name: "夏威夷标准时间",
			location: "US/Hawaii",
			zh: "夏威夷",
			en: "Hawaii"
		},
		{
			id: "Marquesas Standard Time",
			name: "马克萨斯群岛标准时间",
			location: "Pacific/Marquesas",
			zh: "马克萨斯群岛",
			en: "Marquesas Islands"
		},
		{
			id: "Alaskan Standard Time",
			name: "阿拉斯加标准时间",
			location: "US/Alaska",
			zh: "阿拉斯加",
			en: "Alaska"
		},
		{
			id: "UTC-09",
			name: "UTC-09",
			location: "Etc/GMT+9",
			zh: "协调世界时-09",
			en: "Coordinated Universal Time-09"
		},
		{
			id: "Pacific Standard Time (Mexico)",
			name: "太平洋标准时间(墨西哥)",
			location: "America/Tijuana",
			zh: "下加利福尼亚州",
			en: "Baja California"
		},
		{
			id: "UTC-08",
			name: "UTC-08",
			location: "Etc/GMT+8",
			zh: "协调世界时-08",
			en: "Coordinated Universal Time-08"
		},
		{
			id: "Pacific Standard Time",
			name: "太平洋标准时间",
			location: "America/Los_Angeles",
			zh: "太平洋时间(美国和加拿大)",
			en: "Pacific Time (USA and Canada)"
		},
		{
			id: "US Mountain Standard Time",
			name: "美国山地标准时间",
			location: "America/Phoenix",
			zh: "亚利桑那",
			en: "Arizona"
		},
		{
			id: "Mountain Standard Time",
			name: "山地标准时间",
			location: "MST",
			zh: "山地标准时间",
			en: "Mountain Standard Time"
		},
		{
			id: "Mountain Time",
			name: "山地时间",
			location: "MST7MDT",
			zh: "山地时间(美国和加拿大)",
			en: "Mountain Time(USA and Canada)"
		},
		{
			id: "Mountain Standard Time (Mexico)",
			name: "山地标准时间(墨西哥)",
			location: "America/Chihuahua",
			zh: "拉巴斯、马扎特兰",
			en: "La Paz, Mazatlan"
		},
		{
			id: "Yukon Standard Time",
			name: "育空标准时间",
			location: "Canada/Yukon",
			zh: "育空",
			en: "Yukon"
		},
		{
			id: "Central America Standard Time",
			name: "中美洲标准时间",
			location: "America/Guatemala",
			zh: "中美洲",
			en: "Central America"
		},
		{
			id: "Central Standard Time",
			name: "中部标准时间",
			location: "America/Chicago",
			zh: "中部时间(美国和加拿大)",
			en: "Central Time (USA and Canada)"
		},
		{
			id: "Easter Island Standard Time",
			name: "复活节岛标准时间",
			location: "Chile/EasterIsland",
			zh: "复活节岛",
			en: "Easter Island"
		},
		{
			id: "Central Standard Time (Mexico)",
			name: "中部标准时间(墨西哥)",
			location: "America/Mexico_City",
			zh: "瓜达拉哈拉，墨西哥城，蒙特雷",
			en: "Guadalajara, Mexico City, Monterey"
		},
		{
			id: "Canada Central Standard Time",
			name: "加拿大中部标准时间",
			location: "America/Regina",
			zh: "萨斯喀彻温",
			en: "Saskatchewan"
		},
		{
			id: "Eastern Standard Time",
			name: "东部标准时间",
			location: "America/New_York",
			zh: "东部时间(美国和加拿大)",
			en: "Eastern Time (USA and Canada)"
		},
		{
			id: "Eastern Standard Time (Mexico)",
			name: "东部标准时间(墨西哥)",
			location: "America/Cancun",
			zh: "切图马尔",
			en: "Chetumal"
		},
		{
			id: "US Eastern Standard Time",
			name: "美国东部标准时间",
			location: "America/Indiana/Indianapolis",
			zh: "印地安那州(东部)",
			en: "Indiana (Eastern)"
		},
		{
			id: "Cuba Standard Time",
			name: "古巴标准时间",
			location: "Cuba",
			zh: "哈瓦那",
			en: "Havana "
		},
		{
			id: "SA Pacific Standard Time",
			name: "南美洲太平洋标准时间",
			location: "America/Bogota",
			zh: "波哥大，利马，基多，里奥布朗库",
			en: "Bogota, Lima, Quito, Rio Bronco"
		},
		{
			id: "Haiti Standard Time",
			name: "海地标准时间",
			location: "America/Port-au-Prince",
			zh: "海地",
			en: "Haiti"
		},
		{
			id: "Turks And Caicos Standard Time",
			name: "特克斯和凯科斯群岛标准时间",
			location: "America/Grand_Turk",
			zh: "特克斯和凯科斯群岛",
			en: "Turks and Caicos Islands"
		},
		{
			id: "SA Western Standard Time",
			name: "南美洲西部标准时间",
			location: "America/La_Paz",
			zh: "乔治敦，拉巴斯，马瑙斯，圣胡安",
			en: "Georgetown, La Paz, Manaus, San Juan"
		},
		{
			id: "Paraguay Standard Time",
			name: "巴拉圭标准时间",
			location: "America/Asuncion",
			zh: "亚松森",
			en: "Asuncion"
		},
		{
			id: "Venezuela Standard Time",
			name: "委内瑞拉标准时间",
			location: "America/Caracas",
			zh: "加拉加斯",
			en: "Caracas"
		},
		{
			id: "Pacific SA Standard Time",
			name: "太平洋南美洲标准时间",
			location: "America/Santiago",
			zh: "圣地亚哥",
			en: "Santiago"
		},
		{
			id: "Atlantic Standard Time",
			name: "大西洋标准时间",
			location: "America/Halifax",
			zh: "大西洋时间(加拿大)",
			en: "Atlantic Time (Canada)"
		},
		{
			id: "Central Brazilian Standard Time",
			name: "巴西中部标准时间",
			location: "America/Cuiaba",
			zh: "库亚巴",
			en: "Manaus"
		},
		{
			id: "Newfoundland Standard Time",
			name: "纽芬兰标准时间",
			location: "America/St_Johns",
			zh: "纽芬兰",
			en: "Newfoundland"
		},
		{
			id: "SA Eastern Standard Time",
			name: "南美洲东部标准时间",
			location: "America/Cayenne",
			zh: "卡宴，福塔雷萨",
			en: "Cayenne, Fortaleza"
		},
		{
			id: "Saint Pierre Standard Time",
			name: "圣皮埃尔标准时间",
			location: "America/Miquelon",
			zh: "圣皮埃尔和密克隆群岛",
			en: "Saint Pierre and Miquelon Islands"
		},
		{
			id: "E. South America Standard Time",
			name: "东部南美洲标准时间",
			location: "America/Sao_Paulo",
			zh: "巴西利亚",
			en: "Brasilia"
		},
		{
			id: "Argentina Standard Time",
			name: "阿根廷标准时间",
			location: "America/Buenos_Aires",
			zh: "布宜诺斯艾利斯",
			en: "Buenos Aires"
		},
		{
			id: "Bahia Standard Time",
			name: "巴伊亚标准时间",
			location: "America/Bahia",
			zh: "萨尔瓦多",
			en: "El Salvador"
		},
		{
			id: "Montevideo Standard Time",
			name: "蒙得维的亚标准时间",
			location: "America/Montevideo",
			zh: "蒙得维的亚",
			en: "Montevideo"
		},
		{
			id: "Magallanes Standard Time",
			name: "麦哲伦标准时间",
			location: "America/Punta_Arenas",
			zh: "蓬塔阿雷纳斯",
			en: "Punta Arenas"
		},
		{
			id: "Tocantins Standard Time",
			name: "托坎廷斯标准时间",
			location: "America/Araguaina",
			zh: "阿拉瓜伊纳",
			en: "Araguaina"
		},
		{
			id: "Greenland Standard Time",
			name: "格陵兰标准时间",
			location: "America/Godthab",
			zh: "格陵兰",
			en: "Greenland"
		},
		{
			id: "UTC-02",
			name: "UTC-02",
			location: "Etc/GMT+2",
			zh: "协调世界时-02",
			en: "Coordinated Universal Time-02"
		},
		{
			id: "Azores Standard Time",
			name: "亚速尔群岛标准时间",
			location: "Atlantic/Azores",
			zh: "亚速尔群岛",
			en: "Azores"
		},
		{
			id: "Cape Verde Standard Time",
			name: "佛得角标准时间",
			location: "Atlantic/Cape_Verde",
			zh: "佛得角群岛",
			en: "Cape Verde Is."
		},
		{
			id: "UTC",
			name: "协调世界时",
			location: "Etc/GMT",
			zh: "协调世界时",
			en: "Coordinated Universal Time"
		},
		{
			id: "Sao Tome Standard Time",
			name: "圣多美标准时",
			location: "Africa/Sao_Tome",
			zh: "圣多美",
			en: "Sao Tome"
		},
		{
			id: "Greenwich Standard Time",
			name: "格林威治标准时间",
			location: "Atlantic/Reykjavik",
			zh: "蒙罗维亚，雷克雅未克",
			en: "Monrovia, Reykjavik"
		},
		{
			id: "GMT Standard Time",
			name: "GMT 标准时间",
			location: "Europe/Dublin",
			zh: "都柏林，爱丁堡，里斯本，伦敦",
			en: "Greenwich Mean Time : Dublin, Edinburgh, Lisbon, London"
		},
		{
			id: "Morocco Standard Time",
			name: "摩洛哥标准时间",
			location: "Africa/Casablanca",
			zh: "卡萨布兰卡",
			en: "Casablanca"
		},
		{
			id: "W. Central Africa Standard Time",
			name: "中非西部标准时间",
			location: "Africa/Lagos",
			zh: "中非西部",
			en: "West Central Africa"
		},
		{
			id: "Romance Standard Time",
			name: "罗马标准时间",
			location: "Europe/Brussels",
			zh: "布鲁塞尔，哥本哈根，马德里，巴黎",
			en: "Brussels, Copenhagen, Madrid, Paris"
		},
		{
			id: "Central European Standard Time",
			name: "中欧的标准时间",
			location: "Europe/Sarajevo",
			zh: "萨拉热窝，斯科普里，华沙，萨格勒布",
			en: "Sarajevo, Skopje, Warsaw, Zagreb"
		},
		{
			id: "Central Europe Standard Time",
			name: "中欧标准时间",
			location: "Europe/Belgrade",
			zh: "贝尔格莱德，布拉迪斯拉发，布达佩斯，卢布尔雅那，布拉格",
			en: "Belgrade, Bratislava, Budapest, Ljubljana, Prague"
		},
		{
			id: "W. Europe Standard Time",
			name: "西欧标准时间",
			location: "Europe/Amsterdam",
			zh: "阿姆斯特丹，柏林，伯尔尼，罗马，斯德哥尔摩，维也纳",
			en: "Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna"
		},
		{
			id: "West Bank Standard Time",
			name: "西岸加沙标准时间",
			location: "Asia/Gaza",
			zh: "加沙，希伯伦",
			en: "Gaza, Hebron"
		},
		{
			id: "Kaliningrad Standard Time",
			name: "俄罗斯 TZ 1 标准时间",
			location: "Europe/Kaliningrad",
			zh: "加里宁格勒",
			en: "Kaliningrad"
		},
		{
			id: "South Africa Standard Time",
			name: "南非标准时间",
			location: "Africa/Harare",
			zh: "哈拉雷，比勒陀利亚",
			en: "Harare, Pretoria"
		},
		{
			id: "Sudan Standard Time",
			name: "苏丹标准时",
			location: "Africa/Khartoum",
			zh: "喀土穆",
			en: "Khartoum"
		},
		{
			id: "E. Europe Standard Time",
			name: "东欧标准时间",
			location: "Europe/Bucharest",
			zh: "基希讷乌",
			en: "Chisinau"
		},
		{
			id: "Egypt Standard Time",
			name: "埃及标准时间",
			location: "Africa/Cairo",
			zh: "开罗",
			en: "Cairo"
		},
		{
			id: "South Sudan Standard Time",
			name: "南苏丹标准时间",
			location: "Africa/Juba",
			zh: "朱巴",
			en: "Juba"
		},
		{
			id: "Namibia Standard Time",
			name: "纳米比亚标准时间",
			location: "Africa/Windhoek",
			zh: "温得和克",
			en: "Windhoek"
		},
		{
			id: "Libya Standard Time",
			name: "利比亚标准时间",
			location: "Africa/Tripoli",
			zh: "的黎波里",
			en: "Tripoli"
		},
		{
			id: "Israel Standard Time",
			name: "耶路撒冷标准时间",
			location: "Asia/Jerusalem",
			zh: "耶路撒冷",
			en: "Jerusalem"
		},
		{
			id: "Middle East Standard Time",
			name: "中东标准时间",
			location: "Asia/Beirut",
			zh: "贝鲁特",
			en: "Beirut"
		},
		{
			id: "FLE Standard Time",
			name: "FLE 标准时间",
			location: "Europe/Helsinki",
			zh: "赫尔辛基，基辅，里加，索非亚，塔林，维尔纽斯",
			en: "Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius"
		},
		{
			id: "GTB Standard Time",
			name: "GTB 标准时间",
			location: "Europe/Athens",
			zh: "雅典，布加勒斯特",
			en: "Athens, Bucharest, Istanbul"
		},
		{
			id: "Turkey Standard Time",
			name: "土耳其标准时间",
			location: "Europe/Istanbul",
			zh: "伊斯坦布尔",
			en: "Istanbul"
		},
		{
			id: "Volgograd Standard Time",
			name: "伏尔加格勒标准时间",
			location: "Europe/Volgograd",
			zh: "伏尔加格勒",
			en: "Volgograd"
		},
		{
			id: "E. Africa Standard Time",
			name: "东非标准时间",
			location: "Africa/Nairobi",
			zh: "内罗毕",
			en: "Nairobi"
		},
		{
			id: "Syria Standard Time",
			name: "叙利亚标准时间",
			location: "Asia/Damascus",
			zh: "大马士革",
			en: "Damascus"
		},
		{
			id: "Jordan Standard Time",
			name: "约旦标准时间",
			location: "Asia/Amman",
			zh: "安曼",
			en: "Amman"
		},
		{
			id: "Arabic Standard Time",
			name: "阿拉伯 (Arabic) 标准时间",
			location: "Asia/Baghdad",
			zh: "巴格达",
			en: "Baghdad"
		},
		{
			id: "Belarus Standard Time",
			name: "白俄罗斯标准时间",
			location: "Europe/Minsk",
			zh: "明斯克",
			en: "Minsk"
		},
		{
			id: "Arab Standard Time",
			name: "阿拉伯 (Arab) 标准时间",
			location: "Asia/Kuwait",
			zh: "科威特，利雅得",
			en: "Kuwait, Riyadh"
		},
		{
			id: "Russian Standard Time",
			name: "俄罗斯 TZ 2 标准时间",
			location: "Europe/Moscow",
			zh: "莫斯科，圣彼得堡",
			en: "Moscow, St. Petersburg"
		},
		{
			id: "Iran Standard Time",
			name: "伊朗标准时间",
			location: "Asia/Tehran",
			zh: "德黑兰",
			en: "Tehran"
		},
		{
			id: "Caucasus Standard Time",
			name: "高加索标准时间",
			location: "Asia/Yerevan",
			zh: "埃里温",
			en: "Yerevan"
		},
		{
			id: "Azerbaijan Standard Time",
			name: "阿塞拜疆标准时间",
			location: "Asia/Baku",
			zh: "巴库",
			en: "Baku"
		},
		{
			id: "Georgian Standard Time",
			name: "格鲁吉亚标准时间",
			location: "Asia/Tbilisi",
			zh: "第比利斯",
			en: "Tbilisi"
		},
		{
			id: "Saratov Standard Time",
			name: "萨拉托夫标准时间",
			location: "Europe/Saratov",
			zh: "萨拉托夫",
			en: "Saratov"
		},
		{
			id: "Mauritius Standard Time",
			name: "毛里求斯标准时间",
			location: "Indian/Mauritius",
			zh: "路易港",
			en: "Port Louis"
		},
		{
			id: "Arabian Standard Time",
			name: "阿拉伯半岛标准时间",
			location: "Asia/Dubai",
			zh: "阿布扎比，马斯喀特",
			en: "Abu Dhabi, Muscat"
		},
		{
			id: "Astrakhan Standard Time",
			name: "阿斯特拉罕标准时间",
			location: "Europe/Astrakhan",
			zh: "阿斯特拉罕，乌里扬诺夫斯克",
			en: "Astrakhan, Ulyanovsk"
		},
		{
			id: "Afghanistan Standard Time",
			name: "阿富汗标准时间",
			location: "Asia/Kabul",
			zh: "喀布尔",
			en: "Kabul"
		},
		{
			id: "Pakistan Standard Time",
			name: "巴基斯坦标准时间",
			location: "Asia/Karachi",
			zh: "伊斯兰堡，卡拉奇",
			en: "Islamabad, Karachi"
		},
		{
			id: "Ekaterinburg Standard Time",
			name: "俄罗斯 TZ 4 标准时间",
			location: "Asia/Yekaterinburg",
			zh: "叶卡捷琳堡",
			en: "Yekaterinburg"
		},
		{
			id: "West Asia Standard Time",
			name: "西亚标准时间",
			location: "Asia/Tashkent",
			zh: "阿什哈巴德，塔什干",
			en: "Ashgabat, Tashkent"
		},
		{
			id: "Qyzylorda Standard Time",
			name: "克孜洛尔达标准时间",
			location: "Asia/Qyzylorda",
			zh: "阿斯塔纳",
			en: "Qyzylorda"
		},
		{
			id: "Sri Lanka Standard Time",
			name: "斯里兰卡标准时间",
			location: "Asia/Colombo",
			zh: "斯里加亚渥登普拉",
			en: "Sri Jayawardenepura"
		},
		{
			id: "India Standard Time",
			name: "印度标准时间",
			location: "Asia/Kolkata",
			zh: "钦奈，加尔各答，孟买，新德里",
			en: "Chennai, Kolkata, Mumbai, New Delhi"
		},
		{
			id: "Nepal Standard Time",
			name: "尼泊尔标准时间",
			location: "Asia/Katmandu",
			zh: "加德满都",
			en: "Katmandu"
		},
		{
			id: "Central Asia Standard Time",
			name: "中亚标准时间",
			location: "Asia/Bishkek",
			zh: "比什凯克",
			en: "Bishkek"
		},
		{
			id: "Bangladesh Standard Time",
			name: "孟加拉国标准时间",
			location: "Asia/Dhaka",
			zh: "达卡",
			en: "Dhaka"
		},
		{
			id: "Omsk Standard Time",
			name: "鄂木斯克标准时间",
			location: "Asia/Omsk",
			zh: "鄂木斯克",
			en: "Omsk"
		},
		{
			id: "Myanmar Standard Time",
			name: "缅甸标准时间",
			location: "Asia/Rangoon",
			zh: "仰光",
			en: "Yangon (Rangoon)"
		},
		{
			id: "North Asia Standard Time",
			name: "俄罗斯 TZ 6 标准时间",
			location: "Asia/Krasnoyarsk",
			zh: "克拉斯诺亚尔斯克",
			en: "Krasnoyarsk"
		},
		{
			id: "Altai Standard Time",
			name: "阿尔泰标准时间",
			location: "Asia/Barnaul",
			zh: "巴尔瑙尔，戈尔诺-阿尔泰斯克",
			en: "Barnaul, Gorno Altysk"
		},
		{
			id: "Tomsk Standard Time",
			name: "托木斯克标准时间",
			location: "Asia/Tomsk",
			zh: "托木斯克",
			en: "Tomsk"
		},
		{
			id: "N. Central Asia Standard Time",
			name: "新西伯利亚标准时间",
			location: "Asia/Novosibirsk",
			zh: "新西伯利亚",
			en: "Novosibirsk"
		},
		{
			id: "SE Asia Standard Time",
			name: "东南亚标准时间",
			location: "Asia/Bangkok",
			zh: "曼谷，河内，雅加达",
			en: "Bangkok, Hanoi, Jakarta"
		},
		{
			id: "W. Mongolia Standard Time",
			name: "西蒙古标准时间",
			location: "Asia/Hovd",
			zh: "科布多",
			en: "Hovd"
		},
		{
			id: "Ulaanbaatar Standard Time",
			name: "乌兰巴托标准时间",
			location: "Asia/Ulaanbaatar",
			zh: "乌兰巴托",
			en: "Ulaanbaatar"
		},
		{
			id: "North Asia East Standard Time",
			name: "俄罗斯 TZ 7 标准时间",
			location: "Asia/Irkutsk",
			zh: "伊尔库茨克",
			en: "Irkutsk"
		},
		{
			id: "China Standard Time",
			name: "中国标准时间",
			location: "Asia/Shanghai",
			zh: "北京，重庆，香港，乌鲁木齐",
			en: "Beijing, Chongqing, Hong Kong, Urumqi"
		},
		{
			id: "Taipei Standard Time",
			name: "台北标准时间",
			location: "Asia/Taipei",
			zh: "台北",
			en: "Taipei"
		},
		{
			id: "Singapore Standard Time",
			name: "马来西亚半岛标准时间",
			location: "Asia/Singapore",
			zh: "吉隆坡，新加坡",
			en: "Kuala Lumpur, Singapore"
		},
		{
			id: "W. Australia Standard Time",
			name: "澳大利亚西部标准时间",
			location: "Australia/Perth",
			zh: "珀斯",
			en: "Perth"
		},
		{
			id: "Aus Central W. Standard Time",
			name: "澳大利亚中西部标准时间",
			location: "Australia/Eucla",
			zh: "尤克拉",
			en: "Eucla"
		},
		{
			id: "Tokyo Standard Time",
			name: "东京标准时间",
			location: "Asia/Tokyo",
			zh: "大阪，札幌，东京",
			en: "Osaka, Sapporo, Tokyo"
		},
		{
			id: "North Korea Standard Time",
			name: "朝鲜标准时间",
			location: "Asia/Pyongyang",
			zh: "平壤",
			en: "Pyongyang"
		},
		{
			id: "Transbaikal Standard Time",
			name: "外贝加尔标准时间",
			location: "Asia/Chita",
			zh: "赤塔市",
			en: "Chita"
		},
		{
			id: "Yakutsk Standard Time",
			name: "俄罗斯 TZ 8 标准时间",
			location: "Asia/Yakutsk",
			zh: "雅库茨克",
			en: "Yakutsk"
		},
		{
			id: "Korea Standard Time",
			name: "韩国标准时间",
			location: "Asia/Seoul",
			zh: "首尔",
			en: "Seoul"
		},
		{
			id: "AUS Central Standard Time",
			name: "澳大利亚中部标准时间",
			location: "Australia/Darwin",
			zh: "达尔文",
			en: "Darwin"
		},
		{
			id: "Cen. Australia Standard Time",
			name: "中部澳大利亚标准时间",
			location: "Australia/Adelaide",
			zh: "阿德莱德",
			en: "Adelaide"
		},
		{
			id: "West Pacific Standard Time",
			name: "太平洋西部标准时间",
			location: "Pacific/Guam",
			zh: "关岛，莫尔兹比港",
			en: "Guam, Port Moresby"
		},
		{
			id: "AUS Eastern Standard Time",
			name: "澳大利亚东部标准时间",
			location: "Australia/Sydney",
			zh: "堪培拉，墨尔本，悉尼",
			en: "Canberra, Melbourne, Sydney"
		},
		{
			id: "E. Australia Standard Time",
			name: "东部澳大利亚标准时间",
			location: "Australia/Brisbane",
			zh: "布里斯班",
			en: "Brisbane"
		},
		{
			id: "Vladivostok Standard Time",
			name: "俄罗斯 TZ 9 标准时间",
			location: "Asia/Vladivostok",
			zh: "符拉迪沃斯托克",
			en: "Vladivostok"
		},
		{
			id: "Tasmania Standard Time",
			name: "塔斯马尼亚岛标准时间",
			location: "Australia/Hobart",
			zh: "霍巴特",
			en: "Hobart"
		},
		{
			id: "Lord Howe Standard Time",
			name: "豪勋爵岛标准时间",
			location: "Australia/Lord_Howe",
			zh: "豪勋爵岛",
			en: "Lord Howe Island"
		},
		{
			id: "Russia Time Zone 10",
			name: "俄罗斯 TZ 10 标准时间",
			location: "Asia/Srednekolymsk",
			zh: "乔库尔达赫",
			en: "Chokurdah"
		},
		{
			id: "Bougainville Standard Time",
			name: "布干维尔岛标准时间",
			location: "Pacific/Bougainville",
			zh: "布干维尔岛",
			en: "Bougainville Island"
		},
		{
			id: "Central Pacific Standard Time",
			name: "太平洋中部标准时间",
			location: "Pacific/Guadalcanal",
			zh: "所罗门群岛，新喀里多尼亚",
			en: "Solomon Islands, New Caledonia"
		},
		{
			id: "Sakhalin Standard Time",
			name: "萨哈林标准时间",
			location: "Asia/Sakhalin",
			zh: "萨哈林",
			en: "Sakhalin"
		},
		{
			id: "Norfolk Standard Time",
			name: "诺福克岛标准时间",
			location: "Pacific/Norfolk",
			zh: "诺福克岛",
			en: "Norfolk Island"
		},
		{
			id: "Magadan Standard Time",
			name: "马加丹标准时间",
			location: "Asia/Magadan",
			zh: "马加丹",
			en: "Magadan"
		},
		{
			id: "UTC+12",
			name: "UTC+12",
			location: "Etc/GMT-12",
			zh: "协调世界时+12",
			en: "Coordinated Universal Time+12"
		},
		{
			id: "New Zealand Standard Time",
			name: "新西兰标准时间",
			location: "Pacific/Auckland",
			zh: "奥克兰，惠灵顿",
			en: "Auckland, Wellington"
		},
		{
			id: "Fiji Standard Time",
			name: "斐济标准时间",
			location: "Pacific/Fiji",
			zh: "斐济",
			en: "Fiji"
		},
		{
			id: "Russia Time Zone 11",
			name: "俄罗斯 TZ 11 标准时间",
			location: "Asia/Kamchatka",
			zh: "阿纳德尔，堪察加彼得罗巴甫洛夫斯克",
			en: "Anadel, Kamchatka Petropavlovsk"
		},
		{
			id: "Chatham Islands Standard Time",
			name: "查塔姆群岛标准时间",
			location: "Pacific/Chatham",
			zh: "查塔姆群岛",
			en: "Chatham Islands"
		},
		{
			id: "Tonga Standard Time",
			name: "汤加标准时间",
			location: "Pacific/Tongatapu",
			zh: "努库阿洛法",
			en: "Nukualofa"
		},
		{
			id: "UTC+13",
			name: "UTC+13",
			location: "Etc/GMT-13",
			zh: "协调世界时+13",
			en: "Coordinated Universal Time+13"
		},
		{
			id: "Samoa Standard Time",
			name: "萨摩亚群岛标准时间",
			location: "Pacific/Apia",
			zh: "萨摩亚群岛",
			en: "Samoa Islands"
		},
		{
			id: "Line Islands Standard Time",
			name: "莱恩群岛标准时间",
			location: "Pacific/Kiritimati",
			zh: "圣诞岛",
			en: "Kiritimati"
		}
	]` // 替换为你的完整 JSON

	// 替换所有的键名前加上双引号，并在键名后面加上冒号和双引号
	data := strings.NewReplacer(
		"id:", `"id":`,
		"name:", `"name":`,
		"location:", `"location":`,
		"zh:", `"zh":`,
		"en:", `"en":`,
	).Replace(jsonData)

	var timeZones []TimeZoneInfo
	if err := json.Unmarshal([]byte(data), &timeZones); err != nil {
		fmt.Println("Error parsing JSON:", err)
		return
	}

	// 创建 Excel 文件
	f := excelize.NewFile()
	sheet := "Sheet1"

	// 设置表头
	headers := []string{"ID", "Name", "Location", "中文", "English", "时区"}
	for col, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(col+1, 1)
		err := f.SetCellValue(sheet, cell, header)
		if err != nil {
			panic(err)
		}
	}

	// 写入数据
	for row, tz := range timeZones {
		// 每行数据
		err := f.SetCellValue(sheet, fmt.Sprintf("A%d", row+2), tz.ID)
		if err != nil {
			panic(err)
		}
		err = f.SetCellValue(sheet, fmt.Sprintf("B%d", row+2), tz.Name)
		if err != nil {
			panic(err)
		}
		err = f.SetCellValue(sheet, fmt.Sprintf("C%d", row+2), tz.Location)
		if err != nil {
			panic(err)
		}
		err = f.SetCellValue(sheet, fmt.Sprintf("D%d", row+2), tz.Zh)
		if err != nil {
			panic(err)
		}
		err = f.SetCellValue(sheet, fmt.Sprintf("E%d", row+2), tz.En)
		if err != nil {
			panic(err)
		}
		utc := GetUtc(tz.Location)
		err = f.SetCellValue(sheet, fmt.Sprintf("F%d", row+2), utc)
		if err != nil {
			panic(err)
		}
	}

	// 保存文件
	if err := f.SaveAs("timezones.xlsx"); err != nil {
		panic(err)
	}

	fmt.Println("Excel 文件已生成: timezones.xlsx")
}

// GetUtc 支持半时区，例如 UTC+08:45
func GetUtc(tz string) string {
	location, err := time.LoadLocation(tz)
	if err != nil {
		fmt.Println("加载时区失败:", err)
		panic(err)
	}

	// 获取该时区当前时间
	now := time.Now().In(location)

	// 计算与UTC的偏移量（秒）
	_, offset := now.Zone()

	hours := offset / 3600          // 将秒转换为小时
	minutes := (offset % 3600) / 60 // 剩余的秒数转换为分钟

	// 确定符号并处理负数情况
	sign := "+"
	if hours < 0 {
		sign = "-"
	}
	hoursAbs := abs(hours)     // 绝对值用于显示
	minutesAbs := abs(minutes) // 绝对值用于显示

	// 格式化输出
	utcOffsetStr := fmt.Sprintf("UTC%s%02d:%02d", sign, hoursAbs, minutesAbs)
	return utcOffsetStr
}

// 辅助函数：计算绝对值
func abs(a int) int {
	if a < 0 {
		return -a
	}
	return a
}

// UpdateProjectTimeZoneTz 更新timeZone字段为string类型，并加tz字段
func UpdateProjectTimeZoneTz(t *testing.T) {

	type Info struct {
		Number   string `bson:"number"`
		TimeZone int    `bson:"timeZone"`
	}

	var results []struct {
		ID   primitive.ObjectID `bson:"_id"`
		Info Info               `bson:"info"` // 正确映射嵌套的info文档
	}
	cursor, err := DB.Collection("project").Find(
		nil,
		bson.M{}, // 查询条件，这里是空表示匹配所有文档
		options.Find().SetProjection(bson.M{
			"_id":           1,
			"info.number":   1,
			"info.timeZone": 1, // 注意这里的点符号用于访问嵌套的字段
		}),
	)
	if err != nil {
		panic(errors.WithStack(err))
	}
	if err = cursor.All(nil, &results); err != nil {
		panic(errors.WithStack(err))
	}

	if results != nil && len(results) > 0 {
		for _, project := range results {
			filter := bson.M{"_id": project.ID}
			timeZone := fmt.Sprintf("%d", project.Info.TimeZone)
			tz := ""
			if project.Info.TimeZone == -12 {
				tz = "Etc/GMT+12"
			} else if project.Info.TimeZone == -11 {
				tz = "Etc/GMT+11"
			} else if project.Info.TimeZone == -10 {
				tz = "US/Hawaii"
			} else if project.Info.TimeZone == -9 {
				tz = "America/Adak"
			} else if project.Info.TimeZone == -8 {
				tz = "US/Alaska"
			} else if project.Info.TimeZone == -7 {
				tz = "America/Los_Angeles"
			} else if project.Info.TimeZone == -6 {
				tz = "America/Guatemala"
			} else if project.Info.TimeZone == -5 {
				tz = "America/Chicago"
			} else if project.Info.TimeZone == -4 {
				tz = "America/New_York"
			} else if project.Info.TimeZone == -3 {
				tz = "America/Sao_Paulo"
			} else if project.Info.TimeZone == -2 {
				tz = "America/Miquelon"
			} else if project.Info.TimeZone == -1 {
				tz = "America/Godthab"
			} else if project.Info.TimeZone == 0 {
				tz = "Etc/GMT"
			} else if project.Info.TimeZone == 1 {
				tz = "Europe/Dublin"
			} else if project.Info.TimeZone == 2 {
				tz = "Europe/Amsterdam"
			} else if project.Info.TimeZone == 3 {
				tz = "Europe/Moscow"
			} else if project.Info.TimeZone == 4 {
				tz = "Asia/Dubai"
			} else if project.Info.TimeZone == 5 {
				tz = "Asia/Tashkent"
			} else if project.Info.TimeZone == 6 {
				tz = "Asia/Bishkek"
			} else if project.Info.TimeZone == 7 {
				tz = "Asia/Bangkok"
			} else if project.Info.TimeZone == 8 {
				tz = "Asia/Shanghai"
			} else if project.Info.TimeZone == 9 {
				tz = "Asia/Tokyo"
			} else if project.Info.TimeZone == 10 {
				tz = "Australia/Sydney"
			} else if project.Info.TimeZone == 11 {
				tz = "Pacific/Guadalcanal"
			} else if project.Info.TimeZone == 12 {
				tz = "Etc/GMT-12"
			}
			update := bson.M{"$set": bson.M{"info.timeZoneStr": timeZone, "info.tz": tz}}
			_, err := DB.Collection("project").UpdateOne(nil, filter, update)
			if err != nil {
				panic(errors.WithStack(err))
			}
		}
	}
}

// UpdateVisitNoticeTimeZoneTz 更新time_zone字段为string类型
func UpdateVisitNoticeTimeZoneTz(t *testing.T) {

	var results []struct {
		ID       primitive.ObjectID `bson:"_id"`
		TimeZone int                `bson:"time_zone"` // 受试者时区
	}
	cursor, err := DB.Collection("visit_notice").Find(nil, bson.M{})
	if err != nil {
		panic(errors.WithStack(err))
	}
	if err = cursor.All(nil, &results); err != nil {
		panic(errors.WithStack(err))
	}

	if results != nil && len(results) > 0 {
		for _, vn := range results {
			filter := bson.M{"_id": vn.ID}
			timeZone := float64(vn.TimeZone)
			update := bson.M{"$set": bson.M{"time_zone_float": timeZone}}
			_, err := DB.Collection("visit_notice").UpdateOne(nil, filter, update)
			if err != nil {
				panic(errors.WithStack(err))
			}
		}
	}
}

// UpdateTimeZoneTz 更新time_zone表的time_zone字段为为支持半时区的如：UTC-9:30
func UpdateTimeZoneTz(t *testing.T) {

	tzList := []string{
		"Etc/GMT+12",
		"Etc/GMT+11",
		"America/Adak",
		"US/Hawaii",
		"Pacific/Marquesas",
		"US/Alaska",
		"Etc/GMT+9",
		"America/Tijuana",
		"Etc/GMT+8",
		"America/Los_Angeles",
		"America/Phoenix",
		"MST",
		"MST7MDT",
		"America/Chihuahua",
		"Canada/Yukon",
		"America/Guatemala",
		"America/Chicago",
		"Chile/EasterIsland",
		"America/Mexico_City",
		"America/Regina",
		"America/New_York",
		"America/Cancun",
		"America/Indiana/Indianapolis",
		"Cuba",
		"America/Bogota",
		"America/Port-au-Prince",
		"America/Grand_Turk",
		"America/La_Paz",
		"America/Asuncion",
		"America/Caracas",
		"America/Santiago",
		"America/Halifax",
		"America/Cuiaba",
		"America/St_Johns",
		"America/Cayenne",
		"America/Miquelon",
		"America/Sao_Paulo",
		"America/Buenos_Aires",
		"America/Bahia",
		"America/Montevideo",
		"America/Punta_Arenas",
		"America/Araguaina",
		"America/Godthab",
		"Etc/GMT+2",
		"Atlantic/Azores",
		"Atlantic/Cape_Verde",
		"Etc/GMT",
		"Africa/Sao_Tome",
		"Atlantic/Reykjavik",
		"Europe/Dublin",
		"Africa/Casablanca",
		"Africa/Lagos",
		"Europe/Brussels",
		"Europe/Sarajevo",
		"Europe/Belgrade",
		"Europe/Amsterdam",
		"Asia/Gaza",
		"Europe/Kaliningrad",
		"Africa/Harare",
		"Africa/Khartoum",
		"Europe/Bucharest",
		"Africa/Cairo",
		"Africa/Juba",
		"Africa/Windhoek",
		"Africa/Tripoli",
		"Asia/Jerusalem",
		"Asia/Beirut",
		"Europe/Helsinki",
		"Europe/Athens",
		"Europe/Istanbul",
		"Europe/Volgograd",
		"Africa/Nairobi",
		"Asia/Damascus",
		"Asia/Amman",
		"Asia/Baghdad",
		"Europe/Minsk",
		"Asia/Kuwait",
		"Europe/Moscow",
		"Asia/Tehran",
		"Asia/Yerevan",
		"Asia/Baku",
		"Asia/Tbilisi",
		"Europe/Saratov",
		"Indian/Mauritius",
		"Asia/Dubai",
		"Europe/Astrakhan",
		"Asia/Kabul",
		"Asia/Karachi",
		"Asia/Yekaterinburg",
		"Asia/Tashkent",
		"Asia/Qyzylorda",
		"Asia/Colombo",
		"Asia/Kolkata",
		"Asia/Katmandu",
		"Asia/Bishkek",
		"Asia/Dhaka",
		"Asia/Omsk",
		"Asia/Rangoon",
		"Asia/Krasnoyarsk",
		"Asia/Barnaul",
		"Asia/Tomsk",
		"Asia/Novosibirsk",
		"Asia/Bangkok",
		"Asia/Hovd",
		"Asia/Ulaanbaatar",
		"Asia/Irkutsk",
		"Asia/Shanghai",
		"Asia/Taipei",
		"Asia/Singapore",
		"Australia/Perth",
		"Australia/Eucla",
		"Asia/Tokyo",
		"Asia/Pyongyang",
		"Asia/Chita",
		"Asia/Yakutsk",
		"Asia/Seoul",
		"Australia/Darwin",
		"Australia/Adelaide",
		"Pacific/Guam",
		"Australia/Sydney",
		"Australia/Brisbane",
		"Asia/Vladivostok",
		"Australia/Hobart",
		"Australia/Lord_Howe",
		"Asia/Srednekolymsk",
		"Pacific/Bougainville",
		"Pacific/Guadalcanal",
		"Asia/Sakhalin",
		"Pacific/Norfolk",
		"Asia/Magadan",
		"Etc/GMT-12",
		"Pacific/Auckland",
		"Pacific/Fiji",
		"Asia/Kamchatka",
		"Pacific/Chatham",
		"Pacific/Tongatapu",
		"Etc/GMT-13",
		"Pacific/Apia",
		"Pacific/Kiritimati",
	}

	for _, tz := range tzList {
		float, err := tools.GetLocationFloat(tz)
		if err != nil {
			panic(errors.WithStack(err))
		}
		timeZoneStr := fmt.Sprintf("%.10g", float)
		if count, _ := DB.Collection("time_zone").CountDocuments(nil, bson.M{"tz": tz}); count > 0 {
			if _, err := DB.Collection("time_zone").UpdateMany(nil, bson.M{"tz": tz}, bson.M{"$set": bson.M{"time_zone": timeZoneStr}}); err != nil {
				panic(err)
			}
		} else {
			timeZone := models.TimeZone{
				ID:       primitive.NewObjectID(),
				TimeZone: timeZoneStr,
				Tz:       tz,
			}
			_, err = DB.Collection("time_zone").InsertOne(nil, timeZone)
			if err != nil {
				panic(err)
			}
		}

	}

	//查询更新time_zone
	timeZoneList := make([]models.TimeZone, 0)
	cursor, err := DB.Collection("time_zone").Find(nil, bson.M{})
	if err != nil {
		panic(errors.WithStack(err))
	}
	err = cursor.All(nil, &timeZoneList)
	if err != nil {
		panic(errors.WithStack(err))
	}

	if timeZoneList != nil && len(timeZoneList) > 0 {
		for _, zone := range timeZoneList {
			float, err := tools.GetLocationFloat(zone.Tz)
			if err != nil {
				panic(errors.WithStack(err))
			}
			//timeZone := "8"
			timeZoneStr := fmt.Sprintf("%.10g", float)
			//更新time_zone中心时区time_zone字段
			if _, err := DB.Collection("time_zone").UpdateOne(nil, bson.M{"tz": zone.Tz}, bson.M{"$set": bson.M{"time_zone": timeZoneStr}}); err != nil {
				panic(err)
			}

			str, err := tools.GetUTCOffsetString(zone.Tz)
			if err != nil {
				panic(errors.WithStack(err))
			}
			//更新project_site中心时区time_zone字段
			if _, err := DB.Collection("project_site").UpdateMany(nil, bson.M{"tz": zone.Tz}, bson.M{"$set": bson.M{"time_zone": str}}); err != nil {
				panic(err)
			}
		}
	}

	projectSiteList := make([]models.ProjectSite, 0)
	filter := bson.M{
		"$or": []bson.M{
			{"tz": ""},                       // tz 是空字符串
			{"tz": bson.M{"$exists": false}}, // tz 字段不存在
		},
	}

	cus, err := DB.Collection("project_site").Find(nil, filter)
	if err != nil {
		panic(err)
	}
	err = cus.All(nil, &projectSiteList)
	if err != nil {
		panic(err)
	}

	if projectSiteList != nil && len(projectSiteList) > 0 {
		for _, site := range projectSiteList {
			if site.TimeZone != "" && strings.Index(site.TimeZone, ":") == -1 {
				s := strings.Replace(site.TimeZone, "UTC", "", 1)
				num, _ := strconv.Atoi(s)
				str := formatTZOffset(num)
				str = "UTC" + str
				//更新project_site中心时区time_zone字段
				if _, err := DB.Collection("project_site").UpdateOne(nil, bson.M{"_id": site.ID}, bson.M{"$set": bson.M{"time_zone": str}}); err != nil {
					panic(err)
				}
			}

		}
	}

}

func formatTZOffset(offset int) string {
	if offset >= 0 {
		return fmt.Sprintf("+%02d:00", offset) // 处理正数，如 +09:00
	} else {
		return fmt.Sprintf("-%02d:00", -offset) // 处理负数，如 -09:00
	}
}

// UpdateProjectEnvCohortIsCopy 更新project表Environment字段为true的cohorts字段IsCopy字段为true
func UpdateProjectEnvCohortIsCopy(t *testing.T) {

	projectList := make([]models.Project, 0)
	pCursor, err := DB.Collection("project").Find(nil, bson.M{})
	if err != nil {
		panic(err)
	}
	err = pCursor.All(nil, &projectList)
	if err != nil {
		panic(err)
	}

	if projectList != nil && len(projectList) > 0 {
		for _, project := range projectList {
			if project.Type != 1 {
				if project.Environments != nil && len(project.Environments) > 0 {
					prodEnv := models.Environment{}
					for _, environment := range project.Environments {
						if environment.Name == "PROD" {
							prodEnv = environment
						}
					}
					if prodEnv.ID != primitive.NilObjectID && prodEnv.Cohorts != nil && len(prodEnv.Cohorts) > 0 {
						for _, environment := range project.Environments {
							if environment.IsCopy {
								if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
									for i := range environment.Cohorts {
										for _, cohort := range prodEnv.Cohorts {
											if environment.Cohorts[i].Name == cohort.Name {
												environment.Cohorts[i].IsCopy = true
											}
										}
									}
									update := bson.M{
										"$set": bson.M{
											"envs.$[env].cohorts": environment.Cohorts, // 注意这里是直接赋值一个新数组
										},
									}

									opts := &options.UpdateOptions{
										ArrayFilters: &options.ArrayFilters{
											Filters: bson.A{bson.M{"env.id": environment.ID}},
										},
									}
									if _, err := DB.Collection("project").UpdateOne(nil, bson.M{"_id": project.ID}, update, opts); err != nil {
										panic(err)
									}

								}
							}
						}
					}
				}
			}
		}
	}

}
