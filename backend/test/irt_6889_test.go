package test

import (
	"clinflash-irt/models"
	"testing"

	"go.mongodb.org/mongo-driver/bson"
)

// UpdateEdcProjectAll
func UpdateEdcProjectAll(t *testing.T) {
	projectNumbers := [...]string{"ZSJ 项目编号01", "EDC对接", "Kawin-KW053-1", "HZ-H08905-301", "LWY23090C", "LM302-03-101", "YR001-A03", "ESG401-301", "HengLi009-Ⅱ", "2024-760-00CH1", "MHB039A-A-101", "2024-013-00CH1"}

	// 查询对接的项目
	var projects []models.Project
	cursor, err := DB.Collection("project").Find(nil, bson.M{"info.connect_edc": 1, "info.push_mode": 2, "info.edc_supplier": 1})
	err = cursor.All(nil, &projects)
	if err != nil {
		panic(err)
	}

	for _, project := range projects {
		sign := false

		registerPush := true          // 登记是否推送
		updateRandomFrontPush := true // 修改 随机前是否推送
		updateRandomAfterPush := true // 修改 随机后是否推送
		randomPush := true            // 随机是否推送
		randomBlockPush := false      // 分层不一致是否阻断随机
		dispensingPush := true        // 发药是否推送

		if project.PushTypeEdc == "OnlyRandom" { // 仅随机
			dispensingPush = false
		} else if project.PushTypeEdc == "OnlyDrug" { // 仅发药
			randomPush = false
		}

		for _, num := range projectNumbers {
			if num == project.ProjectInfo.Number {
				sign = true
				break
			}
		}

		if sign {
			registerPush = false
			updateRandomFrontPush = false
		}

		update := bson.M{"$set": bson.M{
			"info.push_rules":                             2, // 受试者UID
			"info.push_scenario.register_push":            registerPush,
			"info.push_scenario.update_random_front_push": updateRandomFrontPush,
			"info.push_scenario.update_random_after_push": updateRandomAfterPush,
			"info.push_scenario.random_push":              randomPush,
			"info.push_scenario.random_block_push":        randomBlockPush,
			"info.push_scenario.dispensing_push":          dispensingPush,
		}}
		_, err := DB.Collection("project").UpdateMany(nil,
			bson.M{"_id": project.ID},
			update,
		)
		if err != nil {
			panic(err)
		}
	}
}
