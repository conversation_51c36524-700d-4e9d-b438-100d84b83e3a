<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="https://cdn.bootcss.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <title>Dreamflash</title>
    <style>
        .bg-grey {
            background: lightgrey;
        }
        .mar-10 {
            margin: 10px;
        }
        .pad-10 {
            padding: 10px;
        }
        .text-header {
            color: lightseagreen;
        }
        .text-foot {
            font-size: 13px;
        }
        .text-body {
            font-size: 14px;
            color: dimgrey;
        }

        table.mytable {
            border: 1px solid #444;
            border-spacing: 1px;
        }
        table.mytable tr,table.mytable th {
            border-bottom: 1px solid #444
        }
        table.mytable td,table.mytable th{
            border-left: 1px solid #444;
            border-right: 1px solid #444;
            border-top: 1px solid #444;
            border-bottom: 1px solid #444;
            padding: 5px;
        }
        th.myth {
            width: 120px;
        }
    </style>
</head>
<body>
<div class="container-fluid text-body">

    {{if .projectNumberShow}}
    <p>项目编号:<span >{{.projectNumber}}</span></p>
    {{end}}
    {{if .projectNameShow}}
    <p>项目名称:<span >{{.projectName}}</span></p>
    {{end}}
    {{if .envNameShow}}
    <p>项目环境:<span >{{.envName}}</span></p>
    {{end}}
    <p>目的地编号:<span >{{.instituteNumber}}</span></p>
    <p>目的地名称:<span >{{.instituteName}}</span></p>
    <table class="mytable">
        <tr style="text-align: left">
            <th class="myth">批次号</th>
            <th>研究产品编号</th>
            <th class="myth">数量</th>
            <th class="myth">有效期</th>
        </tr>
        {{range $index, $result := .results}}
        <tr>
            <td >{{$result.batchNumber}}</td>
            <td >{{$result.number}}</td>
            <td >{{$result.count}}</td>
            <td >{{$result.expiryDate}}</td>
        </tr>
        {{end}}
    </table>

    <p></p>
    <p></p>

    {{if .projectNumberShow}}
    <p>Project Number:<span >{{.projectNumber}}</span></p>
    {{end}}
    {{if .projectNameShow}}
    <p>Project Name:<span >{{.projectName}}</span></p>
    {{end}}
    {{if .envNameShow}}
    <p>Project Environment:<span >{{.envName}}</span></p>
    {{end}}
    <p>Organization Number:<span >{{.instituteNumber}}</span></p>
    <p>Organization Name:<span >{{.instituteNameEn}}</span></p>
    <table class="mytable">
        <tr style="text-align: left">
            <th class="myth">Lot ID</th>
            <th>IP Number</th>
            <th class="myth">Number</th>
            <th class="myth">Expiration time</th>
        </tr>
        {{range $index, $result := .results}}
        <tr>
            <td >{{$result.batchNumber}}</td>
            <td >{{$result.number}}</td>
            <td >{{$result.count}}</td>
            <td >{{$result.expiryDate}}</td>
        </tr>
        {{end}}
    </table>




</div>
<script src="https://cdn.bootcss.com/jquery/3.2.1/jquery.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
<script src="https://cdn.bootcss.com/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
<script src="https://cdn.bootcss.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
</body>
</html>
