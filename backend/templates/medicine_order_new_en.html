<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Clinflash IRT</title>
    <style>
        .text-body {
            font-size: 14px;
            color: dimgrey;
        }

        table.mytable {
            border-collapse: collapse;
            border: 0;
            border-spacing: 0;
        }

        table.mytable th {
            border: 0.5px solid #E0E5EB;
            background: #F1F3F7;
            padding: 5px;
            color: #4D587A;
            text-align: left;
        }

        table.mytable td {
            border: 0.5px solid #E0E5EB;
            padding: 5px;
            color: #4D587A;
            width: 300px;
            table-layout: fixed;
            word-break: break-all;
            word-wrap: break-word;
        }

        table.mytable tr:first-child th {
            border-top: none;
        }

        /*table.mytable tr:last-child td {*/
        /*    border-bottom: none;*/
        /*}*/

        table.mytable td:first-child,
        table.mytable th:first-child {
            border-left: none;
        }

        table.mytable td:last-child,
        table.mytable th:last-child {
            border-right: none;
        }
    </style>
</head>

<body>
    <table align="center" border="0" cellspacing="0" cellpadding="0"
        style="font-size: 14px; background-color: #fff; table-layout: fixed; border-top: #0A47ED solid 6px;">
        <tbody>
            <tr>
                <td colspan="12" style="padding: 40px 0 40px 50px;">
                    <img style="width: 238px; height: 40px;" src="{{.irt_url}}/api/img/mail" />
                </td>
            </tr>
            <tr>
                <td colspan="12" style="padding: 5px 50px;">
                    <div class="container-fluid text-body">
                        {{if .projectNumberShow}}
                        <p>Project Number:<span>{{.projectNumber}}</span></p>
                        {{end}}
                        {{if .projectNameShow}}
                        <p>Project Name:<span>{{.projectName}}</span></p>
                        {{end}}
                        {{if .envNameShow}}
                        <p>Project Environment:<span>{{.envName}}</span></p>
                        {{end}}
                        <p>Origin:{{.startEn}}</p>
                        <p>Destination:{{.destinationEn}}</p>
                        <p>Order Number:{{.orderNumber}}</p>
                        <p>Contact Person:{{.contacts}}</p>
                        <p>TEL:{{.phone}}</p>
                        <p>E-mail:{{.email}}</p>
                        <p>Address:{{.address}}</p>
                        {{if .expectedArrivalTimeShow}}
                        <p>Expected Arrival Time:<span>{{.expectedArrivalTime}}</span></p>
                        {{end}}
                        <p>IP</p>
                        <table class="mytable">
                            <tr>
                                <th>Lot ID</th>
                                {{if .packageIsOpen}}
                                <th>Package Number</th>
                                {{end}}
                                <th>IP</th>
                                <th>Number</th>
                                <th>Expiration Date</th>
                            </tr>
                            {{range $index, $result := .results}}
                            {{range $indexDetail, $detail := $result.details}}
                            {{if eq $indexDetail 0}}
                            <tr>
                                {{if eq $result.batch nil}}
                                <td rowspan={{$result.rowsSpan}}>-</td>
                                {{else if eq $result.batch ""}}
                                <td rowspan={{$result.rowsSpan}}>-</td>
                                {{else}}
                                <td rowspan={{$result.rowsSpan}}>{{$result.batch}}</td>
                                {{end}}
                                {{if $result.packageIsOpen}}
                                {{if eq $detail.packageNumber nil}}
                                <td>-</td>
                                {{else if eq $detail.packageNumber ""}}
                                <td>-</td>
                                {{else}}
                                <td>{{$detail.packageNumber}}</td>
                                {{end}}
                                {{end}}
                                <td class="td">{{$detail.number}}</td>
                                {{if $detail.packageMethod}}
                                <td>{{$detail.count }}({{$detail.packageCount}})</td>
                                {{else}}
                                <td>{{$detail.count}}</td>
                                {{end}}
                                {{if eq $result.expiryDate nil}}
                                <td rowspan={{$result.rowsSpan}}>-</td>
                                {{else if eq $result.expiryDate ""}}
                                <td rowspan={{$result.rowsSpan}}>-</td>
                                {{else}}
                                <td rowspan={{$result.rowsSpan}}>{{$result.expiryDate}}</td>
                                {{end}}
                            </tr>
                            {{else}}
                            <tr>
                                {{if $result.packageIsOpen}}
                                {{if eq $detail.packageNumber nil}}
                                <td>-</td>
                                {{else if eq $detail.packageNumber ""}}
                                <td>-</td>
                                {{else}}
                                <td>{{$detail.packageNumber}}</td>
                                {{end}}
                                {{end}}
                                <td class="td">{{$detail.number}}</td>
                                {{if $detail.packageMethod}}
                                <td>{{$detail.count }}({{$detail.packageCount}})</td>
                                {{else}}
                                <td>{{$detail.count}}</td>
                                {{end}}
                            </tr>
                            {{end}}
                            {{end}}
                            {{end}}

                        </table>
                        <br>
                    </div>
                </td>
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td colspan="12"
                    style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3">
                    <div>This email contains a secure link. Please do not share this email, link, or access code with
                        others.</div>
                    <div>This email is sent automatically by the system, please do not reply directly.</div>
                    <div>Copyright ©2020 Clinflash Healthcare Technology (Jiaxing) Co.,Ltd. All Rights Reserved</div>
                </td>
            </tr>
        </tfoot>
    </table>
</body>