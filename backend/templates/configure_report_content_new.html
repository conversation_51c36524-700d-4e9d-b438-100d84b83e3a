<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinflash</title>
    <style>
        /* 字体 */
        /* 微软雅黑 */
        @font-face {
            font-family: MicrosoftYaHei;
            font-weight: normal;
            src: url("./assets/fonts/Microsoft-YaHei.ttf") format('truetype');
        }

        /* 微软雅黑粗体 */
        @font-face {
            font-family: MicrosoftYaHei;
            font-weight: bold;
            src: url("./assets/fonts/Microsoft-YaHei-Bold.ttf") format('truetype');
        }

        /* 宋体 */
        @font-face {
            font-family: Song;
            font-weight: normal;
            src: url("./assets/fonts/SourceHanSerifCN-Regular.ttf") format('truetype');
        }

        /* 宋体加粗 */
        @font-face {
            font-family: Song;
            font-weight: bold;
            src: url("./assets/fonts/SourceHanSerifCN-Bold.ttf") format('truetype');
        }

        /* 等线 Light */
        @font-face {
            font-family: DengXianLight;
            font-weight: normal;
            src: url("./assets/fonts/DengXian-Light.ttf") format('truetype');
        }

        /* Arial */
        @font-face {
            font-family: Arial;
            font-weight: normal;
            src: url("./assets/fonts/Arial.ttf") format('truetype');
        }

        /* Calibri */
        @font-face {
            font-family: Calibri;
            font-weight: normal;
            src: url("./assets/fonts/Calibri-Regular.ttf") format('truetype');
        }


        /* TimesNewRoman */
        @font-face {
            font-family: TimesNewRoman;
            font-weight: normal;
            src: url("./assets/fonts/Times-New-Roman.ttf") format('truetype');
        }


        /* 清除默认的 HTML 和 body 的 margin，避免影响 WeasyPrint 的渲染 */
        html, body {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 设置统一的页面内边距 */
        @page {
            size: A4;
            margin-top: 1.3cm;
            margin-bottom: 2.75cm;
            margin-left: 1.4cm;
            margin-right: 1.4cm;
        }

        /* 在每页的页脚中显示当前页码和总页数 */
        @page {
            @top-center {
                content: '';
                background: #000; /* 设定细线颜色 */
                display: block;
                height: 0.05mm; /* 设置线条的高度 */
                opacity: 0.15; /* 设定透明度 */
                width: 115%; /* 设置宽度为页宽 */
                transform: translate(2mm, -2mm); /* 向右偏移 2mm，向上偏移 2mm */
            }
            @bottom-right {
                content: counter(page) " / " counter(pages); /* 在右下角显示页码 */
                font-size: 9pt;
                font-family: MicrosoftYaHei;
                height: 1cm;
                vertical-align: middle;
                transform: translateY(1.2cm); /* 向下偏移 */
            }
        }

        /* 页眉和页脚样式 */
        header, footer {
            position: fixed;
            left: 0; /* 左边距与正文一致 */
            right: 0; /* 右边距与正文一致 */
            text-align: center;
            margin: 0;
        }

        /* 页眉固定在页面顶部 */
        header {
            top: -1.2cm;
        }

        /* 设置页眉内容为水平排列 */
        .header-content {
            display: flex;
            justify-content: space-between; /* 左右两边对齐 */
            align-items: center; /* 垂直居中 */
        }

        /* 页眉页脚样式  */
        .header-text, .footer-text {
            font-family: Song, sans-serif;
            font-size: 10pt;
        }

        .header-text-en {
            font-family: TimesNewRoman, sans-serif;
            font-size: 10pt;
        }

        /* 右侧的 logo */
        .header-logo {
            width: 3.2cm;
            height: 1.2cm;
        }

        /* 页脚固定在页面底部 */
        footer {
            bottom: -2.2cm;
        }

        /* 设置页脚内容为水平排列 */
        .footer-content {
            display: flex;
            justify-content: space-between; /* 左右两边对齐 */
            align-items: center; /* 垂直居中 */
            font-size: 12px;
            height: 1cm;
        }

        /* 右侧的页码 */
        .footer-page-number {

        }


        /* 封面页的样式 */
        .cover-page {
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            page-break-after: always; /* 确保封面后自动分页 */
        }


        #toc ul {
            display: block;
            padding-left: 0;
            font-family: MicrosoftYaHei, sans-serif;
            font-weight: bold;
            font-size: 11pt;
        }

        #toc ul ul {
            display: block;
            padding-left: 2em; /* 二级目录缩进 */
        }

        #toc ul ul ul {
            display: block;
            padding-left: 2em; /* 三级目录缩进 */
            font-family: MicrosoftYaHei, sans-serif;
        }

        #toc li {
            display: block;
        }

        #toc a {
            color: inherit;
            text-decoration: none;
            display: block;
            line-height: 1.08; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 8pt; /* 段后 8 磅 */
        }

        #toc a::before {
            /*content: target-text(attr(href)) ' ' leader('.') ' ' target-counter(attr(href), page);*/
            content: target-text(attr(href)) ' ';
        }

        /* 分成两部分显示，来为 ... 和页码单独设置字体 */
        #toc a::after {
            content: leader('.') ' ' target-counter(attr(href), page);
            font-family: Calibri, sans-serif;
            font-size: 11pt;
        }

        #toc p {
            font-family: DengXianLight, sans-serif;
            font-size: 18pt; /* 三号字体对应16pt */
            color: #568fc3;
            margin: 0;
        }

        /* 设置页面内容 */
        .chapter {
            break-after: page; /* 模拟分页 */
            margin: 0;
        }

        .cover-content p {
            font-family: Song, sans-serif;
            font-weight: bold;
            font-size: 18pt; /* 小二号字体对应18pt */
            line-height: 1.1; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 58.5pt; /* 段后 58.5 磅 */
        }

        /* 一级标题样式 */
        h1 {
            font-family: MicrosoftYaHei, sans-serif;
            font-weight: bold;
            font-size: 14pt; /* 四号字体对应14pt */
            line-height: 1.08; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            /*margin-bottom: 6.95pt; !* 段后 6.95 磅 *!*/
            margin-bottom: 24.2pt; /* 段后 24.2 磅 */
        }

        /* 二级标题样式 */
        h2 {
            font-family: MicrosoftYaHei, sans-serif;
            font-weight: bold;
            font-size: 12pt; /* 小四号字体对应12pt */
            line-height: 1.08; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 8pt; /* 段后 58.5 磅 */
        }

        /* 三级标题样式 */
        h3 {
            font-family: MicrosoftYaHei, sans-serif;
            font-size: 10.5pt; /* 五号字体对应10.5pt */
            line-height: 1.73; /* 行距 */
            margin-top: 13pt; /* 段前 0 行 */
            margin-bottom: 13pt; /* 段后 58.5 磅 */
        }

        .content {
            font-family: Song, sans-serif;
            font-size: 10.5pt; /* 五号字体对应10.5pt */
            /*hyphens: auto; !* 长单词中自动添加连字符，使折断更自然。 *!*/
            word-break: break-all;
        }

        /* 单词中间不折断 */
        .break-normal {
            word-break: normal;
        }

        /* 正文样式 */
        .content p {
            line-height: 1.64; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 6.95pt; /* 段后 6.95 磅 */
        }

        .small-title {
            line-height: 1.08; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 8pt; /* 段后 6.95 磅 */
        }

        .small-part {
            margin-bottom: 30px;
        }

        .small-title-bold {
            font-weight: bold;
            line-height: 1.08; /* 行距 */
            margin-top: 0; /* 段前 0 行 */
            margin-bottom: 8pt; /* 段后 6.95 磅 */
        }

        /* 首行缩进的段落 */
        .indentP {
            text-indent: 2em; /* 首行缩进，em单位是相对于当前字体尺寸的单位，1em等于当前字体尺寸 */
        }

        /*!* 正文英文样式 *!*/
        /*.content .english {*/
        /*    font-family: Arial, sans-serif; !* Arial，如果没有则使用微软雅黑，再没有则使用无衬线字体 *!*/
        /*    font-size: 10.5pt; !* 五号字体对应10.5pt *!*/
        /*}*/


        table {
            width: 100%;
            border-collapse: collapse; /* 去除单元格之间的间隙 */
            break-inside: avoid;
            /*table-layout: fixed;*/
        }

        th, td {
            border: 1px solid black; /* 设置边框 */
            padding: 5px; /* 设置单元格内边距 */
            text-align: left; /* 内容居中 */
        }

        .small-td td {
            font-size: 8pt;
            padding-left: 1px;
            padding-right: 1px;
        }

        /*td {*/
        /*    background-color: #f1f1f1; !* 表头背景色 *!*/
        /*}*/
    </style>
</head>
<body>

<!-- 页眉 -->
<header>
    <div class="header-content">
        <span class="header-text-en">Clinflash IRT&nbsp;&nbsp;<span
                class="header-text">{{.ConfigureReport}}</span></span>
        <img src="./assets/images/report_logo.png" alt="Logo" class="header-logo">
    </div>
</header>

<!-- 页脚 -->
<footer>
    <div class="footer-content">
        <div class="footer-text">{{.ProjectNumber}}({{.Env}})</div>
        <div class="footer-page-number"></div>
    </div>
</footer>


<!-- 封面 -->
<div class="cover-page">
    <div class="cover-content">
        <p>&nbsp;</p>
        <p>{{.Sponsor}}</p>
        <p>{{.ProjectNumber}}</p>
        <p>{{.ConfigureReport}}</p>
        <p>{{.Env}}</p>
        <p>{{.GenerationTime}}{{.CreateDate}}</p>
        <p>{{.Generator}}{{.CreateBy}}</p>
    </div>
</div>

<!-- 目录 -->
<div class="chapter" id="toc">
    <p>{{.Directory}}</p>
    <ul>
        <li><a href="#chapter-1-h1"></a></li>
        <li><a href="#chapter-2-h1"></a></li>
        {{if .IsGroupStage}}
        <li><a href="#chapter-3-h1"></a></li>
        {{end}}

        {{range .ConfigureDetailList}}
        <li>
            <a href="#chapter-{{.Index}}-h1"></a>
            <ul>
                <li>
                    <a href="#chapter-{{.Index}}.1-h2"></a>
                    <ul>
                        <li><a href="#chapter-{{.Index}}.1.1-h3"></a></li>
                        <li><a href="#chapter-{{.Index}}.1.2-h3"></a></li>
                        <li><a href="#chapter-{{.Index}}.1.3-h3"></a></li>
                    </ul>
                </li>

                {{if ne .RandomConfigure.Level ""}}
                <li>
                    <a href="#chapter-{{.Index}}{{.RandomConfigure.Level}}-h2"></a>
                    <ul>
                        {{if ne .RandomConfigure.RandomDesign.Level ""}}
                        <li>
                            <a href="#chapter-{{.Index}}{{.RandomConfigure.Level}}{{.RandomConfigure.RandomDesign.Level}}-h3"></a>
                        </li>
                        {{end}}
                        {{if ne .RandomConfigure.StratificationCalculation.Level ""}}
                        <li>
                            <a href="#chapter-{{.Index}}{{.RandomConfigure.Level}}{{.RandomConfigure.StratificationCalculation.Level}}-h3"></a>
                        </li>
                        {{end}}
                    </ul>
                </li>
                {{end}}

                {{if ne .FormConfigure.Level ""}}
                <li>
                    <a href="#chapter-{{.Index}}{{.FormConfigure.Level}}-h2"></a>
                </li>
                {{end}}

                {{if ne .IpManagement.Level ""}}
                <li>
                    <a href="#chapter-{{.Index}}{{.IpManagement.Level}}-h2"></a>
                    <ul>
                        {{if ne .IpManagement.VisitManagement.Level ""}}
                        <li>
                            <a href="#chapter-{{.Index}}{{.IpManagement.Level}}{{.IpManagement.VisitManagement.Level}}-h3"></a>
                        </li>
                        {{end}}
                        {{if ne .IpManagement.TreatmentDesign.Level ""}}
                        <li>
                            <a href="#chapter-{{.Index}}{{.IpManagement.Level}}{{.IpManagement.TreatmentDesign.Level}}-h3"></a>
                        </li>
                        {{end}}
                    </ul>
                </li>
                {{end}}
            </ul>
        </li>
        {{end}}
    </ul>
</div>

<!-- 章节 1 -->
<div class="chapter" id="chapter-1">
    <div class="content">
        <h1 id="chapter-1-h1">1 {{.Summary}}</h1>
        <p class="indentP">
            {{.SummaryDetails}}
        </p>
    </div>
</div>

<!-- 章节 2 -->
<div class="chapter" id="chapter-2">
    <div class="content">
        <h1 id="chapter-2-h1">2 {{.BasicInformation}}</h1>
        <table>
            <colgroup>
                <col style="width: 40%;">
                <col style="width: 60%;">
            </colgroup>
            <tr>
                <td>{{.ProjectTimeZoneLabel}}</td>
                <td>{{.ProjectTimeZone}}</td>
            </tr>
            <tr>
                <td>{{.ProjectTypeLabel}}</td>
                <td>{{.ProjectType}}</td>
            </tr>
            <tr>
                <td>{{.ProjectOrderCheckLabel}}</td>
                <td>{{.ProjectOrderCheck}}</td>
            </tr>
            <tr>
                <td>{{.ProjectOrderConfirmationLabel}}</td>
                <td>{{.ProjectOrderConfirmation}}</td>
            </tr>
            <tr>
                <td>{{.ProjectDeIsolationApprovalLabel}}</td>
                <td>{{.ProjectDeIsolationApproval}}</td>
            </tr>
            <tr>
                <td>{{.ProjectUnblindingControlLabel}}</td>
                <td>{{.ProjectUnblindingControl}}</td>
            </tr>
            <tr>
                <td>{{.ProjectOrderApprovalControlLabel}}</td>
                <td>{{.ProjectOrderApprovalControl}}</td>
            </tr>
            <tr>
                <td>{{.ProjectNoticeLabel}}</td>
                <td>{{.ProjectNotice}}</td>
            </tr>
            <tr>
                <td>{{.ProjectConnectEdcLabel}}</td>
                <td>{{.ProjectConnectEdc}}</td>
            </tr>
            <tr>
                <td>{{.ProjectPushModeLabel}}</td>
                <td>{{.ProjectPushMode}}</td>
            </tr>
            <tr>
                <td>{{.ProjectSynchronizationModeLabel}}</td>
                <td>{{.ProjectSynchronizationMode}}</td>
            </tr>
        </table>
    </div>
</div>

<!-- 章节 3 -->
{{if .IsGroupStage}}
<div class="chapter" id="chapter-3">
    <div class="content">
        <h1 id="chapter-3-h1">3 {{.CohortOrStage}}{{.GeneralSituation}}</h1>
        <p>{{.ProjectNumber}}&nbsp;({{.Env}}){{.AllOfThem}}{{.CohortOrStage}}:</p>
        <table>
            <tr>
                <td>{{.Name}}</td>
            </tr>
            {{range .CohortNameList}}
            <tr>
                <td>{{.}}</td>
            </tr>
            {{end}}
        </table>
    </div>
</div>
{{end}}

<!-- 章节 4-n -->
{{range .ConfigureDetailList}}
<div class="chapter content" id="chapter-{{.Index}}">
    <!--属性配置-->
    <div class="chapter">
        <h1 id="chapter-{{.Index}}-h1">{{.Index}} {{.Title}}</h1>
        <h2 id="chapter-{{.Index}}.1-h2">{{.Index}}.1 {{.AttributeConfigure.Title}}</h2>
        <!--系统配置-->
        <div>
            <h3 id="chapter-{{.Index}}.1.1-h3">{{.Index}}.1.1 {{.AttributeConfigure.SystemConfigure.Title}}</h3>
            <table>
                <colgroup>
                    <col style="width: 50%;">
                    <col style="width: 50%;">
                </colgroup>
                <tr>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.RandomizeLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.Randomize}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.DisplayRandomizationIDLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.DisplayRandomizationID}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.DisplayRandomSequenceNumberLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.DisplayRandomSequenceNumber}}
                    </td>
                </tr>
                {{if .AttributeConfigure.SystemConfigure.DisplayRandomSequenceNumberShowFlag}}
                <tr>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.RandomSequenceNumberPrefixLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.RandomSequenceNumberPrefix}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.RandomSequenceNumberDigitLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.RandomSequenceNumberDigit}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.RandomSequenceNumberStartLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.RandomSequenceNumberStart}}
                    </td>
                </tr>
                {{end}}
                <tr>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.BlindDesignLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.BlindDesign}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.TreatmentDesignLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.TreatmentDesign}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.DTPRuleLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.DTPRule}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.RandomizationSupplyCheckLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.RandomizationSupplyCheck}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.SubjectScreeningProcessLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SystemConfigure.SubjectScreeningProcess}}
                    </td>
                </tr>
            </table>
        </div>
        <!--受试者号规则-->
        <div>
            <h3 id="chapter-{{.Index}}.1.2-h3">{{.Index}}.1.2 {{.AttributeConfigure.SubjectIDRules.Title}}</h3>
            <table>
                <colgroup>
                    <col style="width: 50%;">
                    <col style="width: 50%;">
                </colgroup>
                <tr>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.SubjectNumberInputRuleLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.SubjectNumberInputRule}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.SubjectPrefixLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.SubjectPrefix}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.SubjectIDPrefixLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.SubjectIDPrefix}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.ReplacementTextForSubjectIDLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.ReplacementTextForSubjectID}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.ReplacementTextEnForSubjectIDLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.ReplacementTextEnForSubjectID}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.SubjectIDDigitLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.SubjectIDDigit}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.SubjectReplacementLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.SubjectIDRules.SubjectReplacement}}
                    </td>
                </tr>
            </table>
            <p>{{.AttributeConfigure.SubjectIDRules.TakeCare}}</p>
        </div>
        <!--其他规则-->
        <div>
            <h3 id="chapter-{{.Index}}.1.3-h3">{{.Index}}.1.3 {{.AttributeConfigure.OtherRules.Title}}</h3>
            <table>
                <colgroup>
                    <col style="width: 50%;">
                    <col style="width: 50%;">
                </colgroup>
                <tr>
                    <td>
                        {{.AttributeConfigure.OtherRules.StopUnblindedSubjectsLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.OtherRules.StopUnblindedSubjects}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.OtherRules.QuarantinedIPCountingRuleLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.OtherRules.QuarantinedIPCountingRule}}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{.AttributeConfigure.OtherRules.TransportAccordingPackagingLabel}}
                    </td>
                    <td>
                        {{.AttributeConfigure.OtherRules.TransportAccordingPackaging}}
                    </td>
                </tr>
            </table>
            <p>
                {{.AttributeConfigure.OtherRules.Deactivate}}
            </p>
            <p>
                {{.AttributeConfigure.OtherRules.Quarantine}}
            </p>
            <p>
                {{.AttributeConfigure.OtherRules.Packing}}
            </p>
        </div>
    </div>

    <!--随机配置-->
    {{if ne .RandomConfigure.Level ""}}
    <div class="chapter">
        <h2 id="chapter-{{.Index}}{{.RandomConfigure.Level}}-h2">{{.Index}}{{.RandomConfigure.Level}}
            {{.RandomConfigure.Title}}</h2>

        <!--随机设计-->
        {{if ne .RandomConfigure.RandomDesign.Level ""}}
        <div>
            <h3 id="chapter-{{.Index}}{{.RandomConfigure.Level}}{{.RandomConfigure.RandomDesign.Level}}-h3">
                {{.Index}}{{.RandomConfigure.Level}}{{.RandomConfigure.RandomDesign.Level}}
                {{.RandomConfigure.RandomDesign.Title}}</h3>
            <table>
                <tr>
                    <td>
                        {{.RandomConfigure.RandomDesign.RandomTypeLabel}}
                    </td>
                    <td colspan="5">
                        {{.RandomConfigure.RandomDesign.RandomType}}
                    </td>
                </tr>
                <!--组别-->
                {{if gt (len .RandomConfigure.RandomDesign.Group) 0}}
                <tr>
                    <td rowspan="{{len .RandomConfigure.RandomDesign.Group}}">
                        {{.RandomConfigure.RandomDesign.GroupLabel}}
                    </td>
                    <td colspan="5">{{index .RandomConfigure.RandomDesign.Group 0}}</td>
                </tr>
                {{range $index, $item := .RandomConfigure.RandomDesign.Group}}
                {{if ne $index 0}}
                <tr>
                    <td colspan="5">{{ $item }}</td>
                </tr>
                {{end}}
                {{end}}
                {{else}}
                <tr>
                    <td>{{.RandomConfigure.RandomDesign.GroupLabel}}</td>
                    <td colspan="5">/</td>
                </tr>
                {{end}}

                <!--地区分层-->
                <tr>
                    <td>
                        {{.RandomConfigure.RandomDesign.RegionFactorLabel}}
                    </td>
                    <td colspan="5">
                        {{.RandomConfigure.RandomDesign.RegionFactor}}
                    </td>
                </tr>

                <!--分层因素 开始-->
                {{if gt (len .RandomConfigure.RandomDesign.FactorList) 0}}
                <tr>
                    <td rowspan="{{.RandomConfigure.RandomDesign.Length}}">
                        {{.RandomConfigure.RandomDesign.FactorOptionLabel}}
                    </td>
                    <td>{{.RandomConfigure.RandomDesign.FieldNumberLabel}}</td>
                    <td>{{.RandomConfigure.RandomDesign.FieldNameLabel}}</td>
                    <td>{{.RandomConfigure.RandomDesign.ControlTypeLabel}}</td>
                    <td>{{.RandomConfigure.RandomDesign.OptionLabel}}</td>
                    <td>{{.RandomConfigure.RandomDesign.StatusLabel}}</td>
                </tr>
                {{range .RandomConfigure.RandomDesign.FactorList}}
                <tr>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">{{.FieldNumber}}</td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">{{.FieldName}}</td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">{{.ControlType}}</td>
                    <td>
                        {{if gt (len .Option) 0}}
                        {{index .Option 0}}
                        {{else }}
                        <span></span>
                        {{end}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">{{.Status}}</td>
                </tr>
                {{if gt (len .Option) 1}}
                {{range $optionIndex, $option := .Option}}
                {{if ne $optionIndex 0}}
                <tr>
                    <td>{{ $option }}</td>
                </tr>
                {{end}}
                {{end}}
                {{end}}
                {{end}}

                {{else }}
                <tr>
                    <td rowspan="2">{{.RandomConfigure.RandomDesign.FactorOptionLabel}}</td>
                    <td>{{.RandomConfigure.RandomDesign.FieldNumberLabel}}</td>
                    <td>{{.RandomConfigure.RandomDesign.FieldNameLabel}}</td>
                    <td>{{.RandomConfigure.RandomDesign.ControlTypeLabel}}</td>
                    <td>{{.RandomConfigure.RandomDesign.OptionLabel}}</td>
                    <td>{{.RandomConfigure.RandomDesign.StatusLabel}}</td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                {{end}}
                <!--分层因素 结束-->

            </table>
        </div>
        {{end}}

        <!--分层计算-->
        {{if ne .RandomConfigure.StratificationCalculation.Level ""}}
        <div>
            <h3 id="chapter-{{.Index}}{{.RandomConfigure.Level}}{{.RandomConfigure.StratificationCalculation.Level}}-h3">
                {{.Index}}{{.RandomConfigure.Level}}{{.RandomConfigure.StratificationCalculation.Level}}
                {{.RandomConfigure.StratificationCalculation.Title}}</h3>

            <table>
                <tr>
                    <td>
                        {{.RandomConfigure.StratificationCalculation.FieldNumberLabel}}
                    </td>
                    <td>
                        {{.RandomConfigure.StratificationCalculation.FormulaTypeLabel}}
                    </td>
                    <td>
                        {{.RandomConfigure.StratificationCalculation.CustomFormulaLabel}}
                    </td>
                    <td>
                        {{.RandomConfigure.StratificationCalculation.RetainDecimalsLabel}}
                    </td>
                    <td>
                        {{.RandomConfigure.StratificationCalculation.LayeredNameLabel}}
                    </td>
                    <td>
                        {{.RandomConfigure.StratificationCalculation.LayeredOptionLabel}}
                    </td>
                    <td>
                        {{.RandomConfigure.StratificationCalculation.StatusLabel}}
                    </td>
                </tr>
                {{if gt (len .RandomConfigure.StratificationCalculation.FieldList) 0}}
                {{range .RandomConfigure.StratificationCalculation.FieldList}}
                <tr>
                    <td rowspan="{{if gt (len .LayeredOption) 0}}{{len .LayeredOption}}{{else}}1{{end}}">
                        {{.FieldNumber}}
                    </td>
                    <td rowspan="{{if gt (len .LayeredOption) 0}}{{len .LayeredOption}}{{else}}1{{end}}">
                        {{.FormulaType}}
                    </td>
                    <td rowspan="{{if gt (len .LayeredOption) 0}}{{len .LayeredOption}}{{else}}1{{end}}">
                        {{.CustomFormula}}
                    </td>
                    <td rowspan="{{if gt (len .LayeredOption) 0}}{{len .LayeredOption}}{{else}}1{{end}}">
                        {{.RetainDecimals}}
                    </td>
                    <td rowspan="{{if gt (len .LayeredOption) 0}}{{len .LayeredOption}}{{else}}1{{end}}">
                        {{.LayeredName}}
                    </td>
                    <td>
                        {{if gt (len .LayeredOption) 0}}
                        {{index .LayeredOption 0}}
                        {{else}}
                        <span></span>
                        {{end}}
                    </td>

                    <td rowspan="{{if gt (len .LayeredOption) 0}}{{len .LayeredOption}}{{else}}1{{end}}">
                        {{.Status}}
                    </td>
                </tr>
                {{if gt (len .LayeredOption) 1}}
                {{range $optionIndex, $option := .LayeredOption}}
                {{if ne $optionIndex 0}}
                <tr>
                    <td>{{$option}}</td>
                </tr>
                {{end}}
                {{end}}
                {{end}}

                {{end}}
                {{else}}
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                {{end}}
            </table>

        </div>
        {{end}}
    </div>
    {{end}}

    <!--表单配置-->
    {{if ne .FormConfigure.Level ""}}
    <div class="chapter">
        <h2 id="chapter-{{.Index}}{{.FormConfigure.Level}}-h2">{{.Index}}{{.FormConfigure.Level}}
            {{.FormConfigure.Title}}</h2>

        <!--受试者登记-->
        {{if ne .FormConfigure.SubjectRegistration.Level ""}}
        <div class="small-part">
            <div class="small-title">
                {{.FormConfigure.SubjectRegistration.Level}} {{.FormConfigure.SubjectRegistration.Title}}
            </div>
            <table class="break-normal">
                <tr>
                    <td>
                        {{.FormConfigure.SubjectRegistration.FieldNameLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.SubjectRegistration.IsEditableLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.SubjectRegistration.RequiredLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.SubjectRegistration.VariableIdLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.SubjectRegistration.ControlTypeLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.SubjectRegistration.OptionLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.SubjectRegistration.FormatTypeLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.SubjectRegistration.VariableFormatLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.SubjectRegistration.VariableRangeLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.SubjectRegistration.StatusLabel}}
                    </td>
                </tr>
                {{if gt (len .FormConfigure.SubjectRegistration.FieldList) 0}}
                {{range .FormConfigure.SubjectRegistration.FieldList}}
                <tr>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.FieldName}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.IsEditable}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.Required}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.VariableId}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.ControlType}}
                    </td>
                    <td>
                        {{if gt (len .Option) 0}}
                        {{index .Option 0}}
                        {{else}}
                        <span></span>
                        {{end}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.FormatType}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.VariableFormat}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.VariableRange}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.Status}}
                    </td>
                </tr>
                {{if gt (len .Option) 1}}
                {{range $optionIndex, $option := .Option}}
                {{if ne $optionIndex 0}}
                <tr>
                    <td>{{$option}}</td>
                </tr>
                {{end}}
                {{end}}
                {{end}}


                {{end}}
                {{else}}
                <tr>
                    <td>
                        &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                </tr>
                {{end}}
            </table>
        </div>
        {{end}}

        <!--公式计算-->
        {{if ne .FormConfigure.CustomFormula.Level ""}}
        <div class="small-part">
            <div class="small-title">
                {{.FormConfigure.CustomFormula.Level}} {{.FormConfigure.CustomFormula.Title}}
            </div>
            <table>
                <tr>
                    <td>
                        {{.FormConfigure.CustomFormula.FieldNameLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.CustomFormula.RequiredLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.CustomFormula.VariableIdLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.CustomFormula.ControlTypeLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.CustomFormula.FormatTypeLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.CustomFormula.VariableFormatLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.CustomFormula.VariableRangeLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.CustomFormula.StatusLabel}}
                    </td>
                </tr>
                {{if gt (len .FormConfigure.CustomFormula.FieldList) 0}}
                {{range .FormConfigure.CustomFormula.FieldList}}
                <tr>
                    <td>
                        {{.FieldName}}
                    </td>
                    <td>
                        {{.Required}}
                    </td>
                    <td>
                        {{.VariableId}}
                    </td>
                    <td>
                        {{.ControlType}}
                    </td>
                    <td>
                        {{.FormatType}}
                    </td>
                    <td>
                        {{.VariableFormat}}
                    </td>
                    <td>
                        {{.VariableRange}}
                    </td>
                    <td>
                        {{.Status}}
                    </td>
                </tr>
                {{end}}
                {{else}}
                <tr>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                </tr>
                {{end}}
            </table>
        </div>
        {{end}}

        <!--剂量调整-->
        {{if ne .FormConfigure.DoseAdjustment.Level ""}}
        <div class="small-part">
            <div class="small-title">
                {{.FormConfigure.DoseAdjustment.Level}} {{.FormConfigure.DoseAdjustment.Title}}
            </div>
            <table>
                <tr>
                    <td>
                        {{.FormConfigure.DoseAdjustment.FieldNameLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.DoseAdjustment.RequiredLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.DoseAdjustment.VariableIdLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.DoseAdjustment.ControlTypeLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.DoseAdjustment.OptionLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.DoseAdjustment.StatusLabel}}
                    </td>
                </tr>
                {{if gt (len .FormConfigure.DoseAdjustment.FieldList) 0}}
                {{range .FormConfigure.DoseAdjustment.FieldList}}
                <tr>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.FieldName}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.Required}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.VariableId}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.ControlType}}
                    </td>
                    <td>
                        {{if gt (len .Option) 0}}
                        {{index .Option 0}}
                        {{else}}
                        <span></span>
                        {{end}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.Status}}
                    </td>
                </tr>
                {{if gt (len .Option) 1}}
                {{range $optionIndex, $option := .Option}}
                {{if ne $optionIndex 0}}
                <tr>
                    <td>{{$option}}</td>
                </tr>
                {{end}}
                {{end}}
                {{end}}

                {{end}}
                {{else}}
                <tr>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                </tr>
                {{end}}
            </table>
        </div>
        {{end}}

        <!--分层计算-->
        {{if ne .FormConfigure.LayeredCalculation.Level ""}}
        <div>
            <div class="small-title">
                {{.FormConfigure.LayeredCalculation.Level}} {{.FormConfigure.LayeredCalculation.Title}}
            </div>
            <table>
                <tr>
                    <td>
                        {{.FormConfigure.LayeredCalculation.FieldNameLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.LayeredCalculation.IsEditableLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.LayeredCalculation.RequiredLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.LayeredCalculation.VariableIdLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.LayeredCalculation.ControlTypeLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.LayeredCalculation.OptionLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.LayeredCalculation.FormatTypeLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.LayeredCalculation.VariableFormatLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.LayeredCalculation.VariableRangeLabel}}
                    </td>
                    <td>
                        {{.FormConfigure.LayeredCalculation.StatusLabel}}
                    </td>
                </tr>
                {{if gt (len .FormConfigure.LayeredCalculation.FieldList) 0}}
                {{range .FormConfigure.LayeredCalculation.FieldList}}
                <tr>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.FieldName}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.IsEditable}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.Required}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.VariableId}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.ControlType}}
                    </td>
                    <td>
                        {{if gt (len .Option) 0}}
                        {{index .Option 0}}
                        {{else}}
                        <span></span>
                        {{end}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.FormatType}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.VariableFormat}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.VariableRange}}
                    </td>
                    <td rowspan="{{if gt (len .Option) 0}}{{len .Option}}{{else}}1{{end}}">
                        {{.Status}}
                    </td>
                </tr>
                {{if gt (len .Option) 1}}
                {{range $optionIndex, $option := .Option}}
                {{if ne $optionIndex 0}}
                <tr>
                    <td>{{$option}}</td>
                </tr>
                {{end}}
                {{end}}
                {{end}}


                {{end}}
                {{else}}
                <tr>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                </tr>
                {{end}}
            </table>
        </div>
        {{end}}

    </div>
    {{end}}

    <!--研究产品管理-->
    {{if ne .IpManagement.Level ""}}
    <div class="chapter">
        <h2 id="chapter-{{.Index}}{{.IpManagement.Level}}-h2">{{.Index}}{{.IpManagement.Level}}
            {{.IpManagement.Title}}</h2>

        <!--访视管理-->
        {{if ne .IpManagement.VisitManagement.Level ""}}

        <h3 id="chapter-{{.Index}}{{.IpManagement.Level}}{{.IpManagement.VisitManagement.Level}}-h3">
            {{.Index}}{{.IpManagement.Level}}{{.IpManagement.VisitManagement.Level}}
            {{.IpManagement.VisitManagement.Title}}</h3>
        <table>
            <tr>
                <td colspan="3">
                    {{.IpManagement.VisitManagement.CycleVersionLabel}}
                </td>
                <td colspan="7">
                    {{.IpManagement.VisitManagement.CycleVersion}}
                </td>
            </tr>
            <tr>
                <td colspan="3">
                    {{.IpManagement.VisitManagement.VisitOffsetTypeLabel}}
                </td>
                <td colspan="7">
                    {{.IpManagement.VisitManagement.VisitOffsetType}}
                </td>
            </tr>
            <tr>
                <td>
                    {{.IpManagement.VisitManagement.VisitNumberLabel}}
                </td>
                <td>
                    {{.IpManagement.VisitManagement.VisitNameLabel}}
                </td>
                <td>
                    {{.IpManagement.VisitManagement.GroupLabel}}
                </td>
                <td>
                    {{.IpManagement.VisitManagement.IntervalDurationLabel}}
                </td>
                <td>
                    {{.IpManagement.VisitManagement.WindowLabel}}
                </td>
                <td>
                    {{.IpManagement.VisitManagement.IsDispenseLabel}}
                </td>
                <td>
                    {{.IpManagement.VisitManagement.IsRandomizeLabel}}
                </td>
                <td>
                    {{.IpManagement.VisitManagement.IsDTPLabel}}
                </td>
                <td>
                    {{.IpManagement.VisitManagement.IsSubjectReplaceLabel}}
                </td>
                <td>
                    {{.IpManagement.VisitManagement.IsDoseAdjustmentLabel}}
                </td>
            </tr>
            {{if gt (len .IpManagement.VisitManagement.VisitList) 0}}
            {{range .IpManagement.VisitManagement.VisitList}}
            <tr>
                <td>
                    {{.VisitNumber}}
                </td>
                <td>
                    {{.VisitName}}
                </td>
                <td>
                    {{.Group}}
                </td>
                <td>
                    {{.IntervalDuration}}
                </td>
                <td>
                    {{.Window}}
                </td>
                <td>
                    {{.IsDispense}}
                </td>
                <td>
                    {{.IsRandomize}}
                </td>
                <td>
                    {{.IsDTP}}
                </td>
                <td>
                    {{.IsSubjectReplace}}
                </td>
                <td>
                    {{.IsDoseAdjustment}}
                </td>
            </tr>
            {{end}}
            {{else}}
            <tr>
                <td>
                    &nbsp;
                </td>
                <td>
                    &nbsp;
                </td>
                <td>
                    &nbsp;
                </td>
                <td>
                    &nbsp;
                </td>
                <td>
                    &nbsp;
                </td>
                <td>
                    &nbsp;
                </td>
                <td>
                    &nbsp;
                </td>
                <td>
                    &nbsp;
                </td>
                <td>
                    &nbsp;
                </td>
                <td>
                    &nbsp;
                </td>
            </tr>
            {{end}}
        </table>
        {{end}}

        <!--研究产品配置-->
        {{if ne .IpManagement.TreatmentDesign.Level ""}}

        <h3 id="chapter-{{.Index}}{{.IpManagement.Level}}{{.IpManagement.TreatmentDesign.Level}}-h3">
            {{.Index}}{{.IpManagement.Level}}{{.IpManagement.TreatmentDesign.Level}}
            {{.IpManagement.TreatmentDesign.Title}}</h3>

        {{- $dtpRule := .AttributeConfigure.SystemConfigure.DTPRule -}}

        <!--DTP研究产品-->
        {{if or (eq $dtpRule "研究产品") (eq $dtpRule "IP")}}
        {{if ne .IpManagement.TreatmentDesign.DTPIp.Level ""}}
        <div class="small-part">
            <div class="small-title">
                {{.IpManagement.TreatmentDesign.DTPIp.Level}} {{.IpManagement.TreatmentDesign.DTPIp.Title}}
            </div>
            {{if gt (len .IpManagement.TreatmentDesign.DTPIp.DTPIpFieldList) 0}}
            <table>
                <colgroup>
                    <col style="width: 50%;">
                    <col style="width: 50%;">
                </colgroup>
                <tr>
                    <td>
                        {{.IpManagement.TreatmentDesign.DTPIp.IpLabel}}
                    </td>
                    <td>
                        {{.IpManagement.TreatmentDesign.DTPIp.DTPModeLabel}}
                    </td>
                </tr>
                {{range .IpManagement.TreatmentDesign.DTPIp.DTPIpFieldList}}
                <tr>
                    <td rowspan="{{len .DTPTypeList}}" style="{{if .IsOtherDrug}}color: #FFAE00;{{end}}">
                        {{.IpName}}
                    </td>
                    <td>
                        {{if gt (len .DTPTypeList) 0}}
                        {{index .DTPTypeList 0}}
                        {{else}}
                        <span></span>
                        {{end}}
                    </td>
                </tr>
                {{if gt (len .DTPTypeList) 1}}
                {{range $index, $type := .DTPTypeList}}
                {{if gt $index 0}}
                <tr>
                    <td>{{$type}}</td>
                </tr>
                {{end}}
                {{end}}
                {{end}}

                {{end}}
            </table>
            <p>
                {{.IpManagement.TreatmentDesign.DTPIp.TakeCare}}
            </p>
            {{end}}

        </div>
        {{end}}
        {{end}}

        <!--按标签/开放配置-->
        {{if ne .IpManagement.TreatmentDesign.LabelOpen.Level ""}}
        <div class="small-part">
            <div class="small-title">
                {{.IpManagement.TreatmentDesign.LabelOpen.Level}} {{.IpManagement.TreatmentDesign.LabelOpen.Title}}
            </div>
            <table>
                <tr class="small-td">
                    <td>
                        {{.IpManagement.TreatmentDesign.LabelOpen.GroupLabel}}
                    </td>
                    <td>
                        {{.IpManagement.TreatmentDesign.LabelOpen.VisitNameLabel}}
                    </td>
                    <td>
                        {{.IpManagement.TreatmentDesign.LabelOpen.IpNameLabel}}
                    </td>
                    <td>
                        {{.IpManagement.TreatmentDesign.LabelOpen.DispensationQuantityLabel}}
                    </td>
                    <td>
                        {{.IpManagement.TreatmentDesign.LabelOpen.CustomFormulaLabel}}
                    </td>
                    <td>
                        {{.IpManagement.TreatmentDesign.LabelOpen.CombinedDispensationLabel}}
                    </td>
                    <td>
                        {{.IpManagement.TreatmentDesign.LabelOpen.IpSpecificationLabel}}
                    </td>
                    <td>
                        {{.IpManagement.TreatmentDesign.LabelOpen.SpecificationLabel}}
                    </td>
                    <td>
                        {{.IpManagement.TreatmentDesign.LabelOpen.AutomaticAssignmentLabel}}
                    </td>
                    <td>
                        {{.IpManagement.TreatmentDesign.LabelOpen.CalculationUnitLabel}}
                    </td>
                </tr>
                {{if gt (len .IpManagement.TreatmentDesign.LabelOpen.LabelOpenFieldList) 0}}
                {{range .IpManagement.TreatmentDesign.LabelOpen.LabelOpenFieldList}}
                <tr>
                    <td>
                        {{.Group}}
                    </td>
                    <td style="max-width: 400px">
                        {{.VisitName}}
                    </td>
                    <td style="{{if .IsOtherDrug}}color: #FFAE00;{{end}}">
                        {{.IpName}}
                    </td>
                    <td>
                        {{.DispensationQuantity}}
                    </td>
                    <td>
                        {{.CustomFormula}}
                    </td>
                    <td>
                        {{.CombinedDispensation}}
                    </td>
                    <td>
                        {{.IpSpecification}}
                    </td>
                    <td>
                        {{.Specification}}
                    </td>
                    <td>
                        {{.AutomaticAssignment}}
                    </td>
                    <td>
                        {{.CalculationUnit}}
                    </td>
                </tr>
                {{end}}
                {{else}}
                <tr>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                    <td>
                       &nbsp;
                    </td>
                </tr>
                {{end}}
            </table>
            <p>
                {{.IpManagement.TreatmentDesign.LabelOpen.TakeCare}}
            </p>
        </div>
        {{end}}


        <!--按公式计算-->
        {{if ne .IpManagement.TreatmentDesign.FormulaConfig.Level ""}}
        <div>
            <div class="small-title">
                {{.IpManagement.TreatmentDesign.FormulaConfig.Level}}
                {{.IpManagement.TreatmentDesign.FormulaConfig.Title}}
            </div>

            <!--年龄-->
            {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.FormulaAgeFieldList) 0}}
            <div class="small-part">
                <div class="small-title-bold">
                    {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.Title}}:
                </div>
                <table>
                    <tr class="small-td">
                        <td>
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.GroupLabel}}
                        </td>
                        <td>
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.VisitNameLabel}}
                        </td>
                        <td>
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.IpNameLabel}}
                        </td>
                        <td>
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.IpSpecificationLabel}}
                        </td>
                        <td>
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.AgeRangeLabel}}
                        </td>
                        <td>
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.DispensationQuantityLabel}}
                        </td>
                        <td>
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.IsOpenIpLabel}}
                        </td>
                        <td>
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.KeepDecimalPlacesLabel}}
                        </td>
                    </tr>
                    {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.FormulaAgeFieldList) 0}}
                    {{range .IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.FormulaAgeFieldList}}
                    {{- $dispensationQuantity := .DispensationQuantity -}}
                    <tr>
                        <td rowspan="{{if gt (len .AgeRange) 0}}{{len .AgeRange}}{{else}}1{{end}}">
                            {{.Group}}
                        </td>
                        <td style="max-width: 400px" rowspan="{{if gt (len .AgeRange) 0}}{{len .AgeRange}}{{else}}1{{end}}">
                            {{.VisitName}}
                        </td>
                        <td rowspan="{{if gt (len .AgeRange) 0}}{{len .AgeRange}}{{else}}1{{end}}" style="{{if .IsOtherDrug}}color: #FFAE00;{{end}}">
                            {{.IpName}}
                        </td>
                        <td rowspan="{{if gt (len .AgeRange) 0}}{{len .AgeRange}}{{else}}1{{end}}">
                            {{.IpSpecification}}
                        </td>
                        <td>
                            {{if gt (len .AgeRange) 0}}
                            {{index .AgeRange 0}}
                            {{else}}
                            <span></span>
                            {{end}}
                        </td>
                        <td>
                            {{if gt (len .DispensationQuantity) 0}}
                            {{index .DispensationQuantity 0}}
                            {{else}}
                            <span></span>
                            {{end}}
                        </td>
                        <td rowspan="{{if gt (len .AgeRange) 0}}{{len .AgeRange}}{{else}}1{{end}}">
                            {{.IsOpenIp}}
                        </td>
                        <td rowspan="{{if gt (len .AgeRange) 0}}{{len .AgeRange}}{{else}}1{{end}}">
                            {{.KeepDecimalPlaces}}
                        </td>
                    </tr>
                    {{if gt (len .AgeRange) 1}}
                    {{range $index, $age := .AgeRange}}
                    {{if gt $index 0}}
                    <tr>
                        <td>{{$age}}</td>
                        <td>{{index $dispensationQuantity $index}}</td>
                    </tr>
                    {{end}}
                    {{end}}
                    {{end}}

                    {{end}}
                    {{else}}
                    <tr>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                    </tr>
                    {{end}}
                </table>
                <p>
                    {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaAge.TakeCare}}
                </p>
            </div>
            {{end}}

            <!--体重-->
            {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.FormulaWeightFieldList) 0}}
            <div class="small-part">
                <div class="small-title-bold">
                    {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.Title}}:
                </div>
                <table>
                    <tr class="small-td">
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.GroupLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.VisitNameLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.IpNameLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.IpSpecificationLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.WeightRangeLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.DispensationQuantityLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.IsOpenIpLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.KeepDecimalPlacesLabel}}
                        </td>
                        <td colspan="3">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.WeightComparisonCalculationLabel}}
                        </td>
                    </tr>
                    <tr class="small-td">
                        <td rowspan="1">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.ComparedWithLabel}}
                        </td>
                        <td rowspan="1">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.ChangeLabel}}
                        </td>
                        <td rowspan="1">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.CalculationLabel}}
                        </td>
                    </tr>
                    {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.FormulaWeightFieldList)
                    0}}
                    {{range .IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.FormulaWeightFieldList}}
                    {{- $dispensationQuantity := .DispensationQuantity -}}
                    <tr>
                        <td rowspan="{{if gt (len .WeightRange) 0}}{{len .WeightRange}}{{else}}1{{end}}">
                            {{.Group}}
                        </td>
                        <td rowspan="{{if gt (len .WeightRange) 0}}{{len .WeightRange}}{{else}}1{{end}}"
                            style="max-width: 400px">
                            {{.VisitName}}
                        </td>
                        <td rowspan="{{if gt (len .WeightRange) 0}}{{len .WeightRange}}{{else}}1{{end}}" style="{{if .IsOtherDrug}}color: #FFAE00;{{end}}">
                            {{.IpName}}
                        </td>
                        <td rowspan="{{if gt (len .WeightRange) 0}}{{len .WeightRange}}{{else}}1{{end}}">
                            {{.IpSpecification}}
                        </td>
                        <td>
                            {{if gt (len .WeightRange) 0}}
                            {{index .WeightRange 0}}
                            {{else}}
                            <span></span>
                            {{end}}
                        </td>
                        <td>
                            {{if gt (len .DispensationQuantity) 0}}
                            {{index .DispensationQuantity 0}}
                            {{else}}
                            <span></span>
                            {{end}}
                        </td>
                        <td rowspan="{{if gt (len .WeightRange) 0}}{{len .WeightRange}}{{else}}1{{end}}">
                            {{.IsOpenIp}}
                        </td>
                        <td rowspan="{{if gt (len .WeightRange) 0}}{{len .WeightRange}}{{else}}1{{end}}">
                            {{.KeepDecimalPlaces}}
                        </td>
                        <td rowspan="{{if gt (len .WeightRange) 0}}{{len .WeightRange}}{{else}}1{{end}}">
                            {{.ComparedWith}}
                        </td>
                        <td rowspan="{{if gt (len .WeightRange) 0}}{{len .WeightRange}}{{else}}1{{end}}">
                            {{.Change}}
                        </td>
                        <td rowspan="{{if gt (len .WeightRange) 0}}{{len .WeightRange}}{{else}}1{{end}}">
                            {{.Calculation}}
                        </td>
                    </tr>
                    {{if gt (len .WeightRange) 1}}
                    {{range $index, $weight := .WeightRange}}
                    {{if gt $index 0}}
                    <tr>
                        <td>{{$weight}}</td>
                        <td>{{index $dispensationQuantity $index}}</td>
                    </tr>
                    {{end}}
                    {{end}}
                    {{end}}


                    {{end}}
                    {{else}}
                    <tr>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                    </tr>
                    {{end}}
                </table>
                <p>
                    {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaWeight.TakeCare}}
                </p>
            </div>
            {{end}}

            <!--简易体表面积BSA-->
            {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.FormulaSimpleBSAFieldList) 0}}
            <div class="small-part">
                <div class="small-title-bold">
                    {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.Title}}:
                </div>
                <table>
                    <tr class="small-td">
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.GroupLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.VisitNameLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.IpNameLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.IpSpecificationLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.UnitCapacityLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.UnitCalculationStandardLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.IsOpenIpLabel}}
                        </td>
                        <td rowspan="1" colspan="3">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.WeightComparisonCalculationLabel}}
                        </td>
                    </tr>
                    <tr class="small-td">
                        <td rowspan="1">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.ComparedWithLabel}}
                        </td>
                        <td rowspan="1">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.ChangeLabel}}
                        </td>
                        <td rowspan="1">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.CalculationLabel}}
                        </td>
                    </tr>
                    {{if gt (len
                    .IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.FormulaSimpleBSAFieldList)
                    0}}
                    {{range .IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.FormulaSimpleBSAFieldList}}
                    <tr>
                        <td>
                            {{.Group}}
                        </td>
                        <td style="max-width: 400px">
                            {{.VisitName}}
                        </td>
                        <td style="{{if .IsOtherDrug}}color: #FFAE00;{{end}}">
                            {{.IpName}}
                        </td>
                        <td>
                            {{.IpSpecification}}
                        </td>
                        <td>
                            {{.UnitCapacity}}
                        </td>
                        <td>
                            {{.UnitCalculationStandard}}
                        </td>
                        <td>
                            {{.IsOpenIp}}
                        </td>
                        <td>
                            {{.ComparedWith}}
                        </td>
                        <td>
                            {{.Change}}
                        </td>
                        <td>
                            {{.Calculation}}
                        </td>
                    </tr>
                    {{end}}
                    {{else}}
                    <tr>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                    </tr>
                    {{end}}
                </table>
                <p>
                    {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaSimpleBSA.TakeCare}}
                </p>
            </div>
            {{end}}

            <!--其他体表面积(按自定义公式)：-->
            {{if gt (len .IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.FormulaOtherBSAFieldList) 0}}
            <div class="small-part">
                <div class="small-title-bold">
                    {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.Title}}:
                </div>
                <table>
                    <tr class="small-td">
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.GroupLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.VisitNameLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.IpNameLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.IpSpecificationLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.UnitCapacityLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.UnitCalculationStandardLabel}}
                        </td>
                        <td rowspan="2">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.IsOpenIpLabel}}
                        </td>
                        <td rowspan="1" colspan="3">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.WeightComparisonCalculationLabel}}
                        </td>
                    </tr>
                    <tr class="small-td">
                        <td rowspan="1">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.ComparedWithLabel}}
                        </td>
                        <td rowspan="1">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.ChangeLabel}}
                        </td>
                        <td rowspan="1">
                            {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.CalculationLabel}}
                        </td>
                    </tr>
                    {{if gt (len
                    .IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.FormulaOtherBSAFieldList)
                    0}}
                    {{range .IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.FormulaOtherBSAFieldList}}
                    <tr>
                        <td>
                            {{.Group}}
                        </td>
                        <td style="max-width: 400px">
                            {{.VisitName}}
                        </td>
                        <td style="{{if .IsOtherDrug}}color: #FFAE00;{{end}}">
                            {{.IpName}}
                        </td>
                        <td>
                            {{.IpSpecification}}
                        </td>
                        <td>
                            {{.UnitCapacity}}
                        </td>
                        <td>
                            {{.UnitCalculationStandard}}
                        </td>
                        <td>
                            {{.IsOpenIp}}
                        </td>
                        <td>
                            {{.ComparedWith}}
                        </td>
                        <td>
                            {{.Change}}
                        </td>
                        <td>
                            {{.Calculation}}
                        </td>
                    </tr>
                    {{end}}
                    {{else}}
                    <tr>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                    </tr>
                    {{end}}
                </table>
                <p>
                    {{.IpManagement.TreatmentDesign.FormulaConfig.FormulaOtherBSA.TakeCare}}
                </p>
            </div>
            {{end}}
        </div>
        {{end}}

        {{end}}
    </div>
    {{end}}

</div>
{{end}}

</body>
</html>