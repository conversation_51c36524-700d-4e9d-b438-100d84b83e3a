<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Clinflash IRT</title>
    <style>
        .text-body {
            font-size: 14px;
            color: dimgrey;
        }

        table.mytable {
            border-collapse: collapse;
            border: 0;
            border-spacing: 0;
        }

        table.mytable th {
            border: 0.5px solid #E0E5EB;
            background: #F1F3F7;
            padding: 5px;
            color: #4D587A;
        }

        table.mytable td {
            border: 0.5px solid #E0E5EB;
            padding: 5px;
            color: #4D587A;
            width: 300px;
            table-layout: fixed;
            word-break: break-all;
            word-wrap: break-word;
        }

        table.mytable tr:first-child th {
            border-top: none;
        }
        /*table.mytable tr:last-child td {*/
        /*    border-bottom: none;*/
        /*}*/
        table.mytable td:first-child,table.mytable th:first-child {
            border-left: none;
        }

        table.mytable td:last-child,
        table.mytable th:last-child {
            border-right: none;
        }
    </style>
</head>

<body>
    <table align="center" border="0" cellspacing="0" cellpadding="0"
        style="font-size: 14px; background-color: #fff; table-layout: fixed; border-top: #0A47ED solid 6px;">
        <tbody>
            <tr>
                <td colspan="12" style="padding: 40px 0 40px 50px;">
                    <img style="width: 238px; height: 40px;" src="{{.irt_url}}/api/img/mail" />
                </td>
            </tr>
            <tr>
                <td colspan="12" style="padding: 5px 50px;">
                    <div class="text-body">
                        {{if .projectNumberShow}}
                        <p>项目编号:<span>{{.projectNumber}}</span></p>
                        {{end}}
                        {{if .projectNameShow}}
                        <p>项目名称:<span>{{.projectName}}</span></p>
                        {{end}}
                        {{if .envNameShow}}
                        <p>项目环境:<span>{{.envName}}</span></p>
                        {{end}}
                        {{if .instituteType}}
                        <p>机构编号:<span>{{.instituteNumber}}</span></p>
                        {{end}}
                        <p>机构名称:<span>{{.instituteName}}</span></p>
                        <p>隔离编号:<span>{{.freezeNumber}}</span></p>
                        <p>解隔离时间:<span>{{.freezeDate}}</span></p>
                        <p>解隔离原因:<span>{{.reason}}</span></p>
                        {{if .results}}
                        <table class="mytable">
                            <tr style="text-align: left">
                                <th>批次号</th>
                                {{if .packageIsOpen}}
                                <th>包装号</th>
                                {{end}}
                                <th>研究产品</th>
                                <th>数量</th>
                                <th>有效期</th>
                            </tr>
                            {{range $index, $result := .results}}
                            {{range $indexDetail, $detail := $result.details}}
                            {{if eq $indexDetail 0}}
                            <tr>
                                <td rowspan={{$result.rowsSpan}}>{{$result.batch}}</td>
                                {{if $result.packageIsOpen}}
                                {{if eq $detail.packageNumber nil}}
                                <td>-</td>
                                {{else if eq $detail.packageNumber ""}}
                                <td>-</td>
                                {{else}}
                                <td>{{$detail.packageNumber}}</td>
                                {{end}}
                                {{end}}
                                <td class="td">{{$detail.number}}</td>
                                {{if $detail.packageMethod}}
                                <td>{{$detail.count }}({{$detail.packageCount}})</td>
                                {{else}}
                                <td>{{$detail.count}}</td>
                                {{end}}
                                <td rowspan={{$result.rowsSpan}}>{{$result.expiryDate}}</td>
                            </tr>
                            {{else}}
                            <tr>
                                {{if $result.packageIsOpen}}
                                {{if eq $detail.packageNumber nil}}
                                <td>-</td>
                                {{else if eq $detail.packageNumber ""}}
                                <td>-</td>
                                ·
                                {{else}}
                                <td>{{$detail.packageNumber}}</td>
                                {{end}}
                                {{end}}
                                <td class="td">{{$detail.number}}</td>
                                {{if $detail.packageMethod}}
                                <td>{{$detail.count }}({{$detail.packageCount}})</td>
                                {{else}}
                                <td>{{$detail.count}}</td>
                                {{end}}
                            </tr>
                            {{end}}
                            {{end}}
                            {{end}}
                        </table>
                        {{end}}
                    </div>
                </td>
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td colspan="12"
                    style="background-color: #F8F8F9; padding: 16px 50px; font-size: 10px; line-height: 20px; color: #A4A9B3">
                    <div>{{.Security}}</div>
                    <div>{{.Noreply}}</div>
                    <div>{{.Copyright}}</div>
                </td>
            </tr>
        </tfoot>
    </table>
</body>