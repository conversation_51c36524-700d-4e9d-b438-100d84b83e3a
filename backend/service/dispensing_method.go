package service

import (
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	datastructure "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/wxnacy/wgo/arrays"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func GetDispensingList(ctx *gin.Context, subjectID string, roleID string) ([]models.ResDispensing, error) {
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	match := bson.M{"subject_id": subjectOID}

	// 查询该项目是否为盲法
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var attribute models.Attribute
	attributeFilter := bson.M{"env_id": subject.EnvironmentID}
	if !subject.CohortID.IsZero() {
		attributeFilter = bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
	}
	err = tools.Database.Collection("attribute").FindOne(nil, attributeFilter).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, attributeFilter).Decode(&visitCycle)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	visitCycleMap := map[string]models.VisitCycleInfo{}
	for _, info := range visitCycle.Infos {
		visitCycleMap[info.Number] = info
	}
	strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if strTimeZone == "" {
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return nil, err
		}
		strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
		//strTimeZone = fmt.Sprintf("UTC%+d", zone)
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

	var dispensing []models.ResDispensing
	cursor, err := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine_order",
			"let": bson.M{
				"dispensing_id": "$_id",
				//"order_number":  "$order",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$dispensing_id", "$$dispensing_id"}}}},
				//bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$order_number", "$$order_number"}}}},
			},

			"as": "medicine_order",
		}}},
		//{{Key: "$unwind", Value: bson.M{"path": "$medicine_order", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$sort", Value: bson.M{"serial_number": 1}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	isBlindedRole := false
	if roleID != "" {
		isBlindedRole, err = tools.IsBlindedRole(roleID)
		if err != nil {
			return nil, err
		}
	}

	// TODO 7064

	subjectMap := make(map[string]models.Subject)
	if !visitCycle.BaseCohort.IsZero() && visitCycle.BaseCohort != subject.CohortID {
		subjectMap, err = task.GetBaseCohortSubjectMap(nil, visitCycle.BaseCohort, primitive.NilObjectID, subject.Info[0].Value.(string))
	}

	lastTime := time.Duration(0)
	afterRandom := false
	interval := float64(0)

	firstTime := time.Duration(0)

	for i, resDispensing := range dispensing {
		if !resDispensing.VisitSign {
			// TODO 7064
			randomTime := subject.RandomTime
			if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
				subject.RandomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
			}
			if firstTime == 0 && resDispensing.DispensingTime != 0 && visitCycleMap[resDispensing.VisitInfo.Number].Interval != nil && randomTime == 0 {
				randomTime = time.Duration(time.Unix(int64(resDispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleMap[resDispensing.VisitInfo.Number].Interval)).Unix())
				firstTime = resDispensing.DispensingTime
			}

			dispensing[i].Period = handlePeriod(afterRandom, visitCycle.VisitType, visitCycleMap[resDispensing.VisitInfo.Number], randomTime, lastTime, resDispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
			if resDispensing.Status == 2 {
				interval = 0
				lastTime = resDispensing.DispensingTime
			}
		}
		if resDispensing.VisitInfo.Random {
			afterRandom = true
		}

		// 兼容访视发布 不更新编号 名称的情况
		dispensing[i].VisitInfo.Dispensing = visitCycleMap[resDispensing.VisitInfo.Number].Dispensing
		dispensing[i].DTP = visitCycleMap[resDispensing.VisitInfo.Number].DTP

		for j, medicine := range resDispensing.DispensingMedicines {
			canRetrieval := true
			// isOpenDrug, _ := tools.IsOpenDrug(subject.EnvironmentID, subject.CohortID, medicine.Name)
			// isOtherDrug, _ := tools.IsOtherDrug(subject.EnvironmentID, medicine.Name)
			// if isBlindedRole && !isOpenDrug && !isOtherDrug {
			// 	dispensing[i].DispensingMedicines[j].Name = tools.BlindData
			// }
			isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
			if isBlindedRole && isBlindedDrug {
				dispensing[i].DispensingMedicines[j].Name = tools.BlindData
			}
			for _, orderDispensing := range resDispensing.MedicineOrder {
				if orderDispensing.Status != 5 && orderDispensing.Status != 8 && orderDispensing.Status != 9 {
					for _, id := range orderDispensing.Medicines {
						if medicine.MedicineID == id && medicine.RealMedicineID == primitive.NilObjectID {
							canRetrieval = false
						}
					}
				}
			}
			if canRetrieval {
				dispensing[i].CanRetrieval = append(dispensing[i].CanRetrieval, dispensing[i].DispensingMedicines[j])
			}

		}
		for j, medicine := range resDispensing.OtherDispensingMedicines {
			count := 0
			isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
			if isBlindedRole && isBlindedDrug {
				dispensing[i].OtherDispensingMedicines[j].Name = tools.BlindData
			}
			for _, orderDispensing := range resDispensing.MedicineOrder {
				if orderDispensing.Status != 5 && orderDispensing.Status != 8 && orderDispensing.Status != 9 {
					for _, item := range orderDispensing.OtherMedicines {
						if medicine.Name == item.Name && medicine.Batch == item.Batch && medicine.ExpireDate == item.ExpireDate {
							count = count + item.UseCount
						}
					}
				}
			}
			if count != medicine.Count {
				dispensing[i].OtherCanRetrieval = append(dispensing[i].OtherCanRetrieval, dispensing[i].OtherDispensingMedicines[j])
			}
		}
		if isBlindedRole {
			for j, medicine := range resDispensing.ReplaceMedicines {
				// isOpenDrug, _ := tools.IsOpenDrug(subject.EnvironmentID, subject.CohortID, medicine.Name)
				// isOtherDrug, _ := tools.IsOtherDrug(subject.EnvironmentID, medicine.Name)
				// if !isOpenDrug && !isOtherDrug {
				// 	dispensing[i].ReplaceMedicines[j].Name = tools.BlindData
				// }
				isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
				if isBlindedRole && isBlindedDrug {
					dispensing[i].ReplaceMedicines[j].Name = tools.BlindData
				}
			}
			for j, medicine := range resDispensing.RealDispensingMedicines {
				// isOpenDrug, _ := tools.IsOpenDrug(subject.EnvironmentID, subject.CohortID, medicine.Name)
				// isOtherDrug, _ := tools.IsOtherDrug(subject.EnvironmentID, medicine.Name)
				// if !isOpenDrug && !isOtherDrug {
				// 	dispensing[i].RealDispensingMedicines[j].Name = tools.BlindData
				// }
				isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
				if isBlindedRole && isBlindedDrug {
					dispensing[i].RealDispensingMedicines[j].Name = tools.BlindData
				}
			}
			for j, medicine := range resDispensing.RealOtherDispensingMedicines {
				isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
				//isOtherDrug, _ := tools.IsOtherDrug(subject.EnvironmentID, medicine.Name)
				if isBlindedDrug {
					dispensing[i].RealOtherDispensingMedicines[j].Name = tools.BlindData
				}
			}
			for j, medicine := range resDispensing.CancelMedicinesHistory {
				isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
				if isBlindedRole && isBlindedDrug {
					dispensing[i].CancelMedicinesHistory[j].Name = tools.BlindData
				}
			}
		}
	}
	return dispensing, nil
}

// PatchAppDispenseTask app发放任务
func PatchAppDispenseTask(ctx *gin.Context, subjectID string, roleID string) error {
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)

	resDispensingList, err := GetDispensingList(ctx, subjectID, roleID)
	if err != nil {
		return err
	}

	// 查询该项目是否为盲法
	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return errors.WithStack(err)
	}

	var attribute models.Attribute
	filter := bson.M{"customer_id": subject.CustomerID, "env_id": subject.EnvironmentID}
	if !subject.CohortID.IsZero() {
		filter = bson.M{"customer_id": subject.CustomerID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
	}

	err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return errors.WithStack(err)
	}

	dispatchMatch := bson.M{
		"project_id": subject.ProjectID,
		"env_id":     subject.EnvironmentID,
	}

	if !subject.CohortID.IsZero() {
		dispatchMatch["cohort_id"] = subject.CohortID
	}

	// 查询访视周期
	visitCycles := make([]models.VisitCycle, 0)
	cursor, err := tools.Database.Collection("visit_cycle").Find(nil, dispatchMatch)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return errors.WithStack(err)
	}
	visitCycleP, _ := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
		return item.EnvironmentID == subject.EnvironmentID && item.CohortID == subject.CohortID
	})
	visitCycle := models.VisitCycle{}
	if visitCycleP != nil {
		visitCycle = *visitCycleP
	}

	randomIndex := -1
	for index, visitCycleInfo := range visitCycle.Infos {
		if visitCycleInfo.Random {
			randomIndex = index
			break
		}
	}

	//标记是否需要发药
	dispensingSign := true
	if randomIndex == 0 && (subject.Status == 1 || subject.Status == 2 || subject.Status == 7 || subject.Status == 8) { //第一个是随机操作  受试者未随机 不显示发药
		dispensingSign = false
	}

	var projectRolePermission models.ProjectRolePermission
	err = tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": roleOID}).Decode(&projectRolePermission)
	if err != nil {
		return errors.WithStack(err)
	}

	//TODO 判断是否有发药权限--operation.subject.medicine.dispensing、operation.subject-dtp.medicine.dispensing
	index := arrays.ContainsString(projectRolePermission.Permissions, "operation.subject.medicine.dispensing")
	dtpIndex := arrays.ContainsString(projectRolePermission.Permissions, "operation.subject-dtp.medicine.dispensing")

	// TODO 7064
	subjectMap := make(map[string]models.Subject)
	if !visitCycle.BaseCohort.IsZero() && visitCycle.BaseCohort != subject.CohortID {
		subjectMap, err = task.GetBaseCohortSubjectMap(nil, visitCycle.BaseCohort, primitive.NilObjectID, subject.Info[0].Value.(string))
	}
	if (index != -1 || dtpIndex != -1) && dispensingSign {
		callback := func(sctx mongo.SessionContext) (interface{}, error) {
			permissions := []string{"operation.subject.medicine.dispensing"}
			if dtpIndex != -1 {
				permissions = []string{"operation.subject-dtp.medicine.dispensing"}
			}
			siteOrStoreIDs := []primitive.ObjectID{subject.ProjectSiteID}
			userIds, err := tools.GetPermissionUserIds(sctx, permissions, subject.ProjectID, subject.EnvironmentID, siteOrStoreIDs...)
			if len(userIds) != 0 {
				//resDispensingStatus := -1
				orderIsConfirm := true
				randomTime := subject.RandomTime
				if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
					subject.RandomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
				}
				for _, resDispensing := range resDispensingList {
					if resDispensing.Status == 1 {
						//periodMax := 0
						//待发药--插入任务表中
						match := bson.M{"customer_id": resDispensing.CustomerID, "project_id": resDispensing.ProjectID, "env_id": resDispensing.EnvironmentID}
						if resDispensing.CohortID != primitive.NilObjectID {
							match = bson.M{"customer_id": resDispensing.CustomerID, "project_id": resDispensing.ProjectID, "env_id": resDispensing.EnvironmentID, "cohort_id": resDispensing.CohortID}
						}
						var visit models.VisitCycle
						err = tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visit)
						if err != nil {
							return nil, errors.WithStack(err)
						}

						//resDispensingStatus = resDispensing.Status
						checkCondition := bson.M{
							"customer_id":        subject.CustomerID,
							"project_id":         subject.ProjectID,
							"env_id":             subject.EnvironmentID,
							"info.work_type":     11,
							"info.dispensing_id": resDispensing.ID,
							"info.status":        0,
							"deleted":            bson.M{"$ne": true},
						}
						if subject.CohortID != primitive.NilObjectID {
							checkCondition = bson.M{
								"customer_id":        subject.CustomerID,
								"project_id":         subject.ProjectID,
								"env_id":             subject.EnvironmentID,
								"cohort_id":          subject.CohortID,
								"info.work_type":     11,
								"info.dispensing_id": resDispensing.ID,
								"info.status":        0,
								"deleted":            bson.M{"$ne": true},
							}
						}
						if count, _ := tools.Database.Collection("work_task").CountDocuments(sctx, checkCondition); count == 0 {

							if orderIsConfirm {

								visitCycleMap := map[string]models.VisitCycleInfo{}
								for _, info := range visit.Infos {
									visitCycleMap[info.Number] = info
								}
								strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
								if err != nil {
									return nil, errors.WithStack(err)
								}
								if strTimeZone == "" {
									zone, err := tools.GetTimeZone(subject.ProjectID)
									if err != nil {
										return nil, err
									}
									//strTimeZone = fmt.Sprintf("UTC%+d", zone)
									strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
								}
								//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
								//intTimeZone, _ := strconv.ParseFloat(strings.Replace(strTimeZone, "UTC", "", 1), 64)
								intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

								lastTime := time.Duration(0)
								firstTime := time.Duration(0)
								afterRandom := false
								interval := float64(0)
								var period models.Period
								for _, res := range resDispensingList {
									if !res.VisitSign {
										if res.Status == 2 {
											interval = 0
											lastTime = res.DispensingTime
										}
										if res.VisitInfo.Random {
											afterRandom = true
										}

										if firstTime == 0 && res.DispensingTime != 0 && visitCycleMap[resDispensing.VisitInfo.Number].Interval != nil && randomTime == 0 {
											randomTime = time.Duration(time.Unix(int64(res.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleMap[resDispensing.VisitInfo.Number].Interval)).Unix())
											firstTime = res.DispensingTime
										}
										if res.ID == resDispensing.ID {
											// TODO 7064

											period = handlePeriod(afterRandom, visit.VisitType, visitCycleMap[resDispensing.VisitInfo.Number], randomTime, lastTime, resDispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)

										}
									}
								}

								workTask := models.WorkTask{
									ID:            primitive.NewObjectID(),
									CustomerID:    subject.CustomerID,
									ProjectID:     subject.ProjectID,
									EnvironmentID: subject.EnvironmentID,
									CohortID:      subject.CohortID,
									UserIDs:       userIds,
									Info: models.WorkTaskInfo{
										WorkType:    11,
										Status:      0,
										CreatedTime: time.Duration(time.Now().Unix()),
										//Deadline:    time.Duration(time.Now().AddDate(0, 0, periodMax).Unix()),
										MedicineIDs: []primitive.ObjectID{},
										//MedicineOrderID: orderId,
										DispensingID: resDispensing.ID,
									},
									Deleted: false,
								}
								if len(period.MaxPeriod) != 0 {
									workTask.Info.Deadline = time.Duration(period.MaximumTime)
								} else {
									if subject.RandomTime != 0 {
										workTask.Info.Deadline = subject.RandomTime
									}
									if subject.JoinTime != "" {
										parse, _ := time.Parse("2006-01-02", subject.JoinTime)

										hours := time.Duration(intTimeZone)
										minutes := time.Duration((intTimeZone - float64(hours)) * 60)

										duration := hours*time.Hour + minutes*time.Minute
										eTime := parse.Add(duration)
										workTask.Info.Deadline = time.Duration(eTime.Unix())
									}
								}

								_, err = tools.Database.Collection("work_task").InsertOne(ctx, workTask)
								if err != nil {
									return nil, errors.WithStack(err)
								}
								break
							}
							break
						}
						break
					} else if resDispensing.Status == 2 {
						condition := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "subject_id": resDispensing.SubjectID, "dispensing_id": resDispensing.ID}
						if count, _ := tools.Database.Collection("medicine_order").CountDocuments(ctx, condition); count > 0 {
							var order models.MedicineOrder
							err = tools.Database.Collection("medicine_order").FindOne(ctx, condition).Decode(&order)
							if err != nil {
								return nil, errors.WithStack(err)
							}
							if order.Status == 6 {
								orderIsConfirm = false
							}
						}
					}
				}
			}
			return nil, nil
		}
		err = tools.Transaction(callback)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func customDispensingNumberToSelectOption(number string) []int {
	parts := strings.Split(number, ",") // 以逗号为分隔符分割字符串
	//data := []map[string]int{}
	data := []int{}
	for _, part := range parts {
		if strings.Contains(part, "~") { // 如果字符串中包含波浪线
			rangeParts := strings.Split(part, "~")  // 以波浪线为分隔符分割字符串
			start, _ := strconv.Atoi(rangeParts[0]) // 起始数字
			end, _ := strconv.Atoi(rangeParts[1])   // 结束数字
			for i := start; i <= end; i++ {
				if i == 0 {
					continue
				}
				data = append(data, i)
			}
		} else {
			num, _ := strconv.Atoi(part) // 将单个数字转换为整数
			if num == 0 {
				continue
			}
			data = append(data, num)

		}
	}
	return data
}

// PatchAppDispenseTaskAgainPush 研究产品管理发布-更新app发放任务
func PatchAppDispenseTaskAgainPush(sctx mongo.SessionContext, customerID string, projectID string, envID string, cohortID string, roleID string) error {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "deleted": bson.M{"$ne": true}}
	if cohortID != "" {
		filter["cohort_id"] = cohortOID
	}

	subjects := make([]models.Subject, 0)
	cursor, err := tools.Database.Collection("subject").Find(nil, filter, &options.FindOptions{
		Projection: bson.M{
			"info": 1,
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &subjects)
	if err != nil {
		return errors.WithStack(err)
	}
	//新增研究产品管理并发布
	for _, subject := range subjects {
		subjectID := subject.ID.Hex()
		resDispensingList, err := GetDispensingList(nil, subjectID, roleID)
		if err != nil {
			return errors.WithStack(err)
		}

		dispatchMatch := bson.M{
			"project_id": subject.ProjectID,
			"env_id":     subject.EnvironmentID,
		}

		if !subject.CohortID.IsZero() {
			dispatchMatch["cohort_id"] = subject.CohortID
		}

		// 查询访视周期
		visitCycles := make([]models.VisitCycle, 0)
		cursor, err := tools.Database.Collection("visit_cycle").Find(nil, dispatchMatch)
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(nil, &visitCycles)
		if err != nil {
			return errors.WithStack(err)
		}
		visitCycleP, _ := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
			return item.EnvironmentID == subject.EnvironmentID && item.CohortID == subject.CohortID
		})
		visitCycle := models.VisitCycle{}
		if visitCycleP != nil {
			visitCycle = *visitCycleP
		}

		randomIndex := -1
		for index, visitCycleInfo := range visitCycle.Infos {
			if visitCycleInfo.Random {
				randomIndex = index
				break
			}
		}

		//标记是否需要发药
		dispensingSign := true
		if randomIndex == 0 && (subject.Status == 1 || subject.Status == 2 || subject.Status == 7 || subject.Status == 8) { //第一个是随机操作  受试者未随机 不显示发药
			dispensingSign = false
		}

		var projectRolePermission models.ProjectRolePermission
		err = tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": roleOID}).Decode(&projectRolePermission)
		if err != nil {
			return errors.WithStack(err)
		}

		//TODO 判断是否有发药权限--operation.subject.medicine.dispensing、"operation.subject-dtp.medicine.dispensing"
		index := arrays.ContainsString(projectRolePermission.Permissions, "operation.subject.medicine.dispensing")
		dtpIndex := arrays.ContainsString(projectRolePermission.Permissions, "operation.subject-dtp.medicine.dispensing")
		if (index != -1 || dtpIndex != -1) && dispensingSign {
			callback := func(sctx mongo.SessionContext) (interface{}, error) {
				permissions := []string{"operation.subject.medicine.dispensing"}
				if dtpIndex != -1 {
					permissions = []string{"operation.subject-dtp.medicine.dispensing"}
				}
				siteOrStoreIDs := []primitive.ObjectID{subject.ProjectSiteID}
				userIds, err := tools.GetPermissionUserIds(sctx, permissions, subject.ProjectID, subject.EnvironmentID, siteOrStoreIDs...)

				for _, resDispensing := range resDispensingList {
					if resDispensing.Status == 1 {
						periodMax := 0
						//待发药--插入任务表中
						match := bson.M{"customer_id": resDispensing.CustomerID, "project_id": resDispensing.ProjectID, "env_id": resDispensing.EnvironmentID}
						if resDispensing.CohortID != primitive.NilObjectID {
							match = bson.M{"customer_id": resDispensing.CustomerID, "project_id": resDispensing.ProjectID, "env_id": resDispensing.EnvironmentID, "cohort_id": resDispensing.CohortID}
						}
						var visit models.VisitCycle
						err = tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visit)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						firstDispenseRandomly := true
						no := -1
						isRandomlyDispense := true
						if visit.Infos != nil && len(visit.Infos) > 0 {
							for i, info := range visit.Infos {
								if info.Random == true {
									no = i
								}
								if info.ID == resDispensing.VisitInfo.VisitCycleInfoID {
									if info.PeriodMax != nil {
										periodMax = int(*info.PeriodMax)
									}
									if no != -1 && no > i {
										isRandomlyDispense = false
									}
									if no == -1 {
										isRandomlyDispense = false
									}
								}
								if i == 0 {
									if info.Random == false {
										firstDispenseRandomly = false
									}
								}
							}
						}

						checkCondition := bson.M{
							"customer_id":        subject.CustomerID,
							"project_id":         subject.ProjectID,
							"env_id":             subject.EnvironmentID,
							"cohort_id":          subject.CohortID,
							"info.work_type":     11,
							"info.dispensing_id": resDispensing.ID,
							"info.status":        0,
						}
						if count, _ := tools.Database.Collection("work_task").CountDocuments(sctx, checkCondition); count == 0 {

							if firstDispenseRandomly || (!firstDispenseRandomly && isRandomlyDispense) {
								workTask := models.WorkTask{
									ID:            primitive.NewObjectID(),
									CustomerID:    subject.CustomerID,
									ProjectID:     subject.ProjectID,
									EnvironmentID: subject.EnvironmentID,
									CohortID:      subject.CohortID,
									UserIDs:       userIds,
									Info: models.WorkTaskInfo{
										WorkType:    11,
										Status:      0,
										CreatedTime: time.Duration(time.Now().Unix()),
										Deadline:    time.Duration(time.Now().AddDate(0, 0, periodMax).Unix()),
										MedicineIDs: []primitive.ObjectID{},
										//MedicineOrderID: orderId,
										DispensingID: resDispensing.ID,
									},
									Deleted: false,
								}
								_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
								if err != nil {
									return nil, errors.WithStack(err)
								}
								break
							}
							break
						}
						break
					}
				}
				return nil, nil
			}
			err = tools.Transaction(callback)
			if err != nil {
				return errors.WithStack(err)
			}
		}
	}
	//删除研究产品管理并发布
	var workTaskList []models.WorkTask
	workTaskCursor, err := tools.Database.Collection("work_task").Find(nil, bson.M{"info.work_type": 11, "info.status": 0})
	if err != nil {
		return errors.WithStack(err)
	}
	err = workTaskCursor.All(nil, &workTaskList)
	if err != nil {
		return errors.WithStack(err)
	}
	for _, task := range workTaskList {
		var dispensing models.Dispensing
		err := tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"_id": task.Info.DispensingID}).Decode(&dispensing)
		if err != nil {
			return errors.WithStack(err)
		}
		if err == mongo.ErrNoDocuments {
			var work models.WorkTask
			err := tools.Database.Collection("work_task").FindOneAndUpdate(sctx, bson.M{"info.dispensing_id": task.Info.DispensingID, "info.status": 0}, bson.M{"$set": bson.M{"deleted": true}}).Decode(&work)
			if err != nil && err != mongo.ErrNoDocuments {
				return errors.WithStack(err)
			}
		}
	}

	return nil
}

func otherDispensingMedicinesHandle(DtpIpList []models.DtpIp, dispensing []models.ResDispensing, resDispensing models.ResDispensing, subject models.Subject, isBlindedRole bool, i int) {

	otherDispensingMedicines := []models.OtherDispensingMedicineRes{}
	for _, medicine := range resDispensing.OtherDispensingMedicines {
		updateIndex := -1
		_, ok := slice.Find(otherDispensingMedicines, func(index int, item models.OtherDispensingMedicineRes) bool {
			updateIndex = index
			return medicine.Name == item.Name && medicine.Batch == item.Batch && medicine.ExpireDate == item.ExpireDate
		})
		if ok {
			otherDispensingMedicines[updateIndex].Count = otherDispensingMedicines[updateIndex].Count + medicine.Count
		} else {
			otherDispensingMedicines = append(otherDispensingMedicines, medicine)
		}
	}

	for j, medicine := range otherDispensingMedicines {
		DTPs := getDTPInfo(DtpIpList, nil, medicine.Name)
		otherDispensingMedicines[j].DTPs = DTPs

		count := 0

		for _, orderDispensing := range resDispensing.MedicineOrder {
			if orderDispensing.Status != 5 && orderDispensing.Status != 8 && orderDispensing.Status != 9 {
				for _, other := range resDispensing.OthersDispensingMedicines {
					_, ok := slice.Find(orderDispensing.OtherMedicinesNew, func(index int, item primitive.ObjectID) bool {
						return other.MedicineID == item && other.BatchNumber == medicine.Batch && other.Name == medicine.Name && other.ExpirationDate == medicine.ExpireDate
					})
					if ok {
						count++
					}
				}

			}
		}
		isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
		if isBlindedRole && isBlindedDrug {
			otherDispensingMedicines[j].Name = tools.BlindData
		}
		if count != medicine.Count {
			dispensing[i].OtherCanRetrieval = append(dispensing[i].OtherCanRetrieval, otherDispensingMedicines[j])
		}
	}

	dispensing[i].OtherDispensingMedicines = otherDispensingMedicines

}

func getVisitInfo(customerOID primitive.ObjectID, projectOID primitive.ObjectID, envOID primitive.ObjectID, cohortOID primitive.ObjectID, visitOID primitive.ObjectID) (models.VisitCycle, models.VisitCycleInfo, error) {
	var visitCycle models.VisitCycle
	err := tools.Database.Collection("visit_cycle").FindOne(nil, bson.M{"env_id": envOID, "cohort_id": cohortOID}).Decode(&visitCycle)
	if err != nil {
		return models.VisitCycle{}, models.VisitCycleInfo{}, errors.WithStack(err)
	}

	visitInfoP, _ := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
		return item.ID == visitOID
	})
	visitInfo := *visitInfoP
	return visitCycle, visitInfo, nil

}

func getVisitInfos(envOID primitive.ObjectID, cohortOID primitive.ObjectID, visitOID primitive.ObjectID) ([]models.VisitCycle, models.VisitCycle, models.VisitCycleInfo, error) {
	var visitCycles []models.VisitCycle
	cursor, err := tools.Database.Collection("visit_cycle").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return nil, models.VisitCycle{}, models.VisitCycleInfo{}, errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return nil, models.VisitCycle{}, models.VisitCycleInfo{}, errors.WithStack(err)

	}

	visitCycleP, ok := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
		return item.CohortID == cohortOID
	})

	var visitCycle models.VisitCycle
	if ok {
		visitCycle = *visitCycleP
	}

	visitCycleInfoP, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
		return item.ID == visitOID
	})

	var visitCycleInfo models.VisitCycleInfo
	if ok {
		visitCycleInfo = *visitCycleInfoP
	}

	return visitCycles, visitCycle, visitCycleInfo, err
}

func mail(ctx *gin.Context, mails []models.Mail, dispensing models.Dispensing,
	dispensingTime time.Duration, operationTime time.Duration, registerNumberReq string,
	visitCycle models.VisitCycle, visitInfo models.VisitCycleInfo, medicineNumberReq []string, title string, replaceNumberReq []string, subjectReplaceText string, subjectReplaceTextEn string, attribute models.Attribute, remark string, orderNumber string, reason string) ([]models.Mail, error) {
	// 查询通知配置 是否需要邮件通知
	state, err := tools.MailCustomContentState(dispensing.EnvironmentID, title)
	if err != nil {
		return mails, errors.WithStack(err)
	}
	if !state {
		return mails, nil
	}

	var project models.Project
	projectFilter := bson.M{"_id": dispensing.ProjectID}
	err = tools.Database.Collection("project").FindOne(ctx, projectFilter).Decode(&project)
	envInfo, cohortInfo := database.GetEnvCohortInfo(project, dispensing.EnvironmentID, dispensing.CohortID)
	// JIRA3036 查询开放药物配置 未编号如果是开放药物不打码，是盲态药物 根据角色判断是否打码
	//IsOpenDrugMap, err := tools.IsBlindDrugMap(dispensing.EnvironmentID)
	if err != nil {
		return mails, errors.WithStack(err)
	}
	//  发药邮件通知
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": dispensing.SubjectID}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
		{{Key: "$project",
			Value: bson.M{
				"_id":            1,
				"customer_id":    1,
				"project_id":     1,
				"env_id":         1,
				"cohort_id":      1,
				"random_list_id": 1,
				"site_id":        "$project_site._id",
				"name":           models.ProjectSiteNameLookUpBsonLang("zh"),
				"nameEn":         models.ProjectSiteNameLookUpBsonLang("en"),
				"number":         "$project_site.number",
				"subject":        "$info.value",
				"status":         1,
				"group":          1,
				"par_group_name": 1,
				"sub_group_name": 1,
				"random_number":  1,
			},
		}},
	}
	var siteInfos []models.MailDispensingInfo
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, pipeline)
	if err != nil {
		return mails, errors.WithStack(err)
	}
	err = cursor.All(nil, &siteInfos)
	if err != nil {
		return mails, errors.WithStack(err)
	}
	siteInfo := siteInfos[0]

	if siteInfo.SubGroupName != "" {
		if siteInfo.RandomListID.IsZero() {
			var beSubject models.Subject
			err := tools.Database.Collection("subject").FindOne(nil, bson.M{"replace_subject_id": siteInfo.ID, "deleted": bson.M{"$ne": true}}).Decode(&beSubject)
			if err != nil {
				return mails, errors.WithStack(err)
			}
			siteInfo.RandomListID = beSubject.RandomListID
		}

		var randomList models.RandomList
		err := tools.Database.Collection("random_list").FindOne(nil, bson.M{"_id": siteInfo.RandomListID}).Decode(&randomList)
		if err != nil {
			return mails, errors.WithStack(err)
		}
		groupP, b2 := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
			return siteInfo.ParGroupName == item.ParName && siteInfo.SubGroupName == item.SubName
		})
		if b2 {
			siteInfo.SubGroupBlind = groupP.Blind
		}
	}

	strTimeZone, err := tools.GetSiteTimeZone(siteInfo.SiteID)
	if err != nil {
		return mails, errors.WithStack(err)
	}
	if strTimeZone == "" {
		zone, err := tools.GetTimeZone(siteInfo.ProjectID)
		if err != nil {
			return mails, err
		}
		//strTimeZone = fmt.Sprintf("UTC%+d", zone)
		strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)
	hours := time.Duration(intTimeZone)
	minutes := time.Duration((intTimeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	dispensingDate := time.Unix(int64(dispensingTime), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
	dispensingDate = dispensingDate + " (" + strTimeZone + ")"

	operationTimeStr := time.Unix(int64(operationTime), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
	operationTimeStr = operationTimeStr + " (" + strTimeZone + ")"

	userMail, err := tools.GetRoleUsersMailWithRole(siteInfo.ProjectID, siteInfo.EnvironmentID, "notice.subject.dispensing", siteInfo.SiteID)

	for _, userEmail := range userMail {
		var toUserMail []string
		medicineNumberStr := ""
		drugNumberStr := ""

		medicineNumber := make([]string, len(medicineNumberReq))
		copy(medicineNumber, medicineNumberReq)
		registerNumber := registerNumberReq
		replaceNumber := make([]string, len(replaceNumberReq))
		copy(replaceNumber, replaceNumberReq)

		medicineNumberStr = strings.Join(medicineNumber, " ")

		if title == "dispensing.retrieval-title" {
			retrievalNumber := make([]string, len(replaceNumberReq))
			copy(retrievalNumber, replaceNumberReq)
			drugNumberStr = strings.Join(retrievalNumber, " ")
		}
		dispensingType := 1

		toUserMail = append(toUserMail, userEmail.Email)
		data := map[string]interface{}{
			"projectNumber":  project.ProjectInfo.Number,
			"projectName":    project.ProjectInfo.Name,
			"envName":        envInfo.Name,
			"siteNumber":     siteInfo.Number,
			"siteName":       siteInfo.Name,
			"siteNameEn":     siteInfo.NameEn,
			"cohortName":     models.GetCohortReRandomName(cohortInfo),
			"subjectNumber":  siteInfo.Subject,
			"drugNumber":     medicineNumberStr,
			"visitName":      visitInfo.Name,
			"visitNameEn":    visitInfo.Name,
			"dispensingDate": operationTimeStr,
			"group":          siteInfo.Group,
			"parName":        siteInfo.ParGroupName,
			"subGroup":       siteInfo.SubGroupName,
			"random_number":  siteInfo.RandomNumber,
			"orderNumber":    orderNumber,
			"remark":         remark,
			"reason":         reason,
			"label":          subjectReplaceText,
			"labelEn":        subjectReplaceTextEn,
		}
		if dispensing.VisitSign {
			dispensingType = 2
			data["visitName"] = visitInfo.Name + "(" + locales.TrWithLang("zh", "export.dispensing.outVisit") + ")"
			data["visitNameEn"] = visitInfo.Name + "(" + locales.TrWithLang("en", "export.dispensing.outVisit") + ")"
			data["unscheduled"] = locales.TrWithLang("zh", "export.dispensing.outVisit")
			data["unscheduledEn"] = locales.TrWithLang("en", "export.dispensing.outVisit")
			if visitCycle.SetInfo.IsOpen {
				data["visitName"] = visitInfo.Name + "(" + visitCycle.SetInfo.NameZh + ")"
				data["visitNameEn"] = visitInfo.Name + "(" + visitCycle.SetInfo.NameEn + ")"
				data["unscheduled"] = visitCycle.SetInfo.NameZh
				data["unscheduledEn"] = visitCycle.SetInfo.NameEn

			}
		}
		if dispensing.Reissue == 1 {
			dispensingType = 3
			data["visitName"] = visitInfo.Name + "(" + locales.TrWithLang("zh", "export.dispensing.reissue") + ")"
			data["visitNameEn"] = visitInfo.Name + "(" + locales.TrWithLang("en", "export.dispensing.reissue") + ")"
		}
		if title == "dispensing.register-title" {
			dispensingType = 5
			data["dispensingDate"] = dispensingDate
			data["dispensingTime"] = operationTimeStr
			data["registerNumber"] = registerNumber
		}
		if len(replaceNumber) > 0 {
			dispensingType = 4
			replaceNumberStr := strings.Join(replaceNumber, " ")
			data["replaceNumber"] = replaceNumberStr
		}
		if title == "dispensing.retrieval-title" {
			dispensingType = 6
			data["dispensingDate"] = dispensingDate
			data["dispensingTime"] = operationTimeStr
			data["retrievalNumber"] = medicineNumberStr
			data["drugNumber"] = drugNumberStr
		}

		if data["subGroup"] != nil && data["subGroup"].(string) != "" {
			data["group"] = data["parName"]
		} else {
			data["group"] = siteInfo.Group
		}
		if userEmail.IsBlind && attribute.AttributeInfo.Blind && siteInfo.Group != "" {
			data["group"] = tools.BlindData
		}
		if userEmail.IsBlind && siteInfo.SubGroupBlind {
			data["subGroup"] = tools.BlindData
		}
		if title == "dispensing.not-attend-title" {
			dispensingType = 7
			data["dispensingTime"] = operationTimeStr
		}
		err = bodyContentKeysAppend(ctx, title, data, attribute, dispensing.EnvironmentID, &mails, toUserMail, dispensingType, visitCycle)
		if err != nil {
			return nil, errors.WithStack(err)
		}

	}
	return mails, nil
}
func bodyContentKeysAppend(ctx *gin.Context, title string, data map[string]interface{}, attribute models.Attribute, envOID primitive.ObjectID, mails *[]models.Mail, toUserMail []string, dispensingType int, visitCycleInfo models.VisitCycle) error {
	const (
		projectNumber int = iota + 1
		projectName
		env
		siteNumber
		siteName
		subjectNumber
		group
		subGroup
		number
		drugNumber
		orderNumber
		visitCycle
		dispensingDate
		replaceNumber
		beReplaceNumber
		replaceTime
		reason
		registerNumber
		retrievalNumber
		dispensingTime
		remark
	)

	bodyContentKeys := make([]models.ContentKey, 0)
	var err error
	{ // 通用部分
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: projectNumber,
			Key:   "project.number",
			Map:   bson.M{"projectNumber": data["projectNumber"]},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: projectName,
			Key:   "project.name",
			Map:   bson.M{"projectName": data["projectName"]},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: env,
			Key:   "project.env",
			Map:   bson.M{"envName": data["envName"]},
		})

		if data["cohortName"] != nil && data["cohortName"] != "" {
			bodyContentKeys = append(bodyContentKeys, models.ContentKey{
				Index: env,
				Key:   "project.cohort",
				Map:   bson.M{"cohortName": data["cohortName"]},
			})
		}
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: siteNumber,
			Key:   "project.site.number",
			Map:   bson.M{"siteNumber": data["siteNumber"]},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: siteName,
			Key:   "project.site.name",
			Map:   bson.M{"siteName": data["siteName"], "siteNameEn": data["siteNameEn"]},
		})

		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: subjectNumber,
			Key:   "dispensing.single.subject",
			Map:   bson.M{"label": data["label"], "labelEn": data["labelEn"], "subjectNumber": data["subjectNumber"]},
		})

		if data["orderNumber"] != nil && data["orderNumber"] != "" {

			bodyContentKeys = append(bodyContentKeys, models.ContentKey{
				Index: orderNumber,
				Key:   "dispensing.single.orderNumber",
				Map:   bson.M{"orderNumber": data["orderNumber"]},
			})
		}
		//随机前的发药，发放邮件中，随机号和组别字段应该隐藏
		if data["group"] != nil && data["group"] != "" {
			bodyContentKeys = append(bodyContentKeys, models.ContentKey{
				Index: group,
				Key:   "dispensing.group",
				Map:   bson.M{"group": data["group"]},
			})
			if data["subGroup"] != nil && data["subGroup"].(string) != "" {
				bodyContentKeys = append(bodyContentKeys, models.ContentKey{
					Index: subGroup,
					Key:   "dispensing.subGroup",
					Map:   bson.M{"subGroup": data["subGroup"]},
				})
			}
			if attribute.AttributeInfo.IsRandomNumber {
				bodyContentKeys = append(bodyContentKeys, models.ContentKey{
					Index: number,
					Key:   "dispensing.number",
					Map:   bson.M{"random_number": data["random_number"]},
				})
			}
		}
	}

	if dispensingType != 4 {
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: visitCycle,
			Key:   "dispensing.single.visitCycle",
			Map:   bson.M{"visitCycle": data["visitName"], "visitCycleEn": data["visitNameEn"]},
		})
		if dispensingType != 7 {
			bodyContentKeys = append(bodyContentKeys, models.ContentKey{
				Index: drugNumber,
				Key:   "dispensing.single.drugNumber",
				Map:   bson.M{"drugNumber": data["drugNumber"]},
			})
			bodyContentKeys = append(bodyContentKeys, models.ContentKey{
				Index: dispensingDate,
				Key:   "dispensing.single.dispensingDate",
				Map:   bson.M{"dispensingDate": data["dispensingDate"]},
			})
		}

	}

	bodyContentKeys = append(bodyContentKeys, models.ContentKey{
		Index: remark,
		Key:   "dispensing.single.remark",
		Map:   bson.M{"remark": data["remark"]},
	})

	if dispensingType == 2 { // 计划外
		key := "dispensing.single.unscheduled-dispensing-reason"
		if visitCycleInfo.SetInfo.IsOpen {
			key = "dispensing.single.unscheduled-dispensing-reason-customer"
		}
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: reason,
			Key:   key,
			Map: bson.M{"reason": data["reason"],
				"dispensingTypeZh": visitCycleInfo.SetInfo.NameZh,
				"dispensingTypeEn": visitCycleInfo.SetInfo.NameEn,
			},
		})
	}
	if dispensingType == 3 { // 补发
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: reason,
			Key:   "dispensing.single.re-dispensing-reason",
			Map:   bson.M{"reason": data["reason"]},
		})
	}
	if dispensingType == 4 { // 替换

		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: replaceNumber,
			Key:   "dispensing.single.replaceNumber",
			Map:   bson.M{"replaceNumber": data["replaceNumber"]},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: beReplaceNumber,
			Key:   "dispensing.single.beReplaceNumber",
			Map:   bson.M{"beReplaceNumber": data["drugNumber"]},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: replaceTime,
			Key:   "dispensing.single.replaceTime",
			Map:   bson.M{"replaceTime": data["dispensingDate"]},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: reason,
			Key:   "dispensing.single.replace-reason",
			Map:   bson.M{"reason": data["reason"]},
		})
	}
	if dispensingType == 5 { // 登记
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: registerNumber,
			Key:   "dispensing.single.registerNumber",
			Map:   bson.M{"registerNumber": data["registerNumber"]},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: dispensingTime,
			Key:   "dispensing.single.dispensingTime",
			Map:   bson.M{"dispensingTime": data["dispensingTime"]},
		})

	}
	if dispensingType == 6 { // 取回
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: retrievalNumber,
			Key:   "dispensing.single.retrievalNumber",
			Map:   bson.M{"retrievalNumber": data["retrievalNumber"]},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: dispensingTime,
			Key:   "dispensing.single.dispensingTime",
			Map:   bson.M{"dispensingTime": data["dispensingTime"]},
		})

	}
	if dispensingType == 7 {
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: dispensingTime,
			Key:   "dispensing.single.dispensingTime",
			Map:   bson.M{"dispensingTime": data["dispensingTime"]},
		})
	}

	var noticeConfig models.NoticeConfig
	err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	langList := make([]string, 0)
	if noticeConfig.Manual != 0 {
		if noticeConfig.Manual == 1 {
			langList = append(langList, "zh")
		} else if noticeConfig.Manual == 2 {
			langList = append(langList, "en")
		} else if noticeConfig.Manual == 3 {
			langList = append(langList, "zh")
			langList = append(langList, "en")
		}
	} else {
		langList = append(langList, ctx.GetHeader("Accept-Language"))
	}

	bodyContentKeys, err = DispensingRemoveKey(ctx, envOID, "notice.subject.dispensing", bodyContentKeys)
	if err != nil {
		return errors.WithStack(err)

	}
	err = slice.SortByField(bodyContentKeys, "Index")
	if err != nil {
		return errors.WithStack(err)
	}
	itemMail := models.Mail{
		ID:      primitive.NewObjectID(),
		Subject: title,
		SubjectData: map[string]interface{}{"projectNumber": data["projectNumber"],
			"projectName":   data["projectName"],
			"envName":       data["envName"],
			"unscheduled":   data["unscheduled"],
			"unscheduledEn": data["unscheduledEn"],
			"siteNumber":    data["siteNumber"],
			"siteName":      data["siteName"],
			"siteNameEn":    data["siteNameEn"]},
		Content:        "",
		ContentData:    nil,
		To:             toUserMail, // test测试
		Lang:           ctx.GetHeader("Accept-Language"),
		LangList:       langList,
		Status:         0,
		CreatedTime:    time.Duration(time.Now().Unix()),
		ExpectedTime:   time.Duration(time.Now().Unix()),
		SendTime:       time.Duration(time.Now().Unix()),
		BodyContentKey: bodyContentKeys,
	}
	*mails = append(*mails, itemMail)
	return nil
}

func DispensingRemoveKey(ctx *gin.Context, envOID primitive.ObjectID, noticeType string, bodyContentKeys []models.ContentKey) ([]models.ContentKey, error) {
	notice := models.NoticeConfig{}
	//放到ctx里面避免for循环内多次查询
	n, _ := ctx.Get("notice" + noticeType)
	if n != nil {
		notice = n.(models.NoticeConfig)
	} else {
		err := tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": noticeType}).Decode(&notice)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.WithStack(err)
		}
		ctx.Set("notice"+noticeType, notice)
	}

	dispensingInitKeys := []string{
		"group",
		"random_number",
		"projectName",
		"projectNumber",
		"siteNumber",
		"siteName",
	}

	dispensingInitKeyMap := map[string][]string{
		"group":         []string{"dispensing.group", "dispensing.subGroup"},
		"random_number": []string{"dispensing.number"},
		"projectName":   []string{"project.name"},
		"projectNumber": []string{"project.number"},
		"siteNumber":    []string{"project.site.number"},
		"siteName":      []string{"project.site.name"},
	}

	removeKey := slice.Difference(dispensingInitKeys, notice.FieldsConfig)
	for _, key := range removeKey {
		for _, value := range dispensingInitKeyMap[key] {
			bodyContentKeys = slice.Filter(bodyContentKeys, func(index int, item models.ContentKey) bool {
				return item.Key != value
			})
		}

	}

	return bodyContentKeys, nil
}

func getBatch(count int64, medicines []models.Medicine, segmentLen int64) []models.Medicine {
	batchMedicine := []models.Medicine{}
	floor := func(item models.Medicine) string {
		return item.BatchNumber
	}
	result := slice.GroupWith(medicines, floor)

	keys := []string{}

	for key := range result {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	for _, key := range keys {

		if count == 0 {
			break
		}
		// 打乱数组元素顺序
		randomMedicine := result[key]
		medicineLen := len(result[key])
		if segmentLen != 0 && medicineLen > int(segmentLen) { //分段取号 && 药物总数 > 长度   取区组长度的药随机
			randomMedicine = result[key][:segmentLen]
			if segmentLen < count { // 分段小于发药数量
				realLen := int(segmentLen + count) //真是分段数量
				if medicineLen > realLen {
					randomMedicine = result[key][:realLen]
				} else {
					randomMedicine = result[key]
				}
			}
		}

		rand.Seed(time.Now().UnixNano())
		rand.Shuffle(len(randomMedicine), func(i, j int) {
			randomMedicine[i], randomMedicine[j] = randomMedicine[j], randomMedicine[i]
		})

		if len(randomMedicine) >= int(count) { //足够药物
			batchMedicine = append(batchMedicine, randomMedicine[:count]...)
			break
		} else {
			batchCount := len(randomMedicine)
			batchMedicine = append(batchMedicine, randomMedicine[:batchCount]...)
			count = count - int64(batchCount)
		}

	}
	return batchMedicine
}

func getPageMedicine(sctx mongo.SessionContext, drugMatch bson.M, count int64, sortRandom bool) ([]models.Medicine, error) {
	var medicines []models.Medicine
	matchIDs := bson.A{}

	var drugData []models.GroupMedicineUnwind
	//var drugDatas []map[string]interface{}
	drugMatch["package_number"] = bson.M{"$nin": bson.A{nil, ""}}
	medicinePipeline := mongo.Pipeline{
		{{Key: "$match", Value: drugMatch}},
		models.MedicineProject,
		{{Key: "$group", Value: bson.M{
			"_id":            bson.M{"name": "$name", "package_number": "$package_number", "expiration_date": "$expiration_date"},
			"availableCount": bson.M{"$sum": 1},
			"medicines":      bson.M{"$push": bson.M{"_id": "$_id", "serial_number": "$serial_number"}},
		}}},
		{{"$unwind", "$medicines"}},
		{{Key: "$sort", Value: bson.D{{"availableCount", 1}, {"_id.expiration_date", 1}, {"medicines.serial_number", 1}}}},
		{{"$project", bson.M{
			"name":            "$_id.name",
			"package_number":  "$_id.package_number",
			"expiration_date": "$_id.expiration_date",
			"availableCount":  1,
			"medicines":       1,
		}}},
		{{Key: "$limit", Value: count}},
	}
	cursor, err := tools.Database.Collection("medicine").Aggregate(sctx, medicinePipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &drugData)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, item := range drugData {
		matchIDs = append(matchIDs, item.Medicines.ID)
	}

	cursor, err = tools.Database.Collection("medicine").Find(sctx, bson.M{"_id": bson.M{"$in": matchIDs}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &medicines)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return medicines, nil
}

func randomPageMedicine(ctx *gin.Context, sctx mongo.SessionContext, drugMatch bson.M, subjectOID primitive.ObjectID, count int) ([]models.Medicine, error) {
	var pageMedicine []primitive.ObjectID
	var drugs []models.GroupMedicine
	// 使用当前时间作为随机数生成器的种子
	rand.Seed(time.Now().UnixNano())

	// 定义一个长度为count的浮点数切片来存储随机数
	randomNumbers := make([]float64, count)
	for i := range randomNumbers {
		randomNumbers[i] = rand.Float64()
	}
	drugMatchTmp := bson.M{}
	for key, value := range drugMatch {
		drugMatchTmp[key] = value
	}
	drugMatchTmp["package_number"] = bson.M{"$nin": bson.A{nil, ""}}
	name := drugMatchTmp["name"].(string)
	delete(drugMatchTmp, "name")
	delete(drugMatchTmp, "spec")
	delete(drugMatchTmp, "status")
	//delete(drugMatch, "$or")
	cursor, err := tools.Database.Collection("medicine").Aggregate(sctx, mongo.Pipeline{
		{{Key: "$match", Value: drugMatch}},
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{
				"package_number":        "$package_number",
				"package_serial_number": "$package_serial_number",
				"expiration_date":       "$expiration_date",
			},
			"availableCount": bson.M{"$sum": 1},
			"medicines": bson.M{"$push": bson.M{
				"_id":           "$_id",
				"serial_number": "$serial_number",
				"name":          "$name",
				"status":        "$status",
			}},
		}}},
		// 匹配获取有对应可用药物的包装
		{{"$match", bson.M{
			"$or": []bson.M{
				{
					"medicines": bson.M{
						"$elemMatch": bson.M{
							"name":   name,
							"status": 1,
						},
					},
				},
				//{
				//	"medicines": bson.M{
				//		"$elemMatch": bson.M{
				//			"name":       name,
				//			"status":     14,
				//			"subject_id": subjectOID,
				//		},
				//	},
				//},
			},
		}}},
		{{"$sort", bson.D{
			{"availableCount", 1},
			{"_id.expiration_date", 1},
			{"_id.package_serial_number", 1},
		}}},
		{{"$project", bson.M{
			"name":            "$_id.name",
			"package_number":  "$_id.package_number",
			"expiration_date": "$_id.expiration_date",
			"availableCount":  1,
			"medicines":       1,
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &drugs)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for _, number := range randomNumbers {
		if len(drugs) == 0 {
			return nil, nil
		}
		if number > 0.9 {
			OID, err := getPageMedicineNumber(ctx, &drugs, name, subjectOID, true)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			pageMedicine = append(pageMedicine, OID)
		} else {
			OID, err := getPageMedicineNumber(ctx, &drugs, name, subjectOID, false)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			pageMedicine = append(pageMedicine, OID)
		}
	}
	var medicines []models.Medicine
	cursor, err = tools.Database.Collection("medicine").Find(sctx, bson.M{"_id": bson.M{"$in": pageMedicine}})
	if err != nil {
		return nil, err
	}
	err = cursor.All(nil, &medicines)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return medicines, nil
}

func getPageMedicineNumber(ctx *gin.Context, drugsP *[]models.GroupMedicine, name string, subjectOID primitive.ObjectID, isNext bool) (primitive.ObjectID, error) {
	drugs := *drugsP
	if len(drugs) == 0 {
		return primitive.NilObjectID, nil
	}

	index := 0
	// 可用数量
	useMedicine := slice.Filter(drugs[0].Medicines, func(index int, item models.PushMedicine) bool {
		return item.Status == 1
	})
	floatUseMedicine, _ := convertor.ToFloat(len(useMedicine))
	floatMedicine := math.Ceil(float64(len(drugs[0].Medicines)) * 0.5)
	if isNext &&
		floatUseMedicine <= floatMedicine &&
		len(drugs) > 1 { // 随机大于百分9-0 && 当前包可用大于50 && 有下一个包 则取下一个包装的药物
		index = 1
	}

	// 可选药物名称
	medicineNameArr := slice.Filter(drugs[index].Medicines, func(index int, item models.PushMedicine) bool {
		return item.Status == 1 && item.Name == name
	})

	// 随机取药
	selectIndex := rand.Intn(len(medicineNameArr))
	OID := medicineNameArr[selectIndex].ID

	// 如果当前包装所有数量取完 则拿掉这个包装  如果未用完 则拿掉这个包装的其中一个药物
	if len(medicineNameArr) == 1 {
		*drugsP = slice.DeleteAt(*drugsP, index)
	} else {
		for i, pushMedicine := range drugs[index].Medicines {
			if OID == pushMedicine.ID {
				drugs[index].Medicines[i].Status = 5
			}
		}
	}
	return OID, nil
}

// BlindGetDrug BlindGetDrug..  盲法项目, 按过期批次 随机优先获取过期的研究产品
func BlindGetDrug(ctx *gin.Context, sctx mongo.SessionContext, drugMatch bson.M, subjectOID primitive.ObjectID, number64 int64, attribute models.Attribute, segmentLen int64) ([]models.Medicine, error) {
	var AllMedicines []models.Medicine
	segment := attribute.AttributeInfo.Segment
	segmentType := bson.D{{Key: "$sort", Value: bson.D{{"serial_number", 1}}}}
	if attribute.AttributeInfo.SegmentType == 1 {
		segmentType = bson.D{{Key: "$sort", Value: bson.D{{"number", 1}}}}
	}
	// 优先获取 已绑定受试者的研究产品
	var boundMedicines []models.Medicine
	drugMatch["$or"] = bson.A{
		bson.M{"subject_id": subjectOID},
	}
	cursor, err := tools.Database.Collection("medicine").Find(sctx, drugMatch, &options.FindOptions{
		Sort:  bson.D{{"subject_id", -1}, {"expiration_date", 1}, {"batch_number", 1}, {"serial_number", 1}, {"number", 1}}, // 优先获取受试者绑定的研究产品  subject_id
		Limit: &number64,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &boundMedicines)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(boundMedicines) > 0 {
		AllMedicines = append(AllMedicines, boundMedicines...)
	}
	count := number64 - int64(len(boundMedicines))

	// 按过期日期 随机获取研究产品号， 直到 数量等于 count
	var groups []map[string]interface{}
	orSubject := bson.A{
		bson.M{"subject_id": bson.M{"$exists": 0}},
		bson.M{"subject_id": primitive.NilObjectID},
	}
	drugMatch["$or"] = orSubject
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: drugMatch}},
		{{Key: "$group", Value: bson.M{"_id": "$expiration_date"}}},
		{{Key: "$sort", Value: bson.D{{Key: "_id", Value: 1}}}},
	}
	cursor, err = tools.Database.Collection("medicine").Aggregate(sctx, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &groups)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	expirationDate := drugMatch["expiration_date"]

	if count > 0 {
		if segment {
			for _, group := range groups { //有效期分组
				drugMatch["expiration_date"] = group["_id"]
				var medicines []models.Medicine
				pipeline := mongo.Pipeline{
					{{Key: "$match", Value: drugMatch}},
					models.MedicineProject,
					segmentType,
				}
				cursor, err := tools.Database.Collection("medicine").Aggregate(nil, pipeline)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(nil, &medicines)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				batchMedicine := getBatch(count, medicines, segmentLen)
				count = count - int64(len(batchMedicine))
				AllMedicines = append(AllMedicines, batchMedicine...)

				medicinesLen := int64(len(medicines))
				if medicinesLen > count {
					if medicinesLen < segmentLen {
						segmentLen = medicinesLen
					}
					limitMedicines := medicines[:segmentLen]
					rand.Shuffle(len(limitMedicines), func(i, j int) {
						limitMedicines[i], limitMedicines[j] = limitMedicines[j], limitMedicines[i]
					})
					appendMedicines := limitMedicines[:count]
					AllMedicines = append(AllMedicines, appendMedicines...)
					count = count - int64(len(appendMedicines))
				} else {
					AllMedicines = append(AllMedicines, medicines...)
					count = count - int64(len(medicines))
				}
				if count == 0 { // 获取指定数量研究产品 中止循环
					break
				}
			}
		} else {
			for _, group := range groups { //有效期分组
				drugMatch["expiration_date"] = group["_id"]
				if drugMatch["storehouse_id"] != nil { // DTP仓库发药分两次查询
					drugMatch["package_number"] = bson.M{"$in": bson.A{nil, ""}}
				}
				pipeline := mongo.Pipeline{
					{{Key: "$match", Value: drugMatch}},
				}
				cursor, err := tools.Database.Collection("medicine").Aggregate(sctx, pipeline)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				var medicines []models.Medicine
				err = cursor.All(nil, &medicines)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				// 有效期分组后   按批次号分组
				batchMedicines := getBatch(count, medicines, 0)
				count = count - int64(len(batchMedicines))
				AllMedicines = append(AllMedicines, batchMedicines...)

				if count == 0 { // 获取指定数量研究产品 中止循环
					break
				}
			}

			// 仓库DTP 先取单品 再取拆包
			if drugMatch["storehouse_id"] != nil {
				drugMatch["expiration_date"] = expirationDate
				if count > 0 {
					medicines, err := randomPageMedicine(ctx, sctx, drugMatch, subjectOID, int(count))
					if err != nil {
						return nil, errors.WithStack(err)
					}
					AllMedicines = append(AllMedicines, medicines...)
				}
			}

		}
	}

	return AllMedicines, nil
}

func getExpireDateKey(subjectOID primitive.ObjectID) (map[string]interface{}, map[string]int32, error) {
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	expireDateKey := make(map[string]interface{})
	dispensingAlarmNumber := make(map[string]int32)

	// 查询研究产品配置的药物配置
	var drugPackageConfigure models.DrugPackageConfigure
	err = tools.Database.Collection("drug_package_configure").FindOne(nil, bson.M{"env_id": subject.EnvironmentID}).Decode(&drugPackageConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, nil, errors.WithStack(err)

	}
	for _, config := range drugPackageConfigure.UnProvideDateConfig {
		hours, _ := time.ParseDuration(fmt.Sprintf("%sh", strconv.Itoa(config.Number*24)))
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return nil, nil, err
		}
		ho := time.Duration(zone)
		minutes := time.Duration((zone - float64(ho)) * 60)
		duration := ho*time.Hour + minutes*time.Minute
		times := time.Now().UTC().Add(duration).Add(hours).Format("2006-01-02")
		expireDateKey[config.Name] = times
	}

	// 查询中的供应计划
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": subject.ProjectSiteID}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "supply_plan_medicine",
			"localField":   "supply_plan_id",
			"foreignField": "supply_plan_id",
			"as":           "supply_plan_medicine",
		}}},
		{{Key: "$unwind", Value: "$supply_plan_medicine"}},
		{{Key: "$project", Value: bson.M{
			"_id":              0,
			"medicine_name":    "$supply_plan_medicine.info.medicine_name",
			"un_provide_date":  "$supply_plan_medicine.info.un_provide_date",
			"dispensing_alarm": "$supply_plan_medicine.info.dispensing_alarm",
		}}},
	}
	supplyPlanCursor, err := tools.Database.Collection("project_site").Aggregate(nil, pipeline)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	var supplyPlan []map[string]interface{}
	err = supplyPlanCursor.All(nil, &supplyPlan)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	for _, item := range supplyPlan {
		hours, _ := time.ParseDuration(fmt.Sprintf("%sh", strconv.Itoa(int(item["un_provide_date"].(int32))*24)))
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return nil, nil, err
		}
		hour := time.Duration(zone)
		minute := time.Duration((zone - float64(hour)) * 60)
		duration := hour*time.Hour + minute*time.Minute
		times := time.Now().UTC().Add(duration).Add(hours).Format("2006-01-02")
		expireDateKey[item["medicine_name"].(string)] = times
		if item["dispensing_alarm"] != nil {
			dispensingAlarmNumber[item["medicine_name"].(string)] = item["dispensing_alarm"].(int32)
		}
	}
	return expireDateKey, dispensingAlarmNumber, nil
}

func checkVisitOrder(sctx mongo.SessionContext, subjectOID primitive.ObjectID, visitOID primitive.ObjectID, visitSign bool) (bool, error) {

	/*
		按序号顺序查询发药记录
		if visitSign== True  (访视外发药)
			当前访视 == 发药记录最后一条已发药访视
		if visitSign== false  (访视发药)
			当前访视 == 可发药的访视的最前一条发药访视
	*/
	var dispensings []models.Dispensing
	cursor, err := tools.Database.Collection("dispensing").Aggregate(sctx, mongo.Pipeline{
		{{"$match", bson.M{"subject_id": subjectOID}}},
		{{"$sort", bson.M{"serial_number": 1}}},
	})
	if err != nil {
		return false, errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensings)
	if err != nil {
		return false, errors.WithStack(err)
	}

	visitDispensingOID := primitive.NilObjectID
	if visitSign {
		for _, dispensing := range dispensings {
			if dispensing.Status == 2 || dispensing.Status == 3 {
				visitDispensingOID = dispensing.VisitInfo.VisitCycleInfoID
			}
		}
	} else {
		for _, dispensing := range dispensings {
			//
			if !dispensing.VisitInfo.Dispensing {
				continue
			}
			if dispensing.Status == 1 {
				visitDispensingOID = dispensing.VisitInfo.VisitCycleInfoID
				break
			}
		}
	}
	if visitDispensingOID != visitOID {
		return false, nil
	}
	return true, nil
}

func checkConfirmOrder(sctx mongo.SessionContext, subjectOID primitive.ObjectID, status int) (bool, error) {

	/*
		存在一个待配送 则无法发药
	*/
	var dispensings []map[string]interface{}
	cursor, err := tools.Database.Collection("dispensing").Aggregate(sctx, mongo.Pipeline{
		{{"$match", bson.M{"subject_id": subjectOID}}},
		{{"$lookup", bson.M{
			"from":         "medicine_order",
			"localField":   "subject_id",
			"foreignField": "subject_id",
			"as":           "medicine_order",
		}}},
		{{"$match", bson.M{"medicine_order.status": status}}},
	})
	if err != nil {
		return false, errors.WithStack(err)
	}

	err = cursor.All(nil, &dispensings)
	if err != nil {
		return false, errors.WithStack(err)
	}
	if len(dispensings) > 0 {
		return false, nil
	}
	return true, nil
}

// checkSubjectStatus 判断当前受试者状态是否允许发药
func checkSubjectStatus(ctx *gin.Context, sctx mongo.SessionContext, subjectOID primitive.ObjectID) (bool, error) {

	/*

	 */

	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return false, errors.WithStack(err)
	}
	if subject.Status == 4 || subject.Status == 5 {
		return false, nil
	}

	var attribute models.Attribute
	filter := bson.M{"env_id": subject.EnvironmentID}
	if subject.CohortID != primitive.NilObjectID {
		filter["cohort_id"] = subject.CohortID
	}
	err = tools.Database.Collection("attribute").FindOne(sctx, filter).Decode(&attribute)
	if err != nil {
		return false, errors.WithStack(err)
	}

	//
	if attribute.AttributeInfo.UnBlindingRestrictions && subject.Status == 6 {
		return false, tools.BuildServerError(ctx, "subject_status_no_dispensing")
	}

	if attribute.AttributeInfo.PvUnBlindingRestrictions && subject.PvUnblindingStatus == 1 {
		return false, tools.BuildServerError(ctx, "subject_status_no_dispensing")

	}

	return true, nil
}

func setDispensingMedicine(
	sctx mongo.SessionContext,
	ctx *gin.Context,
	orderOID primitive.ObjectID,
	drugData []models.UpdateDrugConfigure,
	expireDateKey map[string]interface{},
	subject models.Subject,
	otherDispensingMedicine *[]models.OtherDispensingMedicine,
	medicineNumber *[]string,
	dispensingMedicine *[]models.DispensingMedicine,
	attribute models.Attribute,
	labels *[]string,
	StoreHouseOID primitive.ObjectID, visitSign bool, now time.Duration, dispensingAlarmNumber map[string]int32, allDrugMap map[string]bool) error {
	// 多个标签进行发药
	types := 1
	if visitSign {
		types = 5
	}

	for _, item := range drugData {
		*labels = append(*labels, item.Configures.Label)
		for _, values := range item.Configures.Values {
			number := int(values.DispensingNumber)
			zone, err := tools.GetTimeZone(subject.ProjectID)
			if err != nil {
				return errors.WithStack(err)
			}
			hour := time.Duration(zone)
			minute := time.Duration((zone - float64(hour)) * 60)
			duration := hour*time.Hour + minute*time.Minute
			date := time.Now().UTC().Add(duration).Format("2006-01-02")
			if expireDateKey[values.DrugName] != nil {
				date = expireDateKey[values.DrugName].(string)
			}
			//  改成查具体仓库
			drugMatch := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "info.name": values.DrugName,
				"info.expire_date": bson.M{"$gt": date},
			}
			update := bson.M{}
			count, _ := tools.Database.Collection("medicine_other_institute").CountDocuments(sctx, drugMatch)
			if count > 0 {
				var medicineOthers []models.MedicineOtherInstitute
				//查具体仓库
				drugMatch = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "storehouse_id": StoreHouseOID,
					"env_id": subject.EnvironmentID, "info.name": values.DrugName,
					"info.expire_date": bson.M{"$gt": date}}
				opts := &options.FindOptions{
					Sort: bson.D{{"info.expire_date", 1}},
				}
				// 按过期时间，获取不同批次的非编号药物。
				cursor, err := tools.Database.Collection("medicine_other_institute").Find(sctx, drugMatch, opts)
				if err != nil {
					return errors.WithStack(err)
				}

				err = cursor.All(nil, &medicineOthers)
				if err != nil {
					return errors.WithStack(err)
				}

				// TODO 未编号药 发放警戒
				err = checkDispensingOtherAlarm(ctx, medicineOthers, count, dispensingAlarmNumber[values.DrugName])
				if err != nil {
					return err
				}

				var medicineOther models.MedicineOtherInstitute
				useCount := 0
				needCount := number
				//按批次 取药物 当前批次不够则取完当前批次 剩余的取下一个批次的药物 直到取到足量的药物
				for _, institute := range medicineOthers {
					if needCount == 0 {
						// 按批次 拿到足够的药物 退出循环
						break
					}
					if institute.Info.Count < needCount { // 一个批次不足 取药   减去这个批次剩下的药物
						if institute.Info.Count != 0 {
							update = bson.M{
								"$set": bson.M{"edit": true},

								"$inc": bson.M{
									"info.count":       institute.Info.Count * -1,
									"info.apply_count": institute.Info.Count * 1,
								},
							}
							err := tools.Database.Collection("medicine_other_institute").FindOneAndUpdate(sctx, bson.M{"_id": institute.ID}, update).Decode(&medicineOther)
							if err != nil {
								return errors.WithStack(err)
							}
							var batchNumber []models.BatchCount

							batchNumber = append(batchNumber, models.BatchCount{Batch: institute.Info.Batch, Count: institute.Info.Count})
							useCount += institute.Info.Count
							needCount -= institute.Info.Count
							*otherDispensingMedicine = append(*otherDispensingMedicine, models.OtherDispensingMedicine{
								ID:              primitive.NewObjectID(),
								MedicineOtherID: institute.ID,
								Name:            institute.Info.Name,
								Count:           institute.Info.Count,
								Batch:           institute.Info.Batch,
								ExpireDate:      institute.Info.ExpireDate,
								Time:            now,
								Type:            1,
								BatchCount:      batchNumber,
							})

							// 插入未编号表新数据或者更新 申请数量
							if count, _ = tools.Database.Collection("medicine_other_institute").CountDocuments(sctx, bson.M{
								"subject_id":       subject.ID,
								"info.name":        institute.Info.Name,
								"info.batch":       institute.Info.Batch,
								"info.expire_date": institute.Info.ExpireDate,
							}); count == 0 {
								tools.Database.Collection("medicine_other_institute").InsertOne(sctx, models.MedicineOtherInstitute{
									ID:            primitive.NewObjectID(),
									ProjectID:     subject.ProjectID,
									EnvironmentID: subject.EnvironmentID,
									CustomerID:    subject.CustomerID,
									InstituteID:   subject.ProjectSiteID,
									SubjectID:     subject.ID,
									Info: models.MedicineOtherInfo{
										ID:         institute.ID,
										Name:       institute.Info.Name,
										Batch:      institute.Info.Batch,
										ExpireDate: institute.Info.ExpireDate,
										ApplyCount: institute.Info.Count,
									},
								})
							} else {
								_, err := tools.Database.Collection("medicine_other_institute").UpdateOne(sctx, bson.M{
									"subject_id":       subject.ID,
									"info.name":        institute.Info.Name,
									"info.batch":       institute.Info.Batch,
									"info.expire_date": institute.Info.ExpireDate,
								}, bson.M{
									"$set": bson.M{"edit": true},
									"$inc": bson.M{
										"info.apply_count": institute.Info.Count * 1,
									},
								})
								if err != nil {
									return errors.WithStack(err)
								}
							}
						}
					} else { // 当前批次数量 足够取药  -所需药物数量
						update = bson.M{
							"$set": bson.M{"edit": true},
							"$inc": bson.M{
								"info.count":       needCount * -1,
								"info.apply_count": needCount * 1,
							},
						}
						err := tools.Database.Collection("medicine_other_institute").FindOneAndUpdate(sctx, bson.M{"_id": institute.ID}, update).Decode(&medicineOther)
						if err != nil {
							return errors.WithStack(err)
						}
						var batchNumber []models.BatchCount
						batchNumber = append(batchNumber, models.BatchCount{Batch: institute.Info.Batch, Count: needCount})
						*otherDispensingMedicine = append(*otherDispensingMedicine, models.OtherDispensingMedicine{
							ID:              primitive.NewObjectID(),
							MedicineOtherID: institute.ID,
							Name:            institute.Info.Name,
							Count:           needCount,
							Batch:           institute.Info.Batch,
							ExpireDate:      institute.Info.ExpireDate,
							Time:            now,
							Type:            types,
							BatchCount:      batchNumber,
						})

						// 插入未编号表新数据或者更新 申请数量
						if count, _ = tools.Database.Collection("medicine_other_institute").CountDocuments(sctx, bson.M{
							"subject_id":       subject.ID,
							"info.name":        institute.Info.Name,
							"info.batch":       institute.Info.Batch,
							"info.expire_date": institute.Info.ExpireDate,
						}); count == 0 {
							tools.Database.Collection("medicine_other_institute").InsertOne(sctx, models.MedicineOtherInstitute{
								ID:            primitive.NewObjectID(),
								ProjectID:     subject.ProjectID,
								EnvironmentID: subject.EnvironmentID,
								CustomerID:    subject.CustomerID,
								InstituteID:   subject.ProjectSiteID,
								SubjectID:     subject.ID,
								Info: models.MedicineOtherInfo{
									ID:         institute.ID,
									Name:       institute.Info.Name,
									Batch:      institute.Info.Batch,
									ExpireDate: institute.Info.ExpireDate,
									ApplyCount: needCount,
								},
							})
						} else {
							tools.Database.Collection("medicine_other_institute").UpdateOne(sctx, bson.M{
								"subject_id":       subject.ID,
								"info.name":        institute.Info.Name,
								"info.batch":       institute.Info.Batch,
								"info.expire_date": institute.Info.ExpireDate,
							},
								bson.M{
									"$set": bson.M{"edit": true},
									"$inc": bson.M{
										"info.apply_count": needCount * 1,
									},
								})
						}
						useCount += needCount
						needCount -= needCount

					}

				}
				if useCount != number {
					return tools.BuildServerError(ctx, "subject_medicine_count_store")
				}
			} else {
				// 研究产品名称在未编号研究产品中查找不到
				drugMatch = bson.M{
					"customer_id":     subject.CustomerID,
					"project_id":      subject.ProjectID,
					"env_id":          subject.EnvironmentID,
					"status":          bson.M{"$in": bson.A{1, 14}},
					"name":            values.DrugName,
					"storehouse_id":   StoreHouseOID,
					"expiration_date": bson.M{"$gt": date},
					"spec":            values.DrugSpec,
				}
				updateM := bson.M{
					"$set": bson.M{
						"status":     13,
						"order_id":   orderOID,
						"subject_id": subject.ID,
					},
				}

				// 盲法项目 随机获取研究产品号，
				var medicines []models.Medicine

				number64 := int64(values.DispensingNumber)

				//  DTP 发药警戒校验
				err = checkDispensingAlarm(ctx, sctx, values.DrugName, drugMatch, number64, dispensingAlarmNumber)
				if err != nil {
					return err
				}

				if !allDrugMap[values.DrugName] {
					// 非盲法项目， 按日期排序 发药
					opts := &options.FindOptions{
						Sort:  bson.D{{"subject_id", -1}, {"expiration_date", 1}, {"batch_number", 1}, {"serial_number", 1}, {"number", 1}}, // 优先获取受试者绑定的研究产品  subject_id
						Limit: &number64,
					}
					cursor, err := tools.Database.Collection("medicine").Find(sctx, drugMatch, opts)
					if err != nil {
						return errors.WithStack(err)
					}
					err = cursor.All(nil, &medicines)
					if err != nil {
						return errors.WithStack(err)
					}
				} else {
					segmentLen := int64(0)
					randomMatch := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
					if subject.CohortID != primitive.NilObjectID {
						randomMatch = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
					}
					// RandomDesign
					var randomDesign models.RandomDesign
					err := tools.Database.Collection("random_design").FindOne(nil, randomMatch).Decode(&randomDesign)
					if err != nil {
						return errors.WithStack(err)
					}
					for _, group := range randomDesign.Info.Groups {
						if group.SubGroup != nil && len(group.SubGroup) > 0 {
							for _, subGroup := range group.SubGroup {
								if subject.Group == group.Name+" "+subGroup.Name {
									segmentLen = int64(subGroup.SegmentLength)
									break
								}
							}
						} else {
							if group.Name == subject.Group {
								segmentLen = int64(group.SegmentLength)
								break
							}
						}
					}
					// 盲法项目 按照日期大小  随机获取研究产品号
					medicines, err = BlindGetDrug(ctx, sctx, drugMatch, subject.ID, number64, attribute, segmentLen)
					if err != nil {
						return errors.WithStack(err)
					}
				}

				if len(medicines) != number {
					return tools.BuildServerError(ctx, "subject_medicine_count_store")
				}
				// 修改编号研究产品状态
				medicineMatch := bson.A{}
				for _, medicine := range medicines {
					medicineMatch = append(medicineMatch, medicine.ID)
					*medicineNumber = append(*medicineNumber, medicine.Number)
					*dispensingMedicine = append(*dispensingMedicine, models.DispensingMedicine{
						MedicineID:     medicine.ID,
						Name:           medicine.Name,
						Number:         medicine.Number,
						ShortCode:      medicine.ShortCode,
						ExpirationDate: medicine.ExpirationDate,
						BatchNumber:    medicine.BatchNumber,
						PackageNumber:  medicine.PackageNumber,
						Time:           now,
						Type:           types,
					})
				}
				drugMatch = bson.M{"_id": bson.M{"$in": medicineMatch}}
				if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, drugMatch, updateM); err != nil {
					return errors.WithStack(err)
				}
			}

		}

	}

	return nil
}

func setHistory(ctx *gin.Context, dispensingOID primitive.ObjectID, subject models.Subject, attribute models.Attribute, otherDispensingMedicine []models.OtherDispensingMedicine, medicineNumber []string, room string, reason string, dispensingMedicine []models.DispensingMedicine, remark string, now time.Duration) {
	u, _ := ctx.Get("user")
	user := u.(models.User)
	userName := user.Name

	subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

	key := "history.dispensing.dtp-dispensing"
	historyData := map[string]interface{}{"label": subjectReplaceText, "subject": subject.Info[0].Value, "medicine": medicineNumber}
	if reason != "" {
		key = "history.dispensing.dtp-dispensingVisit"
		historyData["reason"] = reason
	}
	// 既有药物编号，未编号药物 history
	var otherMedicine []string
	for _, item := range otherDispensingMedicine {
		otherMedicine = append(otherMedicine, fmt.Sprintf("%s/%d/%s/%s", item.Name, item.Count, item.Batch, item.ExpireDate))
	}

	if len(otherDispensingMedicine) > 0 {
		key = "history.dispensing.dtp-dispensing-other"
		historyData = map[string]interface{}{"label": subjectReplaceText, "subject": subject.Info[0].Value, "medicine": otherMedicine}
		if reason != "" {
			key = "history.dispensing.dtp-dispensingVisit-other"
			historyData = map[string]interface{}{"label": subjectReplaceText, "subject": subject.Info[0].Value, "medicine": otherMedicine, "reason": reason}
		}
	}

	if len(medicineNumber) > 0 && len(otherDispensingMedicine) > 0 {
		key = "history.dispensing.dtp-dispensing-with-other"
		historyData = map[string]interface{}{"label": subjectReplaceText, "subject": subject.Info[0].Value, "medicine": medicineNumber, "other_medicine": otherMedicine}
		if reason != "" {
			key = "history.dispensing.dtp-dispensing-with-other-reason"
			historyData = map[string]interface{}{"label": subjectReplaceText, "subject": subject.Info[0].Value, "medicine": medicineNumber, "other_medicine": otherMedicine, "reason": reason, "remark": remark}
		}
	}
	historyData["remark"] = remark
	if room != "" {
		historyData["room"] = room
	}

	var histories []models.History
	history := models.History{
		Key:  key,
		OID:  dispensingOID,
		Data: historyData,
		Time: now,
		UID:  user.ID,
		User: userName,
	}
	histories = append(histories, history)

	for _, medicine := range dispensingMedicine {
		histories = append(histories, models.History{
			Key:  "history.medicine.apply",
			OID:  medicine.MedicineID,
			Time: now,
			UID:  user.ID,
			User: userName,
		})
	}

	ctx.Set("HISTORY", histories)
}

func generateOrder(sctx mongo.SessionContext, ctx *gin.Context, orderOID primitive.ObjectID, dispensing models.Dispensing, storeOID primitive.ObjectID, projectSiteOID primitive.ObjectID, remark string) (string, error) {
	u, _ := ctx.Get("user")
	user := u.(models.User)

	// 获取订单号
	orderNumber, err := getOrderNumber(sctx, dispensing.ProjectID)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 生成药物订单

	var operHistories []models.History
	operHistory := models.History{
		Key:  "history.order.create",
		Data: bson.M{"orderNumber": orderNumber},
		OID:  orderOID,
		Time: time.Duration(time.Now().Unix()),
		UID:  user.ID,
		User: user.Name,
	}
	operHistories = append(operHistories, operHistory)
	ctx.Set("HISTORY", operHistories)
	medicines := []primitive.ObjectID{}
	otherMedicines := []models.OtherMedicineCount{}
	for _, medicine := range dispensing.DispensingMedicines {
		medicines = append(medicines, medicine.MedicineID)
	}
	for _, medicine := range dispensing.OtherDispensingMedicines {
		otherMedicines = append(otherMedicines, models.OtherMedicineCount{
			ID:           medicine.MedicineOtherID,
			Name:         medicine.Name,
			UseCount:     medicine.Count,
			ReceiveCount: 0,
			ExpireDate:   medicine.ExpireDate,
			Batch:        medicine.Batch,
		})
	}

	order := models.MedicineOrder{
		ID:               orderOID,
		CustomerID:       dispensing.CustomerID,
		ProjectID:        dispensing.ProjectID,
		EnvironmentID:    dispensing.EnvironmentID,
		SendID:           storeOID,
		ReceiveID:        projectSiteOID,
		Status:           7, //已申请
		SortIndex:        15,
		Mode:             1,
		Medicines:        medicines,
		MedicinesHistory: []models.MedicinesHistory{},
		OtherMedicines:   otherMedicines,
		OrderNumber:      orderNumber,
		Type:             5,
		Meta: models.Meta{
			CreatedBy: user.ID,
			CreatedAt: time.Duration(time.Now().Unix()),
		},
		SubjectID:    dispensing.SubjectID,
		DispensingID: dispensing.ID,
	}
	_, err = tools.Database.Collection("medicine_order").InsertOne(sctx, order)
	if err != nil {
		return "", errors.WithStack(err)
	}
	// 插入订单轨迹
	tools.Database.Collection("history").InsertOne(sctx, models.History{
		Key:  "history.order.apply",
		OID:  orderOID,
		Data: bson.M{"remark": remark},
		Time: time.Duration(time.Now().Unix()),
		UID:  user.ID,
		User: user.Name,
	})

	//创建待确认的订单任务
	permissions := []string{"operation.supply.shipment.cancel", "operation.supply.shipment.confirm"}
	siteOrStoreIDs := []primitive.ObjectID{storeOID, projectSiteOID}
	userIds, err := tools.GetPermissionUserIds(sctx, permissions, dispensing.ProjectID, dispensing.EnvironmentID, siteOrStoreIDs...)
	if err != nil {
		return "", errors.WithStack(err)
	}

	if len(userIds) > 0 {
		workTask := models.WorkTask{
			ID:            primitive.NewObjectID(),
			CustomerID:    dispensing.CustomerID,
			ProjectID:     dispensing.ProjectID,
			EnvironmentID: dispensing.EnvironmentID,
			CohortID:      dispensing.CohortID,
			UserIDs:       userIds,
			Info: models.WorkTaskInfo{
				WorkType:        2,
				Status:          0,
				CreatedTime:     time.Duration(time.Now().Unix()),
				Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
				MedicineIDs:     []primitive.ObjectID{},
				MedicineOrderID: orderOID,
				DispensingID:    primitive.NilObjectID,
			},
		}
		_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
		if err != nil {
			return "", errors.WithStack(err)
		}
	}

	// TODO 检查订单号是否已经占用了

	return orderNumber, nil

	// 发送订单邮件
}

// 查询药物应补发数量
func getReissueCount(ctx *gin.Context, drugConfig []models.DrugValue, repeatedDrug []models.DrugValue) ([]models.DrugValue, error) {
	var prepareDrug []models.DrugValue
	if len(drugConfig) < len(repeatedDrug) {
		return nil, tools.BuildServerError(ctx, "edc.drug.reissue.error")
	}

	for i, config := range drugConfig {
		index := -1
		needCount := 0
		for _, repeated := range repeatedDrug {
			if config.DrugName == repeated.DrugName {
				if config.DispensingNumber < repeated.DispensingNumber {
					return nil, tools.BuildServerError(ctx, "edc.drug.reissue.error")
				} else {
					index = i
					needCount = config.DispensingNumber - repeated.DispensingNumber
				}
			}
		}
		if index == -1 {
			prepareDrug = append(prepareDrug, models.DrugValue{
				DrugName:         config.DrugName,
				DispensingNumber: config.DispensingNumber,
				PkgSpec:          config.PkgSpec,
				DrugSpec:         config.DrugSpec,
			})
		} else {
			if needCount != 0 {
				prepareDrug = append(prepareDrug, models.DrugValue{
					DrugName:         config.DrugName,
					DispensingNumber: needCount,
					PkgSpec:          config.PkgSpec,
					DrugSpec:         config.DrugSpec,
				})
			}
		}
	}
	if len(prepareDrug) == 0 {
		return nil, tools.BuildServerError(ctx, "edc.drug.reissue.error")
	}
	return prepareDrug, nil
}

func getOrderNumber(sctx mongo.SessionContext, OID primitive.ObjectID) (string, error) {
	var orderNumber string
	var data []models.MedicineOrder
	timeZone, err := tools.GetTimeZone(OID)
	if err != nil {
		return "", errors.WithStack(err)
	}
	hour := time.Duration(timeZone)
	minute := time.Duration((timeZone - float64(hour)) * 60)
	duration := hour*time.Hour + minute*time.Minute
	now := time.Now().UTC().Add(duration).Format("20060102")
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"order_number": bson.M{"$regex": "^" + now}}}},
		{{Key: "$sort", Value: bson.D{{"order_number", -1}}}},
		{{Key: "$limit", Value: 1}},
	}
	cursor, err := tools.Database.Collection("medicine_order").Aggregate(sctx, pipepine)
	if err != nil {
		return "", errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return "", errors.WithStack(err)
	}

	if data != nil {
		maxOrderNumber := data[0].OrderNumber
		if maxOrderNumber[:8] == now {
			maxNumber, _ := strconv.Atoi(maxOrderNumber[9:])
			number := strconv.Itoa(maxNumber + 1)
			formatNumber := fmt.Sprintf("%04s", number)
			orderNumber = now + formatNumber
		} else {
			orderNumber = now + "0001"
		}
	} else {
		orderNumber = now + "0001"
	}

	return orderNumber, nil
}

func mailWithDTP(ctx *gin.Context, attribute models.Attribute, subjectOID primitive.ObjectID, envOID primitive.ObjectID, visitInfo models.VisitCycleInfo, medicineNumber []string, subject string, content string, replaceNumber []string, remark string, orderNumber string, reason string, now time.Duration) error {

	//subjectReplaceText := GetSubjectReplaceText(ctx, attribute)
	subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
	subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)

	//  发药邮件通知
	showNumber := attribute.AttributeInfo.IsRandomNumber
	blind := attribute.AttributeInfo.Blind
	medicineNumberStr := strings.Join(medicineNumber, " ")
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": subjectOID}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project_site",
			"localField":   "project_site_id",
			"foreignField": "_id",
			"as":           "project_site",
		}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "project",
			"localField":   "project_id",
			"foreignField": "_id",
			"as":           "project",
		}}},
		{{Key: "$unwind", Value: "$project"}},
		{{Key: "$unwind", Value: "$project.envs"}},
		{{Key: "$match", Value: bson.M{"project.envs.id": envOID}}},
		{{Key: "$project",
			Value: bson.M{
				"_id":             0,
				"customer_id":     1,
				"project_id":      1,
				"env_id":          1,
				"cohort_id":       1,
				"random_list_id":  1,
				"site_id":         "$project_site._id",
				"name":            models.ProjectSiteNameLookUpBsonLang("zh"),
				"nameEn":          models.ProjectSiteNameLookUpBsonLang("en"),
				"number":          "$project_site.number",
				"subject":         "$info.value",
				"env":             "$project.envs.name",
				"projectName":     "$project.info.name",
				"projectNumber":   "$project.info.number",
				"projectTimeZone": "$project.info.timeZone",
				"group":           1,
				"par_group_name":  1,
				"sub_group_name":  1,
				"random_number":   1,
			},
		}},
	}
	var siteInfo []map[string]interface{}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &siteInfo)
	if err != nil {
		return errors.WithStack(err)
	}
	if siteInfo[0]["sub_group_name"] != nil && siteInfo[0]["sub_group_name"].(string) != "" {
		var randomList models.RandomList
		err := tools.Database.Collection("random_list").FindOne(nil, bson.M{"_id": siteInfo[0]["random_list_id"].(primitive.ObjectID)}).Decode(&randomList)
		if err != nil {
			return errors.WithStack(err)
		}
		groupP, b2 := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
			return siteInfo[0]["par_group_name"].(string) == item.ParName && siteInfo[0]["sub_group_name"].(string) == item.SubName
		})
		if b2 {
			siteInfo[0]["subGroupBlind"] = groupP.Blind
		}
	}
	var mails []models.Mail
	strTimeZone, err := tools.GetSiteTimeZone(siteInfo[0]["site_id"].(primitive.ObjectID))
	if err != nil {
		return errors.WithStack(err)
	}
	if strTimeZone == "" {
		zone, err := tools.GetTimeZone(siteInfo[0]["project_id"].(primitive.ObjectID))
		if err != nil {
			return err
		}
		//strTimeZone = fmt.Sprintf("UTC%+d", zone)
		strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)
	hours := time.Duration(intTimeZone)
	minutes := time.Duration((intTimeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	dispensingDate := time.Unix(int64(now), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
	dispensingDate = dispensingDate + " (" + strTimeZone + ")"
	number := ""
	if showNumber {
		number = siteInfo[0]["random_number"].(string)
	}

	data := map[string]interface{}{
		"projectNumber":  siteInfo[0]["projectNumber"],
		"projectName":    siteInfo[0]["projectName"],
		"envName":        siteInfo[0]["env"],
		"siteNumber":     siteInfo[0]["number"],
		"siteName":       siteInfo[0]["name"],
		"siteNameEn":     siteInfo[0]["nameEn"],
		"label":          subjectEmailReplaceTextZh,
		"labelEn":        subjectEmailReplaceTextEn,
		"subjectNumber":  siteInfo[0]["subject"],
		"drugNumber":     medicineNumberStr,
		"visitName":      visitInfo.Name,
		"dispensingDate": dispensingDate,
		"orderNumber":    orderNumber,
		"remark":         remark,
		"group":          siteInfo[0]["group"],
		"parName":        siteInfo[0]["par_group_name"],
		"subGroup":       siteInfo[0]["sub_group_name"],
		"random_number":  number,
	}
	noticeType := "notice.medicine.order"
	userMail, err := tools.GetRoleUsersMailWithRole(siteInfo[0]["project_id"].(primitive.ObjectID), siteInfo[0]["env_id"].(primitive.ObjectID), "notice.medicine.order", siteInfo[0]["site_id"].(primitive.ObjectID))
	if err != nil {
		return err
	}
	if len(replaceNumber) > 0 {
		replaceNumberStr := strings.Join(replaceNumber, " ")
		data = map[string]interface{}{
			"projectNumber":  siteInfo[0]["projectNumber"],
			"projectName":    siteInfo[0]["projectName"],
			"envName":        siteInfo[0]["env"],
			"siteNumber":     siteInfo[0]["number"],
			"siteName":       siteInfo[0]["name"],
			"siteNameEn":     siteInfo[0]["nameEn"],
			"subjectNumber":  siteInfo[0]["subject"],
			"drugNumber":     medicineNumberStr,
			"replaceNumber":  replaceNumberStr,
			"dispensingDate": dispensingDate,
			"orderNumber":    orderNumber,
			"reason":         reason,
			"group":          siteInfo[0]["group"],
			"parName":        siteInfo[0]["par_group_name"],
			"subGroup":       siteInfo[0]["sub_group_name"],
			"label":          subjectEmailReplaceTextZh,
			"labelEn":        subjectEmailReplaceTextEn,
			"random_number":  number}
		userMail, err = tools.GetRoleUsersMailWithRole(siteInfo[0]["project_id"].(primitive.ObjectID), siteInfo[0]["env_id"].(primitive.ObjectID), "notice.subject.dispensing", siteInfo[0]["site_id"].(primitive.ObjectID))
		if err != nil {
			return err
		}
		noticeType = "notice.subject.dispensing"
	}

	if subject == "dispensing.unscheduled-apply-title" {
		data["visitName"] = visitInfo.Name + "(" + locales.Tr(ctx, "export.dispensing.outVisit") + ")"
		data["reason"] = reason
	}
	if subject == "dispensing.reissue-dtp-title" {
		data["visitName"] = visitInfo.Name + "(" + locales.Tr(ctx, "export.dispensing.reissue") + ")"
	}

	// JIRA3036 查询开放药物配置 未编号如果是开放药物不打码，是盲态药物 根据角色判断是否打码
	IsOpenDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return errors.WithStack(err)
	}

	bodyContentKeys, _, err := tools.MailCustomContent(ctx, envOID, noticeType, data, map[string]bool{})
	if err != nil {
		return errors.WithStack(err)
	}

	var noticeConfig models.NoticeConfig
	err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	langList := make([]string, 0)
	if noticeConfig.Manual != 0 {
		if noticeConfig.Manual == 1 {
			langList = append(langList, "zh")
		} else if noticeConfig.Manual == 2 {
			langList = append(langList, "en")
		} else if noticeConfig.Manual == 3 {
			langList = append(langList, "zh")
			langList = append(langList, "en")
		}
	} else {
		langList = append(langList, ctx.GetHeader("Accept-Language"))
	}

	for _, userEmail := range userMail {
		insertData := make(map[string]interface{})
		for k, v := range data {
			insertData[k] = v
		}

		if userEmail.IsBlind {
			for i, item := range medicineNumber {
				for key := range IsOpenDrugMap {
					if strings.Contains(item, key) {
						medicineNumber[i] = strings.Replace(item, key, tools.BlindData, 1)
					}
				}
			}
			medicineNumberStr = strings.Join(medicineNumber, " ")
			insertData["drugNumber"] = medicineNumberStr
		}

		var toUserMail []string
		if insertData["subGroup"] != nil && insertData["subGroup"] != "" {
			insertData["group"] = insertData["parName"]
		}
		if userEmail.IsBlind && blind {
			insertData["group"] = tools.BlindData
		}
		if userEmail.IsBlind && siteInfo[0]["subGroupBlind"] != nil && siteInfo[0]["subGroupBlind"].(bool) {
			data["subGroup"] = tools.BlindData
		} else {
			data["subGroup"] = siteInfo[0]["subGroup"]
		}
		_, contentKeys, err := tools.MailCustomContent(ctx, envOID, "notice.subject.dispensing", insertData, map[string]bool{})
		if err != nil {
			return errors.WithStack(err)
		}

		toUserMail = append(toUserMail, userEmail.Email)
		mails = append(mails, models.Mail{
			ID:      primitive.NewObjectID(),
			Subject: subject,
			SubjectData: map[string]interface{}{"projectNumber": siteInfo[0]["projectNumber"],
				"projectName": siteInfo[0]["projectName"],
				"envName":     siteInfo[0]["env"],
				"siteNumber":  siteInfo[0]["number"],
				"siteName":    siteInfo[0]["name"],
				"siteNameEn":  siteInfo[0]["nameEn"],
			},

			Content:        content,
			ContentData:    insertData,
			To:             toUserMail, // test测试
			Lang:           ctx.GetHeader("Accept-Language"),
			LangList:       langList,
			Status:         0,
			CreatedTime:    time.Duration(time.Now().Unix()),
			ExpectedTime:   time.Duration(time.Now().Unix()),
			SendTime:       time.Duration(time.Now().Unix()),
			BodyContentKey: bodyContentKeys,
			ContentKey:     contentKeys,
		})
	}
	ctx.Set("MAIL", mails)
	if len(mails) > 0 {
		var envs []models.MailEnv
		for _, m := range mails {
			envs = append(envs, models.MailEnv{
				ID:         primitive.NewObjectID(),
				MailID:     m.ID,
				CustomerID: siteInfo[0]["customer_id"].(primitive.ObjectID),
				ProjectID:  siteInfo[0]["project_id"].(primitive.ObjectID),
				EnvID:      siteInfo[0]["env_id"].(primitive.ObjectID),
				CohortID:   siteInfo[0]["cohort_id"].(primitive.ObjectID),
			})
		}
		ctx.Set("MAIL-ENV", envs)
	}
	return nil
}

// DTP模式下 替换操作更新订单信息
func updateOrderInfoWithDTP(ctx *gin.Context, sctx mongo.SessionContext, orderOID primitive.ObjectID, BeReplace, Replace []primitive.ObjectID, order models.MedicineOrder, StoreHouseOID, projectSiteOID primitive.ObjectID, remark string) (string, error) {
	// 去除被替换的ID
	orderNumber := ""
	var err error
	if order.Status == 7 {
		update := bson.M{
			"$pull": bson.M{
				"medicines": bson.M{"$in": BeReplace},
			},
		}

		_, err := tools.Database.Collection("medicine_order").UpdateOne(sctx, bson.M{"_id": orderOID}, update)
		if err != nil {
			return orderNumber, errors.WithStack(err)
		}
		// 加入已替换的ID
		update = bson.M{
			"$addToSet": bson.M{
				"medicines": bson.M{"$each": Replace},
			},
		}

		_, err = tools.Database.Collection("medicine_order").UpdateOne(sctx, bson.M{"_id": orderOID}, update)
		if err != nil {
			return orderNumber, errors.WithStack(err)
		}
	} else { // 接收态 新增订单
		// 生成订单, 并获取订单号
		var dispensing models.Dispensing
		for _, id := range Replace {
			dispensing.DispensingMedicines = append(dispensing.DispensingMedicines, models.DispensingMedicine{
				MedicineID: id,
			})
		}
		dispensing.CustomerID = order.CustomerID
		dispensing.ProjectID = order.ProjectID
		dispensing.EnvironmentID = order.EnvironmentID
		dispensing.ID = order.DispensingID
		dispensing.SubjectID = order.SubjectID

		orderNumber, err = generateOrder(sctx, ctx, primitive.NewObjectID(), dispensing, StoreHouseOID, projectSiteOID, remark)
		if err != nil {
			return orderNumber, errors.WithStack(err)
		}
	}

	return orderNumber, nil
}

// 通用DTP模式下 替换操作更新订单信息
func updateOrderInfo(ctx *gin.Context, sctx mongo.SessionContext, dispensing models.Dispensing, BeReplace []primitive.ObjectID, Replace []primitive.ObjectID, sendType int, siteOID, StoreOID, siteOrderOID, storeOrderOID primitive.ObjectID, DTPMap map[primitive.ObjectID]int, histories *[]models.History, medicineOtherCount, beOtherMedicineCounts []primitive.ObjectID) (models.MedicineOrder, error) {
	user := models.User{}
	var userName = "EDC"
	u, _ := ctx.Get("user")
	if u != nil {
		user = u.(models.User)
		userName = user.Name
	}
	// 检查之前的药物是否在改访视下 有生成订单，存在则 pull选中的被药物ID
	var order models.MedicineOrder
	var orders []models.MedicineOrder
	cursor, err := tools.Database.Collection("medicine_order").Find(sctx, bson.M{"dispensing_id": dispensing.ID,
		"status": bson.M{"$nin": bson.A{4, 5, 8, 9}},
		"$or": bson.A{
			bson.M{"medicines": bson.M{"$in": BeReplace}},
			bson.M{"other_medicines_new": bson.M{"$in": beOtherMedicineCounts}},
		}}, &options.FindOptions{
		Sort: bson.M{"order_number": 1},
	})
	if err != nil && err != mongo.ErrNoDocuments {
		return models.MedicineOrder{}, errors.WithStack(err)

	}

	err = cursor.All(nil, &orders)
	if err != nil {
		return models.MedicineOrder{}, errors.WithStack(err)
	}
	for _, medicineOrder := range orders {

		if !(medicineOrder.Status == 1 || medicineOrder.Status == 6 || medicineOrder.Status == 3) { // 待确认、已确认、已接收可以替换
			return models.MedicineOrder{}, tools.BuildServerError(ctx, "subject_visit_cannot_replace")
		}
		update := bson.M{}
		if len(BeReplace) > 0 {
			update["$pull"] = bson.M{"medicines": bson.M{"$in": BeReplace}}
		}
		if len(beOtherMedicineCounts) > 0 {
			update["$pull"] = bson.M{"other_medicines_new": bson.M{"$in": beOtherMedicineCounts}}
		}
		res := slice.Difference(medicineOrder.Medicines, BeReplace)
		OhterRes := slice.Difference(medicineOrder.OtherMedicinesNew, beOtherMedicineCounts)
		if len(res) == 0 && len(OhterRes) == 0 {
			// 订单药物全部更新 更新订单状态未关闭
			update["$set"] = bson.M{"status": 9, "sort_index": 40, "reason": "All IP are replaced"}
			// 写入轨迹
			*histories = append(*histories, models.History{
				Key:  "history.order.close-with-dtp",
				Data: bson.M{"orderNumber": medicineOrder.OrderNumber},
				OID:  medicineOrder.ID,
				Time: time.Duration(time.Now().Unix()),
				UID:  user.ID,
				User: userName,
			})
			//订单关闭，对应的app任务也需要更新
			_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"info.medicine_order_id": medicineOrder.ID,
				"info.status": 0, "deleted": false}, bson.M{"$set": bson.M{"deleted": true}})
			if err != nil {
				return models.MedicineOrder{}, errors.WithStack(err)
			}
		}
		if update["$set"] != nil || update["$pull"] != nil {
			_, err = tools.Database.Collection("medicine_order").UpdateOne(sctx, bson.M{"_id": medicineOrder.ID}, update)
			if err != nil {
				return models.MedicineOrder{}, errors.WithStack(err)
			}
		}

	}

	// 已替换的ID 插入新的订单
	orderNumber, err := getOrderNumber(sctx, dispensing.ProjectID)
	if err != nil {
		return models.MedicineOrder{}, errors.WithStack(err)
	}

	Replace = slice.Filter(Replace, func(index int, item primitive.ObjectID) bool {
		return DTPMap[item] == sendType
	})
	medicineOtherCount = slice.Filter(medicineOtherCount, func(index int, item primitive.ObjectID) bool {
		return DTPMap[item] == sendType
	})

	types := 5
	orderOID := storeOrderOID

	sendSource := StoreOID
	if sendType == 1 {
		types = 6
		sendSource = siteOID
		orderOID = siteOrderOID
	}
	if sendType != 0 && (len(Replace) > 0 || len(medicineOtherCount) > 0) {

		order = models.MedicineOrder{
			ID:                orderOID,
			CustomerID:        dispensing.CustomerID,
			ProjectID:         dispensing.ProjectID,
			EnvironmentID:     dispensing.EnvironmentID,
			SendID:            sendSource,
			Status:            6, //待确认
			SortIndex:         1,
			Mode:              1,
			Medicines:         Replace,
			MedicinesHistory:  []models.MedicinesHistory{},
			OtherMedicinesNew: medicineOtherCount,
			OrderNumber:       orderNumber,
			Type:              types,
			Meta: models.Meta{
				CreatedBy: user.ID,
				CreatedAt: time.Duration(time.Now().Unix()),
			},
			SubjectID:    dispensing.SubjectID,
			DispensingID: dispensing.ID,
		}
		_, err = tools.Database.Collection("medicine_order").InsertOne(sctx, order)
		if err != nil {
			return order, errors.WithStack(err)
		}

		//创建待确认的订单任务
		permissions := []string{"operation.supply.shipment.cancel", "operation.supply.shipment.confirm"}
		siteOrStoreIDs := []primitive.ObjectID{siteOID}
		userIds, err := tools.GetPermissionUserIds(sctx, permissions, dispensing.ProjectID, dispensing.EnvironmentID, siteOrStoreIDs...)
		if err != nil {
			return order, errors.WithStack(err)
		}

		if len(userIds) > 0 {
			workTask := models.WorkTask{
				ID:            primitive.NewObjectID(),
				CustomerID:    dispensing.CustomerID,
				ProjectID:     dispensing.ProjectID,
				EnvironmentID: dispensing.EnvironmentID,
				CohortID:      dispensing.CohortID,
				UserIDs:       userIds,
				Info: models.WorkTaskInfo{
					WorkType:        2,
					Status:          0,
					CreatedTime:     time.Duration(time.Now().Unix()),
					Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
					MedicineIDs:     []primitive.ObjectID{},
					MedicineOrderID: orderOID,
					DispensingID:    primitive.NilObjectID,
				},
			}
			_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
			if err != nil {
				return order, errors.WithStack(err)
			}
		}

		_, err = tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": Replace}}, bson.M{"$set": bson.M{"order_id": orderOID}})
		if err != nil && err != mongo.ErrNoDocuments {
			return models.MedicineOrder{}, errors.WithStack(err)
		}
		*histories = append(*histories, models.History{
			Key:  "history.order.create",
			OID:  orderOID,
			Time: time.Duration(time.Now().Unix()),
			UID:  user.ID,
			User: userName,
		})
	}

	return order, nil
}

func formulaMax(dispensing models.Dispensing, name string, other bool) int {
	max := 0
	if other {
		medicines := slice.Filter(dispensing.OtherDispensingMedicines, func(index int, item models.OtherDispensingMedicine) bool {
			return item.Name == name
		})
		for _, medicine := range medicines {
			max = max + medicine.Count
		}
	} else {
		medicines := slice.Filter(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
			return item.Name == name
		})
		max = len(medicines)
	}
	return max
}

func getOtherMedicine(ctx *gin.Context, sctx mongo.SessionContext, subject models.Subject, values models.DrugValue, date string, number int, sendType int, otherMedicines *[]models.OtherMedicineCount, now time.Duration, sourceSend primitive.ObjectID, types int, dispensingAlarmNumber map[string]int32, labelStr string) ([]models.OtherDispensingMedicine, error) {

	DTPProject, _ := ctx.Get("dtp")

	var medicineOtherInstitutes []models.MedicineOtherInstitute
	var medicineOthers []models.MedicineOtherInstitute
	// 按过期时间，获取不同批次的非编号药物。
	useCount := 0
	needCount := number
	var batches []string
	var expireDate []string
	var batchNumber []models.BatchCount
	otherDispensingMedicine := []models.OtherDispensingMedicine{}

	if sendType != 2 { // 发放方式 中心发药
		var medicineOtherInstitute models.MedicineOtherInstitute
		drugMatch := bson.M{"institute_id": subject.ProjectSiteID, "customer_id": subject.CustomerID, "project_id": subject.ProjectID,
			"env_id": subject.EnvironmentID, "info.name": values.DrugName,
			"info.expire_date": bson.M{"$gt": date}}
		opts := &options.FindOptions{
			Sort: bson.D{{"info.expire_date", 1}},
		}
		cursor, err := tools.Database.Collection("medicine_other_institute").Find(sctx, drugMatch, opts)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &medicineOtherInstitutes)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		err = checkDispensingOtherAlarm(ctx, medicineOtherInstitutes, int64(needCount), dispensingAlarmNumber[values.DrugName])
		if err != nil {
			return nil, err
		}

		for _, institute := range medicineOtherInstitutes { // 按批次便利中心未编号药 取药直到 needCount == count
			if needCount == 0 {
				break
			}
			if institute.Info.Count < needCount {
				if institute.Info.Count != 0 {
					update :=
						bson.M{
							"$set": bson.M{"edit": true},
							"$inc": bson.M{
								"info.count":      institute.Info.Count * -1,
								"info.used_count": institute.Info.Count * 1,
							},
						}

					if sendType == 1 {
						update =
							bson.M{
								"$set": bson.M{"edit": true},
								"$inc": bson.M{
									"info.count":               institute.Info.Count * -1,
									"info.to_be_confirm_count": institute.Info.Count * 1,
								},
							}
					}
					err := tools.Database.Collection("medicine_other_institute").FindOneAndUpdate(sctx, bson.M{"_id": institute.ID}, update).Decode(&medicineOtherInstitute)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					batches = append(batches, institute.Info.Batch)
					expireDate = append(expireDate, institute.Info.ExpireDate)
					batchNumber = append(batchNumber, models.BatchCount{Batch: institute.Info.Batch, Count: institute.Info.Count})
					useCount += institute.Info.Count
					needCount -= institute.Info.Count
					*otherMedicines = append(*otherMedicines, models.OtherMedicineCount{ // 后续写入订单结构
						ID:         institute.ID,
						Name:       institute.Info.Name,
						UseCount:   institute.Info.Count,
						ExpireDate: institute.Info.ExpireDate,
						Batch:      institute.Info.Batch,
					})

					otherDispensingMedicine = append(otherDispensingMedicine, models.OtherDispensingMedicine{
						ID:              primitive.NewObjectID(),
						MedicineOtherID: institute.ID,
						Name:            institute.Info.Name,
						Count:           institute.Info.Count,
						Batch:           institute.Info.Batch,
						ExpireDate:      institute.Info.ExpireDate,
						Time:            now,
						Type:            types,
						BatchCount:      batchNumber,
						Label:           getLabelStr(values.Label, labelStr),
					})
				}
			} else {
				update := bson.M{
					"$set": bson.M{"edit": true},
					"$inc": bson.M{
						"info.count":      needCount * -1,
						"info.used_count": needCount * 1,
					},
				}

				if sendType == 1 {
					update = bson.M{
						"$set": bson.M{"edit": true},
						"$inc": bson.M{
							"info.count":               needCount * -1,
							"info.to_be_confirm_count": needCount * 1,
						},
					}
				}
				err := tools.Database.Collection("medicine_other_institute").FindOneAndUpdate(sctx, bson.M{"_id": institute.ID}, update).Decode(&medicineOtherInstitute)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				batches = append(batches, institute.Info.Batch)
				expireDate = append(expireDate, institute.Info.ExpireDate)
				batchNumber = append(batchNumber, models.BatchCount{Batch: institute.Info.Batch, Count: needCount})
				*otherMedicines = append(*otherMedicines, models.OtherMedicineCount{ // 后续写入订单结构
					ID:         institute.ID,
					Name:       institute.Info.Name,
					UseCount:   needCount,
					ExpireDate: institute.Info.ExpireDate,
					Batch:      institute.Info.Batch,
				})
				otherDispensingMedicine = append(otherDispensingMedicine, models.OtherDispensingMedicine{
					ID:              primitive.NewObjectID(),
					MedicineOtherID: institute.ID,
					Name:            institute.Info.Name,
					Count:           needCount,
					Batch:           institute.Info.Batch,
					ExpireDate:      institute.Info.ExpireDate,
					Time:            now,
					Type:            types,
					BatchCount:      batchNumber,
					Label:           getLabelStr(values.Label, labelStr),
				})
				useCount += needCount
				needCount -= needCount
			}

		}
	} else { // 从库房拿药
		var medicineOtherInstitute models.MedicineOtherInstitute
		//查具体仓库
		drugMatch := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "storehouse_id": sourceSend,
			"env_id": subject.EnvironmentID, "info.name": values.DrugName,
			"info.expire_date": bson.M{"$gt": date}}
		opts := &options.FindOptions{
			Sort: bson.D{{"info.expire_date", 1}},
		}
		cursor, err := tools.Database.Collection("medicine_other_institute").Find(sctx, drugMatch, opts)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &medicineOthers)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = checkDispensingOtherAlarm(ctx, medicineOthers, int64(needCount), dispensingAlarmNumber[values.DrugName])
		if err != nil {
			return nil, err
		}
		for _, institute := range medicineOthers {
			if needCount == 0 {
				break
			}
			if institute.Info.Count < needCount {
				if institute.Info.Count != 0 {
					update := bson.M{
						"$set": bson.M{"edit": true},
						"$inc": bson.M{
							"info.count":               institute.Info.Count * -1,
							"info.to_be_confirm_count": institute.Info.Count * 1,
						},
					}

					if DTPProject != nil && DTPProject.(bool) {
						update =
							bson.M{
								"$set": bson.M{"edit": true},
								"$inc": bson.M{
									"info.count":       institute.Info.Count * -1,
									"info.apply_count": institute.Info.Count * 1,
								},
							}
					}
					err := tools.Database.Collection("medicine_other_institute").FindOneAndUpdate(sctx, bson.M{"_id": institute.ID}, update).Decode(&medicineOtherInstitute)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					batches = append(batches, institute.Info.Batch)
					expireDate = append(expireDate, institute.Info.ExpireDate)
					batchNumber = append(batchNumber, models.BatchCount{Batch: institute.Info.Batch, Count: institute.Info.Count})

					*otherMedicines = append(*otherMedicines, models.OtherMedicineCount{ // 后续写入订单结构
						ID:         institute.ID,
						Name:       institute.Info.Name,
						UseCount:   institute.Info.Count,
						ExpireDate: institute.Info.ExpireDate,
						Batch:      institute.Info.Batch,
					})
					otherDispensingMedicine = append(otherDispensingMedicine, models.OtherDispensingMedicine{
						ID:              primitive.NewObjectID(),
						MedicineOtherID: institute.ID,
						Name:            institute.Info.Name,
						Count:           institute.Info.Count,
						Batch:           institute.Info.Batch,
						ExpireDate:      institute.Info.ExpireDate,
						Time:            now,
						Type:            types,
						BatchCount:      batchNumber,
						Label:           getLabelStr(values.Label, labelStr),
					})
					useCount += institute.Info.Count
					needCount -= institute.Info.Count
				}
			} else {
				update := bson.M{
					"$set": bson.M{"edit": true},
					"$inc": bson.M{
						"info.count":               needCount * -1,
						"info.to_be_confirm_count": needCount * 1,
					},
				}
				if DTPProject != nil && DTPProject.(bool) {
					update = bson.M{
						"$set": bson.M{"edit": true},
						"$inc": bson.M{
							"info.count":       needCount * -1,
							"info.apply_count": needCount * 1,
						},
					}
				}
				err := tools.Database.Collection("medicine_other_institute").FindOneAndUpdate(sctx, bson.M{"_id": institute.ID}, update).Decode(&medicineOtherInstitute)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				batches = append(batches, institute.Info.Batch)
				expireDate = append(expireDate, institute.Info.ExpireDate)
				batchNumber = append(batchNumber, models.BatchCount{Batch: institute.Info.Batch, Count: needCount})
				*otherMedicines = append(*otherMedicines, models.OtherMedicineCount{ // 后续写入订单结构
					ID:         institute.ID,
					Name:       institute.Info.Name,
					UseCount:   needCount,
					ExpireDate: institute.Info.ExpireDate,
					Batch:      institute.Info.Batch,
				})
				otherDispensingMedicine = append(otherDispensingMedicine, models.OtherDispensingMedicine{
					ID:              primitive.NewObjectID(),
					MedicineOtherID: institute.ID,
					Name:            institute.Info.Name,
					Count:           needCount,
					Batch:           institute.Info.Batch,
					ExpireDate:      institute.Info.ExpireDate,
					Time:            now,
					Type:            types,
					BatchCount:      batchNumber,
					Label:           getLabelStr(values.Label, labelStr),
				})
				useCount += needCount
				needCount -= needCount
			}
		}
	}
	if useCount != number {
		if sendType != 2 {
			return nil, tools.BuildServerError(ctx, "subject_medicine_count")
		} else {
			return nil, tools.BuildServerError(ctx, "subject_medicine_count_store")

		}
	}
	return otherDispensingMedicine, nil
}

func getLabelStr(label1 string, label2 string) string {
	if label1 != "" {
		return label1
	} else if label2 != "" {
		return label2
	} else {
		return ""
	}
}

func getOthersMedicine(ctx *gin.Context, sctx mongo.SessionContext, subject models.Subject, date string, number int, values models.DispensingDrugValue, otherMedicines *[]primitive.ObjectID, sendType int, siteOID, StoreOID, siteOrderOID, StoreOrderOID primitive.ObjectID, now time.Duration, dispensingAlarmNumber map[string]int32, labelStr string, types int, batch string, attribute models.Attribute, alarmCapacityInfoData map[int]map[string]models.WarnCapacityActual, heritanceName map[string]string, lastDate string) ([]models.OtherDispensingMedicine, []models.OthersDispensingMedicine, error) {
	DTPProject, _ := ctx.Get("dtp")
	drugMatch := bson.M{
		"customer_id":     subject.CustomerID,
		"project_id":      subject.ProjectID,
		"env_id":          subject.EnvironmentID,
		"status":          1,
		"name":            values.DrugName,
		"expiration_date": bson.M{"$gt": date},
		//"spec":            values.DrugSpec,
		"$or": bson.A{
			bson.M{"subject_id": bson.M{"$exists": 0}},
			bson.M{"subject_id": primitive.NilObjectID},
			bson.M{"subject_id": subject.ID},
		},
	}
	update := bson.M{
		"$set": bson.M{
			"status": 5,
		},
	}
	alarmBatchMap := map[string]models.WarnCapacityActual{} //

	if batch != "" {
		drugMatch["batch_number"] = bson.M{"$ne": batch}
	}
	if sendType != 2 { //  中心库存发放   中心寄送
		if alarmCapacityInfoData != nil {
			alarmBatchMap = alarmCapacityInfoData[1]
		}
		drugMatch["site_id"] = siteOID
	} else { //  仓库寄送
		if StoreOID.IsZero() {
			return nil, nil, tools.BuildServerError(ctx, "subject_visit_dispensing_store")
		}
		drugMatch["storehouse_id"] = StoreOID
		if alarmCapacityInfoData != nil {
			alarmBatchMap = alarmCapacityInfoData[2]
		}

	}
	orderOID := primitive.NilObjectID
	if sendType != 0 { // 使用物流  药物置为待确认
		update = bson.M{
			"$set": bson.M{
				"status": 11,
			},
		}
		if sendType == 1 {
			orderOID = siteOrderOID
		} else {
			orderOID = StoreOrderOID
		}
		update = bson.M{
			"$set": bson.M{
				"status":   11,
				"order_id": orderOID,
			},
		}
	}
	if DTPProject != nil && DTPProject.(bool) {
		update = bson.M{
			"$set": bson.M{
				"status": 13,
			},
		}
	}

	// 阿里对接 匹配包装规格
	if values.PkgSpec != "" {
		drugMatch["pkg_spec"] = values.PkgSpec
	}

	// 盲法项目 随机获取研究产品号，
	var medicines []models.OtherMedicine

	number64 := int64(values.DispensingNumber)

	// 校验发药警戒值
	err := checkDispensingOthersAlarm(ctx, sctx, values.DrugName, drugMatch, number64, dispensingAlarmNumber)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	// 继承访视
	if attribute.AttributeInfo.IPInheritance && heritanceName != nil && heritanceName[values.DrugName] != "" && batch == "" {
		drugMatch["batch_number"] = heritanceName[values.DrugName]
	}

	// 首次访视 选择继承批次
	{ // 继承发放 && 首次没得继承
		if attribute.AttributeInfo.IPInheritance && heritanceName[values.DrugName] == "" { //
			// 首次发药选择批次
			batchs := []string{}
			hasMedicine := true
			if date < lastDate { // 查询 剩余访视周期 <= 计划发放批次药物的剩余有效期的
				hasMedicine, batchs, err = canUseBatchMatch(sctx, "medicine_others", lastDate, date, drugMatch)
				if err != nil {
					return nil, nil, err
				}
				if hasMedicine && len(batchs) == 0 {
					return nil, nil, tools.BuildServerError(ctx, "subject_medicine_batch_count")
				}
			}

			// 排除已经上限的批次
			for key, info := range alarmBatchMap {
				if info.Capacity <= info.Actual {
					batchs = slice.Filter(batchs, func(index int, item string) bool {
						return item != key
					})
				}
			}
			if len(batchs) > 0 {
				drugMatch["batch_number"] = bson.M{"$in": batchs}
			}
		}
	}

	//  按日期排序 发药
	opts := &options.FindOptions{
		Sort:  bson.D{{"expiration_date", 1}, {"batch_number", 1}, {"package_number", 1}, {"_id", 1}},
		Limit: &number64,
	}
	cursor, err := tools.Database.Collection("medicine_others").Find(sctx, drugMatch, opts)
	if err != nil {
		return nil, nil, errors.WithStack(err)

	}
	err = cursor.All(nil, &medicines)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	//DTP发放  可以发没有批次的
	if number-len(medicines) > 0 && sendType != 0 {
		var unBatchMedicine []models.OtherMedicine
		drugMatch["expiration_date"] = bson.M{"$in": bson.A{nil, ""}}
		needNumber := int64(number - len(medicines))
		cursor, err := tools.Database.Collection("medicine_others").Find(sctx, drugMatch, &options.FindOptions{
			Sort:  bson.M{"_id": 1},
			Limit: &needNumber,
		})
		if err != nil {
			return nil, nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &unBatchMedicine)
		if err != nil {
			return nil, nil, errors.WithStack(err)

		}
		medicines = append(medicines, unBatchMedicine...)
	}

	if len(medicines) != number {
		if sendType != 2 {
			return nil, nil, tools.BuildServerError(ctx, "subject_medicine_count")
		} else {
			return nil, nil, tools.BuildServerError(ctx, "subject_medicine_count_store")

		}
	}
	// 修改编号研究产品状态
	otherDispensingMedicine := []models.OtherDispensingMedicine{}
	othersDispensingMedicine := []models.OthersDispensingMedicine{}

	for _, item := range medicines {
		*otherMedicines = append(*otherMedicines, item.ID)
		medicine := models.OthersDispensingMedicine{
			MedicineID:     item.ID,
			Name:           item.Name,
			ExpirationDate: item.ExpirationDate,
			BatchNumber:    item.BatchNumber,
			PackageNumber:  item.PackageNumber,
			Time:           now,
			Type:           types,
			Label:          getLabelStr(values.Label, labelStr),
			DTP:            &sendType,
		}
		if !orderOID.IsZero() {
			medicine.OrderOID = &orderOID
		}
		othersDispensingMedicine = append(othersDispensingMedicine, medicine)

	}
	drugMatch = bson.M{"_id": bson.M{"$in": *otherMedicines}}
	if _, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, drugMatch, update); err != nil {
		return nil, nil, errors.WithStack(err)

	}

	floor := func(item models.OtherMedicine) string {
		return item.BatchNumber + item.ExpirationDate
	}
	result := slice.GroupWith(medicines, floor)
	for _, value := range result {
		medicine := models.OtherDispensingMedicine{
			ID:         primitive.NewObjectID(),
			Name:       value[0].Name,
			Count:      len(value),
			Batch:      value[0].BatchNumber,
			ExpireDate: value[0].ExpirationDate,
			Time:       now,
			Type:       types,
			Label:      getLabelStr(values.Label, labelStr),
			DTP:        &sendType,
		}
		if !orderOID.IsZero() {
			medicine.OrderOID = &orderOID
		}
		otherDispensingMedicine = append(otherDispensingMedicine, medicine)

	}

	return otherDispensingMedicine, othersDispensingMedicine, nil
}

func getMedicine(ctx *gin.Context, sctx mongo.SessionContext, subject models.Subject, date string, number int, values models.DispensingDrugValue, attribute models.Attribute, userName string, user models.User, medicineNumber *[]string, sendType int, siteOID, storeOID, siteOrderOID, StoreOrderOID primitive.ObjectID, histories *[]models.History, dispensingType int, now time.Duration, visitName string, visitSign bool, dispensingAlarmNumber map[string]int32, labelStr string, allDrugMap map[string]bool, oldOpenProject bool, alarmCapacityInfoData map[int]map[string]models.WarnCapacityActual, heritanceName map[string]string, lastDate string) ([]models.DispensingMedicine, error) {
	DTPProject, _ := ctx.Get("dtp")

	matchOr := bson.A{
		bson.M{"subject_id": bson.M{"$exists": 0}},
		bson.M{"subject_id": primitive.NilObjectID},
		bson.M{"subject_id": subject.ID},
	}
	drugMatch := bson.M{
		"customer_id":     subject.CustomerID,
		"project_id":      subject.ProjectID,
		"env_id":          subject.EnvironmentID,
		"status":          bson.M{"$in": bson.A{1, 14}},
		"name":            values.DrugName,
		"expiration_date": bson.M{"$gt": date},
		"spec":            values.DrugSpec,
		"$or":             matchOr,
		"frozen":          bson.M{"$ne": true},
	}

	medicineKey := "history.medicine.sku-used-subject"
	update := bson.M{
		"$set": bson.M{
			"status": 5,
		},
	}

	alarmBatchMap := map[string]models.WarnCapacityActual{} //

	if sendType != 2 { //  中心库存发放   中心寄送
		drugMatch["site_id"] = siteOID
		alarmBatchMap = alarmCapacityInfoData[1]
	} else { //  仓库寄送
		if storeOID.IsZero() {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_store")
		}
		drugMatch["storehouse_id"] = storeOID
		alarmBatchMap = alarmCapacityInfoData[2]

	}

	orderOID := primitive.NilObjectID
	if sendType != 0 { // 使用物流  药物置为待确认
		update = bson.M{
			"$set": bson.M{
				"status": 11,
			},
		}
		medicineKey = "history.medicine.sku-in-order-subject"
		if sendType == 1 {
			orderOID = siteOrderOID
		} else {
			orderOID = StoreOrderOID
		}

	}
	if DTPProject != nil && DTPProject.(bool) {
		update = bson.M{
			"$set": bson.M{
				"status": 13,
			},
		}
		medicineKey = "history.medicine.apply"
	}

	// 阿里对接 匹配包装规格
	if values.PkgSpec != "" {
		drugMatch["pkg_spec"] = values.PkgSpec
	}

	// 盲法项目 随机获取研究产品号，
	var medicines []models.Medicine

	number64 := int64(values.DispensingNumber)

	// 校验发药警戒值
	err := checkDispensingAlarm(ctx, sctx, values.DrugName, drugMatch, number64, dispensingAlarmNumber)
	if err != nil {
		return nil, err
	}

	if attribute.AttributeInfo.IPInheritance && heritanceName[values.DrugName] != "" {
		drugMatch["batch_number"] = heritanceName[values.DrugName]
	}
	{ // 继承发放 && 首次没得继承
		if attribute.AttributeInfo.IPInheritance && heritanceName[values.DrugName] == "" { //
			// 首次发药选择批次
			batchs := []string{}
			hasMedicine := true
			if date < lastDate { // 查询 剩余访视周期 <= 计划发放批次药物的剩余有效期的
				hasMedicine, batchs, err = canUseBatchMatch(sctx, "medicine", lastDate, date, drugMatch)
				if err != nil {
					return nil, err
				}
				if hasMedicine && len(batchs) == 0 {
					return nil, tools.BuildServerError(ctx, "subject_medicine_batch_count")
				}
			}

			// 排除已经上限的批次
			for key, info := range alarmBatchMap {
				if info.Capacity <= info.Actual {
					batchs = slice.Filter(batchs, func(index int, item string) bool {
						return item != key
					})
				}
			}
			if len(batchs) > 0 {
				drugMatch["batch_number"] = bson.M{"$in": batchs}
			}
		}
	}

	segmentLen := int64(0)
	if (!allDrugMap[values.DrugName] || oldOpenProject) && !attribute.AttributeInfo.Segment { // 开放药物并且没有号段随机
		// 非盲法项目， 按日期排序 发药
		opts := &options.FindOptions{
			Sort:  bson.D{{"subject_id", -1}, {"expiration_date", 1}, {"batch_number", 1}, {"serial_number", 1}, {"number", 1}}, // 优先获取受试者绑定的研究产品  subject_id
			Limit: &number64,
		}
		if drugMatch["storehouse_id"] != nil { // DTP仓库发药分两次查询
			drugMatch["package_number"] = bson.M{"$in": bson.A{nil, ""}}
		}
		cursor, err := tools.Database.Collection("medicine").Find(sctx, drugMatch, opts)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &medicines)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 单品取药不够 按包装号取药
		count := int(number64) - len(medicines)
		if count > 0 {
			pageMedicines, err := getPageMedicine(sctx, drugMatch, int64(count), false)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			medicines = append(medicines, pageMedicines...)
		}

	} else {

		randomMatch := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
		if subject.CohortID != primitive.NilObjectID {
			randomMatch = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
		}
		// RandomDesign
		// RandomDesign
		var randomDesign models.RandomDesign
		err := tools.Database.Collection("random_design").FindOne(sctx, randomMatch).Decode(&randomDesign)
		if err != nil && mongo.ErrNoDocuments != err {
			return nil, errors.WithStack(err)
		}
		for _, group := range randomDesign.Info.Groups {
			if group.SubGroup != nil && len(group.SubGroup) > 0 {
				for _, subGroup := range group.SubGroup {
					if subject.Group == group.Name+" "+subGroup.Name {
						segmentLen = int64(subGroup.SegmentLength)
						break
					}
				}
			} else {
				if group.Name == subject.Group {
					segmentLen = int64(group.SegmentLength)
					break
				}
			}
		}

		// 盲法项目 按照日期大小  随机获取研究产品号
		medicines, err = BlindGetDrug(ctx, sctx, drugMatch, subject.ID, number64, attribute, segmentLen)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	//DTP发放  可以发没有批次的
	if number-len(medicines) > 0 && sendType != 0 {
		var unBatchMedicine []models.Medicine
		drugMatch["expiration_date"] = ""
		delete(drugMatch, "package_number")
		needNumber := int64(number - len(medicines))
		if !allDrugMap[values.DrugName] || oldOpenProject {
			opts := &options.FindOptions{
				Sort:  bson.D{{"subject_id", -1}, {"serial_number", 1}}, // 优先获取受试者绑定的研究产品  subject_id
				Limit: &needNumber,
			}
			cursor, err := tools.Database.Collection("medicine").Find(sctx, drugMatch, opts)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &unBatchMedicine)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else {

			unBatchMedicine, err = BlindGetDrug(ctx, sctx, drugMatch, subject.ID, number64, attribute, segmentLen)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		medicines = append(medicines, unBatchMedicine...)
	}

	if len(medicines) != number {
		if sendType != 2 {
			return nil, tools.BuildServerError(ctx, "subject_medicine_count")
		} else {
			return nil, tools.BuildServerError(ctx, "subject_medicine_count_store")
		}
	}
	// 修改编号研究产品状态
	medicineMatch := bson.A{}
	dispensingMedicine := []models.DispensingMedicine{}
	// 轨迹类型  操作：1.发药、2.取回、3.研究产品替换、4计划外发药、5.补发、6.登记实际使用研究产品
	operation := 1
	if visitSign {
		operation = 4
	}
	if dispensingType == 2 {
		operation = 5
	}

	for _, medicine := range medicines {
		medicineMatch = append(medicineMatch, medicine.ID)
		*medicineNumber = append(*medicineNumber, medicine.Number)
		tmpMedicine := models.DispensingMedicine{
			MedicineID:     medicine.ID,
			Name:           medicine.Name,
			Number:         medicine.Number,
			ShortCode:      medicine.ShortCode,
			ExpirationDate: medicine.ExpirationDate,
			BatchNumber:    medicine.BatchNumber,
			Time:           now,
			Type:           dispensingType,
			PackageNumber:  medicine.PackageNumber,
			Label:          getLabelStr(values.Label, labelStr),
			DTP:            &sendType,
		}
		if !orderOID.IsZero() {
			tmpMedicine.OrderOID = &orderOID
		}
		dispensingMedicine = append(dispensingMedicine, tmpMedicine)
		//轨迹内容

		medicineHistoryData := map[string]interface{}{"subject": subject.Info[0].Value, "medicine": medicine.Number, "visit": visitName, "operation": operation}
		medicineHistory := models.History{
			Key:  medicineKey,
			OID:  medicine.ID,
			Data: medicineHistoryData,
			Time: now,
			UID:  user.ID,
			User: userName,
		}
		*histories = append(*histories, medicineHistory)
	}
	drugMatch = bson.M{"_id": bson.M{"$in": medicineMatch}}
	if _, err := tools.Database.Collection("medicine").UpdateMany(sctx, drugMatch, update); err != nil {
		return nil, errors.WithStack(err)
	}
	return dispensingMedicine, nil
}

// 通用下DTP模式发药 写入订单信息
func setDispensingOrder(ctx *gin.Context, sctx mongo.SessionContext, subject models.Subject, dispensing models.Dispensing, sendType int, sendSource, orderOID primitive.ObjectID) (models.MedicineOrder, error) {
	user := models.User{}
	u, _ := ctx.Get("user")
	if u != nil {
		user = u.(models.User)
	} else {
		user.Name = "edc"
	}
	types := 5
	if sendType == 1 { // 中心到受试者
		types = 6

	}
	orderNumber, err := getOrderNumber(sctx, subject.ProjectID)
	if err != nil {
		return models.MedicineOrder{}, errors.WithStack(err)
	}
	medicines := []primitive.ObjectID{}
	otherMedicines := []primitive.ObjectID{}

	// 不同发放方式过滤订单
	dispensing.DispensingMedicines = slice.Filter(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
		return item.DTP != nil && *item.DTP == sendType
	})
	dispensing.OthersDispensingMedicines = slice.Filter(dispensing.OthersDispensingMedicines, func(index int, item models.OthersDispensingMedicine) bool {
		return item.DTP != nil && *item.DTP == sendType
	})
	for _, medicine := range dispensing.DispensingMedicines {
		medicines = append(medicines, medicine.MedicineID)
	}
	for _, medicine := range dispensing.OthersDispensingMedicines {
		otherMedicines = append(otherMedicines, medicine.MedicineID)
	}
	order := models.MedicineOrder{
		ID:                orderOID,
		CustomerID:        dispensing.CustomerID,
		ProjectID:         dispensing.ProjectID,
		EnvironmentID:     dispensing.EnvironmentID,
		SendID:            sendSource,
		Status:            6, //待确认
		SortIndex:         1,
		Mode:              1,
		Medicines:         medicines,
		MedicinesHistory:  []models.MedicinesHistory{},
		OtherMedicinesNew: otherMedicines,
		OrderNumber:       orderNumber,
		Type:              types,
		Meta: models.Meta{
			CreatedBy: user.ID,
			CreatedAt: time.Duration(time.Now().Unix()),
		},
		SubjectID:    dispensing.SubjectID,
		DispensingID: dispensing.ID,
	}
	// 写入订单轨迹
	_, err = tools.Database.Collection("history").InsertOne(sctx, models.History{
		Key:  "history.order.create",
		OID:  orderOID,
		Time: time.Duration(time.Now().Unix()),
		UID:  user.ID,
		User: user.Name,
	})
	if err != nil {
		return models.MedicineOrder{}, errors.WithStack(err)
	}
	_, err = tools.Database.Collection("medicine_order").InsertOne(sctx, order)
	if err != nil {
		return models.MedicineOrder{}, errors.WithStack(err)
	}
	//创建待确认的订单任务
	permissions := []string{"operation.supply.shipment.cancel", "operation.supply.shipment.confirm"}
	siteOrStoreIDs := []primitive.ObjectID{sendSource}
	userIds, err := tools.GetPermissionUserIds(sctx, permissions, dispensing.ProjectID, dispensing.EnvironmentID, siteOrStoreIDs...)
	if err != nil {
		return order, errors.WithStack(err)
	}
	_, err = tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": medicines}}, bson.M{"$set": bson.M{"order_id": orderOID}})
	if err != nil && err != mongo.ErrNoDocuments {
		return models.MedicineOrder{}, errors.WithStack(err)
	}
	if len(userIds) > 0 {
		workTask := models.WorkTask{
			ID:            primitive.NewObjectID(),
			CustomerID:    dispensing.CustomerID,
			ProjectID:     dispensing.ProjectID,
			EnvironmentID: dispensing.EnvironmentID,
			CohortID:      dispensing.CohortID,
			UserIDs:       userIds,
			Info: models.WorkTaskInfo{
				WorkType:        2,
				Status:          0,
				CreatedTime:     time.Duration(time.Now().Unix()),
				Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
				MedicineIDs:     []primitive.ObjectID{},
				MedicineOrderID: orderOID,
				DispensingID:    primitive.NilObjectID,
			},
		}
		_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
		if err != nil {
			return order, errors.WithStack(err)
		}
	}
	return order, nil
}

func getBoundStore(ctx *gin.Context, sctx mongo.SessionContext, subject models.Subject) (primitive.ObjectID, error) {
	var projectSite models.ProjectSite
	err := tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil && err != mongo.ErrNoDocuments {
		return [12]byte{}, errors.WithStack(err)
	}
	if len(projectSite.StoreHouseID) == 0 {
		return [12]byte{}, tools.BuildServerError(ctx, "subject_visit_dispensing_store")
	}
	if projectSite.StoreHouseID[0] == primitive.NilObjectID {
		return [12]byte{}, tools.BuildServerError(ctx, "subject_visit_dispensing_store")

	}
	return projectSite.StoreHouseID[0], nil
}

func getOpenDrug(ctx *gin.Context, sctx mongo.SessionContext, openDrug models.OpenDrug) error {
	subject := openDrug.Subject
	visitName := openDrug.VisitName
	visitSign := openDrug.VisitSign
	expireDateKey := openDrug.ExpireDateKey
	sendType := openDrug.SendType
	otherDispensingMedicine := openDrug.OtherDispensingMedicine
	othersDispensingMedicine := openDrug.OthersDispensingMedicine
	otherMedicineCount := openDrug.OtherMedicineCount
	now := openDrug.Now
	attribute := openDrug.Attribute
	userName := openDrug.UserName
	user := openDrug.User
	medicineNumber := openDrug.MedicineNumber
	dispensingMedicine := openDrug.DispensingMedicine
	siteOID := openDrug.SiteOID
	histories := openDrug.Histories
	dispensingType := openDrug.DispensingType
	dispensingAlarmNumber := openDrug.DispensingAlarmNumber
	doseInfo := openDrug.DoseInfo
	allDrugMap := openDrug.AllDrugMap
	oldOpenProject := openDrug.OldOpenProject
	siteOrderOID := openDrug.SiteOrderOID
	storeOrderOID := openDrug.StoreOrderOID
	storeOID := openDrug.StoreOID
	alarmCapacityInfoData := openDrug.AlarmCapacityInfoData
	nameBatch := openDrug.NameBatch
	lastDate := openDrug.LastDate
	for _, medicine := range openDrug.MedicineInfo {
		tmpSendType := sendType
		if attribute.AttributeInfo.DtpRule == 1 && medicine.(map[string]interface{})["dtp"] == nil {
			return tools.BuildServerError(ctx, "subject_medicine_dtp_error")
		}
		if attribute.AttributeInfo.DtpRule == 1 && medicine.(map[string]interface{})["dtp"] != nil {
			tmpSendType = int(medicine.(map[string]interface{})["dtp"].(float64))
		}
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return err
		}
		medicineName := medicine.(map[string]interface{})["name"].(string)
		if medicine.(map[string]interface{})["saltName"] != nil && medicine.(map[string]interface{})["saltName"] != "" {
			medicineName = tools.Decrypt(medicine.(map[string]interface{})["saltName"].(string), medicine.(map[string]interface{})["salt"].(string))
		}

		hour := time.Duration(zone)
		minute := time.Duration((zone - float64(hour)) * 60)
		duration := hour*time.Hour + minute*time.Minute
		date := time.Now().UTC().Add(duration).Format("2006-01-02")
		if expireDateKey[medicineName] != nil {
			date = expireDateKey[medicineName].(string)
		}
		// 查具体仓库 判断是否是未编号药物
		//drugMatch := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID,
		//	"info.name": medicineName}
		//count, err := tools.Database.Collection("medicine_other_institute").CountDocuments(sctx, drugMatch)
		//if err != nil {
		//	return errors.WithStack(err)
		//}
		// 未编号研究产品
		value := models.DispensingDrugValue{}
		number := int(medicine.(map[string]interface{})["count"].(float64))
		value.DrugName = medicineName
		value.DrugSpec = medicine.(map[string]interface{})["spec"].(string)
		value.DispensingNumber = number
		if medicine.(map[string]interface{})["is_other"] != nil {
			value.IsOther = medicine.(map[string]interface{})["is_other"].(bool)
		}
		if value.IsOther {
			otherDispensingMedicineSinge, othersDispensingMedicineSinge, err := getOthersMedicine(ctx, sctx, subject, date, number, value, otherMedicineCount, tmpSendType, siteOID, storeOID, siteOrderOID, storeOrderOID, now, dispensingAlarmNumber, "", 1, "", attribute, alarmCapacityInfoData, nameBatch, lastDate)
			if err != nil {
				return err
			}

			if medicine.(map[string]interface{})["useFormula"] != nil {
				useFormula := models.CustomerFormula{
					Key:   medicine.(map[string]interface{})["useFormula"].(string),
					Value: medicine.(map[string]interface{})["formulaCount"].(float64),
				}
				for i := range otherDispensingMedicineSinge {
					otherDispensingMedicineSinge[i].UseFormulas = &useFormula
				}
				for i := range othersDispensingMedicineSinge {
					othersDispensingMedicineSinge[i].UseFormulas = &useFormula
				}
			}
			for i := range otherDispensingMedicineSinge {
				otherDispensingMedicineSinge[i].DoseInfo = doseInfo
			}
			for i := range othersDispensingMedicineSinge {
				othersDispensingMedicineSinge[i].DoseInfo = doseInfo
			}
			*otherDispensingMedicine = slice.Concat(*otherDispensingMedicine, otherDispensingMedicineSinge)
			*othersDispensingMedicine = slice.Concat(*othersDispensingMedicine, othersDispensingMedicineSinge)

		} else {
			// 已编号研究产品
			dispensingMedicineSinge, err := getMedicine(ctx, sctx, subject, date, number, value, attribute, userName, user, medicineNumber, tmpSendType, siteOID, storeOID, siteOrderOID, storeOrderOID, histories, dispensingType, now, visitName, visitSign, dispensingAlarmNumber, "", allDrugMap, oldOpenProject, alarmCapacityInfoData, nameBatch, lastDate)
			if err != nil {
				return err
			}

			if medicine.(map[string]interface{})["useFormula"] != nil && medicine.(map[string]interface{})["formulaCount"] != nil {
				useFormula := models.CustomerFormula{
					Key:   medicine.(map[string]interface{})["useFormula"].(string),
					Value: medicine.(map[string]interface{})["formulaCount"].(float64),
				}
				for i := range dispensingMedicineSinge {
					dispensingMedicineSinge[i].UseFormulas = &useFormula
				}
			}
			if medicine.(map[string]interface{})["level"] != nil {
				for i := range dispensingMedicineSinge {
					dispensingMedicineSinge[i].DoseInfo = doseInfo
				}
			}
			if dispensingType == 1 {
				for i := range dispensingMedicineSinge {
					if medicine.(map[string]interface{})["open_setting"] != nil && medicine.(map[string]interface{})["open_setting"] == 3 {
						dispensingMedicineSinge[i].OpenSetting = 3
					} else {
						dispensingMedicineSinge[i].OpenSetting = 2
					}
				}
			}

			*dispensingMedicine = slice.Concat(*dispensingMedicine, dispensingMedicineSinge)
		}
	}
	return nil
}

func updateRegisterOtherMedicineOrder(ctx *gin.Context, sctx mongo.SessionContext, dispensingID primitive.ObjectID, updatePushIDs []primitive.ObjectID, count int, status int, user models.User, now time.Duration) (models.MedicineOrder, error) {
	var order models.MedicineOrder
	key := "history.order.close-with-register"
	reason := `IP has been registered to be "available/frozen"`
	if status == 2 {
		reason = `IP has been registered to be "Lost/Void"`
		key = "history.order.close-with-register-lost"
	}
	err := tools.Database.Collection("medicine_order").FindOne(sctx, bson.M{"dispensing_id": dispensingID,
		"other_medicines_new": bson.M{"$in": updatePushIDs}, "status": bson.M{"$nin": bson.A{3, 4, 5, 8, 9}}}).Decode(&order)
	if err != nil && err != mongo.ErrNoDocuments {
		return order, errors.WithStack(err)
	}
	// 订单状态待确认响应报错
	if order.Status == 6 {
		return order, tools.BuildServerError(ctx, "subject_visit_dispensing_order_status")
	}

	// 查不到订单往下不执行执行
	if order.ID.IsZero() {
		return order, nil
	}
	orderUpdateStatus := 9
	orderUpdateSortIndex := 40
	if order.Status == 2 {
		orderUpdateStatus = 8
		orderUpdateSortIndex = 35
	}

	if len(order.OtherMedicinesNew) == len(updatePushIDs) && len(order.Medicines) == 0 {
		// 关闭订单
		_, err := tools.Database.Collection("medicine_order").UpdateOne(sctx,
			bson.M{"_id": order.ID},
			bson.M{"$set": bson.M{"status": orderUpdateStatus, "sort_index": orderUpdateSortIndex, "reason": reason, "canceller_at": now, "meta.updated_at": now}})
		if err != nil {
			return order, errors.WithStack(err)
		}
		// 写入订单轨迹
		_, err = tools.Database.Collection("history").InsertOne(sctx, models.History{
			Key:  key,
			OID:  order.ID,
			Data: map[string]interface{}{"orderNumber": order.OrderNumber},
			Time: now,
			UID:  user.ID,
			User: user.Name,
		})
		if err != nil {
			return order, errors.WithStack(err)
		}
	} else {
		// 订单拿掉对应的药物
		order.OtherMedicinesNew = slice.Filter(order.OtherMedicinesNew, func(index int, item primitive.ObjectID) bool {
			_, ok := slice.Find(updatePushIDs, func(index int, updateID primitive.ObjectID) bool {
				return item == updateID
			})
			return !ok
		})
		order.OtherMedicinesHistoryNew = slice.Filter(order.OtherMedicinesHistoryNew, func(index int, item primitive.ObjectID) bool {
			_, ok := slice.Find(updatePushIDs, func(index int, updateID primitive.ObjectID) bool {
				return item == updateID
			})
			return !ok
		})
		_, err := tools.Database.Collection("medicine_order").UpdateOne(sctx, bson.M{"_id": order.ID}, bson.M{"$set": bson.M{"other_medicines_new": order.OtherMedicinesNew, "other_medicines_history_new": order.OtherMedicinesHistoryNew}})
		if err != nil {
			return order, errors.WithStack(err)
		}
	}

	return order, nil

}

func updateRegisterOrder(ctx *gin.Context, sctx mongo.SessionContext, dispensingID primitive.ObjectID, medicineID primitive.ObjectID, status int, user models.User, now time.Duration) error {
	var order models.MedicineOrder
	key := "history.order.close-with-register"
	reason := `IP has been registered to be "available/frozen"`
	if status == 2 {
		reason = `IP has been registered to be "Lost/Void"`
		key = "history.order.close-with-register-lost"
	}
	err := tools.Database.Collection("medicine_order").FindOne(sctx, bson.M{"dispensing_id": dispensingID, "medicines": medicineID, "status": bson.M{"$nin": bson.A{3, 4, 5, 8, 9}}}).Decode(&order)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	// 订单状态待确认响应报错
	if order.Status == 6 {
		return tools.BuildServerError(ctx, "subject_visit_dispensing_order_status")
	}

	// 查不到订单往下不执行执行
	if order.ID.IsZero() {
		return nil
	}
	orderUpdateStatus := 9
	orderUpdateSortIndex := 40
	if order.Status == 2 {
		orderUpdateStatus = 8
		orderUpdateSortIndex = 35
	}
	if len(order.Medicines) == 1 && len(order.OtherMedicines) == 0 {
		// 关闭订单
		_, err := tools.Database.Collection("medicine_order").UpdateOne(sctx,
			bson.M{"_id": order.ID},
			bson.M{"$set": bson.M{"status": orderUpdateStatus, "sort_index": orderUpdateSortIndex, "reason": reason, "canceller_at": now, "meta.updated_at": now}})
		if err != nil {
			return errors.WithStack(err)
		}
		// 写入订单轨迹
		_, err = tools.Database.Collection("history").InsertOne(sctx, models.History{
			Key:  key,
			OID:  order.ID,
			Data: map[string]interface{}{"orderNumber": order.OrderNumber},
			Time: now,
			UID:  user.ID,
			User: user.Name,
		})
		if err != nil {
			return errors.WithStack(err)
		}
	} else {
		// 订单拿掉对应的药物
		_, err := tools.Database.Collection("medicine_order").UpdateOne(sctx, bson.M{"_id": order.ID}, bson.M{"$pull": bson.M{"medicines": medicineID}})
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil

}

func inDispensingCount(number int, dispensingNumber string) bool {
	numbers := customDispensingNumberToSelectOption(dispensingNumber)
	_, ok := slice.Find(numbers, func(index int, item int) bool {
		return item == number
	})
	return ok
}

func customerFormula(formula string, form map[string]float64) (float64, bool) {
	customerFormulas := []models.CustomerFormula{}
	for key, value := range form {
		customerFormulas = append(customerFormulas, models.CustomerFormula{
			Key:   "{" + key + "}",
			Value: value,
		})
	}
	isErr, res := tools.CustomMultipleFormulaExec(formula, customerFormulas)
	return res, isErr
}

func useWeight(weight, randomWeight, formulaWeight, lastWeight float64, value models.DrugValue, configure models.DrugConfigureInfo) (float64, float64, bool) {
	use := false
	//radio := 0.0
	comparisonRatio := *value.ComparisonRatio
	if configure.CalculationType == 2 && value.CalculationInfo.Precision != nil {
		weight = tools.TruncateDecimal(weight, *value.CalculationInfo.Precision, 1)
		randomWeight = tools.TruncateDecimal(randomWeight, *value.CalculationInfo.Precision, 1)
		formulaWeight = tools.TruncateDecimal(formulaWeight, *value.CalculationInfo.Precision, 1)
		lastWeight = tools.TruncateDecimal(lastWeight, *value.CalculationInfo.Precision, 1)
	}
	switch *value.CalculationInfo.ComparisonType {
	case 1:
		if formulaWeight > 0 {
			radio := math.Abs(weight-formulaWeight) / formulaWeight
			if useWeightFormulaType(radio, comparisonRatio, value.ComparisonSymbols) {
				weight = useWeightType(weight, randomWeight, formulaWeight, lastWeight, value)
				use = true
			}
		}
	case 2:
		if lastWeight > 0 {
			radio := math.Abs(weight-lastWeight) / lastWeight
			if useWeightFormulaType(radio, comparisonRatio, value.ComparisonSymbols) {
				weight = useWeightType(weight, randomWeight, formulaWeight, lastWeight, value)
				use = true
			}
		}

	case 3:
		if randomWeight > 0 {
			radio := math.Abs(weight-randomWeight) / randomWeight
			if useWeightFormulaType(radio, comparisonRatio, value.ComparisonSymbols) {
				weight = useWeightType(weight, randomWeight, formulaWeight, lastWeight, value)
				use = true
			}
		}

	}
	return weight, comparisonRatio, use
}

func useWeightFormulaType(radio float64, comparisonRatio float64, useType int) bool {
	switch useType {
	case 0:
		return radio > comparisonRatio/100
	case 1:
		return radio < comparisonRatio/100
	case 2:
		return radio <= comparisonRatio/100
	case 3:
		return radio >= comparisonRatio/100
	}
	return false
}

func useWeightType(weight, randomWeight, formulaWeight, lastWeight float64, value models.DrugValue) float64 {
	currentWeight := float64(0)
	switch *value.CalculationInfo.CurrentComparisonType {
	case 1:
		currentWeight = formulaWeight
	case 2:
		currentWeight = lastWeight
	case 3:
		currentWeight = randomWeight
	}
	if currentWeight != 0 {
		weight = currentWeight
	}
	return weight
}

func GetEmailSubjectReplaceText(lang string, attribute models.Attribute) string {
	subjectReplaceText := ""
	if lang == "zh" {
		subjectReplaceText = "受试者号"
		if len(attribute.AttributeInfo.SubjectReplaceText) != 0 {
			subjectReplaceText = attribute.AttributeInfo.SubjectReplaceText
		}
	} else if lang == "en" {
		subjectReplaceText = "Subject ID"
		if len(attribute.AttributeInfo.SubjectReplaceTextEn) != 0 {
			subjectReplaceText = attribute.AttributeInfo.SubjectReplaceTextEn
		} else {
			if len(attribute.AttributeInfo.SubjectReplaceText) != 0 {
				subjectReplaceText = attribute.AttributeInfo.SubjectReplaceText
			}
		}
	}

	return subjectReplaceText
}

func GetVisitDispensingTypeReplaceText(ctx *gin.Context, visitCycle models.VisitCycle) string {
	subjectReplaceText := locales.Tr(ctx, "history.dispensing.single.dispensingVisit")
	if locales.Lang(ctx) == "zh" {
		subjectReplaceText = visitCycle.SetInfo.NameZh
		//else {
		//	if len(attribute.AttributeInfo.SubjectReplaceTextEn) != 0 {
		//		subjectReplaceText = attribute.AttributeInfo.SubjectReplaceTextEn
		//	}
		//}
	} else if locales.Lang(ctx) == "en" {
		subjectReplaceText = visitCycle.SetInfo.NameEn

	}

	return subjectReplaceText
}

func GetSubjectReplaceText(ctx *gin.Context, attribute models.Attribute) string {
	subjectReplaceText := locales.Tr(ctx, "subject.number")
	if locales.Lang(ctx) == "zh" {
		if len(attribute.AttributeInfo.SubjectReplaceText) != 0 {
			subjectReplaceText = attribute.AttributeInfo.SubjectReplaceText
		}
		//else {
		//	if len(attribute.AttributeInfo.SubjectReplaceTextEn) != 0 {
		//		subjectReplaceText = attribute.AttributeInfo.SubjectReplaceTextEn
		//	}
		//}
	} else if locales.Lang(ctx) == "en" {
		if len(attribute.AttributeInfo.SubjectReplaceTextEn) != 0 {
			subjectReplaceText = attribute.AttributeInfo.SubjectReplaceTextEn
		} else {
			if len(attribute.AttributeInfo.SubjectReplaceText) != 0 {
				subjectReplaceText = attribute.AttributeInfo.SubjectReplaceText
			}
		}
	}

	return subjectReplaceText
}

func handlePeriod(afterRandom bool, visitType int, info models.VisitCycleInfo, baseTime time.Duration, lastTime time.Duration, currentTime time.Duration, timeZoneInt float64, interval *float64, attribute models.Attribute, joinTime string) models.Period {
	var period models.Period
	// 随机发药项目
	if (info.Interval == nil) && attribute.AttributeInfo.Random && visitType == 0 {
		return period

	}
	if visitType == 0 && !attribute.AttributeInfo.Random && joinTime == "" {
		return period
	}
	if info.Interval == nil {
		return period
	}
	Unit, Interval, PeriodMin := tools.ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMin)
	_, _, PeriodMax := tools.ReturnUnitIntervalPeriod(info.Unit, info.Interval, info.PeriodMax)
	converTimes := tools.ConvertTime(Unit, Interval, 0)
	periodMin := tools.ConvertTime(Unit, Interval, PeriodMin)
	periodMax := tools.ConvertTime(Unit, Interval, PeriodMax)
	if (visitType == 0 && (baseTime != 0 || joinTime != "")) || (visitType == 1 && lastTime != 0) {
		// 计算最小和最大时间
		var minTime, maxTime, lineTime time.Time
		//timeZone := time.Duration(timeZoneInt)

		hours := time.Duration(timeZoneInt)
		minutes := time.Duration((timeZoneInt - float64(hours)) * 60)

		duration := hours*time.Hour + minutes*time.Minute

		if visitType == 0 { //基准日期
			// TODO 仅发药并且配置了入组时间
			if !attribute.AttributeInfo.Random && joinTime != "" {
				parse, err := time.Parse("2006-01-02", joinTime)
				if err != nil {
					return period
				}
				lineTime = parse.Add(time.Hour * time.Duration(converTimes))
				minTime = parse.Add(time.Hour * time.Duration(periodMin))
				maxTime = parse.Add(time.Hour * time.Duration(periodMax))
			} else if baseTime != 0 {
				lineTime = time.Unix(int64(baseTime), 0).Add(time.Hour * time.Duration(converTimes)).Add(duration)
				minTime = time.Unix(int64(baseTime), 0).Add(time.Hour * time.Duration(periodMin)).Add(duration)
				maxTime = time.Unix(int64(baseTime), 0).Add(time.Hour * time.Duration(periodMax)).Add(duration)
			}

		} else { //上一次发药日期
			lineTime = time.Unix(int64(lastTime), 0).Add(time.Hour * time.Duration(*interval)).Add(time.Hour * time.Duration(converTimes)).Add(duration)
			minTime = time.Unix(int64(lastTime), 0).Add(time.Hour * time.Duration(*interval)).Add(time.Hour * time.Duration(periodMin)).Add(duration)
			maxTime = time.Unix(int64(lastTime), 0).Add(time.Hour * time.Duration(*interval)).Add(time.Hour * time.Duration(periodMax)).Add(duration)
		}

		// 判断是否超出时间范围
		//if (currentTime < time.Duration(minTime.Unix()) || currentTime > time.Duration(maxTime.Unix())) && currentTime != 0 {
		//	period.OutSize = true
		//}
		// 格式化时间
		period.MinPeriod = minTime.UTC().Format("2006-01-02")
		period.MaxPeriod = maxTime.UTC().Format("2006-01-02")
		period.LineTime = lineTime.UTC().Format("2006-01-02")
		period.MaximumTime = maxTime.UTC().Unix()

		current := time.Unix(int64(currentTime), 0).UTC().Add(duration).UTC().Format("2006-01-02")
		if (current < period.MinPeriod || current > period.MaxPeriod) && currentTime != 0 {
			period.OutSize = true
			period.OutSizeWindow = true
		}

		if currentTime == 0 {
			now := time.Now().UTC().Add(duration).UTC().Format("2006-01-02")
			if now > period.MaxPeriod {
				period.OutSize = true
			}
			if now < period.MinPeriod || now > period.MaxPeriod {
				period.OutSizeWindow = true
			}
		}
	}

	// 间隔时间计算
	*interval = *interval + tools.ConvertTime(Unit, Interval, 0)

	return period
}

func checkDispensingAlarm(ctx *gin.Context, sctx mongo.SessionContext, name string, filter bson.M, number int64, dispensingAlarmNumber map[string]int32) error {
	// 查询包括订单的确认 运送的数量
	if dispensingAlarmNumber[name] == 0 {
		return nil
	}
	filter["status"] = bson.M{"$in": bson.A{1, 14}}
	total, err := tools.Database.Collection("medicine").CountDocuments(sctx, filter)
	if err != nil {
		return nil
	}
	if total < number {
		if filter["site_id"] != nil {
			return tools.BuildServerError(ctx, "subject_medicine_count")
		} else {
			return tools.BuildServerError(ctx, "subject_medicine_count_store")
		}
	}

	if total-number < int64(dispensingAlarmNumber[name]) { // 发药后 数量低于发药警戒值
		if filter["site_id"] != nil {
			return tools.BuildServerError(ctx, "subject_medicine_count")
		} else {
			return tools.BuildServerError(ctx, "subject_medicine_count_store")
		}
	}
	filter["status"] = bson.M{"$in": bson.A{1, 14}}
	return nil
}

func checkDispensingOthersAlarm(ctx *gin.Context, sctx mongo.SessionContext, name string, filter bson.M, number int64, dispensingAlarmNumber map[string]int32) error {
	// 查询包括订单的确认 运送的数量
	if dispensingAlarmNumber[name] == 0 {
		return nil
	}
	filter["status"] = 1
	total, err := tools.Database.Collection("medicine_others").CountDocuments(sctx, filter)
	if err != nil {
		return nil
	}
	if total < number {
		if filter["site_id"] != nil {
			return tools.BuildServerError(ctx, "subject_medicine_count")
		} else {
			return tools.BuildServerError(ctx, "subject_medicine_count_store")
		}
	}

	if total-number < int64(dispensingAlarmNumber[name]) { // 发药后 数量低于发药警戒值
		if filter["site_id"] != nil {
			return tools.BuildServerError(ctx, "subject_medicine_count")
		} else {
			return tools.BuildServerError(ctx, "subject_medicine_count_store")
		}
	}
	filter["status"] = bson.M{"$in": bson.A{1, 14}}
	return nil
}

func checkDispensingOtherAlarm(ctx *gin.Context, medicineOtherInstitutes []models.MedicineOtherInstitute, count int64, alarmCount int32) error {
	siteCount := 0
	isSite := true
	for _, institute := range medicineOtherInstitutes {
		if institute.InstituteID.IsZero() {
			isSite = false
		}
		siteCount = siteCount + institute.Info.Count
	}
	if siteCount < int(count) {
		if isSite {
			return tools.BuildServerError(ctx, "subject_medicine_count")
		} else {
			return tools.BuildServerError(ctx, "subject_medicine_count_store")
		}

	}
	if siteCount-int(count) < int(alarmCount) {
		if isSite {
			return tools.BuildServerError(ctx, "subject_medicine_count")
		} else {
			return tools.BuildServerError(ctx, "subject_medicine_count_store")
		}
	}
	return nil
}

func updateForm(sctx mongo.SessionContext, envID, cohortID primitive.ObjectID, form []models.CustomerFormula, value *models.FormValue) error {
	IDs := []string{}
	for _, formula := range form {
		IDs = append(IDs, formula.Key)
	}
	if value != nil {
		IDs = append(IDs, value.Key)
	}
	if len(IDs) > 0 {
		_, err := tools.Database.Collection("form").UpdateOne(sctx,
			bson.M{"env_id": envID, "cohort_id": cohortID, "variable": bson.M{"$in": IDs}, "used": false},
			bson.M{"$set": bson.M{"used": true}},
		)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func setCustomerDispensingHistory(ctx, info map[string]interface{}) {
	keyIndex := []string{
		"randomNumber",
		"visit",
		"height",
		"form",
	}
	history := models.History{}
	for _, key := range keyIndex {
		if info[key] != nil && info[key] != "" {
			history.CustomTemps = append(history.CustomTemps, models.CustomTemp{
				ParKey:              "data",
				ConnectingSymbolKey: "comma",
				LastSymbolKey:       "comma",
				CustomTempOptions:   append([]models.CustomTempOption{}, models.CustomTempOption{}),
			})
		}
	}

}

func AllocationRooms(sctx mongo.SessionContext, subject models.Subject, roomNumbers []string) (string, error) {
	room := ""
	if subject.RoomNumber == "" && len(roomNumbers) != 0 {
		roomPipeline := mongo.Pipeline{
			{{Key: "$match", Value: bson.M{
				"project_site_id": subject.ProjectSiteID,
				"env_id":          subject.EnvironmentID,
				"room_number":     bson.M{"$in": roomNumbers},
				"deleted":         bson.M{"$ne": true}}}},
			{{Key: "$group", Value: bson.M{"_id": "$room_number", "count": bson.M{"$sum": 1}}}},
			{{Key: "$sort", Value: bson.D{{"count", 1}, {"_id", 1}}}},
		}
		var roomSubject []map[string]interface{}
		roomCursor, err := tools.Database.Collection("subject").Aggregate(sctx, roomPipeline)
		if err != nil {
			return "", errors.WithStack(err)
		}
		err = roomCursor.All(sctx, &roomSubject)
		if err != nil {
			return "", errors.WithStack(err)
		}
		if len(roomSubject) == 0 { //中心未分配房间号
			room = roomNumbers[0]
		} else {
			if len(roomSubject) == len(roomNumbers) { // 所有房间号已分配 选择最小的
				room = roomSubject[0]["_id"].(string)
			} else { // 部分房间号未分配 按顺序选择未选的房间号

				roomNumbersMap := map[string]bool{}
				for _, number := range roomNumbers {
					roomNumbersMap[number] = true
				}

				for _, item := range roomSubject {
					if _, ok := roomNumbersMap[item["_id"].(string)]; ok {
						delete(roomNumbersMap, item["_id"].(string))
					}
				}
				var numbers []string
				for key := range roomNumbersMap {
					numbers = append(numbers, key)
				}
				sort.Sort(sort.StringSlice(numbers))
				room = numbers[0]
			}
		}
		update := bson.M{
			"$set": bson.M{
				"room_number": room,
			}}
		_, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": subject.ID}, update)
		if err != nil {
			return "", errors.WithStack(err)
		}
	}
	return room, nil
}

func getVisitGroupLabel(ctx *gin.Context, visitID primitive.ObjectID, subject models.Subject, labels []string) ([]models.LabelValue, []models.LabelValue, []models.LabelValue, error) {
	resLabel := []models.LabelValue{}
	resName := []models.LabelValue{}
	resFormulaName := []models.LabelValue{}

	var drugConfigure models.DrugConfigure
	err := tools.Database.Collection("drug_configure").FindOne(nil, bson.M{
		"env_id":    subject.EnvironmentID,
		"cohort_id": subject.CohortID,
	}).Decode(&drugConfigure)
	if err != nil {
		return nil, nil, nil, errors.WithStack(err)
	}

	for _, label := range labels {
		var data map[string]interface{}
		err := json.Unmarshal([]byte(label), &data)
		if err != nil {
			return nil, nil, nil, errors.WithStack(err)
		}
		for _, configure := range drugConfigure.Configures {
			if configure.ID.Hex() == data["id"].(string) && subject.Group == configure.Group {
				if configure.Label == data["name"].(string) {
					resLabel = append(resLabel, models.LabelValue{
						ID:    configure.ID,
						Label: configure.Label,
					})
					break
				}
				for _, info := range configure.Values {
					if configure.OpenSetting == 1 && info.Label == data["name"].(string) {
						resLabel = append(resLabel, models.LabelValue{
							ID:    configure.ID,
							Label: info.Label,
							Value: info.DispensingNumber,
						})
					}
					if configure.OpenSetting == 2 &&
						info.DrugName+"/"+convertor.ToString(info.CustomDispensingNumber)+"/"+info.DrugSpec == data["name"].(string) {
						if len(customDispensingNumberToSelectOption(info.CustomDispensingNumber)) > 1 {
							resName = append(resName, models.LabelValue{
								ID:    configure.ID,
								Label: info.DrugName,
							})
						} else {
							resName = append(resName, models.LabelValue{
								ID:    configure.ID,
								Label: info.DrugName,
								Value: info.DispensingNumber,
							})
						}

					}

					if configure.OpenSetting == 3 &&
						info.DrugName+"//"+info.DrugSpec == data["name"].(string) {
						resFormulaName = append(resFormulaName, models.LabelValue{
							ID:    configure.ID,
							Label: info.DrugName,
						})

					}
				}
			}
		}
	}

	return resLabel, resName, resFormulaName, nil
}

// getCurrentDose 获取当前选择的水平
func getCurrentDose(ctx *gin.Context, subject models.Subject, value string) (models.Dispensing, models.DoseLevel, models.DoseLevel, models.VisitJudgment, int, error) {
	var lastDoseLevel models.DoseLevel
	var doseLevel models.DoseLevel
	var lastDoseDispensing models.Dispensing
	doseType := -1
	var visitJudgment models.VisitJudgment
	var drugConfigureSetting models.DrugConfigureSetting
	err := tools.Database.Collection("drug_configure_setting").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&drugConfigureSetting)
	if err != nil {
		return lastDoseDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, errors.WithStack(err)
	}
	// 查询受试者信息
	var dispensings []models.Dispensing
	cursor, err := tools.Database.Collection("dispensing").Find(nil, bson.M{"subject_id": subject.ID, "status": 2}, &options.FindOptions{
		Sort: bson.D{
			{"serial_number", 1},
		},
	})
	if err != nil {
		return lastDoseDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensings)
	if err != nil {
		return lastDoseDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, errors.WithStack(err)
	}

	// 查询表单信息
	var form models.Form
	err = tools.Database.Collection("form").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&form)
	if err != nil {
		return lastDoseDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, errors.WithStack(err)
	}

	formItemP, ok := slice.Find(form.Fields, func(index int, item models.Field) bool {
		return item.ID.Hex() == drugConfigureSetting.DoseFormId && *item.Status == 1
	})
	if !ok {
		return lastDoseDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, errors.WithStack(err)
	}
	formItem := *formItemP
	tmpSubjectGroup := "N/A"
	if subject.Group != "" {
		tmpSubjectGroup = subject.Group
	}
	if drugConfigureSetting.SelectType == 1 { // 剂量调整

		//过滤当前组别
		doseLevelListGroup := slice.Filter(drugConfigureSetting.DoseLevelList, func(index int, item models.DoseLevel) bool {
			_, matchGroup := slice.Find(item.Group, func(index int, group string) bool {
				return group == tmpSubjectGroup
			})
			return matchGroup
		})

		// 查上一次使用的剂量
		doseLevelListLastP, lastOk := slice.FindLast(dispensings, func(index int, item models.Dispensing) bool {
			return item.DoseInfo != nil && item.DoseInfo.DoseLevelList != nil && !item.DoseInfo.DoseLevelList.ID.IsZero()
		})
		if lastOk {
			doseLevelListLast := *doseLevelListLastP
			doseLevelListP, lastDoseOk := slice.Find(doseLevelListGroup, func(index int, item models.DoseLevel) bool {
				return doseLevelListLast.DoseInfo.DoseLevelList.ID == item.ID
			})
			if lastDoseOk {
				lastDoseLevel = *doseLevelListP
				lastDoseDispensing = doseLevelListLast
			}
		}

		optionP, optionOk := slice.Find(formItem.Options, func(index int, item models.Option) bool {
			return value == item.Value
		})

		if optionOk {
			option := *optionP
			switch option.Label {
			case "form.control.type.options.one": // 初始剂量
				doseLevelList, ok := slice.Find(drugConfigureSetting.DoseLevelList, func(index int, item models.DoseLevel) bool {
					_, matchGroup := slice.Find(item.Group, func(index int, group string) bool {
						return group == tmpSubjectGroup
					})
					return matchGroup && item.InitialDose
				})
				if !ok {
					return lastDoseDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, tools.BuildServerError(ctx, "subject_medicine_dose_error")
				}
				doseLevel = *doseLevelList
				doseType = 0

			case "form.control.type.options.two": // 维持上一次
				// 查得到上一次
				if !lastDoseLevel.ID.IsZero() {
					doseLevel = lastDoseLevel
				} else {
					return lastDoseDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, tools.BuildServerError(ctx, "subject_medicine_dose_error")

				}
				doseType = 1

			case "form.control.type.options.three": // 下降一个水平
				// 查得到上一次
				if lastDoseLevel.ID.IsZero() {
					return lastDoseDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, tools.BuildServerError(ctx, "subject_medicine_dose_error")
				}
				i := -1
				slice.ForEach(doseLevelListGroup, func(index int, item models.DoseLevel) {
					if lastDoseLevel.ID == item.ID {
						i = index
					}
				})
				// 上一次剂量水平查不到 或者是最低的了
				if !ok || i <= 0 {
					return lastDoseDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, tools.BuildServerError(ctx, "subject_medicine_dose_error")
				}
				//  doseLevelListGroup[i-1] 下降一个水平取对应的标签 药物名称
				doseLevel = doseLevelListGroup[i-1]
				doseType = 2
			}
		}
	} else {
		visitJudgmentP, ok := slice.Find(drugConfigureSetting.VisitJudgmentList, func(index int, item models.VisitJudgment) bool {
			return item.Name == value
		})
		if !ok {
			return lastDoseDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, tools.BuildServerError(ctx, "subject_medicine_dose_error")
		}
		visitJudgment = *visitJudgmentP
	}
	return lastDoseDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, nil
}

func updateLabelNameCounts(lastDispensing models.Dispensing, name string) int {
	countTmp := slice.Count(lastDispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
		return item.Name == name
	})
	if countTmp > 0 {
		return countTmp
	}
	otherDispensingMedicines := slice.Filter(lastDispensing.OtherDispensingMedicines, func(index int, item models.OtherDispensingMedicine) bool {
		return item.Name == name
	})
	for _, dispensingMedicine := range otherDispensingMedicines {
		countTmp = countTmp + dispensingMedicine.Count
	}
	if countTmp > 0 {
		return countTmp
	}
	return 0
}

func addDispensingHistoryData(
	ctx *gin.Context,
	key string,
	histories *[]models.History,
	subjectReplaceText string,
	subject models.Subject,
	dispensing models.Dispensing,
	formulaInfo models.FormulaInfo,
	user models.User,
	userName string,
	now time.Duration,
	sendType int,
	customerFormulas []models.CustomerFormula,
	form models.Form,
	formValue models.FormValue,
	logisticsInfo models.LogisticsInfo,
	data map[string]interface{},
	attribute models.Attribute,
) error {
	// 发药轨迹 判断是否是EDC对接项目
	//
	const (
		dispensingCustomer = iota + 1
		randomNumber
		visit
		formInfo
		height
		weight
		date
		dose
		initLevel
		medicine
		otherMedicine
		order
		initSendType
		vendor
		outSize
		reason
		reasonDispensingVisit
		reasonReplace
		replaceDrug
		beReplaceDrug
		systemDrug
		realDrug
		retrievalDrug
		remark
	)

	historyData := bson.M{
		"label":   subjectReplaceText,
		"subject": subject.Info[0].Value,
	}

	customTempOption := []models.CustomTempOption{}

	reasonKey := "history.dispensing.single.reason"
	if key == "history.dispensing.dispensingCustomer-dispensingVisit" {
		reasonKey = "history.dispensing.single.reasonDispensingVisit"
	}
	if key == "history.dispensing.dispensingCustomer-reissue" {
		reasonKey = "history.dispensing.single.reasonReissue"
	}
	if key == "history.dispensing.dispensingCustomer-replace" {
		reasonKey = "history.dispensing.single.reasonReplace"
	}
	if key == "history.dispensing.dispensingCustomer-dispensingVisitCustomer" {
		reasonKey = "history.dispensing.single.reasonDispensingVisitCustomer"
	}

	replaceOrRetrievalOrRegister := key == "history.dispensing.dispensingCustomer-replace" || key == "history.dispensing.dispensingCustomer-retrieval" || key == "history.dispensing.dispensingCustomer-register"
	if !replaceOrRetrievalOrRegister {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: randomNumber,
			Key:   "history.dispensing.single.randomNumber",
			Data:  bson.M{"randomNumber": subject.RandomNumber},
		})

		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: visit,
			Key:   "history.dispensing.single.visit",
			Data:  bson.M{"visit": dispensing.VisitInfo.Name},
		})
	}

	if formulaInfo.Age != nil {
		historyData["date"] = formulaInfo.Age
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: date,
			Key:   "history.dispensing.single.date",
			Data:  bson.M{"date": formulaInfo.Age},
		})
	}
	if formulaInfo.Height != nil {
		historyData["height"] = formulaInfo.Height
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: height,
			Key:   "history.dispensing.single.height",
			Data:  bson.M{"height": formulaInfo.Height},
		})
	}
	if formulaInfo.Weight != nil {
		historyData["weight"] = formulaInfo.Weight
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: weight,
			Key:   "history.dispensing.single.weight",
			Data:  bson.M{"weight": formulaInfo.Weight},
		})
	}

	{ //{{.form}}
		customerFormulasArr := []string{}

		for _, formula := range customerFormulas {
			fieldP, ok := slice.Find(form.Fields, func(index int, item models.Field) bool {
				return item.Variable == formula.Key
			})
			if ok {
				field := *fieldP
				strValue := convertor.ToString(formula.Value)
				if field.Type == "inputNumber" && field.FormatType == "decimalLength" && field.Length != nil && formula.Value != 0 {
					lengthString := strconv.FormatFloat(*field.Length, 'f', -1, 64)
					if strings.Contains(lengthString, ".") {
						digits, _ := getFractionDigits(*field.Length)
						str := strconv.FormatFloat(formula.Value, 'f', digits, 64)
						strValue = str
					}
				}
				customerFormulasArr = append(customerFormulasArr, field.Label+":"+strValue)
			}
		}
		if len(customerFormulasArr) != 0 {
			customTempOption = append(customTempOption, models.CustomTempOption{
				Index: formInfo,
				Key:   "history.dispensing.single.form",
				Data:  bson.M{"form": strings.Join(customerFormulasArr, ",")},
			})
		}
	}

	if dispensing.DoseInfo != nil && dispensing.DoseInfo.DoseLevelList != nil {
		fieldP, ok := slice.Find(form.Fields, func(index int, item models.Field) bool {
			return item.Variable == formValue.Key
		})
		if ok {
			field := *fieldP
			optionP, optionOk := slice.Find(field.Options, func(index int, item models.Option) bool {
				return item.Value == formValue.Value
			})
			if optionOk {
				option := optionP
				customTempOption = append(customTempOption, models.CustomTempOption{
					Index:     dose,
					Key:       field.Label,
					TransData: option.Label,
					TransType: models.UnKeyData,
				})
			}
		}
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: initLevel,
			Key:   "history.dispensing.single.level",
			Data:  bson.M{"level": dispensing.DoseInfo.DoseLevelList.Name},
		})
	}

	if dispensing.DoseInfo != nil && dispensing.DoseInfo.VisitJudgmentList != nil {
		fieldP, ok := slice.Find(form.Fields, func(index int, item models.Field) bool {
			return item.Variable == formValue.Key
		})
		if ok {
			field := *fieldP
			optionP, optionOk := slice.Find(field.Options, func(index int, item models.Option) bool {
				return item.Value == formValue.Value
			})
			if optionOk {
				option := optionP
				transType := models.UnKeyUnData
				if option.Label == "form.control.type.options.one" || option.Label == "form.control.type.options.two" || option.Label == "form.control.type.options.three" {
					transType = models.UnKeyData
				}

				customTempOption = append(customTempOption, models.CustomTempOption{
					Index:     initLevel,
					Key:       field.Label,
					TransData: option.Label,
					TransType: transType,
				})
			}
		}
	}

	if len(dispensing.DispensingMedicines) > 0 {
		//medicineGroup := slice.GroupWith(dispensing.DispensingMedicines, func(item models.DispensingMedicine) string {
		//	return item.Label
		//})

		err := slice.SortByField(dispensing.DispensingMedicines, "Number")
		dispensing.DispensingMedicines = sortTypeMedicineNumber(dispensing.DispensingMedicines)
		if err != nil {
			return errors.WithStack(err)
		}
		labelMedicines := dispensingHistoryMedicineLabel(dispensing.DispensingMedicines)
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: medicine,
			Key:   "history.dispensing.single.medicine",
			Data:  bson.M{"medicine": strings.Join(labelMedicines, ",")},
		})
	}
	if len(dispensing.OtherDispensingMedicines) > 0 {
		medicineGroup := slice.GroupWith(dispensing.OtherDispensingMedicines, func(item models.OtherDispensingMedicine) string {
			return item.Label
		})
		var labelMedicines []string
		for label, medicines := range medicineGroup {
			err := slice.SortByField(medicines, "Name")
			if err != nil {
				return errors.WithStack(err)
			}

			medicineNumber := slice.Map(medicines, func(index int, item models.OtherDispensingMedicine) string {
				if item.Batch == "" {
					item.Batch = "-"
				}
				if item.ExpireDate == "" {
					item.ExpireDate = "-"
				}
				return "[" + item.Name + "/" + convertor.ToString(item.Count) + "/" + item.Batch + "/" + item.ExpireDate + "]"
			})
			labelMedicines = append(labelMedicines, label+strings.Join(medicineNumber, ""))

		}

		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: otherMedicine,
			Key:   "history.dispensing.single.otherMedicine",
			Data:  bson.M{"medicine": strings.Join(labelMedicines, ",")},
		})
	}
	if sendType != 0 {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: order,
			Key:   "history.dispensing.single.order",
			Data:  bson.M{"order": dispensing.Order},
		})
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index:     initSendType,
			Key:       "history.dispensing.single.sendType",
			TransData: "history.dispensing.send-type-" + convertor.ToString(sendType),
			TransType: models.KeyData,
		})
		//var logisticsCompanyCode models.LogisticsCompanyCode
		//err := tools.Database.Collection("logistics_company_code").FindOne(nil, bson.M{"code": logisticsInfo.Logistics}).Decode(&logisticsCompanyCode)
		//if err != nil && err != mongo.ErrNoDocuments {
		//	return errors.WithStack(err)
		//}
		//customTempOption = append(customTempOption, models.CustomTempOption{
		//	Index: vendor,
		//	Key:   "history.dispensing.single.vendor",
		//	Data:  bson.M{"vendor": logisticsCompanyCode.Name, "number": logisticsInfo.Number},
		//})
	}

	if data["out_size"] != nil && data["out_size"].(bool) {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: outSize,
			Key:   "history.dispensing.single.outSize",
		})
	}
	if data["reason"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: reason,
			Key:   reasonKey,
			Data:  bson.M{"reasonDispensingVisit": data["reason"]},
		})
	}
	if data["remark"] != nil && data["remark"] != "" {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: remark,
			Key:   "history.dispensing.single.remark",
			Data:  bson.M{"remark": data["remark"]},
		})
	}

	if data["beReplaceMedicine"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: beReplaceDrug,
			Key:   "history.dispensing.single.beReplaceMedicine",
			Data:  bson.M{"beReplaceMedicine": data["beReplaceMedicine"]},
		})
	}
	if data["replaceNumber"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: replaceDrug,
			Key:   "history.dispensing.single.replaceNumber",
			Data:  bson.M{"replaceNumber": data["replaceNumber"]},
		})
	}
	if data["systemMedicine"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: systemDrug,
			Key:   "history.dispensing.single.systemMedicine",
			Data:  bson.M{"systemMedicine": data["systemMedicine"]},
		})
	}
	if data["realMedicine"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: realDrug,
			Key:   "history.dispensing.single.realMedicine",
			Data:  bson.M{"realMedicine": data["realMedicine"]},
		})
	}
	if data["retrievalMedicine"] != nil {
		customTempOption = append(customTempOption, models.CustomTempOption{
			Index: retrievalDrug,
			Key:   "history.dispensing.single.retrievalMedicine",
			Data:  bson.M{"retrievalMedicine": data["retrievalMedicine"]},
		})
	}

	customTemps := []models.CustomTemp{}
	showCustomTemps := []models.CustomTemp{}
	if len(customTempOption) > 0 {
		customTemps = append(customTemps, models.CustomTemp{
			ParKey:              "data",
			ConnectingSymbolKey: "history.dispensing.single.comma",
			LastSymbolKey:       "history.dispensing.single.period",
			CustomTempOptions:   customTempOption,
		})
	} else {
		historyData["data"] = ""
	}

	// TableInfo
	history := models.History{
		Key:         key,
		OID:         dispensing.ID,
		Data:        historyData,
		Time:        now,
		CustomTemps: customTemps,
		UID:         user.ID,
		User:        userName,
	}
	if (key == "history.dispensing.dispensingCustomer-dispensing" || key == "history.dispensing.dispensingCustomer-dispensingVisitCustomer" || key == "history.dispensing.dispensingCustomer-reissue") &&
		attribute.AttributeInfo.DtpRule == 1 {
		remarkInfo := ""
		if data["remark"] != nil {
			remarkInfo = data["remark"].(string)
		}
		showCustomTempOption := slice.Filter(customTempOption, func(index int, item models.CustomTempOption) bool {
			return !(item.Index == medicine || item.Index == otherMedicine)
		})
		showCustomTemps = append(showCustomTemps, models.CustomTemp{
			ParKey:              "data",
			ConnectingSymbolKey: "history.dispensing.single.comma",
			LastSymbolKey:       "history.dispensing.single.period",
			CustomTempOptions:   showCustomTempOption,
		})
		tableInfo := models.TableInfo{}
		tableInfo.Title = append(tableInfo.Title, []string{
			"report.attributes.research",
			"report.attributes.dispensing.send.type",
			"operation_log.project.descriptions",
		}...)
		medicineGroup := slice.GroupWith(dispensing.DispensingMedicines, func(item models.DispensingMedicine) int {
			dtp := 0
			if item.DTP != nil {
				dtp = *item.DTP
			}

			return dtp
		})
		otherMedicineGroup := slice.GroupWith(dispensing.OtherDispensingMedicines, func(item models.OtherDispensingMedicine) int {
			dtp := 0
			if item.DTP != nil {
				dtp = *item.DTP
			}

			return dtp
		})
		for value, item := range medicineGroup {
			tmpCol := []models.CustomTempOption{}
			numbers := slice.Map(item, func(index int, item models.DispensingMedicine) string {
				return item.Number
			})
			tmpCol = append(tmpCol, models.CustomTempOption{
				Index: 1,
				Key:   "history.dispensing.single.noKey",
				Data:  bson.M{"data": numbers},
			})

			tmpCol = append(tmpCol, models.CustomTempOption{
				Index:     2,
				Key:       "history.dispensing.single.noKey",
				TransData: "history.dispensing.send-type-" + convertor.ToString(value),
				TransType: models.KeyData,
			})

			tmpCol = append(tmpCol, models.CustomTempOption{
				Index: 3,
				Key:   "history.dispensing.single.noKey",
				Data:  bson.M{"data": remarkInfo},
			})
			tableInfo.Value = append(tableInfo.Value, tmpCol)
		}
		for value, item := range otherMedicineGroup {
			tmpCol := []models.CustomTempOption{}
			numbers := slice.Map(item, func(index int, item models.OtherDispensingMedicine) string {
				if item.Batch == "" {
					item.Batch = "-"
				}
				if item.ExpireDate == "" {
					item.ExpireDate = "-"
				}
				return item.Name + "/" + item.Batch + "/" + item.ExpireDate + "/" + convertor.ToString(item.Count)
			})
			tmpCol = append(tmpCol, models.CustomTempOption{
				Index: 1,
				Key:   "history.dispensing.single.noKey",
				Data:  bson.M{"data": numbers},
			})

			tmpCol = append(tmpCol, models.CustomTempOption{
				Index:     2,
				Key:       "history.dispensing.single.noKey",
				TransData: "history.dispensing.send-type-" + convertor.ToString(value),
				TransType: models.KeyData,
			})
			tmpCol = append(tmpCol, models.CustomTempOption{
				Index: 3,
				Key:   "history.dispensing.single.noKey",
				Data:  bson.M{"data": remarkInfo},
			})
			tableInfo.Value = append(tableInfo.Value, tmpCol)
		}
		history.TableInfo = &tableInfo
		history.ShowCustomTemps = &showCustomTemps
	}

	// 发药、计划外发药处理

	*histories = append(*histories, history)
	ctx.Set("HISTORY", *histories)
	return nil
}

// GetComparisonSymbols ..
func GetComparisonSymbols(ctx *gin.Context, key int) string {
	switch key {
	case 0:
		return ">"
	case 1:
		return "<="
	case 2:
		return ">="
	case 3:
		return "<"
	}
	return ""
}

func UpdateDispensingCohortStatus(sctx mongo.SessionContext, attribute models.Attribute, dispensing models.Dispensing, project models.Project) error {

	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return dispensing.EnvironmentID == item.ID
	})
	env := *envP
	if project.ProjectInfo.Type == 2 || project.ProjectInfo.Type == 3 {
		cohortP, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
			return dispensing.CohortID == item.ID
		})
		cohort := *cohortP
		count, err := tools.CountSubject(sctx, attribute, bson.M{"env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID})
		if err != nil {
			return errors.WithStack(err)
		}
		find, b := slice.Find(cohort.AlertThresholds, func(index int, item models.AlertThreshold) bool {
			return item.Type == 3
		})
		opts := &options.UpdateOptions{
			ArrayFilters: &options.ArrayFilters{
				Filters: bson.A{bson.M{"env.id": dispensing.EnvironmentID}, bson.M{"cohort.id": dispensing.CohortID}},
			},
		}
		if b && count >= find.Capacity {
			_, err = tools.Database.Collection("project").UpdateOne(sctx, bson.M{"_id": project.ID},
				bson.M{"$set": bson.M{"envs.$[env].cohorts.$[cohort].status": 5}}, opts)
			if err != nil {
				return errors.WithStack(err)
			}
		}
	} else {
		count, err := tools.CountSubject(sctx, attribute, bson.M{"env_id": dispensing.EnvironmentID})
		if err != nil {
			return errors.WithStack(err)
		}
		find, b := slice.Find(env.AlertThresholds, func(index int, item models.AlertThreshold) bool {
			return item.Type == 3
		})
		opts := &options.UpdateOptions{
			ArrayFilters: &options.ArrayFilters{
				Filters: bson.A{bson.M{"env.id": dispensing.EnvironmentID}},
			},
		}
		if b && count >= find.Capacity {
			_, err = tools.Database.Collection("project").UpdateOne(sctx, bson.M{"_id": project.ID},
				bson.M{"$set": bson.M{"envs.$[env].status": 5}}, opts)
			if err != nil {
				return errors.WithStack(err)
			}

		}
	}
	return nil
}

func dispensingHistoryMedicineLabel(medicine []models.DispensingMedicine) []string {
	type LabelNumber struct {
		Label  string
		Number []string
	}
	res := []LabelNumber{}
	for _, item := range medicine {
		if len(res) == 0 {
			res = append(res, LabelNumber{
				Label:  item.Label,
				Number: []string{item.Number},
			})
		} else {
			if res[len(res)-1].Label == item.Label {
				res[len(res)-1].Number = append(res[len(res)-1].Number, item.Number)
			} else {
				res = append(res, LabelNumber{
					Label:  item.Label,
					Number: []string{item.Number},
				})
			}
		}
	}
	data := []string{}
	for _, item := range res {
		medicineNumber := slice.Map(item.Number, func(index int, item string) string {
			return "[" + item + "]"
		})
		data = append(data, item.Label+strings.Join(medicineNumber, ""))

	}
	return data
}

func sortTypeMedicineNumber(medicines []models.DispensingMedicine) []models.DispensingMedicine {
	_, ok := slice.Find(medicines, func(index int, item models.DispensingMedicine) bool {
		return item.OpenSetting != 0
	})
	if ok {
		medicineNumber := []models.DispensingMedicine{}
		labelMedicine := slice.Filter(medicines, func(index int, item models.DispensingMedicine) bool {
			return item.OpenSetting == 1
		})
		slice.SortByField(labelMedicine, "Number")
		medicineNumber = append(medicineNumber, labelMedicine...)
		openMedicine := slice.Filter(medicines, func(index int, item models.DispensingMedicine) bool {
			return item.OpenSetting == 2 || item.OpenSetting == 0
		})
		slice.SortByField(openMedicine, "Number")
		medicineNumber = append(medicineNumber, openMedicine...)
		formulaMedicine := slice.Filter(medicines, func(index int, item models.DispensingMedicine) bool {
			return item.OpenSetting == 3
		})
		slice.SortByField(formulaMedicine, "Number")
		medicineNumber = append(medicineNumber, formulaMedicine...)
		return medicineNumber
	}
	return medicines
}

func checkRegisterChangeGroup(ctx *gin.Context, sctx mongo.SessionContext, attribute models.Attribute, drugConfigure models.DrugConfigure, subject models.Subject, visitNumber string, beRegisterName, name string) (bool, error) {
	// 没开启组别变更 未随机过 已变更组别
	if !attribute.AttributeInfo.AllowRegisterGroup || subject.Group == "" || subject.RegisterGroup != "" {
		return false, nil
	}

	match := bson.M{
		"env_id":    subject.EnvironmentID,
		"cohort_id": subject.CohortID,
	}
	var visitCycle models.VisitCycle
	err := tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visitCycle)
	if err != nil {
		return false, errors.WithStack(err)
	}
	// 判断是否是随机后的第一个发药  随机发药同个访视  随机的下一个发药访视
	randomVisit, err := checkFirstRandomVisit(visitCycle, visitNumber)
	if err != nil {
		return false, errors.WithStack(err)
	}
	if !randomVisit {
		return false, nil
	}

	var dispensing []models.Dispensing
	cursor, err := tools.Database.Collection("dispensing").Find(sctx, bson.M{"subject_id": subject.ID, "visit_info.number": visitNumber})
	if err != nil {
		return false, errors.WithStack(err)
	}

	err = cursor.All(nil, &dispensing)
	if err != nil {
		return false, errors.WithStack(err)
	}
	// 多组别的药，登记的药物

	// 已经登记的药都包含groups 且只有一个组别全部包含
	group, ok := AllRegisterGroup(dispensing, drugConfigure, name)
	if !ok {
		return false, nil
	}
	// 访视一致
	ok = checkSingeGroupAndSameVisit(group, visitCycle, subject)
	if !ok {
		return false, nil
	}

	updateRegister, err := registerGroup(sctx, subject, dispensing, drugConfigure, beRegisterName, name, group)
	if err != nil {
		return false, errors.WithStack(err)
	}

	return updateRegister, err
}

func GroupName(drugConfigure models.DrugConfigure, name string) datastructure.Set[string] {
	sets := datastructure.Set[string]{}
	for _, item := range drugConfigure.Configures {
		for _, value := range item.Values {
			if value.DrugName == name && item.Group != "N/A" {
				sets.Add(item.Group)
			}
		}
	}

	return sets
}

func AllRegisterGroup(dispensing []models.Dispensing, drugConfigure models.DrugConfigure, name string) (string, bool) {

	groups := GroupName(drugConfigure, name)

	registerSets := []datastructure.Set[string]{}
	for _, item := range dispensing {
		for _, medicine := range item.RealOtherDispensingMedicines {
			name := GroupName(drugConfigure, medicine.Name)
			registerSets = append(registerSets, name)
		}
		for _, medicine := range item.RealDispensingMedicines {
			name := GroupName(drugConfigure, medicine.Name)
			registerSets = append(registerSets, name)

		}
	}
	returnGroups := []string{}
	for _, item := range groups.Values() {
		count := 0
		for _, set := range registerSets {
			ok := slice.Contain(set.Values(), item)
			if ok {
				count++
			}
		}
		if count == len(registerSets) {
			returnGroups = append(returnGroups, item)
		}
	}

	if len(returnGroups) != 1 {
		return "", false
	}

	return returnGroups[0], true
}

func registerGroup(sctx mongo.SessionContext, subject models.Subject, dispensings []models.Dispensing, drugConfigure models.DrugConfigure, beRegisterName, name string, group string) (bool, error) {

	if subject.Group == group {
		return false, nil
	}

	// 全开放
	blindMedicineAll, err := tools.IsBlindDrugMap(subject.EnvironmentID)
	if err != nil {
		return false, errors.WithStack(err)
	}
	hasOpen := false
	hasBlind := false
	for _, dispensing := range dispensings {
		for _, item := range dispensing.DispensingMedicines {
			if blindMedicineAll[item.Name] {
				hasBlind = true
			} else {
				hasOpen = true
			}
		}
		if len(dispensing.OtherDispensingMedicines) > 0 {
			hasOpen = true
		}
	}

	if hasBlind { // 盲态药物未全部登记完
		return false, nil
	}

	// 盲态药物全部登记完
	/*
		if	登记盲态药物
				if 存在开放药物  (开放加盲态）

				else  全盲态

		if 登记开放药物  //全开放
	*/

	if blindMedicineAll[beRegisterName] {
		// 是否存在开放药物
		change := medicineGroupSame(dispensings, drugConfigure, group, hasOpen)
		if !change {
			return false, nil
		}
	} else {
		// 判断第一次登记
		openCount := 0
		for _, item := range dispensings {
			openCount = openCount + len(item.RealDispensingMedicines) + len(item.RealOtherDispensingMedicines)
		}
		if openCount > 1 {
			return false, nil
		}
		change := medicineGroupSame(dispensings, drugConfigure, group, hasOpen)
		if !change {
			return false, nil
		}

		//if !hasOpen { // 登记最后一个是开放  还没变组别 没得比较
		//	return false, nil
		//}
	}

	err = updateSubjectRegisterGroup(sctx, subject.ID, group)
	if err != nil {
		return false, err
	}

	return true, err
}

func medicineGroupSame(dispensings []models.Dispensing, drugConfigure models.DrugConfigure, group string, hasOpen bool) bool {
	for _, dispensing := range dispensings {
		// 是否存在开放药物
		if hasOpen {
			// 判断所有开放药物在两个组别
			for _, item := range dispensing.OtherDispensingMedicines {
				if !checkInGroup(drugConfigure, item.Name, group) {
					return false
				}
			}
			for _, item := range dispensing.DispensingMedicines {
				if !checkInGroup(drugConfigure, item.Name, group) {
					return false
				}
			}
		}
		// 判断所有登记的药物 包含该组别
		for _, item := range dispensing.RealDispensingMedicines {
			if !checkInGroup(drugConfigure, item.Name, group) {
				return false
			}
		}
		for _, item := range dispensing.RealOtherDispensingMedicines {
			if !checkInGroup(drugConfigure, item.Name, group) {
				return false
			}
		}
	}
	return true
}

func updateSubjectRegisterGroup(sctx mongo.SessionContext, subjectID primitive.ObjectID, group string) error {
	_, err := tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": subjectID}, bson.M{"$set": bson.M{"register_group": group}})
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func checkFirstRandomVisit(visitCycle models.VisitCycle, visitNumber string) (bool, error) {

	index := -1
	slice.Find(visitCycle.Infos, func(i int, item models.VisitCycleInfo) bool {
		index = i
		return item.Random
	})
	if index == -1 { //仅发药
		return false, nil
	}
	visitCycleP, ok := slice.Find(visitCycle.Infos, func(i int, item models.VisitCycleInfo) bool {
		return i >= index && item.Dispensing
	})
	if ok {
		visitCycles := *visitCycleP
		if visitCycles.Number == visitNumber {
			return true, nil
		}
	}
	return false, nil
}

func checkInGroup(drugConfigure models.DrugConfigure, name string, group string) bool {
	sets := datastructure.Set[string]{}
	for _, item := range drugConfigure.Configures {
		for _, value := range item.Values {
			if value.DrugName == name {
				sets.Add(item.Group)
			}
		}
	}
	if sets.Contain(group) {
		return true
	}
	return false
}

func getRegisterGroups(drugConfigure models.DrugConfigure, name string) []string {
	group := []string{}
	for _, item := range drugConfigure.Configures {
		for _, value := range item.Values {
			if value.DrugName == name {
				group = append(group, item.Group)
			}
		}
	}
	return group
}

func checkSingeGroupAndSameVisit(group string, visitCycle models.VisitCycle, subject models.Subject) bool {

	for _, item := range visitCycle.Infos {

		_, subjectOk := slice.Find(item.Group, func(i int, it interface{}) bool {
			return it == subject.Group
		})

		_, registerOk := slice.Find(item.Group, func(i int, it interface{}) bool {
			return it.(string) == group
		})

		if subjectOk && !registerOk {
			return false
		}
		if !subjectOk && registerOk {
			return false
		}
	}

	return true
}

func getTransOldSiteID(subject models.Subject, dispensingTime time.Duration) primitive.ObjectID {
	returnSiteID := subject.ProjectSiteID
	if len(subject.TransferSiteInfos) != 0 {
		for _, item := range subject.TransferSiteInfos {
			if dispensingTime <= item.TransTime {
				returnSiteID = item.OldSiteID
				break
			}
		}
	}
	return returnSiteID

}

func dispensingConfig(subject models.Subject) (map[string]models.VisitCycleInfo, []models.ResDispensing, models.Attribute, models.VisitCycle, error) {
	var dispensing []models.ResDispensing
	match := bson.M{"subject_id": subject.ID}

	var attribute models.Attribute
	var visitCycle models.VisitCycle

	attributeFilter := bson.M{"env_id": subject.EnvironmentID}
	if !subject.CohortID.IsZero() {
		attributeFilter = bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
	}
	err := tools.Database.Collection("attribute").FindOne(nil, attributeFilter).Decode(&attribute)
	if err != nil {
		return nil, nil, attribute, visitCycle, errors.WithStack(err)
	}

	err = tools.Database.Collection("visit_cycle").FindOne(nil, attributeFilter).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, nil, attribute, visitCycle, errors.WithStack(err)

	}

	visitCycleMap := map[string]models.VisitCycleInfo{}
	for _, info := range visitCycle.Infos {
		visitCycleMap[info.Number] = info
	}
	cursor, err := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine_order",
			"let": bson.M{
				"dispensing_id": "$_id",
				//"order_number":  "$order",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$dispensing_id", "$$dispensing_id"}}}},
				//bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$order_number", "$$order_number"}}}},
			},

			"as": "medicine_order",
		}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "history",
			"localField":   "_id",
			"foreignField": "oid",
			"as":           "history",
		}}},
		{{"$addFields", bson.M{
			"history": bson.M{"$size": "$history"},
		}}},
		{{Key: "$sort", Value: bson.M{"serial_number": 1}}},
	})
	if err != nil {
		return nil, nil, attribute, visitCycle, errors.WithStack(err)

	}
	err = cursor.All(nil, &dispensing)
	if err != nil {
		return nil, nil, attribute, visitCycle, errors.WithStack(err)

	}
	return visitCycleMap, dispensing, attribute, visitCycle, nil
}

func dispensingsInfo(ctx *gin.Context, project models.Project, roleID string, attribute models.Attribute, dispensing []models.ResDispensing, visitCycle models.VisitCycle, subject models.Subject, visitCycleMap map[string]models.VisitCycleInfo, intTimeZone float64) ([]models.ResDispensing, bool, bool, primitive.ObjectID, error) {
	outsizeVisit := false
	canReissue := false
	hasBeConfirmOrder := false
	isBlindedRole := false
	visitOID := primitive.NilObjectID
	var err error
	if roleID != "" {
		isBlindedRole, err = tools.IsBlindedRole(roleID)
		if err != nil {
			return nil, hasBeConfirmOrder, canReissue, visitOID, errors.WithStack(err)
		}
	}

	var drugConfigureSetting models.DrugConfigureSetting
	err = tools.Database.Collection("drug_configure_setting").FindOne(nil, bson.M{
		"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID,
	}).Decode(&drugConfigureSetting)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, hasBeConfirmOrder, canReissue, visitOID, errors.WithStack(err)

	}
	if attribute.AttributeInfo.DtpRule != 1 {
		drugConfigureSetting.DtpIpList = []models.DtpIp{}
	}
	lastTime := time.Duration(0)
	afterRandom := false
	interval := float64(0)

	canResumeLastIndex := -1
	dispensingLastIndex := -1
	canDispensing := -1
	randomIndex := -1
	if attribute.AttributeInfo.Random {
		afterRandom = true
	}
	// TODO 7064

	firstTime := time.Duration(0)
	randomTime := subject.RandomTime

	subjectMap := make(map[string]models.Subject)
	if !visitCycle.BaseCohort.IsZero() && visitCycle.BaseCohort != subject.CohortID {
		subjectMap, err = task.GetBaseCohortSubjectMap(nil, visitCycle.BaseCohort, primitive.NilObjectID, subject.Info[0].Value.(string))
	}
	if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
		randomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
	}

	medicineOIDs := bson.A{}
	var medicines []models.Medicine
	slice.ForEach(dispensing, func(index int, item models.ResDispensing) {
		for _, medicine := range item.DispensingMedicines {
			medicineOIDs = append(medicineOIDs, medicine.MedicineID)
		}
	})
	if len(medicineOIDs) > 0 {
		cursor, err := tools.Database.Collection("medicine").Find(nil, bson.M{"_id": bson.M{"$in": medicineOIDs}})
		if err != nil {
			return nil, false, false, [12]byte{}, errors.WithStack(err)
		}
		err = cursor.All(nil, &medicines)
		if err != nil {
			return nil, false, false, [12]byte{}, errors.WithStack(err)
		}
	}

	IsBlindDrugMap, err := tools.IsBlindDrugMap(subject.EnvironmentID)
	if err != nil {
		return nil, false, false, [12]byte{}, errors.WithStack(err)
	}

	for i, resDispensing := range dispensing {
		outPlan := locales.Tr(ctx, "export.dispensing.outVisit")
		if visitCycle.SetInfo.IsOpen {
			if ctx.GetHeader("Accept-Language") == "zh" {
				outPlan = visitCycle.SetInfo.NameZh
			} else {
				outPlan = visitCycle.SetInfo.NameEn
			}
		}
		dispensing[i].OutVisitStr = outPlan

		if !resDispensing.VisitSign {
			if firstTime == 0 && resDispensing.DispensingTime != 0 && visitCycleMap[resDispensing.VisitInfo.Number].Interval != nil && randomTime == 0 {
				randomTime = time.Duration(time.Unix(int64(resDispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleMap[resDispensing.VisitInfo.Number].Interval)).Unix())
				firstTime = resDispensing.DispensingTime
			}

			// TODO 7064
			if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
				subject.RandomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
			}
			if firstTime == 0 && resDispensing.DispensingTime != 0 && visitCycleMap[resDispensing.VisitInfo.Number].Interval != nil && randomTime == 0 {
				randomTime = time.Duration(time.Unix(int64(resDispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleMap[resDispensing.VisitInfo.Number].Interval)).Unix())
				firstTime = resDispensing.DispensingTime
			}
			dispensing[i].Period = handlePeriod(afterRandom, visitCycle.VisitType, visitCycleMap[resDispensing.VisitInfo.Number], randomTime, lastTime, resDispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
			if resDispensing.Status == 2 {
				interval = 0
				lastTime = resDispensing.DispensingTime
			}
		}
		if resDispensing.VisitInfo.Random {
			afterRandom = true
		}

		if resDispensing.Status == 3 {
			canResumeLastIndex = i
		}
		if resDispensing.Status == 2 {
			dispensingLastIndex = i
		}

		if resDispensing.Status == 2 || resDispensing.Status == 3 {
			visitOID = resDispensing.VisitInfo.VisitCycleInfoID
		}

		// 兼容访视发布 不更新编号 名称的情况
		dispensing[i].VisitInfo.Dispensing = visitCycleMap[resDispensing.VisitInfo.Number].Dispensing
		dispensing[i].VisitInfo.Random = visitCycleMap[resDispensing.VisitInfo.Number].Random
		dispensing[i].DTP = visitCycleMap[resDispensing.VisitInfo.Number].DTP

		if canDispensing == -1 && dispensing[i].VisitInfo.Dispensing && resDispensing.Status == 1 {
			canDispensing = i
		}

		if dispensing[i].VisitInfo.Random && !resDispensing.VisitSign {
			randomIndex = i
		}
		if randomIndex != -1 && i >= randomIndex {
			dispensing[i].Random = true
		}

		current := time.Unix(int64(dispensing[i].DispensingTime), 0).UTC().Format("2006-01-02")
		if current >= "2024-03-15" {
			err = slice.SortByField(dispensing[i].DispensingMedicines, "Number")
			if err != nil {
				return nil, hasBeConfirmOrder, canReissue, visitOID, errors.WithStack(err)
			}
		}

		tmpStructMedicine := slice.Map(dispensing[i].DispensingMedicines, func(index int, item models.DispensingMedicineRes) models.DispensingMedicine {
			return item.DispensingMedicine
		})

		tmpStructMedicine = sortTypeMedicineNumber(tmpStructMedicine)
		newMedicine := []models.DispensingMedicineRes{}

		for _, item := range tmpStructMedicine {
			tmp := models.DispensingMedicineRes{}
			tmp.DispensingMedicine = item
			tmp.DTPs = getDTPInfo(drugConfigureSetting.DtpIpList, nil, item.Name)
			tmp.UnBlind = 0
			if IsBlindDrugMap[item.Name] {
				tmp.UnBlind = 1
				slice.ForEach(medicines, func(index int, medicine models.Medicine) {
					if medicine.ID == item.MedicineID {

						if medicine.Unblinding != nil && medicine.Unblinding.OperationTime != 0 {
							tmp.UnBlind = 2
						}

						for _, approval := range medicine.UnblindingApprovals {
							if approval.Status == 1 {
								tmp.UnBlind = 2
							}
							if approval.Status == 0 {
								tmp.UnReplace = true
							}
						}
					}
				})
			}
			newMedicine = append(newMedicine, tmp)

		}
		dispensing[i].DispensingMedicines = newMedicine

		for j, medicine := range dispensing[i].DispensingMedicines {
			canRetrieval := true
			// isOpenDrug, _ := tools.IsOpenDrug(subject.EnvironmentID, subject.CohortID, medicine.Name)
			// isOtherDrug, _ := tools.IsOtherDrug(subject.EnvironmentID, medicine.Name)
			// if isBlindedRole && !isOpenDrug && !isOtherDrug {
			// 	dispensing[i].DispensingMedicines[j].Name = tools.BlindData
			// }
			if medicine.Type == 3 {
				dispensing[i].DispensingMedicines[j].Label = ""
			}
			isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
			if isBlindedRole && isBlindedDrug {
				dispensing[i].DispensingMedicines[j].Name = tools.BlindData
			}
			slice.ForEach(medicines, func(index int, item models.Medicine) {
				if medicine.MedicineID == item.ID {
					for _, approval := range item.UnblindingApprovals {
						if approval.Status == 0 {
							canRetrieval = false
						}
					}
				}
			})
			for _, orderDispensing := range resDispensing.MedicineOrder {
				if orderDispensing.Status != 5 && orderDispensing.Status != 8 && orderDispensing.Status != 9 {
					for _, id := range orderDispensing.Medicines {
						if medicine.MedicineID == id && medicine.RealMedicineID == primitive.NilObjectID {
							canRetrieval = false
						}
					}
				}
			}

			// 揭盲不允许替换取回
			if canRetrieval {
				dispensing[i].CanRetrieval = append(dispensing[i].CanRetrieval, dispensing[i].DispensingMedicines[j])
			}

		}
		otherDispensingMedicinesHandle(drugConfigureSetting.DtpIpList, dispensing, resDispensing, subject, isBlindedRole, i)
		if isBlindedRole {
			for j, medicine := range resDispensing.ReplaceMedicines {
				isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
				if isBlindedRole && isBlindedDrug {
					dispensing[i].ReplaceMedicines[j].Name = tools.BlindData
				}
			}
			for j, medicine := range resDispensing.RealDispensingMedicines {
				isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
				if isBlindedRole && isBlindedDrug {
					dispensing[i].RealDispensingMedicines[j].Name = tools.BlindData
				}
			}
			for j, medicine := range resDispensing.RealOtherDispensingMedicines {
				isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
				//isOtherDrug, _ := tools.IsOtherDrug(subject.EnvironmentID, medicine.Name)
				if isBlindedDrug {
					dispensing[i].RealOtherDispensingMedicines[j].Name = tools.BlindData
				}
			}
			for j, medicine := range resDispensing.CancelMedicinesHistory {
				isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
				if isBlindedRole && isBlindedDrug {
					dispensing[i].CancelMedicinesHistory[j].Name = tools.BlindData
				}
			}
		}

		_, ok := slice.Find(resDispensing.MedicineOrder, func(index int, item models.MedicineOrderDispensing) bool {
			return item.Status == 6 || item.Status == 7
		})
		if ok {
			hasBeConfirmOrder = true
		}

		if !resDispensing.VisitSign && ok { // 主访视存在 待确认 申请订单 不允许访视外
			hasBeConfirmOrder = true
		}
		if (len(dispensing[i].DispensingMedicines) > 0 || len(dispensing[i].OtherDispensingMedicines) > 0) && !ok {
			dispensing[i].CanRegister = true
		}

	}

	// 7574 剂量判断后续访视是否发药
	visitNumbers, visitInheritanceCount := DoseOpenCheck(subject, drugConfigureSetting, dispensing, 1)

	if canDispensing != -1 && !hasBeConfirmOrder {
		if subject.Group == "" && (canDispensing < randomIndex || randomIndex == -1) {
			dispensing[canDispensing].CanDispensing = true

		}
		if subject.Group != "" && canDispensing >= randomIndex {
			dispensing[canDispensing].CanDispensing = true
		}

		// 继承次数大于0 并且超过继承的访视
		if visitInheritanceCount && !visitNumbers[dispensing[canDispensing].VisitInfo.Number] {
			dispensing[canDispensing].CanDispensing = false
		}
	}

	if canResumeLastIndex > dispensingLastIndex {
		if canResumeLastIndex > -1 {
			if subject.Group == "" && (canResumeLastIndex < randomIndex || randomIndex == -1) && !hasBeConfirmOrder {
				dispensing[canResumeLastIndex].CanResume = true
				outsizeVisit = true

			}
			if subject.Group != "" && canResumeLastIndex >= randomIndex && !hasBeConfirmOrder {
				dispensing[canResumeLastIndex].CanResume = true
				outsizeVisit = true

			}
		}

	} else {
		if dispensingLastIndex > -1 {
			if subject.Group == "" && (dispensingLastIndex < randomIndex || randomIndex == -1) {
				if len(dispensing[dispensingLastIndex].DispensingMedicines) > 0 || len(dispensing[dispensingLastIndex].OtherDispensingMedicines) > 0 {
					_, ok := slice.Find(dispensing[dispensingLastIndex].MedicineOrder, func(index int, item models.MedicineOrderDispensing) bool {
						return item.Status == 2 || (project.ResearchAttribute == 1 && item.Status == 1) // 存在运送订单 或者dtp项目已确认 不给替换
					})
					if !ok {
						dispensing[dispensingLastIndex].CanReplace = true
					}
				}
				if len(dispensing[dispensingLastIndex].CanRetrieval) > 0 || len(dispensing[dispensingLastIndex].OtherCanRetrieval) > 0 {
					dispensing[dispensingLastIndex].Retrieval = true
				}
				if !hasBeConfirmOrder {
					outsizeVisit = true
					canReissue = true
				}
			}
			if subject.Group != "" && dispensingLastIndex >= randomIndex {
				if len(dispensing[dispensingLastIndex].DispensingMedicines) > 0 || len(dispensing[dispensingLastIndex].OtherDispensingMedicines) > 0 {
					_, ok := slice.Find(dispensing[dispensingLastIndex].MedicineOrder, func(index int, item models.MedicineOrderDispensing) bool {
						return item.Status == 2 || (project.ResearchAttribute == 1 && item.Status == 1) // 存在运送订单 或者dtp项目已确认 不给替换
					})
					if !ok {
						dispensing[dispensingLastIndex].CanReplace = true
					}
				}
				if len(dispensing[dispensingLastIndex].CanRetrieval) > 0 || len(dispensing[dispensingLastIndex].OtherCanRetrieval) > 0 {
					dispensing[dispensingLastIndex].Retrieval = true
				}
				if !hasBeConfirmOrder {
					outsizeVisit = true
					canReissue = true
				}
			}
		}

	}
	return dispensing, outsizeVisit, canReissue, visitOID, nil
}

func ReplaceDrugMethod(ctx *gin.Context, data map[string]interface{}, edcUserName string, requestType int) ([]models.ResDispensingInfo, []models.ReplaceCodeDTOS, error) {
	var replaceCodeDTOS []models.ReplaceCodeDTOS
	var resOrderInfo []models.ResDispensingInfo
	OID, _ := primitive.ObjectIDFromHex(data["id"].(string))
	projectOID, _ := primitive.ObjectIDFromHex(data["project_id"].(string))
	envOID, _ := primitive.ObjectIDFromHex(data["env_id"].(string))
	customerOID, _ := primitive.ObjectIDFromHex(data["customer_id"].(string))
	subjectOID, _ := primitive.ObjectIDFromHex(data["subject_id"].(string))
	var histories []models.History

	reason := ""
	remark := ""
	sendType := 0
	if data["reason"] != nil {
		reason = data["reason"].(string)
	}
	if data["remark"] != nil {
		remark = data["remark"].(string)
	}

	if data["send_type"] != nil {
		sendType = int(data["send_type"].(float64))
	}

	var logisticsInfo models.LogisticsInfo
	if data["logistics"] != nil {
		logisticsInfo.Logistics = data["logistics"].(string)
		if data["other"] != nil {
			logisticsInfo.Other = data["other"].(string)
		}
	}
	if data["number"] != nil {
		logisticsInfo.Number = data["number"].(string)
	}

	isBlindedRole := false
	if data["role_id"] != nil {
		var err error
		isBlindedRole, err = tools.IsBlindedRole(data["role_id"].(string))
		if err != nil {
			return nil, nil, errors.WithStack(err)
		}
	}

	var project models.Project
	projectOpts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}, projectOpts).Decode(&project); err != nil {
		return nil, nil, errors.WithStack(err)
	}

	oldOpenProject, err := tools.Database.Collection("setting_config").CountDocuments(nil, bson.M{"key": "open-project", "data": project.ProjectInfo.Number})
	if err != nil {
		return nil, nil, errors.WithStack(err)

	}

	now := time.Duration(time.Now().Unix())
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 校验 受试者是否已经揭盲 退出 替换
		var medicines []models.Medicine
		replaceCodeDTOS = []models.ReplaceCodeDTOS{}
		resOrderInfo = []models.ResDispensingInfo{}
		BeDTPMap := map[primitive.ObjectID]int{}
		DTPMap := map[primitive.ObjectID]int{}
		status, err := checkSubjectStatus(ctx, sctx, subjectOID)
		if err != nil {
			return nil, err
		}
		if !status {
			return nil, tools.BuildServerError(ctx, "subject_status_no_replace_dispensing")
		}

		// 校验当前替换的是否为最新的访视
		var dispensing models.Dispensing
		err = tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"_id": OID}).Decode(&dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var dispensingLast models.Dispensing
		err = tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"subject_id": subjectOID, "status": bson.M{"$ne": 1}}, &options.FindOneOptions{
			Sort: bson.D{{"serial_number", -1}},
		}).Decode(&dispensingLast)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if dispensing.ID != dispensingLast.ID {
			return nil, tools.BuildServerError(ctx, "edc.drug.replace.error")
		}

		// 校验通用订单是否存在待确认订单
		checkConfirm, err := checkConfirmOrder(sctx, subjectOID, 2)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if !checkConfirm {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_order_confrim")
		}

		visitCycle, visitInfo, err := getVisitInfo(customerOID, projectOID, envOID, dispensing.CohortID, dispensing.VisitInfo.VisitCycleInfoID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if !visitInfo.DTP && sendType != 0 {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_order_dtp")
		}
		drugs := bson.A{}
		otherDrugs := []models.OtherDispensingMedicine{}
		newReplaceOthersDispensingMedicines := []models.OthersDispensingMedicine{}

		//medicineIDs := bson.A{}
		if data["info_count"] != nil {
			for _, item := range data["info_count"].([]interface{}) {
				drug := false
				medicineInfoCount, ok := item.(map[string]interface{})
				if !ok {
					return nil, err
				}
				medicineOID, _ := primitive.ObjectIDFromHex(medicineInfoCount["medicine"].(string))
				BeDTPMap[medicineOID] = sendType

				if medicineInfoCount["dtp"] != nil && sendType == 0 {
					BeDTPMap[medicineOID] = int(medicineInfoCount["dtp"].(float64))
				}

				for _, medicine := range dispensing.OtherDispensingMedicines {
					if medicine.ID == medicineOID {
						medicine.ReplaceCount = int(medicineInfoCount["count"].(float64))
						otherDrugs = append(otherDrugs, medicine)
						drug = true
						continue
					}
				}
				if !drug {
					drugs = append(drugs, bson.M{"_id": medicineOID})
				}
			}
		} else {
			//medicineIDs := bson.A{}
			if data["info"] != nil {
				for _, item := range data["info"].([]interface{}) {
					id, ok := item.(string)
					if !ok {
						return nil, err
					}
					medicineOID, _ := primitive.ObjectIDFromHex(id)
					BeDTPMap[medicineOID] = sendType
					drugs = append(drugs, bson.M{"_id": medicineOID})

				}
			}
		}

		var medicineInfo []models.Medicine
		// DTP 模式下  非申请状态不允许替换

		if len(drugs) > 0 {
			drugsMatch := bson.M{"$or": drugs}
			if project.ProjectInfo.ResearchAttribute == 1 {
				drugsMatch = bson.M{"$or": drugs, "status": bson.M{"$in": bson.A{5, 13}}}

			}
			cursor, _ := tools.Database.Collection("medicine").Find(sctx, drugsMatch)
			err = cursor.All(sctx, &medicineInfo)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if len(drugs) != len(medicineInfo) {
				return nil, tools.BuildServerError(ctx, "subject_visit_cannot_replace")
			}
		}

		var medicineNumber []string
		var replaceNumber []string
		var BeReplaceNumber []string
		BeReplaceID := []primitive.ObjectID{}
		ReplaceID := []primitive.ObjectID{}

		expireDateKey, dispensingAlarmNumber, err := getExpireDateKey(subjectOID)
		if err != nil {
			return nil, err
		}
		var subject models.Subject
		err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// DTP 模式下 获取 供应计划配置的仓库
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil && err != mongo.ErrNoDocuments {
			return [12]byte{}, errors.WithStack(err)
		}
		siteOID := subject.ProjectSiteID  // 药物起运地
		storeOID := primitive.NilObjectID // 药物起运地
		// DTP
		storeOrderID := primitive.NewObjectID()
		siteOrderID := primitive.NewObjectID()
		if len(projectSite.StoreHouseID) > 0 && projectSite.StoreHouseID[0] != primitive.NilObjectID {
			storeOID = projectSite.StoreHouseID[0] // 药物起运地
		}
		if (sendType == 2 || project.ProjectInfo.ResearchAttribute == 1) && storeOID.IsZero() {
			return [12]byte{}, tools.BuildServerError(ctx, "subject_visit_dispensing_store")
		}

		var attribute models.Attribute
		attributeMatch := bson.M{"project_id": projectOID, "env_id": envOID}
		if subject.CohortID != primitive.NilObjectID {
			attributeMatch = bson.M{"project_id": projectOID, "env_id": envOID, "cohort_id": subject.CohortID}
		}
		err = tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if oldOpenProject == 1 && attribute.AttributeInfo.Blind { // 如果当前cohort属性为盲态
			oldOpenProject = 0
		}

		subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

		orderOID := primitive.NilObjectID
		var order models.MedicineOrder
		var orderNew models.MedicineOrder

		if dispensing.Order != "" {
			err = tools.Database.Collection("medicine_order").FindOne(sctx, bson.M{"dispensing_id": dispensing.ID, "order_number": dispensing.Order}).Decode(&order)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			orderOID = order.ID
		}
		zone, err := tools.GetTimeZone(projectOID)

		sortMedicineInfo := []models.Medicine{}

		for _, mapInfo := range drugs {
			tmpMedicineP, _ := slice.Find(medicineInfo, func(index int, item models.Medicine) bool {
				return item.ID == mapInfo.(primitive.M)["_id"]
			})
			tmpMedicine := *tmpMedicineP
			sortMedicineInfo = append(sortMedicineInfo, tmpMedicine)
		}

		for _, value := range sortMedicineInfo {
			var medicine models.Medicine
			date := time.Now().UTC().Add(time.Hour * time.Duration(zone)).Format("2006-01-02")
			if expireDateKey[value.Name] != nil {
				date = expireDateKey[value.Name].(string)
			}
			medicineNumber = append(medicineNumber, value.Number)
			// 获取可用研究产品编号并更新研究产品状态
			match := bson.M{
				"status":          bson.M{"$in": bson.A{1, 14}},
				"customer_id":     subject.CustomerID,
				"project_id":      projectOID,
				"env_id":          envOID,
				"name":            value.Name,
				"spec":            value.Spec,
				"expiration_date": bson.M{"$gt": date},
				"$or": bson.A{
					bson.M{"subject_id": bson.M{"$exists": 0}},
					bson.M{"subject_id": primitive.NilObjectID},
					bson.M{"subject_id": subjectOID},
				},
				"frozen": bson.M{"$ne": true},
			}
			update := bson.M{}

			// DTP模式下 获取 库房的药物， 修改药物状态为已申请
			if project.ResearchAttribute == 1 {
				if storeOID.IsZero() {
					return [12]byte{}, tools.BuildServerError(ctx, "subject_visit_dispensing_store")
				}
				match["storehouse_id"] = storeOID
				update = bson.M{"$set": bson.M{"status": 13, "order_id": orderOID, "subject_id": dispensing.SubjectID}}
			} else if BeDTPMap[value.ID] == 2 {
				if storeOID.IsZero() {
					return [12]byte{}, tools.BuildServerError(ctx, "subject_visit_dispensing_store")
				}
				match["storehouse_id"] = storeOID
				update = bson.M{"$set": bson.M{"status": 11, "order_id": primitive.NilObjectID, "subject_id": dispensing.SubjectID}}
			} else if BeDTPMap[value.ID] == 1 {
				match["site_id"] = siteOID
				update = bson.M{"$set": bson.M{"status": 11, "order_id": primitive.NilObjectID, "subject_id": dispensing.SubjectID}}
			} else if BeDTPMap[value.ID] == 0 {
				match["site_id"] = siteOID
				update = bson.M{"$set": bson.M{"status": 5}}
			}

			//TODO 校验发放警戒
			err = checkDispensingAlarm(ctx, sctx, value.Name, match, 1, dispensingAlarmNumber)
			if err != nil {
				return nil, err
			}

			_ = tools.Database.Collection("medicine").FindOneAndUpdate(sctx, match, update,
				&options.FindOneAndUpdateOptions{
					Sort: bson.D{{"subject_id", -1}, {"expiration_date", 1}, {"batch_number", 1}, {"serial_number", 1}, {"number", 1}}, // 优先获取受试者绑定的研究产品  subject_id
				}).Decode(&medicine)

			// DTP 无批次号
			if BeDTPMap[value.ID] != 0 && medicine.ID == primitive.NilObjectID {
				match["expiration_date"] = ""
				_ = tools.Database.Collection("medicine").FindOneAndUpdate(sctx, match, update,
					&options.FindOneAndUpdateOptions{
						Sort: bson.D{{"subject_id", -1}, {"serial_number", 1}, {"number", 1}}, // 优先获取受试者绑定的研究产品  subject_id
					}).Decode(&medicine)

			}
			if medicine.ID == primitive.NilObjectID {
				if BeDTPMap[value.ID] == 2 {
					return nil, tools.BuildServerError(ctx, "subject_medicine_count_store")
				}
				return nil, tools.BuildServerError(ctx, "subject_medicine_count")
			}
			DTPMap[medicine.ID] = BeDTPMap[value.ID]
			medicines = append(medicines, medicine)
			replaceNumber = append(replaceNumber, medicine.Number)
			BeReplaceNumber = append(BeReplaceNumber, value.Number)
			BeReplaceID = append(BeReplaceID, value.ID)
			ReplaceID = append(ReplaceID, medicine.ID)
			replaceCodeDTOS = append(replaceCodeDTOS, models.ReplaceCodeDTOS{
				OldCode:         value.Number,
				NewCode:         medicine.Number,
				NoReplaceReason: reason,
			})
			// 更新发药编号
			match = bson.M{"subject_id": subjectOID, "customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "dispensing_medicines.name": value.Name, "dispensing_medicines.number": value.Number}

			// 查询获取 被替换研究产品 初始发药时间

			beMedicineP, ok := slice.Find(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
				return item.Number == value.Number
			})
			beMedicine := models.DispensingMedicine{}
			if ok {
				beMedicine = *beMedicineP
			}
			if err != nil {
				return nil, errors.WithStack(err)
			}
			dispensing.Reasons = append(dispensing.Reasons, models.Reason{
				ReasonID:   primitive.NewObjectID(),
				ReasonType: 1,
				ReasonTime: now,
				Reason:     reason})
			dispensing.ReplaceMedicines = append(dispensing.ReplaceMedicines, models.ReplaceMedicines{
				MedicineID:     value.ID,
				Name:           value.Name,
				Number:         value.Number,
				ExpirationDate: value.ExpirationDate,
				BatchNumber:    value.BatchNumber,
				MedicineNewID:  medicine.ID,
				NewNumber:      medicine.Number,
				Time:           beMedicine.Time, // 被替换研究产品 初始发药时间
				OperationTime:  now,
				Label:          beMedicine.Label,
				UseFormulas:    beMedicine.UseFormulas,
				DoseInfo:       beMedicine.DoseInfo,
				OpenSetting:    beMedicine.OpenSetting,
				OrderOID:       beMedicine.OrderOID,
			})
			updateInfo := bson.M{
				"dispensing_medicines.$[id].number":          medicine.Number,
				"dispensing_medicines.$[id].name":            medicine.Name,
				"dispensing_medicines.$[id].medicine_id":     medicine.ID,
				"dispensing_medicines.$[id].expiration_date": medicine.ExpirationDate,
				"dispensing_medicines.$[id].batch_number":    medicine.BatchNumber,
				"dispensing_medicines.$[id].package_number":  medicine.PackageNumber,
				"dispensing_medicines.$[id].short_code":      medicine.ShortCode,
				"dispensing_medicines.$[id].time":            now, // 替换时间
				"dispensing_medicines.$[id].type":            3,
				"reasons":                                    dispensing.Reasons,
				"replace_medicines":                          dispensing.ReplaceMedicines,
			}
			if BeDTPMap[value.ID] == 1 {
				updateInfo["dispensing_medicines.$[id].order_oid"] = siteOrderID
			}
			if BeDTPMap[value.ID] == 2 {
				updateInfo["dispensing_medicines.$[id].order_oid"] = storeOrderID

			}

			update = bson.M{
				"$set": updateInfo,
			}
			opts := &options.UpdateOptions{
				ArrayFilters: &options.ArrayFilters{
					Filters: bson.A{
						bson.M{"id.medicine_id": value.ID},
					},
				},
			}
			_, err = tools.Database.Collection("dispensing").UpdateOne(sctx, match, update, opts)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// 将旧编号状态置为作废
			blindMedcine, err := tools.IsBlindedDrug(envOID, medicine.Name)

			if err != nil {
				return nil, errors.WithStack(err)
			}
			updateID := primitive.NilObjectID
			if blindMedcine && oldOpenProject == 0 {
				updateID = subject.ID
			}
			matchSku := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "_id": value.ID}
			updateSku := bson.M{
				"$set": bson.M{
					"status":     6,
					"subject_id": updateID,
				},
			}
			if _, err := tools.Database.Collection("medicine").UpdateOne(sctx, matchSku, updateSku); err != nil {
				return nil, errors.WithStack(err)
			}
		}

		//  未编号药替换
		otherMedicineCounts := []primitive.ObjectID{}
		beOtherMedicineCounts := []primitive.ObjectID{}
		othersDispensingMedicine := []models.OthersDispensingMedicine{}
		useOtherDispensingMedicine := []models.OtherDispensingMedicine{}
		for _, drug := range otherDrugs {
			tmpSendType := sendType
			if sendType == 0 {
				tmpSendType = BeDTPMap[drug.ID]
			}
			// 替换的批次 可用-1  待确认+1/已使用+1
			date := time.Now().UTC().Add(time.Hour * time.Duration(zone)).Format("2006-01-02")
			if expireDateKey[drug.Name] != nil {
				date = expireDateKey[drug.Name].(string)
			}
			filter := bson.M{"info.name": drug.Name, "info.batch": bson.M{"$ne": drug.Batch}, "info.expire_date": bson.M{"$gt": date}, "status": 1}
			allFilter := bson.M{"info.name": drug.Name, "info.expire_date": bson.M{"$gt": date}, "status": 1}
			if tmpSendType == 2 {
				if storeOID.IsZero() {
					return [12]byte{}, tools.BuildServerError(ctx, "subject_visit_dispensing_store")
				}
				allFilter["storehouse_id"] = storeOID
				filter["storehouse_id"] = storeOID
			} else {
				allFilter["site_id"] = siteOID
				filter["site_id"] = siteOID
			}
			values := models.DispensingDrugValue{}
			values.DrugName = drug.Name
			values.DispensingNumber = drug.ReplaceCount
			otherMedicineCount := []primitive.ObjectID{}
			otherDispensingMedicineSinge, othersDispensingMedicineSinge, err := getOthersMedicine(ctx, sctx, subject, date, drug.ReplaceCount, values, &otherMedicineCount, tmpSendType, siteOID, storeOID, siteOrderID, storeOrderID, now, dispensingAlarmNumber, "", 3, drug.Batch, attribute, nil, nil, "")
			if err != nil {
				return nil, err
			}
			for _, item := range othersDispensingMedicineSinge {
				DTPMap[item.MedicineID] = BeDTPMap[drug.ID]
			}
			otherMedicineCounts = append(otherMedicineCounts, otherMedicineCount...)
			newDispensingOtherDispensingMedicines := []models.OtherDispensingMedicine{}
			replacedCount := drug.ReplaceCount
			for _, medicine := range dispensing.OtherDispensingMedicines {
				if medicine.Name == drug.Name && medicine.Batch == drug.Batch && medicine.ExpireDate == drug.ExpireDate {
					if medicine.Count > replacedCount {
						medicine.Count = medicine.Count - replacedCount
						newDispensingOtherDispensingMedicines = append(newDispensingOtherDispensingMedicines, medicine)
						replacedCount = 0
					} else {
						replacedCount = replacedCount - medicine.Count
					}
				} else {
					newDispensingOtherDispensingMedicines = append(newDispensingOtherDispensingMedicines, medicine)

				}
			}
			newID := primitive.NewObjectID()

			othersDispensingMedicine = append(othersDispensingMedicine, othersDispensingMedicineSinge...)
			useOtherDispensingMedicine = append(useOtherDispensingMedicine, otherDispensingMedicineSinge...)
			item := models.OtherDispensingMedicineInfo{
				ID:         newID,
				Name:       otherDispensingMedicineSinge[0].Name,
				Count:      drug.ReplaceCount,
				Batch:      otherDispensingMedicineSinge[0].Batch,
				ExpireDate: otherDispensingMedicineSinge[0].ExpireDate,
				Time:       now,
				Type:       3,
				BeInfo:     &drug,
			}
			if BeDTPMap[drug.ID] == 1 {
				item.OrderOID = &siteOrderID
			}
			if BeDTPMap[drug.ID] == 2 {
				item.OrderOID = &storeOrderID
			}
			dispensing.ReplaceOtherMedicines = append(dispensing.ReplaceOtherMedicines, item)
			dispensing.OtherMedicinesHistory = append(dispensing.OtherMedicinesHistory, item)

			// 拿掉未编号单品中的数据
			replaceIDs := []primitive.ObjectID{}
			newOthersDispensingMedicines := []models.OthersDispensingMedicine{}
			newReplaceOthersDispensingMedicines = []models.OthersDispensingMedicine{}
			for _, medicine := range dispensing.OthersDispensingMedicines {
				if len(replaceIDs) == drug.ReplaceCount {
					newOthersDispensingMedicines = append(newOthersDispensingMedicines, medicine)
				} else {
					if medicine.Name == drug.Name && medicine.BatchNumber == drug.Batch && medicine.ExpirationDate == drug.ExpireDate {
						replaceIDs = append(replaceIDs, medicine.MedicineID)
						newReplaceOthersDispensingMedicines = append(newReplaceOthersDispensingMedicines, medicine)
					} else {
						newOthersDispensingMedicines = append(newOthersDispensingMedicines, medicine)
					}
				}
			}
			newOthersDispensingMedicines = append(newOthersDispensingMedicines, othersDispensingMedicineSinge...)
			newDispensingOtherDispensingMedicines = append(newDispensingOtherDispensingMedicines, otherDispensingMedicineSinge...)
			dispensing.OtherDispensingMedicines = newDispensingOtherDispensingMedicines
			dispensing.ReplaceOthersMedicines = append(dispensing.ReplaceOthersMedicines, newReplaceOthersDispensingMedicines...)

			_, err = tools.Database.Collection("dispensing").UpdateOne(sctx, bson.M{"_id": OID}, bson.M{"$set": bson.M{
				"other_dispensing_medicines": dispensing.OtherDispensingMedicines,
				"replace_other_medicines":    dispensing.ReplaceOtherMedicines,
				"other_medicines_history":    dispensing.OtherMedicinesHistory,

				"others_dispensing_medicines": newOthersDispensingMedicines,
				"replace_others_medicines":    dispensing.ReplaceOthersMedicines,
			}})
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// 旧的编号置为作废
			beOtherMedicineCounts = append(beOtherMedicineCounts, replaceIDs...)
			_, err = tools.Database.Collection("medicine_others").UpdateMany(sctx,
				bson.M{"_id": bson.M{"$in": replaceIDs}},
				bson.M{"$set": bson.M{"status": 6}},
			)
			if err != nil {
				return nil, errors.WithStack(err)

			}

			replaceNumber = append(replaceNumber, otherDispensingMedicineSinge[0].Name+"/"+strconv.Itoa(drug.ReplaceCount)+"/"+otherDispensingMedicineSinge[0].Batch+"/"+otherDispensingMedicineSinge[0].ExpireDate)
			BeReplaceNumber = append(BeReplaceNumber, drug.Name+"/"+strconv.Itoa(drug.ReplaceCount)+"/"+drug.Batch+"/"+drug.ExpireDate)
			medicineNumber = append(medicineNumber, drug.Name+"/"+strconv.Itoa(drug.ReplaceCount)+"/"+drug.Batch+"/"+drug.ExpireDate)

		}
		mails := make([]models.Mail, 0)
		if project.ResearchAttribute == 1 {
			// DTP 模式下 更新订单信息
			orderNumber := dispensing.Order
			orderNumberNew, err := updateOrderInfoWithDTP(ctx, sctx, orderOID, BeReplaceID, ReplaceID, order, storeOID, subject.ProjectSiteID, reason)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if orderNumberNew != "" {
				orderNumber = orderNumberNew
			}
			err = mailWithDTP(ctx, attribute, subjectOID, envOID, visitInfo, medicineNumber, "dispensing.replace-dtp-title", "dispensing.replace-dtp", replaceNumber, remark, orderNumber, reason, now)
			if err != nil {
				return nil, err
			}
		} else {
			var siteOrder models.MedicineOrder
			var storeOrder models.MedicineOrder
			siteSend := false
			siteSubjectSend := false
			storeSubjectSend := false
			{ // 根据发放类型生成不同订单
				// 通用非DTP模式

				for _, value := range BeDTPMap {
					if value == 0 {
						siteSend = true
					}
					if value == 1 {
						siteSubjectSend = true
					}
					if value == 2 {
						storeSubjectSend = true
					}
				}
				if siteSend {
					_, err = updateOrderInfo(ctx, sctx, dispensing, BeReplaceID, ReplaceID, 0, siteOID, storeOID, siteOrderID, storeOrderID, DTPMap, &histories, otherMedicineCounts, beOtherMedicineCounts)
					if err != nil {
						return nil, errors.WithStack(err)
					}
				}
				if siteSubjectSend {
					siteOrder, err = updateOrderInfo(ctx, sctx, dispensing, BeReplaceID, ReplaceID, 1, siteOID, storeOID, siteOrderID, storeOrderID, DTPMap, &histories, otherMedicineCounts, beOtherMedicineCounts)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					orderNew.OrderNumber = siteOrder.OrderNumber
				}
				if storeSubjectSend {
					storeOrder, err = updateOrderInfo(ctx, sctx, dispensing, BeReplaceID, ReplaceID, 2, siteOID, storeOID, siteOrderID, storeOrderID, DTPMap, &histories, otherMedicineCounts, beOtherMedicineCounts)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					orderNew.OrderNumber = siteOrder.OrderNumber

				}

				subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
				subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)
				mails, err = mail(ctx, mails, dispensing, dispensing.DispensingTime, now, "", visitCycle, visitInfo, medicineNumber, "dispensing.replace-title", replaceNumber, subjectEmailReplaceTextZh, subjectEmailReplaceTextEn, attribute, remark, "", reason)
				if err != nil {
					return nil, err
				}
			}
			if siteSubjectSend || storeSubjectSend {
				// 响应返回的订单信息
				for _, medicine := range medicines {
					orderNumber := ""
					if DTPMap[medicine.ID] == 1 {
						orderNumber = siteOrder.OrderNumber
					}
					if DTPMap[medicine.ID] == 2 {
						orderNumber = storeOrder.OrderNumber
					}
					isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
					if isBlindedRole && isBlindedDrug {
						medicine.Name = tools.BlindData
					}
					resOrderInfo = append(resOrderInfo, models.ResDispensingInfo{
						Name:       medicine.Name,
						Number:     medicine.Number,
						Order:      orderNumber,
						ExpireDate: medicine.ExpirationDate,
					})
				}
				// 未编号药
				for _, medicine := range useOtherDispensingMedicine {
					orderNumber := siteOrder.OrderNumber
					if medicine.DTP != nil && *medicine.DTP == 2 {
						orderNumber = storeOrder.OrderNumber

					}
					count := medicine.Name + "(" + strconv.Itoa(medicine.Count) + ")" // 替换默认1一个药
					resOrderInfo = append(resOrderInfo, models.ResDispensingInfo{
						Name:       medicine.Name,
						Number:     count,
						Order:      orderNumber,
						ExpireDate: medicine.ExpireDate,
					})
				}
			}

		}
		ctx.Set("MAIL", mails)
		if len(mails) > 0 {
			var envs []models.MailEnv
			for _, m := range mails {
				envs = append(envs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     m.ID,
					CustomerID: dispensing.CustomerID,
					ProjectID:  dispensing.ProjectID,
					EnvID:      dispensing.EnvironmentID,
					CohortID:   dispensing.CohortID,
				})
			}
			ctx.Set("MAIL-ENV", envs)
		}
		user := models.User{}
		u, _ := ctx.Get("user")
		if u != nil {
			user = u.(models.User)
		}
		var userName string
		if project.ProjectInfo.ConnectEdc == 1 && project.ProjectInfo.PushMode == 1 && user.Name == "" { // EDC对接项目
			userName = edcUserName
		} else {
			userName = user.Name
		}

		if requestType == 1 {
			userName = "码上放心"
		}

		if project.ResearchAttribute == 1 {
			for i, item := range BeReplaceID {
				histories = append(histories, models.History{
					Key:  "history.medicine.replace-dtp",
					OID:  item,
					Data: map[string]interface{}{"replace": replaceNumber[i], "beReplace": BeReplaceNumber[i]},
					Time: now,
					UID:  user.ID,
					User: userName,
				})
			}
			for _, item := range ReplaceID {
				histories = append(histories, models.History{
					Key:  "history.medicine.apply",
					OID:  item,
					Time: now,
					UID:  user.ID,
					User: userName,
				})
			}
		} else {
			for _, item := range BeReplaceID {
				histories = append(histories, models.History{
					Key:  "history.medicine.sku-lost-subject",
					OID:  item,
					Data: map[string]interface{}{"subject": subject.Info[0].Value, "visit": dispensing.VisitInfo.Name, "operation": 3},
					Time: now,
					UID:  user.ID,
					User: userName,
				})
			}
			history, err := getOtherMedicineHistory(dispensing, othersDispensingMedicine, siteOID, storeOID, "", user, user.Name, now)
			if err != nil {
				return nil, err
			}
			histories = append(histories, history...)

			// 被替换
			history, err = getOtherMedicineHistory(dispensing, newReplaceOthersDispensingMedicines, siteOID, storeOID, "history.medicine.otherLost", user, user.Name, now)
			if err != nil {
				return nil, err
			}
			histories = append(histories, history...)

			if sendType == 0 {
				// 未编号药
				for _, item := range ReplaceID {
					histories = append(histories, models.History{
						Key:  "history.medicine.sku-used-subject",
						Data: map[string]interface{}{"subject": subject.Info[0].Value, "visit": dispensing.VisitInfo.Name, "operation": 3},
						OID:  item,
						Time: now,
						UID:  user.ID,
						User: userName,
					})
				}
			} else {
				for i, item := range ReplaceID {
					histories = append(histories, models.History{
						Key:  "history.medicine.in-order",
						Data: map[string]interface{}{"subject": subject.Info[0].Value, "medicine": replaceNumber[i]},
						OID:  item,
						Time: now,
						UID:  user.ID,
						User: userName,
					})
				}
			}

		}

		//更新后的药物
		var newDispensing models.Dispensing
		err = tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"_id": OID}).Decode(&newDispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var medicineIds []primitive.ObjectID
		if len(newDispensing.DispensingMedicines) > 0 {
			for _, v := range newDispensing.DispensingMedicines {
				medicineIds = append(medicineIds, v.MedicineID)
			}
			//判断app端是否有发药任务，如果有，关闭任务
			var workTask models.WorkTask
			err = tools.Database.Collection("work_task").FindOneAndUpdate(ctx, bson.M{"info.dispensing_id": OID, "info.work_type": 11, "info.status": 0, "deleted": bson.M{"$ne": true}}, bson.M{"$set": bson.M{"info.medicine_ids": medicineIds}}).Decode(&workTask)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
		}

		historyData := map[string]interface{}{"reason": reason, "replaceNumber": replaceNumber, "beReplaceMedicine": BeReplaceNumber, "remark": remark}
		dispensing.DispensingMedicines = []models.DispensingMedicine{}
		dispensing.OtherDispensingMedicines = []models.OtherDispensingMedicine{}
		dispensing.DoseInfo = nil
		dispensing.Order = ""
		if sendType != 0 && orderNew.OrderNumber != "" {
			dispensing.Order = orderNew.OrderNumber
		}
		err = addDispensingHistoryData(ctx, "history.dispensing.dispensingCustomer-replace", &histories, subjectReplaceText, subject, dispensing, models.FormulaInfo{},
			user, userName, now, sendType, nil, models.Form{}, models.FormValue{}, logisticsInfo, historyData, attribute)
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return nil, nil, err
	} else {
		if project.ProjectInfo.OrderCheck == 2 {
			err = task.AlarmMedicineNew(2, envOID)
			if err != nil {
				return nil, nil, errors.WithStack(err)
			}
		}
	}

	// 推给EDC
	if tools.PushScenarioFilter(project.ConnectEdc, project.PushMode, project.EdcSupplier, project.PushScenario.DispensingPush) {
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectDispensingPush(logData, OID, 6, now)
	}
	//if project.ConnectEdc == 1 && project.PushMode == 2 && (project.PushTypeEdc == "" || project.PushTypeEdc == "OnlyDrug" || project.PushTypeEdc == "RandomAndDrug") {
	//	SubjectDispensingPush(ctx, OID, 6, now)
	//}
	return resOrderInfo, replaceCodeDTOS, nil
}

func RetrievalDrugMethod(ctx *gin.Context, id string, data map[string]interface{}, requestType int) error {
	OID, _ := primitive.ObjectIDFromHex(id)
	now := time.Duration(time.Now().Unix())

	var project models.Project
	var dispensing models.Dispensing
	var subject models.Subject

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var medicinesOIDs []primitive.ObjectID
		var otherMedicinesOIDs []primitive.ObjectID
		var othersSkuOID []primitive.ObjectID

		var medicinesNumber []string
		var drugNumber []string
		var histories []models.History
		remark := ""
		if data["remark"] != nil {
			remark = data["remark"].(string)
		}
		u, _ := ctx.Get("user")
		user := u.(models.User)
		var openMedicineOIDs []primitive.ObjectID
		var blindMedicineOIDs []primitive.ObjectID
		err := tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"_id": OID}).Decode(&dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		updatePull := bson.M{}
		for _, medicine := range data["number"].([]interface{}) {
			medicineOID, _ := primitive.ObjectIDFromHex(medicine.(string))
			medicinesOIDs = append(medicinesOIDs, medicineOID)
		}
		// 更新编号药物
		if len(medicinesOIDs) > 0 {
			updatePull["dispensing_medicines"] = bson.M{"medicine_id": bson.M{"$in": medicinesOIDs}}
			// 删除访视上 对应取回的研究产品
			update := bson.M{
				"$pull": bson.M{
					"dispensing_medicines": bson.M{
						"medicine_id": bson.M{"$in": medicinesOIDs},
					},
				},
			}
			_, err := tools.Database.Collection("dispensing").UpdateOne(sctx, bson.M{"_id": OID}, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		if data["other_number_count"] != nil {
			for _, medicine := range data["other_number_count"].([]interface{}) {
				// 使用类型断言将interface{}转换为Person对象
				medicineInfoCount, ok := medicine.(map[string]interface{})
				if !ok {
					return nil, err
				}
				medicineOID, _ := primitive.ObjectIDFromHex(medicineInfoCount["id"].(string))
				otherMedicinesOIDs = append(otherMedicinesOIDs, medicineOID)
			}
		}
		// 更新未编号药物

		if count, _ := tools.Database.Collection("project").CountDocuments(sctx, bson.M{"_id": dispensing.ProjectID, "info.research_attribute": 1}); count == 1 {
			return nil, tools.BuildServerError(ctx, "subject_visit_cannot_cancel")
		}

		tools.Database.Collection("project").FindOne(nil, bson.M{"_id": dispensing.ProjectID}).Decode(&project)
		otherDispensingMedicines := []models.OtherDispensingMedicine{}
		if len(otherMedicinesOIDs) > 0 {
			for _, medicine := range dispensing.OtherDispensingMedicines {
				push := true
				retrievalCount := 1
				retrievalBatch := ""
				drugNumber = append(drugNumber, medicine.Name+"/"+strconv.Itoa(medicine.Count)+"/"+medicine.Batch+"/"+medicine.ExpireDate)
				for _, otherMedicinesOID := range otherMedicinesOIDs {
					if medicine.ID == otherMedicinesOID {
						if data["other_number_count"] != nil {
							for _, me := range data["other_number_count"].([]interface{}) {
								// 使用类型断言将interface{}转换为Person对象
								medicineInfoCount, ok := me.(map[string]interface{})
								if !ok {
									return nil, err
								}
								oid, _ := primitive.ObjectIDFromHex(medicineInfoCount["id"].(string))
								if oid == otherMedicinesOID {
									retrievalCount = int(medicineInfoCount["retrievalCount"].(float64))
									retrievalBatch = medicineInfoCount["batch"].(string)
								}
								//retrievalCount = int(medicineInfoCount["retrievalCount"].(float64))
							}
						}
						medicinesNumber = append(medicinesNumber, medicine.Name+"/"+strconv.Itoa(retrievalCount)+"/"+medicine.Batch+"/"+medicine.ExpireDate)
						//medicinesNumber = append(medicinesNumber, medicine.Name+"/1/"+medicine.Batch+"/"+medicine.ExpireDate)
						if retrievalBatch == medicine.Batch {
							if medicine.Count == retrievalCount {
								push = false
							} else {
								medicine.Count = medicine.Count - retrievalCount
							}
						}

						//
						i := 0
						for _, item := range dispensing.OthersDispensingMedicines {
							if retrievalCount == i {
								break
							}
							if item.BatchNumber == medicine.Batch && item.Name == medicine.Name {
								i++
								othersSkuOID = append(othersSkuOID, item.MedicineID)
							}
						}
						history, err := getOtherMedicineHistory(dispensing, dispensing.OthersDispensingMedicines, subject.ProjectSiteID, primitive.NilObjectID, "history.medicine.otherCanUse", user, user.Name, now)
						if err != nil {
							return nil, err
						}
						histories = append(histories, history...)

						dispensing.OtherMedicinesHistory = append(dispensing.OtherMedicinesHistory,
							models.OtherDispensingMedicineInfo{
								ID:              medicine.ID,
								MedicineOtherID: medicine.MedicineOtherID,
								Name:            medicine.Name,
								Count:           retrievalCount,
								Batch:           medicine.Batch,
								ExpireDate:      medicine.ExpireDate,
								Time:            now,
								Type:            6,
							})

					}
				}
				if push {
					otherDispensingMedicines = append(otherDispensingMedicines, medicine)
				}

			}
			_, err = tools.Database.Collection("dispensing").UpdateOne(sctx, bson.M{"_id": OID}, bson.M{
				"$set": bson.M{"other_dispensing_medicines": otherDispensingMedicines, "other_medicines_history": dispensing.OtherMedicinesHistory},
				"$pull": bson.M{
					"others_dispensing_medicines": bson.M{
						"medicine_id": bson.M{"$in": othersSkuOID},
					},
				}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			_, err := tools.Database.Collection("medicine_others").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": othersSkuOID}},
				bson.M{"$set": bson.M{"status": 1}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else {
			otherDispensingMedicines = dispensing.OtherDispensingMedicines
		}

		// 校验 受试者是否已经揭盲 退出 替换
		status, err := checkSubjectStatus(ctx, sctx, dispensing.SubjectID)
		if err != nil {
			return nil, err
		}
		if !status {
			return nil, tools.BuildServerError(ctx, "subject_status_no_retrieval")
		}

		// 查询项目属性配置
		var attribute models.Attribute
		if dispensing.CohortID != primitive.NilObjectID {
			err = tools.Database.Collection("attribute").FindOne(sctx, bson.M{"env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID}).Decode(&attribute)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else {
			err = tools.Database.Collection("attribute").FindOne(sctx, bson.M{"env_id": dispensing.EnvironmentID}).Decode(&attribute)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

		visit := 0
		if dispensing.VisitSign {
			visit = 1
		}
		if dispensing.Reissue == 1 {
			visit = 2
		}
		historyData := bson.M{"subject": subject.Info[0].Value, "visit": dispensing.VisitInfo.Name, "type": visit, "operation": 2}

		// 更新研究产品状态(1.开放项目为可用所有受试者均可用 2.盲态项目为冻结并绑定受试者) 开放药物设置为可用
		if len(medicinesOIDs) > 0 {
			// 获取取回研究产品编号，记录轨迹
			var medicines []models.Medicine
			cursor, err := tools.Database.Collection("medicine").Find(sctx, bson.M{"_id": bson.M{"$in": medicinesOIDs}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &medicines)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			// 旧数据 开放项目 + 盲态药物。 药物作废不绑定， 药物设为可用
			oldOpenProject, err := tools.Database.Collection("setting_config").CountDocuments(nil, bson.M{"key": "open-project", "data": project.ProjectInfo.Number})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if oldOpenProject == 1 && attribute.AttributeInfo.Blind { // 如果当前cohort属性为盲态
				oldOpenProject = 0
			}
			for _, medicine := range medicines {
				// isOpenDrug, err := tools.IsOpenDrug(dispensing.EnvironmentID, dispensing.CohortID, medicine.Name)
				// if err != nil {
				// 	return nil, errors.WithStack(err)
				// }
				// if isOpenDrug {
				// 	openMedicineOIDs = append(openMedicineOIDs, medicine.ID)
				// } else {
				// 	blindMedicineOIDs = append(blindMedicineOIDs, medicine.ID)
				// }
				isBlindedDrug, err := tools.IsBlindedDrug(dispensing.EnvironmentID, medicine.Name)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				if isBlindedDrug && oldOpenProject == 0 {
					blindMedicineOIDs = append(blindMedicineOIDs, medicine.ID)
				} else {
					openMedicineOIDs = append(openMedicineOIDs, medicine.ID)
				}
				medicinesNumber = append(medicinesNumber, medicine.Number)
				drugNumber = append(drugNumber, medicine.Number)

			}

			medicineUpdate := bson.M{}
			if len(openMedicineOIDs) > 0 {
				medicineUpdate = bson.M{"$set": bson.M{"status": 1, "subject_id": primitive.NilObjectID}}
				_, err = tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": openMedicineOIDs}}, medicineUpdate)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				for _, ID := range openMedicineOIDs {
					histories = append(histories, models.History{
						Key:  "history.medicine.sku-use-subject",
						Data: historyData,
						OID:  ID,
						Time: now,
						UID:  user.ID,
						User: user.Name,
					})
				}

			}
			if len(blindMedicineOIDs) > 0 {
				medicineUpdate = bson.M{"$set": bson.M{"status": 14, "subject_id": dispensing.SubjectID}}
				_, err = tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": blindMedicineOIDs}}, medicineUpdate)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				for _, ID := range blindMedicineOIDs {
					histories = append(histories, models.History{
						Key:  "history.medicine.sku-freeze-subject",
						Data: historyData,
						OID:  ID,
						Time: now,
						UID:  user.ID,
						User: user.Name,
					})
				}
			}
		}
		var dispensingMedicines []models.DispensingMedicine
		for _, medicine := range dispensing.DispensingMedicines {
			for _, medicinesOID := range medicinesOIDs {
				if medicinesOID == medicine.MedicineID {
					dispensing.CancelMedicinesHistory = append(dispensing.CancelMedicinesHistory, medicine)
					medicine.Time = now
					medicine.Type = 6
					// 取回后的数据
					dispensingMedicines = append(dispensingMedicines, medicine)
				}
			}
		}

		dispensing.CancelMedicinesHistory = append(dispensing.CancelMedicinesHistory, dispensingMedicines...)
		// 判断是否 取回全部编号研究产品
		if len(dispensing.DispensingMedicines) == len(medicinesOIDs) {
			//判断app端是否有发药任务，如果有，关闭任务

			determine := bson.M{
				"project_id":  dispensing.ProjectID,
				"env_id":      dispensing.EnvironmentID,
				"customer_id": dispensing.CustomerID,
				"subject_id":  dispensing.SubjectID,
			}
			if dispensing.CohortID != primitive.NilObjectID {
				determine = bson.M{
					"project_id":  dispensing.ProjectID,
					"env_id":      dispensing.EnvironmentID,
					"customer_id": dispensing.CustomerID,
					"cohort_id":   dispensing.CohortID,
					"subject_id":  dispensing.SubjectID,
				}
			}
			dispensingList := make([]*models.Dispensing, 0)
			cursor, err := tools.Database.Collection("dispensing").Find(nil, determine)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &dispensingList)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			//隐藏还未完成的app发药任务
			if dispensingList != nil && len(dispensingList) > 0 {
				for _, dis := range dispensingList {
					var workTask models.WorkTask
					err = tools.Database.Collection("work_task").FindOneAndUpdate(ctx, bson.M{"info.dispensing_id": dis.ID, "info.work_type": 11, "info.status": 0, "deleted": bson.M{"$ne": true}}, bson.M{"$set": bson.M{"deleted": true}}).Decode(&workTask)
					if err != nil && err != mongo.ErrNoDocuments {
						return nil, errors.WithStack(err)
					}
				}
			}
		} else {
			// 删除任务上对应取回的研究产品
			if len(medicinesOIDs) > 0 {
				workTaskUpdate := bson.M{
					"$pull": bson.M{
						"info.medicine_ids": bson.M{"$in": medicinesOIDs},
					},
				}
				_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"info.dispensing_id": OID, "info.work_type": 11, "info.status": 0, "deleted": false}, workTaskUpdate)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
			}
		}
		// 判断是否 取回全部研究产品
		if len(dispensing.DispensingMedicines) == len(medicinesOIDs) && len(otherDispensingMedicines) == 0 && len(dispensing.RealDispensingMedicines) == 0 {
			update := bson.M{
				"$set": bson.M{
					"dispensing_time": nil,
					"status":          1,
					"work_task_id":    nil,
				},
			}
			update["$set"].(bson.M)["cancel_medicines_history"] = dispensing.CancelMedicinesHistory
			_, err := tools.Database.Collection("dispensing").UpdateOne(sctx, bson.M{"_id": OID}, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			//全部取回-更新app发药任务为未完成且完成时间为空
			checkCondition := bson.M{
				"customer_id":        subject.CustomerID,
				"project_id":         subject.ProjectID,
				"env_id":             subject.EnvironmentID,
				"cohort_id":          subject.CohortID,
				"info.work_type":     11,
				"info.dispensing_id": dispensing.ID,
				"info.status":        1,
				"deleted":            bson.M{"$ne": true},
			}
			var workTask models.WorkTask
			err = tools.Database.Collection("work_task").FindOneAndUpdate(ctx, checkCondition, bson.M{"$set": bson.M{"info.status": 0, "info.finish_time": nil}}).Decode(&workTask)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}

		} else {
			update := bson.M{
				"$set": bson.M{
					"cancel_medicines_history": dispensing.CancelMedicinesHistory,
				},
			}
			_, err := tools.Database.Collection("dispensing").UpdateOne(sctx, bson.M{"_id": OID}, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		//判断首次发药取回，判断入组已满状态
		if !attribute.AttributeInfo.Random && attribute.AttributeInfo.Dispensing {
			var cohort models.Cohort
			var env models.Environment
			envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
				return item.ID == subject.EnvironmentID
			})
			env = *envP
			//查询条件
			filter := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
			if subject.CohortID != primitive.NilObjectID {
				filter = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}

				cohortP, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
					return item.ID == subject.CohortID
				})
				cohort = *cohortP
			}
			currentDispensingCount, err := tools.Database.Collection("dispensing").CountDocuments(sctx, bson.M{"subject_id": subject.ID, "status": bson.M{"$in": []int{2, 3}}})
			if err != nil {
				return 0, errors.WithStack(err)
			}
			count, err := tools.CountSubject(sctx, attribute, filter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if project.ProjectInfo.Type == 1 {
				find, b := slice.Find(env.AlertThresholds, func(index int, item models.AlertThreshold) bool {
					return item.Type == 3
				})
				if currentDispensingCount == 0 && *env.Status == 5 && (b && count >= find.Capacity) {
					opts := &options.UpdateOptions{
						ArrayFilters: &options.ArrayFilters{
							Filters: bson.A{bson.M{"env.id": subject.EnvironmentID}},
						},
					}
					projectUpdate := bson.M{"$set": bson.M{"envs.$[env].status": 2}}
					if _, err := tools.Database.Collection("project").UpdateOne(sctx, bson.M{"_id": subject.ProjectID}, projectUpdate, opts); err != nil {
						return nil, errors.WithStack(err)
					}
				}
			} else {
				find, b := slice.Find(cohort.AlertThresholds, func(index int, item models.AlertThreshold) bool {
					return item.Type == 3
				})

				if currentDispensingCount == 0 && cohort.Status == 5 && (b && count < find.Capacity) {
					opts := &options.UpdateOptions{
						ArrayFilters: &options.ArrayFilters{
							Filters: bson.A{bson.M{"env.id": subject.EnvironmentID}, bson.M{"cohort.id": subject.CohortID}},
						},
					}
					projectUpdate := bson.M{"$set": bson.M{"envs.$[env].cohorts.$[cohort].status": 2}}
					if _, err := tools.Database.Collection("project").UpdateOne(sctx, bson.M{"_id": subject.ProjectID}, projectUpdate, opts); err != nil {
						return nil, errors.WithStack(err)
					}
				}
			}
		}
		sort.Strings(medicinesNumber)
		sort.Strings(drugNumber)
		sortDispensingMedicines := sortTypeMedicineNumber(dispensing.DispensingMedicines)
		dispensing.DispensingMedicines = sortDispensingMedicines
		histroryData := bson.M{"retrievalMedicine": medicinesNumber, "remark": remark}
		key := "history.dispensing.dispensingCustomer-retrieval"
		dispensing.DispensingMedicines = []models.DispensingMedicine{}
		dispensing.OtherDispensingMedicines = []models.OtherDispensingMedicine{}
		dispensing.DoseInfo = nil
		if requestType == 1 {
			user.Name = "码上放心"
		}
		err = addDispensingHistoryData(ctx, key, &histories, subjectReplaceText, subject, dispensing, models.FormulaInfo{},
			user, user.Name, now, 0, nil, models.Form{}, models.FormValue{}, models.LogisticsInfo{}, histroryData, attribute)

		ctx.Set("HISTORY", histories)
		visitCycle, visitInfo, err := getVisitInfo(dispensing.CustomerID, dispensing.ProjectID, dispensing.EnvironmentID, dispensing.CohortID, dispensing.VisitInfo.VisitCycleInfoID)

		mails := make([]models.Mail, 0)
		subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
		subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)
		mails, err = mail(ctx, mails, dispensing, dispensing.DispensingTime, now, "", visitCycle, visitInfo, medicinesNumber, "dispensing.retrieval-title", drugNumber, subjectEmailReplaceTextZh, subjectEmailReplaceTextEn, attribute, remark, "", "")
		if err != nil {
			return nil, err
		}
		ctx.Set("MAIL", mails)
		if len(mails) > 0 {
			var envs []models.MailEnv
			for _, m := range mails {
				envs = append(envs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     m.ID,
					CustomerID: dispensing.CustomerID,
					ProjectID:  dispensing.ProjectID,
					EnvID:      dispensing.EnvironmentID,
					CohortID:   dispensing.CohortID,
				})
			}
			ctx.Set("MAIL-ENV", envs)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	// 推给EDC
	if tools.PushScenarioFilter(project.ConnectEdc, project.PushMode, project.EdcSupplier, project.PushScenario.DispensingPush) {
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectDispensingPush(logData, OID, 9, now)
	}
	//if project.ConnectEdc == 1 && project.PushMode == 2 && (project.PushTypeEdc == "" || project.PushTypeEdc == "OnlyDrug" || project.PushTypeEdc == "RandomAndDrug") {
	//	SubjectDispensingPush(ctx, OID, 9, now)
	//}
	callbackNotice := func(sctx mongo.SessionContext) (interface{}, error) {
		err = task.UpdateNotice(sctx, 4, dispensing.EnvironmentID, dispensing.CohortID, subject.ProjectSiteID, subject.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err = tools.Transaction(callbackNotice)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func getDTPInfo(list []models.DtpIp, values []models.DrugValue, name string) []int {
	dtps := []int{0}
	if name != "" {
		dtpP, ok := slice.Find(list, func(index int, item models.DtpIp) bool {
			return item.IP == name
		})
		if ok {
			dtp := *dtpP
			dtpValue := slice.Map(dtp.DtpTypeList, func(index int, item int) int {
				return item - 1
			})
			dtps = dtpValue
		}
	} else {
		tmpDTP := make([][]int, 0)

		for _, value := range values {
			dtpP, ok := slice.Find(list, func(index int, item models.DtpIp) bool {
				return item.IP == value.DrugName
			})
			if ok {
				dtp := *dtpP
				xx := slice.Map(dtp.DtpTypeList, func(index int, item int) int {
					return item - 1
				})
				tmpDTP = append(tmpDTP, xx)

			} else {
				tmpDTP = append(tmpDTP, []int{0})
			}
		}
		dtps = slice.Intersection(tmpDTP...)

		// 组合标签并集DTP发放方式

	}
	return dtps
}

func (s *DispensingService) OrderSend(ctx *gin.Context, sctx mongo.SessionContext, isBlindedRole bool, subject models.Subject, dispensing models.Dispensing, sendType int, sourceSend, orderOID primitive.ObjectID) ([]models.ResDispensingInfo, error) {

	resOrderInfo := make([]models.ResDispensingInfo, 0)
	orderInfo, err := setDispensingOrder(ctx, sctx, subject, dispensing, sendType, sourceSend, orderOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var receiveOrder models.RecevieOrderView
	var receiveOtherMedicineCounts []models.ReceiveOtherMedicineCountView

	// 不同发放方式过滤订单
	dispensing.DispensingMedicines = slice.Filter(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
		return item.DTP != nil && *item.DTP == sendType
	})
	dispensing.OthersDispensingMedicines = slice.Filter(dispensing.OthersDispensingMedicines, func(index int, item models.OthersDispensingMedicine) bool {
		return item.DTP != nil && *item.DTP == sendType
	})

	// 响应返回的订单信息
	for _, medicine := range dispensing.DispensingMedicines {
		// isOpenDrug, _ := tools.IsOpenDrug(envOID, cohortOID, medicine.Name)
		// isOtherDrug, _ := tools.IsOtherDrug(envOID, medicine.Name)
		// if isBlindedRole && attribute.AttributeInfo.Blind && !isOpenDrug && !isOtherDrug {
		// 	medicine.Name = tools.BlindData
		// }
		isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
		if isBlindedRole && isBlindedDrug {
			medicine.Name = tools.BlindData
		}
		resOrderInfo = append(resOrderInfo, models.ResDispensingInfo{
			Name:       medicine.Name,
			Number:     medicine.Number,
			Order:      orderInfo.OrderNumber,
			ExpireDate: medicine.ExpirationDate,
		})
	}
	for _, medicine := range dispensing.OtherDispensingMedicines {
		isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
		if isBlindedRole && isBlindedDrug {
			medicine.Name = tools.BlindData
		}
		receiveOtherMedicineCount := models.ReceiveOtherMedicineCountView{
			Name:       medicine.Name,
			Batch:      medicine.Batch,
			ExpireDate: medicine.ExpireDate,
			UseCount:   medicine.Count,
		}
		receiveOtherMedicineCounts = append(receiveOtherMedicineCounts, receiveOtherMedicineCount)
		countStr := strconv.Itoa(medicine.Count)
		count := medicine.Name + "(" + countStr + ")"
		resOrderInfo = append(resOrderInfo, models.ResDispensingInfo{
			Name:       medicine.Name,
			Number:     count,
			Order:      orderInfo.OrderNumber,
			ExpireDate: medicine.ExpireDate,
		})
	}
	// 发送邮件

	receiveOrder.OtherMedicines = receiveOtherMedicineCounts
	receiveOrder.Medicines = orderInfo.Medicines
	err = s.medicineOrderService.SendOrderMail(ctx, orderInfo, receiveOrder, "order.medicine_order_title", dispensing, false, "order.medicine_order_title")
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return resOrderInfo, nil
}

func getOtherMedicineHistory(dispensing models.Dispensing, medicines []models.OthersDispensingMedicine, siteOID, storeOID primitive.ObjectID, key string, user models.User, userName string, now time.Duration) ([]models.History, error) {
	histories := []models.History{}
	otherKeyMatch := bson.M{"customer_id": dispensing.CustomerID, "project_id": dispensing.ProjectID, "env_id": dispensing.EnvironmentID,
		"$or": bson.A{
			bson.M{"site_id": siteOID},
			bson.M{"storehouse_id": storeOID},
		},
	}
	var medicineOtherKeys []models.MedicineOtherKey
	cursor, err := tools.Database.Collection("medicine_other_key").Find(nil, otherKeyMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &medicineOtherKeys)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	groupMedicine := slice.GroupWith(medicines, func(t models.OthersDispensingMedicine) string {
		useID := siteOID
		dtp := 0
		if t.DTP != nil {
			dtp = *t.DTP
			if *t.DTP == 2 {
				useID = storeOID

			}
		}
		return convertor.ToString(dtp) + t.Name + "-" + t.BatchNumber + "-" + t.ExpirationDate + "-" + useID.Hex()
	})

	for _, t := range medicineOtherKeys {
		useID := t.SiteID
		if useID.IsZero() {
			useID = t.StorehouseID
		}

		dtps := []int{0, 1, 2}
		for _, item := range dtps {
			if len(groupMedicine[convertor.ToString(item)+t.Name+"-"+t.Batch+"-"+t.ExpireDate+"-"+useID.Hex()]) > 0 {

				if key == "" { // 发药使用通用key.  登记使用传进来的key
					key = "history.medicine.otherUse"
					if item != 0 {
						key = "history.medicine.otherToBeConfirm"

					}
				}

				history := models.History{
					Key:  key,
					OID:  t.ID,
					Data: bson.M{"name": t.Name, "batch": t.Batch, "expireDate": t.ExpireDate, "count": len(groupMedicine[convertor.ToString(item)+t.Name+"-"+t.Batch+"-"+t.ExpireDate+"-"+useID.Hex()])},
					Time: now,
					UID:  user.ID,
					User: userName,
				}
				histories = append(histories, history)
			}
		}

	}

	return histories, nil
}

func DoseOpenCheck(subject models.Subject, drugConfigureSetting models.DrugConfigureSetting, dispensing []models.ResDispensing, types int) (map[string]bool, bool) {
	// 7574 剂量判断后续访视是否发药
	_, ok := slice.Find(drugConfigureSetting.GroupType, func(index int, item models.GroupType) bool {
		return item.Group == subject.Group && item.Type == types
	})
	// 当前组别需要控制访视停止发放
	visitInheritanceCount := 0
	doseDispensing := models.ResDispensing{}
	if ok {
		for _, judgment := range drugConfigureSetting.VisitJudgmentList {
			if judgment.VisitInheritance && judgment.VisitInheritanceCount != nil && *judgment.VisitInheritanceCount > 0 {
				// 选了当前选项 && 当前选项有继承次数
				dispensingP, optionOK := slice.Find(dispensing, func(index int, item models.ResDispensing) bool {
					return item.DoseInfo != nil && item.DoseInfo.VisitJudgmentList != nil && item.DoseInfo.VisitJudgmentList.Name == judgment.Name && item.Status == 2
				})
				if optionOK {
					doseDispensing = *dispensingP
					visitInheritanceCount = *judgment.VisitInheritanceCount
					break
				}
			}
		}
	} else {

	}

	// 限制次数大于0
	visitNumbers := map[string]bool{}
	if visitInheritanceCount > 0 {
		startCount := false // 开始计算标记
		for _, resDispensing := range dispensing {
			if resDispensing.VisitSign {
				continue
			}
			if resDispensing.VisitInfo.Number == doseDispensing.VisitInfo.Number {
				startCount = true
				continue
			}
			if startCount {
				if len(visitNumbers) >= visitInheritanceCount {
					break
				}
				visitNumbers[resDispensing.VisitInfo.Number] = true // 可以继续发放的访视
			}

		}
	}

	return visitNumbers, visitInheritanceCount > 0
}

// IsFirstDispensingIPInheritance 是否随机首次发药  继承发药的药物信息
func IsFirstDispensingIPInheritance(subject models.Subject, attribute models.Attribute, dispensings []models.Dispensing, visitCycles []models.VisitCycle, visitCycle models.VisitCycle) (map[string]string, string, error) {
	var err error
	var lastDate string
	nameBatch := map[string]string{} // 药物名称 继承的批次

	if !attribute.AttributeInfo.IPInheritance {
		return nil, "", nil
	}
	dispensingP, ok := slice.Find(dispensings, func(index int, item models.Dispensing) bool {
		return item.Status == 2
	})
	dispensing := models.Dispensing{}
	if ok {
		dispensing = *dispensingP
	}
	// 随机后
	if subject.Group != "" {
		dispensing = models.Dispensing{}
		isRandom := false
		for _, item := range dispensings {
			if item.VisitInfo.Random {
				isRandom = true
			}
			if isRandom && item.Status == 2 {
				dispensing = item
			}
		}
	}

	if !dispensing.ID.IsZero() {
		useMap := map[string][]string{}
		for _, medicine := range dispensing.DispensingMedicines {
			useMap[medicine.Name] = append(useMap[medicine.Name], medicine.BatchNumber)
		}
		for _, medicine := range dispensing.OthersDispensingMedicines {
			useMap[medicine.Name] = append(useMap[medicine.Name], medicine.BatchNumber)
		}
		for key, value := range useMap {
			value = slice.Unique(value)
			if len(value) == 1 {
				nameBatch[key] = value[0]
			}
		}
	} else {
		subjectMap := make(map[string]models.Subject)
		for _, value := range visitCycles {
			if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
				subjectMap, err = task.GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")
			}
		}

		lastDate, err = getHandleLastDate(dispensings, subject, subjectMap, visitCycle, attribute)

	}
	return nameBatch, lastDate, err
}

// getHandleLastDate 获取最后一个访视的访视周期
func getHandleLastDate(dispensing []models.Dispensing, subject models.Subject, subjectMap map[string]models.Subject, visitCycle models.VisitCycle, attribute models.Attribute) (string, error) {

	//
	visitCycleMap := map[string]models.VisitCycleInfo{}
	for _, info := range visitCycle.Infos {
		visitCycleMap[info.Number] = info
	}
	strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if strTimeZone == "" {
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return "", err
		}
		//strTimeZone = fmt.Sprintf("UTC%+d", zone)
		strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	//intTimeZone, _ := strconv.ParseFloat(strings.Replace(strTimeZone, "UTC", "", 1), 64)
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

	lastTime := time.Duration(0)
	firstTime := time.Duration(0)
	randomTime := subject.RandomTime
	afterRandom := false
	interval := float64(0)
	var period models.Period

	// TODO 7064
	if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
		randomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
	}

	setDispensingTime := time.Duration(0)

	for _, resDispensing := range dispensing {
		if resDispensing.VisitInfo.Dispensing && setDispensingTime == 0 {
			// lastdate 将第一次发药的时间置为当前
			setDispensingTime = time.Duration(time.Now().Unix())
			resDispensing.DispensingTime = time.Duration(time.Now().Unix())
		}
		if !resDispensing.VisitSign {
			if firstTime == 0 && resDispensing.DispensingTime != 0 && visitCycleMap[resDispensing.VisitInfo.Number].Interval != nil && randomTime == 0 {
				randomTime = time.Duration(time.Unix(int64(resDispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleMap[resDispensing.VisitInfo.Number].Interval)).Unix())
				firstTime = resDispensing.DispensingTime
			}

			if firstTime == 0 && resDispensing.DispensingTime != 0 && visitCycleMap[resDispensing.VisitInfo.Number].Interval != nil && randomTime == 0 {
				randomTime = time.Duration(time.Unix(int64(resDispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleMap[resDispensing.VisitInfo.Number].Interval)).Unix())
				firstTime = resDispensing.DispensingTime
			}
			period = handlePeriod(afterRandom, visitCycle.VisitType, visitCycleMap[resDispensing.VisitInfo.Number], randomTime, lastTime, resDispensing.DispensingTime, intTimeZone, &interval, attribute, subject.JoinTime)
			if resDispensing.Status == 2 {
				interval = 0
				lastTime = resDispensing.DispensingTime
			}
		}
	}

	date := period.LineTime
	if attribute.AttributeInfo.RemainingVisit != nil {

		hours := time.Duration(intTimeZone)
		minutes := time.Duration((intTimeZone - float64(hours)) * 60)

		duration := hours*time.Hour + minutes*time.Minute
		if visitCycle.VisitType == 0 { //basedate 用随机时间计算 剩余周期的日期
			tmpDate := time.Unix(int64(randomTime), 0).Add(time.Hour * 24 * time.Duration(*attribute.AttributeInfo.RemainingVisit)).Add(duration).Format("2006-01-02")
			if tmpDate < date {
				date = tmpDate
			}
		} else { //lastdate 用首次发药 剩余周期的日期
			tmpDate := time.Unix(int64(setDispensingTime), 0).Add(time.Hour * 24 * time.Duration(*attribute.AttributeInfo.RemainingVisit)).Add(duration).Format("2006-01-02")
			if tmpDate < date {
				date = tmpDate
			}
		}
	}

	return date, nil
}

// alarmCapacityInfo 批次组别对应的人数 以及实际人数
func alarmCapacityInfo(sctx mongo.SessionContext, project models.Project, envOID primitive.ObjectID, projectSite models.ProjectSite, subject models.Subject) (map[int]map[string]models.WarnCapacityActual, error) {
	alarmBatchMap := map[int]map[string]models.WarnCapacityActual{} //

	var projectSites []models.ProjectSite
	var subjectDispensing []models.SubjectDispensing
	match := bson.M{"env_id": envOID, "project_site_id": projectSite.ID, "deleted": bson.M{"$ne": true}}

	_, cohortInfo := database.GetEnvCohortInfo(project, envOID, subject.CohortID)
	if cohortInfo.Name != "" {
		cohortInfo.Name = cohortInfo.Name + "-"
	}
	if len(projectSite.StoreHouseID) != 0 {
		cursor, err := tools.Database.Collection("project_site").Find(nil, bson.M{"storehouse_id": projectSite.StoreHouseID})
		if err != nil {
			return alarmBatchMap, errors.WithStack(err)
		}
		err = cursor.All(nil, &projectSites)
		if err != nil {
			return alarmBatchMap, errors.WithStack(err)
		}
		IDs := slice.Map(projectSites, func(index int, item models.ProjectSite) primitive.ObjectID {
			return item.ID
		})
		match = bson.M{"env_id": envOID, "project_site_id": bson.M{"$in": IDs}, "deleted": bson.M{"$ne": true}}

		cursor, err = tools.Database.Collection("subject").Aggregate(sctx, mongo.Pipeline{
			{{"$match", match}},
			{{Key: "$lookup", Value: bson.M{
				"from": "dispensing",
				"let":  bson.M{"subject_id": "$_id"},
				"pipeline": bson.A{
					bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$subject_id", "$$subject_id"}}}},
					bson.M{"$sort": bson.D{{"serial_number", 1}}},
				},
				"as": "dispensing",
			}}},
		})
		if err != nil {
			return alarmBatchMap, errors.WithStack(err)
		}
		err = cursor.All(nil, &subjectDispensing)
		if err != nil {
			return alarmBatchMap, errors.WithStack(err)
		}
	} else {
		match = bson.M{"env_id": envOID, "project_site_id": projectSite.ID, "deleted": bson.M{"$ne": true}}
		cursor, err := tools.Database.Collection("subject").Aggregate(sctx, mongo.Pipeline{
			{{"$match", match}},
			{{Key: "$lookup", Value: bson.M{
				"from": "dispensing",
				"let":  bson.M{"subject_id": "$_id"},
				"pipeline": bson.A{
					bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$subject_id", "$$subject_id"}}}},
					bson.M{"$sort": bson.D{{"serial_number", 1}}},
				},
				"as": "dispensing",
			}}},
		})
		if err != nil {
			return alarmBatchMap, errors.WithStack(err)
		}
		err = cursor.All(nil, &subjectDispensing)
		if err != nil {
			return alarmBatchMap, errors.WithStack(err)
		}
	}

	{ //  库房 批次人数
		var depotBatchGroup models.DepotBatchGroup
		err := tools.Database.Collection("depot_batch_group").FindOne(nil, bson.M{"env_id": envOID}).Decode(&depotBatchGroup)
		if err != nil && err != mongo.ErrNoDocuments {
			return alarmBatchMap, errors.WithStack(err)
		}
		batchGroupAlarmP, ok := slice.Find(depotBatchGroup.BatchGroupAlarm, func(index int, item models.DepotBatchGroupAlarm) bool {
			return len(projectSite.StoreHouseID) == 1 && item.Depot == projectSite.StoreHouseID[0]
		})

		if ok {
			batchGroupAlarm := *batchGroupAlarmP
			tmpMap := map[string]models.WarnCapacityActual{}
			tmpSubjectDispensing := slice.Filter(subjectDispensing, func(index int, item models.SubjectDispensing) bool {
				return item.Group == subject.Group && item.CohortID == subject.CohortID
			})

			for _, alarm := range batchGroupAlarm.Info {
				for _, groupAlarm := range alarm.Info {
					if groupAlarm.Group == cohortInfo.Name+subject.Group {
						groupBatchCount := slice.Count(tmpSubjectDispensing, func(index int, item models.SubjectDispensing) bool {
							for _, dispensing := range item.Dispensing {
								_, ok := slice.Find(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
									dtp := 1
									if item.DTP != nil && *item.DTP == 2 {
										dtp = 2
									}
									return item.BatchNumber == alarm.Batch && dtp == 2
								})
								_, otherOk := slice.Find(dispensing.OtherDispensingMedicines, func(index int, item models.OtherDispensingMedicine) bool {
									dtp := 1
									if item.DTP != nil && *item.DTP == 2 {
										dtp = 2
									}
									return item.Batch == alarm.Batch && dtp == 2
								})
								if ok || otherOk {
									return true
								}
							}
							return false
						})

						// 当前组别受试者使用该批次的数量
						tmpMap[alarm.Batch] = models.WarnCapacityActual{
							Warn:     groupAlarm.Warn,
							Capacity: groupAlarm.Capacity,
							Actual:   groupBatchCount,
						}
					}

				}
			}
			alarmBatchMap[2] = tmpMap

		}
	}

	{ // 中心 批次人数
		if projectSite.BatchGroupAlarmOpen {
			tmpSubjectDispensing := slice.Filter(subjectDispensing, func(index int, item models.SubjectDispensing) bool {
				return item.Group == subject.Group && item.CohortID == subject.CohortID && subject.ProjectSiteID == item.ProjectSiteID
			})
			tmpMap := map[string]models.WarnCapacityActual{}

			for _, alarm := range projectSite.BatchGroupAlarm {
				for _, groupAlarm := range alarm.Info {
					if groupAlarm.Group == cohortInfo.Name+subject.Group {
						groupBatchCount := slice.Count(tmpSubjectDispensing, func(index int, item models.SubjectDispensing) bool {
							for _, dispensing := range item.Dispensing {
								_, ok := slice.Find(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
									dtp := 1
									if item.DTP != nil && *item.DTP == 2 {
										dtp = 2
									}
									return item.BatchNumber == alarm.Batch && dtp == 1
								})
								_, otherOk := slice.Find(dispensing.OtherDispensingMedicines, func(index int, item models.OtherDispensingMedicine) bool {
									dtp := 1
									if item.DTP != nil && *item.DTP == 2 {
										dtp = 2
									}
									return item.Batch == alarm.Batch && dtp == 1
								})
								if ok || otherOk {
									return true
								}
							}
							return false
						})
						tmpMap[alarm.Batch] = models.WarnCapacityActual{
							Warn:     groupAlarm.Warn,
							Capacity: groupAlarm.Capacity,
							Actual:   groupBatchCount,
						}
					}
				}
			}
			alarmBatchMap[1] = tmpMap
		}

	}
	return alarmBatchMap, nil

}

// getDispensingSort 通用 当前受试者的发药信息 按serial_number顺序
func getDispensingSort(sctx mongo.SessionContext, subjectOID primitive.ObjectID) ([]models.Dispensing, error) {

	/*
		按序号顺序查询发药记录
		if visitSign== True  (访视外发药)
			当前访视 == 发药记录最后一条已发药访视
		if visitSign== false  (访视发药)
			当前访视 == 可发药的访视的最前一条发药访视
	*/
	var dispensings []models.Dispensing
	cursor, err := tools.Database.Collection("dispensing").Aggregate(sctx, mongo.Pipeline{
		{{"$match", bson.M{"subject_id": subjectOID}}},
		{{"$sort", bson.M{"serial_number": 1}}},
	})
	if err != nil {
		return dispensings, errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensings)
	if err != nil {
		return dispensings, errors.WithStack(err)
	}
	return dispensings, errors.WithStack(err)

}

// noticeBatchAlarmMail 根本发药结果 判断批次是否需要警戒通知
func noticeBatchAlarmMail(ctx *gin.Context, mail *[]models.Mail, info map[int]map[string]models.WarnCapacityActual, dispensing models.Dispensing, project models.Project, projectSite models.ProjectSite) error {

	storeAlert := []string{}
	storeLimit := []string{}
	siteAlert := []string{}
	siteLimit := []string{}

	for _, medicine := range dispensing.DispensingMedicines {
		dtp := 1
		if medicine.DTP != nil && *medicine.DTP == 2 {
			dtp = 2
		}
		actual := info[dtp][medicine.BatchNumber].Actual + 1

		if info[dtp] != nil && actual == info[dtp][medicine.BatchNumber].Warn {
			if dtp == 1 {
				siteAlert = append(siteAlert, medicine.BatchNumber+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Warn)+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Warn))
			} else {
				storeAlert = append(storeAlert, medicine.BatchNumber+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Warn)+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Warn))
			}
		}
		if info[dtp] != nil && actual == info[dtp][medicine.BatchNumber].Capacity {
			if dtp == 1 {
				siteLimit = append(siteLimit, medicine.BatchNumber+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Capacity)+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Capacity))
			} else {
				storeLimit = append(storeLimit, medicine.BatchNumber+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Capacity)+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Capacity))
			}
		}

	}
	for _, medicine := range dispensing.OthersDispensingMedicines {
		dtp := 1
		if medicine.DTP != nil && *medicine.DTP == 2 {
			dtp = 2
		}
		if info[dtp][medicine.BatchNumber].Actual+1 == info[dtp][medicine.BatchNumber].Warn {
			if dtp == 1 {
				siteAlert = append(siteAlert, medicine.BatchNumber+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Warn)+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Warn))
			} else {
				storeAlert = append(storeAlert, medicine.BatchNumber+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Warn)+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Warn))
			}
		}
		if info[dtp][medicine.BatchNumber].Actual+1 == info[dtp][medicine.BatchNumber].Capacity {
			if dtp == 1 {
				siteLimit = append(siteLimit, medicine.BatchNumber+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Capacity)+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Capacity))
			} else {
				storeLimit = append(storeLimit, medicine.BatchNumber+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Capacity)+"/"+convertor.ToString(info[dtp][medicine.BatchNumber].Capacity))
			}
		}
	}
	storeAlert = slice.Unique(storeAlert)
	storeLimit = slice.Unique(storeLimit)
	siteAlert = slice.Unique(siteAlert)
	siteLimit = slice.Unique(siteLimit)
	envInfo, cohortInfo := database.GetEnvCohortInfo(project, dispensing.EnvironmentID, dispensing.CohortID)
	var projectStorehouseInfo models.ProjectStorehouseInfo
	if len(projectSite.StoreHouseID) > 0 && !projectSite.StoreHouseID[0].IsZero() {
		projectStorehouseInfos, err := database.GetProjectStorehouseInfo(projectSite.StoreHouseID)
		if err != nil {
			return err
		}
		projectStorehouseInfo = projectStorehouseInfos[0]
	}
	data := map[string]interface{}{
		"projectNumber": project.Number,
		"projectName":   project.Name,
		"envName":       envInfo.Name,
		"cohortName":    cohortInfo.Name,
	}
	if len(storeAlert) > 0 {
		data["depotName"] = projectStorehouseInfo.Storehouse.Name
		data["alarm"] = storeAlert
		userMail, err := tools.GetRoleUsersMailWithRole(dispensing.ProjectID, dispensing.EnvironmentID, "notice.subject.medicine.alarm", projectSite.StoreHouseID[0])
		if err != nil {
			return errors.WithStack(err)
		}
		for _, email := range userMail {
			err := mailInfo(ctx, data, "batch-group.alert-depot-title", dispensing.EnvironmentID, []string{email.Email}, mail)
			if err != nil {
				return err
			}
		}

	}
	if len(storeLimit) > 0 {
		data["depotName"] = projectStorehouseInfo.Storehouse.Name
		data["alarm"] = ""
		data["limit"] = storeLimit
		userMail, err := tools.GetRoleUsersMailWithRole(dispensing.ProjectID, dispensing.EnvironmentID, "notice.subject.medicine.capping", projectSite.StoreHouseID[0])
		if err != nil {
			return errors.WithStack(err)
		}
		for _, email := range userMail {
			err = mailInfo(ctx, data, "batch-group.limit-depot-title", dispensing.EnvironmentID, []string{email.Email}, mail)
			if err != nil {
				return err
			}
		}

	}
	if len(siteAlert) > 0 {
		data["siteNumber"] = projectSite.Number
		data["siteName"] = tools.GetProjectSiteLangName(projectSite, "zh")
		data["siteNameEn"] = tools.GetProjectSiteLangName(projectSite, "en")
		data["alarm"] = siteAlert
		userMail, err := tools.GetRoleUsersMailWithRole(dispensing.ProjectID, dispensing.EnvironmentID, "notice.subject.medicine.alarm", projectSite.ID)
		if err != nil {
			return errors.WithStack(err)
		}
		for _, email := range userMail {
			err = mailInfo(ctx, data, "batch-group.alert-site-title", dispensing.EnvironmentID, []string{email.Email}, mail)
			if err != nil {
				return err
			}
		}

	}
	if len(siteLimit) > 0 {
		data["siteNumber"] = projectSite.Number
		data["siteName"] = tools.GetProjectSiteLangName(projectSite, "zh")
		data["siteNameEn"] = tools.GetProjectSiteLangName(projectSite, "en")
		data["alarm"] = ""
		data["limit"] = siteLimit
		userMail, err := tools.GetRoleUsersMailWithRole(dispensing.ProjectID, dispensing.EnvironmentID, "notice.subject.medicine.capping", projectSite.ID)
		if err != nil {
			return errors.WithStack(err)
		}
		for _, email := range userMail {
			err = mailInfo(ctx, data, "batch-group.limit-site-title", dispensing.EnvironmentID, []string{email.Email}, mail)
			if err != nil {
				return err
			}
		}

	}
	return nil
}

func mailInfo(ctx *gin.Context, data map[string]interface{}, title string, envOID primitive.ObjectID, toUserMail []string, mails *[]models.Mail) error {
	const (
		projectNumber int = iota + 1
		projectName
		env
		cohort
		siteNumber
		siteName
		depotName
		alarm
		limit
	)

	noticeType := "notice.subject.medicine.alarm"

	if strings.Contains(title, "limit") {
		noticeType = "notice.subject.medicine.capping"
	}
	var notice models.NoticeConfig
	err := tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": noticeType}).Decode(&notice)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return errors.WithStack(err)
	}

	bodyContentKeys := make([]models.ContentKey, 0)
	_, projectNumberShow := slice.Find(notice.FieldsConfig, func(index int, item string) bool {
		return item == "projectNumber"
	})
	_, projectNameShow := slice.Find(notice.FieldsConfig, func(index int, item string) bool {
		return item == "projectName"
	})
	if projectNumberShow {
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: projectNumber,
			Key:   "project.number",
			Map:   bson.M{"projectNumber": data["projectNumber"]},
		})
	}
	if projectNameShow {
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: projectName,
			Key:   "project.name",
			Map:   bson.M{"projectName": data["projectName"]},
		})
	}

	bodyContentKeys = append(bodyContentKeys, models.ContentKey{
		Index: env,
		Key:   "project.env",
		Map:   bson.M{"envName": data["envName"]},
	})

	if data["cohortName"] != nil && data["cohortName"] != "" {
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: cohort,
			Key:   "project.cohort",
			Map:   bson.M{"cohortName": data["cohortName"]},
		})
	}

	if data["siteNumber"] != nil && data["siteNumber"] != "" {
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: siteNumber,
			Key:   "project.site.number",
			Map:   bson.M{"siteNumber": data["siteNumber"]},
		})
	}

	if data["siteName"] != nil && data["siteName"] != "" || data["siteNameEn"] != nil && data["siteNameEn"] != "" {
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: siteName,
			Key:   "batch-group.siteName",
			Map:   bson.M{"siteName": data["siteName"], "siteNameEn": data["siteNameEn"]},
		})
	}

	if data["depotName"] != nil && data["depotName"] != "" {
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: depotName,
			Key:   "batch-group.depotName",
			Map:   bson.M{"depotName": data["depotName"]},
		})
	}

	if data["alarm"] != nil && data["alarm"] != "" {
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: alarm,
			Key:   "batch-group.alarm",
			Map:   bson.M{"alarm": data["alarm"]},
		})
	}

	if data["limit"] != nil && data["limit"] != "" {
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: limit,
			Key:   "batch-group.limit",
			Map:   bson.M{"limit": data["limit"]},
		})
	}

	var noticeConfig models.NoticeConfig
	err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	langList := make([]string, 0)
	if noticeConfig.Manual != 0 {
		if noticeConfig.Manual == 1 {
			langList = append(langList, "zh")
		} else if noticeConfig.Manual == 2 {
			langList = append(langList, "en")
		} else if noticeConfig.Manual == 3 {
			langList = append(langList, "zh")
			langList = append(langList, "en")
		}
	} else {
		langList = append(langList, ctx.GetHeader("Accept-Language"))
	}
	err = slice.SortByField(bodyContentKeys, "Index")
	if err != nil {
		return errors.WithStack(err)
	}
	itemMail := models.Mail{
		ID:      primitive.NewObjectID(),
		Subject: title,
		SubjectData: map[string]interface{}{"projectNumber": data["projectNumber"],
			"projectName": data["projectName"],
			"envName":     data["envName"],
			"siteNumber":  data["siteNumber"],
			"depotName":   data["depotName"],
			"siteName":    data["siteName"],
			"siteNameEn":  data["siteNameEn"]},
		Content:        "",
		ContentData:    nil,
		To:             toUserMail, // test测试
		Lang:           ctx.GetHeader("Accept-Language"),
		LangList:       langList,
		Status:         0,
		CreatedTime:    time.Duration(time.Now().Unix()),
		ExpectedTime:   time.Duration(time.Now().Unix()),
		SendTime:       time.Duration(time.Now().Unix()),
		BodyContentKey: bodyContentKeys,
	}
	*mails = append(*mails, itemMail)
	return nil
}

func canUseBatchMatch(sctx mongo.SessionContext, medicineType, lastdate, date string, drugMatch bson.M) (bool, []string, error) {
	type Batch struct {
		Batch          string `bson:"batch"`
		ExpirationDate string `bson:"expirationDate"`
	}
	var batchs []Batch
	cursor, err := tools.Database.Collection(medicineType).Aggregate(sctx, mongo.Pipeline{
		{{"$match", drugMatch}},
		{{Key: "$group", Value: bson.M{"_id": bson.M{"batchNumber": "$batch_number", "expirationDate": "$expiration_date"}}}},

		{{"$project", bson.M{
			"batch":          "$_id.batchNumber",
			"expirationDate": "$_id.expirationDate",
		}}},
	})
	if err != nil {
		return false, nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &batchs)
	if err != nil {
		return false, nil, errors.WithStack(err)
	}
	infos := []string{}
	for _, b := range batchs {
		if b.ExpirationDate >= lastdate {
			infos = append(infos, b.Batch)
		}
	}
	return len(batchs) > 0, infos, nil
}

func UnbindingApproval(ctx *gin.Context, sctx mongo.SessionContext, subject models.Subject, medicineOID, dispensingOID primitive.ObjectID, approvalNumber string, agree int,
	rejectReason string, roleOID primitive.ObjectID, now time.Time) (interface{}, error) {
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	envp, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == subject.EnvironmentID
	})
	env := *envp

	me, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}
	var cohort models.Cohort

	match := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
	if subject.CohortID != primitive.NilObjectID {
		match = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
	}

	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//subjectReplaceText := GetSubjectReplaceText(ctx, attribute)
	subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
	subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)

	if env.Cohorts != nil && len(env.Cohorts) > 0 {
		for _, co := range env.Cohorts {
			if co.ID == subject.CohortID {
				cohort = co
				break
			}
		}
	}

	var medicine models.Medicine
	err = tools.Database.Collection("medicine").FindOne(nil, bson.M{"_id": medicineOID}).Decode(&medicine)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	approvalP, _ := slice.Find(medicine.UnblindingApprovals, func(index int, item models.UrgentUnblindingApproval) bool {
		return item.Number == approvalNumber
	})
	ap := *approvalP
	if ap.Status == 1 || ap.Status == 2 {
		return nil, tools.BuildServerError(ctx, "urgentUnblinding_approval_task_error")
	}

	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 添加轨迹
	opts := &options.UpdateOptions{
		ArrayFilters: &options.ArrayFilters{
			Filters: bson.A{bson.M{"element.number": approvalNumber}},
		},
	}
	update := bson.M{"$set": bson.M{
		"unblinding_approvals.$[element].status":            agree,
		"unblinding_approvals.$[element].approval_time":     time.Duration(now.Unix()),
		"unblinding_approvals.$[element].approval_by":       me.ID,
		"unblinding_approvals.$[element].approval_by_email": me.Email,
		"unblinding_approvals.$[element].reject_reason":     rejectReason,
	}}
	customTempOption := []models.CustomTempOption{}
	customTemps := []models.CustomTemp{}
	customTempOption = append(customTempOption, models.CustomTempOption{
		Index: 1,
		Key:   "history.dispensing.single.unBlindMedicine",
		Data:  bson.M{"number": medicine.Number},
	})
	customTempOption = append(customTempOption, models.CustomTempOption{
		Index: 2,
		Key:   "history.dispensing.single.unBlindApprovalNumber",
		Data:  bson.M{"approvalNumber": approvalNumber},
	})
	statusKey := "history.dispensing.single.unBlindSuccess"
	if agree == 2 {
		statusKey = "history.dispensing.single.unBlindReject"
	}
	customTempOption = append(customTempOption, models.CustomTempOption{
		Index:     3,
		Key:       "history.dispensing.single.unBlindStatus",
		TransData: statusKey,
		TransType: models.KeyData,
	})
	customTemps = append(customTemps, models.CustomTemp{
		ParKey:              "data",
		ConnectingSymbolKey: "history.dispensing.single.comma",
		LastSymbolKey:       "history.dispensing.single.period",
		CustomTempOptions:   customTempOption,
	})
	history := models.History{
		OID:         dispensingOID,
		Time:        time.Duration(now.Unix()),
		UID:         me.ID,
		User:        me.Name,
		CustomTemps: customTemps,
		Key:         "history.dispensing.dispensingCustomer-unblinding-application",
		Data:        map[string]interface{}{"approvalNumber": approvalNumber, "number": medicine.Number},
	}
	//项目动态  通过
	if agree == 1 {
		now := time.Duration(time.Now().Unix())
		siteName := tools.GetProjectSiteLangName(projectSite, "zh")
		siteNameEn := tools.GetProjectSiteLangName(projectSite, "en")
		OID := subject.EnvironmentID
		if !subject.CohortID.IsZero() {
			OID = subject.CohortID
		}
		typeTran := "project_dynamics_type_emergency_unblinding_ip"
		contentTran := "project_dynamics_content_emergency_unblinding_ip"
		dynamics := models.ProjectDynamics{
			ID:          primitive.NewObjectID(),
			Operator:    me.ID,
			OID:         OID,
			Time:        now,
			SceneTran:   "project_dynamics_scene_unblinding",
			TypeTran:    typeTran,
			ContentTran: contentTran,
			ContentData: map[string]interface{}{
				"subjectId":   subject.ID,
				"subjectName": subject.Info[0].Value,
				"siteId":      projectSite.ID,
				"siteName":    siteName,
				"siteNameEn":  siteNameEn,
			},
		}
		_, err := tools.Database.Collection("project_dynamics").InsertOne(sctx, dynamics)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	_, err = tools.Database.Collection("medicine").UpdateOne(sctx, bson.M{"_id": medicine.ID}, update, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	ctx.Set("HISTORY", []models.History{history})

	// 揭盲审批发送的邮件通知（通知对象：操作人+审批人）
	userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
	userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"env_id": subject.EnvironmentID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userProjectEnvironmentCursor.All(nil, &userProjectEnvironmentList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var userIDs []primitive.ObjectID
	for _, upel := range userProjectEnvironmentList {
		userIDs = append(userIDs, upel.UserID)
	}
	// 查询user
	var userList []models.User
	userCursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIDs}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userCursor.All(nil, &userList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询项目角色权限
	projectRolePermissionList := make([]models.ProjectRolePermission, 0)
	projectRolePermissionCursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": subject.ProjectID, "name": bson.M{"$ne": "Project-Admin"}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = projectRolePermissionCursor.All(nil, &projectRolePermissionList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 查询用户关联的角色
	userSiteList := make([]models.UserSite, 0)
	userSiteCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"env_id": subject.EnvironmentID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userSiteCursor.All(nil, &userSiteList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var nc models.NoticeConfig
	err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": env.ID, "key": "notice.subject.unblinding"}).Decode(&nc)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, err
	}

	excludeRecipientMap := make(map[string]bool)
	if nc.ExcludeRecipientList != nil && len(nc.ExcludeRecipientList) > 0 {
		excludeRecipientMap = getArrMap(nc.ExcludeRecipientList)
	}

	emailList := make([]string, 0)
	_, _, ipUnblindingApprovalUser := queryApprovalUser(userProjectEnvironmentList, userList, projectRolePermissionList, userSiteList, subject.ProjectSiteID)
	for _, uau := range ipUnblindingApprovalUser {
		emailList = append(emailList, uau.Email)
	}

	// 查询揭盲审批发起人
	emailSign := true
	var applicationUser models.User
	err = tools.Database.Collection("user").FindOne(nil, bson.M{"_id": ap.ApplicationBy}).Decode(&applicationUser)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if emailList != nil && len(emailList) > 0 {
		for _, e := range emailList {
			if e == applicationUser.Email {
				emailSign = false
				break
			}
		}
	}
	if emailSign {
		emailList = append(emailList, applicationUser.Email)
	}

	emails := make([]string, 0)
	for _, email := range emailList {
		isExists := false
		if _, is := excludeRecipientMap[email]; is {
			isExists = true
		}
		if !isExists {
			emails = append(emails, email)
		}
	}

	shTime, err := tools.GetTimeZoneTime(now.UTC(), projectSite, project)
	if err != nil {
		return nil, err
	}

	// 收件人
	var mails []models.Mail
	var envs []models.MailEnv

	var mailsA []models.Mail

	var noticeConfig models.NoticeConfig
	err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	langList := make([]string, 0)
	if noticeConfig.Manual != 0 {
		if noticeConfig.Manual == 1 {
			langList = append(langList, "zh")
		} else if noticeConfig.Manual == 2 {
			langList = append(langList, "en")
		} else if noticeConfig.Manual == 3 {
			langList = append(langList, "zh")
			langList = append(langList, "en")
		}
	} else {
		langList = append(langList, ctx.GetHeader("Accept-Language"))
	}
	bodyContentKeys := make([]models.ContentKey, 0)

	{
		const (
			projectNumber int = iota + 1
			projectName
			envName
			cohortName
			siteNumber
			siteName
			subjectNumber
			randomNumberIndex
			drugNumber
			unblindingTime
			unblindingReason
			remark
			approvalCode
			approvalResult
			commonReason
		)

		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: projectNumber,
			Key:   "project.number",
			Map:   bson.M{"projectNumber": project.Number},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: projectName,
			Key:   "project.name",
			Map:   bson.M{"projectName": project.Name},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: envName,
			Key:   "project.env",
			Map:   bson.M{"envName": env.Name},
		})
		if cohort.Name != "" {
			bodyContentKeys = append(bodyContentKeys, models.ContentKey{
				Index: envName,
				Key:   "project.cohort",
				Map:   bson.M{"cohortName": cohort.Name},
			})
		}
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: siteNumber,
			Key:   "project.site.number",
			Map:   bson.M{"siteNumber": projectSite.Number},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: siteName,
			Key:   "project.site.name",
			Map:   bson.M{"siteName": tools.GetProjectSiteLangName(projectSite, "zh"), "siteNameEn": tools.GetProjectSiteLangName(projectSite, "en")},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: subjectNumber,
			Key:   "dispensing.single.subject",
			Map:   bson.M{"label": subjectEmailReplaceTextZh, "labelEn": subjectEmailReplaceTextEn, "subjectNumber": subject.Info[0].Value},
		})
		randomNumber := tools.BlindData
		if attribute.AttributeInfo.IsRandomNumber {
			randomNumber = subject.RandomNumber
		}
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: randomNumberIndex,
			Key:   "dispensing.single.randomNumber",
			Map:   bson.M{"randomNumber": randomNumber},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: drugNumber,
			Key:   "dispensing.single.drugNumber",
			Map:   bson.M{"drugNumber": medicine.Number},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: unblindingTime,
			Key:   "dispensing.single.unblindingTime",
			Map:   bson.M{"unblindingTime": shTime},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: unblindingReason,
			Key:   "dispensing.single.unblindingReason",
			Map:   bson.M{"unblindingReason": ap.ReasonStr},
		})
		if ap.Remark != "" {
			bodyContentKeys = append(bodyContentKeys, models.ContentKey{
				Index: remark,
				Key:   "dispensing.single.remark",
				Map:   bson.M{"remark": ap.Remark},
			})
		}

		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: approvalCode,
			Key:   "dispensing.single.approvalCode",
			Map:   bson.M{"approvalCode": approvalNumber},
		})
		bodyContentKeys = append(bodyContentKeys, models.ContentKey{
			Index: approvalResult,
			Key:   "dispensing.single.approvalResult",
			Map:   bson.M{"approvalResult": tools.GetApprovalResultTranLang("zh", agree), "approvalResultEn": tools.GetApprovalResultTranLang("en", agree)},
		})
		if rejectReason != "" {
			bodyContentKeys = append(bodyContentKeys, models.ContentKey{
				Index: commonReason,
				Key:   "dispensing.single.commonReason",
				Map:   bson.M{"reason": rejectReason},
			})
		}

		bodyContentKeys, err = DispensingRemoveKey(ctx, subject.EnvironmentID, "notice.subject.unblinding", bodyContentKeys)
		if err != nil {
			return nil, errors.WithStack(err)

		}
		err = slice.SortByField(bodyContentKeys, "Index")
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if emails != nil && len(emails) > 0 {
		for _, email := range emails {
			var toUserMail []string
			toUserMail = append(toUserMail, email)
			mail := models.Mail{
				ID:           primitive.NewObjectID(),
				To:           toUserMail,
				Lang:         ctx.GetHeader("Accept-Language"),
				LangList:     langList,
				Status:       0,
				CreatedTime:  time.Duration(now.Unix()),
				ExpectedTime: time.Duration(now.Unix()),
				SendTime:     time.Duration(now.Unix()),
			}
			mail.Subject = "subject.ip-unblinding-approval.title"
			mail.SubjectData = map[string]interface{}{
				"projectNumber": project.Number,
				"envName":       env.Name,
				"siteNumber":    projectSite.Number,
				"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
				"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
			}
			mail.BodyContentKey = bodyContentKeys
			mailsA = append(mailsA, mail)
			mails = append(mails, mail)
		}
	}
	if len(mailsA) > 0 {
		for _, m := range mailsA {
			envs = append(envs, models.MailEnv{
				ID:         primitive.NewObjectID(),
				MailID:     m.ID,
				CustomerID: subject.CustomerID,
				ProjectID:  subject.ProjectID,
				EnvID:      subject.EnvironmentID,
				CohortID:   subject.CohortID,
			})
		}
	}

	if mails != nil && len(mails) > 0 {
		ctx.Set("MAIL", mails)
	}
	if envs != nil && len(envs) > 0 {
		ctx.Set("MAIL-ENV", envs)
	}

	// 同步app任务表
	// 查询当前受试者的app任务表是否有未完成的揭盲审批任务
	//existFilter := bson.M{
	//	"customer_id":    subject.CustomerID,
	//	"project_id":     subject.ProjectID,
	//	"env_id":         subject.EnvironmentID,
	//	"info.work_type": 8, // 揭盲审批任务
	//	"info.status":    0, // 状态未完成
	//}
	//if unblindingSign == "3" { // pv揭盲
	//	existFilter["info.subject_pv_approval.subject_id"] = subject.ID
	//	existFilter["info.work_type"] = 10
	//} else { // 紧急揭盲
	//	existFilter["info.subject_approval.subject_id"] = subject.ID
	//}
	//
	//if !subject.CohortID.IsZero() {
	//	existFilter["cohort_id"] = subject.CohortID
	//}
	//var existWorkTask models.WorkTask
	//err = tools.Database.Collection("work_task").FindOne(sctx, existFilter).Decode(&existWorkTask)
	//if err != nil && err != mongo.ErrNoDocuments {
	//	return nil, errors.WithStack(err)
	//}
	//if !existWorkTask.ID.IsZero() { // 有未完成的任务 要把状态同步为完成
	//	workTaskUpdate := bson.M{"$set": bson.M{
	//		"info.status":         1,
	//		"info.finish_time":    time.Duration(time.Now().Unix()),
	//		"info.finish_user_id": me.ID,
	//		"info.finish_role_id": roleOID,
	//	}}
	//	_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"_id": existWorkTask.ID}, workTaskUpdate)
	//}

	matchApproval := bson.M{"project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "type": 4, "status": 0, "unblinding_data.subject_id": subject.ID, "unblinding_data.number": approvalNumber}

	updateApproval := bson.M{
		"$set": bson.M{
			"approval_status":                   agree,
			"approval_time":                     time.Duration(time.Now().Unix()),
			"approval_by":                       me.ID,
			"approval_by_email":                 me.Email,
			"reason":                            rejectReason,
			"status":                            1,
			"unblinding_data.approval_time":     time.Duration(time.Now().Unix()),
			"unblinding_data.approval_by":       me.ID,
			"unblinding_data.approval_by_email": me.Email,
			"unblinding_data.reason":            rejectReason,
			"unblinding_data.status":            agree,
		},
	}
	_, err = tools.Database.Collection("approval_process").UpdateOne(sctx, matchApproval, updateApproval)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return nil, nil
}

func RegisterDispensingAddOrder(sctx mongo.SessionContext, medicine models.Medicine, dispensing models.Dispensing, histories *[]models.History, user models.User) (models.MedicineOrder, error) {
	sendID := medicine.SiteID
	types := 6
	if sendID.IsZero() {
		sendID = medicine.StorehouseID
		types = 5
	}
	orderNumber, err := getOrderNumber(sctx, medicine.ProjectID)
	orderOID := primitive.NewObjectID()
	order := models.MedicineOrder{
		ID:                orderOID,
		CustomerID:        medicine.CustomerID,
		ProjectID:         medicine.ProjectID,
		EnvironmentID:     medicine.EnvironmentID,
		SendID:            sendID,
		Status:            1, //已确认
		SortIndex:         10,
		Medicines:         []primitive.ObjectID{medicine.ID},
		MedicinesHistory:  []models.MedicinesHistory{},
		OtherMedicinesNew: []primitive.ObjectID{},
		OrderNumber:       orderNumber,
		Type:              types,
		Meta: models.Meta{
			CreatedBy: user.ID,
			CreatedAt: time.Duration(time.Now().Unix()),
		},
		SubjectID:    dispensing.SubjectID,
		DispensingID: dispensing.ID,
	}
	_, err = tools.Database.Collection("medicine_order").InsertOne(sctx, order)
	if err != nil {
		return order, errors.WithStack(err)
	}

	//创建待确认的订单任务
	permissions := []string{"operation.supply.shipment.cancel", "operation.supply.shipment.confirm"}
	siteOrStoreIDs := []primitive.ObjectID{sendID}
	userIds, err := tools.GetPermissionUserIds(sctx, permissions, dispensing.ProjectID, dispensing.EnvironmentID, siteOrStoreIDs...)
	if err != nil {
		return order, errors.WithStack(err)
	}

	if len(userIds) > 0 {
		workTask := models.WorkTask{
			ID:            primitive.NewObjectID(),
			CustomerID:    dispensing.CustomerID,
			ProjectID:     dispensing.ProjectID,
			EnvironmentID: dispensing.EnvironmentID,
			CohortID:      dispensing.CohortID,
			UserIDs:       userIds,
			Info: models.WorkTaskInfo{
				WorkType:        2,
				Status:          0,
				CreatedTime:     time.Duration(time.Now().Unix()),
				Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
				MedicineIDs:     []primitive.ObjectID{},
				MedicineOrderID: orderOID,
				DispensingID:    primitive.NilObjectID,
			},
		}
		_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
		if err != nil {
			return order, errors.WithStack(err)
		}
	}

	_, err = tools.Database.Collection("medicine").UpdateOne(sctx, bson.M{"_id": medicine.ID}, bson.M{"$set": bson.M{"order_id": orderOID, "status": 2}})
	if err != nil && err != mongo.ErrNoDocuments {
		return models.MedicineOrder{}, errors.WithStack(err)
	}
	*histories = append(*histories, models.History{
		Key:  "history.order.confrim-new",
		OID:  orderOID,
		Time: time.Duration(time.Now().Unix()),
		UID:  user.ID,
		Data: bson.M{"orderNumber": orderNumber},
		User: user.Name,
	})
	*histories = append(*histories, models.History{
		Key:  "history.medicine.confirmedNew",
		OID:  medicine.ID,
		Time: time.Duration(time.Now().Unix()),
		UID:  user.ID,
		Data: bson.M{"orderNumber": orderNumber},
		User: user.Name,
	})
	return order, nil
}

func RegisterDispensingOtherAddOrder(sctx mongo.SessionContext, medicineOthers []models.OtherMedicine, dispensing models.Dispensing, histories *[]models.History, user models.User, now time.Duration) (models.MedicineOrder, error) {
	sendID := medicineOthers[0].SiteID
	medicineOther := medicineOthers[0]
	types := 6
	if sendID.IsZero() {
		sendID = medicineOthers[0].StorehouseID
		types = 5
	}
	updateIDs := slice.Map(medicineOthers, func(index int, item models.OtherMedicine) primitive.ObjectID {
		return item.ID
	})
	orderNumber, err := getOrderNumber(sctx, dispensing.ProjectID)
	orderOID := primitive.NewObjectID()
	order := models.MedicineOrder{
		ID:                orderOID,
		CustomerID:        dispensing.CustomerID,
		ProjectID:         dispensing.ProjectID,
		EnvironmentID:     dispensing.EnvironmentID,
		SendID:            sendID,
		Status:            1, //待确认
		SortIndex:         10,
		Medicines:         []primitive.ObjectID{},
		MedicinesHistory:  []models.MedicinesHistory{},
		OtherMedicinesNew: updateIDs,
		OrderNumber:       orderNumber,
		Type:              types,
		Meta: models.Meta{
			CreatedBy: user.ID,
			CreatedAt: now,
		},
		SubjectID:    dispensing.SubjectID,
		DispensingID: dispensing.ID,
	}
	_, err = tools.Database.Collection("medicine_order").InsertOne(sctx, order)
	if err != nil {
		return order, errors.WithStack(err)
	}
	*histories = append(*histories, models.History{
		Key:  "history.order.confrim-new",
		OID:  orderOID,
		Time: time.Duration(time.Now().Unix()),
		UID:  user.ID,
		Data: bson.M{"orderNumber": orderNumber},
		User: user.Name,
	})
	//创建待确认的订单任务
	permissions := []string{"operation.supply.shipment.cancel", "operation.supply.shipment.confirm"}
	siteOrStoreIDs := []primitive.ObjectID{sendID}
	userIds, err := tools.GetPermissionUserIds(sctx, permissions, dispensing.ProjectID, dispensing.EnvironmentID, siteOrStoreIDs...)
	if err != nil {
		return order, errors.WithStack(err)
	}

	if len(userIds) > 0 {
		workTask := models.WorkTask{
			ID:            primitive.NewObjectID(),
			CustomerID:    dispensing.CustomerID,
			ProjectID:     dispensing.ProjectID,
			EnvironmentID: dispensing.EnvironmentID,
			CohortID:      dispensing.CohortID,
			UserIDs:       userIds,
			Info: models.WorkTaskInfo{
				WorkType:        2,
				Status:          0,
				CreatedTime:     now,
				Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
				MedicineIDs:     []primitive.ObjectID{},
				MedicineOrderID: orderOID,
				DispensingID:    primitive.NilObjectID,
			},
		}
		_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
		if err != nil {
			return order, errors.WithStack(err)
		}
	}

	_, err = tools.Database.Collection("medicine_others").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": updateIDs}}, bson.M{"$set": bson.M{"status": 2}})
	if err != nil && err != mongo.ErrNoDocuments {
		return models.MedicineOrder{}, errors.WithStack(err)
	}

	//查询otherKey
	var medicineOtherKey models.MedicineOtherKey
	filter := bson.M{"customer_id": medicineOther.CustomerID, "project_id": medicineOther.ProjectID, "env_id": medicineOther.EnvironmentID,
		"name": medicineOther.Name, "expiration_date": medicineOther.ExpirationDate, "batch_number": medicineOther.BatchNumber}
	if medicineOthers[0].SiteID.IsZero() {
		filter["storehouse_id"] = medicineOthers[0].StorehouseID
	} else {
		filter["site_id"] = medicineOthers[0].SiteID
	}
	err = tools.Database.Collection("medicine_other_key").FindOne(sctx, filter).Decode(&medicineOtherKey)
	if err != nil {
		return models.MedicineOrder{}, errors.WithStack(err)
	}
	if err == mongo.ErrNoDocuments {
		return models.MedicineOrder{}, nil
	}
	if medicineOtherKey.ID != primitive.NilObjectID {

		history := models.History{
			Key:  "history.medicine.otherConfirmed",
			OID:  medicineOtherKey.ID,
			Data: bson.M{"name": medicineOther.Name, "batch": medicineOther.BatchNumber, "expireDate": medicineOther.ExpirationDate, "count": len(medicineOthers)},
			Time: now,
			UID:  user.ID,
			User: user.Name,
		}
		*histories = append(*histories, history)
	}

	return order, nil
}

// CheckDispensingApprovalProcess 判断是否走申请流程
// 参数:
//   - ctx: gin上下文
//   - data: 请求数据，包含apply字段判断是否申请流程
//   - subject: 受试者信息
//   - attribute: 属性配置
//   - visitCycle: 访视周期信息
//   - dispensing: 发药信息
//   - dispensingType: 发放类型 1-主访视 2-访视外 3-补发
//
// 返回:
//   - bool: true表示可以继续处理，false表示需要走审批流程
//   - error: 错误信息
func CheckDispensingApprovalProcess(ctx *gin.Context, data map[string]interface{}, subject models.Subject, attribute models.Attribute, visitCycle models.VisitCycle, dispensing models.Dispensing, dispensingType int) (bool, error) {
	// 1. 检查是否为申请流程
	apply, exists := data["apply"]
	if !exists {
		return true, nil // 没有apply字段，直接允许
	}

	applyBool, ok := apply.(bool)
	if !ok || !applyBool {
		return true, nil // apply不为true，直接允许
	}

	// 2. 获取必要的参数
	visitCycleInfoIDStr, ok := data["visitCycleInfoId"].(string)
	if !ok {
		return false, errors.New("缺少访视周期信息ID")
	}
	visitCycleInfoOID, err := primitive.ObjectIDFromHex(visitCycleInfoIDStr)
	if err != nil {
		return false, errors.WithStack(err)
	}

	// 6. 创建访视周期映射
	visitCycleMap := make(map[string]models.VisitCycleInfo)
	var currentVisitInfo models.VisitCycleInfo
	for _, info := range visitCycle.Infos {
		visitCycleMap[info.Number] = info
		if info.ID == visitCycleInfoOID {
			currentVisitInfo = info
		}
	}

	// 7. 获取时区信息
	strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return false, errors.WithStack(err)
	}
	if strTimeZone == "" {
		zone, err := tools.GetTimeZone(subject.ProjectID)
		if err != nil {
			return false, errors.WithStack(err)
		}
		strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
	}
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

	// 8. 获取发药历史，计算窗口期
	var dispensings []models.Dispensing
	cursor, err := tools.Database.Collection("dispensing").Find(ctx, bson.M{
		"subject_id": subjectOID,
		"status":     2, // 已发药状态
	}, &options.FindOptions{
		Sort: bson.D{{"dispensing_time", 1}},
	})
	if err != nil {
		return false, errors.WithStack(err)
	}
	err = cursor.All(ctx, &dispensings)
	if err != nil {
		return false, errors.WithStack(err)
	}

	// 9. 计算是否超窗
	lastTime := time.Duration(0)
	afterRandom := false
	interval := float64(0)

	// 找到最后一次发药时间和是否已随机
	for _, dispensing := range dispensings {
		if dispensing.DispensingTime > lastTime {
			lastTime = dispensing.DispensingTime
		}
		if dispensing.VisitInfo.Random {
			afterRandom = true
		}
	}

	// 计算当前访视的窗口期
	currentTime := time.Duration(time.Now().Unix())
	period := handlePeriod(afterRandom, visitCycle.VisitType, currentVisitInfo,
		subject.RandomTime, lastTime, currentTime, intTimeZone, &interval, attribute, subject.JoinTime)

	// 10. 判断是否需要审批流程
	isOutWindow := period.OutSize

	// 检查是否开启审批流程
	approvalEnabled := false
	if attribute.OverdueVisitApproval {
		// 根据发放类型判断是否开启审批
		switch dispensingType {
		case 1: // 主访视
			approvalEnabled = attribute.AttributeInfo.OverdueVisitProcess || attribute.OverdueVisitSms
		case 2: // 访视外
			approvalEnabled = attribute.OverdueVisitProcess || attribute.OverdueVisitSms
		case 3: // 补发
			approvalEnabled = attribute.OverdueVisitProcess || attribute.OverdueVisitSms
		}
	}

	// 11. 如果超窗且开启审批流程
	if isOutWindow && approvalEnabled {
		// 检查是否已有待审批的申请
		existingApproval, err := checkExistingDispensingApproval(ctx, subjectOID, visitCycleInfoOID, dispensingType)
		if err != nil {
			return false, errors.WithStack(err)
		}

		if existingApproval != nil {
			// 已有待审批申请
			return false, errors.New("发放申请审批中，请等待")
		}

		// 创建新的审批申请
		err = createDispensingApproval(ctx, data, subjectOID, visitCycleInfoOID, dispensingType, subject, attribute)
		if err != nil {
			return false, errors.WithStack(err)
		}

		return false, nil
	}

	// 12. 不需要审批，直接允许
	return true, nil
}

// checkExistingDispensingApproval 检查是否已有待审批的发药申请
func checkExistingDispensingApproval(ctx *gin.Context, subjectID, visitCycleInfoID primitive.ObjectID, dispensingType int) (*models.ApprovalProcess, error) {
	filter := bson.M{
		"type":                                5, // 假设5为发药审批类型
		"status":                              0, // 待审批状态
		"dispensing_approval_data.subject_id": subjectID,
		"dispensing_approval_data.visit_cycle_info_id": visitCycleInfoID,
		"dispensing_approval_data.dispensing_type":     dispensingType,
	}

	var approval models.ApprovalProcess
	err := tools.Database.Collection("approval_process").FindOne(ctx, filter).Decode(&approval)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, errors.WithStack(err)
	}

	return &approval, nil
}

// createDispensingApproval 创建发药审批申请
func createDispensingApproval(ctx *gin.Context, data map[string]interface{}, subjectID, visitCycleInfoID primitive.ObjectID,
	dispensingType int, subject models.Subject, attribute models.Attribute) error {

	// 获取当前用户信息
	userInterface, exists := ctx.Get("user")
	if !exists {
		return errors.New("用户信息不存在")
	}
	user := userInterface.(models.User)

	// 生成审批编号
	approvalNumber := fmt.Sprintf("DA%d%s", time.Now().Unix(), subjectID.Hex()[:6])

	now := time.Now()
	applicationTime := time.Duration(now.Unix())

	// 将请求数据序列化为JSON
	requestDataJSON, err := json.Marshal(data)
	if err != nil {
		return errors.WithStack(err)
	}

	// 获取申请原因和备注
	reason, _ := data["reason"].(string)
	remark, _ := data["remark"].(string)
	if reason == "" {
		reason = "超窗发药申请"
	}

	// 创建审批流程记录
	approvalProcess := models.ApprovalProcess{
		Number:                  approvalNumber,
		Name:                    getDispensingApprovalName(dispensingType),
		Type:                    5,                                             // 发药审批类型
		Status:                  0,                                             // 待审批
		EstimatedCompletionTime: applicationTime + time.Duration(24*time.Hour), // 预期24小时内完成
		ApplicationTime:         applicationTime,
		ApplicationBy:           user.ID,
		ApplicationByEmail:      user.Email,
		ApprovalStatus:          0, // 提交申请
		DispensingApprovalData: bson.M{
			"subject_id":          subjectID,
			"visit_cycle_info_id": visitCycleInfoID,
			"dispensing_type":     dispensingType,
			"request_data":        string(requestDataJSON),
			"reason":              reason,
			"remark":              remark,
			"project_id":          subject.ProjectID,
			"env_id":              subject.EnvironmentID,
			"cohort_id":           subject.CohortID,
		},
	}

	// 插入审批流程记录
	_, err = tools.Database.Collection("approval_process").InsertOne(ctx, approvalProcess)
	if err != nil {
		return errors.WithStack(err)
	}

	// 保存审批编号
	approvalNumberSave := models.ApprovalNumber{
		ID:         primitive.NewObjectID(),
		CustomerID: user.CustomerID,
		ProjectID:  subject.ProjectID,
		Number:     approvalNumber,
	}
	_, err = tools.Database.Collection("approval_number").InsertOne(ctx, approvalNumberSave)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// getDispensingApprovalName 根据发放类型获取审批名称
func getDispensingApprovalName(dispensingType int) string {
	switch dispensingType {
	case 1:
		return "主访视超窗发药审批"
	case 2:
		return "访视外超窗发药审批"
	case 3:
		return "补发超窗发药审批"
	default:
		return "超窗发药审批"
	}
}
