package service

import (
	"clinflash-irt/models"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestMedicineOrderService_ReceiveRecoveryOrder(t *testing.T) {
	type args struct {
		ctx          *gin.Context
		receiveOrder models.RecevieOrder
	}
	tests := []struct {
		name    string
		s       *MedicineOrderService
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.s.ReceiveRecoveryOrder(tt.args.ctx, tt.args.receiveOrder); (err != nil) != tt.wantErr {
				t.<PERSON>rf("MedicineOrderService.ReceiveRecoveryOrder() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
