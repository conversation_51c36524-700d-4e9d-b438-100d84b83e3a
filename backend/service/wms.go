package service

import (
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type WmsService struct {
	orderService MedicineOrderService
}

func (s *WmsService) UpdateShipmentConfirm(ctx *gin.Context, data map[string]interface{}) {
	var responErr error
	dataType, _ := json.Marshal(data)
	if data["projectNo"] == nil {
		tools.Response(ctx, tools.BuildCustomError("项目编号不能为空"))
		return
	}
	if data["orderNumber"] == nil {
		tools.Response(ctx, tools.BuildCustomError("订单号不能为空"))
		return
	}
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"info.number": data["projectNo"]}).Decode(&project)
	if err != nil {
		tools.Response(ctx, tools.BuildCustomError("项目编号不能为空"))
		return
	}
	if project.ID == primitive.NilObjectID {
		tools.Response(ctx, tools.BuildCustomError("项目编号不能为空"))
		return
	}

	//查询订单信息
	filter := bson.M{"order_number": data["orderNumber"].(string)}
	var order models.MedicineOrder
	if err := tools.Database.Collection("medicine_order").FindOne(nil, filter).Decode(&order); err != nil {
		tools.Response(ctx, tools.BuildCustomError("订单号不存在"))
		return
	}

	if project.ID != order.ProjectID {
		tools.Response(ctx, tools.BuildCustomError("项目下没有该订单"))
		return
	}

	user := models.User{
		UserInfo: models.UserInfo{
			Name: "System",
		},
	}
	recoveryRequestInterfaceID := primitive.NewObjectID()
	recoveryRequestInterface := models.RequestInterface{
		ID:        recoveryRequestInterfaceID,
		Type:      "api.order.transport",
		API:       "api.order.transport",
		Body:      string(dataType),
		CreatedAt: time.Duration(time.Now().Unix()),
		Status:    0,
		SendAt:    time.Duration(time.Now().Unix()),
	}

	if _, err := tools.Database.Collection("request_interface").InsertOne(nil, recoveryRequestInterface); err != nil {
		panic(err)
	}

	var projectStorehouse models.ProjectStorehouse
	if err := tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": order.SendID, "deleted": 2}).Decode(&projectStorehouse); err != nil {
		return
	}

	if !projectStorehouse.Connected {
		tools.Response(ctx, tools.BuildCustomError("该订单未对接仓库"))
		return
	}

	if order.Status == 1 {
		//判断出库订单状态
		status := data["status"].(float64)
		if status != 1 { //出库失败，取消订单
			//失败的研究产品
			products := data["products"].([]interface{})
			var numbersReason1 []string
			var numbersReason2 []string
			var willFreezeNumber []string

			for _, product := range products {
				productValue := product.(map[string]interface{})
				number := productValue["number"].(string)
				reason := productValue["reason"].(float64)
				if reason == 1 {
					numbersReason1 = append(numbersReason1, number)
				} else {
					numbersReason2 = append(numbersReason2, number)
				}
				willFreezeNumber = append(willFreezeNumber, number)
			}

			if projectStorehouse.Connected {
				requestUrl := ""
				key := ""
				if projectStorehouse.Supplier == "baicheng" {
					key = "baicheng.shipment.cancel"
				} else if projectStorehouse.Supplier == "shengsheng" {
					key = "shengsheng.shipment.cancel"
				}
				var settingConfig map[string]interface{}
				tools.Database.Collection("setting_config").FindOne(nil, bson.M{"key": key}).Decode(&settingConfig)
				requestUrl = settingConfig["data"].(string)

				body := fmt.Sprintf(`{
					"projectNo":"%s",
					"orderNumber":"%s",
					"companyCode":"IRT"
					}`, project.Number, order.OrderNumber)

				requestInterfaceID := primitive.NewObjectID()
				receiptRequestInterface := models.RequestInterface{
					ID:        requestInterfaceID,
					Type:      "wms.shipment.cancel",
					API:       requestUrl,
					Body:      string(body),
					CreatedAt: time.Duration(time.Now().Unix()),
					Status:    0,
					SendAt:    time.Duration(time.Now().Unix()),
				}

				if _, err := tools.Database.Collection("request_interface").InsertOne(nil, receiptRequestInterface); err != nil {
					panic(err)
				}

				update := bson.M{
					"$set": bson.M{
						"status": 1,
					},
				}
				bytes, err := tools.RequestExternalPost(nil, requestUrl, body)
				if err != nil {
					update = bson.M{
						"$set": bson.M{
							"status": 2,
							"reason": err.Error(),
						},
					}
					_, updateErr := tools.Database.Collection("request_interface").UpdateOne(nil, bson.M{"_id": requestInterfaceID}, update)
					if updateErr != nil {
						panic(err)
					}
				} else {
					if _, err := tools.WMSResultData(bytes); err != nil {
						update = bson.M{
							"$set": bson.M{
								"status": 3,
								"reason": err.Error(),
							},
						}

					} else {
						update = bson.M{
							"$set": bson.M{
								"status": 1,
							},
						}
					}
					_, updateErr := tools.Database.Collection("request_interface").UpdateOne(nil, bson.M{"_id": requestInterfaceID}, update)
					if updateErr != nil {
						panic(err)
					}
				}
			}

			setUpdate := bson.M{
				"status":     5,
				"sort_index": 30,
				"reason":     "wms出库失败",
				"meta": models.Meta{
					CreatedAt: order.CreatedAt,
					CreatedBy: order.CreatedBy,
					UpdatedAt: time.Duration(time.Now().Unix()),
					//UpdatedBy: user.ID,
				},
			}

			//setUpdate["canceller_by"] = user.ID
			setUpdate["canceller_at"] = time.Duration(time.Now().Unix())
			if project.ResearchAttribute == 1 {
				err := cancelVisit(ctx, nil, order, order.DispensingID, 5)
				if err != nil {
					return
				}
			}

			update := bson.M{
				"$set": setUpdate,
			}

			res, err := tools.Database.Collection("medicine_order").UpdateOne(nil, filter, update)
			if err != nil {
				return
			}
			if res.ModifiedCount != 1 {
				return
			}

			subject := "order.cancel_title"
			content := "order.cancel"
			if project.ResearchAttribute == 1 {
				subject = "order.cancel_title_dtp"
				content = "order.cancel_dtp"
			}
			updateSet := bson.M{}
			key := "history.order.cancel"
			keySku := "history.medicine.use-dtp"
			if order.Type == 1 { //仓库-中心
				updateSet = bson.M{
					"status":        1,
					"site_id":       nil,
					"order_id":      nil,
					"storehouse_id": order.SendID,
				}
			} else if order.Type == 5 { //仓库-受试者
				subjectID := order.SubjectID
				var sub models.Subject
				err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectID}).Decode(&sub)
				if err != nil {
					return
				}
				if sub.Status == 4 || sub.Status == 5 {
					updateSet = bson.M{
						"status":        1,
						"subject_id":    primitive.NilObjectID,
						"order_id":      nil,
						"storehouse_id": order.SendID,
					}
				} else {
					updateSet = bson.M{
						"status":        1,
						"subject_id":    order.SubjectID,
						"order_id":      nil,
						"storehouse_id": order.SendID,
					}
				}
			}

			medicineFilter := bson.M{"_id": bson.M{"$in": order.Medicines}, "project_id": order.ProjectID, "env_id": order.EnvironmentID}
			medicineUpdate := bson.M{
				"$set": updateSet,
			}
			if res, err := tools.Database.Collection("medicine").UpdateMany(nil, medicineFilter, medicineUpdate); err != nil {
				fmt.Println("matchCount", res.MatchedCount)
				return
			}

			updateFreezeMedicineFilter := bson.M{"number": bson.M{"$in": willFreezeNumber}, "project_id": order.ProjectID, "env_id": order.EnvironmentID}
			medicineUpdateFreeze := bson.M{
				"$set": bson.M{
					"status": 4,
				},
			}
			if _, err := tools.Database.Collection("medicine").UpdateMany(nil, updateFreezeMedicineFilter, medicineUpdateFreeze); err != nil {
				return
			}

			//更新之前状态是隔离的研究产品
			var frozenMedicines []primitive.ObjectID
			for _, medicineHistory := range order.MedicinesHistory {
				frozenMedicines = append(frozenMedicines, medicineHistory.ID)
			}
			if len(frozenMedicines) > 0 {
				frozenUpdateSet := bson.M{
					"status": 4,
				}
				frozenMedicineFilter := bson.M{"_id": bson.M{"$in": frozenMedicines}}
				frozenMedicineUpdate := bson.M{
					"$set": frozenUpdateSet,
				}
				if _, err := tools.Database.Collection("medicine").UpdateMany(nil, frozenMedicineFilter, frozenMedicineUpdate); err != nil {
					return
				}
			}

			//更新未编号的研究产品
			otherMedicineFilter := bson.M{"_id": bson.M{"$in": order.OtherMedicinesNew}, "project_id": order.ProjectID, "env_id": order.EnvironmentID}
			otherMedicineUpdate := bson.M{
				"$set": updateSet,
			}
			if res, err := tools.Database.Collection("medicine_others").UpdateMany(nil, otherMedicineFilter, otherMedicineUpdate); err != nil {
				fmt.Println("matchCount", res.MatchedCount)
				return
			}
			// otherMedicines := order.OtherMedicines
			// for _, otherMedicine := range otherMedicines {
			// 	//机构中未编码研究产品的更新
			// 	var medicineOtherInstitute models.MedicineOtherInstitute
			// 	otherMatch := bson.M{"env_id": order.EnvironmentID, "institute_id": order.ReceiveID, "info.name": otherMedicine.Name, "info.batch": otherMedicine.Batch, "info.expire_date": otherMedicine.ExpireDate}
			// 	if project.ResearchAttribute == 1 {
			// 		otherMatch = bson.M{"env_id": order.EnvironmentID, "subject_id": order.SubjectID, "info.name": otherMedicine.Name, "info.batch": otherMedicine.Batch, "info.expire_date": otherMedicine.ExpireDate}
			// 	}
			// 	err := tools.Database.Collection("medicine_other_institute").FindOne(nil, otherMatch).Decode(&medicineOtherInstitute)
			// 	if err != nil {
			// 		return
			// 	}
			// 	//更新
			// 	applyCount := medicineOtherInstitute.Info.ApplyCount             //已申请
			// 	toBeConfirmCount := medicineOtherInstitute.Info.ToBeConfirmCount //订单中待确认
			// 	toBeSendCount := medicineOtherInstitute.Info.ToBeSendCount       //待发送
			// 	inTransitCount := medicineOtherInstitute.Info.InTransitCount     //运送中
			// 	lostCount := medicineOtherInstitute.Info.LostCount               //作废、丢失
			// 	count := medicineOtherInstitute.Info.Count                       //可用
			// 	useCount := medicineOtherInstitute.Info.UsedCount                //已使用

			// 	if project.ResearchAttribute == 1 {
			// 		applyCount = applyCount - otherMedicine.UseCount
			// 	} else {
			// 		toBeSendCount = toBeSendCount - otherMedicine.UseCount
			// 	}

			// 	count = count + otherMedicine.UseCount

			// 	otherMedicineInstituteUpdate := bson.M{
			// 		"$set": bson.M{
			// 			"edit":                     true,
			// 			"info.to_be_confirm_count": toBeConfirmCount,
			// 			"info.to_be_send_count":    toBeSendCount,
			// 			"info.in_transit_count":    inTransitCount,
			// 			"info.lost_count":          lostCount,
			// 			"info.apply_count":         applyCount,
			// 			"info.count":               count,
			// 		},
			// 	}

			// 	_, err = tools.Database.Collection("medicine_other_institute").UpdateOne(nil, bson.M{"_id": medicineOtherInstitute.ID}, otherMedicineInstituteUpdate)
			// 	if err != nil {
			// 		return
			// 	}
			// 	if project.ResearchAttribute == 1 && applyCount == 0 && inTransitCount == 0 && useCount == 0 {
			// 		_, err = tools.Database.Collection("medicine_other_institute").DeleteOne(nil, bson.M{"env_id": order.EnvironmentID, "subject_id": order.SubjectID, "info.name": otherMedicine.Name, "info.batch": otherMedicine.Batch, "info.expire_date": otherMedicine.ExpireDate})
			// 		if err != nil {
			// 			return
			// 		}
			// 	}

			// 	//medicine_other的更新
			// 	var medicineOther models.MedicineOther
			// 	err = tools.Database.Collection("medicine_other_institute").FindOne(nil, bson.M{"storehouse_id": order.SendID, "env_id": order.EnvironmentID, "info.name": otherMedicine.Name, "info.batch": otherMedicine.Batch, "info.expire_date": otherMedicine.ExpireDate}).Decode(&medicineOther)
			// 	if err != nil {
			// 		return
			// 	}
			// 	//更新
			// 	otherApplyCount := medicineOtherInstitute.Info.ApplyCount
			// 	otherToBeConfirmCount := medicineOther.Info.ToBeConfirmCount //订单中待确认
			// 	otherToBeSendCount := medicineOther.Info.ToBeSendCount       //待发送
			// 	otherInTransitCount := medicineOther.Info.InTransitCount     //运送中
			// 	otherLostCount := medicineOther.Info.LostCount               //作废、丢失
			// 	otherCount := medicineOther.Info.Count

			// 	if project.ResearchAttribute == 1 {
			// 		otherApplyCount = otherApplyCount - otherMedicine.UseCount
			// 	} else {
			// 		otherToBeSendCount = otherToBeSendCount - otherMedicine.UseCount
			// 	}

			// 	otherCount = otherCount + otherMedicine.UseCount

			// 	otherMedicineUpdate := bson.M{
			// 		"$set": bson.M{
			// 			"edit":                     true,
			// 			"info.to_be_confirm_count": otherToBeConfirmCount,
			// 			"info.to_be_send_count":    otherToBeSendCount,
			// 			"info.in_transit_count":    otherInTransitCount,
			// 			"info.lost_count":          otherLostCount,
			// 			"info.count":               otherCount,
			// 			"info.apply_count":         otherApplyCount,
			// 		},
			// 	}

			// 	_, err = tools.Database.Collection("medicine_other_institute").UpdateOne(nil, bson.M{"_id": medicineOther.ID}, otherMedicineUpdate)
			// 	if err != nil {
			// 		return
			// 	}
			// }

			// 邮件通知 （取消）
			if project.ResearchAttribute == 1 {
				err = SendUpdateMailWithDTP(ctx, order.EnvironmentID, order.ID, subject, content, user, order.SubjectID, order.DispensingID, "WMS出库失败", subject, models.UpdateOrderInfo{}, order)
				if err != nil {
					return
				}
			} else {
				err = SendUpdateMail(ctx, order.EnvironmentID, order.ID, subject, content, user, "", subject, order.ExpectedArrivalTime)
				if err != nil {
					panic(err)
				}
			}
			var reason1Medicines []models.Medicine
			if len(numbersReason1) > 0 {
				cursor, err := tools.Database.Collection("medicine").Find(nil, bson.M{"number": bson.M{"$in": numbersReason1}, "env_id": order.EnvironmentID})
				if err != nil {
					panic(err)
				}
				err = cursor.All(nil, &reason1Medicines)
				if err != nil {
					panic(err)
				}
			}

			var reason2Medicines []models.Medicine
			if len(numbersReason2) > 0 {
				cursor2, err2 := tools.Database.Collection("medicine").Find(nil, bson.M{"number": bson.M{"$in": numbersReason2}, "env_id": order.EnvironmentID})
				if err2 != nil {
					panic(err)
				}
				err2 = cursor2.All(nil, &reason2Medicines)
				if err2 != nil {
					panic(err)
				}
			}

			var operHistories []models.History
			var freezeIds []primitive.ObjectID
			operHistory := models.History{
				Key:  key,
				OID:  order.ID,
				Data: bson.M{"reason": "WMS出库失败"},
				Time: time.Duration(time.Now().Unix()),
				UID:  user.ID,
				User: user.Name,
			}
			operHistories = append(operHistories, operHistory)
			for _, medicine := range reason1Medicines {
				operHistories = append(operHistories, models.History{
					Key:  "history.medicine.freeze",
					OID:  medicine.ID,
					Data: bson.M{"reason": "WMS出库失败,研究产品超温需隔离", "packNumber": medicine.Number},
					Time: time.Duration(time.Now().Unix()),
					UID:  user.ID,
					User: user.Name,
				})
				freezeIds = append(freezeIds, medicine.ID)
			}
			for _, medicine := range reason2Medicines {
				operHistories = append(operHistories, models.History{
					Key:  "history.medicine.freeze",
					OID:  medicine.ID,
					Data: bson.M{"reason": "WMS出库失败,研究产品丢失/作废", "packNumber": medicine.Number},
					Time: time.Duration(time.Now().Unix()),
					UID:  user.ID,
					User: user.Name,
				})
				freezeIds = append(freezeIds, medicine.ID)
			}
			if project.ResearchAttribute == 1 {
				for _, medicine := range order.Medicines {
					operHistories = append(operHistories, models.History{
						Key:  keySku,
						OID:  medicine,
						Time: time.Duration(time.Now().Unix()),
						UID:  user.ID,
						User: user.Name,
					})
				}
			}

			ctx.Set("HISTORY", operHistories)

			//创建隔离记录
			freezeNumber, err := s.orderService.getFreezeNumber(ctx)
			medicineFreezeOID := primitive.NewObjectID()
			medicineFreeze := models.MedicineFreeze{
				ID:            medicineFreezeOID,
				CustomerID:    order.CustomerID,
				ProjectID:     order.ProjectID,
				EnvironmentID: order.EnvironmentID,
				InstituteType: 1,
				InstituteID:   order.ReceiveID,
				Number:        freezeNumber,
				Reason:        "WMS出库失败",
				Medicines:     freezeIds,
				History:       freezeIds,
				Meta: models.Meta{
					CreatedAt: time.Duration(time.Now().Unix()),
					CreatedBy: user.ID,
				},
			}
			if _, err := tools.Database.Collection("medicine_freeze").InsertOne(ctx, medicineFreeze); err != nil {
				return
			}
		} else { //出库成功,运送中
			orderInfo := models.UpdateOrderInfo{
				ID:     order.ID,
				Status: 2,
			}
			if data["carrier"] != nil {
				orderInfo.Carrier = data["carrier"].(string)

			}
			if data["LOGOrderNo"] != nil {
				orderInfo.LOGOrderNo = data["LOGOrderNo"].(string)
			}
			ctx.Set("user", user)
			s.orderService.UpdateOrder(ctx, orderInfo)
		}

		update := bson.M{
			"$set": bson.M{
				"status": 1,
			},
		}
		_, updateErr := tools.Database.Collection("request_interface").UpdateOne(nil, bson.M{"_id": recoveryRequestInterfaceID}, update)
		if updateErr != nil {
			return
		}
	}

	tools.Response(ctx, responErr)
}
