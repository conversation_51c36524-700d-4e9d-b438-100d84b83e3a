package service

import (
	"clinflash-irt/data"
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"math"
	"regexp"
	"strconv"
	"strings"
	"time"
)

func ExportUnblindingReport(ctx *gin.Context, projectID string, envID string, templateId string, projectSiteIds []string, now time.Time, cohortIds []string) (string, []byte, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	// project
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}

	match := bson.A{}
	filter := bson.M{"urgent_unblinding_status": 1, "env_id": envOID, "deleted": bson.M{"$ne": true}}
	attrFilter := bson.M{
		"env_id": envOID,
	}
	// 是否为cohort项目
	if len(cohortIds) > 0 {
		var cohortOIDs []primitive.ObjectID
		for _, cohortId := range cohortIds {
			cohortOID, _ := primitive.ObjectIDFromHex(cohortId)
			cohortOIDs = append(cohortOIDs, cohortOID)
		}
		filter["cohort_id"] = bson.M{
			"$in": cohortOIDs,
		}
		attrFilter["cohort_id"] = bson.M{
			"$in": cohortOIDs,
		}
	}

	var projectSiteOIds []primitive.ObjectID
	for _, siteId := range projectSiteIds {
		projectSiteOID, _ := primitive.ObjectIDFromHex(siteId)
		projectSiteOIds = append(projectSiteOIds, projectSiteOID)
	}
	filter["project_site_id"] = bson.M{
		"$in": projectSiteOIds,
	}

	// 查询项目属性配置
	var attributes []models.Attribute
	cursor, err := tools.Database.Collection("attribute").Find(nil, attrFilter)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}
	if err = cursor.All(ctx, &attributes); err != nil {
		return "", nil, errors.WithStack(err)
	}

	match = append(match, filter)
	pagePipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"$and": match}}},
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "project_site_id", "foreignField": "_id", "as": "project_site"}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$lookup", Value: bson.M{"from": "region", "localField": "project_site.region_id", "foreignField": "_id", "as": "region"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$region", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "urgent_unblinding_people", "foreignField": "_id", "as": "user"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$user", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$urgent_unblinding_approvals", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$match", Value: bson.M{"$or": bson.A{bson.M{"urgent_unblinding_people": primitive.NilObjectID, "urgent_unblinding_approvals.status": 1}, bson.M{"urgent_unblinding_people": bson.M{"$ne": primitive.NilObjectID}}}}}},
		{{Key: "$lookup", Value: bson.M{"from": "user", "localField": "urgent_unblinding_approvals.application_by", "foreignField": "_id", "as": "application_by"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$application_by", "preserveNullAndEmptyArrays": true}}},
		//
		{{Key: "$sort", Value: bson.D{{"serial_number", 1}}}},
		{{
			Key: "$project",
			Value: bson.M{
				"country":    bson.M{"$first": "$project_site.country"},
				"siteNumber": "$project_site.number",

				"siteName":                           models.ProjectSiteNameLookUpBson(ctx),
				"timeZone":                           "$project_site.time_zone",
				"tz":                                 "$project_site.tz",
				"randomNumber":                       "$random_number",
				"info":                               "$info",
				"urgentUnblindingPeople":             "$user.info.name",
				"urgentUnblindingPeopleUnicode":      "$user.info.unicode",
				"urgentUnblindingTime":               "$urgent_unblinding_time",
				"urgentUnblindingReason":             "$urgent_unblinding_reason",
				"urgentUnblindingReasonStr":          "$urgent_unblinding_reason_str",
				"isSponsor":                          "$is_sponsor",
				"remark":                             "$remark",
				"urgentUnblindingPeopleApply":        "$application_by.info.name",
				"urgentUnblindingPeopleApplyUnicode": "$application_by.info.unicode",
				"urgentUnblindingApprovalsReasonStr": "$urgent_unblinding_approvals.reason_str",
				"urgentUnblindingApprovalsRemark":    "$urgent_unblinding_approvals.remark",
				"cohort_id":                          1,
				"region":                             "$region.name",
			},
		}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "dispensing",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "dispensing",
		}}},
	}

	var subjectData []map[string]interface{}
	cursor, err = tools.Database.Collection("subject").Aggregate(nil, pagePipeline)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjectData)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	countries := bson.M{}
	countries, err = database.GetCountries(ctx)

	zone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return "", nil, err
	}

	subjectReplaceText := GetSubjectReplaceText(ctx, attributes[0])
	showRandomNumber := map[primitive.ObjectID]bool{}
	for _, attribute := range attributes {
		ok := true
		if len(cohortIds) > 0 {
			_, ok = slice.Find(cohortIds, func(index int, cohortID string) bool {
				return cohortID == attribute.CohortID.Hex()
			})
		}
		if attribute.AttributeInfo.IsRandomNumber && ok {
			showRandomNumber[attribute.CohortID] = true
		}
	}

	title := make([]interface{}, 0)
	content := make([][]interface{}, len(subjectData))
	title = append(title, locales.Tr(ctx, "report.attributes.project.number"))
	title = append(title, locales.Tr(ctx, "report.attributes.project.name"))

	if templateId != "" {

		if project.Type == 2 {
			title = append(title, locales.Tr(ctx, "report.attributes.random.cohort"))
		} else if project.Type == 3 {
			title = append(title, locales.Tr(ctx, "report.attributes.random.stage"))
		}

		templateOID, _ := primitive.ObjectIDFromHex(templateId)
		var template models.CustomTemplate
		err := tools.Database.Collection("custom_template").FindOne(context.Background(), bson.M{
			"_id": templateOID,
		}).Decode(&template)
		if err != nil {
			return "", nil, err
		}

		for _, field := range template.Fields {
			if field == "report.attributes.info.country" {
				title = append(title, locales.Tr(ctx, "medicine_download_country"))
			} else if field == "report.attributes.info.region" {
				title = append(title, locales.Tr(ctx, "medicine_download_region"))
			} else if field == "report.attributes.info.site.number" {
				title = append(title, locales.Tr(ctx, "site.number"))
			} else if field == "report.attributes.info.site.name" {
				title = append(title, locales.Tr(ctx, "site.name"))
			} else if field == "report.attributes.info.subject.number" {
				if subjectReplaceText != "" {
					title = append(title, subjectReplaceText)
				} else {
					title = append(title, locales.Tr(ctx, "subject.number"))
				}
			} else if field == "report.attributes.random.number" {
				if len(showRandomNumber) > 0 {
					title = append(title, locales.Tr(ctx, "export.random.number"))
				}
			} else if field == "report.attributes.unblinding.sponsor" {
				title = append(title, locales.Tr(ctx, "subject.unblinding.sponsor"))
			} else if field == "report.attributes.unblinding.mark" {
				title = append(title, locales.Tr(ctx, "export.unblinding.remark"))
			} else if field == "report.attributes.unblinding.reason" {
				title = append(title, locales.Tr(ctx, "export.unblinding.reason"))
			} else if field == "report.attributes.unblinding.reason.mark" {
				title = append(title, locales.Tr(ctx, "export.unblinding.reason_mark"))
			} else if field == "report.attributes.unblinding.operator" {
				title = append(title, locales.Tr(ctx, "export.unblinding.operator"))
			} else if field == "report.attributes.unblinding.time" {
				title = append(title, locales.Tr(ctx, "export.unblinding.operate_time"))
			} else if field == "report.attributes.dispensing.medicine.real.number" {
				title = append(title, locales.Tr(ctx, "export.dispensing.realMedicineNumber"))
			}
		}

		for i := 0; i < len(subjectData); i++ {
			var attribute models.Attribute
			for _, attr := range attributes {
				if attr.CohortID == subjectData[i]["cohort_id"] {
					attribute = attr
					break
				}
			}

			item := subjectData[i]
			row := make([]interface{}, 0)

			row = append(row, project.ProjectInfo.Number)
			row = append(row, project.ProjectInfo.Name)

			if project.Type == 2 || project.Type == 3 {
				var val string
				for _, env := range project.Environments {
					if env.ID == envOID {
						for _, cohort := range env.Cohorts {
							if cohort.ID == item["cohort_id"].(primitive.ObjectID) {
								val = models.GetCohortReRandomName(cohort)
								break
							}
						}
						break
					}
				}
				row = append(row, val)
			}

			for _, field := range template.Fields {
				if field == "report.attributes.info.country" {
					if _, ok := item["country"]; attribute.AttributeInfo.CountryLayered && ok {
						row = append(row, countries[item["country"].(string)])
					} else {
						row = append(row, "")
					}
				} else if field == "report.attributes.info.region" {
					if _, ok := item["region"]; attribute.AttributeInfo.RegionLayered && ok {
						row = append(row, item["region"])
					} else {
						row = append(row, "")
					}
				} else if field == "report.attributes.info.site.number" {
					row = append(row, item["siteNumber"])
				} else if field == "report.attributes.info.site.name" {
					row = append(row, item["siteName"])
				} else if field == "report.attributes.info.subject.number" {
					row = append(row, item["info"].(primitive.A)[0].(map[string]interface{})["value"].(string))
				} else if field == "report.attributes.random.number" {
					if len(showRandomNumber) > 0 {
						if attribute.AttributeInfo.IsRandomNumber { // 展示随机号
							row = append(row, item["randomNumber"])
						} else {
							row = append(row, "")
						}
					}
				} else if field == "report.attributes.unblinding.sponsor" {
					if project.UnblindingControl == 1 {
						row = append(row, "-")
						continue
					}
					isSponsorStr := locales.Tr(ctx, "common.no")
					if item["isSponsor"] != nil && item["isSponsor"].(bool) {
						isSponsorStr = locales.Tr(ctx, "common.yes")
					}
					row = append(row, isSponsorStr)
				} else if field == "report.attributes.unblinding.mark" {
					//if project.UnblindingControl == 1 {
					//	row = append(row, "-")
					//} else {
					//	row = append(row, item["remark"])
					//}
					if item["remark"] != nil {
						row = append(row, item["remark"])
					} else {
						row = append(row, "-")
					}
				} else if field == "report.attributes.unblinding.reason" {
					if item["urgentUnblindingPeople"] != nil {
						row = append(row, item["urgentUnblindingReasonStr"])
					} else {
						row = append(row, item["urgentUnblindingApprovalsReasonStr"])
					}
				} else if field == "report.attributes.unblinding.reason.mark" {
					row = append(row, item["urgentUnblindingReason"])
				} else if field == "report.attributes.unblinding.reason_mark" {
					if (item["urgentUnblindingReasonStr"] != nil && item["urgentUnblindingReasonStr"] == "其他") || (item["urgentUnblindingApprovalsReasonStr"] != nil && item["urgentUnblindingApprovalsReasonStr"] == "其他") {
						row = append(row, item["urgentUnblindingReason"])
						//row = append(row, item["urgentUnblindingApprovalsRemark"])
					} else {
						row = append(row, "-")
					}
				} else if field == "report.attributes.unblinding.operator" {
					if item["urgentUnblindingPeople"] != nil {
						row = append(row, fmt.Sprintf("%s(%d)", item["urgentUnblindingPeople"], item["urgentUnblindingPeopleUnicode"]))
					} else {
						row = append(row, fmt.Sprintf("%s(%d)", item["urgentUnblindingPeopleApply"], item["urgentUnblindingPeopleApplyUnicode"]))
					}
				} else if field == "report.attributes.unblinding.time" {
					if item["tz"] != nil && item["tz"] != "" {
						//timeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
						//var randomTime string
						//randomTime = time.Unix(item["urgentUnblindingTime"].(int64), 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
						//randomTime = randomTime + "(" + item["timeZone"].(string) + ")"

						var randomTime string
						timeStr, err := tools.GetLocationUtc(item["tz"].(string), item["urgentUnblindingTime"].(int64))
						if err != nil {
							panic(err)
						}
						randomTime = timeStr
						row = append(row, randomTime)

					} else if item["timeZone"] != nil && item["timeZone"] != "" {
						timeZone, _ := tools.ParseTimezoneOffset(item["timeZone"].(string))
						hours := time.Duration(timeZone)
						minutes := time.Duration((timeZone - float64(hours)) * 60)
						duration := hours*time.Hour + minutes*time.Minute
						var randomTime string
						randomTime = time.Unix(item["urgentUnblindingTime"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						//strTimeZone := fmt.Sprintf("UTC%+d", zone)
						randomTime = randomTime + "(" + item["timeZone"].(string) + ")"
						row = append(row, randomTime)
					} else {
						var randomTime string
						hours := time.Duration(zone)
						minutes := time.Duration((zone - float64(hours)) * 60)
						duration := hours*time.Hour + minutes*time.Minute
						randomTime = time.Unix(item["urgentUnblindingTime"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						//strTimeZone := fmt.Sprintf("UTC%+d", zone)
						strTimeZone := tools.FormatOffsetToZoneStringUtc(zone)
						randomTime = randomTime + "(" + strTimeZone + ")"
						row = append(row, randomTime)
					}
				} else if field == "report.attributes.dispensing.medicine.real.number" {
					var realMedicine []string
					for _, dep := range item["dispensing"].(primitive.A) {
						if dep.(map[string]interface{})["real_dispensing_medicines"] != nil {
							for _, med := range dep.(map[string]interface{})["real_dispensing_medicines"].(primitive.A) {
								realMedicine = append(realMedicine, med.(map[string]interface{})["number"].(string))
							}
						}
					}
					for _, dep := range item["dispensing"].(primitive.A) {
						if dep.(map[string]interface{})["real_other_dispensing_medicines"] != nil {
							for _, med := range dep.(map[string]interface{})["real_other_dispensing_medicines"].(primitive.A) {
								medMap := med.(map[string]interface{})
								realMedicine = append(realMedicine,
									convertor.ToString(medMap["name"])+"/"+
										convertor.ToString(medMap["count"])+"/"+
										convertor.ToString(medMap["expire_date"])+"/"+
										convertor.ToString(medMap["batch"]))
							}
						}
					}
					row = append(row, strings.Join(realMedicine, ";"))
				}
			}
			content[i] = row
		}
	} else {
		title = append(title, locales.Tr(ctx, "medicine_download_country"))
		title = append(title, locales.Tr(ctx, "medicine_download_region"))
		title = append(title, locales.Tr(ctx, "site.number"))
		title = append(title, locales.Tr(ctx, "site.name"))

		if project.Type == 2 {
			title = append(title, locales.Tr(ctx, "report.attributes.random.cohort"))
		} else if project.Type == 3 {
			title = append(title, locales.Tr(ctx, "report.attributes.random.stage"))
		}
		if subjectReplaceText != "" {
			title = append(title, subjectReplaceText)
		} else {
			title = append(title, locales.Tr(ctx, "subject.number"))
		}
		if len(showRandomNumber) > 0 {
			title = append(title, locales.Tr(ctx, "export.random.number"))
		}
		title = append(title, locales.Tr(ctx, "subject.unblinding.sponsor"))
		title = append(title, locales.Tr(ctx, "export.unblinding.remark"))
		title = append(title, locales.Tr(ctx, "export.unblinding.reason"))
		title = append(title, locales.Tr(ctx, "export.unblinding.reason_mark"))
		title = append(title, locales.Tr(ctx, "export.unblinding.operator"))
		title = append(title, locales.Tr(ctx, "export.unblinding.operate_time"))

		for i := 0; i < len(subjectData); i++ {
			var attribute models.Attribute
			for _, attr := range attributes {
				if attr.CohortID == subjectData[i]["cohort_id"] {
					attribute = attr
					break
				}
			}

			row := make([]interface{}, 0)
			row = append(row, project.ProjectInfo.Number)
			row = append(row, project.ProjectInfo.Name)
			item := subjectData[i]
			isSponsorStr := locales.Tr(ctx, "common.no")
			if item["isSponsor"] != nil && item["isSponsor"].(bool) {
				isSponsorStr = locales.Tr(ctx, "common.yes")
			}
			if _, ok := item["country"]; attribute.AttributeInfo.CountryLayered && ok {
				row = append(row, countries[item["country"].(string)])
			} else {
				row = append(row, "")
			}
			if _, ok := item["region"]; attribute.AttributeInfo.RegionLayered && ok {
				row = append(row, item["region"].(string))
			} else {
				row = append(row, "")
			}
			row = append(row, item["siteNumber"])
			row = append(row, item["siteName"])
			if project.Type == 2 || project.Type == 3 {
				var val string
				for _, env := range project.Environments {
					if env.ID == envOID {
						for _, cohort := range env.Cohorts {
							if cohort.ID == item["cohort_id"].(primitive.ObjectID) {
								val = models.GetCohortReRandomName(cohort)
								break
							}
						}
						break
					}
				}
				row = append(row, val)
			}
			row = append(row, item["info"].(primitive.A)[0].(map[string]interface{})["value"].(string))
			if len(showRandomNumber) > 0 {
				if attribute.AttributeInfo.IsRandomNumber { // 展示随机号
					row = append(row, item["randomNumber"])
				} else {
					row = append(row, "")
				}
			}
			if project.UnblindingControl == 1 {
				row = append(row, "-")
			} else {
				row = append(row, isSponsorStr)
			}
			//if project.UnblindingControl == 1 {
			//	row = append(row, "-")
			//} else {
			//	row = append(row, item["remark"])
			//}
			if item["remark"] != nil {
				row = append(row, item["remark"])
			} else {
				row = append(row, "-")
			}

			if item["urgentUnblindingPeople"] != nil {
				row = append(row, item["urgentUnblindingReasonStr"])
			} else {
				row = append(row, item["urgentUnblindingApprovalsReasonStr"])
			}
			if (item["urgentUnblindingReasonStr"] != nil && item["urgentUnblindingReasonStr"] == "其他") || (item["urgentUnblindingApprovalsReasonStr"] != nil && item["urgentUnblindingApprovalsReasonStr"] == "其他") {
				row = append(row, item["urgentUnblindingReason"])
				//row = append(row, item["urgentUnblindingApprovalsRemark"])
			} else {
				row = append(row, "-")
			}
			if item["urgentUnblindingPeople"] != nil {
				row = append(row, item["urgentUnblindingPeople"])
			} else {
				row = append(row, item["urgentUnblindingPeopleApply"])
			}

			if item["tz"] != nil && item["tz"] != "" {
				//timeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
				//var randomTime string
				//randomTime = time.Unix(item["urgentUnblindingTime"].(int64), 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
				//randomTime = randomTime + "(" + item["timeZone"].(string) + ")"

				var randomTime string
				timeStr, err := tools.GetLocationUtc(item["tz"].(string), item["urgentUnblindingTime"].(int64))
				if err != nil {
					panic(err)
				}
				randomTime = timeStr
				row = append(row, randomTime)

			} else if item["timeZone"] != nil && item["timeZone"] != "" {
				timeZone, _ := tools.ParseTimezoneOffset(item["timeZone"].(string))
				hours := time.Duration(timeZone)
				minutes := time.Duration((timeZone - float64(hours)) * 60)
				duration := hours*time.Hour + minutes*time.Minute
				var randomTime string
				randomTime = time.Unix(item["urgentUnblindingTime"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				//strTimeZone := fmt.Sprintf("UTC%+d", zone)
				randomTime = randomTime + "(" + item["timeZone"].(string) + ")"
				row = append(row, randomTime)
			} else {
				hours := time.Duration(zone)
				minutes := time.Duration((zone - float64(hours)) * 60)
				duration := hours*time.Hour + minutes*time.Minute
				var randomTime string
				randomTime = time.Unix(item["urgentUnblindingTime"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				//strTimeZone := fmt.Sprintf("UTC%+d", zone)
				strTimeZone := tools.FormatOffsetToZoneStringUtc(zone)
				randomTime = randomTime + "(" + strTimeZone + ")"
				row = append(row, randomTime)
			}

			content[i] = row
		}
	}

	envCode := getEnvName(project, envOID)
	fileName := fmt.Sprintf("%s[%s]UnblindingReport_%s.xlsx", project.Number, envCode, now.Format("20060102150405"))
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	tools.ExportSheet(f, "Sheet1", title, content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, err
	}
	return fileName, buffer.Bytes(), errors.WithStack(err)
}

func ExportSubjectStatisticsReport(ctx *gin.Context, envID string, roleID string, cohortIDs []string, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOIDs := make([]primitive.ObjectID, 0)
	if len(cohortIDs) > 0 {
		for _, cohortID := range cohortIDs {
			cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
			cohortOIDs = append(cohortOIDs, cohortOID)
		}
	}

	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return "", nil, err
	}
	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	cohorts := slice.Filter(envP.Cohorts, func(index int, item models.Cohort) bool {
		return slice.Contain(cohortOIDs, item.ID)
	})
	attributeFilter := bson.M{"env_id": envOID}
	if len(cohortIDs) > 0 {
		attributeFilter = bson.M{"env_id": envOID, "cohort_id": bson.M{"$in": cohortOIDs}}
	}
	attributes := make([]models.Attribute, 0)
	cursor, err := tools.Database.Collection("attribute").Find(nil, attributeFilter)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	_, haveRandom := slice.Find(attributes, func(index int, item models.Attribute) bool {
		return item.AttributeInfo.Random
	})
	_, haveScreen := slice.Find(attributes, func(index int, item models.Attribute) bool {
		return item.AttributeInfo.IsScreen
	})
	_, haveBlind := slice.Find(attributes, func(index int, item models.Attribute) bool {
		return item.AttributeInfo.Blind
	})
	_, haveJustDispensing := slice.Find(attributes, func(index int, item models.Attribute) bool {
		return item.AttributeInfo.Dispensing && !item.AttributeInfo.Random
	})
	haveCohortReRandom := false
	if project.Type == 2 {
		_, b := slice.Find(cohorts, func(index int, item models.Cohort) bool {
			return item.Type == 1
		})
		if b {
			haveCohortReRandom = true
		}
	}
	stageProject := (project.Type == 3 && !tools.InRandomIsolation(project.Number)) || (project.Type == 2 && haveCohortReRandom)
	firstCohort := false
	secondCohort := false
	if stageProject {
		if project.Type == 3 && !tools.InRandomIsolation(project.Number) {
			_, firstCohort = slice.Find(cohorts, func(index int, item models.Cohort) bool {
				return item.ID == envP.Cohorts[0].ID
			})
			_, secondCohort = slice.Find(cohorts, func(index int, item models.Cohort) bool {
				return item.ID == envP.Cohorts[1].ID
			})
		} else if project.Type == 2 && haveCohortReRandom {
			_, firstCohort = slice.Find(cohorts, func(index int, item models.Cohort) bool {
				return item.Type == 1 && item.LastID.IsZero()
			})
			_, secondCohort = slice.Find(cohorts, func(index int, item models.Cohort) bool {
				return item.Type == 1 && !item.LastID.IsZero()
			})
		}
	}

	d, err := GetSubjectStatisticsData(ctx, envID, roleID, cohortIDs)
	if err != nil {
		return "", nil, err
	}
	siteData, err := GetSubjectSiteStatisticsData(ctx, envID, roleID, cohortIDs)
	if err != nil {
		return "", nil, err
	}

	projectInfoTitle := []interface{}{
		locales.Tr(ctx, "report.attributes.project.number"),
		locales.Tr(ctx, "report.attributes.project.name"),
	}
	projectSiteInfoTitle := []interface{}{
		locales.Tr(ctx, "export.dispensing.siteNumber"),
		locales.Tr(ctx, "export.dispensing.siteName"),
	}

	registerInfoTitle := []interface{}{
		locales.Tr(ctx, "export.random.register"),
	}
	justDispensingInfoTitle := []interface{}{
		locales.Tr(ctx, "subject.status.join"),
	}

	screenInfoTitle := []interface{}{
		locales.Tr(ctx, "export.random.screenSuccess"),
		locales.Tr(ctx, "export.random.screenFail"),
	}

	toBeRandomInfoTitle := []interface{}{
		//locales.Tr(ctx, "export.random.toBeRandom") + "(" + secondCohortName + ")",
		locales.Tr(ctx, "export.random.toBeRandom"),
	}

	randomInfoTitle := []interface{}{
		locales.Tr(ctx, "export.random.random"),
	}
	exitInfoTitle := []interface{}{
		locales.Tr(ctx, "export.random.exit"),
	}

	unBlindInfoTitle := []interface{}{
		locales.Tr(ctx, "export.random.unBlind"),
	}
	pvInfoTitle := []interface{}{
		locales.Tr(ctx, "export.random.pv"),
	}

	finishInfoTitle := []interface{}{
		locales.Tr(ctx, "export.random.finish"),
	}

	firstTitle := projectInfoTitle
	secondTitle := make([]interface{}, 0)
	firstContent := make([]interface{}, 0)

	firstContent = []interface{}{
		project.Number,
		project.Name,
	}
	if stageProject {
		if firstCohort {
			firstTitle = append(firstTitle, registerInfoTitle...)
			firstContent = append(firstContent, []interface{}{strconv.Itoa(d.Register)}...)

			if haveScreen {
				firstTitle = append(firstTitle, screenInfoTitle...)
				firstContent = append(firstContent, []interface{}{strconv.Itoa(d.ScreenSuccess), strconv.Itoa(d.ScreenFail)}...)
			}
		}
		if secondCohort {
			firstTitle = append(firstTitle, toBeRandomInfoTitle...)
			firstContent = append(firstContent, []interface{}{strconv.Itoa(d.ToBeRandom)}...)
		}
	} else {
		firstTitle = append(firstTitle, registerInfoTitle...)
		firstContent = append(firstContent, []interface{}{strconv.Itoa(d.Register)}...)
		if haveJustDispensing {
			firstTitle = append(firstTitle, justDispensingInfoTitle...)
			firstContent = append(firstContent, []interface{}{strconv.Itoa(d.Join)}...)
		}
		if haveScreen {
			firstTitle = append(firstTitle, screenInfoTitle...)
			firstContent = append(firstContent, []interface{}{strconv.Itoa(d.ScreenSuccess), strconv.Itoa(d.ScreenFail)}...)
		}
	}

	if haveRandom {
		firstTitle = append(firstTitle, randomInfoTitle...)
		firstContent = append(firstContent, []interface{}{strconv.Itoa(d.Random)}...)
	}

	firstTitle = append(firstTitle, exitInfoTitle...)
	firstContent = append(firstContent, []interface{}{strconv.Itoa(d.Exit)}...)

	if haveBlind && haveRandom {
		firstTitle = append(firstTitle, unBlindInfoTitle...)
		firstContent = append(firstContent, []interface{}{strconv.Itoa(d.UnBlind)}...)
		firstTitle = append(firstTitle, pvInfoTitle...)
		firstContent = append(firstContent, []interface{}{strconv.Itoa(d.PvUnBlind)}...)

	}
	firstTitle = append(firstTitle, finishInfoTitle...)
	firstContent = append(firstContent, []interface{}{strconv.Itoa(d.Finish)}...)

	secondTitle = append(projectInfoTitle, projectSiteInfoTitle...)
	secondTitle = append(secondTitle, firstTitle[2:]...)

	if project.Type == 2 || project.Type == 3 {
		cohortNames := make([]string, 0)
		if len(cohortOIDs) > 0 {
			for _, cohort := range envP.Cohorts {
				if slice.Contain(cohortOIDs, cohort.ID) {
					cohortNames = append(cohortNames, models.GetCohortReRandomName(cohort))
				}
			}
		} else {
			for _, cohort := range envP.Cohorts {
				cohortNames = append(cohortNames, models.GetCohortReRandomName(cohort))
			}
		}
		cohortName := strings.Join(cohortNames, "&")
		if project.Type == 2 {
			firstTitle = append(firstTitle, locales.Tr(ctx, "report.attributes.random.cohort"))
		} else if project.Type == 3 {
			firstTitle = append(firstTitle, locales.Tr(ctx, "report.attributes.random.stage"))
		}
		firstContent = append(firstContent, cohortName)
	}
	var firstContents [][]interface{}
	firstContents = append(firstContents, firstContent)
	if project.Type == 2 {
		secondTitle = append(secondTitle, locales.Tr(ctx, "report.attributes.random.cohort"))
	} else if project.Type == 3 {
		secondTitle = append(secondTitle, locales.Tr(ctx, "report.attributes.random.stage"))
	}
	var secondContents [][]interface{}
	for _, item := range siteData {
		var content []interface{}
		content = append(content, project.Number)
		content = append(content, project.Name)
		content = append(content, item.Number)
		content = append(content, item.Name)
		register := 0
		randomCount := 0
		exit := 0
		unBlind := 0
		pv := 0
		screenSuccess := 0
		screenFail := 0
		finish := 0
		toBeRandom := 0
		join := 0
		for _, status := range item.SubjectStatusCount {
			switch status.Status {
			case 1:
				register = int(status.Count)
			case 3:
				randomCount = int(status.Count)
			case 5:
				exit = exit + int(status.Count)
			case 6:
				unBlind = int(status.Count)
			case 7:
				screenSuccess = int(status.Count)
			case 8:
				screenFail = int(status.Count)
			case 9:
				finish = int(status.Count)
			case 10:
				toBeRandom = int(status.Count)
			case 11:
				join = int(status.Count)
			case 12:
				pv = int(status.Count)
			}
		}

		if stageProject {
			if firstCohort {
				content = append(content, register)
				if haveScreen {
					content = append(content, screenSuccess)
					content = append(content, screenFail)
				}
			}
			if secondCohort {
				content = append(content, toBeRandom)
			}
		} else {
			content = append(content, register)
			if haveJustDispensing {
				content = append(content, join)
			}
			if haveScreen {
				content = append(content, screenSuccess)
				content = append(content, screenFail)
			}
		}
		if haveRandom {
			content = append(content, randomCount)

		}
		content = append(content, exit)
		if haveBlind && haveRandom {
			content = append(content, unBlind)
			content = append(content, pv)
		}
		content = append(content, finish)
		if project.Type == 2 || project.Type == 3 {
			cohortNames := make([]string, 0)
			if len(cohortOIDs) > 0 {
				for _, cohort := range envP.Cohorts {
					if slice.Contain(cohortOIDs, cohort.ID) {
						cohortNames = append(cohortNames, models.GetCohortReRandomName(cohort))
					}
				}
			} else {
				for _, cohort := range envP.Cohorts {
					cohortNames = append(cohortNames, models.GetCohortReRandomName(cohort))
				}
			}
			cohortName := strings.Join(cohortNames, "&")

			content = append(content, cohortName)
		}
		secondContents = append(secondContents, content)
	}

	fileName := fmt.Sprintf("%s[%s]SubjectStatisticsExport_%s.xlsx", project.Number, getEnvName(project, envOID), now.Format("20060102150405"))

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	tools.ExportSheet(f, "Sheet1", firstTitle, firstContents)
	f.NewSheet("Sheet2")
	tools.ExportSheet(f, "Sheet2", secondTitle, secondContents)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	return fileName, buffer.Bytes(), nil

}

func ExportReportTypeRandomizeReport(ctx *gin.Context, projectID string, envID string, roleID string, cohortIds []interface{}, projectSiteIds []interface{}, subjects []interface{}, templateId string, now time.Time) (string, []byte, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	attrFilter := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	randomFilter := bson.M{
		"env_id":     envOID,
		"project_id": projectOID,
	}
	selectCohort := map[primitive.ObjectID]bool{}
	var cohortOIDs []primitive.ObjectID
	for _, cohortId := range cohortIds {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortId.(string))
		cohortOIDs = append(cohortOIDs, cohortOID)
		selectCohort[cohortOID] = true
	}

	var projectSiteOIDs []primitive.ObjectID
	for _, projectSiteId := range projectSiteIds {
		projectSiteOID, _ := primitive.ObjectIDFromHex(projectSiteId.(string))
		projectSiteOIDs = append(projectSiteOIDs, projectSiteOID)
	}

	// project

	var subjectOIDs []primitive.ObjectID
	for _, subject := range subjects {
		subjectOID, _ := primitive.ObjectIDFromHex(subject.(string))
		subjectOIDs = append(subjectOIDs, subjectOID)
	}

	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return "", nil, errors.WithStack(err)
	}

	envInfo, _ := database.GetEnvCohortInfo(project, envOID, primitive.NilObjectID)
	if len(cohortOIDs) == 0 {
		cohortOIDs = slice.Map(envInfo.Cohorts, func(index int, item models.Cohort) primitive.ObjectID {
			return item.ID
		})
	} else { // 群组再随机选了第一阶段  自动选上第二阶段  过滤隔离项目
		slice.ForEach(envInfo.Cohorts, func(index int, item models.Cohort) {
			if selectCohort[item.LastID] && !tools.InRandomIsolation(project.Number) {
				cohortOIDs = append(cohortOIDs, item.ID)
				selectCohort[item.ID] = true
			}
		})
	}

	haveCohortReRandom := models.HaveCohortReRandomInCohortIds(project, envID, cohortOIDs)

	match := bson.A{}
	//var statusArray = [...]int{3, 4, 5, 6}
	filter := bson.M{"project_id": projectOID, "env_id": envOID, "_id": bson.M{"$in": subjectOIDs}, "deleted": bson.M{"$ne": true}}
	match = append(match, filter)

	pagePipeline := mongo.Pipeline{
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "project_site_id", "foreignField": "_id", "as": "project_site"}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "subject", // 关联的集合名称
			"let": bson.M{
				"replace_subject_id": "$replace_subject_id", // 将当前文档的 replace_subject_id 字段赋值给变量 replace_subject_id
			},
			"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
				bson.M{
					"$match": bson.M{
						"$expr": bson.M{
							"$and": bson.A{
								bson.M{"$eq": bson.A{"$_id", "$$replace_subject_id"}}, // 关联条件：_id 等于 replace_subject_id
								bson.M{"$ne": bson.A{"$deleted", true}},               // 过滤条件：deleted 不为 true
							},
						},
					},
				},
			},
			"as": "replace_subject", // 输出字段名称
		}}},
		{{Key: "$lookup", Value: bson.M{"from": "history", "localField": "_id", "foreignField": "oid", "as": "history"}}},
		{{Key: "$unwind", Value: "$project_site"}},
		{{Key: "$lookup", Value: bson.M{"from": "region", "localField": "project_site.region_id", "foreignField": "_id", "as": "region"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$region", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$replace_subject", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$match", Value: bson.M{"$and": match}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "dispensing",
			"let": bson.M{
				"subject_id": "$_id",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$subject_id", "$$subject_id"}}}},
				bson.M{"$project": bson.M{
					"_id":             0,
					"status":          1,
					"visit_number":    "$visit_info.number",
					"serial_number":   1,
					"visit_sign":      1,
					"dispensing_time": 1,
				}},
			},
			"as": "dispensing",
		}}},

		{{Key: "$sort", Value: bson.D{{"random_time", 1}}}},
		{{
			Key: "$project",
			Value: bson.M{
				"_id":                     1,
				"project_id":              1,
				"env_id":                  1,
				"cohort_id":               1,
				"project_site_id":         1,
				"site_number":             "$project_site.number",
				"random_list_id":          1,
				"register_random_list_id": 1,
				"history":                 1,
				"site_name":               models.ProjectSiteNameLookUpBson(ctx),
				"time_zone":               "$project_site.time_zone",
				"tz":                      "$project_site.tz",
				"random_number":           1,
				"subject":                 "$info",
				"actual_info":             "$actual_info",
				"random_time":             "$random_time",
				"register_time":           "$meta.created_at",
				"status":                  "$status",
				"group":                   "$group",
				"par_group_name":          "$par_group_name",
				"sub_group_name":          "$sub_group_name",
				"replace_subject_id":      1,
				"replace_subject":         "$replace_subject.info",
				"replace_number":          bson.M{"$ifNull": bson.A{"$replace_number", ""}},
				"replace_subject_status":  "$replace_subject.status",
				"country":                 bson.M{"$first": "$project_site.country"},
				"region":                  "$region.name",
				"is_screen":               1,
				"screen_time":             1,
				"icf_time":                1,
				"sign_out_real_time":      1,
				"sign_out_people":         1,
				"sign_out_time":           1,
				"sign_out_reason":         1,
				"join_time":               1,
				"register_group":          1,
				"finish_remark":           1,
				"random_sequence_number":  1,
				"dispensing":              "$dispensing",
			},
		}},
	}

	var subjectData []models.ExportSubjectNumber
	optTrue := true
	opt := &options.AggregateOptions{
		AllowDiskUse: &optTrue,
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, pagePipeline, opt)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjectData)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	userIds := make([]primitive.ObjectID, 0)
	for _, d := range subjectData {
		for _, h := range d.History {
			userIds = append(userIds, h.UID)
		}
	}
	userIds = slice.Unique(userIds)
	users := make([]models.User, 0)
	userMap := make(map[primitive.ObjectID]int32)
	if len(userIds) > 0 {
		cursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIds}})
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &users)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		for _, u := range users {
			userMap[u.ID] = u.Unicode
		}
	}

	// 替换受试者 获取 被替换的随机列表
	// 按随机时间排序 取出替换受试者对应的随机表
	dataMap := map[primitive.ObjectID]primitive.ObjectID{}
	// 1 替换 2  2 替换3  {1: 1list}  {2: 1list} {3: 1list}
	for i := range subjectData {
		if subjectData[i].ReplaceNumber != "" {
			dataMap[subjectData[i].ReplaceSubjectID] = subjectData[i].RandomListID
		}
		if dataMap[subjectData[i].ID] != primitive.NilObjectID {
			subjectData[i].RandomListID = dataMap[subjectData[i].ID]
		}
	}

	// 按ID排序
	err = slice.SortByField(subjectData, "RegisterTime")
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	// 查询随机表配置(用于取出分层因素)
	var randomLists []models.RandomList
	cursor, err = tools.Database.Collection("random_list").Find(nil, randomFilter, &options.FindOptions{
		Sort: bson.D{{"meta.created_at", 1}},
	})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomLists)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	haveSubGroup := false
	_, haveSubGroup = slice.Find(randomLists, func(index int, item models.RandomList) bool {
		_, b := slice.Find(item.Design.Groups, func(index int, i models.RandomListGroup) bool {
			return i.SubName != ""
		})
		return b
	})
	forms := make([]models.Form, 0)
	cursor, err = tools.Database.Collection("form").Find(nil, attrFilter)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &forms)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	if len(cohortOIDs) > 0 {
		forms = slice.Filter(forms, func(index int, item models.Form) bool {
			_, b := slice.Find(cohortOIDs, func(index int, cohortID primitive.ObjectID) bool {
				return cohortID == item.CohortID
			})
			return b
		})
	}

	//区分form字段是否参与了自定义公式计算
	for _, form := range forms {
		lists := slice.Filter(randomLists, func(index int, item models.RandomList) bool {
			return item.CohortID == form.CohortID
		})
		for _, list := range lists {
			for _, factor := range list.Design.Factors {
				if factor.IsCalc {
					re := regexp.MustCompile(`\{([\x{4e00}-\x{9fa5}\w]+)\}`)
					customFormulas := factor.CustomFormulas
					matches := re.FindAllStringSubmatch(customFormulas, -1)
					fieldNames := slice.Map(matches, func(index int, item []string) string {
						return item[1]
					})
					for i, field := range form.Fields {
						if field.Variable != "" && slice.Contain(fieldNames, field.Variable) {
							form.Fields[i].IsCustomFormulaField = true
						}
					}

				}
			}
		}
	}

	// 查询项目属性配置
	var attributes []models.Attribute
	showRandomNumber := map[primitive.ObjectID]bool{}
	showRandomSequenceNumber := map[primitive.ObjectID]bool{}
	showScreenTime := map[primitive.ObjectID]bool{}
	showRegisterGroup := map[primitive.ObjectID]bool{}
	cursor, err = tools.Database.Collection("attribute").Find(nil, attrFilter, &options.FindOptions{
		Sort: bson.D{{"_id", 1}},
	})
	err = cursor.All(nil, &attributes)
	if err != nil {
		return "", nil, err
	}
	for _, attribute := range attributes {
		ok := true
		if len(cohortOIDs) > 0 {
			_, ok = slice.Find(cohortOIDs, func(index int, cohortID primitive.ObjectID) bool {
				return cohortID == attribute.CohortID
			})
		}
		if attribute.AttributeInfo.IsRandomNumber && ok {
			showRandomNumber[attribute.CohortID] = true
		}
		if attribute.AttributeInfo.IsScreen && ok {
			showScreenTime[attribute.CohortID] = true
		}
		if attribute.AttributeInfo.AllowRegisterGroup && ok {
			showRegisterGroup[attribute.CohortID] = true
		}
		if attribute.AttributeInfo.IsRandomSequenceNumber && ok {
			showRandomSequenceNumber[attribute.CohortID] = true
		}
	}

	subjectReplaceText := ""
	if locales.Lang(ctx) == "zh" {
		if len(attributes[0].AttributeInfo.SubjectReplaceText) != 0 {
			subjectReplaceText = attributes[0].AttributeInfo.SubjectReplaceText
		} else {
			if len(attributes[0].AttributeInfo.SubjectReplaceTextEn) != 0 {
				subjectReplaceText = attributes[0].AttributeInfo.SubjectReplaceTextEn
			}
		}
	} else if locales.Lang(ctx) == "en" {
		if len(attributes[0].AttributeInfo.SubjectReplaceTextEn) != 0 {
			subjectReplaceText = attributes[0].AttributeInfo.SubjectReplaceTextEn
		} else {
			if len(attributes[0].AttributeInfo.SubjectReplaceText) != 0 {
				subjectReplaceText = attributes[0].AttributeInfo.SubjectReplaceText
			}
		}
	}
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	countries, err := database.GetCountries(ctx)
	cohortMap := map[primitive.ObjectID]string{}

	var resultData []models.SubjectView
	for _, result := range subjectData {
		subjectView := models.SubjectView{
			ID:           result.ID,
			CohortID:     result.CohortID,
			RandomListID: result.RandomListID,
			Info:         result.Subject,
		}
		resultData = append(resultData, subjectView)
	}
	usedInfos := getUsedInfo(resultData, forms, randomLists, attributes[0])

	title := make([]interface{}, 0)
	allCohortIDs := make([]primitive.ObjectID, 0)
	for _, environment := range project.Environments {
		if environment.ID == envOID {
			for _, cohort := range environment.Cohorts {
				//cohortName := models.GetCohortReRandomName(cohort)
				cohortMap[cohort.ID] = cohort.Name
				allCohortIDs = append(allCohortIDs, cohort.ID)
			}
			break
		}
	}

	var template models.CustomTemplate
	fieldsArray := []string{}
	var glUsedInfoLength int
	if templateId != "" {
		templateOID, _ := primitive.ObjectIDFromHex(templateId)
		err = tools.Database.Collection("custom_template").FindOne(nil, bson.M{
			"_id": templateOID,
		}).Decode(&template)
		if err != nil {
			return "", nil, errors.WithStack(err)
		}
		hasRandomFactor := false
		for _, field := range template.Fields {
			if field == data.ReportAttributesInfoSubjectNumber {
				if subjectReplaceText != "" {
					title = append(title, subjectReplaceText)
					fieldsArray = append(fieldsArray, field)
					continue
				}
			}
			if field == data.ReportAttributesRandomSubjectNumberReplace {
				if subjectReplaceText != "" {
					title = append(title, locales.Tr(ctx, "report.attributes.random.subject.number.replace.substitute")+subjectReplaceText)
					fieldsArray = append(fieldsArray, field)
					continue
				}
			}
			if field == data.ReportAttributesRandomNumber && len(showRandomNumber) == 0 {
				continue
			}
			if field == data.ReportAttributesRandomSequenceNumber && len(showRandomSequenceNumber) == 0 {
				continue
			}

			if field == data.ReportAttributesDispensingMedicineRealGroup && len(showRegisterGroup) == 0 {
				continue
			}

			//没开启筛选流程就去掉筛选时间
			if field == data.ReportAttributesRandomScreenTime {
				if len(showScreenTime) == 0 {
					continue
				}
				_, b := slice.Find(attributes, func(index int, item models.Attribute) bool {
					return item.AttributeInfo.IsScreen
				})
				if !b {
					continue
				}
			}
			fieldsArray = append(fieldsArray, field)
			//再随机
			if project.Type == 3 || (project.Type == 2 && haveCohortReRandom) {
				if field == data.ReportAttributesRandomTime ||
					field == data.ReportAttributesRandomOperator ||
					field == data.ReportAttributesRandomSubjectReplaceNumber ||
					field == data.ReportAttributesInfoStatus ||
					field == data.ReportAttributesRandomFactor ||
					field == data.ReportAttributesRandomConfigCode ||
					field == data.ReportAttributesRandomGroup ||
					field == data.ReportAttributesDispensingMedicineRealGroup ||
					field == data.ReportAttributesRandomNumber ||
					field == data.ReportAttributesRandomSequenceNumber ||
					field == data.ReportAttributesRandomFactorCalc ||
					field == data.ReportAttributesRandomActualFactor ||
					field == data.ReportAttributesRandomForm {
					if hasRandomFactor {
						continue
					}
					hasRandomFactor = true
					for _, cohort := range envInfo.Cohorts {
						if len(selectCohort) > 0 && selectCohort[cohort.ID] == false {
							continue
						}
						isShowRandomNumber := false
						_, ok := slice.Find(attributes, func(index int, attribute models.Attribute) bool {
							return cohort.ID == attribute.CohortID && attribute.AttributeInfo.IsRandomNumber
						})
						if ok {
							isShowRandomNumber = true
						}
						isShowRandomSequenceNumber := false
						_, ok = slice.Find(attributes, func(index int, attribute models.Attribute) bool {
							return cohort.ID == attribute.CohortID && attribute.AttributeInfo.IsRandomSequenceNumber
						})
						if ok {
							isShowRandomSequenceNumber = true
						}

						isRegisterGroup := false
						_, isRegisterGroupOk := slice.Find(attributes, func(index int, attribute models.Attribute) bool {
							return cohort.ID == attribute.CohortID && attribute.AttributeInfo.AllowRegisterGroup
						})
						if isRegisterGroupOk {
							isRegisterGroup = true
						}

						for _, curField := range template.Fields {
							cohortName := models.GetCohortReRandomName(cohort)
							if curField == data.ReportAttributesRandomTime {
								title = append(title, cohortName+"-"+locales.Tr(ctx, curField))
							}
							if curField == data.ReportAttributesRandomOperator {
								title = append(title, cohortName+"-"+locales.Tr(ctx, curField))
							}
							if curField == data.ReportAttributesInfoStatus {
								title = append(title, cohortName+"-"+locales.Tr(ctx, data.ReportAttributesInfoStatus))
							}
							if curField == data.ReportAttributesRandomForm {
								formP, b := slice.Find(forms, func(index int, data models.Form) bool {
									return data.CohortID == cohort.ID
								})
								if b {
									form := *formP
									//过滤掉无效并且没使用过的表单字段
									form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
										_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
											return item.Label == info.Label
										})
										if b {
											return true
										}
										return false
									})
									for _, formItem := range form.Fields {
										if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && !formItem.IsCustomFormulaField {
											label := formItem.Label
											if formItem.Status != nil && *formItem.Status == 2 {
												label = fmt.Sprintf("%s-%s", formItem.Label, locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
											}
											title = append(title, cohortName+"-"+label)
										}
									}
								}
								continue
							}
							if curField == data.ReportAttributesRandomFactorCalc {
								formP, b := slice.Find(forms, func(index int, data models.Form) bool {
									return data.CohortID == cohort.ID
								})
								if b {
									form := *formP
									//过滤掉无效并且没使用过的表单字段
									form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
										_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
											return item.Label == info.Label
										})
										if b {
											return true
										}
										return false
									})
									for _, formItem := range form.Fields {
										if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && formItem.IsCustomFormulaField {
											label := fmt.Sprintf("%s(%s)", formItem.Label, locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.form.page"))
											if formItem.Status != nil && *formItem.Status == 2 {
												label = fmt.Sprintf("%s(%s)-%s", formItem.Label,
													locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.form.page"),
													locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
											}
											title = append(title, cohortName+"-"+label)
										}
									}
								}
								continue
							}
							if curField == data.ReportAttributesRandomFactor {
								randomListCohort := slice.Filter(randomLists, func(index int, data models.RandomList) bool {
									return data.CohortID == cohort.ID
								})
								randomFactors := getFlatFactor(randomListCohort)
								glUsedInfo := slice.Filter(usedInfos, func(index int, info models.ListField) bool {
									_, b := slice.Find(randomFactors, func(index int, item string) bool {
										return item == info.Label
									})
									if b {
										return true
									}
									return false
								})

								for _, info := range glUsedInfo {
									if info.InvalidDisplay == "" {
										t := fmt.Sprintf("%s(%s)",
											info.Label,
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.factor"))
										title = append(title, cohortName+"-"+t)
									} else {
										t := fmt.Sprintf("%s(%s)-%s",
											info.Label,
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.factor"),
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
										title = append(title, cohortName+"-"+t)
									}
								}
							}
							if curField == data.ReportAttributesRandomActualFactor {
								formP, b := slice.Find(forms, func(index int, data models.Form) bool {
									return data.CohortID == cohort.ID
								})
								if b {
									form := *formP
									//过滤掉无效并且没使用过的表单字段
									form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
										_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
											return item.Label == info.Label
										})
										if b {
											return true
										}
										return false
									})
									for _, formItem := range form.Fields {
										if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && formItem.IsCustomFormulaField {
											label := fmt.Sprintf("%s(%s)", formItem.Label, locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual"))
											if formItem.Status != nil && *formItem.Status == 2 {
												label = fmt.Sprintf("%s(%s)-%s", formItem.Label,
													locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual"),
													locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
											}
											title = append(title, cohortName+"-"+label)
										}
									}
								}
								randomListCohort := slice.Filter(randomLists, func(index int, data models.RandomList) bool {
									return data.CohortID == cohort.ID
								})
								randomFactors := getFlatFactor(randomListCohort)
								glUsedInfo := slice.Filter(usedInfos, func(index int, info models.ListField) bool {
									_, b := slice.Find(randomFactors, func(index int, item string) bool {
										return item == info.Label
									})
									if b {
										return true
									}
									return false
								})
								glUsedInfoLength = len(glUsedInfo)
								for _, info := range glUsedInfo {
									if info.InvalidDisplay == "" {
										t := fmt.Sprintf("%s-%s(%s)",
											cohortName,
											info.Label,
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual"))
										title = append(title, t)
									} else {
										t := fmt.Sprintf("%s-%s(%s)-%s",
											cohortName,
											info.Label,
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual"),
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
										title = append(title, t)
									}
								}
								continue
							}
							if curField == data.ReportAttributesRandomGroup {
								title = append(title, cohortName+"-"+locales.Tr(ctx, curField))
								if haveSubGroup {
									title = append(title, cohortName+"-"+locales.Tr(ctx, data.ReportAttributesRandomSubGroup))
								}
							}
							if curField == data.ReportAttributesDispensingMedicineRealGroup && isRegisterGroup {
								title = append(title, cohortName+"-"+locales.Tr(ctx, curField))
							}
							if curField == data.ReportAttributesRandomConfigCode {
								title = append(title, cohortName+"-"+locales.Tr(ctx, curField))
							}
							if isShowRandomNumber {
								if curField == data.ReportAttributesRandomNumber {
									title = append(title, cohortName+"-"+locales.Tr(ctx, curField))
								}
								if curField == data.ReportAttributesRandomSubjectReplaceNumber {
									title = append(title, cohortName+"-"+locales.Tr(ctx, curField))
								}
							}
							if isShowRandomSequenceNumber {
								if curField == data.ReportAttributesRandomSequenceNumber {
									title = append(title, cohortName+"-"+locales.Tr(ctx, curField))
								}
							}

						}
					}
					continue
				}
			} else {
				if field == data.ReportAttributesRandomGroup {
					title = append(title, locales.Tr(ctx, field))
					if haveSubGroup {
						title = append(title, locales.Tr(ctx, data.ReportAttributesRandomSubGroup))
					}
					continue
				}
				if field == data.ReportAttributesDispensingMedicineRealGroup {
					title = append(title, locales.Tr(ctx, field))
					continue
				}

				if field == data.ReportAttributesRandomForm {
					if project.ProjectInfo.Type == 1 {
						for _, form := range forms {
							//过滤掉无效并且没使用过的表单字段
							form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
								_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
									return item.Label == info.Label
								})
								if b {
									return true
								}
								return false
							})
							for _, formItem := range form.Fields {
								if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && !formItem.IsCustomFormulaField {
									label := formItem.Label
									if formItem.Status != nil && *formItem.Status == 2 {
										label = fmt.Sprintf("%s-%s", formItem.Label, locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
									}
									title = append(title, label)
								}
							}
						}
					} else if project.ProjectInfo.Type == 2 {
						for _, cohort := range envInfo.Cohorts {
							cohortName := models.GetCohortReRandomName(cohort)
							formP, b := slice.Find(forms, func(index int, data models.Form) bool {
								return data.CohortID == cohort.ID
							})
							if b {
								form := *formP
								//过滤掉无效并且没使用过的表单字段
								form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
									_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
										return item.Label == info.Label
									})
									if b {
										return true
									}
									return false
								})
								for _, formItem := range form.Fields {
									if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && !formItem.IsCustomFormulaField {
										label := formItem.Label
										if formItem.Status != nil && *formItem.Status == 2 {
											label = fmt.Sprintf("%s-%s", formItem.Label, locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
										}
										title = append(title, cohortName+"-"+label)
									}
								}
							}
						}
					}
					continue
				}
				if field == data.ReportAttributesRandomFactorCalc {
					if project.ProjectInfo.Type == 1 {
						for _, form := range forms {
							//过滤掉无效并且没使用过的表单字段
							form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
								_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
									return item.Label == info.Label
								})
								if b {
									return true
								}
								return false
							})
							for _, formItem := range form.Fields {
								if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && formItem.IsCustomFormulaField {
									label := fmt.Sprintf("%s(%s)", formItem.Label,
										locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.form.page"))
									if formItem.Status != nil && *formItem.Status == 2 {
										label = fmt.Sprintf("%s(%s)-%s", formItem.Label,
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.form.page"),
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
									}
									title = append(title, label)
								}
							}
						}
					} else if project.ProjectInfo.Type == 2 {
						for _, cohort := range envInfo.Cohorts {
							cohortName := models.GetCohortReRandomName(cohort)
							formP, b := slice.Find(forms, func(index int, data models.Form) bool {
								return data.CohortID == cohort.ID
							})
							if b {
								form := *formP
								//过滤掉无效并且没使用过的表单字段
								form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
									_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
										return item.Label == info.Label
									})
									if b {
										return true
									}
									return false
								})
								for _, formItem := range form.Fields {
									if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && formItem.IsCustomFormulaField {
										label := fmt.Sprintf("%s(%s)", formItem.Label,
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.form.page"))
										if formItem.Status != nil && *formItem.Status == 2 {
											label = fmt.Sprintf("%s(%s)-%s", formItem.Label,
												locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.form.page"),
												locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
										}
										title = append(title, cohortName+"-"+label)
									}
								}
							}
						}
					}
					continue
				}
				if field == data.ReportAttributesRandomFactor {
					randomFactors := getFlatFactor(randomLists)
					glUsedInfo := slice.Filter(usedInfos, func(index int, info models.ListField) bool {
						_, b := slice.Find(randomFactors, func(index int, item string) bool {
							return item == info.Label
						})
						if b {
							return true
						}
						return false
					})
					glUsedInfoLength = len(glUsedInfo)
					for _, info := range glUsedInfo {
						if info.InvalidDisplay == "" {
							t := fmt.Sprintf("%s(%s)",
								info.Label,
								locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.factor"))
							title = append(title, t)
						} else {
							t := fmt.Sprintf("%s(%s)-%s",
								info.Label,
								locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.factor"),
								locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
							title = append(title, t)
						}
					}
					continue
				}
				if field == data.ReportAttributesRandomActualFactor {
					if project.ProjectInfo.Type == 1 {
						for _, form := range forms {
							//过滤掉无效并且没使用过的表单字段
							form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
								_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
									return item.Label == info.Label
								})
								if b {
									return true
								}
								return false
							})
							for _, formItem := range form.Fields {
								if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && formItem.IsCustomFormulaField {
									label := fmt.Sprintf("%s(%s)", formItem.Label,
										locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual"))
									if formItem.Status != nil && *formItem.Status == 2 {
										label = fmt.Sprintf("%s(%s)-%s", formItem.Label,
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual"),
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
									}
									title = append(title, label)
								}
							}
						}
					} else if project.ProjectInfo.Type == 2 {
						for _, cohort := range envInfo.Cohorts {
							cohortName := models.GetCohortReRandomName(cohort)
							formP, b := slice.Find(forms, func(index int, data models.Form) bool {
								return data.CohortID == cohort.ID
							})
							if b {
								form := *formP
								//过滤掉无效并且没使用过的表单字段
								form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
									_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
										return item.Label == info.Label
									})
									if b {
										return true
									}
									return false
								})
								for _, formItem := range form.Fields {
									if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && formItem.IsCustomFormulaField {
										label := fmt.Sprintf("%s(%s)", formItem.Label,
											locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual"))
										if formItem.Status != nil && *formItem.Status == 2 {
											label = fmt.Sprintf("%s(%s)-%s", formItem.Label,
												locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual"),
												locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
										}
										title = append(title, cohortName+"-"+label)
									}
								}
							}
						}
					}
					randomFactors := getFlatFactor(randomLists)
					glUsedInfo := slice.Filter(usedInfos, func(index int, info models.ListField) bool {
						_, b := slice.Find(randomFactors, func(index int, item string) bool {
							return item == info.Label
						})
						if b {
							return true
						}
						return false
					})
					glUsedInfoLength = len(glUsedInfo)
					for _, info := range glUsedInfo {
						if info.InvalidDisplay == "" {
							t := fmt.Sprintf("%s(%s)",
								info.Label,
								locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual"))
							title = append(title, t)
						} else {
							t := fmt.Sprintf("%s(%s)-%s",
								info.Label,
								locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.actual"),
								locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
							title = append(title, t)
						}
					}
					continue
				}
			}

			title = append(title, locales.Tr(ctx, field))
		}
	} else {
		for _, field := range data.RandomizeReport.MultiDefaultFields {
			if field.Type == project.Type {
				randomFactor := false
				for _, defaultField := range field.DefaultFields {
					if defaultField.Key == data.ReportAttributesInfoSubjectNumber {
						if subjectReplaceText != "" {
							title = append(title, subjectReplaceText)
							fieldsArray = append(fieldsArray, defaultField.Key)
							continue
						}
					}
					if defaultField.Key == data.ReportAttributesRandomSubjectNumberReplace {
						if subjectReplaceText != "" {
							title = append(title, locales.Tr(ctx, "report.attributes.random.subject.number.replace.substitute")+subjectReplaceText)
							fieldsArray = append(fieldsArray, defaultField.Key)
							continue
						}
					}

					if defaultField.Key == data.ReportAttributesRandomNumber && len(showRandomNumber) == 0 {
						continue
					}

					if defaultField.Key == data.ReportAttributesDispensingMedicineRealGroup && len(showRegisterGroup) == 0 {
						continue
					}

					fieldsArray = append(fieldsArray, defaultField.Key)
					//没开启筛选流程就去掉筛选时间
					if defaultField.Key == data.ReportAttributesRandomScreenTime {
						if len(showScreenTime) == 0 {
							continue
						}
						_, b := slice.Find(attributes, func(index int, item models.Attribute) bool {
							return item.AttributeInfo.IsScreen
						})
						if !b {
							continue
						}
					}
					if project.Type == 3 || (project.Type == 2 && haveCohortReRandom) {
						if defaultField.Key == data.ReportAttributesRandomTime ||
							defaultField.Key == data.ReportAttributesRandomOperator ||
							defaultField.Key == data.ReportAttributesRandomFactor ||
							defaultField.Key == data.ReportAttributesRandomGroup ||
							defaultField.Key == data.ReportAttributesInfoStatus ||
							defaultField.Key == data.ReportAttributesDispensingMedicineRealGroup ||
							defaultField.Key == data.ReportAttributesRandomConfigCode ||
							defaultField.Key == data.ReportAttributesRandomNumber ||
							defaultField.Key == data.ReportAttributesRandomSubjectReplaceNumber {
							if randomFactor {
								continue
							}

							for _, cohort := range envInfo.Cohorts {
								if len(selectCohort) > 0 && selectCohort[cohort.ID] == false {
									continue
								}
								cohortName := models.GetCohortReRandomName(cohort)
								isShowRandomNumber := false
								_, ok := slice.Find(attributes, func(index int, attribute models.Attribute) bool {
									return attribute.CohortID == cohort.ID && attribute.AttributeInfo.IsRandomNumber
								})
								if ok {
									isShowRandomNumber = true
								}
								haveRegister := false
								_, haveRegisterOk := slice.Find(attributes, func(index int, attribute models.Attribute) bool {
									return cohort.ID == attribute.CohortID && attribute.AttributeInfo.AllowRegisterGroup
								})
								if haveRegisterOk {
									haveRegister = true
								}
								title = append(title, cohortName+"-"+locales.Tr(ctx, data.ReportAttributesInfoStatus))
								for _, fields := range field.DefaultFields {
									if fields.Key == data.ReportAttributesRandomTime {
										title = append(title, cohortName+"-"+locales.Tr(ctx, fields.Key))
									}
									if fields.Key == data.ReportAttributesRandomOperator {
										title = append(title, cohortName+"-"+locales.Tr(ctx, fields.Key))
									}
									if fields.Key == data.ReportAttributesRandomFactor {
										randomListCohort := slice.Filter(randomLists, func(index int, data models.RandomList) bool {
											return data.CohortID == cohort.ID
										})
										randomFactors := getFlatFactor(randomListCohort)
										glUsedInfo := slice.Filter(usedInfos, func(index int, info models.ListField) bool {
											_, b := slice.Find(randomFactors, func(index int, item string) bool {
												return item == info.Label
											})
											if b {
												return true
											}
											return false
										})
										for _, info := range glUsedInfo {
											if info.InvalidDisplay == "" {
												t := fmt.Sprintf("%s(%s)",
													info.Label,
													locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.factor"))
												title = append(title, cohortName+"-"+t)
											} else {
												t := fmt.Sprintf("%s(%s)-%s",
													info.Label,
													locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.factor"),
													locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
												title = append(title, cohortName+"-"+t)
											}
										}
									}
									if fields.Key == data.ReportAttributesRandomGroup {
										title = append(title, cohortName+"-"+locales.Tr(ctx, fields.Key))
										if haveSubGroup {
											title = append(title, cohortName+"-"+locales.Tr(ctx, data.ReportAttributesRandomSubGroup))
										}
									}
									if fields.Key == data.ReportAttributesDispensingMedicineRealGroup && haveRegister {
										title = append(title, cohortName+"-"+locales.Tr(ctx, fields.Key))
									}
									if fields.Key == data.ReportAttributesRandomConfigCode {
										title = append(title, cohortName+"-"+locales.Tr(ctx, fields.Key))
									}
									if isShowRandomNumber {
										if fields.Key == data.ReportAttributesRandomNumber {
											title = append(title, cohortName+"-"+locales.Tr(ctx, fields.Key))
										}
										if fields.Key == data.ReportAttributesRandomSubjectReplaceNumber {
											title = append(title, cohortName+"-"+locales.Tr(ctx, fields.Key))
										}
									}

								}
								randomFactor = true
								continue

							}
							continue
						}
					} else {
						if defaultField.Key == data.ReportAttributesRandomGroup {
							title = append(title, locales.Tr(ctx, defaultField.Key))
							if haveSubGroup {
								title = append(title, locales.Tr(ctx, data.ReportAttributesRandomSubGroup))
							}
							continue
						}
						if defaultField.Key == data.ReportAttributesDispensingMedicineRealGroup {
							title = append(title, locales.Tr(ctx, defaultField.Key))
							continue
						}
						if defaultField.Key == data.ReportAttributesRandomFactor {
							randomFactors := getFlatFactor(randomLists)
							glUsedInfo := slice.Filter(usedInfos, func(index int, info models.ListField) bool {
								_, b := slice.Find(randomFactors, func(index int, item string) bool {
									return item == info.Label
								})
								if b {
									return true
								}
								return false
							})
							glUsedInfoLength = len(glUsedInfo)
							for _, info := range glUsedInfo {
								if info.InvalidDisplay == "" {
									t := fmt.Sprintf("%s(%s)",
										info.Label,
										locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.factor"))
									title = append(title, t)
								} else {
									t := fmt.Sprintf("%s(%s)-%s",
										info.Label,
										locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.minimize.calc.factor"),
										locales.Tr(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"))
									title = append(title, t)
								}
							}
							continue
						}
					}

					title = append(title, locales.Tr(ctx, defaultField.Key))

				}
				break
			}
		}
	}
	content := make([][]interface{}, len(subjectData))
	zone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	//标记第一阶段的subject
	subjectCohortMap := map[string]models.ExportSubjectNumber{}

	if project.Type == 3 && !tools.InRandomIsolation(project.Number) {
		data := []models.ExportSubjectNumber{}
		for _, datum := range subjectData {
			if datum.CohortID == envInfo.Cohorts[0].ID {
				data = append(data, datum)
				//subjectCohortMap[datum.Subject[0].Value.(string)] = datum
			} else {
				subjectCohortMap[datum.Subject[0].Value.(string)] = datum
			}
		}
		subjectData = data
	}
	if project.Type == 2 && haveCohortReRandom {
		data := []models.ExportSubjectNumber{}

		baseCohorts := slice.Filter(envInfo.Cohorts, func(index int, item models.Cohort) bool {
			return item.LastID.IsZero()
		})
		baseCohortsMap := map[primitive.ObjectID]bool{}
		for _, cohort := range baseCohorts {
			baseCohortsMap[cohort.ID] = true
		}
		for _, datum := range subjectData {
			if baseCohortsMap[datum.CohortID] {
				data = append(data, datum)
				//subjectCohortMap[datum.Subject[0].Value.(string)] = datum
			} else {
				subjectCohortMap[datum.Subject[0].Value.(string)] = datum
			}
		}
		subjectData = data

	}

	// 获取环境下所有cycleVisit
	var visitCycles []models.VisitCycle
	visitCyclesMap := map[primitive.ObjectID]models.VisitCycle{}
	cursor, err = tools.Database.Collection("visit_cycle").Find(nil, bson.M{"env_id": envOID})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	for _, item := range visitCycles {
		visitCyclesMap[item.CohortID] = item
	}

	for i, item := range subjectData {
		attributeP, _ := slice.Find(attributes, func(index int, data models.Attribute) bool {
			return item.CohortID == data.CohortID
		})
		attribute := *attributeP
		country := ""
		region := ""
		siteNumber := item.SiteNumber
		siteName := item.SiteName
		subject := item.Subject[0].Value
		formData := []string{}
		cohortFormData := make(map[primitive.ObjectID]string)
		formCalcData := []string{}
		cohortFormCalcData := make(map[primitive.ObjectID]string)
		factors := []string{}
		actualFactors := []string{}
		cohortActualFactors := make(map[primitive.ObjectID]string)
		randomNumber := ""
		randomSequenceNumber := ""
		group := tools.BlindData
		registerGroup := tools.BlindData
		subGroup := ""
		groupCode := tools.BlindData
		replaceSubject := ""
		replaceSubjectNumber := ""
		replaceSubjectStatus := ""
		replaceSubjectTime := ""
		randomTime := ""
		randomOperator := ""
		registerTime := ""
		registerOperator := ""
		signOutOperator := ""
		signOutTime := ""
		signOutReason := ""
		factorFields, _ := typeRandomKeyGetFactorFields(item, randomLists)

		c := make([]interface{}, 0)
		if attribute.AttributeInfo.CountryLayered {
			if item.Country != "" {
				country = countries[item.Country].(string)
			}
		}
		if attribute.AttributeInfo.RegionLayered {
			if item.Region != "" {
				region = item.Region
			}
		}

		//表单
		if project.ProjectInfo.Type == 2 || project.Type == 3 {
			for _, cohort := range envInfo.Cohorts {
				form, b := slice.Find(forms, func(index int, data models.Form) bool {
					return data.CohortID == cohort.ID
				})
				if b {
					//过滤掉无效并且没使用过的表单字段
					form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
						_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
							return item.Label == info.Label
						})
						if b {
							return true
						}
						return false
					})
					for _, field := range form.Fields {
						if (field.ApplicationType == nil || *field.ApplicationType == 1 || *field.ApplicationType == 4) && !field.IsCustomFormulaField {
							fieldValue := ""
							if item.CohortID == cohort.ID {
								fieldValue, err = handleFormData(item, field, fieldValue)
								if err != nil {
									return "", nil, err
								}
							} else {
								if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
									fieldValue, err = handleFormData(subjectCohortMap[subject.(string)], field, fieldValue)
									if err != nil {
										return "", nil, err
									}
								}
							}
							if field.Type == "inputNumber" && field.FormatType == "decimalLength" && field.Length != nil && fieldValue != "" {
								lengthString := strconv.FormatFloat(*field.Length, 'f', -1, 64)
								if strings.Contains(lengthString, ".") {
									digits, _ := getFractionDigits(*field.Length)
									f, _ := strconv.ParseFloat(fieldValue, 64)
									str := strconv.FormatFloat(f, 'f', digits, 64)
									fieldValue = str
								}
							}
							cohortFormData[field.ID] = fieldValue
						}
					}
				}

			}
		} else if project.Type == 1 {
			if len(forms) > 0 {
				form := forms[0]
				//过滤掉无效并且没使用过的表单字段
				form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
					_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
						return item.Label == info.Label
					})
					if b {
						return true
					}
					return false
				})
				for _, field := range form.Fields {
					if (field.ApplicationType == nil || *field.ApplicationType == 1 || *field.ApplicationType == 4) && !field.IsCustomFormulaField {
						fieldValue := ""
						fieldValue, err = handleFormData(item, field, fieldValue)
						if err != nil {
							return "", nil, err
						}
						formData = append(formData, fieldValue)
					}
				}
			}
		}

		//表单参与分层计算
		if project.ProjectInfo.Type == 2 || project.Type == 3 {
			for _, cohort := range envInfo.Cohorts {
				form, b := slice.Find(forms, func(index int, data models.Form) bool {
					return data.CohortID == cohort.ID
				})
				if b {
					//过滤掉无效并且没使用过的表单字段
					form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
						_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
							return item.Label == info.Label
						})
						if b {
							return true
						}
						return false
					})
					for _, field := range form.Fields {
						if (field.ApplicationType == nil || *field.ApplicationType == 1 || *field.ApplicationType == 4) && field.IsCustomFormulaField {
							fieldValue := ""
							if item.CohortID == cohort.ID {
								fieldValue, err = handleFormData(item, field, fieldValue)
								if err != nil {
									return "", nil, err
								}
							} else {
								if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
									fieldValue, err = handleFormData(subjectCohortMap[subject.(string)], field, fieldValue)
									if err != nil {
										return "", nil, err
									}
								}
							}
							cohortFormCalcData[field.ID] = fieldValue
						}
					}
				}

			}
		} else if project.Type == 1 {
			if len(forms) > 0 {
				form := forms[0]
				//过滤掉无效并且没使用过的表单字段
				form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
					_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
						return item.Label == info.Label
					})
					if b {
						return true
					}
					return false
				})
				for _, field := range form.Fields {
					if (field.ApplicationType == nil || *field.ApplicationType == 1 || *field.ApplicationType == 4) && field.IsCustomFormulaField {
						fieldValue := ""
						fieldValue, err = handleFormData(item, field, fieldValue)
						if err != nil {
							return "", nil, err
						}
						formCalcData = append(formCalcData, fieldValue)
					}
				}
			}
		}
		{
			//分层/实际分层
			//实际分层
			if project.Type == 2 || project.Type == 3 {
				for _, cohort := range envInfo.Cohorts {
					form, b := slice.Find(forms, func(index int, data models.Form) bool {
						return data.CohortID == cohort.ID
					})
					if b {
						//过滤掉无效并且没使用过的表单字段
						form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
							_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
								return item.Label == info.Label
							})
							if b {
								return true
							}
							return false
						})
						for _, field := range form.Fields {
							if (field.ApplicationType == nil || *field.ApplicationType == 1 || *field.ApplicationType == 4) && field.IsCustomFormulaField {
								fieldValue := ""
								if item.CohortID == cohort.ID {
									fieldValue, err = handleFormCalcData(item, field, fieldValue)
									if err != nil {
										return "", nil, err
									}
								} else {
									if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
										fieldValue, err = handleFormCalcData(subjectCohortMap[subject.(string)], field, fieldValue)
										if err != nil {
											return "", nil, err
										}
									}
								}
								cohortActualFactors[field.ID] = fieldValue
							}
						}
					}

				}
			} else if project.Type == 1 {
				if len(forms) > 0 {
					form := forms[0]
					//过滤掉无效并且没使用过的表单字段
					form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
						_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
							return item.Label == info.Label
						})
						if b {
							return true
						}
						return false
					})
					for _, field := range form.Fields {
						if (field.ApplicationType == nil || *field.ApplicationType == 1 || *field.ApplicationType == 4) && field.IsCustomFormulaField {
							fieldValue := ""
							fieldValue, err = handleFormCalcData(item, field, fieldValue)
							if err != nil {
								return "", nil, err
							}
							actualFactors = append(actualFactors, fieldValue)
						}
					}
				}
			}

			//当前受试者随机表分层因素
			factorFields, _ = typeRandomKeyGetFactorFields(item, randomLists)
			var glUsedInfo []models.ListField
			err = slice.SortByField(usedInfos, "InvalidDisplay", "desc")
			if err != nil {
				return "", nil, err
			}
			//有多个相同名字的分层因素
			sameFactors := make(map[string]bool)
			for _, info := range usedInfos {
				if info.InvalidDisplay != "" {
					sameFactors[info.Label] = true
				}
			}
			randomFactors := getFlatFactor(randomLists)
			glUsedInfo = slice.Filter(usedInfos, func(index int, info models.ListField) bool {
				_, b := slice.Find(randomFactors, func(index int, item string) bool {
					return item == info.Label
				})
				if b {
					return true
				}
				return false
			})
			for _, factor := range glUsedInfo {
				labelValue := ""
				actualLabelValue := ""
				fields := slice.Filter(factorFields, func(index int, f models.Field) bool {
					return f.Label == factor.Label
				})
				if len(fields) > 0 {
					for _, field := range fields {
						info, b2 := slice.Find(item.Subject, func(index int, item models.Info) bool {
							return item.Name == field.Name
						})
						if b2 {
							option, b3 := slice.Find(field.Options, func(index int, item models.Option) bool {
								return item.Value == info.Value
							})
							if b3 {
								labelValue = option.Label
							}
						}
						actualInfo, b4 := slice.Find(item.ActualInfo, func(index int, item models.Info) bool {
							return item.Name == field.Name
						})
						if b4 {
							option, b5 := slice.Find(field.Options, func(index int, item models.Option) bool {
								return item.Value == actualInfo.Value
							})
							if b5 {
								actualLabelValue = option.Label
							}
						}
					}
				}
				factors = append(factors, labelValue)
				actualFactors = append(actualFactors, actualLabelValue)
			}
		}

		if attribute.AttributeInfo.IsRandomNumber {
			randomNumber = item.RandomNumber
		}
		if attribute.AttributeInfo.IsRandomSequenceNumber {
			randomSequenceNumber = item.RandomSequenceNumber
		}

		randomTime, err = typeRandomKeyGetRandomTime(item, zone)
		if err != nil {
			return "", nil, err

		}
		if !attribute.AttributeInfo.Random && item.JoinTime != "" {

		}
		if item.RegisterTime != 0 {
			if item.Tz != "" {
				//timeZone, _ := strconv.Atoi(strings.Replace(item.TimeZone, "UTC", "", 1))
				//registerTime = time.Unix(item.RegisterTime, 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
				//registerTime = registerTime + "(" + item.TimeZone + ")"

				timeStr, err := tools.GetLocationUtc(item.Tz, item.RegisterTime)
				if err != nil {
					panic(err)
				}
				registerTime = timeStr

			} else {
				hours := time.Duration(zone)
				minutes := time.Duration((zone - float64(hours)) * 60)
				duration := hours*time.Hour + minutes*time.Minute
				register := time.Unix(item.RegisterTime, 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				//strTimeZone := fmt.Sprintf("UTC%+d", zone)
				strTimeZone := tools.FormatOffsetToZoneString(zone)
				registerTime = register + "(" + strTimeZone + ")"
			}
		}

		if item.SignOutTime != 0 {
			if item.Tz != "" {
				//timeZone, _ := strconv.Atoi(strings.Replace(item.TimeZone, "UTC", "", 1))
				//signOutTime = time.Unix(item.SignOutTime, 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
				//signOutTime = signOutTime + "(" + item.TimeZone + ")"

				timeStr, err := tools.GetLocationUtc(item.Tz, item.SignOutTime)
				if err != nil {
					panic(err)
				}
				signOutTime = timeStr
			} else {
				hours := time.Duration(zone)
				minutes := time.Duration((zone - float64(hours)) * 60)
				duration := hours*time.Hour + minutes*time.Minute
				signout := time.Unix(item.SignOutTime, 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				//strTimeZone := fmt.Sprintf("UTC%+d", zone)
				strTimeZone := tools.FormatOffsetToZoneString(zone)
				signOutTime = signout + "(" + strTimeZone + ")"
			}
			signOutReason = item.SignOutReason
		}

		if subjectCohortMap[subject.(string)].SignOutTime != 0 {
			if item.Tz != "" {
				//timeZone, _ := strconv.Atoi(strings.Replace(item.TimeZone, "UTC", "", 1))
				//signOutTime = time.Unix(item.SignOutTime, 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
				//signOutTime = signOutTime + "(" + item.TimeZone + ")"

				timeStr, err := tools.GetLocationUtc(item.Tz, subjectCohortMap[subject.(string)].SignOutTime)
				if err != nil {
					panic(err)
				}
				signOutTime = timeStr
			} else {
				hours := time.Duration(zone)
				minutes := time.Duration((zone - float64(hours)) * 60)
				duration := hours*time.Hour + minutes*time.Minute
				signout := time.Unix(subjectCohortMap[subject.(string)].SignOutTime, 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
				//strTimeZone := fmt.Sprintf("UTC%+d", zone)
				strTimeZone := tools.FormatOffsetToZoneString(zone)
				signOutTime = signout + "(" + strTimeZone + ")"
			}
			signOutReason = subjectCohortMap[subject.(string)].SignOutReason
			item.SignOutRealTime = subjectCohortMap[subject.(string)].SignOutRealTime
		}

		group, subGroup, groupCode = typeRandomKeyGetGroupGroup(haveSubGroup, isBlindedRole, attribute, item, randomLists)
		if !(attribute.AttributeInfo.Blind && isBlindedRole) {
			registerGroup = item.RegisterGroup
		}
		if len(item.ReplaceSubject) > 0 {
			replaceSubject = item.ReplaceSubject[0].Value.(string)
		}
		if attribute.AttributeInfo.IsRandomNumber {
			replaceSubjectNumber = item.ReplaceNumber
		}

		//	被替换受试者号

		// 历史轨迹获取登记操作人 随机操作人
		//再随机合并项目，合并轨迹
		allHistory := item.History
		if project.Type == 3 && !tools.InRandomIsolation(project.Number) || haveCohortReRandom {
			if subjectCohortMap[subject.(string)].CohortID != item.CohortID {
				allHistory = append(allHistory, subjectCohortMap[subject.(string)].History...)
			}
		}
		for _, history := range allHistory {
			unicode := userMap[history.UID]
			operator := fmt.Sprintf("%s(%d)", history.User, unicode)
			//登记
			if history.Key == "history.subject.add" || history.Key == "history.subject.at-random-add" {
				registerOperator = operator
			}
			if history.Key == "history.subject.random" ||
				history.Key == "history.subject.randomNoNumber" ||
				history.Key == "history.subject.randomSub" ||
				history.Key == "history.subject.label.random" ||
				history.Key == "history.subject.label.randomSub" ||
				history.Key == "history.subject.label.randomNoNumber" ||
				history.Key == "history.subject.label.randomNoNumberSub" ||
				history.Key == "history.subject.randomNoNumberSub" ||
				history.Key == "history.subject.label.at-random-random" ||
				history.Key == "history.subject.label.at-random-randomSub" ||
				history.Key == "history.subject.label.at-random-randomNoNumber" ||
				history.Key == "history.subject.label.at-random-randomNoNumberSub" ||
				history.Key == "history.subject.label.at-random-replaced-new-a" ||
				history.Key == "history.subject.label.at-random-replaced-new-b" {
				randomOperator = operator
				if randomTime == "" { // 旧数据没记录随机时间字段 取轨迹随机时间
					if item.Tz != "" {
						//timeZone, _ := strconv.Atoi(strings.Replace(item.TimeZone, "UTC", "", 1))
						//randomTime = time.Unix(int64(history.Time), 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
						//randomTime = randomTime + "(" + item.TimeZone + ")"

						timeStr, err := tools.GetLocationUtc(item.Tz, int64(history.Time))
						if err != nil {
							panic(err)
						}
						randomTime = timeStr
					} else {
						hours := time.Duration(zone)
						minutes := time.Duration((zone - float64(hours)) * 60)
						duration := hours*time.Hour + minutes*time.Minute

						randomTimeStr := time.Unix(int64(history.Time), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						//strTimeZone := fmt.Sprintf("UTC%+d", zone)
						strTimeZone := tools.FormatOffsetToZoneString(zone)
						randomTimeStr = randomTimeStr + "(" + strTimeZone + ")"
						randomTime = randomTimeStr
					}
				}
			}
			if history.Key == "history.subject.replaced" || history.Key == "history.subject.label.replaced-new" || history.Key == "history.subject.label.at-random-replaced-new-a" || history.Key == "history.subject.label.at-random-replaced-new-b" {
				if item.Tz != "" {
					//timeZone, _ := strconv.Atoi(strings.Replace(item.TimeZone, "UTC", "", 1))
					//replaceSubjectTime = time.Unix(int64(history.Time), 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
					//replaceSubjectTime = replaceSubjectTime + "(" + item.TimeZone + ")"

					timeStr, err := tools.GetLocationUtc(item.Tz, int64(history.Time))
					if err != nil {
						panic(err)
					}
					replaceSubjectTime = timeStr

				} else {
					hours := time.Duration(zone)
					minutes := time.Duration((zone - float64(hours)) * 60)
					duration := hours*time.Hour + minutes*time.Minute
					replaceTime := time.Unix(int64(history.Time), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
					//strTimeZone := fmt.Sprintf("UTC%+d", zone)
					strTimeZone := tools.FormatOffsetToZoneString(zone)
					replaceTimeStr := replaceTime + "(" + strTimeZone + ")"
					replaceSubjectTime = replaceTimeStr
				}
				if replaceSubject == "" && history.Data["name"].(string) == item.Subject[0].Value {
					replaceSubject = history.Data["name"].(string)
					var beReplaceSubject models.Subject
					matchBeReplace := bson.M{"env_id": envOID, "info": bson.D{{"name", "shortname"}, {"value", history.Data["name"]}}, "deleted": bson.M{"$ne": true}}
					err := tools.Database.Collection("subject").FindOne(nil, matchBeReplace).Decode(&beReplaceSubject)
					if err != nil {
						return "", nil, errors.WithStack(err)
					}
					status := int32(beReplaceSubject.Status)
					item.ReplaceSubjectStatus = &status
					if attribute.AttributeInfo.IsRandomNumber {
						replaceSubjectNumber = beReplaceSubject.RandomNumber
					}
				}
				if randomTime == "" { // 旧数据替换受试者没记录随机时间字段 取轨迹替换时间
					if item.Tz != "" {
						//timeZone, _ := strconv.Atoi(strings.Replace(item.TimeZone, "UTC", "", 1))
						//randomTime = time.Unix(int64(history.Time), 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
						//randomTime = randomTime + "(" + item.TimeZone + ")"

						timeStr, err := tools.GetLocationUtc(item.Tz, int64(history.Time))
						if err != nil {
							panic(err)
						}
						randomTime = timeStr

					} else {
						hours := time.Duration(zone)
						minutes := time.Duration((zone - float64(hours)) * 60)
						duration := hours*time.Hour + minutes*time.Minute
						randomTimeStr := time.Unix(int64(history.Time), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						//strTimeZone := fmt.Sprintf("UTC%+d", zone)
						strTimeZone := tools.FormatOffsetToZoneString(zone)
						randomTimeStr = randomTimeStr + "(" + strTimeZone + ")"
						randomTime = randomTimeStr
					}
				}
			}
			if strings.Contains(history.Key, "subject.beReplaced") {
				if replaceSubject == "" {
					replaceSubject = history.Data["name"].(string)
					var beReplaceSubject models.Subject
					matchBeReplace := bson.M{"env_id": envOID, "info": bson.D{{"name", "shortname"}, {"value", history.Data["name"]}}, "deleted": bson.M{"$ne": true}}
					err := tools.Database.Collection("subject").FindOne(nil, matchBeReplace).Decode(&beReplaceSubject)
					if err != nil {
						return "", nil, errors.WithStack(err)
					}
					status := int32(beReplaceSubject.Status)
					item.ReplaceSubjectStatus = &status
					if attribute.AttributeInfo.IsRandomNumber {
						replaceSubjectNumber = beReplaceSubject.RandomNumber
					}
					if item.Tz != "" {
						//timeZone, _ := strconv.Atoi(strings.Replace(item.TimeZone, "UTC", "", 1))
						//replaceSubjectTime = time.Unix(int64(history.Time), 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
						//replaceSubjectTime = replaceSubjectTime + "(" + item.TimeZone + ")"

						timeStr, err := tools.GetLocationUtc(item.Tz, int64(history.Time))
						if err != nil {
							panic(err)
						}
						replaceSubjectTime = timeStr

					} else {
						hours := time.Duration(zone)
						minutes := time.Duration((zone - float64(hours)) * 60)
						duration := hours*time.Hour + minutes*time.Minute
						replaceTime := time.Unix(int64(history.Time), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						//strTimeZone := fmt.Sprintf("UTC%+d", zone)
						strTimeZone := tools.FormatOffsetToZoneString(zone)
						replaceTimeStr := replaceTime + "(" + strTimeZone + ")"
						replaceSubjectTime = replaceTimeStr
					}
					if randomTime == "" { // 旧数据替换受试者没记录随机时间字段 取轨迹替换时间
						if item.Tz != "" {
							//timeZone, _ := strconv.Atoi(strings.Replace(item.TimeZone, "UTC", "", 1))
							//randomTime = time.Unix(int64(history.Time), 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
							//randomTime = randomTime + "(" + item.TimeZone + ")"

							timeStr, err := tools.GetLocationUtc(item.Tz, int64(history.Time))
							if err != nil {
								panic(err)
							}
							randomTime = timeStr

						} else {
							hours := time.Duration(zone)
							minutes := time.Duration((zone - float64(hours)) * 60)
							duration := hours*time.Hour + minutes*time.Minute
							randomTimeStr := time.Unix(int64(history.Time), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							//strTimeZone := fmt.Sprintf("UTC%+d", zone)
							strTimeZone := tools.FormatOffsetToZoneString(zone)
							randomTimeStr = randomTimeStr + "(" + strTimeZone + ")"
							randomTime = randomTimeStr
						}
					}
				}
			}
			if history.Key == "history.subject.signOut" ||
				history.Key == "history.subject.label.signOut" ||
				history.Key == "history.subject.label.signOutReal" ||
				history.Key == "history.subject.label.at-random-signOut" ||
				history.Key == "history.subject.label.at-random-signOutReal" {
				signOutOperator = operator
				if signOutTime == "" { // 旧数据没记录停用时间字段 取轨迹随机时间
					if item.Tz != "" {
						//timeZone, _ := strconv.Atoi(strings.Replace(item.TimeZone, "UTC", "", 1))
						//signOutTime = time.Unix(int64(history.Time), 0).UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02 15:04:05")
						//signOutTime = signOutTime + "(" + item.TimeZone + ")"

						timeStr, err := tools.GetLocationUtc(item.Tz, int64(history.Time))
						if err != nil {
							panic(err)
						}
						signOutTime = timeStr

					} else {
						hours := time.Duration(zone)
						minutes := time.Duration((zone - float64(hours)) * 60)
						duration := hours*time.Hour + minutes*time.Minute
						signOutTimeStr := time.Unix(int64(history.Time), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						//strTimeZone := fmt.Sprintf("UTC%+d", zone)
						strTimeZone := tools.FormatOffsetToZoneString(zone)
						signOutTimeStr = signOutTimeStr + "(" + strTimeZone + ")"
						signOutTime = signOutTimeStr
					}
					signOutReason = history.Data["reason"].(string)
				}
			}
		}
		randomFactor := false
		for _, field := range fieldsArray {
			// 分层因素 随机时间 再随机
			if (field == data.ReportAttributesRandomFactor ||
				field == data.ReportAttributesRandomActualFactor ||
				field == data.ReportAttributesRandomGroup ||
				field == data.ReportAttributesRandomFactorCalc ||
				field == data.ReportAttributesDispensingMedicineRealGroup ||
				field == data.ReportAttributesInfoStatus ||
				field == data.ReportAttributesRandomTime ||
				field == data.ReportAttributesRandomOperator ||
				field == data.ReportAttributesRandomConfigCode ||
				field == data.ReportAttributesRandomNumber ||
				field == data.ReportAttributesRandomSequenceNumber ||
				field == data.ReportAttributesRandomForm ||
				field == data.ReportAttributesRandomSubjectReplaceNumber) && (project.Type == 3 || project.Type == 2 && haveCohortReRandom) {
				// 分层因素
				if randomFactor {
					continue
				}
				randomFactor = true
				for _, cohort := range envInfo.Cohorts {
					if len(selectCohort) > 0 && selectCohort[cohort.ID] == false {
						continue
					}
					isShowRandomNumber := false
					_, ok := slice.Find(attributes, func(index int, attribute models.Attribute) bool {
						return cohort.ID == attribute.CohortID && attribute.AttributeInfo.IsRandomNumber
					})
					if ok {
						isShowRandomNumber = true
					}
					isShowRandomSequenceNumber := false
					_, isShowRandomSequenceNumberOk := slice.Find(attributes, func(index int, attribute models.Attribute) bool {
						return cohort.ID == attribute.CohortID && attribute.AttributeInfo.IsRandomSequenceNumber
					})
					if isShowRandomSequenceNumberOk {
						isShowRandomSequenceNumber = true
					}

					haveScreen := false
					_, haveScreenOk := slice.Find(attributes, func(index int, attribute models.Attribute) bool {
						return cohort.ID == attribute.CohortID && attribute.AttributeInfo.IsScreen
					})
					if haveScreenOk {
						haveScreen = true
					}

					haveRegister := false
					_, haveRegisterOk := slice.Find(attributes, func(index int, attribute models.Attribute) bool {
						return cohort.ID == attribute.CohortID && attribute.AttributeInfo.AllowRegisterGroup
					})
					if haveRegisterOk {
						haveRegister = true
					}

					for _, fields := range fieldsArray {
						if fields == data.ReportAttributesInfoStatus {
							status := ""
							if item.CohortID == cohort.ID {
								status = statusItem(ctx, item.Status)
							}
							if project.Type == 3 {
								if cohort.ID != envInfo.Cohorts[0].ID && subjectCohortMap[subject.(string)].CohortID == cohort.ID {
									if subjectCohortMap[subject.(string)].Status == 1 || subjectCohortMap[subject.(string)].Status == 7 {
										status = statusItem(ctx, 10) // 第二阶段已登记 转成已随机
										// 待随机加上第二阶段的名称
										if cohort.Name != "" {
											status = status + "(" + cohort.Name + ")"
										}
									} else {
										status = statusItem(ctx, subjectCohortMap[subject.(string)].Status)
									}
								}
							} else if haveCohortReRandom && !cohort.LastID.IsZero() && subjectCohortMap[subject.(string)].CohortID == cohort.ID {
								if subjectCohortMap[subject.(string)].Status == 1 || subjectCohortMap[subject.(string)].Status == 7 {
									status = statusItem(ctx, 10) // 第二阶段已登记 转成已随机
									if cohort.ReRandomName != "" {
										status = status + "(" + cohort.ReRandomName + ")"
									}
								} else {
									// 待随机加上第二阶段的名称
									status = statusItem(ctx, subjectCohortMap[subject.(string)].Status)

								}

							}
							c = append(c, status)
						}
						if fields == data.ReportAttributesRandomFactor {
							randomListCohort := slice.Filter(randomLists, func(index int, data models.RandomList) bool {
								return data.CohortID == cohort.ID
							})
							randomFactors := getFlatFactor(randomListCohort)
							glUsedInfo := slice.Filter(usedInfos, func(index int, info models.ListField) bool {
								_, b := slice.Find(randomFactors, func(index int, item string) bool {
									return item == info.Label
								})
								if b {
									return true
								}
								return false
							})
							if item.CohortID == cohort.ID || subjectCohortMap[subject.(string)].CohortID == cohort.ID {
								if item.CohortID == cohort.ID {
									for _, factor := range glUsedInfo {
										labelValue := ""

										if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
											factorFields, _ = typeRandomKeyGetFactorFields(item, randomLists)
										}

										field, b := slice.Find(factorFields, func(index int, item models.Field) bool {
											if factor.InvalidDisplay != "" {
												return item.Label == factor.Label && item.Status != nil && *item.Status == 2
											} else {
												return item.Label == factor.Label
											}
										})
										if b {
											info, b2 := slice.Find(item.Subject, func(index int, item models.Info) bool {
												return item.Name == field.Name
											})
											if b2 {
												option, b3 := slice.Find(field.Options, func(index int, item models.Option) bool {
													return item.Value == info.Value
												})
												if b3 {
													labelValue = option.Label
												}
											}
										}
										c = append(c, labelValue)
									}
								} else {
									for _, factor := range glUsedInfo {
										labelValue := ""

										if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
											factorFields, _ = typeRandomKeyGetFactorFields(subjectCohortMap[subject.(string)], randomLists)
										}

										field, b := slice.Find(factorFields, func(index int, item models.Field) bool {
											return item.Label == factor.Label
										})
										if b {
											info, b2 := slice.Find(subjectCohortMap[subject.(string)].Subject, func(index int, item models.Info) bool {
												return item.Name == field.Name
											})
											if b2 {
												option, b3 := slice.Find(field.Options, func(index int, item models.Option) bool {
													return item.Value == info.Value
												})
												if b3 {
													labelValue = option.Label
												}
											}
										}
										c = append(c, labelValue)
									}
								}

							} else {
								for range glUsedInfo {
									c = append(c, "")
								}
							}
						}
						if fields == data.ReportAttributesRandomActualFactor {
							formP, b := slice.Find(forms, func(index int, data models.Form) bool {
								return data.CohortID == cohort.ID
							})
							if b {
								form := *formP
								//过滤掉无效并且没使用过的表单字段
								form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
									_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
										return item.Label == info.Label
									})
									if b {
										return true
									}
									return false
								})
								for _, formItem := range form.Fields {
									if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && formItem.IsCustomFormulaField {
										val, exist := cohortActualFactors[formItem.ID]
										if exist {
											c = append(c, val)
										} else {
											c = append(c, "")
										}

									}
								}
							}

							randomListCohort := slice.Filter(randomLists, func(index int, data models.RandomList) bool {
								return data.CohortID == cohort.ID
							})
							randomFactors := getFlatFactor(randomListCohort)
							glUsedInfo := slice.Filter(usedInfos, func(index int, info models.ListField) bool {
								_, b := slice.Find(randomFactors, func(index int, item string) bool {
									return item == info.Label
								})
								if b {
									return true
								}
								return false
							})
							if item.CohortID == cohort.ID || subjectCohortMap[subject.(string)].CohortID == cohort.ID {
								if item.CohortID == cohort.ID {
									for _, factor := range glUsedInfo {
										labelValue := ""

										if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
											factorFields, _ = typeRandomKeyGetFactorFields(item, randomLists)
										}

										field, b := slice.Find(factorFields, func(index int, item models.Field) bool {
											if factor.InvalidDisplay != "" {
												return item.Label == factor.Label && item.Status != nil && *item.Status == 2
											} else {
												return item.Label == factor.Label
											}
										})
										if b {
											info, b2 := slice.Find(item.ActualInfo, func(index int, item models.Info) bool {
												return item.Name == field.Name
											})
											if b2 {
												option, b3 := slice.Find(field.Options, func(index int, item models.Option) bool {
													return item.Value == info.Value
												})
												if b3 {
													labelValue = option.Label
												}
											}
										}
										c = append(c, labelValue)
									}
								} else {
									for _, factor := range glUsedInfo {
										labelValue := ""

										if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
											factorFields, _ = typeRandomKeyGetFactorFields(subjectCohortMap[subject.(string)], randomLists)
										}

										field, b := slice.Find(factorFields, func(index int, item models.Field) bool {
											return item.Label == factor.Label
										})
										if b {
											info, b2 := slice.Find(subjectCohortMap[subject.(string)].ActualInfo, func(index int, item models.Info) bool {
												return item.Name == field.Name
											})
											if b2 {
												option, b3 := slice.Find(field.Options, func(index int, item models.Option) bool {
													return item.Value == info.Value
												})
												if b3 {
													labelValue = option.Label
												}
											}
										}
										c = append(c, labelValue)
									}
								}

							} else {
								for range glUsedInfo {
									c = append(c, "")
								}
							}
						}
						if fields == data.ReportAttributesRandomForm {
							formP, b := slice.Find(forms, func(index int, data models.Form) bool {
								return data.CohortID == cohort.ID
							})
							if b {
								form := *formP
								//过滤掉无效并且没使用过的表单字段
								form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
									_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
										return item.Label == info.Label
									})
									if b {
										return true
									}
									return false
								})
								for _, formItem := range form.Fields {
									if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && !formItem.IsCustomFormulaField {
										val, exist := cohortFormData[formItem.ID]
										if exist {
											c = append(c, val)
										} else {
											c = append(c, "")
										}

									}
								}
							}
							continue
						}
						if fields == data.ReportAttributesRandomFactorCalc {
							formP, b := slice.Find(forms, func(index int, data models.Form) bool {
								return data.CohortID == cohort.ID
							})
							if b {
								form := *formP
								//过滤掉无效并且没使用过的表单字段
								form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
									_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
										return item.Label == info.Label
									})
									if b {
										return true
									}
									return false
								})
								for _, formItem := range form.Fields {
									if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && formItem.IsCustomFormulaField {
										val, exist := cohortFormCalcData[formItem.ID]
										if exist {
											c = append(c, val)
										} else {
											c = append(c, "")
										}

									}
								}
							}
							continue
						}
						// 随机时间
						if fields == data.ReportAttributesRandomTime {
							times := ""
							if item.CohortID == cohort.ID {
								times = randomTime
								if !attribute.AttributeInfo.Random {
									times = item.JoinTime
								}
							}
							if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
								times, err = typeRandomKeyGetRandomTime(subjectCohortMap[subject.(string)], zone)
								if !attribute.AttributeInfo.Random {
									times = subjectCohortMap[subject.(string)].JoinTime
								}
							}
							c = append(c, times)
						}
						//随机人
						if fields == data.ReportAttributesRandomOperator {
							atRandomOperator := ""
							var operatorHistory []models.History
							if item.CohortID == cohort.ID {
								operatorHistory = item.History
							}
							if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
								operatorHistory = subjectCohortMap[subject.(string)].History
							}

							//判断第二阶段是否有替换，如果有替换操作，那么第一阶段的随机操作人，同步
							replaceOperator := ""
							for _, history := range subjectCohortMap[subject.(string)].History {
								unicode := userMap[history.UID]
								operator := fmt.Sprintf("%s(%d)", history.User, unicode)
								if history.Key == "history.subject.label.at-random-replaced-new-a" ||
									history.Key == "history.subject.label.at-random-replaced-new-b" {
									replaceOperator = operator
									break
								}
							}

							for _, history := range operatorHistory {
								unicode := userMap[history.UID]
								operator := fmt.Sprintf("%s(%d)", history.User, unicode)
								if history.Key == "history.subject.random" ||
									history.Key == "history.subject.randomNoNumber" ||
									history.Key == "history.subject.randomSub" ||
									history.Key == "history.subject.label.random" ||
									history.Key == "history.subject.label.randomSub" ||
									history.Key == "history.subject.label.randomNoNumber" ||
									history.Key == "history.subject.label.randomNoNumberSub" ||
									history.Key == "history.subject.randomNoNumberSub" ||
									history.Key == "history.subject.label.at-random-random" ||
									history.Key == "history.subject.label.at-random-randomSub" ||
									history.Key == "history.subject.label.at-random-randomNoNumber" ||
									history.Key == "history.subject.label.at-random-randomNoNumberSub" {
									atRandomOperator = operator
								}
								if history.Key == "history.subject.label.at-random-replaced-new-a" ||
									history.Key == "history.subject.label.at-random-replaced-new-b" {
									replaceOperator = operator
									break
								}
							}
							if atRandomOperator == "" && (item.CohortID == cohort.LastID || item.CohortID == cohort.ID) {
								atRandomOperator = replaceOperator
							}
							c = append(c, atRandomOperator)
						}

						tmpGroup := ""
						tmpSubGroup := ""
						tmpGroupCode := ""
						if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
							tmpGroup, tmpSubGroup, tmpGroupCode = typeRandomKeyGetGroupGroup(haveSubGroup, isBlindedRole, attribute, subjectCohortMap[subject.(string)], randomLists)

						}

						if fields == data.ReportAttributesRandomConfigCode {
							groupCodeInfo := ""
							if item.CohortID == cohort.ID {
								groupCodeInfo = groupCode
							}
							if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
								groupCodeInfo = tmpGroupCode
							}
							c = append(c, groupCodeInfo)
						}
						if fields == data.ReportAttributesRandomGroup {
							groupInfo := ""
							if item.CohortID == cohort.ID {
								groupInfo = group
							}
							if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
								groupInfo = tmpGroup
							}
							c = append(c, groupInfo)
							if haveSubGroup {
								subGroupInfo := ""

								if item.CohortID == cohort.ID {
									subGroupInfo = subGroup
								}
								if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
									subGroupInfo = tmpSubGroup
								}
								c = append(c, subGroupInfo)

							}
						}
						if fields == data.ReportAttributesDispensingMedicineRealGroup && haveRegister {
							groupInfo := ""
							if item.CohortID == cohort.ID {
								if attribute.AttributeInfo.Blind && isBlindedRole {
									groupInfo = tools.BlindData
								} else {
									groupInfo = item.RegisterGroup
								}
							}
							if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
								if attribute.AttributeInfo.Blind && isBlindedRole {
									groupInfo = tools.BlindData
								} else {
									groupInfo = subjectCohortMap[subject.(string)].RegisterGroup
								}
							}
							c = append(c, groupInfo)
						}
						if isShowRandomNumber {
							if fields == data.ReportAttributesRandomNumber {
								tmpRandomNumber := ""
								if item.CohortID == cohort.ID {
									tmpRandomNumber = randomNumber
								}
								if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
									tmpRandomNumber = subjectCohortMap[subject.(string)].RandomNumber
								}
								c = append(c, tmpRandomNumber)
							}
							if fields == data.ReportAttributesRandomSubjectReplaceNumber {
								replaceNumber := ""
								if item.CohortID == cohort.ID {
									replaceNumber = item.ReplaceNumber
								}
								if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
									replaceNumber = subjectCohortMap[subject.(string)].ReplaceNumber
								}
								c = append(c, replaceNumber)
							}
						}
						if isShowRandomSequenceNumber {
							if fields == data.ReportAttributesRandomSequenceNumber {
								tmpRandomNumber := ""
								if item.CohortID == cohort.ID {
									tmpRandomNumber = randomSequenceNumber
								}
								if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
									tmpRandomNumber = subjectCohortMap[subject.(string)].RandomSequenceNumber
								}
								c = append(c, tmpRandomNumber)
							}
						}

						if field == data.ReportAttributesRandomScreenTime && haveScreen {
							tmpScreenTime := ""
							if item.CohortID == cohort.ID {
								tmpScreenTime = item.ScreenTime
							}
							if subjectCohortMap[subject.(string)].CohortID == cohort.ID {
								tmpScreenTime = subjectCohortMap[subject.(string)].ScreenTime

							}
							c = append(c, tmpScreenTime)

						}
					}
				}
			}

			{ // 随机时间是否需要换成 仅发药入组时间
				if !attribute.AttributeInfo.Random {
					randomTime = item.JoinTime
				}
			}

			// 项目
			if field == data.ReportAttributesProjectNumber {
				c = append(c, project.Number)
			}
			if field == data.ReportAttributesProjectName {
				c = append(c, project.Name)
			}
			// 国家
			if field == data.ReportAttributesInfoCountry {
				c = append(c, country)
			}
			// 区域
			if field == data.ReportAttributesInfoRegion {
				c = append(c, region)
			}
			// 中心编号
			if field == data.ReportAttributesInfoSiteNumber {
				c = append(c, siteNumber)
			}

			// 中心名称
			if field == data.ReportAttributesInfoSiteName {
				c = append(c, siteName)
			}

			// 受试者编号
			if field == data.ReportAttributesInfoSubjectNumber {
				c = append(c, subject)
			}

			// 分层因素
			if field == data.ReportAttributesRandomFactor && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {
				for _, factor := range factors {
					c = append(c, factor)
				}
				if len(factors) < glUsedInfoLength {
					for i := 0; i < glUsedInfoLength-len(factors); i++ {
						c = append(c, "")
					}
				}
			}
			// 实际分层因素
			if field == data.ReportAttributesRandomActualFactor && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {
				if project.Type == 2 {
					for _, cohort := range envInfo.Cohorts {
						formP, b := slice.Find(forms, func(index int, data models.Form) bool {
							return data.CohortID == cohort.ID
						})
						if b {
							form := *formP
							//过滤掉无效并且没使用过的表单字段
							form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
								_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
									return item.Label == info.Label
								})
								if b {
									return true
								}
								return false
							})
							for _, formItem := range form.Fields {
								if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && formItem.IsCustomFormulaField {
									val, exist := cohortActualFactors[formItem.ID]
									if exist {
										c = append(c, val)
									} else {
										c = append(c, "")
									}

								}
							}
						}
					}

				}
				for _, form := range actualFactors {
					c = append(c, form)
				}
				if len(actualFactors) < glUsedInfoLength {
					for i := 0; i < glUsedInfoLength-len(actualFactors); i++ {
						c = append(c, "")
					}
				}
			}

			// 表单配置
			if field == data.ReportAttributesRandomForm && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {
				if project.Type == 2 {
					for _, cohort := range envInfo.Cohorts {
						formP, b := slice.Find(forms, func(index int, data models.Form) bool {
							return data.CohortID == cohort.ID
						})
						if b {
							form := *formP
							//过滤掉无效并且没使用过的表单字段
							form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
								_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
									return item.Label == info.Label
								})
								if b {
									return true
								}
								return false
							})
							for _, formItem := range form.Fields {
								if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && !formItem.IsCustomFormulaField {
									val, exist := cohortFormData[formItem.ID]
									if exist {
										c = append(c, val)
									} else {
										c = append(c, "")
									}

								}
							}
						}
					}

				} else {

					for _, form := range formData {
						c = append(c, form)
					}
				}
			}
			// 分层计算表单
			if field == data.ReportAttributesRandomFactorCalc && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {
				if project.Type == 2 {
					for _, cohort := range envInfo.Cohorts {
						formP, b := slice.Find(forms, func(index int, data models.Form) bool {
							return data.CohortID == cohort.ID
						})
						if b {
							form := *formP
							//过滤掉无效并且没使用过的表单字段
							form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
								_, b := slice.Find(usedInfos, func(index int, info models.ListField) bool {
									return item.Label == info.Label
								})
								if b {
									return true
								}
								return false
							})
							for _, formItem := range form.Fields {
								if (formItem.ApplicationType == nil || *formItem.ApplicationType == 1 || *formItem.ApplicationType == 4) && formItem.IsCustomFormulaField {
									val, exist := cohortFormCalcData[formItem.ID]
									if exist {
										c = append(c, val)
									} else {
										c = append(c, "")
									}

								}
							}
						}
					}

				} else {

					for _, form := range formCalcData {
						c = append(c, form)
					}
				}
			}
			// 随机时间
			if field == data.ReportAttributesRandomTime && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {
				c = append(c, randomTime)
			}

			// 组别
			if field == data.ReportAttributesRandomGroup && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {
				c = append(c, group)
				if haveSubGroup {
					c = append(c, subGroup)
				}
			}

			// 组别
			if field == data.ReportAttributesDispensingMedicineRealGroup && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {
				c = append(c, registerGroup)
			}

			// 随机号
			if field == data.ReportAttributesRandomNumber && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {
				c = append(c, randomNumber)
			}

			// 随机顺序号
			if field == data.ReportAttributesRandomSequenceNumber && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {
				c = append(c, randomSequenceNumber)
			}

			// 登记时间
			if field == data.ReportAttributesRandomRegisterTime {
				c = append(c, registerTime)
			}

			// 登记操作人
			if field == data.ReportAttributesRandomRegisterOperator {
				c = append(c, registerOperator)
			}

			// 随机操作人
			if field == data.ReportAttributesRandomOperator {
				c = append(c, randomOperator)
			}

			// 停用操作人
			if field == data.ReportAttributesRandomSignOutOperator {
				c = append(c, signOutOperator)
			}
			// 停用时间
			if field == data.ReportAttributesRandomSignOutTime {
				c = append(c, signOutTime)
			}
			// 实际停用时间
			if field == data.ReportAttributesRandomSignOutRealTime {
				c = append(c, item.SignOutRealTime)
			}
			// 停用原因
			if field == data.ReportAttributesRandomSignOutReason {
				c = append(c, signOutReason)
			}
			// 筛选时间
			if field == data.ReportAttributesRandomScreenTime {
				if len(showScreenTime) > 0 {
					c = append(c, item.ScreenTime)
				}
			}
			// icf签署时间
			if field == data.ReportAttributesRandomICFTime {
				c = append(c, item.ICFTime)
			}
			// 完成研究备注
			if field == data.ReportAttributesRandomFinishRemark {
				finishRemark := item.FinishRemark
				if finishRemark == "" {
					finishRemark = subjectCohortMap[subject.(string)].FinishRemark
				}

				c = append(c, finishRemark)
			}

			// 计划完成时间
			if field == data.ReportAttributesRandomPlanTime {
				planTime := ""
				if item.Group == "" && visitCyclesMap[item.CohortID].VisitType == 0 {
					err = slice.SortByField(item.Dispensing, "SerialNumber")
					if err != nil {
						return "", nil, errors.WithStack(err)
					}
					firstDispensingTime := time.Duration(0)
					visitNumber := ""
					for _, dispensing := range item.Dispensing {
						if !dispensing.VisitSign && dispensing.Status != 1 {
							firstDispensingTime = dispensing.DispensingTime
							visitNumber = dispensing.VisitNumber
							break
						}
					}
					if firstDispensingTime != 0 {
						visitInfoP, ok := slice.Find(visitCyclesMap[item.CohortID].Infos, func(index int, item models.VisitCycleInfo) bool {
							return item.Number == visitNumber && item.Interval != nil
						})
						if ok {
							visitInfo := *visitInfoP
							planRandomTime := time.Duration(time.Unix(int64(firstDispensingTime), 0).Add(time.Hour * time.Duration(-24**visitInfo.Interval)).Unix())

							// 计算计划随机时间
							timeZone := zone
							if item.Tz != "" {
								timeStr, err := tools.GetLocationFloat(item.Tz)
								if err != nil {
									return "", nil, errors.WithStack(err)
								}
								//timeZoneI, err := convertor.ToInt(timeStr)
								//if err != nil {
								//	return "", nil, errors.WithStack(err)
								//}
								timeZone = timeStr
							}
							hours := time.Duration(timeZone)
							minutes := time.Duration((timeZone - float64(hours)) * 60)
							duration := hours*time.Hour + minutes*time.Minute
							planTime = time.Unix(time.Duration(planRandomTime).Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02")
						}
					}

				}

				c = append(c, planTime)
			}

			// 状态
			if field == data.ReportAttributesInfoStatus && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {

				if !attribute.AttributeInfo.Random && attribute.AttributeInfo.Dispensing && (item.Status == 1 || item.Status == 7) {
					_, ok := slice.Find(item.Dispensing, func(index int, it models.DispensingSimpleInfo) bool {
						return it.Status == 2 || it.Status == 3
					})
					if ok {
						item.Status = 11
					}
				}

				c = append(c, statusItem(ctx, item.Status))
			}
			// 阶段
			if field == data.ReportAttributesRandomStage {
				cohort := cohortMap[item.CohortID]
				c = append(c, cohort)
			}

			// 替换受试者号
			if field == data.ReportAttributesRandomSubjectNumberReplace {
				c = append(c, replaceSubject)
			}
			// 替换受试者随机号
			if field == data.ReportAttributesRandomSubjectReplaceNumber && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {
				c = append(c, replaceSubjectNumber)
			}
			// 替换受试者状态
			if field == data.ReportAttributesRandomSubjectReplaceStatus {
				if item.ReplaceSubjectStatus != nil {
					replaceSubjectStatus = statusItem(ctx, *item.ReplaceSubjectStatus)
				}
				c = append(c, replaceSubjectStatus)
			}
			// 替换受试者时间
			if field == data.ReportAttributesRandomSubjectReplaceTime {
				if replaceSubject != "" {
					c = append(c, replaceSubjectTime)
				} else {
					c = append(c, "") // 替换受试者也有替换时间，需要过滤不写入
				}
			}

			// 群组
			if field == data.ReportAttributesRandomCohort {
				cohort := cohortMap[item.CohortID]
				c = append(c, cohort)
			}
			// 组别代码
			if field == data.ReportAttributesRandomConfigCode && (project.Type == 1 || (project.Type == 2 && !haveCohortReRandom)) {
				c = append(c, groupCode)
			}
		}

		content[i] = c
	}

	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	streamWriter, _ := f.NewStreamWriter("Sheet1")
	t := make([]interface{}, len(title))
	for i, item := range title {
		t[i] = excelize.Cell{Value: item}
	}
	_ = streamWriter.SetRow("A1", t)
	for i := 1; i <= len(content); i++ {
		r := make([]interface{}, len(content[i-1]))
		for j := 0; j < len(content[i-1]); j++ {
			r[j] = excelize.Cell{Value: content[i-1][j]}
		}
		cell, _ := excelize.CoordinatesToCellName(1, i+1)
		_ = streamWriter.SetRow(cell, r)
	}
	_ = streamWriter.Flush()

	if len(subjects) > 0 {
		//设置第一行title
		sheet2Title := []interface{}{
			locales.Tr(ctx, "report.attributes.project.number"),
			locales.Tr(ctx, "report.attributes.project.name"),
		}
		if project.Type == 2 {
			sheet2Title = append(sheet2Title, locales.Tr(ctx, "report.attributes.random.cohort"))
		} else if project.Type == 3 {
			sheet2Title = append(sheet2Title, locales.Tr(ctx, "report.attributes.random.stage"))
		}
		sheet2Title = append(sheet2Title, locales.Tr(ctx, "report.attributes.info.site.number"))
		sheet2Title = append(sheet2Title, locales.Tr(ctx, "report.attributes.info.site.name"))
		t := make([]interface{}, len(sheet2Title))
		for i, item := range sheet2Title {
			t[i] = excelize.Cell{Value: item}
		}
		f.NewSheet("Sheet2")
		streamWriter, _ := f.NewStreamWriter("Sheet2")
		_ = streamWriter.SetRow("A1", t)

		//数据部分
		if project.Type != 1 {
			if len(cohortOIDs) <= 0 {
				cohortOIDs = allCohortIDs
			}
			rowIndex := 1
			for _, chortId := range cohortOIDs {
				//查询受试者
				filter := bson.M{"project_id": projectOID, "env_id": envOID, "cohort_id": chortId, "project_site_id": bson.M{"$in": projectSiteOIDs}, "deleted": bson.M{"$ne": true}}
				match := bson.M{"$and": bson.A{
					filter,
					bson.M{"random_number": bson.M{"$ne": nil}},
					bson.M{"random_number": bson.M{"$ne": ""}},
					bson.M{"status": bson.M{"$ne": 5}},
				}}
				roomPipeline := mongo.Pipeline{
					{{Key: "$match", Value: match}},
					{{Key: "$lookup", Value: bson.M{
						"from":         "project_site",
						"localField":   "project_site_id",
						"foreignField": "_id",
						"as":           "project_site",
					}}},
					{{Key: "$unwind", Value: "$project_site"}},
					{{Key: "$project", Value: bson.M{
						"_id":             1,
						"project_site_id": "$project_site._id",
						"site_number":     "$project_site.number",
						"site_name":       models.ProjectSiteNameLookUpBson(ctx),
						"info":            "$info",
					}}},
					{{Key: "$group", Value: bson.M{
						"_id":      bson.M{"project_site_id": "$project_site_id", "site_number": "$site_number", "site_name": "$site_name"},
						"subjects": bson.M{"$push": "$info"},
					}}},
					{{Key: "$sort", Value: bson.D{{"site_number", 1}}}},
				}
				var siteSubjects []map[string]interface{}
				subjectsCursor, err := tools.Database.Collection("subject").Aggregate(ctx, roomPipeline)
				if err != nil {
					return "", nil, errors.WithStack(err)
				}
				err = subjectsCursor.All(ctx, &siteSubjects)
				if err != nil {
					return "", nil, errors.WithStack(err)
				}
				randomListCohort := slice.Filter(randomLists, func(index int, data models.RandomList) bool {
					return data.CohortID == chortId
				})
				randomFactors := getRandomFactor(randomListCohort)
				for _, site := range siteSubjects { //中心
					r1 := make([]interface{}, 100)
					r2 := make([]interface{}, 100)
					r3 := make([]interface{}, 100)
					siteId := site["_id"].(map[string]interface{})
					r1[0] = excelize.Cell{Value: project.Number}
					r1[1] = excelize.Cell{Value: project.Name}
					r1[2] = excelize.Cell{Value: cohortMap[chortId]}
					r1[3] = excelize.Cell{Value: siteId["site_number"].(string)}
					r1[4] = excelize.Cell{Value: siteId["site_name"].(string)}
					facIndex := 5
					subjects := site["subjects"].(primitive.A)
					cellIndex := 5
					for _, factor := range randomFactors { //分层因素
						for _, option := range factor.Options { //分层因素的值
							factorCount := 0
							for _, subjectInfo := range subjects { //该中心下的受试者
								infos := subjectInfo.(primitive.A)
								for _, info := range infos {
									infomap := info.(map[string]interface{})
									if infomap["value"] != nil && infomap["name"].(string) == factor.Name && infomap["value"].(string) == option.Value {
										factorCount++
									}
								}
							}
							r1[facIndex] = factor.Label
							r2[facIndex] = option.Label
							r3[facIndex] = factorCount
							facIndex++
						}
						//分层因素合并
						title1Hcell, _ := excelize.CoordinatesToCellName(cellIndex+1, rowIndex+1)
						cellIndex = cellIndex + len(factor.Options)
						title1Vcell, _ := excelize.CoordinatesToCellName(cellIndex, rowIndex+1)
						_ = streamWriter.MergeCell(title1Hcell, title1Vcell)
					}
					cell1, _ := excelize.CoordinatesToCellName(1, rowIndex+1)
					cell2, _ := excelize.CoordinatesToCellName(1, rowIndex+2)
					cell3, _ := excelize.CoordinatesToCellName(1, rowIndex+3)

					A1Hcell, _ := excelize.CoordinatesToCellName(1, rowIndex+1)
					A3Hcell, _ := excelize.CoordinatesToCellName(1, rowIndex+3)
					_ = streamWriter.MergeCell(A1Hcell, A3Hcell)

					B1Hcell, _ := excelize.CoordinatesToCellName(2, rowIndex+1)
					B3Hcell, _ := excelize.CoordinatesToCellName(2, rowIndex+3)
					_ = streamWriter.MergeCell(B1Hcell, B3Hcell)

					C1Hcell, _ := excelize.CoordinatesToCellName(3, rowIndex+1)
					C3Hcell, _ := excelize.CoordinatesToCellName(3, rowIndex+3)
					_ = streamWriter.MergeCell(C1Hcell, C3Hcell)

					D1Hcell, _ := excelize.CoordinatesToCellName(4, rowIndex+1)
					D3Hcell, _ := excelize.CoordinatesToCellName(4, rowIndex+3)
					_ = streamWriter.MergeCell(D1Hcell, D3Hcell)

					E1Hcell, _ := excelize.CoordinatesToCellName(5, rowIndex+1)
					E3Hcell, _ := excelize.CoordinatesToCellName(5, rowIndex+3)
					_ = streamWriter.MergeCell(E1Hcell, E3Hcell)

					_ = streamWriter.SetRow(cell1, r1)
					_ = streamWriter.SetRow(cell2, r2)
					_ = streamWriter.SetRow(cell3, r3)

					rowIndex = rowIndex + 3
				}
			}
		} else {
			//查询受试者
			filter := bson.M{"project_id": projectOID, "env_id": envOID, "project_site_id": bson.M{"$in": projectSiteOIDs}, "deleted": bson.M{"$ne": true}}
			match := bson.M{"$and": bson.A{
				filter,
				bson.M{"random_number": bson.M{"$ne": nil}},
				bson.M{"random_number": bson.M{"$ne": ""}},
			}}
			roomPipeline := mongo.Pipeline{
				{{Key: "$match", Value: match}},
				{{Key: "$lookup", Value: bson.M{
					"from":         "project_site",
					"localField":   "project_site_id",
					"foreignField": "_id",
					"as":           "project_site",
				}}},
				{{Key: "$unwind", Value: "$project_site"}},
				{{Key: "$project", Value: bson.M{
					"_id":             1,
					"project_site_id": "$project_site._id",
					"site_number":     "$project_site.number",
					"site_name":       models.ProjectSiteNameLookUpBson(ctx),
					"info":            "$info",
				}}},
				{{Key: "$group", Value: bson.M{
					"_id":      bson.M{"project_site_id": "$project_site_id", "site_number": "$site_number", "site_name": "$site_name"},
					"subjects": bson.M{"$push": "$info"},
				}}},
				{{Key: "$sort", Value: bson.D{{"site_number", 1}}}},
			}
			var siteSubjects []map[string]interface{}
			subjectsCursor, err := tools.Database.Collection("subject").Aggregate(ctx, roomPipeline)
			if err != nil {
				return "", nil, errors.WithStack(err)
			}
			err = subjectsCursor.All(ctx, &siteSubjects)
			if err != nil {
				return "", nil, errors.WithStack(err)
			}
			randomFactors := getRandomFactor(randomLists)
			rowIndex := 1
			for _, site := range siteSubjects { //中心
				r1 := make([]interface{}, 100)
				r2 := make([]interface{}, 100)
				r3 := make([]interface{}, 100)
				siteId := site["_id"].(map[string]interface{})
				r1[0] = excelize.Cell{Value: project.Number}
				r1[1] = excelize.Cell{Value: project.Name}
				r1[2] = excelize.Cell{Value: siteId["site_number"].(string)}
				r1[3] = excelize.Cell{Value: siteId["site_name"].(string)}
				facIndex := 4
				subjects := site["subjects"].(primitive.A)
				cellIndex := 4
				for _, factor := range randomFactors { //分层因素
					for _, option := range factor.Options { //分层因素的值
						factorCount := 0
						for _, subjectInfo := range subjects { //该中心下的受试者
							infos := subjectInfo.(primitive.A)
							for _, info := range infos {
								infomap := info.(map[string]interface{})
								if infomap["name"] != nil && infomap["name"].(string) == factor.Name && infomap["value"] != nil && infomap["value"].(string) == option.Value {
									factorCount++
								}
							}
						}
						r1[facIndex] = factor.Label
						r2[facIndex] = option.Label
						r3[facIndex] = factorCount
						facIndex++
					}
					//分层因素合并
					title1Hcell, _ := excelize.CoordinatesToCellName(cellIndex+1, rowIndex+1)
					cellIndex = cellIndex + len(factor.Options)
					title1Vcell, _ := excelize.CoordinatesToCellName(cellIndex, rowIndex+1)
					_ = streamWriter.MergeCell(title1Hcell, title1Vcell)
				}
				cell1, _ := excelize.CoordinatesToCellName(1, rowIndex+1)
				cell2, _ := excelize.CoordinatesToCellName(1, rowIndex+2)
				cell3, _ := excelize.CoordinatesToCellName(1, rowIndex+3)

				A1Hcell, _ := excelize.CoordinatesToCellName(1, rowIndex+1)
				A3Hcell, _ := excelize.CoordinatesToCellName(1, rowIndex+3)
				_ = streamWriter.MergeCell(A1Hcell, A3Hcell)

				B1Hcell, _ := excelize.CoordinatesToCellName(2, rowIndex+1)
				B3Hcell, _ := excelize.CoordinatesToCellName(2, rowIndex+3)
				_ = streamWriter.MergeCell(B1Hcell, B3Hcell)

				C1Hcell, _ := excelize.CoordinatesToCellName(3, rowIndex+1)
				C3Hcell, _ := excelize.CoordinatesToCellName(3, rowIndex+3)
				_ = streamWriter.MergeCell(C1Hcell, C3Hcell)

				D1Hcell, _ := excelize.CoordinatesToCellName(4, rowIndex+1)
				D3Hcell, _ := excelize.CoordinatesToCellName(4, rowIndex+3)
				_ = streamWriter.MergeCell(D1Hcell, D3Hcell)

				_ = streamWriter.SetRow(cell1, r1)
				_ = streamWriter.SetRow(cell2, r2)
				_ = streamWriter.SetRow(cell3, r3)

				rowIndex = rowIndex + 3
			}
		}
		_ = streamWriter.Flush()
	}

	fileName := fmt.Sprintf("%s[%s]SubjectDetailReport_%s.xlsx", project.Number, envInfo.Name, now.Format("20060102150405"))
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	return fileName, buffer.Bytes(), nil
}

// 获取小数部分的数字
func getFractionDigits(f float64) (int, error) {
	// 分离整数和小数部分
	_, fractionalPart := math.Modf(f)

	// 将小数部分转换为字符串形式，以便于处理
	fractionStr := fmt.Sprintf("%.12f", fractionalPart)[2:] // 去掉"0."

	// 移除末尾不必要的零
	for len(fractionStr) > 0 && fractionStr[len(fractionStr)-1] == '0' {
		fractionStr = fractionStr[:len(fractionStr)-1]
	}

	a, err := strconv.Atoi(fractionStr)
	if err != nil {
		return 0, err
	}

	return a, nil
}
