package service

import (
	"clinflash-irt/data"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type RoleService struct {
}

func (s *RoleService) List(ctx *gin.Context, search string, roleType int, all string) ([]models.RolePermission, error) {
	collection := tools.Database.Collection("role_permission")
	d := make([]models.RolePermission, 0)
	if all == "" {
		all = `, "status" : 1`
	} else {
		all = ""
	}
	//str := fmt.Sprintf(`
	//[
	//	{"$match": {"name": {"$regex": "%s"}, "template":%d %s}},
	//	{"$sort": {"name": 1}}
	//]`, search, roleType, all)
	//pipeline := tools.MongoPipeline(str)
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"name": bson.M{"$regex": search}, "template": roleType}}},
		{{Key: "$sort", Value: bson.D{{Key: "name", Value: 1}}}},
	}
	cursor, err := collection.Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor.All(nil, &d)
	return d, nil
}

func (s *RoleService) ListPool(ctx *gin.Context) (interface{}, error) {
	return data.RolePools, nil

}

func (s *RoleService) Add(ctx *gin.Context, role models.RolePermission) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		collection := tools.Database.Collection("role_permission")
		if count, _ := collection.CountDocuments(sctx, bson.M{"name": role.Name, "template": role.Template}); count > 0 {
			return nil, tools.BuildServerError(ctx, "roles.duplicated.names")
		}
		role.ID = primitive.NewObjectID()
		if _, err := collection.InsertOne(sctx, role); err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *RoleService) Update(ctx *gin.Context, id string, role models.RolePermission) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		collection := tools.Database.Collection("role_permission")
		oid, _ := primitive.ObjectIDFromHex(id)
		if count, _ := collection.CountDocuments(sctx, bson.M{"_id": bson.M{"$ne": oid}, "name": role.Name, "template": role.Template}); count > 0 {
			return nil, tools.BuildServerError(ctx, "roles.duplicated.names")
		}
		// 客户管理员-》 角色权限 存在 不允许删除
		if role.Name == "Sys-Admin" {
			set := tools.SetFactory()
			for _, number := range role.Permissions {
				set.Add(number)
			}
			set.Add("operation.settings.roles.view")
			set.Add("operation.settings.roles.add")
			set.Add("operation.settings.roles.edit")
			set.Add("operation.settings.roles.config")
			if set.Len() != len(role.Permissions) {
				return nil, tools.BuildServerError(ctx, "roles.duplicated.sys_check")
			}
		} else {
			// 设置-角色权限，菜单仅开放Sys-Admin。  其他角色配置后的移除
			set := tools.SetFactory()
			for _, number := range role.Permissions {
				set.Add(number)
			}
			set.Remove("operation.settings.roles.view")
			set.Remove("operation.settings.roles.add")
			set.Remove("operation.settings.roles.edit")
			set.Remove("operation.settings.roles.config")
			if set.Len() != len(role.Permissions) {
				var permissionsStrings []string
				for _, item := range set.IterKey() {
					permissionsStrings = append(permissionsStrings, item.(string))
				}
				// 权限强制去除 角色菜单权限
				role.Permissions = permissionsStrings
			}
		}

		//项目管理员 -》 项目查看权限 - 存在 不允许删除
		if role.Name == "Project-admin" {
			set := tools.SetFactory()
			for _, number := range role.Permissions {
				set.Add(number)
			}
			set.Add("operation.projects.main.view")
			if set.Len() != len(role.Permissions) {
				return nil, tools.BuildServerError(ctx, "roles.duplicated.project_admin_check")
			}
		}

		update := bson.M{"$set": bson.M{
			"name":        role.Name,
			"description": role.Description,
			"scope":       role.Scope,
			"template":    role.Template,
			"status":      role.Status,
			"permissions": role.Permissions,
		}}
		if _, err := collection.UpdateOne(sctx, bson.M{"_id": oid}, update); err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *RoleService) Delete(ctx *gin.Context, id string) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		oid, _ := primitive.ObjectIDFromHex(id)
		//判断角色是否已经被使用
		match := bson.M{"customers.roles": oid}
		if count, _ := tools.Database.Collection("user").CountDocuments(sctx, match); count > 0 {
			return nil, tools.BuildServerError(ctx, "roles.delete.message")
		}
		res, err := tools.Database.Collection("role_permission").DeleteOne(sctx, bson.M{"_id": oid})
		if err != nil || res.DeletedCount != 1 {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *RoleService) Get(ctx *gin.Context, id string) (models.RolePermission, error) {
	oid, _ := primitive.ObjectIDFromHex(id)
	var document models.RolePermission
	if err := tools.Database.Collection("role_permission").FindOne(nil, bson.M{"_id": oid}).Decode(&document); err != nil {
		return models.RolePermission{}, errors.WithStack(err)
	}
	return document, nil
}
