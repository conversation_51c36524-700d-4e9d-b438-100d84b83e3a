package service

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/json"
	"sort"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/mongo/options"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// supplyPlanServices
type SupplyPlanServices struct {
}

func (s *SupplyPlanServices) SupplyPlanList(ctx *gin.Context, customerID string, projectID string, envID string, start int, limit int) (map[string]interface{}, error) {
	collection := tools.Database.Collection("supply_plan")
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}
	project := bson.M{
		"id":          "$_id",
		"_id":         0,
		"name":        "$info.name",
		"siteIds":     "$info.site_ids",
		"allSite":     "$info.all_site",
		"status":      "$info.status",
		"storehouse":  "$info.storehouse_id",
		"description": "$info.description",
		"planControl": "$info.plan_control",
		"siteWarning": "$info.site_warning",
		"autoSupply":  "$info.auto_supply",
	}
	total, err := collection.CountDocuments(nil, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var data []map[string]interface{}
	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$project", Value: project}},
		{{Key: "$sort", Value: bson.D{{Key: "name", Value: 1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
	}
	//查询全部
	if limit == 0 {
		pipepine = mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$project", Value: project}},
			{{Key: "$sort", Value: bson.D{{Key: "name", Value: 1}}}},
		}
	}
	cursor, err := collection.Aggregate(nil, pipepine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return map[string]interface{}{"total": total, "items": data}, nil
}

func (s *SupplyPlanServices) AddSupplyPlan(ctx *gin.Context, params models.SupplyPlan) error {
	ID := primitive.NewObjectID()
	data := models.SupplyPlan{
		ID:             ID,
		CustomerID:     params.CustomerID,
		ProjectID:      params.ProjectID,
		EnvironmentID:  params.EnvironmentID,
		SupplyPlanInfo: params.SupplyPlanInfo,
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		count, err := tools.Database.Collection("supply_plan").CountDocuments(sctx, bson.M{"env_id": params.EnvironmentID, "info.name": params.SupplyPlanInfo.Name})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if count > 0 {
			return nil, tools.BuildServerError(ctx, "supply_plan_duplicated_name")
		}

		_, err = tools.Database.Collection("supply_plan").InsertOne(sctx, data)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// TODO保存项目日志
		var OperationLogFieldGroups []models.OperationLogFieldGroup
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply.name",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: params.SupplyPlanInfo.Name,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply.status",
			Old: models.OperationLogField{
				Type:  6,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  6,
				Value: params.SupplyPlanInfo.Status,
			},
		})
		//适用中心
		siteLogValue := models.SupplyPlanSiteOperationLog{}
		if len(params.SupplyPlanInfo.SiteIds) == 0 {
			siteLogValue.AllSiteTranKey = "operation_log.supply.all_site"
		} else {
			siteNameStr := getSiteNameStr(ctx, params.SupplyPlanInfo.SiteIds)
			siteLogValue.SiteNames = siteNameStr
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply.site",
			Old: models.OperationLogField{
				Type:  9,
				Value: models.SupplyPlanSiteOperationLog{},
			},
			New: models.OperationLogField{
				Type:  9,
				Value: siteLogValue,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply.control",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  4,
				Value: params.SupplyPlanInfo.PlanControl,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply.alarm",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  5,
				Value: params.SupplyPlanInfo.SiteWarning,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply.supply",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  5,
				Value: params.SupplyPlanInfo.AutoSupply,
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply.desc",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: params.SupplyPlanInfo.Description,
			},
		})
		marks := []models.Mark{}
		marks = append(marks,
			models.Mark{
				Label: "operation_log.label.supply",
				Value: params.SupplyPlanInfo.Name,
				Blind: false,
			})
		err = tools.SaveOperation(ctx, sctx, "operation_log.module.supply", params.EnvironmentID, 1, OperationLogFieldGroups, marks, ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func getSiteNameStr(ctx *gin.Context, ids []primitive.ObjectID) string {
	opts := &options.FindOptions{
		Projection: bson.M{
			"_id":  0,
			"id":   "$_id",
			"name": models.ProjectSiteNameBson(ctx),
		},
	}
	type ProjectSite struct {
		ID   primitive.ObjectID `bson:"id"`
		Name string             `bson:"name"`
	}
	projectSites := make([]ProjectSite, 0)
	cursor, err := tools.Database.Collection("project_site").Find(nil, bson.M{"_id": bson.M{"$in": ids}}, opts)
	if err != nil {
	}
	err = cursor.All(nil, &projectSites)
	if err != nil {
	}
	siteNames := slice.Map(projectSites, func(index int, item ProjectSite) string {
		return item.Name
	})
	siteNameStr := strings.Join(siteNames, ",")
	return siteNameStr
}

func (s *SupplyPlanServices) UpdateSupplyPlan(ctx *gin.Context, params models.SupplyPlan) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		count, err := tools.Database.Collection("supply_plan").CountDocuments(sctx, bson.M{
			"env_id":    params.EnvironmentID,
			"info.name": params.SupplyPlanInfo.Name,
			"_id":       bson.M{"$ne": params.ID}})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if count > 0 {
			return nil, tools.BuildServerError(ctx, "supply_plan_duplicated_name")
		}

		filter := bson.M{"_id": params.ID}
		update := bson.M{
			"$set": bson.M{
				"info": params.SupplyPlanInfo,
			},
		}
		// 查询旧的供应计划
		var oldSupplyPlan models.SupplyPlan
		err = tools.Database.Collection("supply_plan").FindOne(sctx, filter).Decode(&oldSupplyPlan)
		if err != nil && mongo.ErrNoDocuments != err {
			return nil, err
		}
		res, err := tools.Database.Collection("supply_plan").UpdateOne(sctx, filter, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		nilObjectId := primitive.NilObjectID
		if params.SupplyPlanInfo.Status == 0 {
			_, err = tools.Database.Collection("project_site").UpdateMany(sctx, bson.M{
				"env_id":         params.EnvironmentID,
				"supply_plan_id": params.ID,
			}, bson.M{
				"$set": bson.M{"supply_plan_id": nilObjectId},
			})
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else if len(params.SupplyPlanInfo.SiteIds) > 0 {
			_, err = tools.Database.Collection("project_site").UpdateMany(sctx, bson.M{
				"env_id":         params.EnvironmentID,
				"supply_plan_id": params.ID,
				"_id":            bson.M{"$nin": params.SupplyPlanInfo.SiteIds},
			}, bson.M{
				"$set": bson.M{"supply_plan_id": nilObjectId},
			})
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		if res.ModifiedCount != 0 { // 全都没更新 不需要写入项目日志
			// TODO保存项目日志
			var OperationLogFieldGroups []models.OperationLogFieldGroup
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.supply",
				TranKey: "operation_log.supply.name",
				Old: models.OperationLogField{
					Type:  2,
					Value: oldSupplyPlan.SupplyPlanInfo.Name,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: params.SupplyPlanInfo.Name,
				},
			})
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.supply",
				TranKey: "operation_log.supply.status",
				Old: models.OperationLogField{
					Type:  6,
					Value: oldSupplyPlan.SupplyPlanInfo.Name,
				},
				New: models.OperationLogField{
					Type:  6,
					Value: params.SupplyPlanInfo.Status,
				},
			})
			//适用中心
			siteLogValue := models.SupplyPlanSiteOperationLog{}
			if len(params.SupplyPlanInfo.SiteIds) == 0 {
				siteLogValue.AllSiteTranKey = "operation_log.supply.all_site"
			} else {
				siteNameStr := getSiteNameStr(ctx, params.SupplyPlanInfo.SiteIds)
				siteLogValue.SiteNames = siteNameStr
			}
			oldSiteLogValue := models.SupplyPlanSiteOperationLog{}
			if len(oldSupplyPlan.SupplyPlanInfo.SiteIds) == 0 {
				oldSiteLogValue.AllSiteTranKey = "operation_log.supply.all_site"
			} else {
				siteNameStr := getSiteNameStr(ctx, oldSupplyPlan.SupplyPlanInfo.SiteIds)
				oldSiteLogValue.SiteNames = siteNameStr
			}
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.supply",
				TranKey: "operation_log.supply.site",
				Old: models.OperationLogField{
					Type:  9,
					Value: oldSiteLogValue,
				},
				New: models.OperationLogField{
					Type:  9,
					Value: siteLogValue,
				},
			})

			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.supply",
				TranKey: "operation_log.supply.control",
				Old: models.OperationLogField{
					Type:  4,
					Value: oldSupplyPlan.SupplyPlanInfo.PlanControl,
				},
				New: models.OperationLogField{
					Type:  4,
					Value: params.SupplyPlanInfo.PlanControl,
				},
			})

			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.supply",
				TranKey: "operation_log.supply.alarm",
				Old: models.OperationLogField{
					Type:  5,
					Value: oldSupplyPlan.SupplyPlanInfo.SiteWarning,
				},
				New: models.OperationLogField{
					Type:  5,
					Value: params.SupplyPlanInfo.SiteWarning,
				},
			})

			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.supply",
				TranKey: "operation_log.supply.supply",
				Old: models.OperationLogField{
					Type:  5,
					Value: oldSupplyPlan.SupplyPlanInfo.AutoSupply,
				},
				New: models.OperationLogField{
					Type:  5,
					Value: params.SupplyPlanInfo.AutoSupply,
				},
			})
			marks := []models.Mark{}
			marks = append(marks, models.Mark{
				Label: "operation_log.label.supply",
				Value: oldSupplyPlan.SupplyPlanInfo.Name,
				Blind: false,
			})
			err = tools.SaveOperation(ctx, sctx, "operation_log.module.supply", oldSupplyPlan.EnvironmentID, 2, OperationLogFieldGroups, marks, oldSupplyPlan.ID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *SupplyPlanServices) DeleteSupplyPlan(ctx *gin.Context, id string) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		oid, _ := primitive.ObjectIDFromHex(id)
		var oldSupplyPlan models.SupplyPlan
		err := tools.Database.Collection("supply_plan").FindOne(sctx, bson.M{"_id": oid}).Decode(&oldSupplyPlan)
		if err != nil {
			return nil, err
		}
		if _, err := tools.Database.Collection("supply_plan").DeleteOne(sctx, bson.M{"_id": oid}); err != nil {
			return nil, errors.WithStack(err)
		}

		if _, err := tools.Database.Collection("supply_plan_medicine").DeleteMany(sctx, bson.M{"supply_plan_id": oid}); err != nil {
			return nil, errors.WithStack(err)
		}

		// TODO保存项目日志
		var OperationLogFieldGroups []models.OperationLogFieldGroup
		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.supply",
			Value: oldSupplyPlan.SupplyPlanInfo.Name,
			Blind: false,
		})
		err = tools.SaveOperation(ctx, sctx, "operation_log.module.supply", oldSupplyPlan.EnvironmentID, 3, OperationLogFieldGroups, marks, oid)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *SupplyPlanServices) SupplyPlanMedicineList(ctx *gin.Context, id string, roleId string) ([]models.SupplyPlanMedicine, error) {
	oid, _ := primitive.ObjectIDFromHex(id)
	match := bson.M{
		"supply_plan_id": oid,
	}
	cursor, err := tools.Database.Collection("supply_plan_medicine").Find(nil, match)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var supplyPlanMedicine []models.SupplyPlanMedicine
	err = cursor.All(nil, &supplyPlanMedicine)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// attribute, err := database.GetAttribute(nil, attributeId)
	// if err != nil {
	// 	return nil, err
	// }
	//isBlindedRole := false
	// if attribute.AttributeInfo.Blind {
	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return nil, err
	}
	// }
	// if attribute.AttributeInfo.Blind && isBlindedRole {

	for i := 0; i < len(supplyPlanMedicine); i++ {
		// isOpenDrug, _ := tools.IsOpenDrug(supplyPlanMedicine[i].EnvironmentID, primitive.NilObjectID, supplyPlanMedicine[i].Medicine.MedicineName)
		// isOtherDrug, _ := tools.IsOtherDrug(supplyPlanMedicine[i].EnvironmentID, supplyPlanMedicine[i].Medicine.MedicineName)
		// if !isOpenDrug && !isOtherDrug {
		isBlindedDrug, _ := tools.IsBlindedDrug(supplyPlanMedicine[i].EnvironmentID, supplyPlanMedicine[i].Medicine.MedicineName)
		if isBlindedDrug {
			if isBlindedRole {
				supplyPlanMedicine[i].Medicine.MedicineName = tools.BlindData
			}
			supplyPlanMedicine[i].Medicine.IsBlind = true
		}
	}

	return supplyPlanMedicine, nil

}

func (s *SupplyPlanServices) AddSupplyPlanMedicine(ctx *gin.Context, data models.SupplyPlanMedicine, ssctx mongo.SessionContext) error {
	match := bson.M{
		"supply_plan_id":     data.SupplyPlanID,
		"info.medicine_name": data.Medicine.MedicineName,
	}
	var histories []models.History

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		if ssctx != nil {
			sctx = ssctx
		}
		// 校验补充方式
		if ssctx == nil {
			if count, _ := tools.Database.Collection("supply_plan_medicine").CountDocuments(sctx, match); count > 0 {
				return nil, tools.BuildServerError(ctx, "medicine_duplicated")
			}
			err := checkAutoSupplySize(ctx, sctx, data)
			if err != nil {
				return nil, err
			}
		}

		data.ID = primitive.NewObjectID()
		ret, _ := tools.Database.Collection("supply_plan_medicine").InsertOne(sctx, data)
		var timeUnix = time.Now().Unix()
		u, _ := ctx.Get("user")
		user := u.(models.User)

		// 插入项目日志
		err := insertSupplyPlanMedicineLog(ctx, sctx, data.EnvironmentID, 1, models.MedicinePlanInfo{}, data.Medicine, data.ID, data.SupplyPlanID, "")
		if err != nil {
			return nil, errors.WithStack(err)
		}
		jsondata, _ := json.Marshal(&data.Medicine)
		var dataMap map[string]interface{}
		json.Unmarshal(jsondata, &dataMap)
		history := models.History{
			Key:  "history.supply-plan-medicine.add",
			OID:  ret.InsertedID.(primitive.ObjectID),
			Data: dataMap,
			Time: time.Duration(timeUnix),
			UID:  user.ID,
			User: user.Name,
		}
		histories = append(histories, history)
		ctx.Set("HISTORY", histories)

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *SupplyPlanServices) UpdateSupplyPlanMedicine(ctx *gin.Context, id string, data models.SupplyPlanMedicine, ssctx mongo.SessionContext) error {

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		if ssctx != nil {
			sctx = ssctx
		}
		oid, _ := primitive.ObjectIDFromHex(id)
		match := bson.M{
			"_id": oid,
		}
		var supplyPlan models.SupplyPlanMedicine
		if err := tools.Database.Collection("supply_plan_medicine").FindOne(sctx, match).Decode(&supplyPlan); err != nil {
			return nil, errors.WithStack(err)
		}
		if ssctx == nil {
			if data.Medicine.MedicineName != supplyPlan.Medicine.MedicineName {
				match = bson.M{
					"supply_plan_id":     supplyPlan.SupplyPlanID,
					"info.medicine_name": data.Medicine.MedicineName,
				}
				if count, _ := tools.Database.Collection("supply_plan_medicine").CountDocuments(sctx, match); count > 0 {
					return nil, tools.BuildServerError(ctx, "medicine_duplicated")
				}
			}
		}

		// 校验补充方式 检查自动配给量
		data.EnvironmentID = supplyPlan.EnvironmentID
		data.SupplyPlanID = supplyPlan.SupplyPlanID
		data.ID = oid
		if ssctx == nil {
			err := checkAutoSupplySize(ctx, sctx, data)
			if err != nil {
				return nil, err
			}
		}

		var oldSupplyMedicine models.SupplyPlanMedicine
		err := tools.Database.Collection("supply_plan_medicine").FindOne(sctx, bson.M{"_id": oid}).Decode(&oldSupplyMedicine)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		match = bson.M{"_id": oid}
		update := bson.M{
			"$set": bson.M{
				"info": data.Medicine,
			},
		}

		one, err := tools.Database.Collection("supply_plan_medicine").UpdateOne(sctx, match, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 项目日志
		if one.ModifiedCount != 0 {
			err = insertSupplyPlanMedicineLog(ctx, sctx, oldSupplyMedicine.EnvironmentID, 2, oldSupplyMedicine.Medicine, data.Medicine, oid, oldSupplyMedicine.SupplyPlanID, "")
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		//供应计划轨迹
		var timeUnix = time.Now().Unix()
		//user, _ := ctx.Values().Get("user").(models.User)
		u, _ := ctx.Get("user")
		user := u.(models.User)

		var histories []models.History
		jsondata, _ := json.Marshal(&data.Medicine)
		var dataMap map[string]interface{}
		json.Unmarshal(jsondata, &dataMap)
		history := models.History{
			Key:  "history.supply-plan-medicine.update",
			OID:  oid,
			Data: dataMap,
			Time: time.Duration(timeUnix),
			UID:  user.ID,
			User: user.Name,
		}
		histories = append(histories, history)
		ctx.Set("HISTORY", histories)
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil

}

func checkAutoSupplySize(ctx *gin.Context, sctx mongo.SessionContext, data models.SupplyPlanMedicine) error {
	isBlindedDrugData, _ := tools.IsBlindedDrug(data.EnvironmentID, data.Medicine.MedicineName)

	var supplyPlanMedicines []models.SupplyPlanMedicine
	cursor, err := tools.Database.Collection("supply_plan_medicine").Find(sctx, bson.M{"supply_plan_id": data.SupplyPlanID, "_id": bson.M{"$ne": data.ID}})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &supplyPlanMedicines)
	if err != nil {
		return errors.WithStack(err)

	}
	for _, medicine := range supplyPlanMedicines {
		if medicine.Medicine.SupplyMode != 0 {
			if data.Medicine.SupplyMode != medicine.Medicine.SupplyMode && data.Medicine.SupplyMode != 0 {
				return tools.BuildServerError(ctx, "medicine.errorSupplyMode")
			}
		}
		if isBlindedDrugData {
			isBlindedDrug, _ := tools.IsBlindedDrug(data.EnvironmentID, medicine.Medicine.MedicineName)
			if isBlindedDrug {
				sort.Slice(data.Medicine.AutoSupplySize, func(i, j int) bool { return data.Medicine.AutoSupplySize[i] < data.Medicine.AutoSupplySize[j] })
				sort.Slice(medicine.Medicine.AutoSupplySize, func(i, j int) bool {
					return medicine.Medicine.AutoSupplySize[i] < medicine.Medicine.AutoSupplySize[j]
				})
				isEqual := slice.Equal(medicine.Medicine.AutoSupplySize, data.Medicine.AutoSupplySize)
				if !isEqual {
					return tools.BuildServerError(ctx, "medicine.errorAutoSupplyMode")
				}
			}
		}

	}
	return nil
}

func (s *SupplyPlanServices) DeleteSupplyPlanMedicine(ctx *gin.Context, id string, ssctx mongo.SessionContext) error {
	oid, _ := primitive.ObjectIDFromHex(id)

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		if ssctx != nil {
			sctx = ssctx
		}
		var supplyPlanMedicine models.SupplyPlanMedicine
		err := tools.Database.Collection("supply_plan_medicine").FindOne(sctx, bson.M{"_id": oid}).Decode(&supplyPlanMedicine)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res, err := tools.Database.Collection("supply_plan_medicine").DeleteOne(sctx, bson.M{"_id": oid})
		if err != nil || res.DeletedCount != 1 {
			return nil, errors.WithStack(err)
		}

		// 插入项目日志
		err = insertSupplyPlanMedicineLog(ctx, sctx, supplyPlanMedicine.EnvironmentID, 3, supplyPlanMedicine.Medicine, models.MedicinePlanInfo{}, oid, supplyPlanMedicine.SupplyPlanID, "")
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *SupplyPlanServices) GetSiteAndStorehouse(ctx *gin.Context, customerID string, projectID string, envID string) (map[string]interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}
	project := bson.M{
		"_id":    0,
		"id":     "$_id",
		"number": 1,

		"name": models.ProjectSiteNameBson(ctx),
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
	}
	siteCursor, err := tools.Database.Collection("project_site").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	lookup := bson.M{
		"from":         "storehouse",
		"localField":   "storehouse_id",
		"foreignField": "_id",
		"as":           "detail",
	}
	pipeline = mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: lookup}},
		{{Key: "$unwind", Value: "$detail"}},
		{{Key: "$project", Value: project}},
	}
	storehouseCursor, err := tools.Database.Collection("project_storehouse").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var siteData []map[string]interface{}
	var storehouseData []map[string]interface{}
	siteCursor.All(nil, &siteData)
	storehouseCursor.All(nil, &storehouseData)
	return map[string]interface{}{"site": siteData, "storehouse": storehouseData}, nil
}

func (s *SupplyPlanServices) GetMedicineList(ctx *gin.Context, customerID string, projectID string, envID string) ([]map[string]interface{}, error) {
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}
	project := bson.M{
		"_id":  0,
		"name": "$_id",
	}
	group := bson.M{
		"_id": "$configures.values.drugname",
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$unwind", Value: "$configures"}},
		{{Key: "$unwind", Value: "$configures.values"}},
		{{Key: "$group", Value: group}},
		{{Key: "$project", Value: project}},
	}
	cursor, err := tools.Database.Collection("drug_configure").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var data []map[string]interface{}
	cursor.All(nil, &data)
	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for index, medicine := range data {
		isBlind, exist := isBlindDrugMap[medicine["name"].(string)]
		if exist && isBlind {
			medicine["isBlind"] = true
		} else {
			medicine["isBlind"] = false
		}
		data[index] = medicine
	}
	return data, nil
}

func (s *SupplyPlanServices) GetMedicineConfigures(ctx *gin.Context, customerID string, projectID string, envID string, siteID string, keyword string) ([]map[string]interface{}, error) {
	var data []map[string]interface{}
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	siteOID, _ := primitive.ObjectIDFromHex(siteID)
	match := bson.M{
		"project_id":   projectOID,
		"env_id":       envOID,
		"customer_id":  customerOID,
		"info.site_id": siteOID,
	}
	lookup := bson.M{
		"from":         "supply_plan_medicine",
		"localField":   "_id",
		"foreignField": "supply_plan_id",
		"as":           "ret",
	}
	project := bson.M{
		"_id":  0,
		"info": "$ret.info",
	}
	retMatch := bson.M{
		"ret.info.medicine_name": keyword,
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: lookup}},
		{{Key: "$unwind", Value: "$ret"}},
		{{Key: "$match", Value: retMatch}},
		{{Key: "$project", Value: project}},
	}
	cursor, err := tools.Database.Collection("supply_plan").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor.All(nil, &data)
	return data, nil
}

func (s *SupplyPlanServices) GetSupplyPlan(ctx *gin.Context, siteID string, roleID string, envID string) (interface{}, error) {
	var data []map[string]interface{}
	OID, _ := primitive.ObjectIDFromHex(siteID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	//attributeOID, _ := primitive.ObjectIDFromHex(attributeID)
	// var attribute models.Attribute

	// err := tools.Database.Collection("attribute").FindOne(nil, bson.M{"_id": attributeOID}).Decode(&attribute)
	// if err != nil {
	// 	return nil, errors.WithStack(err)
	// }
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	match := bson.M{
		"_id": OID,
	}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{"from": "supply_plan_medicine", "localField": "supply_plan_id", "foreignField": "supply_plan_id", "as": "supply_plan_medicine"}}},
		{{Key: "$unwind", Value: bson.M{"path": "$supply_plan_medicine", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "medicine",
			"let": bson.M{
				"storehouse_id": bson.M{"$first": "$storehouse_id"},
				"medicine_name": "$supply_plan_medicine.info.medicine_name",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$storehouse_id", "$$storehouse_id"}}}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$name", "$$medicine_name"}}}},
				bson.M{"$match": bson.M{"storehouse_id": bson.M{"$ne": primitive.NilObjectID}}},
				bson.M{"$match": bson.M{"storehouse_id": bson.M{"$ne": nil}}},
				bson.M{"$match": bson.M{"status": 1}},
				bson.M{"$group": bson.M{"_id": "$name", "count": bson.M{"$sum": 1}}},
				bson.M{"$project": bson.M{
					"_id":   0,
					"name":  "$_id",
					"count": 1,
				}},
			},
			"as": "medicine",
		}}},
		{{"$lookup", bson.M{
			"from":         "project_storehouse",
			"localField":   "storehouse_id",
			"foreignField": "_id",
			"as":           "project_storehouse",
		}}},
		{{"$lookup", bson.M{
			"from":         "storehouse",
			"localField":   "project_storehouse.storehouse_id",
			"foreignField": "_id",
			"as":           "storehouse",
		}}},
		{{"$lookup", bson.M{
			"from": "medicine_others",
			"let": bson.M{
				"env_id":        "$env_id",
				"storehouse_id": bson.M{"$first": "$storehouse_id"},
				"medicine_name": "$supply_plan_medicine.info.medicine_name",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$storehouse_id", "$$storehouse_id"}}}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$name", "$$medicine_name"}}}},
				bson.M{"$match": bson.M{"status": 1}},
				bson.M{"$group": bson.M{"_id": "$name", "count": bson.M{"$sum": 1}}},
				bson.M{"$project": bson.M{
					"_id":   0,
					"name":  "$_id",
					"count": 1,
				}},
			},
			"as": "medicine_others",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$medicine", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$medicine_others", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$unwind", Value: bson.M{"path": "$storehouse", "preserveNullAndEmptyArrays": true}}},
		{{"$project", bson.M{
			"otherMedicine":  "$medicine_others.count",
			"medicine":       bson.M{"$ifNull": bson.A{"$medicine.count", 0}},
			"info":           "$supply_plan_medicine.info",
			"storehouseName": bson.M{"$ifNull": bson.A{"$storehouse.name", ""}},
		}}},
	}

	cursor, err := tools.Database.Collection("project_site").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cursor.All(nil, &data)
	if len(data) > 0 {
		rep := []map[string]interface{}{}
		for _, item := range data {
			if item["otherMedicine"] != nil {
				item["info"].(map[string]interface{})["count"] = item["otherMedicine"]
			} else {
				item["info"].(map[string]interface{})["count"] = item["medicine"]
			}
			item["info"].(map[string]interface{})["siteMedicine"] = 0
			item["info"].(map[string]interface{})["storehouseName"] = item["storehouseName"]
			name := item["info"].(map[string]interface{})["medicine_name"].(string)
			isBlindedDrug, _ := tools.IsBlindedDrug(envOID, name)
			// isOpenDrug, _ := tools.IsOpenDrug(attribute.EnvironmentID, attribute.CohortID, name)
			// isOtherDrug, _ := tools.IsOtherDrug(attribute.EnvironmentID, name)
			// if isBlindedRole && attribute.AttributeInfo.Blind && isOpenDrug && !isOtherDrug {
			if isBlindedDrug && isBlindedRole {
				saltName, salt := tools.Encrypt(name)
				item["info"].(map[string]interface{})["medicine_name"] = tools.BlindData
				item["info"].(map[string]interface{})["salt"] = salt
				item["info"].(map[string]interface{})["saltName"] = saltName

			}
			rep = append(rep, item["info"].(map[string]interface{}))
		}
		return rep, nil
	}
	return nil, nil
}

func (s *SupplyPlanServices) GetSupplyCountById(ctx *gin.Context) (int, error) {
	count := 0
	req := struct {
		EnvId              primitive.ObjectID         `json:"envId"`
		SupplyId           primitive.ObjectID         `json:"supplyId"`
		DrugNames          []string                   `json:"drugNames"`
		DrugNamesWithSalts []models.DrugNamesWithSalt `json:"drugNamesWithSalts"`
		Mode               int                        `json:"mode"`
	}{}
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		return count, errors.WithStack(err)
	}

	allDrugMap, err := tools.AllDrugMap(req.EnvId)
	if err != nil {
		return count, errors.WithStack(err)
	}

	for i, drugName := range req.DrugNames {
		_, ok := allDrugMap[drugName]
		if !ok && len(req.DrugNamesWithSalts) > 0 && req.DrugNamesWithSalts[i].SaltName != "" && req.DrugNamesWithSalts[i].Salt != "" {
			drugName = tools.Decrypt(req.DrugNamesWithSalts[i].SaltName, req.DrugNamesWithSalts[i].Salt)
		}
		req.DrugNames[i] = drugName
	}

	//查询这个供应计划下，这个药物
	var supplyPlanMedicines []models.SupplyPlanMedicine
	supplyFilter := bson.M{
		"env_id":         req.EnvId,
		"supply_plan_id": req.SupplyId,
	}
	if len(req.DrugNames) > 0 {
		supplyFilter["info.medicine_name"] = bson.M{"$in": req.DrugNames}
	}
	cursor, err := tools.Database.Collection("supply_plan_medicine").Find(nil, supplyFilter)
	if err != nil {
		return count, errors.WithStack(err)
	}
	err = cursor.All(nil, &supplyPlanMedicines)
	if err != nil {
		return count, errors.WithStack(err)
	}

	// 2：再供应量 3：最大缓冲量
	for _, medicine := range supplyPlanMedicines {
		addValue := 0
		if req.Mode == 2 { //2：再供应量
			addValue = medicine.Medicine.SecondSupply
		} else if req.Mode == 3 {
			addValue = medicine.Medicine.Buffer
		}
		count = count + addValue
	}

	return count, nil
}

func (s *SupplyPlanServices) CheckSite(ctx *gin.Context) (interface{}, error) {
	req := struct {
		ID      primitive.ObjectID   `json:"id"`
		EnvId   primitive.ObjectID   `json:"envId"`
		Status  int                  `json:"status"`
		SiteIds []primitive.ObjectID `json:"siteIds"`
	}{}
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	statusUnBlind := false
	if req.Status == 0 {
		count, err := tools.Database.Collection("project_site").CountDocuments(nil, bson.M{"supply_plan_id": req.ID})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if count > 0 {
			statusUnBlind = true
		}
	}
	siteUnBind := false
	if req.SiteIds != nil && len(req.SiteIds) > 0 {
		count, err := tools.Database.Collection("project_site").CountDocuments(nil, bson.M{
			"env_id":         req.EnvId,
			"supply_plan_id": req.ID,
			"_id":            bson.M{"$nin": req.SiteIds},
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if count > 0 {
			siteUnBind = true
		}
	}
	resp := struct {
		StatusUnBlind bool `json:"statusUnBlind"`
		SiteUnBind    bool `json:"siteUnBind"`
	}{
		StatusUnBlind: statusUnBlind,
		SiteUnBind:    siteUnBind,
	}
	return resp, nil
}

func (s *SupplyPlanServices) GetApplicableSiteSupplyPlan(ctx *gin.Context) (interface{}, error) {
	siteId := ctx.Query("siteId")
	siteOID, err := primitive.ObjectIDFromHex(siteId)
	envId := ctx.Query("envId")
	envOID, err := primitive.ObjectIDFromHex(envId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	type SupplyPlan struct {
		ID   primitive.ObjectID `json:"id"`
		Name string             `json:"name"`
	}
	//查询可选用的供应计划
	optionalSupplyPlan := make([]SupplyPlan, 0)
	cursor, err := tools.Database.Collection("supply_plan").Find(nil, bson.M{"info.status": 1, "env_id": envOID, "$or": bson.A{
		bson.M{"info.all_site": true}, bson.M{"info.site_ids": siteOID},
	}}, &options.FindOptions{
		Projection: bson.M{"_id": 0, "id": "$_id", "name": "$info.name"},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &optionalSupplyPlan)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if siteOID.IsZero() {
		return struct {
			OptionalSupplyPlan []SupplyPlan `json:"optionalSupplyPlan"`
		}{
			OptionalSupplyPlan: optionalSupplyPlan,
		}, nil
	}

	bindProjectSite := models.ProjectSite{}
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": siteOID}).Decode(&bindProjectSite)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	//查询绑定的供应计划
	if !bindProjectSite.SupplyPlanID.IsZero() {
		bindSupplyPlan := SupplyPlan{}
		err = tools.Database.Collection("supply_plan").FindOne(nil, bson.M{"_id": bindProjectSite.SupplyPlanID}, &options.FindOneOptions{
			Projection: bson.M{"_id": 0, "id": "$_id", "name": "$info.name"},
		}).Decode(&bindSupplyPlan)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return struct {
			BindSupplyPlan     SupplyPlan   `json:"bindSupplyPlan"`
			OptionalSupplyPlan []SupplyPlan `json:"optionalSupplyPlan"`
		}{
			BindSupplyPlan:     bindSupplyPlan,
			OptionalSupplyPlan: optionalSupplyPlan,
		}, nil
	}
	return struct {
		OptionalSupplyPlan []SupplyPlan `json:"optionalSupplyPlan"`
	}{
		OptionalSupplyPlan: optionalSupplyPlan,
	}, nil
}

func (s *SupplyPlanServices) BatchSupplyPlanMedicines(ctx *gin.Context, deleteIds string, supplyPlanMedicines []models.SupplyPlanMedicine) ([]models.SupplyPlanMedicineCheck, error) {

	var supplyPlanChecks []models.SupplyPlanMedicineCheck

	autoSupplySizeErr := ""

	if supplyPlanMedicines != nil {
		isBlindDrugMap, err := tools.IsBlindDrugMap(supplyPlanMedicines[0].EnvironmentID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//判断药物是否是盲态药物
		isBindMedicine := false
		for i := 0; i < len(supplyPlanMedicines); i++ {
			isBlind, ok := isBlindDrugMap[supplyPlanMedicines[i].Medicine.MedicineName]
			if ok && isBlind {
				isBindMedicine = true
				break
			}
		}

		for i := 0; i < len(supplyPlanMedicines); i++ {
			data := supplyPlanMedicines[i]
			var supplyPlanCheck models.SupplyPlanMedicineCheck
			supplyPlanCheck.Index = i

			for j := 0; j < len(supplyPlanMedicines); j++ {
				medicine := supplyPlanMedicines[j]
				if i != j {
					if data.Medicine.MedicineName == medicine.Medicine.MedicineName {
						supplyPlanCheck.MedicineName = locales.Tr(ctx, "medicine_duplicated")
					}

					//判断是否有盲态药物，如果有盲态药物，限制补充方式一致，如果全部是开放药物的话，不限制
					// 1.同一个plan中，添加有盲态药物，补充方式为全品、全品+1、单品+1，就需要保证当前plan中的补充方式相同。
					// 2.同一个plan中，全部为开放药物，不限制补充方式的配置。
					if isBindMedicine {
						if data.Medicine.SupplyMode != medicine.Medicine.SupplyMode && data.Medicine.SupplyMode != 0 && medicine.Medicine.SupplyMode != 0 {
							supplyPlanCheck.SupplyMode = locales.Tr(ctx, "medicine.errorSupplyModeHint")
						}
					}

					isBlindedDrugData, _ := tools.IsBlindedDrug(data.EnvironmentID, data.Medicine.MedicineName)
					if isBlindedDrugData {
						isBlindedDrug, _ := tools.IsBlindedDrug(data.EnvironmentID, medicine.Medicine.MedicineName)
						if isBlindedDrug {
							sort.Slice(data.Medicine.AutoSupplySize, func(i, j int) bool { return data.Medicine.AutoSupplySize[i] < data.Medicine.AutoSupplySize[j] })
							sort.Slice(medicine.Medicine.AutoSupplySize, func(i, j int) bool {
								return medicine.Medicine.AutoSupplySize[i] < medicine.Medicine.AutoSupplySize[j]
							})
							isEqual := slice.Equal(medicine.Medicine.AutoSupplySize, data.Medicine.AutoSupplySize)
							if !isEqual {
								supplyPlanCheck.AutoSupplySize = locales.Tr(ctx, "medicine.errorAutoSupplyModeHint")
								autoSupplySizeErr = locales.Tr(ctx, "medicine.errorAutoSupplyModeHint")
							}
						}
					}

				}
			}
			supplyPlanChecks = append(supplyPlanChecks, supplyPlanCheck)
			//if len(supplyPlanCheck.MedicineName) != 0 || len(supplyPlanCheck.AutoSupplySize) != 0 || len(supplyPlanCheck.SupplyMode) != 0 {
			//	supplyPlanChecks = append(supplyPlanChecks, supplyPlanCheck)
			//}
		}

		if len(autoSupplySizeErr) != 0 {
			for _, check := range supplyPlanChecks {
				check.AutoSupplySize = autoSupplySizeErr
			}
		}

		if len(supplyPlanChecks) != 0 {
			var checks []models.SupplyPlanMedicineCheck
			for _, check := range supplyPlanChecks {
				if len(check.MedicineName) != 0 || len(check.AutoSupplySize) != 0 || len(check.SupplyMode) != 0 {
					checks = append(checks, check)
				}
			}
			if len(checks) != 0 {
				return checks, nil
			}
		}

		callback := func(sctx mongo.SessionContext) (interface{}, error) {
			//删除已经删掉的数据
			if len(deleteIds) != 0 {
				deleteIdArr := strings.Split(deleteIds, ",")
				for _, deleteId := range deleteIdArr {
					err := s.DeleteSupplyPlanMedicine(ctx, deleteId, sctx)
					if err != nil {
						return supplyPlanChecks, err
					}
				}
			}

			for _, data := range supplyPlanMedicines {
				if data.ID != primitive.NilObjectID {
					err := s.UpdateSupplyPlanMedicine(ctx, data.ID.Hex(), data, sctx)
					if err != nil {
						return supplyPlanChecks, err
					}
				} else {
					err := s.AddSupplyPlanMedicine(ctx, data, sctx)
					if err != nil {
						return supplyPlanChecks, err
					}
				}
			}
			return nil, nil
		}
		err = tools.Transaction(callback)
		if err != nil {
			return supplyPlanChecks, err
		}
	}
	return nil, nil
}
