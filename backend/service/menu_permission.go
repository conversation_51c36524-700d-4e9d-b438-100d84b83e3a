package service

import (
	"clinflash-irt/data"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/algorithm"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type MenuPermissionService struct{}

func (s *MenuPermissionService) All(ctx *gin.Context) (interface{}, error) {
	return data.MenuPermissions, nil
}

// 导出Excel时, Sheet按角色名排序
type comparator struct{}

func (c *comparator) Compare(a, b any) int {
	a0, _ := a.(tools.Sheet[string])
	b0, _ := b.(tools.Sheet[string])
	return strings.Compare(a0.Name, b0.Name)
}
func (s *MenuPermissionService) Export(ctx *gin.Context, projectId string) error {
	poid, err := primitive.ObjectIDFromHex(projectId)
	if err != nil {
		return errors.WithStack(err)
	}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(ctx, bson.M{"_id": poid}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}
	agg := bson.A{
		bson.D{{Key: "$match", Value: bson.D{{Key: "project_id", Value: poid}}}},
		bson.D{
			{Key: "$project",
				Value: bson.D{
					{Key: "_id", Value: 0},
					{Key: "name", Value: 1},
					{Key: "permissions", Value: 1},
				},
			},
		},
	}
	var permissions []models.ProjectRolePermission
	cursor, err := tools.Database.Collection("project_role_permission").Aggregate(ctx, agg)
	if err != nil {
		return errors.WithStack(err)
	}
	if err = cursor.All(ctx, &permissions); err != nil {
		return errors.WithStack(err)
	}

	positionMap, reverseIndex := reverseIndex(project.ProjectInfo.ResearchAttribute)
	// change reverseIndex to map[string]string
	permission2Menu := make(map[string]string)
	for permission, menu := range reverseIndex {
		permission2Menu[permission] = strings.Join(menu, " > ")
	}

	role := make(map[string]map[string][]string) // role -> menu -> permissions
	for _, permission := range permissions {
		menu := make(map[string][]string)
		for _, p := range permission.Permissions {
			if _, ok := permission2Menu[p]; ok {
				if _, ok := menu[permission2Menu[p]]; !ok {
					menu[permission2Menu[p]] = make([]string, 0)
				}
				menu[permission2Menu[p]] = append(menu[permission2Menu[p]], p)
			}
		}
		role[permission.Name] = menu
	}

	r := make([]tools.Sheet[string], len(role))

	roleIndex := 0
	for roleName, menus := range role {
		// change menus from map[string][]string to [][]string
		tmpM := make([][]string, len(positionMap))
		for _, permissions := range menus {
			if menuCascade, ok := reverseIndex[permissions[0]]; ok {
				lowestMenu := menuCascade[len(menuCascade)-1]
				index := positionMap[lowestMenu]
				tmpM[index] = permissions
			}
		}
		// compact the sparse array m to a dense array menu
		m := make([][]string, len(menus))
		mIndex := 0
		for _, item := range tmpM {
			if len(item) != 0 {
				m[mIndex] = item
				mIndex++
			}
		}
		rows := make([][]string, 0)

		// 表头
		header := make([]string, 0)
		if locales.Lang(ctx) == "zh" {
			header = append(header, "角色")
			header = append(header, "菜单")
			header = append(header, "操作")
		} else {
			header = append(header, "Role")
			header = append(header, "Menu")
			header = append(header, "Operation")
		}
		rows = append(rows, header)

		// 第一行
		firstRow := make([]string, 0)
		// 角色名
		firstRow = append(firstRow, roleName)
		// 层叠菜单
		menuCascade := make([]string, 0)
		if len(m) == 0 || m[0] == nil || len(m[0]) == 0 || m[0][0] == "" {
			rows = append(rows, firstRow)
			r[roleIndex] = tools.Sheet[string]{Name: roleName, Rows: rows}
			roleIndex++
			continue
		}
		for _, code := range reverseIndex[m[0][0]] {
			menuCascade = append(menuCascade, locales.Tr(ctx, code))
		}
		firstRow = append(firstRow, strings.Join(menuCascade, "-"))
		// 权限
		p := make([]string, 0)
		for _, permission := range m[0] {
			p = append(p, locales.Tr(ctx, permission))
		}
		firstRow = append(firstRow, strings.Join(p, "、"))

		rows = append(rows, firstRow)

		// 其他行
		for _, permissions := range m[1:] {
			row := make([]string, 0)
			// 空串占位
			row = append(row, "")
			// 层叠菜单
			menuCascade := make([]string, 0)
			for _, code := range reverseIndex[permissions[0]] {
				menuCascade = append(menuCascade, locales.Tr(ctx, code))
			}
			row = append(row, strings.Join(menuCascade, "-"))
			// 权限
			p := make([]string, 0)
			for _, permission := range permissions {
				p = append(p, locales.Tr(ctx, permission))
			}
			row = append(row, strings.Join(p, "、"))
			rows = append(rows, row)
		}
		r[roleIndex] = tools.Sheet[string]{Name: roleName, Rows: rows}
		roleIndex++
	}

	// 按角色名排序
	algorithm.MergeSort(r, &comparator{})
	bytes, err := tools.ExcelBytes(r)
	if err != nil {
		return errors.WithStack(err)
	}
	timeZone, err := tools.GetTimeZone(poid)
	if err != nil {
		return errors.WithStack(err)
	}
	filename := project.ProjectInfo.Number + "-Project Permission-" + time.Now().UTC().Add(time.Hour*time.Duration(timeZone)).Format("20060102") + ".xlsx"
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(filename)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", bytes)
	return nil
}

func reverseIndex(research int) (map[string]int, map[string][]string) {
	index := make(map[string][]string)
	position := reverseIndexRecur(research, index, data.MenuPermissions, []string{})
	// remove redundant position elem

	positionMap := make(map[string]int)
	for i, p := range position {
		positionMap[p] = i
	}
	return positionMap, index
}

func reverseIndexRecur(research int, index map[string][]string, m []data.MenuPermission, parent []string) []string {
	position := make([]string, 0)
	for _, item := range m {
		//if item.System == 1 {
		//	continue
		//}
		i := sort.SearchInts(item.ResearchAttribute, research)
		if i < len(item.ResearchAttribute) && item.ResearchAttribute[i] == research {
			if item.Children != nil && len(item.Children) > 0 {
				tmp := make([]string, len(parent))
				copy(tmp, parent)
				tmp = append(tmp, item.Text)
				if len(item.Permissions) > 0{
					if !contains(position, item.Text) {
						position = append(position, item.Text)
					}
					for _, permission := range item.Permissions {
						index[permission] = tmp
					}
				}
				subsequent := reverseIndexRecur(research, index, item.Children, tmp)
				for _, p := range subsequent {
					if !contains(position, p) {
						position = append(position, p)
					}
				}
			} else {
				tmp := make([]string, len(parent))
				copy(tmp, parent)
				if !contains(position, item.Text) {
					position = append(position, item.Text)
				}
				tmp = append(tmp, item.Text)
				for _, permission := range item.Permissions {
					index[permission] = tmp
				}
			}
		}
	}
	return position
}




func reverseIndexPermissions(template int) (map[string]int, map[string][]string) {
	index := make(map[string][]string)
	position := reverseIndexRecurPermissions(template,index, data.MenuPermissions, []string{})
	// remove redundant position elem

	positionMap := make(map[string]int)
	for i, p := range position {
		positionMap[p] = i
	}
	return positionMap, index
}

func reverseIndexRecurPermissions(template int, index map[string][]string, m []data.MenuPermission, parent []string) []string {
	position := make([]string, 0)
	for _, item := range m {
		research := 1
		if template == 1 {
			research = 0
		}
		i := sort.SearchInts(item.ResearchAttribute, research)
		if i < len(item.ResearchAttribute) && item.ResearchAttribute[i] == research {
			if item.Children != nil && len(item.Children) > 0 {
				tmp := make([]string, len(parent))
				copy(tmp, parent)
				tmp = append(tmp, item.Text)
				if len(item.Permissions) > 0 {
					if !contains(position, item.Text) {
						position = append(position, item.Text)
					}
					for _, permission := range item.Permissions {
						index[permission] = tmp
					}
				}
				subsequent := reverseIndexRecurPermissions(template, index, item.Children, tmp)
				for _, p := range subsequent {
					if !contains(position, p) {
						position = append(position, p)
					}
				}
			} else {
				tmp := make([]string, len(parent))
				copy(tmp, parent)
				if !contains(position, item.Text) {
					position = append(position, item.Text)
				}
				tmp = append(tmp, item.Text)
				for _, permission := range item.Permissions {
					index[permission] = tmp
				}
			}
		}
	}
	return position
}


func contains[E comparable](s []E, e E) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}
