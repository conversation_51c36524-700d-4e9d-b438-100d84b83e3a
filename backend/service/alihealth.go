package service

import (
	"bytes"
	"clinflash-irt/data"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin/binding"
	"log"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	//"github.com/kataras/iris/v12"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	signFail       = map[string]interface{}{"success": false, "msgInfo": "Illegal request", "msgCode": "sign-check-failure"}
	projectUnExist = map[string]interface{}{"msgCode": "408", "msgInfo": "项目不存在", "success": true}
)

// AliTraceability interface
//type AliTraceability interface {
//	POST(ctx *gin.Context, data map[string]interface{}) (map[string]interface{},error)
//	Transfer(ctx *gin.Context) (map[string]interface{},error)
//	Bound(ctx *gin.Context) (map[string]interface{},error)
//	InStockStatus(ctx *gin.Context) (map[string]interface{},error)
//	CodeStatus(ctx *gin.Context) (map[string]interface{},error)
//	ProjectUpdate(ctx *gin.Context) (map[string]interface{},error)
//	GetCode(ctx *gin.Context) (map[string]interface{},error)
//	GetPatient(ctx *gin.Context) (map[string]interface{},error)
//	RefillMedicine(ctx *gin.Context) (map[string]interface{},error)
//}

type AliTraceabilityService struct {
	dispensingService  DispensingService
	projectSiteService ProjectSiteService
}

// NewAliTraceabilityService ...
//func NewAliTraceabilityService() AliTraceability {
//	return &aliTraceabilityService{}
//}

func (s *AliTraceabilityService) POST(ctx *gin.Context, data map[string]interface{}) (map[string]interface{}, error) {
	method := ctx.DefaultQuery("method", "")

	// sign 验证签名
	verify := sign(ctx, data)
	if !verify {
		return signFail, nil
	}

	var res map[string]interface{}
	switch method {
	case "qimen.alibaba.alihealth.drugcode.center.transferdata":
		res, _ = s.Transfer(ctx)
	case "qimen.alibaba.alihealth.drugcode.center.hospital.bound.centersys.syn.attr.without.review":
		res, err := s.Bound(ctx)
		return res, err
	case "qimen.alibaba.alihealth.drugcode.center.query.code.instockstatus":
		res, err := s.InStockStatus(ctx)
		return res, err
	case "qimen.alibaba.alihealth.drugcode.center.syn.codestatus":
		res, err := s.CodeStatus(ctx)
		return res, err
	case "qimen.alibaba.alihealth.drugcode.center.patient.getcode":
		res, err := s.GetCode(ctx)
		return res, err
	case "qimen.alibaba.alihealth.drugcode.center.code.getpatient":
		res, err := s.GetPatient(ctx)
		return res, err
	case "qimen.alibaba.alihealth.drugcode.center.project.update":
		res, err := s.ProjectUpdate(ctx)
		return res, err
	case "qimen.alibaba.alihealth.drugcode.center.notify.refill.medicine":
		res, err := s.RefillMedicine(ctx)
		return res, err
	case "qimen.alibaba.alihealth.drugcode.center.notify.back.in":
		res, err := s.BackIn(ctx)
		return res, err
	}
	return res, nil

}

// Transfer  给中央随机化系统传输数据：
func (s *AliTraceabilityService) Transfer(ctx *gin.Context) (map[string]interface{}, error) {
	var data map[string]interface{}
	//ctx.ReadJSON(&data)
	//ctx.BindJSON(&data)
	ctx.ShouldBindBodyWith(&data, binding.JSON)

	//fmt.Println(data)
	// 签名校验
	verify := sign(ctx, data)
	if !verify {
		return signFail, nil
	}

	sign_value := ctx.Query("signValue")
	project_id := ctx.Query("trialProjectId")
	// 字符串 转数组
	var encData models.EncData
	for key, value := range data {
		if key == "encData" {
			byt := []byte(value.(string))
			ccc := json.NewDecoder(bytes.NewReader(byt))
			ccc.Decode(&encData)
		}
	}

	// 获取当前项目信息
	var bound models.Bound
	err := tools.Database.Collection("ali_request").FindOne(nil, bson.M{"project_id": project_id, "hospital_id": encData.FromLCnameID}).Decode(&bound)
	if err != nil {
		log.Print("【Bound】 项目未开启对接阿里健康码上放心  ...")
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}
	if bound.Operation == "HOSPITAL_CENTER_UNBOUND" {
		return map[string]interface{}{"msgCode": "401", "msgInfo": fmt.Sprintf("项⽬[%s]未绑定或已解绑！", bound.ProjectName), "success": true}, nil
	}
	if bound.LocalProjectId == primitive.NilObjectID {
		return map[string]interface{}{"msgCode": "408", "msgInfo": "项⽬不存在", "success": true}, nil //项⽬未注册
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": bound.LocalProjectId}).Decode(&project)
	if err != nil {
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}

	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": bound.LocalSiteId}).Decode(&projectSite)
	if err != nil {
		return map[string]interface{}{"msgCode": "405", "msgInfo": fmt.Sprintf("项⽬[%s]中临床研究机构[%s]未绑定或已解绑！", bound.ProjectName, encData.FromLCnameID), "success": true}, nil

	}

	total := 0
	// 重复个数

	hasCount := 0
	var existCode []string

	//验证研究产品号是否重复
	filter := bson.M{"env_id": projectSite.EnvironmentID}

	//利用map集合来验证重复
	mapHasMedicines := make(map[string]bool)
	//var hasMedicines []models.MedicineCode
	//cursor, _ := tools.Database.Collection("medicine").Find(nil, filter)
	//cursor.All(nil, &hasMedicines)
	//for _, hasMedicine := range hasMedicines {
	//	mapHasMedicines[hasMedicine.Number] = true
	//}

	var hasMedicines []models.Medicine
	cursor, _ := tools.Database.Collection("medicine").Find(nil, filter)
	cursor.All(nil, &hasMedicines)
	for _, hasMedicine := range hasMedicines {
		mapHasMedicines[hasMedicine.Number] = true
	}

	//pipeline := mongo.Pipeline{
	//	{{Key: "$match", Value: bson.M{"project_id": project.ID}}},
	//	{{Key: "$unwind", Value: "$configures"}},
	//	{{Key: "$unwind", Value: "$configures.values"}},
	//	{{Key: "$group", Value: bson.M{"_id": bson.M{
	//		"drug_name": "$configures.values.drugname",
	//		"drug_spec": "$configures.values.drug_spec",
	//		"pkg_spec":  "$configures.values.pkg_spec",
	//	}}}},
	//}
	//cursor, _ = tools.Database.Collection("drug_configure").Aggregate(nil, pipeline)
	//var drugConfigure []map[string]interface{}
	//cursor.All(nil, &drugConfigure)

	// 插入研究产品码入库

	projectOID := bound.LocalProjectId

	var transferData models.TransferData
	transferData.SignValue = sign_value
	transferData.TrialProjectId = projectOID
	transferData.EncData = encData

	codeAttributeMap := map[string]interface{}{}
	for _, item := range bound.CodeAttributeInfo {
		codeAttributeMap[item.SecondaryAttributeNo] = item.SecondaryAttrDesc
	}
	if encData.FromLCnameID != bound.HospitalId {
		return map[string]interface{}{"msgCode": "405", "msgInfo": fmt.Sprintf("项目【%s】中临床机构不存在", project.Name), "success": true}, nil //项⽬未注册

	}

	if count, _ := tools.Database.Collection("project_site").CountDocuments(nil, bson.M{"_id": bound.LocalSiteId, "deleted": 2}); count == 0 {
		return map[string]interface{}{"msgCode": "406", "msgInfo": fmt.Sprintf("项目【%s】中临床机构【%s】已停用", project.Name, bound.HospitalName), "success": true}, nil //项⽬未注册
	}
	var medicineCode []interface{}
	for _, drugAndCode := range encData.DrugAndCode {
		//校验研究产品是否存在
		//hasMedicine := false
		//for _, element := range drugConfigure {
		//	key := element["_id"].(map[string]interface{})
		//	if drugAndCode.DrugName == key["drug_name"] && drugAndCode.PreSpec == key["drug_spec"] && drugAndCode.PkgSpec == key["pkg_spec"] {
		//		hasMedicine = true
		//	}
		//}
		//if !hasMedicine {
		//	return map[string]interface{}{"msgCode": "504", "msgInfo": "药物不存在", "success": true}, nil
		//}
		for _, batch := range drugAndCode.Batchs {
			if _, err := time.Parse("2006-01-02 15:04:05", batch.ExprieDate); err != nil {
				return map[string]interface{}{"msgCode": "502", "msgInfo": "药物到期⽇期格式不对，不能⼊库！", "success": true}, nil //
			}
			if batch.ExprieDate < time.Now().Format("2006-01-02 15:04:05") {
				return map[string]interface{}{"msgCode": "503", "msgInfo": "药物到期⽇期已过期，不能⼊库！", "success": true}, nil //
			}

			// 解析时间字符串
			expireDate, _ := time.Parse("2006-01-02 15:04:05", batch.ExprieDate)
			// 格式化为去掉秒的字符串
			formattedExpireDate := expireDate.Format("2006-01-02")

			for _, code := range batch.Codes {
				if _, ok := codeAttributeMap[code.CodeAttribute]; !ok {
					return map[string]interface{}{"msgCode": "409", "msgInfo": fmt.Sprintf(": 项⽬[%s]研究产品属性[%s]未绑定或绑定关系⽆效！", project.Name, code.CodeAttribute), "success": true}, nil //
				}

				total++
				if _, ok := mapHasMedicines[code.Code]; ok {
					hasCount++
					existCode = append(existCode, code.Code)
				} else {
					medicineCode = append(medicineCode, models.Medicine{
						ID:             primitive.NewObjectID(),
						CustomerID:     project.CustomerID,
						ProjectID:      project.ID,
						EnvironmentID:  projectSite.EnvironmentID,
						Name:           drugAndCode.DrugName,
						Number:         code.Code,
						BatchNumber:    batch.BatchNo,
						SiteID:         bound.LocalSiteId,
						ExpirationDate: formattedExpireDate,
						Status:         1,
						SerialNumber:   code.Code,
						//ProduceDate:    batch.ProduceDate,
						//CodeAttribute:  code.CodeAttribute,
						//PrepnType:      drugAndCode.PrepnType,
						//PreSpec:        drugAndCode.PreSpec,
						//PkgSpec:        drugAndCode.PkgSpec,
						//OperDate:       encData.OperDate,
						//FromLCname:     encData.FromLCname,
						//FromLCnameID:   encData.FromLCnameID,
						//ToIWRSname:     encData.ToIWRSname,
						//ToIWRSID:       encData.ToIWRSID,
						Spec:         drugAndCode.PreSpec,
						TransferData: transferData,
					})
				}

			}

		}
	}
	if len(medicineCode) != 0 {
		if _, err := tools.Database.Collection("medicine").InsertMany(nil, medicineCode); err != nil {
			//return errors.WithStack(err)
			return map[string]interface{}{
				"error": errors.WithStack(err).Error(),
			}, err
		}
	}

	if hasCount == 0 {
		return map[string]interface{}{
			"msgCode": "200",
			"msgInfo": "调用成功",
			"success": true,
		}, nil

		//return gin.Map{"msgCode": "200", "msgInfo": "调用成功", "success": true}
	} else {
		msgInfo := fmt.Sprintf("本次共收到{%d}个码，其中重复{%d}个码，重复码为：{%s}，成功⼊库{%d}个码。", total, hasCount, strings.Join(existCode, ","), total-hasCount)
		return map[string]interface{}{
			"msgCode": "500",
			"msgInfo": msgInfo,
			"success": true,
		}, nil

		//return gin.Map{"msgCode": "500", "msgInfo": msgInfo, "success": true}

	}

}

// Bound ..同步⽅式·绑定/解绑+推送药物属性
func (s *AliTraceabilityService) Bound(ctx *gin.Context) (map[string]interface{}, error) {
	var codeInfo map[string]interface{}
	//ctx.ReadJSON(&codeInfo)
	//ctx.BindJSON(&codeInfo)
	ctx.ShouldBindBodyWith(&codeInfo, binding.JSON)
	// 签名校验
	verify := sign(ctx, codeInfo)
	if !verify {
		return signFail, nil
	}
	//ctx.QueryArray("codeAttributeInfo")

	var codeAttributeInfo []models.CodeAttributeInfo
	for key, value := range codeInfo {
		if key == "codeAttributeInfo" {
			byt := []byte(value.(string))
			ccc := json.NewDecoder(bytes.NewReader(byt))
			ccc.Decode(&codeAttributeInfo)
		}
	}

	data := models.Bound{
		ProjectId:         ctx.Query("projectId"),
		ProjectNo:         ctx.Query("projectNo"),
		ProjectName:       ctx.Query("projectName"),
		HospitalId:        ctx.Query("hospitalId"),
		HospitalName:      ctx.Query("hospitalName"),
		Operation:         ctx.Query("operation"),
		CodeAttributeInfo: codeAttributeInfo,
	}

	attributes := make([]models.Attribute, 0)
	cursor, err := tools.Database.Collection("attribute").Find(nil, bson.M{"info.connect_ali": true, "info.ali_project_no": data.ProjectNo})
	if err != nil {
		log.Print("【Bound】 项目未开启对接阿里健康码上放心  ...")
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}
	err = cursor.All(nil, &attributes)
	if err != nil {
		log.Print("【Bound】 项目未开启对接阿里健康码上放心  ...")
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}

	if len(attributes) == 0 {
		log.Print("【Bound】 项目未开启对接阿里健康码上放心  ...")
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}

	// 查询是否存在项目是否对接阿里
	//TODO 条件从阿里取项目名称中判断是哪个环境
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": attributes[0].ProjectID}).Decode(&project)
	if err != nil {
		log.Print("【Bound】 项目未开启对接阿里健康码上放心  ...")
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}

	//if project.ConnectAli == 0 || project.ConnectAli == 2 {
	//	log.Print("【Bound】 项目未开启对接阿里健康码上放心  ...")
	//	return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	//}

	//status := 1
	if data.Operation == "HOSPITAL_CENTER_BOUND" {
		var aliRequest models.Bound
		err = tools.Database.Collection("ali_request").FindOne(
			nil,
			bson.M{"project_id": data.ProjectId, "project_no": data.ProjectNo, "hospital_id": data.HospitalId, "operation": data.Operation},
		).Decode(&aliRequest)
		if err == nil {
			return map[string]interface{}{"msgCode": "407", "msgInfo": "绑定操作，项⽬已经绑定", "success": true, "status": 5}, nil //绑定操作，项⽬已经绑定
		}
		//if project.Bound == 1 {
		//	return map[string]interface{}{"msgCode": "407", "msgInfo": "绑定操作，项⽬已经绑定", "success": true, "status": 5}, nil //绑定操作，项⽬已经绑定
		//}
	} else {
		//status = 2
		var aliRequest models.Bound
		err = tools.Database.Collection("ali_request").FindOne(
			nil,
			bson.M{"project_id": data.ProjectId, "project_no": data.ProjectNo, "hospital_id": data.HospitalId, "operation": data.Operation},
		).Decode(&aliRequest)
		if err == nil {
			return map[string]interface{}{"msgCode": "409", "msgInfo": "解绑操作，项⽬已经解绑", "success": true, "status": 5}, nil //解绑操作，项⽬已经解绑
		}
		//if project.Bound == 2 {
		//	return map[string]interface{}{"msgCode": "409", "msgInfo": "解绑操作，项⽬已经解绑", "success": true, "status": 5}, nil //解绑操作，项⽬已经解绑
		//}
	}

	// 检查研究产品配置是否与推送的数据符合  (中央随机化结合研究产品随机方案，接收平台推送的研究产品属性)
	//medicineNameList := bson.A{}
	//for _, info := range codeAttributeInfo {
	//	medicineNameList = append(medicineNameList, info.SecondaryAttributeName)
	//}
	//
	//pipeline := mongo.Pipeline{
	//	{{Key: "$match", Value: bson.M{"project_id": project.ID}}},
	//	{{Key: "$unwind", Value: "$configures"}},
	//	{{Key: "$unwind", Value: "$configures.values"}},
	//	{{Key: "$group", Value: bson.M{"_id": bson.M{"drug_name":"$configures.values.drugname","spec":"$configures.values.drug_spec"}}}},
	//}
	//cursor, _ := tools.Database.Collection("drug_configure").Aggregate(nil, pipeline)
	//var drugConfigure []map[string]interface{}
	//cursor.All(nil, &drugConfigure)
	//
	//if len(drugConfigure) != len(medicineNameList) {
	//	return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5} //其他异常
	//} else {
	//	pipeline = append(pipeline, bson.D{{Key: "$match", Value: bson.M{"_id": bson.M{"$in": medicineNameList}}}})
	//	cursor, _ := tools.Database.Collection("drug_configure").Aggregate(nil, pipeline)
	//	var drugConfigure []map[string]interface{}
	//	cursor.All(nil, &drugConfigure)
	//	if len(drugConfigure) != len(medicineNameList) {
	//		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5} //其他异常
	//	}
	//}

	// 根据研究机构ID 当前项目下是否 启用 该研究机构（中心）
	//if count, _ := tools.Database.Collection("project_site").CountDocuments(nil, bson.M{"project_id": project.ID, "deleted": 2}); count < 1 {
	//	return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5} //其他异常
	//}

	var projectSite models.ProjectSite
	projectSite.ID = primitive.NewObjectID()
	projectSite.CustomerID = project.CustomerID
	projectSite.ProjectID = project.ID
	//项目内环境
	str := data.ProjectName
	result := strings.SplitN(str, "-", 2)
	if len(result) > 1 {
		fmt.Println(result[1])
	}
	if len(project.Environments) > 0 {
		for _, env := range project.Environments {
			if env.Name == result[1] {
				projectSite.EnvironmentID = env.ID
			}
		}
	} else {
		log.Print("【Bound】 项目未开启对接阿里健康码上放心  ...")
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}
	projectSite.Name = data.HospitalName
	//projectSite.ShortName = data.HospitalName
	projectSite.AliSiteID = data.HospitalId
	projectSite.Deleted = 2
	tools.Database.Collection("project_site").InsertOne(ctx, projectSite)
	//OID := projectSite.EnvironmentID
	//insertProjectSiteLog(ctx, nil, OID, 1, models.ProjectSite{}, projectSite)

	// 更新项目状态
	//update := bson.M{"$set": bson.M{"info.bound": status}}
	//if status == 1 {
	//	update = bson.M{"$set": bson.M{"info.ali_project_id": data.ProjectId, "info.ali_project_No": data.ProjectNo}}
	//}else if status == 2 {
	//	update = bson.M{"$set": bson.M{"info.ali_project_id": data.ProjectId, "info.ali_project_No": data.ProjectNo}}
	//}
	//if _, err := tools.Database.Collection("project").UpdateOne(nil, bson.M{"_id": project.ID}, update); err != nil {
	//	log.Print("【Bound】", err)
	//	return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	//}

	if data.Operation == "HOSPITAL_CENTER_BOUND" {
		//绑定
		data.ID = primitive.NewObjectID()
		data.LocalProjectId = project.ID
		data.Customer = project.CustomerID
		data.LocalSiteId = projectSite.ID
		tools.Database.Collection("ali_request").InsertOne(nil, data)
	} else {
		//解绑
		update := bson.M{"$set": bson.M{"operation": data.Operation}}
		tools.Database.Collection("ali_request").UpdateOne(nil, bson.M{"project_id": data.ProjectId, "project_no": data.ProjectNo, "hospital_id": data.HospitalId}, update)
	}

	return map[string]interface{}{"msgCode": "200", "msgInfo": "调用成功", "success": true, "status": 4}, nil

}

// InStockStatus ..补偿查询中央随机化系统中码的在库状态：
func (s *AliTraceabilityService) InStockStatus(ctx *gin.Context) (map[string]interface{}, error) {
	var body map[string]interface{}
	ctx.ShouldBindBodyWith(&body, binding.JSON)
	// 签名校验
	verify := sign(ctx, body)
	if !verify {
		return signFail, nil
	}

	trialProjectId := ctx.Query("trialProjectId")
	codes := ctx.Query("codes")

	//err := tools.Database.Collection("ali_request").FindOne(nil, bson.M{"project_id": data.ProjectId, "project_no": data.ProjectNo, "hospital_id": data.HospitalId})

	// 获取当前项目信息
	bounds := make([]models.Bound, 0)
	cursor, err := tools.Database.Collection("ali_request").Find(nil, bson.M{"project_id": trialProjectId})
	if err != nil {
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}
	err = cursor.All(nil, &bounds)
	if err != nil {
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}

	if len(bounds) == 0 {
		return map[string]interface{}{"msgCode": "401", "msgInfo": fmt.Sprintf("项⽬[%s]未绑定或已解绑！", bounds[0].ProjectName), "success": true, "status": 5}, nil
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": bounds[0].LocalProjectId}).Decode(&project)
	if err != nil {
		return map[string]interface{}{"msgCode": "408", "msgInfo": "项目不存在", "success": true, "status": 5}, nil //其他异常
	}

	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": bounds[0].LocalSiteId}).Decode(&projectSite)
	if err != nil {
		return map[string]interface{}{"msgCode": "408", "msgInfo": "项目不存在", "success": true, "status": 5}, nil //其他异常
	}

	listStr := strings.Split(codes, ",")
	filter := bson.M{"number": bson.M{"$in": listStr}, "project_id": project.ID, "env_id": projectSite.EnvironmentID}
	var medicines []models.MedicineCode
	cus, _ := tools.Database.Collection("medicine").Find(nil, filter)
	cus.All(nil, &medicines)

	var codeAndStatusBodyDTOS []models.CodeAndStatusBodyDTOS
	for _, item := range listStr {
		hasNumber := false
		for _, medicine := range medicines {
			if item == medicine.Number {
				hasNumber = true
			}
		}

		codeAndStatusBodyDTOS = append(codeAndStatusBodyDTOS, models.CodeAndStatusBodyDTOS{
			TransferCode: item,
			InStockFlag:  hasNumber,
		})
	}
	if len(codeAndStatusBodyDTOS) == 0 {
		codeAndStatusBodyDTOS = make([]models.CodeAndStatusBodyDTOS, 0)
	}
	return map[string]interface{}{"msgCode": "200", "msgInfo": "调用成功", "success": true, "codeAndStatusBodyDTOS": codeAndStatusBodyDTOS}, nil

}

// CodeStatus ..码状态更新后同步码状态：
func (s *AliTraceabilityService) CodeStatus(ctx *gin.Context) (map[string]interface{}, error) {
	var body map[string]interface{}
	ctx.ShouldBindBodyWith(&body, binding.JSON)
	//验证签名
	verify := sign(ctx, body)
	if !verify {
		return signFail, nil
	}
	var rep map[string]interface{}

	if err := tools.MongoClient.UseSession(context.Background(), func(sctx mongo.SessionContext) error {
		sctx.StartTransaction()

		projectID := ctx.Query("trialProjectId")
		hospitalRefEntID := ctx.Query("hospitalRefEntId")
		var bound models.Bound
		var project models.Project

		err := tools.Database.Collection("ali_request").FindOne(nil, bson.M{"project_id": projectID, "hospital_id": hospitalRefEntID}).Decode(&bound)
		if err != nil {
			rep = map[string]interface{}{"msgCode": "408", "msgInfo": "项目不存在", "success": true}
			return nil
		}

		if bound.Operation == "HOSPITAL_CENTER_UNBOUND" {
			rep = map[string]interface{}{"msgCode": "401", "msgInfo": fmt.Sprintf("项⽬[%s]未绑定或已解绑！", project.ProjectInfo.Name), "success": true}
			return nil
		}

		err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": bound.LocalProjectId}).Decode(&project)
		if err != nil {
			rep = map[string]interface{}{"msgCode": "408", "msgInfo": "项目不存在", "success": true}
			return nil
		}

		if project.ID == primitive.NilObjectID {
			rep = map[string]interface{}{"msgCode": "408", "msgInfo": "项目不存在", "success": true}
			return nil
		}

		if hospitalRefEntID != bound.HospitalId {
			rep = map[string]interface{}{"msgCode": "405", "msgInfo": fmt.Sprintf("项目【%s】中临床机构不存在", project.Name), "success": true} //项⽬未注册
			return nil
		}

		// 校验研究机构是否停用
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": bound.LocalSiteId}).Decode(&projectSite)
		if err != nil {
			rep = map[string]interface{}{"msgCode": "405", "msgInfo": fmt.Sprintf("项目【%s】中临床机构不存在", project.Name), "success": true} //项⽬未注册
			return nil
		}
		if projectSite.Deleted != 2 {
			msg := fmt.Sprintf("项⽬[%s]中临床研究机构[%s]已停⽤！", project.Name, projectSite.Name)
			rep = map[string]interface{}{"msgCode": "406", "msgInfo": msg, "success": true}
			return nil
		}

		listStr := strings.Split(ctx.Query("codes"), ",")
		filter := bson.M{"number": bson.M{"$in": listStr}, "site_id": bound.LocalSiteId, "project_id": bound.LocalProjectId}
		status := 0
		endReason := "。不可领药出库！"
		if ctx.Query("operation") == "TAKE_MEDICINE_WAREHOUSE_OUT" {
			status = 8
		}

		if ctx.Query("operation") == "RETURNED_WAREHOUSE_OUT" {
			status = 9
			endReason = "。不可退货出库！"
		}
		if ctx.Query("operation") == "DESTROYED_WAREHOUSE_OUT" {
			status = 10
			endReason = "。不可销毁出库！"
		}

		if status == 0 {
			rep = map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true} //其他异常
			return nil

		}

		var medicines []models.MedicineCode
		cursor, _ := tools.Database.Collection("medicine").Find(nil, filter)
		cursor.All(nil, &medicines)
		if len(medicines) == 0 {
			rep = map[string]interface{}{"msgCode": "410", "msgInfo": "码不存在", "success": true}
			return nil
		}

		availableMedicine := map[string]int{}

		for _, medicine := range medicines {
			if medicine.Status == 1 || medicine.Status == 14 {
				// 可用/冻结状态
				if ctx.Query("operation") != "TAKE_MEDICINE_WAREHOUSE_OUT" {
					availableMedicine[medicine.Number] = 1
				} else {
					if medicine.Status == 14 {
						availableMedicine[medicine.Number] = medicine.Status
					} else {
						availableMedicine[medicine.Number] = -1
					}
				}
			} else {
				if medicine.Status == 5 && ctx.Query("operation") == "TAKE_MEDICINE_WAREHOUSE_OUT" {
					// 已使用状态
					availableMedicine[medicine.Number] = 1
				} else {
					availableMedicine[medicine.Number] = medicine.Status
				}
			}
		}

		// 获取可以操作的码
		var availableFilter []string
		var reasonMedicine []string
		for _, item := range listStr {
			if availableMedicine[item] == 1 {
				availableFilter = append(availableFilter, item)
			} else {
				if availableMedicine[item] != -1 {
					status := data.GetMedicineStatus(ctx, availableMedicine[item])
					if status == "" {
						reasonMedicine = append(reasonMedicine, fmt.Sprintf("研究产品码[%s]不存在", item))
					} else {
						reasonMedicine = append(reasonMedicine, fmt.Sprintf("研究产品码[%s]当前状态[%s]", item, status))

					}
				} else {
					status := data.GetMedicineStatus(ctx, 1)
					if status == "" {
						reasonMedicine = append(reasonMedicine, fmt.Sprintf("研究产品码[%s]不存在", item))
					} else {
						reasonMedicine = append(reasonMedicine, fmt.Sprintf("研究产品码[%s]当前状态[%s]", item, status))

					}
				}

			}
		}

		reason := strings.Join(reasonMedicine, ",") + endReason
		if len(availableFilter) != 0 {
			filter["number"] = bson.M{"$in": availableFilter}
			update := bson.M{"$set": bson.M{"status": status}}
			res, _ := tools.Database.Collection("medicine").UpdateMany(sctx, filter, update)
			if int(res.ModifiedCount) != len(listStr) {
				msg := fmt.Sprintf("本次共收到{%d}个码，其中成功{%d}个，失败{%d}个,%s", len(listStr), res.ModifiedCount, len(listStr)-int(res.ModifiedCount), reason)
				rep = map[string]interface{}{"msgCode": "500", "msgInfo": msg, "success": true}
				sctx.CommitTransaction(sctx)

				return nil
			}

			var medicines []models.Medicine
			condition := bson.M{
				"project_id": projectSite.ProjectID,
				"env_id":     projectSite.EnvironmentID,
				"number":     bson.M{"$in": availableFilter},
				"status":     status,
			}
			kk, _ := tools.Database.Collection("medicine").Find(nil, filter)
			kk.All(nil, &condition)
			kk.All(nil, &medicines)

			var histories []models.History
			now := time.Duration(time.Now().Unix())

			userName := "码上放心"
			//if locales.Lang(ctx) == "en" {
			//	userName = "CodeTrust Platform"
			//}

			key := "history.medicine.rest-receive"
			operation := "码上放心 研究产品领药。"
			if locales.Lang(ctx) == "en" {
				operation = "CodeTrust Platform IP receive"
			}
			if ctx.Query("operation") == "TAKE_MEDICINE_WAREHOUSE_OUT" {
				key = "history.medicine.rest-receive"
				operation = "rest.receiveRest.assured.ip.receive"
			}
			if ctx.Query("operation") == "RETURNED_WAREHOUSE_OUT" {
				key = "history.medicine.rest-return"
				operation = "rest.receiveRest.assured.ip.return"
			}
			if ctx.Query("operation") == "DESTROYED_WAREHOUSE_OUT" {
				key = "history.medicine.rest-destroy"
				operation = "rest.receiveRest.assured.ip.destroy"
			}

			for _, medicine := range medicines {
				var dispensings []models.Dispensing
				filter := bson.M{
					"$or": []bson.M{
						{"dispensing_medicines.medicine_id": medicine.ID},
						{"replace_medicines.medicine_id": medicine.ID},
					},
					"project_id": projectSite.ProjectID,
					"env_id":     projectSite.EnvironmentID,
				}
				cus, _ := tools.Database.Collection("dispensing").Find(nil, filter)
				cus.All(nil, &dispensings)

				if len(dispensings) != 0 {
					var subject models.Subject
					err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": dispensings[0].SubjectID}).Decode(&subject)
					if err != nil {
						return nil
					}

					histories = append(histories, models.History{
						Key:  key,
						OID:  medicine.ID,
						Data: bson.M{"subject": subject.Info[0].Value, "visit": dispensings[0].VisitInfo.Name, "operation": operation},
						Time: now,
						UID:  primitive.NilObjectID,
						User: userName,
					})
				} else {
					if ctx.Query("operation") == "RETURNED_WAREHOUSE_OUT" {
						key = "history.medicine.rest-return-retrieve"
					}
					if ctx.Query("operation") == "DESTROYED_WAREHOUSE_OUT" {
						key = "history.medicine.rest-destroy-retrieve"
					}
					histories = append(histories, models.History{
						Key:  key,
						OID:  medicine.ID,
						Data: bson.M{"operation": operation},
						Time: now,
						UID:  primitive.NilObjectID,
						User: userName,
					})
				}
			}

			ctx.Set("HISTORY", histories)

		} else {
			msg := fmt.Sprintf("本次共收到{%d}个码，其中成功{0}个，失败{%d}个,%s", len(listStr), len(listStr), reason)
			rep = map[string]interface{}{"msgCode": "500", "msgInfo": msg, "success": true}
			return nil
		}
		sctx.CommitTransaction(sctx)
		rep = map[string]interface{}{"msgCode": "200", "msgInfo": "调用成功", "success": true}

		return nil
	}); err != nil {
		//return errors.WithStack(err)
		return map[string]interface{}{
			"error": errors.WithStack(err).Error(),
		}, err
	}
	return rep, nil
}

func convertToObjectIdArray(value interface{}) ([]primitive.ObjectID, error) {
	// 将value断言为[]interface{}类型
	values, ok := value.([]interface{})
	if !ok {
		return nil, fmt.Errorf("value is not an array")
	}

	// 创建一个长度为values的切片
	objectIDs := make([]primitive.ObjectID, len(values))

	// 遍历values切片
	for i, v := range values {
		// 将每个元素断言为primitive.ObjectID类型
		objectID, ok := v.(primitive.ObjectID)
		if !ok {
			return nil, fmt.Errorf("value at index %d is not an ObjectID", i)
		}

		// 将ObjectID赋值给objectIDs切片
		objectIDs[i] = objectID
	}

	return objectIDs, nil
}

// GetCode ..通过患者id获取码：
func (s *AliTraceabilityService) GetCode(ctx *gin.Context) (map[string]interface{}, error) {

	var body map[string]interface{}
	ctx.ShouldBindBodyWith(&body, binding.JSON)
	//验证签名
	verify := sign(ctx, body)
	if !verify {
		return signFail, nil
	}

	listStr := strings.Split(ctx.Query("patientIds"), ",")
	projectID := ctx.Query("trialProjectId")

	bounds := make([]models.Bound, 0)
	cursor, err := tools.Database.Collection("ali_request").Find(nil, bson.M{"project_id": projectID})
	if err != nil {
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}
	err = cursor.All(nil, &bounds)
	if err != nil {
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}

	if len(bounds) == 0 {
		return map[string]interface{}{"msgCode": "401", "msgInfo": fmt.Sprintf("项⽬[%s]未绑定或已解绑！", bounds[0].ProjectName), "success": true, "status": 5}, nil
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": bounds[0].LocalProjectId}).Decode(&project)
	if err != nil {
		return map[string]interface{}{"msgCode": "408", "msgInfo": "项目不存在", "success": true, "status": 5}, nil //其他异常
	}

	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": bounds[0].LocalSiteId}).Decode(&projectSite)
	if err != nil {
		return map[string]interface{}{"msgCode": "408", "msgInfo": "项目不存在", "success": true, "status": 5}, nil //其他异常
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"project_id": project.ID, "env_id": projectSite.EnvironmentID, "deleted": bson.M{"$ne": true}}}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname", "info.value": bson.M{"$in": listStr}}}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "dispensing",
			"localField":   "_id",
			"foreignField": "subject_id",
			"as":           "dispensing",
		}}},
		{{Key: "$project", Value: bson.M{
			"subject_id":  "$info.value",
			"medicine_id": "$dispensing.dispensing_medicines.medicine_id",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$medicine_id", "preserveNullAndEmptyArrays": true}}},

		{{Key: "$lookup", Value: bson.M{
			"from":         "medicine",
			"localField":   "medicine_id",
			"foreignField": "_id",
			"as":           "medicine",
		}}},
		{{Key: "$unwind", Value: bson.M{"path": "$medicine", "preserveNullAndEmptyArrays": true}}},
		{{Key: "$group", Value: bson.M{
			"_id":       "$subject_id",
			"medicine":  bson.M{"$addToSet": "$medicine.number"},
			"medicines": bson.M{"$addToSet": bson.M{"number": "$medicine.number", "status": "$medicine.status"}},
		}}},
		{{Key: "$unwind", Value: "$_id"}},
	}
	var patientCodeDTOS []models.PatientCodeDTOS
	var subjects []map[string]interface{}
	curs, err := tools.Database.Collection("subject").Aggregate(nil, pipeline)
	if err != nil {
		//return errors.WithStack(err)
		return map[string]interface{}{
			"error": errors.WithStack(err).Error(),
		}, nil
	}
	err = curs.All(nil, &subjects)
	if err != nil {
		return nil, err
	}
	for _, item := range listStr {
		inList := false
		for _, subject := range subjects {
			if subject["_id"] == item {
				// 码存在
				inList = true
				medicines := primitive.A{}
				status := 1
				PatientId := subject["_id"].(string)

				if len(subject["medicine"].(primitive.A)) != 0 {
					status = 0
					for _, medicine := range subject["medicines"].(primitive.A) {
						if int(medicine.(map[string]interface{})["status"].(int32)) == 5.0 {
							medicines = append(medicines, medicine.(map[string]interface{})["number"])
						}
					}
					if len(medicines) == 0 {
						status = 1
					}
				}
				patientCodeDTOS = append(patientCodeDTOS, models.PatientCodeDTOS{
					TransferCenterCodes: medicines,
					PatientId:           PatientId,
					CodeNoExistReason:   status,
				})
				break
			}
		}
		// 患者不存在
		if !inList {
			patientCodeDTOS = append(patientCodeDTOS, models.PatientCodeDTOS{
				TransferCenterCodes: primitive.A{},
				PatientId:           item,
				CodeNoExistReason:   2,
			})
		}
	}
	return map[string]interface{}{"msgCode": "200", "msgInfo": "调用成功", "success": true, "patientCodeDTOS": patientCodeDTOS}, nil
}

// GetPatient ..通过码获取患者id：
func (s *AliTraceabilityService) GetPatient(ctx *gin.Context) (map[string]interface{}, error) {
	var body map[string]interface{}
	ctx.ShouldBindBodyWith(&body, binding.JSON)
	//验证签名
	verify := sign(ctx, body)
	if !verify {
		return signFail, nil
	}

	codes := strings.Split(ctx.Query("codes"), ",")
	projectID := ctx.Query("trialProjectId")

	bounds := make([]models.Bound, 0)
	cursor, err := tools.Database.Collection("ali_request").Find(nil, bson.M{"project_id": projectID})
	if err != nil {
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}
	err = cursor.All(nil, &bounds)
	if err != nil {
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}

	if len(bounds) == 0 {
		return map[string]interface{}{"msgCode": "401", "msgInfo": fmt.Sprintf("项⽬[%s]未绑定或已解绑！", bounds[0].ProjectName), "success": true, "status": 5}, nil
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": bounds[0].LocalProjectId}).Decode(&project)
	if err != nil {
		return map[string]interface{}{"msgCode": "408", "msgInfo": "项目不存在", "success": true, "status": 5}, nil //其他异常
	}

	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": bounds[0].LocalSiteId}).Decode(&projectSite)
	if err != nil {
		return map[string]interface{}{"msgCode": "408", "msgInfo": "项目不存在", "success": true, "status": 5}, nil //其他异常
	}

	var CodePatientDTOS []models.CodePatientDTOS
	var info []interface{}
	for _, code := range codes {
		inList := false
		var medicine models.Medicine
		if err := tools.Database.Collection("medicine").FindOne(nil, bson.M{"number": code, "project_id": bounds[0].LocalProjectId, "env_id": projectSite.EnvironmentID}).Decode(&medicine); err == nil {
			info = append(info, medicine.ID)
			status := 0
			if medicine.Status == 0 { // 码未分配给受试者 未销毁 0 0
				status = 2
			}

			if medicine.Status == 6 { // 码已分配给受试者 销毁 1 1
				status = 3
			}

			if medicine.Status == 10 { // 码未分配给受试者 销毁 0 1
				status = 4
			}
			var dispensing []models.Dispensing
			cursor, err := tools.Database.Collection("dispensing").Find(nil, bson.M{"dispensing_medicines.medicine_id": bson.M{"$in": info}, "project_id": bounds[0].LocalProjectId, "env_id": projectSite.EnvironmentID})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			cursor.All(nil, &dispensing)
			if len(dispensing) != 0 {
				var subject models.Subject
				if err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing[0].SubjectID, "project_id": bounds[0].LocalProjectId, "env_id": projectSite.EnvironmentID}).Decode(&subject); err == nil {
					// 码存在
					inList = true
					patientId := ""
					if subject.Info[0].Value != nil {
						patientId = subject.Info[0].Value.(string)
					}

					CodePatientDTOS = append(CodePatientDTOS, models.CodePatientDTOS{
						TransferCenterCode:   code,
						PatientId:            patientId,
						PatientNoExistReason: status,
					})
				}
			} else {
				cus, err := tools.Database.Collection("dispensing").Find(nil, bson.M{"replace_medicines.medicine_id": bson.M{"$in": info}, "project_id": bounds[0].LocalProjectId, "env_id": projectSite.EnvironmentID})
				if err != nil {
					return nil, errors.WithStack(err)
				}
				cus.All(nil, &dispensing)
				if len(dispensing) != 0 {
					var subject models.Subject
					if err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing[0].SubjectID, "project_id": bounds[0].LocalProjectId, "env_id": projectSite.EnvironmentID}).Decode(&subject); err == nil {
						// 码存在
						inList = true
						patientId := ""
						if subject.Info[0].Value != nil {
							patientId = subject.Info[0].Value.(string)
						}

						CodePatientDTOS = append(CodePatientDTOS, models.CodePatientDTOS{
							TransferCenterCode:   code,
							PatientId:            patientId,
							PatientNoExistReason: status,
						})
					}
				}
			}
		}
		// 码不存在
		if !inList {
			CodePatientDTOS = append(CodePatientDTOS, models.CodePatientDTOS{
				TransferCenterCode:   code,
				PatientId:            "",
				PatientNoExistReason: 1,
			})
		}
	}

	return map[string]interface{}{"msgCode": "200", "msgInfo": "调用成功", "success": true, "codePatientDTOS": CodePatientDTOS}, nil
}

// ProjectUpdate ..临床研究机构名称修改
func (s *AliTraceabilityService) ProjectUpdate(ctx *gin.Context) (map[string]interface{}, error) {
	var body map[string]interface{}
	ctx.ShouldBindBodyWith(&body, binding.JSON)
	// 签名校验
	verify := sign(ctx, body)
	if !verify {
		return signFail, nil
	}

	listStr := strings.Split(ctx.Query("trialProjectIds"), ",")

	bounds := make([]models.Bound, 0)
	cursor, err := tools.Database.Collection("ali_request").Find(nil, bson.M{"project_id": bson.M{"$in": listStr}})
	if err != nil {
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}
	err = cursor.All(nil, &bounds)
	if err != nil {
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true, "status": 5}, nil //其他异常
	}

	if len(bounds) == 0 {
		return map[string]interface{}{"msgCode": "401", "msgInfo": fmt.Sprintf("项⽬[%s]未绑定或已解绑！", bounds[0].ProjectName), "success": true, "status": 5}, nil
	}

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": bounds[0].LocalProjectId}).Decode(&project)
	if err != nil {
		return map[string]interface{}{"msgCode": "408", "msgInfo": "项目不存在", "success": true, "status": 5}, nil //其他异常
	}

	var customerOID []primitive.ObjectID
	for _, item := range bounds {
		customerOID = append(customerOID, item.Customer)
	}
	if len(customerOID) == 0 {
		return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true}, nil
	}

	filter := bson.M{"customer_id": bson.M{"$in": customerOID}, "name": ctx.Query("hospitalOldRefEntName")}
	update := bson.M{"$set": bson.M{"name": ctx.Query("hospitalNewRefEntName")}}
	res, _ := tools.Database.Collection("project_site").UpdateMany(nil, filter, update)
	if res.ModifiedCount == 0 {
		msg := fmt.Sprintf("本次共同步%d个有效项⽬的临床研究机构信息，其中成功%d个，失败%d个。", len(listStr), res.ModifiedCount, len(listStr)-int(res.ModifiedCount))
		return map[string]interface{}{"msgCode": "500", "msgInfo": msg, "success": true}, nil
	} else {
		msg := fmt.Sprintf("本次共同步%d个有效项⽬的临床研究机构信息，其中成功%d个，失败%d个。", len(listStr), res.ModifiedCount, len(listStr)-int(res.ModifiedCount))
		return map[string]interface{}{"msgCode": "500", "msgInfo": msg, "success": true}, nil

	}
	if int(res.ModifiedCount) == len(listStr) && len(listStr) != 0 {
		return map[string]interface{}{"msgCode": "200", "msgInfo": "调用成功", "success": true}, nil
	}
	return map[string]interface{}{"msgCode": "400", "msgInfo": "其他异常", "success": true}, nil

}

func sign(ctx *gin.Context, data map[string]interface{}) bool {
	//params := ctx.Query()
	// 获取URL的所有参数
	params := ctx.Request.URL.Query()
	var stringList []string
	for key, _ := range params {
		if key != "sign" {
			stringList = append(stringList, key)
		}
	}
	// 第一步：检查参数是否已经排序
	sort.Strings(stringList)

	//第二步：把所有参数名和参数值串在一起
	var signStr = ""
	for _, key := range stringList {
		value := params[key][0]
		signStr = signStr + fmt.Sprintf("%v%v", key, value)
	}

	if len(data) != 0 {
		jsonData, _ := json.Marshal(data)
		jsonString := string(jsonData)
		signStr = signStr + jsonString
	}

	//mapStr := strings.Join(mapList, "")
	AppSecret := "10f690aa4b45e61bb97a23590d0474a8"

	sign := ""
	// 第三步：使用MD5/HMAC加密
	if ctx.Query("sign_method") == "md5" {
		sign = strings.ToUpper(Md5Encryption(signStr, AppSecret))
	}

	reqSign := ctx.Query("sign")
	if isMD5String(reqSign) {
		if sign == reqSign {
			return true
		} else {
			return false
		}
	} else {
		return false
	}

}

func isMD5String(str string) bool {
	// MD5字符串的正则表达式
	md5Regex := regexp.MustCompile(`^[a-fA-F0-9]{32}$`)
	return md5Regex.MatchString(str)
}

func Md5Encryption(data string, secret string) string {
	strs := fmt.Sprintf("%s%s%s", secret, data, secret)
	hash := md5.Sum([]byte(strs))
	md5Hash := hex.EncodeToString(hash[:])
	return md5Hash
}

func checkProject(ctx *gin.Context) models.Project {
	projectID := ctx.Query("trialProjectId")
	var bound models.Bound
	var project models.Project
	err := tools.Database.Collection("ali_request").FindOne(nil, bson.M{"project_id": projectID}).Decode(&bound)
	if err != nil {
		return models.Project{}
	}
	tools.Database.Collection("project").FindOne(nil, bson.M{"_id": bound.LocalProjectId}).Decode(&project)

	return project
}

func cancelDispensing(sctx mongo.SessionContext, id primitive.ObjectID) bool {
	canOut := false
	var dispensing models.Dispensing
	var subject models.Subject
	var dispensings []models.Dispensing
	tools.Database.Collection("dispensing").FindOne(nil, bson.M{"dispensing_medicines.medicine_id": id}).Decode(&dispensing)
	tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
	opts := &options.FindOptions{
		Sort: bson.D{{"serial_number", 1}},
	}
	cursor, _ := tools.Database.Collection("dispensing").Find(nil, bson.M{"subject_id": dispensing.SubjectID}, opts)
	cursor.All(nil, &dispensings)
	//var tmpDispensing models.Dispensing

	random := false
	randomIndex := -1 // -1、全都是随机前  0、全都是随机后        <i-1 随机前    >i 随机后
	tmpIndex := -1    // 当前已发药访视索引
	for i, item := range dispensings {
		if dispensing.ID == item.ID {
			//tmpDispensing = item
			tmpIndex = i
		}
		if item.VisitInfo.Random == true {
			randomIndex = i // 随机
		}
	}

	if tmpIndex >= randomIndex && randomIndex != -1 {
		random = true
	}
	// 随机状态 区分是否可以 撤销
	if (subject.Status == 3) == random {
		canOut = true
	}

	if canOut {
		// 撤销 操作
		var medicine bson.A
		for _, dispensingMedicine := range dispensing.DispensingMedicines {
			medicine = append(medicine, bson.M{"_id": dispensingMedicine.MedicineID})
		}
		update := bson.M{"$set": bson.M{"status": 1}}
		tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"$or": medicine}, update)

		update = bson.M{
			"$set": bson.M{"dispensing_medicines": nil, "other_dispensing_medicines": nil, "dispensing_time": nil, "status": 1},
		}
		tools.Database.Collection("dispensing").UpdateOne(sctx, bson.M{"_id": dispensing.ID}, update)
	}

	return canOut

}

// RefillMedicine .. 通知中央随机化系统补领药
func (s *AliTraceabilityService) RefillMedicine(ctx *gin.Context) (map[string]interface{}, error) {
	var res map[string]interface{}
	var body map[string]interface{}
	ctx.ShouldBindBodyWith(&body, binding.JSON)
	//验证签名
	verify := sign(ctx, body)
	if !verify {
		return signFail, nil
	}

	codes := strings.Split(ctx.Query("codes"), ",")
	projectID := ctx.Query("trialProjectId")
	hospitalRefEntID := ctx.Query("hospitalRefEntId")
	returnReason := ctx.Query("returnReason")
	var bound models.Bound
	tools.Database.Collection("ali_request").FindOne(nil, bson.M{"project_id": projectID, "hospital_id": hospitalRefEntID}).Decode(&bound)
	var project models.Project
	tools.Database.Collection("project").FindOne(nil, bson.M{"_id": bound.LocalProjectId}).Decode(&project)

	if hospitalRefEntID != bound.HospitalId {
		return map[string]interface{}{"msgCode": "405", "msgInfo": fmt.Sprintf("项目【%s】中临床机构不存在", project.Name), "success": true, "replaceCodeDTOS": bson.A{}}, nil //项⽬未注册
	}

	// 校验研究机构是否停用
	var projectSite models.ProjectSite
	err := tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": bound.LocalSiteId}).Decode(&projectSite)
	if err != nil {
		msg := fmt.Sprintf("项⽬[%s]中临床研究机构[%s]已停⽤！", project.Name, projectSite.Name)
		res = map[string]interface{}{"msgCode": "406", "msgInfo": msg, "success": true, "replaceCodeDTOS": bson.A{}}
		return nil, nil
	}

	//timeZone, err := tools.GetTimeZone(project.ID)
	//if err != nil {
	//	return map[string]interface{}{
	//		"error": errors.WithStack(err).Error(),
	//	}, nil
	//}
	//
	//date := time.Now().UTC().Add(time.Hour * time.Duration(timeZone)).Format("2006-01-02")

	var info []interface{}
	for _, code := range codes {
		var medicine models.Medicine
		if err := tools.Database.Collection("medicine").FindOne(nil, bson.M{"number": code, "project_id": bound.LocalProjectId, "env_id": projectSite.EnvironmentID}).Decode(&medicine); err == nil {
			info = append(info, medicine.ID)
		}
	}
	var id string
	var subjectId string
	if len(info) > 0 {
		var dispensing []models.Dispensing
		cursor, err := tools.Database.Collection("dispensing").Find(nil, bson.M{"dispensing_medicines.medicine_id": bson.M{"$in": info}, "project_id": bound.LocalProjectId, "env_id": projectSite.EnvironmentID})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		cursor.All(nil, &dispensing)
		if len(dispensing) != 0 {
			id = dispensing[0].ID.Hex()
			subjectId = dispensing[0].SubjectID.Hex()
		}
	}

	// 数据操作
	data := map[string]interface{}{
		"cohort_id":   nil,
		"customer_id": project.CustomerID.Hex(),
		"env_id":      projectSite.EnvironmentID.Hex(),
		"id":          id,
		"info":        info,
		"project_id":  project.ID.Hex(),
		"reason":      returnReason,
		"role_id":     nil,
		"subject_id":  subjectId,
	}
	replaceCodeDTOS, err := s.dispensingService.AliReplaceDrug(ctx, data, "")
	if replaceCodeDTOS == nil {
		res = map[string]interface{}{"msgCode": "403", "msgInfo": fmt.Sprintf("无效药物码参数【%s】!", ctx.Query("codes")), "success": true, "replaceCodeDTOS": replaceCodeDTOS}
	} else {
		res = map[string]interface{}{"msgCode": "200", "msgInfo": "调用成功", "success": true, "replaceCodeDTOS": replaceCodeDTOS}
	}

	//if err := tools.MongoClient.UseSession(context.Background(), func(sctx mongo.SessionContext) error {
	//	sctx.StartTransaction()
	//
	//	var replaceCodeDTOSs []models.ReplaceCodeDTOS
	//	for _, code := range codes {
	//		var medicine models.MedicineCode
	//		var replaceCodeDTOS models.ReplaceCodeDTOS
	//		replaceCodeDTOS.OldCode = code
	//		tools.Database.Collection("medicine").FindOne(nil, bson.M{"status": 8, "number": code, "project_id": bound.LocalProjectId}).Decode(&medicine)
	//		if medicine.ID == primitive.NilObjectID {
	//			replaceCodeDTOS.NoReplaceReason = "被替换码不存在"
	//		} else {
	//			var reMedicine models.MedicineCode
	//			tools.Database.Collection("medicine").FindOne(nil, bson.M{
	//				"name":            medicine.Name,
	//				"spec":            medicine.Spec,
	//				"pkg_spec":        medicine.PkgSpec,
	//				"expiration_date": bson.M{"$gt": date},
	//				"status":          1,
	//				"project_id":      bound.LocalProjectId,
	//				"site_id":         bound.LocalSiteId,
	//			}).Decode(&reMedicine)
	//			if reMedicine.ID == primitive.NilObjectID {
	//				replaceCodeDTOS.NoReplaceReason = "库存不够⽆法分配新码"
	//			} else {
	//				dispensingMedicines := models.DispensingMedicine{
	//					MedicineID:     reMedicine.ID,
	//					Name:           reMedicine.Name,
	//					Number:         reMedicine.Number,
	//					ExpirationDate: reMedicine.ExpirationDate,
	//					BatchNumber:    reMedicine.BatchNumber,
	//				}
	//
	//				replaceCodeDTOS.NewCode = reMedicine.Number
	//				// 修改受试者发药信息
	//				update := bson.M{
	//					"$set": bson.M{
	//						"dispensing_medicines.$[oid]": dispensingMedicines,
	//					},
	//				}
	//				opts := &options.UpdateOptions{
	//					ArrayFilters: &options.ArrayFilters{
	//						Filters: bson.A{bson.M{"oid.medicine_id": medicine.ID}},
	//					},
	//				}
	//				_, err := tools.Database.Collection("dispensing").UpdateOne(sctx, bson.M{"dispensing_medicines.medicine_id": medicine.ID}, update, opts)
	//				if err != nil {
	//					return errors.WithStack(err)
	//				}
	//				// 更新被替换研究产品信息状态
	//				_, err = tools.Database.Collection("medicine").UpdateOne(sctx, bson.M{"_id": medicine.ID}, bson.M{"$set": bson.M{"status": 9}})
	//				if err != nil {
	//					return errors.WithStack(err)
	//				}
	//				// 更新替换研究产品信息状态
	//				_, err = tools.Database.Collection("medicine").UpdateOne(sctx, bson.M{"_id": reMedicine.ID}, bson.M{"$set": bson.M{"status": 8}})
	//				if err != nil {
	//					return errors.WithStack(err)
	//				}
	//			}
	//		}
	//		replaceCodeDTOSs = append(replaceCodeDTOSs, replaceCodeDTOS)
	//
	//	}
	//	res = map[string]interface{}{"msgCode": "200", "msgInfo": "调用成功", "success": true, "replaceCodeDTOS": replaceCodeDTOSs}
	//
	//	sctx.CommitTransaction(sctx)
	//
	//	return nil
	//}); err != nil {
	//	//return errors.WithStack(err)
	//	return map[string]interface{}{
	//		"error": errors.WithStack(err).Error(),
	//	}, err
	//}

	return res, nil
}

// BackIn .. 替换⼊库
func (s *AliTraceabilityService) BackIn(ctx *gin.Context) (map[string]interface{}, error) {
	var res map[string]interface{}
	var body map[string]interface{}
	ctx.ShouldBindBodyWith(&body, binding.JSON)
	//验证签名
	verify := sign(ctx, body)
	if !verify {
		return signFail, nil
	}

	codes := strings.Split(body["codes"].(string), ",")
	projectID := body["trialProjectId"].(string)
	hospitalRefEntID := body["hospitalRefEntId"].(string)
	//returnReason := body["returnReason"].(string)
	var bound models.Bound
	tools.Database.Collection("ali_request").FindOne(nil, bson.M{"project_id": projectID, "hospital_id": hospitalRefEntID}).Decode(&bound)
	var project models.Project
	tools.Database.Collection("project").FindOne(nil, bson.M{"_id": bound.LocalProjectId}).Decode(&project)

	if hospitalRefEntID != bound.HospitalId {
		return map[string]interface{}{"msgCode": "405", "msgInfo": fmt.Sprintf("项目【%s】中临床机构不存在", project.Name), "success": true}, nil //项⽬未注册
	}

	// 校验研究机构是否停用

	// 校验研究机构是否停用
	var projectSite models.ProjectSite
	err := tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": bound.LocalSiteId}).Decode(&projectSite)
	if err != nil {
		msg := fmt.Sprintf("项⽬[%s]中临床研究机构[%s]已停⽤！", project.Name, projectSite.Name)
		res = map[string]interface{}{"msgCode": "406", "msgInfo": msg, "success": true}
		return nil, nil
	}

	// 数据操作
	//DispensingService.ReplaceDrug(ctx, data, "")

	var info []interface{}
	var strs []interface{}
	for _, code := range codes {
		var medicine models.Medicine
		if err := tools.Database.Collection("medicine").FindOne(nil, bson.M{"number": code, "project_id": bound.LocalProjectId, "env_id": projectSite.EnvironmentID}).Decode(&medicine); err == nil {
			info = append(info, medicine.ID)
			strs = append(strs, medicine.ID.Hex())
		}
	}
	var id string
	//var subjectId string
	if len(info) > 0 {
		var dispensing []models.Dispensing
		cursor, err := tools.Database.Collection("dispensing").Find(nil, bson.M{"dispensing_medicines.medicine_id": bson.M{"$in": info}, "project_id": bound.LocalProjectId, "env_id": projectSite.EnvironmentID})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		cursor.All(nil, &dispensing)
		if len(dispensing) != 0 {
			id = dispensing[0].ID.Hex()
			//subjectId = dispensing[0].SubjectID.Hex()
		}
	}

	// 数据操作
	data := map[string]interface{}{
		"number":       strs,
		"other_number": make([]interface{}, 0),
	}
	err = s.dispensingService.AliRetrievalDrug(ctx, id, data)
	if err != nil {
		res = map[string]interface{}{"msgCode": "403", "msgInfo": fmt.Sprintf("无效药物码参数【%s】!", ctx.Query("codes")), "success": true}
	} else {
		res = map[string]interface{}{"msgCode": "200", "msgInfo": "调用成功", "success": true}
	}
	return res, nil

}
