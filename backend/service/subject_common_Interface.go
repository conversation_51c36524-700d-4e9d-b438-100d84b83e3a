package service

import (
	"bytes"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/wxnacy/wgo/arrays"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/mongo/options"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// SubjectScreen 受试者筛选
func SubjectScreen(ctx *gin.Context, req models.SubjectScreenReq) error {
	me, err := tools.Me(ctx)
	if err != nil {
		return err
	}
	var project models.Project
	nowDuration := time.Duration(time.Now().Unix())

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		subject := models.Subject{}
		err := tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": req.ID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if subject.Status != 1 {
			return nil, tools.BuildServerError(ctx, "subject_status_error")
		}
		err = tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": subject.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查属性配置
		filter := bson.M{"env_id": subject.EnvironmentID}
		var cohort models.Cohort
		var env models.Environment
		envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == subject.EnvironmentID
		})
		env = *envP
		if !subject.CohortID.IsZero() {
			filter = bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
			cohortP, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return item.ID == subject.CohortID
			})
			cohort = *cohortP
		}
		attribute := models.Attribute{}
		_ = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)

		if req.IsScreen {
			//cohort项目仅发药,判断入组已满时，登记发过药/筛选成功之后不允许登记/筛选
			err = JustDispensingEnrollmentFull(ctx, sctx, project, attribute, filter, env, cohort, 1)
			if err != nil {
				return "", err
			}
		}
		status := 0
		if req.IsScreen {
			status = 7
		} else {
			status = 8
		}
		update := bson.M{"$set": bson.M{
			"is_screen":   req.IsScreen,
			"screen_time": req.ScreenTime,
			"icf_time":    req.ICFTime,
			"status":      status,
		}}
		_, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": req.ID}, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if req.RoleId != primitive.NilObjectID && attribute.AttributeInfo.Dispensing {
			e := PatchAppDispenseTask(ctx, subject.ID.Hex(), req.RoleId.Hex())
			if e != nil {
				return nil, errors.WithStack(e)
			}
		}

		// 在随机
		key := "history.subject.label.screen"
		stage := ""
		have := models.HaveCohortReRandom(project, attribute.EnvironmentID.Hex())
		c, b := models.GetCohort(project, attribute.EnvironmentID.Hex(), attribute.CohortID.Hex())
		if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
			stage = c.Name
		} else if project.ProjectInfo.Type == 2 && have && b && c.Type == 1 {
			stage = c.ReRandomName
		}
		if (project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number)) || (project.ProjectInfo.Type == 2 && have && b && c.Type == 1) {
			key = "history.subject.label.at-random-screen"

		}

		history := models.History{
			Key: key,
			OID: subject.ID,
			Data: map[string]interface{}{
				"label":      attribute.AttributeInfo.SubjectReplaceText,
				"name":       subject.Info[0].Value,
				"isScreen":   req.IsScreen,
				"screenTime": req.ScreenTime,
				"icfTime":    req.ICFTime,
				"stage":      stage,
			},
			Time: nowDuration,
			UID:  me.ID,
			User: me.Name,
		}
		_, err = tools.Database.Collection("history").InsertOne(sctx, history)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//判断是否需要发送邮件
		var noticeConfig models.NoticeConfig
		err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "key": "notice.subject.screen"}).Decode(&noticeConfig)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, err
		}
		// site
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		var mails []models.Mail
		needSendMail := false
		if !noticeConfig.ID.IsZero() {
			if slice.Contain(noticeConfig.State, "subject.screen.success") && (subject.IsScreen == nil || !*subject.IsScreen) && req.IsScreen {
				needSendMail = true
			}
			if slice.Contain(noticeConfig.State, "subject.screen.fail") && (subject.IsScreen == nil || *subject.IsScreen) && !req.IsScreen {
				needSendMail = true
			}
			if needSendMail {
				//发送邮件
				userMail, err := tools.GetRoleUsersMailWithRole(subject.ProjectID, subject.EnvironmentID, "notice.subject.screen", subject.ProjectSiteID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if len(userMail) > 0 {
					//environment
					var environment models.Environment
					for _, env := range project.Environments {
						if env.ID == subject.EnvironmentID {
							environment = env
							break
						}
					}

					subjectData := bson.M{
						"projectNumber": project.Number,
						"envName":       environment.Name,
						"siteNumber":    projectSite.Number,
						"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
						"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
					}

					//subjectReplaceText := GetSubjectReplaceText(ctx, attribute)
					subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
					subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)
					isScreenContentData := tools.GetIsScreenResultTranLang("zh", req.IsScreen)
					isScreenContentDataEn := tools.GetIsScreenResultTranLang("en", req.IsScreen)
					screenTimeContentData := ""
					icfTimeContentData := ""
					screenTimeContentData = req.ScreenTime
					icfTimeContentData = req.ICFTime
					contentData := bson.M{
						"projectNumber": project.Number,
						"projectName":   project.Name,
						"envName":       environment.Name,
						"siteNumber":    projectSite.Number,
						"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
						"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
						"label":         subjectEmailReplaceTextZh,
						"labelEn":       subjectEmailReplaceTextEn,
						"subjectNumber": subject.Info[0].Value,
						"isScreen":      isScreenContentData,
						"isScreenEn":    isScreenContentDataEn,
						"screenTime":    screenTimeContentData,
						"icfTime":       icfTimeContentData,
					}
					if !cohort.ID.IsZero() {
						contentData["cohortName"] = models.GetCohortReRandomName(cohort)
					}
					mailBodyContet, err := tools.MailBodyContent(nil, subject.EnvironmentID, "notice.subject.screen")
					for key, v := range mailBodyContet {
						contentData[key] = v
					}
					if err != nil {
						return nil, errors.WithStack(err)
					}
					var nc models.NoticeConfig
					err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": environment.ID, "key": "notice.basic.settings"}).Decode(&nc)
					if err != nil && err != mongo.ErrNoDocuments {
						return nil, errors.WithStack(err)
					}
					langList := make([]string, 0)
					html := "subject_screen_zh_en.html"
					if nc.Manual != 0 {
						if nc.Manual == 1 {
							langList = append(langList, "zh")
							html = "subject_screen_zh.html"
						} else if nc.Manual == 2 {
							langList = append(langList, "en")
							html = "subject_screen_en.html"
						} else if nc.Manual == 3 {
							langList = append(langList, "zh")
							langList = append(langList, "en")
							html = "subject_screen_zh_en.html"
						}
					} else {
						langList = append(langList, ctx.GetHeader("Accept-Language"))
						if locales.Lang(ctx) == "zh" {
							html = "subject_screen_zh.html"
						} else if locales.Lang(ctx) == "en" {
							html = "subject_screen_en.html"
						}
					}
					for _, email := range userMail {
						insertData := make(map[string]interface{})
						for k, v := range contentData {
							insertData[k] = v
						}
						var toUserMail []string
						toUserMail = append(toUserMail, email.Email)
						mails = append(mails, models.Mail{
							ID:           primitive.NewObjectID(),
							Subject:      "subject.screen.title",
							SubjectData:  subjectData,
							ContentData:  insertData,
							HTML:         html,
							To:           toUserMail,
							Lang:         ctx.GetHeader("Accept-Language"),
							LangList:     langList,
							Status:       0,
							CreatedTime:  nowDuration,
							ExpectedTime: nowDuration,
							SendTime:     nowDuration,
						})
					}

				}
			}
		}
		alertUserMail, err := tools.GetRoleUsersMailWithRole(subject.ProjectID, subject.EnvironmentID, "notice.subject.alert.threshold", subject.ProjectSiteID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var nc models.NoticeConfig
		err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "key": "notice.basic.settings"}).Decode(&nc)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		html := "subject_limit_alert_screen_zh_en.html"
		if nc.Manual != 0 {
			if nc.Manual == 1 {
				html = "subject_limit_alert_screen_zh.html"
			} else if nc.Manual == 2 {
				html = "subject_limit_alert_screen_en.html"
			} else if nc.Manual == 3 {
				html = "subject_limit_alert_screen_zh_en.html"
			}
		} else {
			if locales.Lang(ctx) == "zh" {
				html = "subject_limit_alert_screen_zh.html"
			} else if locales.Lang(ctx) == "en" {
				html = "subject_limit_alert_screen_en.html"
			}
		}
		if len(alertUserMail) > 0 {
			mails, err = alertThresholds(sctx, 7, project, env, cohort, attribute, subject, projectSite, ctx, html, mails)
			if err != nil {
				return models.RemoteSubjectRandom{}, err
			}
		}

		ctx.Set("MAIL", mails)
		if len(mails) > 0 {
			var envs []models.MailEnv
			for _, m := range mails {
				envs = append(envs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     m.ID,
					CustomerID: subject.CustomerID,
					ProjectID:  subject.ProjectID,
					EnvID:      subject.EnvironmentID,
					CohortID:   subject.CohortID,
				})
			}
			ctx.Set("MAIL-ENV", envs)
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return err
	}

	// 推给EDC
	if tools.PushScenarioFilter(project.ProjectInfo.ConnectEdc, project.ProjectInfo.PushMode, project.ProjectInfo.EdcSupplier, project.ProjectInfo.PushScenario.ScreenPush) {
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectRandomPush(logData, req.ID, 13, nowDuration, "", "", "")
	}
	return nil
}

// SubjectRegister 受试者登记
func SubjectRegister(ctx *gin.Context, subject map[string]interface{}, ssctx mongo.SessionContext) (string, string, error) {
	customerOID, _ := primitive.ObjectIDFromHex(subject["customerId"].(string))
	projectOID, _ := primitive.ObjectIDFromHex(subject["projectId"].(string))
	envOID, _ := primitive.ObjectIDFromHex(subject["envId"].(string))
	projectSiteOID, _ := primitive.ObjectIDFromHex(subject["projectSiteId"].(string))

	roleOId := primitive.NilObjectID
	if subject["roleId"] != nil {
		roleOId, _ = primitive.ObjectIDFromHex(subject["roleId"].(string))
	}
	now := time.Duration(time.Now().Unix())

	user := models.User{}
	u, _ := ctx.Get("user")
	if u != nil {
		user = u.(models.User)
	}

	projectFilter := bson.M{"_id": projectOID}
	// 项目
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
	if err != nil {
		return "", "", errors.WithStack(err)
	}
	var cohortOID = primitive.NilObjectID
	var cohort models.Cohort
	var env models.Environment
	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env = *envP
	//查询表单
	filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if subject["cohortId"] != nil {
		cohortOID, _ = primitive.ObjectIDFromHex(subject["cohortId"].(string))
		cohortP, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
			return item.ID == cohortOID
		})
		cohort = *cohortP
		filter = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
		// cohort项目校验
		if cohort.Status == 1 {
			return "", "", tools.BuildServerError(ctx, "subject_cohort_check_register")
		}
	} else {
		if *env.Status == 1 {
			return "", "", tools.BuildServerError(ctx, "subject_cohort_check_register")
		}
	}

	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return "", "", errors.WithStack(err)
	}
	//subjectNumberRule := attribute.AttributeInfo.SubjectNumberRule

	//cohort项目仅发药,判断入组已满时，登记发过药/筛选成功之后不允许登记/筛选
	err = JustDispensingEnrollmentFull(ctx, nil, project, attribute, filter, env, cohort, 0)
	if err != nil {
		return "", "", err
	}

	//判断是否是edc对接
	if project.ConnectEdc == 1 && project.PushMode == 1 {
		if subject["shortname"] == nil || subject["shortname"] == "" {
			return "", "", tools.BuildServerError(ctx, "edc.parameter.error")
		}
	}
	// else {
	// 	//受试者号录入规则
	// 	if subjectNumberRule == 2 || subjectNumberRule == 3 {
	// 		subject["shortname"], err = getSubjectNumber(ctx, projectOID, envOID, projectSiteOID, attribute.AttributeInfo)
	// 		if err != nil {
	// 			return "", "", errors.WithStack(err)
	// 		}
	// 	}
	// }

	var subjectOID primitive.ObjectID
	// 声明接受字段信息
	var fields []models.Field

	fields = append(fields, attribute.AttributeInfo.Field)

	var form models.Form
	_ = tools.Database.Collection("form").FindOne(nil, filter).Decode(&form)
	if form.Fields != nil {
		for _, fm := range form.Fields {
			if (fm.ApplicationType == nil || *fm.ApplicationType == 1 || *fm.ApplicationType == 4) && (fm.Status == nil || *fm.Status == 1) {
				fields = append(fields, fm)
			}
		}
	}
	// 查询是否有分层因素
	randomFilter := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		"status":      1,
		"$or": bson.A{
			bson.M{"site_ids": nil},
			bson.M{"site_ids": projectSiteOID},
		}}
	if subject["cohortId"] != nil {
		randomFilter = bson.M{
			"customer_id": customerOID,
			"env_id":      envOID,
			"status":      1,
			"cohort_id":   cohortOID,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": projectSiteOID},
			}}
	}

	createdAt := time.Duration(time.Now().Unix())
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		if ssctx != nil {
			sctx = ssctx
		}
		// 受试者号校验针对环境或者cohort
		checkCondition := bson.M{
			"customer_id": customerOID,
			"project_id":  projectOID,
			"env_id":      envOID,
			"info": bson.M{
				"$elemMatch": bson.M{
					"name":  "shortname",
					"value": subject["shortname"],
				},
			},
			"deleted": bson.M{"$ne": true},
		}
		if project.ProjectInfo.Type == 3 {
			checkCondition["cohort_id"] = cohortOID
		}

		if count, _ := tools.Database.Collection("subject").CountDocuments(sctx, checkCondition); count > 0 {
			return nil, tools.BuildServerError(ctx, "subject_number_repeat")
		}

		var lastSubject models.Subject
		if (project.Type == 3 || project.Type == 2 && cohort.Type == 1) && subject["lastId"] != nil {
			lastOID, _ := primitive.ObjectIDFromHex(subject["lastId"].(string))
			if lastOID != primitive.NilObjectID {
				subjectFilter := bson.M{
					"customer_id": customerOID,
					"project_id":  projectOID,
					"env_id":      envOID,
					"cohort_id":   lastOID,
					"info": bson.M{
						"$elemMatch": bson.M{
							"name":  "shortname",
							"value": subject["shortname"],
						},
					},
					"deleted": bson.M{"$ne": true},
				}
				err = tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&lastSubject)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
				if lastSubject.Group == "" {
					return nil, tools.BuildServerError(ctx, "subject_cohort_last_group")
				}
				randomFilter = bson.M{
					"customer_id": customerOID,
					"env_id":      envOID,
					"status":      1,
					"cohort_id":   cohortOID,
					"last_group":  lastSubject.Group,
					"$or": bson.A{
						bson.M{"site_ids": nil},
						bson.M{"site_ids": projectSiteOID},
					}}
			}
		}

		var randomList models.RandomList
		err = tools.Database.Collection("random_list").FindOne(sctx, randomFilter).Decode(&randomList)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 查询访视周期
		var visitCycle models.VisitCycle
		err = tools.Database.Collection("visit_cycle").FindOne(sctx, filter).Decode(&visitCycle)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// factorSign为false时表示需要登记分层因素
		factors := randomList.Design.Factors
		factors = slice.Filter(factors, func(index int, item models.RandomFactor) bool {
			if item.Status == nil {
				return true
			}
			return *item.Status != 2
		})
		factorSign := LayeredBl(attribute, visitCycle, factors)
		if !factorSign {
			for i := 0; i < len(factors); i++ {
				if factors[i].IsCalc {
					fields, err = factorCalc(fields, factors[i], subject, ctx)
					if err != nil {
						return nil, err
					}
				} else {
					if subject[factors[i].Name] != nil {
						fields = append(fields, models.Field{
							Name:    factors[i].Name,
							Label:   factors[i].Label,
							Type:    factors[i].Type,
							Options: factors[i].Options,
							Status:  factors[i].Status,
						})
					}
				}
			}
		}
		form.Fields = fields

		// 数据整合
		var info []models.Info
		for i := 0; i < len(form.Fields); i++ {
			info = append(info, models.Info{
				Name:  fields[i].Name,
				Value: subject[fields[i].Name],
			})
		}
		subjectData := models.Subject{
			ID:                     primitive.NewObjectID(),
			CustomerID:             customerOID,
			ProjectID:              projectOID,
			EnvironmentID:          envOID,
			CohortID:               cohortOID,
			ProjectSiteID:          projectSiteOID,
			LastGroup:              lastSubject.Group,
			RegisterRandomListID:   randomList.ID,
			Info:                   info,
			Status:                 1,
			PvUnblindingStatus:     0,
			UrgentUnblindingStatus: 0,
			Meta: models.Meta{
				CreatedAt: createdAt,
				CreatedBy: user.ID,
			},
			UrgentUnblindingApprovals:   []models.UrgentUnblindingApproval{},
			PvUrgentUnblindingApprovals: []models.UrgentUnblindingApproval{},
		}
		if subject["cohortId"] != nil {
			subjectData.CohortID, _ = primitive.ObjectIDFromHex(subject["cohortId"].(string))
		}

		result, err := tools.Database.Collection("subject").InsertOne(sctx, subjectData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		_, err = tools.Database.Collection("subject_shortname").InsertOne(sctx, models.SubjectShortname{
			EnvID:     subjectData.EnvironmentID,
			CohortID:  subjectData.CohortID,
			Shortname: subjectData.Info[0].Value.(string),
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		subjectOID = result.InsertedID.(primitive.ObjectID)
		// 添加轨迹
		_, err = SubjectTrail(sctx, 1, form, info, subject, user, result.InsertedID.(primitive.ObjectID), project, now, attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var newSubject models.Subject
		err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&newSubject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 添加邮件
		err = sendSubjectAddMail(ctx, sctx, attribute, form, subject, newSubject, project, envOID, cohortOID, projectSiteOID, now)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 创建发药访视周期（只有发药项目才需要设置访视周期）
		if attribute.AttributeInfo.Dispensing {
			if visitCycle.Infos == nil || len(visitCycle.Infos) == 0 {
				return nil, tools.BuildServerError(ctx, "subject_no_visit")
			}
			dispensingVisit := make([]interface{}, len(visitCycle.Infos))
			serialNumber := 100
			var reasons []models.Reason
			for index, visit := range visitCycle.Infos {
				dispensingVisit[index] = models.Dispensing{
					ID:            primitive.NewObjectID(),
					CustomerID:    subjectData.CustomerID,
					ProjectID:     subjectData.ProjectID,
					EnvironmentID: subjectData.EnvironmentID,
					CohortID:      subjectData.CohortID,
					SubjectID:     result.InsertedID.(primitive.ObjectID),
					VisitInfo: models.VisitInfo{
						VisitCycleInfoID: visit.ID,
						Number:           visit.Number,
						InstanceRepeatNo: "0",
						BlockRepeatNo:    "0",
						Name:             visit.Name,
						Random:           visit.Random,
						Dispensing:       visit.Dispensing,
					},
					SerialNumber: serialNumber,
					VisitSign:    false,
					Status:       1,
					Reasons:      reasons,
				}
				serialNumber += 100
			}

			// 批量添加发药访视计划
			if _, err := tools.Database.Collection("dispensing").InsertMany(sctx, dispensingVisit); err != nil {
				return nil, errors.WithStack(err)
			}
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return "", "", err
	}

	//添加app发药通知 fixme 仅发药不需要通知？
	if attribute.AttributeInfo.Dispensing && !roleOId.IsZero() {
		err = PatchAppDispenseTask(ctx, subjectOID.Hex(), roleOId.Hex())
		if err != nil {
			return "", "", err
		}
	}

	//TODO: 推给Rave EDC
	if project.ProjectInfo.EdcSupplier == 2 {
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectRandomPush(logData, subjectOID, 1, now, "", "", "")
	}

	// 推给EDC
	if tools.PushScenarioFilter(project.ProjectInfo.ConnectEdc, project.ProjectInfo.PushMode, project.ProjectInfo.EdcSupplier, project.ProjectInfo.PushScenario.RegisterPush) {
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectRandomPush(logData, subjectOID, 1, createdAt, "", "", "")
	}
	//if project.ProjectInfo.ConnectEdc == 1 && project.ProjectInfo.PushMode == 2 && !tools.IrtPushEdcRegisterIsolation(project.ProjectInfo.Number) {
	//	SubjectRandomPush(ctx, subjectOID, 1, createdAt, "", "")
	//}
	return subjectOID.Hex(), subject["shortname"].(string), nil
}

func factorCalc(fields []models.Field, factor models.RandomFactor, subject map[string]interface{}, ctx *gin.Context) ([]models.Field, error) {
	calcFormFields := slice.Filter(fields, func(index int, item models.Field) bool {
		return item.ApplicationType != nil && (*item.ApplicationType == 1 || *item.ApplicationType == 4)
	})
	re := regexp.MustCompile(`\{([\x{4e00}-\x{9fa5}\w]+)\}`)
	customFormulas := factor.CustomFormulas
	matches := re.FindAllStringSubmatch(customFormulas, -1)
	fieldNames := slice.Map(matches, func(index int, item []string) string {
		return item[1]
	})
	customerFields := make([]models.CustomerFormula, 0)
	var findNames []string
	for _, fieldName := range fieldNames {
		if fieldName == "CurrentTime" {
			customerFields = append(customerFields, models.CustomerFormula{
				Key:   "{" + fieldName + "}",
				Value: float64(time.Now().Unix()) / (24 * 60 * 60),
			})
		} else {
			find, b := slice.Find(calcFormFields, func(index int, item models.Field) bool {
				return item.Variable == fieldName
			})
			if !b {
				return nil, tools.BuildServerError(ctx, "randomization_config_factor_not_calc_form")
			}
			findNames = append(findNames, find.Name)
			//区分必填和非必填，如果非必填的并且受试者未填值，就不进行计算
			str, ok := subject[find.Name].(string)
			if b && find.Required == false && (subject[find.Name] == nil || subject[find.Name] != nil && ok && str == "") {
				return fields, nil
			}
			if find.Type == "timePicker" || find.Type == "datePicker" {
				format := "YYYY-MM-DD"
				if find.Type == "timePicker" {
					format = "YYYY-MM-DD HH:mm:ss"
				}
				parse, err := time.Parse(tools.DateFormatParse(format), subject[find.Name].(string))
				if err != nil {
					return nil, errors.WithStack(err)
				}
				customerFields = append(customerFields, models.CustomerFormula{
					Key:   "{" + fieldName + "}",
					Value: float64(parse.Unix()) / (24 * 60 * 60),
				})
			} else if find.Type == "inputNumber" {
				customerFields = append(customerFields, models.CustomerFormula{
					Key:   "{" + fieldName + "}",
					Value: subject[find.Name].(float64),
				})
			}

		}

	}

	isSuccess, res := tools.CustomMultipleFormulaExecNoToLower(customFormulas, customerFields)
	if isSuccess {
		bmi := tools.TruncateDecimal(res, *factor.Precision, factor.Round)
		bmiString := strconv.FormatFloat(bmi, 'f', *factor.Precision, 64)
		matchOption := false
		for _, option := range factor.Options {
			b, err := tools.MatchRangeFormula(bmiString, *option.Formula)
			if err != nil {
				return nil, err
			}
			if b {
				subject[factor.Name] = option.Value
				fields = append(fields, models.Field{
					Name:     factor.Name,
					Label:    factor.Label,
					Options:  factor.Options,
					IsCalc:   factor.IsCalc,
					CalcType: factor.CalcType,
					Status:   factor.Status,
				})
				matchOption = true
				break
			}
		}
		if !matchOption {
			return nil, tools.BuildServerValidateError(ctx, []string{"factor_calc_not_match", "factor_not_match"}, findNames, tools.FactorCalcNotMatch)
		}
	} else {
		return nil, tools.BuildServerValidateError(ctx, []string{"factor_calc_not_match", "factor_not_match"}, findNames, tools.FactorCalcNotMatch)
	}
	return fields, nil
}

// 在随机项目 阶段发药完成后自动添加下一阶段的受试者
func DispensingAddSubject(ctx *gin.Context, sctx mongo.SessionContext, projectId primitive.ObjectID, subjectId primitive.ObjectID, edcUserName string) error {
	//TODO 在随机
	var project models.Project
	err := tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": projectId}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}
	// 查询受试者
	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectId}).Decode(&subject)
	if err != nil {
		return errors.WithStack(err)
	}
	cohort, exist := models.GetCohort(project, subject.EnvironmentID.Hex(), subject.CohortID.Hex())
	// 如果是在随机项目（最后一个访视发完药或者无效后需要自动创建下一阶段的受试者）
	if (project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number)) || (project.Type == 2 && exist && cohort.Type == 1) {
		user := models.User{}
		u, _ := ctx.Get("user")
		if u != nil {
			user = u.(models.User)
		}
		if project.ProjectInfo.ConnectEdc == 1 && project.ProjectInfo.PushMode == 1 && user.Name == "" { // EDC对接项目
			user.Name = edcUserName
		}

		// 查属性配置
		var attribute models.Attribute
		err = tools.Database.Collection("attribute").FindOne(nil, bson.M{"cohort_id": subject.CohortID}).Decode(&attribute)
		if err != nil {
			return errors.WithStack(err)
		}

		// 查询发药的访视
		var dispensingList []models.Dispensing
		opts := &options.FindOptions{
			Sort: bson.D{{"serial_number", 1}},
		}
		dispensingCursor, err := tools.Database.Collection("dispensing").Find(sctx, bson.M{"subject_id": subjectId}, opts)
		if err != nil {
			return errors.WithStack(err)
		}
		err = dispensingCursor.All(nil, &dispensingList)
		if err != nil {
			return errors.WithStack(err)
		}

		// 存在待确认、已确认、已运送 已申请 订单的访视 不进入下一个阶段
		count, err := tools.Database.Collection("medicine_order").CountDocuments(sctx, bson.M{"env_id": subject.EnvironmentID,
			"subject_id": subject.ID, "status": bson.M{"$in": bson.A{1, 2, 6, 7}}})

		_, canDispensing := slice.Find(dispensingList, func(index int, item models.Dispensing) bool {
			return item.Status == 1 && item.VisitInfo.Dispensing
		})

		// 没有可发放的访视了。登记下一阶段的受试者
		if dispensingList == nil || len(dispensingList) == 0 || dispensingList[(len(dispensingList)-1)].Status != 1 && count == 0 && !canDispensing {
			// 筛选环境
			c, _ := models.GetCohort(project, subject.EnvironmentID.Hex(), subject.CohortID.Hex())
			env, _ := models.GetEnv(project, subject.EnvironmentID.Hex())
			var chOID primitive.ObjectID
			if project.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
				secondCohort, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
					return !item.LastID.IsZero()
				})
				chOID = secondCohort.ID
			} else {
				chOID, _ = models.GetReRandomCohortSecondId(project, subject.EnvironmentID.Hex(), c.Name)
			}
			// 登记下一阶段的受试者
			if chOID != primitive.NilObjectID && chOID != subject.CohortID {
				var info []models.Info
				for _, si := range subject.Info {
					if si.Name == "shortname" {
						info = append(info, si)
					}
				}

				if subject.Group == "" {
					return tools.BuildServerError(ctx, "subject_cohort_last_group")
				}

				subjectData := models.Subject{
					ID:                     primitive.NewObjectID(),
					CustomerID:             subject.CustomerID,
					ProjectID:              subject.ProjectID,
					EnvironmentID:          subject.EnvironmentID,
					CohortID:               chOID,
					ProjectSiteID:          subject.ProjectSiteID,
					LastGroup:              subject.Group,
					Info:                   info,
					Status:                 1,
					PvUnblindingStatus:     0,
					UrgentUnblindingStatus: 0,
					Meta: models.Meta{
						CreatedAt: time.Duration(time.Now().Unix()),
						CreatedBy: user.ID,
					},
					UrgentUnblindingApprovals:   []models.UrgentUnblindingApproval{},
					PvUrgentUnblindingApprovals: []models.UrgentUnblindingApproval{},
				}

				if subject.RegisterGroup != "" { //登记过变更了组别
					subjectData.LastGroup = subject.RegisterGroup
				}

				// 如果开启了筛选流程
				if attribute.AttributeInfo.IsScreen {
					subjectData.Status = 7
					subjectData.IsScreen = subject.IsScreen
					subjectData.ScreenTime = subject.ScreenTime
					subjectData.ICFTime = subject.ICFTime
				}

				result, err := tools.Database.Collection("subject").InsertOne(sctx, subjectData)
				if err != nil {
					return errors.WithStack(err)
				}
				_, err = tools.Database.Collection("subject_shortname").InsertOne(sctx, models.SubjectShortname{
					EnvID:     subjectData.EnvironmentID,
					CohortID:  subjectData.CohortID,
					Shortname: subjectData.Info[0].Value.(string),
				})
				if err != nil {
					// 再随机项目 EDC访视外 重复插入提示已经进入第二阶段
					if edcUserName != "" {
						return tools.BuildServerError(ctx, "subject_status_no_dispensing")
					}
					return errors.WithStack(err)
				}

				// 创建发药访视周期（只有发药项目才需要设置访视周期）
				//查询项目属性
				filter := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": chOID}
				var attribute models.Attribute
				err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
				if err != nil {
					return errors.WithStack(err)
				}
				// 查询访视周期
				var visitCycle models.VisitCycle
				err = tools.Database.Collection("visit_cycle").FindOne(sctx, filter).Decode(&visitCycle)
				if err != nil && err != mongo.ErrNoDocuments {
					return errors.WithStack(err)
				}
				if attribute.AttributeInfo.Dispensing {
					if visitCycle.Infos == nil || len(visitCycle.Infos) == 0 {
						return tools.BuildServerError(ctx, "subject_no_visit")
					}
					dispensingVisit := make([]interface{}, len(visitCycle.Infos))
					serialNumber := 100
					var reasons []models.Reason
					for index, visit := range visitCycle.Infos {
						dispensingVisit[index] = models.Dispensing{
							ID:            primitive.NewObjectID(),
							CustomerID:    subjectData.CustomerID,
							ProjectID:     subjectData.ProjectID,
							EnvironmentID: subjectData.EnvironmentID,
							CohortID:      subjectData.CohortID,
							SubjectID:     result.InsertedID.(primitive.ObjectID),
							VisitInfo: models.VisitInfo{
								VisitCycleInfoID: visit.ID,
								Number:           visit.Number,
								InstanceRepeatNo: "0",
								BlockRepeatNo:    "0",
								Name:             visit.Name,
								Random:           visit.Random,
								Dispensing:       visit.Dispensing,
							},
							SerialNumber: serialNumber,
							VisitSign:    false,
							Status:       1,
							Reasons:      reasons,
						}
						serialNumber += 100
					}

					// 批量添加发药访视计划
					if _, err := tools.Database.Collection("dispensing").InsertMany(sctx, dispensingVisit); err != nil {
						return errors.WithStack(err)
					}
				}
			}
		}
	}
	return nil
}

// cohort项目仅发药,判断入组已满时，登记发过药/筛选成功之后不允许登记/筛选
// sign 0登记 1筛选成功
func JustDispensingEnrollmentFull(ctx *gin.Context, sctx mongo.SessionContext, project models.Project, attribute models.Attribute, filter bson.M, env models.Environment, cohort models.Cohort, sign int) error {
	if !attribute.AttributeInfo.Random && attribute.AttributeInfo.Dispensing {

		needCapacity := false
		alertThresholdP := &models.AlertThreshold{}
		isEnrollment := false
		if project.ProjectInfo.Type == 1 {
			alertThresholdP, needCapacity = slice.Find(env.AlertThresholds, func(index int, item models.AlertThreshold) bool {
				if sign == 0 {
					return item.Type == 1
				} else if sign == 1 {
					return item.Type == 7
				}
				return false
			})
			if !needCapacity {
				alertThresholdP, needCapacity = slice.Find(env.AlertThresholds, func(index int, item models.AlertThreshold) bool {
					return item.Type == 3
				})
				if needCapacity {
					isEnrollment = true
				}
			}
		} else {
			alertThresholdP, needCapacity = slice.Find(cohort.AlertThresholds, func(index int, item models.AlertThreshold) bool {
				if sign == 0 {
					return item.Type == 1
				} else if sign == 1 {
					return item.Type == 7
				}
				return false
			})
			if !needCapacity {
				alertThresholdP, needCapacity = slice.Find(cohort.AlertThresholds, func(index int, item models.AlertThreshold) bool {
					return item.Type == 3
				})
				if needCapacity {
					isEnrollment = true
				}
			}
		}
		isStatus := false
		if project.ProjectInfo.Type == 1 {
			if env.Status != nil {
				isStatus = *env.Status == 5
			}
		} else {
			isStatus = cohort.Status == 5
		}
		count := 0
		var err error
		if needCapacity {
			if isEnrollment {
				count, err = tools.CountSubject(sctx, attribute, filter)
				if err != nil {
					return errors.WithStack(err)
				}
			} else {
				countFilter := bson.M{
					"project_id": project.ID,
					"env_id":     env.ID,
					"deleted":    bson.M{"$ne": true},
				}
				if cohort.ID != primitive.NilObjectID {
					countFilter["cohort_id"] = cohort.ID
				}
				if sign == 0 {
					countFilter["status"] = bson.M{"$ne": 5}
				} else if sign == 1 {
					countFilter["is_screen"] = true
				}
				c, err := tools.Database.Collection("subject").CountDocuments(sctx, countFilter)
				if err != nil {
					return errors.WithStack(err)
				}
				count = int(c)
			}
		}

		if isStatus || (needCapacity && count >= alertThresholdP.Capacity) {
			if isEnrollment {
				if sign == 0 {
					return tools.BuildServerError(ctx, "subject.register.enrollment.full")
				} else if sign == 1 {
					return tools.BuildServerError(ctx, "subject.register.screen.fail.enrollment.full")
				}
			} else {
				if sign == 0 {
					return tools.BuildServerError(ctx, "planned_case_register_error")
				} else if sign == 1 {
					return tools.BuildServerError(ctx, "planned_case_screen_error")
				}
			}

		}
	} else {
		//校验入组上限
		needCapacity := false
		alertThresholdP := &models.AlertThreshold{}
		if project.ProjectInfo.Type == 1 {
			alertThresholdP, needCapacity = slice.Find(env.AlertThresholds, func(index int, item models.AlertThreshold) bool {
				if sign == 0 {
					return item.Type == 1
				} else if sign == 1 {
					return item.Type == 7
				}
				return false
			})
		} else {
			alertThresholdP, needCapacity = slice.Find(cohort.AlertThresholds, func(index int, item models.AlertThreshold) bool {
				if sign == 0 {
					return item.Type == 1
				} else if sign == 1 {
					return item.Type == 7
				}
				return false
			})
		}
		if needCapacity {
			countFilter := bson.M{
				"project_id": project.ID,
				"env_id":     env.ID,
				"deleted":    bson.M{"$ne": true},
			}
			if cohort.ID != primitive.NilObjectID {
				countFilter["cohort_id"] = cohort.ID
			}
			if sign == 0 {
				countFilter["status"] = bson.M{"$ne": 5}
			} else if sign == 1 {
				countFilter["is_screen"] = true
			}
			count, err := tools.Database.Collection("subject").CountDocuments(sctx, countFilter)
			if err != nil {
				return errors.WithStack(err)
			}
			plannedCase := int64(alertThresholdP.Capacity)
			if count >= plannedCase {
				if sign == 0 {
					return tools.BuildServerError(ctx, "planned_case_register_error")
				} else if sign == 1 {
					return tools.BuildServerError(ctx, "planned_case_screen_error")
				}

			}
		}
	}
	return nil
}

func getSubjectNumber(ctx *gin.Context, projectOID primitive.ObjectID, envOID primitive.ObjectID, siteOID primitive.ObjectID, attributeInfo models.AttributeInfo) (string, error) {
	var subjectNumber string
	siteNumber := ""
	//受试者号位数限制
	length := attributeInfo.Digit
	//受试者号前缀
	prefixExpression := attributeInfo.PrefixExpression
	var projectSite models.ProjectSite
	err := tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": siteOID}).Decode(&projectSite)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if projectSite.Number != "" {
		siteNumber = projectSite.Number
	}

	if len(prefixExpression) > 0 {
		if strings.Contains(prefixExpression, "{siteNO}") {
			prefixExpression = strings.Replace(prefixExpression, "{siteNO}", siteNumber, 1)
		}
		length = length - len(prefixExpression)
	}

	filter := bson.M{"project_id": projectOID, "env_id": envOID, "deleted": bson.M{"$ne": true}}

	if attributeInfo.SubjectNumberRule == 3 {
		filter["project_site_id"] = siteOID
	}
	var data []struct {
		Number string `bson:"number"`
	}

	infoMatch := bson.M{"$expr": bson.M{"$eq": bson.A{bson.M{"$strLenCP": "$info.value"}, attributeInfo.Digit}}}
	if attributeInfo.Prefix && len(prefixExpression) > 0 {
		infoMatch = bson.M{"$expr": bson.M{"$eq": bson.A{bson.M{"$strLenCP": "$info.value"}, attributeInfo.Digit}}, "info.value": bson.M{"$regex": "^" + prefixExpression}}
	}

	pipepine := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$unwind", Value: "$info"}},
		{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
		{{Key: "$match", Value: infoMatch}},
		{{Key: "$sort", Value: bson.D{{"info.value", -1}}}},
		{{Key: "$limit", Value: 1}},
		{{Key: "$project", Value: bson.M{
			"number": "$info.value",
		}}},
	}
	cursor, err := tools.Database.Collection("subject").Aggregate(nil, pipepine)
	if err != nil {
		return "", errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return "", errors.WithStack(err)
	}

	lengthStr := strconv.Itoa(length)
	if data != nil {
		maxNumber := data[0].Number[len(prefixExpression):]
		re := regexp.MustCompile(`\d+`)
		match := re.FindString(maxNumber)
		max, _ := strconv.Atoi(match)
		number := strconv.Itoa(max + 1)
		if len(prefixExpression) <= 0 {
			prefixExpression = maxNumber[:len(maxNumber)-len(match)]
			lengthStr = strconv.Itoa(length - len(prefixExpression))
		}
		if len(number) > length {
			return "", tools.BuildServerError(ctx, "subject_register_number_fail")
		} else {
			subjectNumber = fmt.Sprintf("%s%0"+lengthStr+"s", prefixExpression, number)
		}
	} else {
		subjectNumber = fmt.Sprintf("%s%0"+lengthStr+"s", prefixExpression, "1")
	}

	return subjectNumber, nil
}

// 随机前保存分层因素数据
func SaveFactors(ctx *gin.Context, subject map[string]interface{}, sign int, ssctx mongo.SessionContext, edcSign int) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		if ssctx != nil {
			sctx = ssctx
		}
		ID, _ := primitive.ObjectIDFromHex(subject["id"].(string))
		subjectFilter := bson.M{"_id": ID}
		var findSubject models.Subject
		err := tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&findSubject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if findSubject.Status != 1 && findSubject.Status != 2 && findSubject.Status != 7 {
			return nil, tools.BuildServerError(ctx, "subject_status_no_update")
		}

		projectFilter := bson.M{"_id": findSubject.ProjectID}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(sctx, projectFilter).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//查询条件
		filter := bson.M{"customer_id": findSubject.CustomerID, "project_id": findSubject.ProjectID, "env_id": findSubject.EnvironmentID}
		if findSubject.CohortID != primitive.NilObjectID {
			filter = bson.M{"customer_id": findSubject.CustomerID, "project_id": findSubject.ProjectID, "env_id": findSubject.EnvironmentID, "cohort_id": findSubject.CohortID}
		}

		// 查询项目属性
		var attribute models.Attribute
		tools.Database.Collection("attribute").FindOne(sctx, filter).Decode(&attribute)

		var form models.Form
		tools.Database.Collection("form").FindOne(sctx, filter).Decode(&form)

		randomFilter := bson.M{
			"customer_id": findSubject.CustomerID,
			"project_id":  findSubject.ProjectID,
			"env_id":      findSubject.EnvironmentID,
			"status":      1,
			"$or": bson.A{
				bson.M{"site_ids": nil},
				bson.M{"site_ids": findSubject.ProjectSiteID},
			}}
		if findSubject.CohortID != primitive.NilObjectID {
			randomFilter = bson.M{
				"customer_id": findSubject.CustomerID,
				"project_id":  findSubject.ProjectID,
				"env_id":      findSubject.EnvironmentID,
				"status":      1,
				"cohort_id":   findSubject.CohortID,
				"$or": bson.A{
					bson.M{"site_ids": nil},
					bson.M{"site_ids": findSubject.ProjectSiteID},
				}}
		}
		c, exist := models.GetCohort(project, findSubject.EnvironmentID.Hex(), findSubject.CohortID.Hex())
		var lastSubject models.Subject
		if (project.Type == 3 || (project.Type == 2 && exist && c.Type == 1)) && subject["lastId"] != nil {
			lastOID, _ := primitive.ObjectIDFromHex(subject["lastId"].(string))
			if lastOID != primitive.NilObjectID {
				subjectFilter := bson.M{
					"customer_id": findSubject.CustomerID,
					"project_id":  findSubject.ProjectID,
					"env_id":      findSubject.EnvironmentID,
					"cohort_id":   lastOID,
					"info": bson.M{
						"$elemMatch": bson.M{
							"name":  "shortname",
							"value": subject["shortname"],
						},
					},
					"deleted": bson.M{"$ne": true},
				}
				err = tools.Database.Collection("subject").FindOne(sctx, subjectFilter).Decode(&lastSubject)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if lastSubject.Group == "" {
					return nil, tools.BuildServerError(ctx, "subject_cohort_last_group")
				}
				randomFilter = bson.M{
					"customer_id": findSubject.CustomerID,
					"project_id":  findSubject.ProjectID,
					"env_id":      findSubject.EnvironmentID,
					"status":      1,
					"cohort_id":   findSubject.CohortID,
					"last_group":  lastSubject.Group,
					"$or": bson.A{
						bson.M{"site_ids": nil},
						bson.M{"site_ids": findSubject.ProjectSiteID},
					}}
			}
		}

		var randomList models.RandomList
		err = tools.Database.Collection("random_list").FindOne(nil, randomFilter).Decode(&randomList)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}

		// 数据整合
		//var info []models.Info
		var subjectInfo = findSubject.Info
		oldSubjectInfo := make([]models.Info, len(findSubject.Info))
		copy(oldSubjectInfo, findSubject.Info)
		for _, factor := range randomList.Design.Factors {
			if factor.Status == nil || *factor.Status == 1 {
				if factor.IsCalc {
					_, err = factorCalc(form.Fields, factor, subject, ctx)
					if err != nil {
						return nil, err
					}
				}
				// 标记数据库是否存在分层因素字段，如果为false表示没有则追加subjectInfo  true表示有则修改subjectInfo
				var bl = false
				for i := 0; i < len(subjectInfo); i++ {
					if factor.Name == subjectInfo[i].Name {
						subjectInfo[i].Value = subject[factor.Name] // 修改subjectInfo
						bl = true
						break
					}
				}

				if !bl { // 追加
					subjectInfo = append(subjectInfo, models.Info{
						Name:  factor.Name,
						Value: subject[factor.Name],
					})
				}
			}
		}

		if (project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) || project.ProjectInfo.Type == 2 && exist && c.Type == 1) && subject["lastId"] != "000000000000000000000000" {
			for _, field := range form.Fields {
				if field.Status == nil || *field.Status == 1 {
					subjectInfo = append(subjectInfo, models.Info{
						Name:  field.Name,
						Value: subject[field.Name],
					})
				}
			}
		} else {
			// 随机前发药的项目随机时也会编辑表单
			for _, field := range form.Fields {
				if field.Status == nil || *field.Status == 1 {
					for j := 0; j < len(subjectInfo); j++ {
						if subjectInfo[j].Name == field.Name {
							subjectInfo[j].Value = subject[field.Name]
						}
					}
				}
			}
		}

		subjectUpdate := bson.M{"$set": bson.M{"info": subjectInfo}}
		if _, err = tools.Database.Collection("subject").UpdateOne(sctx, bson.M{"_id": ID}, subjectUpdate); err != nil {
			return nil, errors.WithStack(err)
		}

		var bf bytes.Buffer
		// 在随机保存轨迹
		if (project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number)) || (project.Type == 2 && exist && c.Type == 1) {
			for _, field := range form.Fields {
				if field.Status == nil || *field.Status == 1 {
					bf.WriteString(field.Label)
					bf.WriteString(":")
					if field.Type == "select" || field.Type == "radio" { // 下拉框或者单选框
						otherValue := "-"
						if subject[field.Name] != nil {
							for _, option := range field.Options {
								if option.Value == subject[field.Name].(string) {
									otherValue = option.Label
								}
							}
						}
						bf.WriteString(otherValue)
						bf.WriteString(",")
					} else if field.Type == "checkbox" { // 多选框
						otherValue := "-"
						if subject[field.Name] != nil {
							var checkboxBf bytes.Buffer
							str := subject[field.Name].([]interface{})
							for _, option := range field.Options {
								for j := 0; j < len(str); j++ {
									if option.Value == str[j].(string) {
										checkboxBf.WriteString(option.Label)
										checkboxBf.WriteString(",")
									}
								}
							}
							otherValue = checkboxBf.String()
						}
						bf.WriteString(otherValue)
					} else if field.Type == "switch" { // 开关
						otherValue := "no"
						if subject[field.Name] != nil {
							if subject[field.Name] == true {
								otherValue = "yes"
							}
						}
						bf.WriteString(otherValue)
						bf.WriteString(",")
					} else if field.Type == "inputNumber" { // 其它
						otherValue := "-"
						if subject[field.Name] != nil {
							otherValue = convertor.ToString(subject[field.Name])
						}
						bf.WriteString(otherValue)
						bf.WriteString(",")
					} else if field.Type == "datePicker" { // 日期选择器
						otherValue := "-"
						dateFormat := "YYYY-MM-DD"
						if field.DateFormat != "" {
							dateFormat = field.DateFormat
						}
						if subject[field.Name] != nil {
							otherValue = fmt.Sprint(subject[field.Name])
							if subject[field.Name] != "" {
								parse, err := time.Parse("2006-01-02", otherValue)
								if err != nil {
									return false, errors.WithStack(err)
								}
								otherValue = parse.Format(tools.DateFormatParse(dateFormat))
							}
						}
						bf.WriteString(otherValue)
						bf.WriteString(",")
					} else if field.Type == "timePicker" {
						otherValue := "-"
						timeFormat := "YYYY-MM-DD HH:mm:ss"
						if field.TimeFormat != "" {
							timeFormat = field.TimeFormat
						}
						if subject[field.Name] != nil {
							otherValue = fmt.Sprint(subject[field.Name])
							if subject[field.Name] != "" {
								parse, err := time.Parse("2006-01-02 15:04:05", otherValue)
								if err != nil {
									return false, errors.WithStack(err)
								}
								otherValue = parse.Format(tools.DateFormatParse(timeFormat))
							}
						}
						bf.WriteString(otherValue)
						bf.WriteString(",")
					} else {
						otherValue := "-"
						if subject[field.Name] != nil {
							otherValue = fmt.Sprintf("%v", subject[field.Name])
						}
						bf.WriteString(otherValue)
						bf.WriteString(",")
					}
				}
			}
		}

		//	添加轨迹
		if randomList.Design.Factors != nil && len(randomList.Design.Factors) > 0 {
			user := models.User{}
			u, _ := ctx.Get("user")
			if u != nil {
				user = u.(models.User)
			}

			if sign == 1 {
				for _, fs := range randomList.Design.Factors {
					bf.WriteString(fs.Label)
					bf.WriteString(":")
					otherValue := "-"
					if subject[fs.Name] != nil {
						for _, option := range fs.Options {
							if option.Value == subject[fs.Name].(string) {
								otherValue = option.Label
							}
						}
					}
					bf.WriteString(otherValue)
					bf.WriteString(";")
				}
			} else if sign == 0 {
				//如果EDC先登记再随机，登记的分层与随机的分层不一样，以随机的为准，并记录修改后的分层轨迹
				for _, fs := range randomList.Design.Factors {
					if fs.Status == nil || *fs.Status == 1 {
						findP, b := slice.Find(oldSubjectInfo, func(index int, item models.Info) bool {
							return item.Name == fs.Name
						})
						h := false
						if b {
							info := *findP
							if info.Value != subject[info.Name].(string) {
								h = true
							}
						} else {
							h = true
						}
						otherValue := "-"
						if subject[fs.Name] != nil && h {
							for _, option := range fs.Options {
								if option.Value == subject[fs.Name].(string) {
									otherValue = option.Label
									break
								}
							}
							bf.WriteString(fs.Label)
							bf.WriteString(":")
							bf.WriteString(otherValue)
							bf.WriteString(";")
						}
					}
				}
			}

			// 判断是否是EDC对接项目
			userName := ""
			if edcSign == 1 {
				userName = "EDC"
				if subject["edcUserName"] != nil {
					userName = subject["edcUserName"].(string)
				}
			} else {
				me, err := tools.Me(ctx)
				if err != nil {
					return nil, err
				}
				userName = me.Name
			}

			key := "history.subject.update"
			stage := ""

			// 在随机
			if (project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number)) || (project.Type == 2 && exist && c.Type == 1) {
				key = "history.subject.at-random-update"
				if subject["lastId"] != "000000000000000000000000" {
					key = "history.subject.at-random-random"
				}
				if findSubject.CohortID != primitive.NilObjectID {
					for _, env := range project.Environments {
						if env.ID == findSubject.EnvironmentID {
							for _, cohort := range env.Cohorts {
								if cohort.ID == findSubject.CohortID {
									stage = cohort.Name
									if cohort.Type == 1 {
										stage = cohort.ReRandomName
									}
									break
								}
							}
							break
						}
					}
				}
			}

			content := bf.String()
			if len(bf.String()) > 0 {
				content = strings.TrimRight(content, ",")
				history := models.History{
					Key:  key,
					OID:  ID,
					Data: map[string]interface{}{"content": content, "stage": stage},
					Time: time.Duration(time.Now().Unix()),
					UID:  user.ID,
					User: userName,
				}
				_, err := tools.Database.Collection("history").InsertOne(sctx, history)
				if err != nil {
					return false, errors.WithStack(err)
				}
			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

/**
 * 添加受试者时添加轨迹
 */
func SubjectTrail(sctx mongo.SessionContext, sign int, form models.Form, info []models.Info, subject map[string]interface{}, user models.User, subjectID primitive.ObjectID, project models.Project, now time.Duration, attribute models.Attribute) (bool, error) {
	// 创建轨迹
	var bf bytes.Buffer
	infoNames := slice.Map(info, func(index int, item models.Info) string {
		return item.Name
	})
	form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
		return (slice.Contain(infoNames, item.Name) && subject[item.Name] != nil) || item.Status == nil || *item.Status == 1
	})
	for i := 0; i < len(form.Fields); i++ {
		if form.Fields[i].IsCalc == true {
			bf.WriteString(form.Fields[i].Label)
			bf.WriteString(":")
			option, b := slice.Find(form.Fields[i].Options, func(index int, item models.Option) bool {
				return item.Value == subject[form.Fields[i].Name]
			})
			if b {
				bf.WriteString(option.Label)
				bf.WriteString(",")
			}
		} else {
			bf.WriteString(form.Fields[i].Label)
			bf.WriteString(":")
			if form.Fields[i].Type == "select" || form.Fields[i].Type == "radio" { // 下拉框或者单选框
				otherValue := "-"
				if subject[form.Fields[i].Name] != nil {
					for _, option := range form.Fields[i].Options {
						if option.Value == subject[form.Fields[i].Name].(string) {
							otherValue = option.Label
						}
					}
				}
				bf.WriteString(otherValue)
				bf.WriteString(",")
			} else if form.Fields[i].Type == "checkbox" { // 多选框
				otherValue := "-"
				if subject[form.Fields[i].Name] != nil {
					var checkboxBf bytes.Buffer
					str := subject[form.Fields[i].Name].([]interface{})
					for _, option := range form.Fields[i].Options {
						for j := 0; j < len(str); j++ {
							if option.Value == str[j].(string) {
								checkboxBf.WriteString(option.Label)
								checkboxBf.WriteString(",")
							}
						}
					}
					otherValue = checkboxBf.String()
				}
				bf.WriteString(otherValue)
			} else if form.Fields[i].Type == "switch" { // 开关
				otherValue := "no"
				if subject[form.Fields[i].Name] != nil {
					if subject[form.Fields[i].Name] == true {
						otherValue = "yes"
					}
				}
				bf.WriteString(otherValue)
				bf.WriteString(",")
			} else if form.Fields[i].Type == "inputNumber" { // 其它
				otherValue := "-"
				if subject[form.Fields[i].Name] != nil {
					if form.Fields[i].Type == "inputNumber" && form.Fields[i].FormatType == "decimalLength" && form.Fields[i].Length != nil {
						lengthString := strconv.FormatFloat(*form.Fields[i].Length, 'f', -1, 64)
						if strings.Contains(lengthString, ".") {
							digits, _ := getFractionDigits(*form.Fields[i].Length)
							str := strconv.FormatFloat(subject[form.Fields[i].Name].(float64), 'f', digits, 64)
							otherValue = str
						} else {
							otherValue = convertor.ToString(subject[form.Fields[i].Name])
						}
					} else {
						otherValue = convertor.ToString(subject[form.Fields[i].Name])
					}
				}
				bf.WriteString(otherValue)
				bf.WriteString(",")
			} else if form.Fields[i].Type == "datePicker" { // 日期选择器
				otherValue := "-"
				dateFormat := "YYYY-MM-DD"
				if form.Fields[i].DateFormat != "" {
					dateFormat = form.Fields[i].DateFormat
				}
				if subject[form.Fields[i].Name] != nil {
					otherValue = fmt.Sprint(subject[form.Fields[i].Name])
					if subject[form.Fields[i].Name] != "" {
						parse, err := time.Parse("2006-01-02", otherValue)
						if err != nil {
							return false, errors.WithStack(err)
						}
						otherValue = parse.Format(tools.DateFormatParse(dateFormat))
					}
				}
				bf.WriteString(otherValue)
				bf.WriteString(",")
			} else if form.Fields[i].Type == "timePicker" {
				otherValue := "-"
				timeFormat := "YYYY-MM-DD HH:mm:ss"
				if form.Fields[i].TimeFormat != "" {
					timeFormat = form.Fields[i].TimeFormat
				}
				if subject[form.Fields[i].Name] != nil {
					otherValue = fmt.Sprint(subject[form.Fields[i].Name])
					if subject[form.Fields[i].Name] != "" {
						parse, err := time.Parse("2006-01-02 15:04:05", otherValue)
						if err != nil {
							return false, errors.WithStack(err)
						}
						otherValue = parse.Format(tools.DateFormatParse(timeFormat))
					}
				}
				bf.WriteString(otherValue)
				bf.WriteString(",")
			} else {
				otherValue := "-"
				if subject[form.Fields[i].Name] != nil {
					otherValue = fmt.Sprintf("%v", subject[form.Fields[i].Name])
				}
				bf.WriteString(otherValue)
				bf.WriteString(",")
			}
		}
	}

	// 判断是否是EDC对接项目
	var userName string
	if project.ProjectInfo.ConnectEdc == 1 && project.ProjectInfo.PushMode == 1 { // EDC对接项目
		userName = "EDC"
		if subject["edcUserName"] != nil {
			userName = subject["edcUserName"].(string)
		}
	} else {
		userName = user.Name
	}
	content := bf.String()
	content = strings.TrimRight(content, ",")

	key := ""
	parKey := ""
	isScreen, isScreenExist := subject["isScreen"]
	signOutRealTime, signOutRealTimeExist := subject["signOutRealTime"]
	if signOutRealTimeExist && signOutRealTime == nil {
		signOutRealTime = ""
	}
	signOutReason, signOutReasonExists := subject["reason"].(string)
	finishRemark, finishRemarkExists := subject["finishRemark"].(string)
	tempOptions := make([]models.CustomTempOption, 0)
	//是否筛选成功：{{.isScreen}}，筛选时间：{{.screenTime}}，ICF签署时间：{{.icfTime}}，实际停用时间：{{.signOutRealTime}}
	screenTime := ""
	if subject["screenTime"] != nil {
		screenTime = subject["screenTime"].(string)
	}
	icfTime := ""
	if subject["icfTime"] != nil {
		icfTime = subject["icfTime"].(string)
	}
	reason := ""
	if subject["reason"] != nil {
		reason = signOutReason
	}

	remark := ""
	if subject["finishRemark"] != nil {
		remark = finishRemark
	}

	stage := ""
	have := models.HaveCohortReRandom(project, attribute.EnvironmentID.Hex())
	c, b := models.GetCohort(project, attribute.EnvironmentID.Hex(), attribute.CohortID.Hex())
	if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number) {
		stage = c.Name
	} else if project.ProjectInfo.Type == 2 && have && b && c.Type == 1 {
		stage = c.ReRandomName
	}

	if sign == 1 { // 添加轨迹
		if (project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number)) || (project.ProjectInfo.Type == 2 && have && b && c.Type == 1) {
			key = "history.subject.at-random-add"
		} else {
			key = "history.subject.add"
		}
	} else { // 修改轨迹
		if (project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number)) || (project.ProjectInfo.Type == 2 && have && b && c.Type == 1) {
			key = "history.subject.label.at-random-update"
		} else {
			key = "history.subject.label.update"
		}
		parKey = "updateFields"
		index := 0
		if isScreenExist || signOutRealTimeExist || signOutReasonExists || finishRemarkExists {
			key = "history.subject.label.updateCustomize"
			if attribute.AttributeInfo.IsScreen {
				if isScreenExist {
					tempOptions = append(tempOptions, models.CustomTempOption{
						Index: index,
						Key:   "history.subject.label.updateCustomizeIsScreen",
						Data: map[string]interface{}{
							"isScreen": isScreen.(bool),
						},
					})
					index++
					tempOptions = append(tempOptions, models.CustomTempOption{
						Index: index,
						Key:   "history.subject.label.updateCustomizeScreenTime",
						Data: map[string]interface{}{
							"screenTime": screenTime,
						},
					})
					index++
					tempOptions = append(tempOptions, models.CustomTempOption{
						Index: index,
						Key:   "history.subject.label.updateCustomizeIcfTime",
						Data: map[string]interface{}{
							"icfTime": icfTime,
						},
					})
					index++

				}
			}
			if signOutRealTimeExist {
				tempOptions = append(tempOptions, models.CustomTempOption{
					Index: index,
					Key:   "history.subject.label.updateCustomizeSignOutRealTime",
					Data: map[string]interface{}{
						"signOutRealTime": signOutRealTime,
					},
				})
				index++
			}
			if signOutReasonExists {
				tempOptions = append(tempOptions, models.CustomTempOption{
					Index: index,
					Key:   "history.subject.label.updateCustomizeReason",
					Data: map[string]interface{}{
						"reason": reason,
					},
				})
				index++
			}
			if finishRemarkExists {
				tempOptions = append(tempOptions, models.CustomTempOption{
					Index: index,
					Key:   "history.subject.label.updateCustomizeRemark",
					Data: map[string]interface{}{
						"remark": remark,
					},
				})
				index++
			}
		}

	}

	temps := []models.CustomTemp{
		{
			ParKey:              parKey,
			ConnectingSymbolKey: "history.subject.label.updateCustomizeConnectingSymbol",
			LastSymbolKey:       "history.subject.label.updateCustomizeLastSymbolKey",
			CustomTempOptions:   tempOptions,
		},
	}
	history := models.History{
		Key:         key,
		OID:         subjectID,
		CustomTemps: temps,
		Data: map[string]interface{}{
			"content": content,
			"label":   attribute.AttributeInfo.SubjectReplaceText,
			"name":    subject["shortname"],
			"stage":   stage,
		},
		Time: now,
		UID:  user.ID,
		User: userName,
	}
	_, err := tools.Database.Collection("history").InsertOne(sctx, history)
	if err != nil {
		return false, errors.WithStack(err)
	}
	return true, nil
}

func QueryDrugCount(ctx *gin.Context, drugName string, subject models.Subject, researchAttribute int, storehouseID primitive.ObjectID, isOtherDrugMap map[string]bool) (int, error) {
	var count int = 0
	//判断是否是未编码药物
	isOtherDrug, _ := isOtherDrugMap[drugName]
	if isOtherDrug {
		filter := bson.M{}
		if researchAttribute == 1 { //DTD
			filter = bson.M{
				"customer_id":   subject.CustomerID,
				"project_id":    subject.ProjectID,
				"env_id":        subject.EnvironmentID,
				"storehouse_id": storehouseID,
				//"info.name":     drugName,
				"name":   drugName,
				"status": 1,
			}
		} else {
			//如果已配置，判断该中心药物数量
			filter = bson.M{
				"customer_id": subject.CustomerID,
				"project_id":  subject.ProjectID,
				"env_id":      subject.EnvironmentID,
				"site_id":     subject.ProjectSiteID,
				//"info.name":    drugName,
				"name":   drugName,
				"status": 1,
			}
		}
		// group := bson.M{
		// 	"_id":   "$info.name",
		// 	"count": bson.M{"$sum": "$info.count"},
		// }

		// var otherMedicine []map[string]interface{}
		// pipeline := mongo.Pipeline{
		// 	{{Key: "$match", Value: filter}},
		// 	{{Key: "$group", Value: group}},
		// }
		// cursor, err := tools.Database.Collection("medicine_other_institute").Aggregate(nil, pipeline)
		// if err != nil {
		// 	return count, errors.WithStack(err)
		// }
		// err = cursor.All(nil, &otherMedicine)
		// if err != nil {
		// 	return count, errors.WithStack(err)
		// }
		// if len(otherMedicine) > 0 {
		// 	count = int(otherMedicine[0]["count"].(int32))
		// }
		var hasOtherMedicines []models.OtherMedicine
		cursor, err := tools.Database.Collection("medicine_others").Find(nil, filter)
		err = cursor.All(nil, &hasOtherMedicines)
		if err != nil {
			return count, errors.WithStack(err)
		}
		if hasOtherMedicines == nil || len(hasOtherMedicines) > 0 {
			count = len(hasOtherMedicines)
		}
	} else {
		filter := bson.M{}
		if researchAttribute == 1 { //DTD
			filter = bson.M{
				"status":        1,
				"customer_id":   subject.CustomerID,
				"project_id":    subject.ProjectID,
				"env_id":        subject.EnvironmentID,
				"storehouse_id": storehouseID,
				"name":          drugName,
			}
		} else {
			//如果已配置，判断该中心药物数量
			filter = bson.M{
				"status":      1,
				"customer_id": subject.CustomerID,
				"project_id":  subject.ProjectID,
				"env_id":      subject.EnvironmentID,
				"site_id":     subject.ProjectSiteID,
				"name":        drugName,
			}
		}
		var hasMedicines []models.Medicine
		cursor, err := tools.Database.Collection("medicine").Find(nil, filter)
		err = cursor.All(nil, &hasMedicines)
		if err != nil {
			return count, errors.WithStack(err)
		}
		if hasMedicines == nil || len(hasMedicines) > 0 {
			count = len(hasMedicines)
		}
	}
	return count, nil
}

func removeElement(arr []string, element string) []string {
	var result []string
	for _, value := range arr {
		if value != element {
			result = append(result, value)
		}
	}
	return result
}

func CheckRandomControl(ctx *gin.Context, randomControlRule int, randomControlGroup int, visitCycle models.VisitCycle, newGroups []string, subject models.Subject, researchAttribute int, storehouseID primitive.ObjectID, group string) (bool, []string, error) {
	canUseGroup := make([]string, 0)

	filter := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
	if subject.CohortID != primitive.NilObjectID {
		filter = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
	}

	isOtherDrugMap, err := tools.IsOtherDrugMap(subject.EnvironmentID)
	if err != nil {
		return false, nil, errors.WithStack(err)
	}

	//判断研究产品是否配置,在DTP模式下，兼容考虑库房/中心有药的场景
	var data []map[string]interface{}
	//所有分组，有供应后允许随机：是指所有的分组，随机后的首次发药访视的最大药物量，足够，才允许随机
	if randomControlRule == 1 {
		//研究产品配置
		var drugConfigure models.DrugConfigure
		err := tools.Database.Collection("drug_configure").FindOne(ctx, filter).Decode(&drugConfigure)
		if err != nil {
			return false, nil, tools.BuildServerError(ctx, "subject_no_drug_configure")
		}

		groupConfigures := make(map[string][]models.DrugConfigureInfo)
		for _, configure := range drugConfigure.Configures {
			//不是 按公式计算的时候
			if configure.OpenSetting != 3 {
				value, oK := groupConfigures[configure.Group]
				if !oK {
					value = append(value, configure)
					groupConfigures[configure.Group] = value
				}
			} else {
				//去掉这个组别的判断
				newGroups = removeElement(newGroups, configure.Group)
			}
		}
		randomVisitCycle := false
		for _, visitCycleInfo := range visitCycle.Infos {
			if visitCycleInfo.Random {
				randomVisitCycle = true
			}
			if randomVisitCycle && visitCycleInfo.Dispensing && len(newGroups) > 0 { //随机后首次访视
				//未匹配中的组别
				unGroups := make([]string, 0)
				for _, group := range newGroups {
					var groupVal interface{}
					groupVal = group
					ok := slice.Contain(visitCycleInfo.Group, groupVal)
					if ok {
						//根据组别和访视，查询研究产品配置
						groupConfigure, exist := groupConfigures[group]
						if exist {
							drugConfigure := make(map[string]int, 0)
							for _, configureInfo := range groupConfigure {
								contain := slice.Contain(configureInfo.VisitCycles, visitCycleInfo.ID)
								if contain {
									for _, v := range configureInfo.Values {
										drugC, drugOk := drugConfigure[v.DrugName]
										if drugOk {
											if drugC < v.DispensingNumber {
												drugConfigure[v.DrugName] = v.DispensingNumber
											}
										} else {
											drugConfigure[v.DrugName] = v.DispensingNumber
										}
									}
								}
							}
							//判断数量是否足够
							for drugName, dispensingNumber := range drugConfigure {
								//判断访视是否是dtp，如果是dtp，是否包含仓库
								if visitCycleInfo.DTP {
									index := arrays.Contains(visitCycleInfo.DTPType, 2)
									if index != -1 { //存在
										storehouselCount, _ := QueryDrugCount(ctx, drugName, subject, 1, storehouseID, isOtherDrugMap)
										if storehouselCount < dispensingNumber {
											return false, nil, tools.BuildServerError(ctx, "subject_no_enough_drug")
										}
									}
								}
								//判断是否dtp，并且只选择了仓库发药，如果是，只判断仓库库存，否则还需要判断中心库存
								if visitCycleInfo.DTP && len(visitCycleInfo.DTPType) == 1 && visitCycleInfo.DTPType[0] == 2 {

								} else {
									totalCount, _ := QueryDrugCount(ctx, drugName, subject, researchAttribute, storehouseID, isOtherDrugMap)
									if totalCount < dispensingNumber {
										return false, nil, tools.BuildServerError(ctx, "subject_no_enough_drug")
									}
								}
							}
						} else {
							return false, nil, tools.BuildServerError(ctx, "subject_no_drug_configure")
						}
					} else {
						unGroups = append(unGroups, group)
					}
				}
				newGroups = unGroups
			}
		}
	} else if randomControlRule == 2 { //已分配分组，有供应后允许随机：预计分配的组别中药物库存数>首次访视发药的最大剂量数(绑定的发药标签中最大的计量数 )
		//获取随机后的访视
		var visitCycleId []primitive.ObjectID
		var visitcycleInfo models.VisitCycleInfo
		randomVisitCycle := false
		for _, visitCycleInfo := range visitCycle.Infos {
			if visitCycleInfo.Random {
				randomVisitCycle = true
			}
			var groupVal interface{}
			groupVal = group
			groupExist := slice.Contain(visitCycleInfo.Group, groupVal)
			if randomVisitCycle && visitCycleInfo.Dispensing && groupExist { //随机后group的首次发药访视
				visitcycleInfo = visitCycleInfo
				visitCycleId = append(visitCycleId, visitCycleInfo.ID)
				break
			}
		}

		if len(visitCycleId) > 0 {
			project := bson.M{
				"_id":                                 0,
				"configures.open_setting":             1,
				"configures.id":                       1,
				"configures.group":                    1,
				"configures.values.dispensing_number": 1,
				"configures.values.drugname":          1,
			}
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: filter}},
				{{Key: "$unwind", Value: "$configures"}},
				{{Key: "$match", Value: bson.M{"configures.group": group, "configures.visit_cycles": bson.M{"$in": visitCycleId}}}},
				{{Key: "$project", Value: project}},
			}
			cursor, err := tools.Database.Collection("drug_configure").Aggregate(nil, pipeline)
			if err != nil {
				return false, nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &data)
			if err != nil {
				return false, nil, errors.WithStack(err)
			}

			if data != nil && len(data) > 0 {
				groupDrugNames := make(map[string]int)
				for _, value := range data {
					openSetting := value["configures"].(map[string]interface{})["open_setting"].(int32)
					if openSetting != 3 {
						for _, item := range value["configures"].(map[string]interface{})["values"].(primitive.A) {
							drugName := item.(map[string]interface{})["drugname"].(string)
							dispensingNumber := int(item.(map[string]interface{})["dispensing_number"].(int32))
							if groupDrugNames[drugName] == 0 {
								groupDrugNames[drugName] = dispensingNumber
							} else {
								if groupDrugNames[drugName] < dispensingNumber {
									groupDrugNames[drugName] = dispensingNumber
								}
							}
						}
					} else {
						//只要有公式计算的，都可以随机
						return true, nil, nil
					}
				}
				//判断研究产品数量是否足够
				for drugName, dispensingNumber := range groupDrugNames {
					//判断访视是否是dtp，如果是dtp，是否包含仓库
					if visitcycleInfo.DTP {
						index := arrays.Contains(visitcycleInfo.DTPType, 2)
						if index != -1 { //存在
							storehouselCount, _ := QueryDrugCount(ctx, drugName, subject, 1, storehouseID, isOtherDrugMap)
							if storehouselCount < dispensingNumber {
								return false, nil, tools.BuildServerError(ctx, "subject_no_enough_drug")
							}
						}
					}
					//判断是否dtp，并且只选择了仓库发药，如果是，只判断仓库库存，否则还需要判断中心库存
					if visitcycleInfo.DTP && len(visitcycleInfo.DTPType) == 1 && visitcycleInfo.DTPType[0] == 2 {

					} else {
						totalCount, _ := QueryDrugCount(ctx, drugName, subject, researchAttribute, storehouseID, isOtherDrugMap)
						if totalCount < dispensingNumber {
							return false, nil, tools.BuildServerError(ctx, "subject_no_enough_drug")
						}
					}
				}
			} else {
				return false, nil, tools.BuildServerError(ctx, "subject_no_drug_configure")
			}
		}
	} else if randomControlRule == 3 { //强制随机到有供应的分组
		var drugConfigure models.DrugConfigure
		err := tools.Database.Collection("drug_configure").FindOne(ctx, filter).Decode(&drugConfigure)
		if err != nil {
			return false, nil, tools.BuildServerError(ctx, "subject_no_drug_configure")
		}
		groupConfigures := make(map[string][]models.DrugConfigureInfo)
		for _, configure := range drugConfigure.Configures {
			if configure.OpenSetting != 3 {
				value, oK := groupConfigures[configure.Group]
				if !oK {
					value = append(value, configure)
					groupConfigures[configure.Group] = value
				}
			} else {
				canUseGroup = append(canUseGroup, configure.Group)
				//去掉这个组别的判断
				newGroups = removeElement(newGroups, configure.Group)
			}
		}
		randomVisitCycle := false
		for _, visitCycleInfo := range visitCycle.Infos {
			if visitCycleInfo.Random {
				randomVisitCycle = true
			}
			if randomVisitCycle && visitCycleInfo.Dispensing && len(newGroups) > 0 { //随机后首次访视
				//未匹配中的组别
				unGroups := make([]string, 0)
				for _, group := range newGroups {
					var groupVal interface{}
					groupVal = group
					ok := slice.Contain(visitCycleInfo.Group, groupVal)
					if ok {
						//根据组别和访视，查询研究产品配置
						groupConfigure, exist := groupConfigures[group]
						if exist {
							drugConfigure := make(map[string]int, 0)
							for _, configureInfo := range groupConfigure {
								contain := slice.Contain(configureInfo.VisitCycles, visitCycleInfo.ID)
								if contain {
									for _, v := range configureInfo.Values {
										drugC, drugOk := drugConfigure[v.DrugName]
										if drugOk {
											if drugC < v.DispensingNumber {
												drugConfigure[v.DrugName] = v.DispensingNumber
											}
										} else {
											drugConfigure[v.DrugName] = v.DispensingNumber
										}
									}
								}
							}
							//判断数量是否足够
							canUse := true
							for drugName, dispensingNumber := range drugConfigure {
								//判断访视是否是dtp，如果是dtp，是否包含仓库
								if visitCycleInfo.DTP {
									index := arrays.Contains(visitCycleInfo.DTPType, 2)
									if index != -1 { //存在
										storehouselCount, _ := QueryDrugCount(ctx, drugName, subject, 1, storehouseID, isOtherDrugMap)
										if storehouselCount < dispensingNumber {
											canUse = false
										}
									}
								}
								//判断是否dtp，并且只选择了仓库发药，如果是，只判断仓库库存，否则还需要判断中心库存
								if visitCycleInfo.DTP && len(visitCycleInfo.DTPType) == 1 && visitCycleInfo.DTPType[0] == 2 {
								} else {
									totalCount, _ := QueryDrugCount(ctx, drugName, subject, researchAttribute, storehouseID, isOtherDrugMap)
									if totalCount < dispensingNumber {
										canUse = false
									}
								}
							}
							if canUse {
								canUseGroup = append(canUseGroup, group)
							}
						}
					} else {
						unGroups = append(unGroups, group)
					}
				}
				newGroups = unGroups
			}
		}
		if len(canUseGroup) < randomControlGroup {
			return false, nil, tools.BuildServerError(ctx, "subject_no_enough_drug")
		}
	}

	return true, canUseGroup, nil
}

/**
 * 查询受试者分层因素是否达到预计录入人数或者警戒值
 * sign(0/1) 标记校验预计录入人数还是警戒值(0:预计录入人数 / 1:警戒值)
 */
func CheckSubject(sctx mongo.SessionContext, randomList models.RandomList, subject models.Subject, sign int) (bool, string, error) {

	// 如果有分层因素需要检测分层因素是否达到预计录入人数(添加前)
	checkBoolean := false
	var s string
	factors := randomList.Design.Factors
	factors = slice.Filter(factors, func(index int, item models.RandomFactor) bool {
		if item.Status == nil {
			return true
		}
		return *item.Status != 2
	})
	if factors != nil && len(factors) > 0 {
		// 查询条件
		filter := bson.M{"customer_id": randomList.CustomerID, "project_id": randomList.ProjectID, "env_id": randomList.EnvironmentID, "random_list_id": randomList.ID, "status": bson.M{"$in": bson.A{3, 4, 6, 9, 5}}} // 状态 已随机 已退出 已揭盲 完成研究 已停用
		if randomList.CustomerID != primitive.NilObjectID {
			filter = bson.M{"customer_id": randomList.CustomerID, "project_id": randomList.ProjectID, "env_id": randomList.EnvironmentID, "cohort_id": randomList.CohortID, "random_list_id": randomList.ID, "status": bson.M{"$in": bson.A{3, 4, 6, 9, 5}}} // 状态 已随机 已退出 已揭盲 完成研究 已停用
		}
		var subjectList []models.Subject
		opts := &options.FindOptions{
			Projection: bson.M{
				"info": 1,
			},
		}
		rlsCursor, err := tools.Database.Collection("subject").Find(sctx, filter, opts)
		if err != nil {
			return false, "", errors.WithStack(err)
		}
		if err = rlsCursor.All(sctx, &subjectList); err != nil {
			return false, "", errors.WithStack(err)
		}
		// 循环分层因素
		factorCodes := make(map[string]string, 0)
		// 获取当前分层因素的code
		for _, subjectFactorCode := range subject.Info {
			factorCode := ""
			for _, factor := range factors {
				if subjectFactorCode.Name == factor.Name {
					if subjectFactorCode.Value != nil {
						factorCode = subjectFactorCode.Value.(string)
						factorCodes[subjectFactorCode.Name] = factorCode
					}
					break
				}
			}
		}
		// 查询设置的预计录入人数
		for _, factorCombination := range randomList.Design.Combination {
			number := 0
			checkFactor := true
			for _, layeredFactor := range factorCombination.LayeredFactors {
				factor, ok := factorCodes[layeredFactor.Name]
				if ok && layeredFactor.Value == factor {
					checkFactor = true
				} else {
					checkFactor = false
					break
				}
			}
			if checkFactor {
				if sign == 0 {
					number = factorCombination.EstimateNumber
				} else {
					number = factorCombination.WarnNumber
				}
				if number > 0 {
					// 检查当前已经入组的人数
					nowNumber := 0
					for _, randomListSubject := range subjectList {
						subjectCheck := true
						for _, layeredFactor := range factorCombination.LayeredFactors {
							factorCheck := false
							for _, subjectFactorCode := range randomListSubject.Info {
								if layeredFactor.Name == subjectFactorCode.Name && (subjectFactorCode.Value != nil && subjectFactorCode.Value.(string) == layeredFactor.Value) {
									factorCheck = true
									break
								}
							}
							if !factorCheck {
								subjectCheck = false
								break
							}
						}
						if subjectCheck {
							nowNumber++
						}
					}

					// 检查录入人数是否达到设置的人数
					if number <= nowNumber {
						// 查询code对应的文本
						label := ""
						for index, layeredFactor := range factorCombination.LayeredFactors {
							if index == len(factorCombination.LayeredFactors)-1 {
								label = label + layeredFactor.Label + "(" + layeredFactor.Text + ")"
							} else {
								label = label + layeredFactor.Label + "(" + layeredFactor.Text + ")" + "、"
							}
						}
						s = fmt.Sprintf("[%s/%v/%v]", label, number, nowNumber)
						checkBoolean = true
					}
				}
			}

		}

	}
	return checkBoolean, s, nil
}

// 判断当前项目登记受试者的时候是否需要填写分层因素（true不需要，false需要）
func LayeredBl(attribute models.Attribute, visitCycle models.VisitCycle, factors []models.RandomFactor) bool {
	if attribute.AttributeInfo.Dispensing && len(visitCycle.Infos) > 0 && !visitCycle.Infos[0].Random && len(factors) > 0 {
		return true
	}
	return false
}

func sendSubjectAddMail(ctx *gin.Context, sctx mongo.SessionContext, attribute models.Attribute, form models.Form, subject map[string]interface{}, newSubject models.Subject, project models.Project, envOID primitive.ObjectID, cohortOID primitive.ObjectID, projectSiteOID primitive.ObjectID, now time.Duration) error {
	subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
	subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)
	var forms []map[string]interface{}
	form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
		return item.Status == nil || *item.Status == 1
	})
	for i := 1; i < len(form.Fields); i++ {
		if form.Fields[i].IsCalc == true {
			option, b := slice.Find(form.Fields[i].Options, func(index int, item models.Option) bool {
				return item.Value == subject[form.Fields[i].Name]
			})
			if b {
				forms = append(forms, map[string]interface{}{"field": form.Fields[i].Label, "value": option.Label})
			}
		} else {
			otherValue := "-"
			if form.Fields[i].Type == "select" || form.Fields[i].Type == "radio" { // 下拉框或者单选框
				if subject[form.Fields[i].Name] != nil {
					for _, option := range form.Fields[i].Options {
						if option.Value == subject[form.Fields[i].Name].(string) {
							otherValue = option.Label
						}
					}
				}
			} else if form.Fields[i].Type == "checkbox" { // 多选框
				if subject[form.Fields[i].Name] != nil {
					var checkboxBf bytes.Buffer
					str := subject[form.Fields[i].Name].([]interface{})
					for _, option := range form.Fields[i].Options {
						for j := 0; j < len(str); j++ {
							if option.Value == str[j].(string) {
								checkboxBf.WriteString(option.Label)
								if j < len(str)-1 {
									checkboxBf.WriteString(",")
								}
							}
						}
					}
					otherValue = checkboxBf.String()
				}
			} else if form.Fields[i].Type == "switch" { // 开关
				otherValue = "no"
				if subject[form.Fields[i].Name] != nil {
					if subject[form.Fields[i].Name] == true {
						otherValue = "yes"
					}
				}
			} else if form.Fields[i].Type == "inputNumber" { // 其它
				if subject[form.Fields[i].Name] != nil {
					if form.Fields[i].FormatType == "decimalLength" && form.Fields[i].Length != nil {
						lengthString := strconv.FormatFloat(*form.Fields[i].Length, 'f', -1, 64)
						if strings.Contains(lengthString, ".") {
							digits, _ := getFractionDigits(*form.Fields[i].Length)
							str := strconv.FormatFloat(subject[form.Fields[i].Name].(float64), 'f', digits, 64)
							otherValue = str
						} else {
							otherValue = convertor.ToString(subject[form.Fields[i].Name])
						}
					} else {
						otherValue = convertor.ToString(subject[form.Fields[i].Name])
					}
				}
			} else if form.Fields[i].Type == "datePicker" { // 日期选择器
				dateFormat := "YYYY-MM-DD"
				if form.Fields[i].DateFormat != "" {
					dateFormat = form.Fields[i].DateFormat
				}
				if subject[form.Fields[i].Name] != nil {
					otherValue = fmt.Sprint(subject[form.Fields[i].Name])
					if subject[form.Fields[i].Name] != "" {
						parse, err := time.Parse("2006-01-02", otherValue)
						if err != nil {
							return errors.WithStack(err)
						}
						otherValue = parse.Format(tools.DateFormatParse(dateFormat))
					}
				}
			} else if form.Fields[i].Type == "timePicker" {
				timeFormat := "YYYY-MM-DD HH:mm:ss"
				if form.Fields[i].TimeFormat != "" {
					timeFormat = form.Fields[i].TimeFormat
				}
				if subject[form.Fields[i].Name] != nil {
					otherValue = fmt.Sprint(subject[form.Fields[i].Name])
					if subject[form.Fields[i].Name] != "" {
						parse, err := time.Parse("2006-01-02 15:04:05", otherValue)
						if err != nil {
							return errors.WithStack(err)
						}
						otherValue = parse.Format(tools.DateFormatParse(timeFormat))
					}
				}
			} else {
				if subject[form.Fields[i].Name] != nil {
					otherValue = subject[form.Fields[i].Name].(string)
				}
			}
			forms = append(forms, map[string]interface{}{"field": form.Fields[i].Label, "value": otherValue})
		}
	}
	userMail, err := tools.GetRoleUsersMailWithRole(project.ID, envOID, "notice.subject.add", projectSiteOID)
	if err != nil {
		return errors.WithStack(err)
	}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": projectSiteOID}).Decode(&projectSite)
	if err != nil {
		return errors.WithStack(err)
	}
	var mails []models.Mail
	//strTimeZone := projectSite.TimeZone
	strTimeZone, err := tools.GetSiteTimeZone(projectSiteOID)
	if err != nil {
		return errors.WithStack(err)
	}
	if strTimeZone == "" {
		timeZone, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
		strTimeZone = timeZone
	}
	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env := *envP
	var cohort models.Cohort
	if env.Cohorts != nil && len(env.Cohorts) > 0 {
		for _, co := range env.Cohorts {
			if co.ID == cohortOID {
				cohort = co
				break
			}
		}
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)
	hours := time.Duration(intTimeZone)
	minutes := time.Duration((intTimeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute
	registerTime := time.Unix(int64(now), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
	registerTime = registerTime + "(" + strTimeZone + ")"
	subjectData := bson.M{
		"projectNumber": project.Number,
		"envName":       env.Name,
		"siteNumber":    projectSite.Number,
		"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
		"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
		"subject":       subject["shortname"],
	}
	contentData := bson.M{
		"projectNumber": project.Number,
		"projectName":   project.Name,
		"envName":       env.Name,
		"siteNumber":    projectSite.Number,
		"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
		"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
		"registerTime":  registerTime,
		"results":       forms,
		"subject":       subject["shortname"],
		"label":         subjectEmailReplaceTextZh,
		"labelEn":       subjectEmailReplaceTextEn,
	}
	if !cohort.ID.IsZero() {
		contentData["cohortName"] = models.GetCohortReRandomName(cohort)
	}
	mailBodyContet, err := tools.MailBodyContent(nil, envOID, "notice.subject.add")
	for key, v := range mailBodyContet {
		contentData[key] = v
	}
	if err != nil {
		return errors.WithStack(err)
	}
	var noticeConfig models.NoticeConfig
	err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": envOID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	langList := make([]string, 0)
	html := "subject_add_new_zh_en.html"
	if noticeConfig.Manual != 0 {
		if noticeConfig.Manual == 1 {
			langList = append(langList, "zh")
			html = "subject_add_new_zh.html"
		} else if noticeConfig.Manual == 2 {
			langList = append(langList, "en")
			html = "subject_add_new_en.html"
		} else if noticeConfig.Manual == 3 {
			langList = append(langList, "zh")
			langList = append(langList, "en")
			html = "subject_add_new_zh_en.html"
		}
	} else {
		langList = append(langList, ctx.GetHeader("Accept-Language"))
		if locales.Lang(ctx) == "zh" {
			html = "subject_add_new_zh.html"
		} else if locales.Lang(ctx) == "en" {
			html = "subject_add_new_en.html"
		}
	}
	for _, email := range userMail {
		var toUserMail []string
		toUserMail = append(toUserMail, email.Email)
		mails = append(mails, models.Mail{
			ID:           primitive.NewObjectID(),
			Subject:      "subject.add.title",
			SubjectData:  subjectData,
			ContentData:  contentData,
			To:           toUserMail,
			Lang:         ctx.GetHeader("Accept-Language"),
			LangList:     langList,
			Status:       0,
			CreatedTime:  time.Duration(time.Now().Unix()),
			ExpectedTime: time.Duration(time.Now().Unix()),
			SendTime:     time.Duration(time.Now().Unix()),
			HTML:         html,
		})
	}
	html = "subject_limit_alert_register_zh_en.html"
	if noticeConfig.Manual != 0 {
		if noticeConfig.Manual == 1 {
			html = "subject_limit_alert_register_zh.html"
		} else if noticeConfig.Manual == 2 {
			html = "subject_limit_alert_register_en.html"
		} else if noticeConfig.Manual == 3 {
			html = "subject_limit_alert_register_zh_en.html"
		}
	} else {
		if locales.Lang(ctx) == "zh" {
			html = "subject_limit_alert_register_zh.html"
		} else if locales.Lang(ctx) == "en" {
			html = "subject_limit_alert_register_en.html"
		}
	}
	mails, err = alertThresholds(sctx, 1, project, env, cohort, attribute, newSubject, projectSite, ctx, html, mails)
	if err != nil {
		return err
	}
	ctx.Set("MAIL", mails)
	if len(mails) > 0 {
		var envs []models.MailEnv
		for _, m := range mails {
			envs = append(envs, models.MailEnv{
				ID:         primitive.NewObjectID(),
				MailID:     m.ID,
				CustomerID: project.CustomerID,
				ProjectID:  project.ID,
				EnvID:      envOID,
				CohortID:   cohortOID,
			})
		}
		ctx.Set("MAIL-ENV", envs)
	}
	return nil
}

func sendSubjectRandomMail(ctx *gin.Context, attribute models.Attribute, form models.Form, subject models.Subject, result models.RandomNumberReturn, project models.Project, envOID primitive.ObjectID, cohortOID primitive.ObjectID, projectSiteOID primitive.ObjectID, now time.Duration, randomList models.RandomList) ([]models.Mail, error) {

	//subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

	subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
	subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)

	repSubject := make(map[string]interface{}, 0)

	for _, v := range subject.Info {
		repSubject[v.Name] = v.Value
	}
	infoNames := slice.Map(subject.Info, func(index int, item models.Info) string {
		return item.Name
	})
	// 声明接受字段信息
	var fields []models.Field
	//只要有效或有值无效的
	form.Fields = slice.Filter(form.Fields, func(index int, item models.Field) bool {
		return (slice.Contain(infoNames, item.Name) && repSubject[item.Name] != nil) || item.Status == nil || *item.Status == 1
	})
	for _, field := range form.Fields {
		if field.Name != "shortname" {
			fields = append(fields, models.Field{
				Name:            field.Name,
				Label:           field.Label,
				Type:            field.Type,
				Options:         field.Options,
				DateFormat:      field.DateFormat,
				TimeFormat:      field.TimeFormat,
				FormatType:      field.FormatType,
				Length:          field.Length,
				ApplicationType: field.ApplicationType,
				Variable:        field.Variable,
			})
		}
	}

	factors := randomList.Design.Factors
	factors = slice.Filter(factors, func(index int, item models.RandomFactor) bool {
		return (slice.Contain(infoNames, item.Name) && repSubject[item.Name] != nil) || item.Status == nil || *item.Status == 1
	})

	for i := 0; i < len(factors); i++ {
		var err error
		if factors[i].IsCalc {
			fields, err = factorCalc(fields, factors[i], repSubject, ctx)
			if err != nil {
				return nil, err
			}
		} else {
			if repSubject[factors[i].Name] == nil {
				return nil, tools.BuildServerError(ctx, "subject_factor_no_null")
			}
			fields = append(fields, models.Field{
				Name:    factors[i].Name,
				Label:   factors[i].Label,
				Type:    factors[i].Type,
				Options: factors[i].Options,
			})
		}
	}

	form.Fields = fields

	var forms []map[string]interface{}

	for i := 0; i < len(form.Fields); i++ {
		if form.Fields[i].IsCalc == true {
			option, b := slice.Find(form.Fields[i].Options, func(index int, item models.Option) bool {
				return item.Value == repSubject[form.Fields[i].Name]
			})
			if b {
				forms = append(forms, map[string]interface{}{"field": form.Fields[i].Label, "value": option.Label})
			}
		} else {
			otherValue := "-"
			if form.Fields[i].Type == "select" || form.Fields[i].Type == "radio" { // 下拉框或者单选框
				if repSubject[form.Fields[i].Name] != nil {
					for _, option := range form.Fields[i].Options {
						if option.Value == repSubject[form.Fields[i].Name].(string) {
							otherValue = option.Label
						}
					}
				}
			} else if form.Fields[i].Type == "checkbox" { // 多选框
				if repSubject[form.Fields[i].Name] != nil {
					var checkboxBf bytes.Buffer
					str := repSubject[form.Fields[i].Name].(primitive.A)
					for _, option := range form.Fields[i].Options {
						for j := 0; j < len(str); j++ {
							if option.Value == str[j].(string) {
								checkboxBf.WriteString(option.Label)
								checkboxBf.WriteString(",")
							}
						}
					}
					otherValue = checkboxBf.String()
				}
			} else if form.Fields[i].Type == "switch" { // 开关
				otherValue = "no"
				if repSubject[form.Fields[i].Name] != nil {
					if repSubject[form.Fields[i].Name] == true {
						otherValue = "yes"
					}
				}
			} else if form.Fields[i].Type == "inputNumber" { // 其它
				if repSubject[form.Fields[i].Name] != nil {
					if form.Fields[i].FormatType == "decimalLength" && form.Fields[i].Length != nil {
						lengthString := strconv.FormatFloat(*form.Fields[i].Length, 'f', -1, 64)
						if strings.Contains(lengthString, ".") {
							digits, _ := getFractionDigits(*form.Fields[i].Length)
							str := strconv.FormatFloat(repSubject[form.Fields[i].Name].(float64), 'f', digits, 64)
							otherValue = str
						} else {
							otherValue = convertor.ToString(repSubject[form.Fields[i].Name])
						}
					} else {
						otherValue = convertor.ToString(repSubject[form.Fields[i].Name])
					}
				}
			} else if form.Fields[i].Type == "datePicker" && form.Fields[i].IsCalc == false { // 日期选择器
				dateFormat := "YYYY-MM-DD"
				if form.Fields[i].DateFormat != "" {
					dateFormat = form.Fields[i].DateFormat
				}
				if repSubject[form.Fields[i].Name] != nil {
					otherValue = fmt.Sprint(repSubject[form.Fields[i].Name])
					if repSubject[form.Fields[i].Name] != "" {
						parse, err := time.Parse("2006-01-02", otherValue)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						otherValue = parse.Format(tools.DateFormatParse(dateFormat))
					}
				}
			} else if form.Fields[i].Type == "timePicker" && form.Fields[i].IsCalc == false {
				timeFormat := "YYYY-MM-DD HH:mm:ss"
				if form.Fields[i].TimeFormat != "" {
					timeFormat = form.Fields[i].TimeFormat
				}
				if repSubject[form.Fields[i].Name] != nil {
					otherValue = fmt.Sprint(repSubject[form.Fields[i].Name])
					if repSubject[form.Fields[i].Name] != "" {
						parse, err := time.Parse("2006-01-02 15:04:05", otherValue)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						otherValue = parse.Format(tools.DateFormatParse(timeFormat))
					}
				}
			} else {
				if repSubject[form.Fields[i].Name] != nil {
					otherValue = repSubject[form.Fields[i].Name].(string)
				}
			}
			forms = append(forms, map[string]interface{}{"field": form.Fields[i].Label, "value": otherValue})
		}
	}

	userMail, err := tools.GetRoleUsersMailWithRole(project.ID, envOID, "notice.subject.random", projectSiteOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": projectSiteOID}).Decode(&projectSite)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var mails []models.Mail
	//strTimeZone := projectSite.TimeZone
	strTimeZone, err := tools.GetSiteTimeZone(projectSiteOID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if strTimeZone == "" {
		timeZone, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
		strTimeZone = timeZone
	}
	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env := *envP
	var cohort models.Cohort
	if env.Cohorts != nil && len(env.Cohorts) > 0 {
		for _, co := range env.Cohorts {
			if co.ID == cohortOID {
				cohort = co
				break
			}
		}
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)

	hours := time.Duration(intTimeZone)
	minutes := time.Duration((intTimeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute
	randomTime := time.Unix(int64(now), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
	randomTime = randomTime + "(" + strTimeZone + ")"
	subjectData := bson.M{
		"projectNumber": project.Number,
		"envName":       env.Name,
		"siteNumber":    projectSite.Number,
		"siteName":      tools.GetProjectSiteLangName(projectSite, "zh"),
		"siteNameEn":    tools.GetProjectSiteLangName(projectSite, "en"),
	}

	randomNumber := tools.BlindData
	randomNumberShow := false
	if attribute.AttributeInfo.IsRandomNumber {
		randomNumber = result.RandomNumber
		if len(randomNumber) > 0 {
			randomNumberShow = true
		}
	}
	randomSequenceNumber := ""
	randomSequenceNumberShow := false
	if attribute.AttributeInfo.IsRandomSequenceNumber {
		randomSequenceNumber = subject.RandomSequenceNumber
		if len(randomNumber) > 0 {
			randomSequenceNumberShow = true
		}
	}

	blind := attribute.AttributeInfo.Blind

	contentData := bson.M{
		"projectNumber":            project.Number,
		"projectName":              project.Name,
		"envName":                  env.Name,
		"siteNumber":               projectSite.Number,
		"siteName":                 tools.GetProjectSiteLangName(projectSite, "zh"),
		"siteNameEn":               tools.GetProjectSiteLangName(projectSite, "en"),
		"randomTime":               randomTime,
		"results":                  forms,
		"subject":                  subject.Info[0].Value,
		"label":                    subjectEmailReplaceTextZh,
		"labelEn":                  subjectEmailReplaceTextEn,
		"randomNumberShow":         randomNumberShow,
		"randomSequenceNumberShow": randomSequenceNumberShow,
		"randomSequenceNumber":     randomSequenceNumber,
		"randomNumber":             result.RandomNumber,
		"group":                    result.Group,
		"subGroup":                 result.SubName,
		"parName":                  result.ParName,
		"forms":                    forms,
	}
	if !cohort.ID.IsZero() {
		contentData["cohortName"] = models.GetCohortReRandomName(cohort)
	}

	if result.SubName != "" {
		groupP, b2 := slice.Find(randomList.Design.Groups, func(index int, item models.RandomListGroup) bool {
			return result.ParName == item.ParName && result.SubName == item.SubName
		})
		if b2 {
			contentData["subGroupBlind"] = groupP.Blind
		}
	}

	mailBodyContet, err := tools.MailBodyContent(nil, envOID, "notice.subject.random")
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for key, v := range mailBodyContet {
		contentData[key] = v
	}
	var noticeConfig models.NoticeConfig
	err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": env.ID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	langList := make([]string, 0)
	html := "subject_random_zh_en.html"
	if noticeConfig.Manual != 0 {
		if noticeConfig.Manual == 1 {
			langList = append(langList, "zh")
			html = "subject_random_zh.html"
		} else if noticeConfig.Manual == 2 {
			langList = append(langList, "en")
			html = "subject_random_en.html"
		} else if noticeConfig.Manual == 3 {
			langList = append(langList, "zh")
			langList = append(langList, "en")
			html = "subject_random_zh_en.html"
		}
	} else {
		langList = append(langList, ctx.GetHeader("Accept-Language"))
		if locales.Lang(ctx) == "zh" {
			html = "subject_random_zh.html"
		} else if locales.Lang(ctx) == "en" {
			html = "subject_random_en.html"
		}
	}
	for _, email := range userMail {
		insertData := make(map[string]interface{})
		for k, v := range contentData {
			insertData[k] = v
		}
		if email.IsBlind && blind {
			if result.Group != "" || (insertData["subGroup"] != nil && insertData["subGroup"] != "") {
				insertData["group"] = tools.BlindData
			}
		} else {
			if insertData["subGroup"] != nil && insertData["subGroup"] != "" {
				insertData["group"] = insertData["parName"]
			} else {
				insertData["group"] = result.Group
			}
		}
		if insertData["subGroupBlind"] != nil && insertData["subGroupBlind"].(bool) {
			if email.IsBlind {
				insertData["subGroup"] = tools.BlindData
			} else {
				insertData["subGroup"] = result.SubName
			}
		}
		var toUserMail []string
		toUserMail = append(toUserMail, email.Email)
		mails = append(mails, models.Mail{
			ID:           primitive.NewObjectID(),
			Subject:      "subject.random.title",
			SubjectData:  subjectData,
			ContentData:  insertData,
			To:           toUserMail,
			Lang:         ctx.GetHeader("Accept-Language"),
			LangList:     langList,
			Status:       0,
			CreatedTime:  time.Duration(time.Now().Unix()),
			ExpectedTime: time.Duration(time.Now().Unix()),
			SendTime:     time.Duration(time.Now().Unix()),
			HTML:         html,
		})
	}

	return mails, nil

}
