package service

import (
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type OperationLogService struct {
}

func (s *OperationLogService) List(ctx *gin.Context, id string, projectId string, envId string, cohortId string, roleId string, startTime int, endTime int, start int, limit int, attributeID string, keyword string, modules string, operatorID string) (map[string]interface{}, error) {
	OID, _ := primitive.ObjectIDFromHex(id)
	projectOId, _ := primitive.ObjectIDFromHex(projectId)
	envOId, _ := primitive.ObjectIDFromHex(envId)
	//cohortOId, _ := primitive.ObjectIDFromHex(cohortId)
	//attributeOID, _ := primitive.ObjectIDFromHex(attributeID)
	var operationLogs []models.OperationLogVo

	var project models.Project
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOId}).Decode(&project); err != nil {
		return nil, errors.WithStack(err)
	}
	filter := bson.M{"oid": OID}
	//人员管理特殊处理，查询客户用户的数据
	if strings.Contains(modules, "operation_log.module.user") {
		filter = bson.M{"oid": bson.M{"$in": primitive.A{OID, project.CustomerID}}}
	}
	//查询出所有的cohortId

	cohortNames := make(map[primitive.ObjectID]string, 0)
	var attributeData []models.Attribute
	match := bson.M{"env_id": envOId}
	if project.Type != 1 {
		for _, environment := range project.Environments {
			if environment.ID == envOId {
				cohortIds := make([]primitive.ObjectID, 0)
				for _, cohort := range environment.Cohorts {
					cohortIds = append(cohortIds, cohort.ID)
					cohortNames[cohort.ID] = models.GetCohortReRandomName(cohort)
				}
				match = bson.M{"cohort_id": bson.M{"$in": cohortIds}}
				cohortIds = append(cohortIds, envOId)

				filter = bson.M{"oid": bson.M{"$in": cohortIds}}
				if strings.Contains(modules, "operation_log.module.user") {
					ids := make([]primitive.ObjectID, 0)
					for _, objectID := range cohortIds {
						ids = append(ids, objectID)
					}
					ids = append(ids, project.CustomerID)
					filter = bson.M{"oid": bson.M{"$in": ids}}
				}
				break
			}
		}
	}

	cursor, err := tools.Database.Collection("attribute").Find(nil, match)
	if err != nil {
		return nil, err
	}
	err = cursor.All(nil, &attributeData)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	attributes := make(map[primitive.ObjectID]models.Attribute, 0)
	for _, v := range attributeData {
		if project.Type != 1 {
			attributes[v.CohortID] = v
		} else {
			attributes[v.EnvironmentID] = v
		}
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: filter}},
		{{Key: "$lookup", Value: bson.M{
			"from":         "user",
			"localField":   "operator",
			"foreignField": "_id",
			"as":           "user",
		}}},
		{{"$unwind", "$user"}},
		//{{Key: "$unwind", Value: bson.M{
		//	"path":                       "$user",
		//	"preserveNullAndEmptyArrays": true,
		//}}},
		{{"$sort", bson.D{{"time", -1}, {"_id", -1}}}},
	}
	if startTime != 0 {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"time": bson.M{"$gt": startTime}}}})
	}
	if endTime != 0 {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"time": bson.M{"$lt": endTime}}}})
	}
	if keyword != "" {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{
			"$or": bson.A{
				bson.M{"user.info.name": bson.M{"$regex": keyword, "$options": "im"}},
				bson.M{"user.info.email": bson.M{"$regex": keyword, "$options": "im"}},
			},
		}}})
	}
	if len(modules) > 0 {
		pipeline = append(pipeline, bson.D{{"$match", bson.M{"module": bson.M{"$in": strings.Split(modules, ",")}}}})
	}
	if operatorID != "" {
		operatorOID, _ := primitive.ObjectIDFromHex(operatorID)

		if strings.Contains(modules, "operation_log.module.user") {
			var userProjectEnv models.UserProjectEnvironment
			err = tools.Database.Collection("user_project_environment").FindOne(nil, bson.M{"_id": operatorOID}).Decode(&userProjectEnv)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			pipeline = append(pipeline, bson.D{{"$match", bson.M{"operatorId": bson.M{"$in": bson.A{operatorOID, userProjectEnv.UserID}}}}})
		} else {
			pipeline = append(pipeline, bson.D{{"$match", bson.M{"operatorId": operatorOID}}})
		}
	}
	var counts []map[string]interface{}
	pipeline = append(pipeline, bson.D{{"$count", "count"}})

	cursor, err = tools.Database.Collection("operation_log").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &counts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	count := int32(0)
	if len(counts) > 0 {
		count = counts[0]["count"].(int32)

	}

	pipeline[len(pipeline)-1] = bson.D{{"$skip", start}}
	pipeline = append(pipeline, bson.D{{"$limit", limit}})
	cursor, err = tools.Database.Collection("operation_log").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &operationLogs)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	RetOperationLogs := []models.RetOperationLog{}

	isBlind, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	isRoomBlind, isRoomErr := tools.IsBlindedRoomRole(roleId)
	if isRoomErr != nil {
		return nil, errors.WithStack(isRoomErr)
	}
	ctx.Set("isRoomBlind", isRoomBlind)

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	ctx.Set("isBlindedRole", isBlindedRole)
	//查询子组别
	_, sub := slice.Find(operationLogs, func(index int, item models.OperationLogVo) bool {
		_, b := slice.Find(item.Fields, func(index int, it models.OperationLogFieldGroup) bool {
			return slice.Contain([]string{
				"operation_log.drug_configure.subGroup",
				"operation_log.visitCycle.group",
				"operation_log.random_design.group_name",
			}, it.TranKey)
		})
		return b
	})
	if sub {
		filter := bson.M{"env_id": envOId}
		// if !cohortOId.IsZero() {
		// 	filter["cohort_id"] = cohortOId
		// }
		var randomDesigns []models.RandomDesign
		cursor, err := tools.Database.Collection("random_design").Find(nil, filter)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &randomDesigns)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		subBlinds := make(map[primitive.ObjectID][]models.GroupBlind)
		for _, randomDesign := range randomDesigns {
			blinds := make([]models.GroupBlind, 0)
			for _, group := range randomDesign.Info.Groups {
				if group.SubGroup != nil && len(group.SubGroup) > 0 {
					for _, subGroup := range group.SubGroup {
						blinds = append(blinds, models.GroupBlind{
							Group:    group.Name + " " + subGroup.Name,
							ParName:  group.Name,
							SubName:  subGroup.Name,
							SubBlind: subGroup.Blind,
						})
					}
				}
			}
			if project.Type != 1 {
				subBlinds[randomDesign.CohortID] = blinds
			} else {
				subBlinds[randomDesign.EnvironmentID] = blinds
			}
		}

		ctx.Set("subGroupBlind", subBlinds)
	}

	//查询项目下的药物配置中的盲态药物
	// JIRA3036 查询开放药物配置 未编号如果是开放药物不打码，是盲态药物 根据角色判断是否打码
	isBlindDrugMap, err := tools.IsBlindDrugMap(envOId)
	if err != nil {
		return map[string]interface{}{
			"items": RetOperationLogs,
			"total": count,
		}, errors.WithStack(err)
	}
	user, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	//num := int32(0)
	for _, log := range operationLogs {
		var context []string
		secondaryMenuName := ""
		labelName := ""
		oldValue := ""
		newValue := ""
		cohortName := ""
		var attribute models.Attribute
		if project.Type != 1 {
			if cohortNames[log.OID] != "" {
				cohortName = "-" + cohortNames[log.OID]
			}
			attribute = attributes[log.OID]
		} else {
			attribute = attributes[envOId]
		}

		if attribute.AttributeInfo.Blind {
			ctx.Set("isBlind", isBlind)
		} else {
			isBlind = false
			ctx.Set("isBlind", isBlind)
		}
		context = append(context, locales.Tr(ctx, log.Module)+cohortName)

		b := false
		if log.Fields != nil && len(log.Fields) > 0 {
			_, b = slice.Find(log.Fields, func(index int, item models.OperationLogFieldGroup) bool {
				return item.TranKey == "operation_log.project_storehouse.onlyID" || item.TranKey == "operation_log.drug_configure.onlyID" ||
					item.TranKey == "operation_log.uploadMedicines.onlyID" ||
					//item.TranKey == "operation_log.random_list.onlyID" ||
					item.TranKey == "operation_log.simulateRandom.onlyID"
			})
		}
		if !b {
			for _, mark := range log.Mark {
				if mark.Label != "" {
					value := mark.Value
					if mark.Label == "operation_log.label.otherMedicines" ||
						mark.Label == "operation_log.label.uploadMedicine" ||
						mark.Label == "operation_log.label.medicine" ||
						mark.Label == "operation_log.project_storehouse.medicine_name" {
						if isBlindDrugMap[value] && isBlindedRole {
							value = tools.BlindData
						}
					} else {
						if mark.Blind && isBlind {
							value = tools.BlindData
						}
					}
					if log.Module != "operation_log.module.subjects" {
						context = append(context, locales.Tr(ctx, mark.Label)+" : "+value)
					}
				}
			}
		}

		lang := locales.Lang(ctx)
		for _, field := range log.Fields {
			var tmp string
			secondaryMenuName = locales.Tr(ctx, field.Key)
			labelName = locales.Tr(ctx, field.TranKey)

			if field.Old.Type != 3 {
				if field.Old.Type == 16 {
					oldValue = converRoles(ctx, field.Old.Value)
				} else if field.Old.Type == 17 || field.Old.Type == 18 ||
					field.Old.Type == 19 || field.Old.Type == 20 || field.Old.Type == 22 || field.Old.Type == 23 || field.Old.Type == 24 || field.Old.Type == 25 {
					oldValue = converContentConfiguration(ctx, field.Old.Type, field.Old.Value)
				} else if field.Old.Type == 21 {
					value := field.Old.Value
					oldValue = strconv.Itoa(int(value.(int32)))
				} else if field.Old.Type == 30 {
					if lang == "zh" {
						oldValue = models.TypeHandle(ctx, 7, field.Old.Value, isBlindDrugMap, log.OID, field.TranKey)
					} else {
						oldValue = models.TypeHandle(ctx, 7, field.Old.ENValue, isBlindDrugMap, log.OID, field.TranKey)
					}
				} else {
					if field.TranKey == "operation_log.form.format" {
						if field.Old.Value == "form.control.type.format.numberLength" {
							oldValue = locales.Tr(ctx, "form.control.type.format.numberLength")
						} else if field.Old.Value == "form.control.type.format.decimalLength" {
							oldValue = locales.Tr(ctx, "form.control.type.format.decimalLength")
						} else {
							oldValue = models.TypeHandle(ctx, field.Old.Type, field.Old.Value, isBlindDrugMap, log.OID, field.TranKey)
						}
					} else if field.TranKey == "operation_log.form.options" {
						oldValue = converOptionValue(ctx, field.Old.Value)
					} else if field.TranKey == "operation_log.drug_configure_setting.visitJudgment" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentName" {
						oldValue = converDefaultValue(ctx, field.Old.Value)
					} else if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
						oldValue = models.TypeHandle(ctx, 7, field.Old.Value, isBlindDrugMap, log.OID, field.TranKey)
					} else {
						oldValue = models.TypeHandle(ctx, field.Old.Type, field.Old.Value, isBlindDrugMap, log.OID, field.TranKey)
					}
				}
			} else {
				oldValue = converCountry(ctx, field.Old.Value)
			}
			if field.New.Type != 3 {
				if field.New.Type == 16 {
					newValue = converRoles(ctx, field.New.Value)
				} else if field.New.Type == 17 || field.New.Type == 18 ||
					field.New.Type == 19 || field.New.Type == 20 || field.New.Type == 22 || field.New.Type == 23 || field.New.Type == 24 || field.New.Type == 25 {
					newValue = converContentConfiguration(ctx, field.New.Type, field.New.Value)
				} else if field.New.Type == 21 {
					value := field.New.Value
					newValue = strconv.Itoa(int(value.(int32)))
				} else if field.New.Type == 30 {
					if lang == "zh" {
						newValue = models.TypeHandle(ctx, 7, field.New.Value, isBlindDrugMap, log.OID, field.TranKey)
					} else {
						newValue = models.TypeHandle(ctx, 7, field.New.ENValue, isBlindDrugMap, log.OID, field.TranKey)
					}
				} else {
					if field.TranKey == "operation_log.form.format" {
						if field.New.Value == "form.control.type.format.numberLength" {
							newValue = locales.Tr(ctx, "form.control.type.format.numberLength")
						} else if field.New.Value == "form.control.type.format.decimalLength" {
							newValue = locales.Tr(ctx, "form.control.type.format.decimalLength")
						} else {
							newValue = models.TypeHandle(ctx, field.New.Type, field.New.Value, isBlindDrugMap, log.OID, field.TranKey)
						}
					} else if field.TranKey == "operation_log.form.options" {
						newValue = converOptionValue(ctx, field.New.Value)
					} else if field.TranKey == "operation_log.drug_configure_setting.visitJudgment" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentName" {
						newValue = converDefaultValue(ctx, field.New.Value)
					} else if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
						newValue = models.TypeHandle(ctx, 7, field.New.Value, isBlindDrugMap, log.OID, field.TranKey)
					} else {
						newValue = models.TypeHandle(ctx, field.New.Type, field.New.Value, isBlindDrugMap, log.OID, field.TranKey)
					}
				}
			} else {
				newValue = converCountry(ctx, field.New.Value)
			}
			if log.Type != 1 && oldValue == newValue && field.Old.Type != 7 && (field.Old.Mark == 0 || field.New.Mark == 0) {
				// 值未改变的 不需要展示
				continue
			}
			//号段随机长度特殊判断
			if log.Type != 1 && oldValue == newValue && field.TranKey == "operation_log.attribute.segmentLength" && !isBlind {
				continue
			}
			if log.Type == 1 {
				tmp = labelName + " : " + newValue
				if log.Module == "operation_log.module.notifications" {
					moduleName := locales.Tr(ctx, log.Module)
					context[0] = moduleName + "-" + secondaryMenuName
				}
				if field.TranKey == "operation_log.project_storehouse.onlyID" || field.TranKey == "operation_log.drug_configure.onlyID" ||
					field.TranKey == "operation_log.uploadMedicines.onlyID" ||
					//field.TranKey == "operation_log.random_list.onlyID" ||
					field.TranKey == "operation_log.simulateRandom.onlyID" {
					if oldValue != "" {
						// 去掉字符串开头的双引号
						if len(oldValue) >= 2 && oldValue[0] == '"' && oldValue[len(oldValue)-1] == '"' {
							oldValue = oldValue[1 : len(oldValue)-1]
						}
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						// 去掉字符串开头的双引号
						if len(newValue) >= 2 && newValue[0] == '"' && newValue[len(newValue)-1] == '"' {
							newValue = newValue[1 : len(newValue)-1]
						}
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
						//kk := tools.UnshuffleString(aes)
						//str := string(aes)
						//if bytes.Equal(plaintext, kk) {
						//	fmt.Println("加密之前:", newValue)
						//	fmt.Println("解密之后:", string(kk))
						//	fmt.Println("加密之后:", str)
						//}
					}
				}

				if field.TranKey == "operation_log.drug_configure_setting.doseLevelID" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentID" {
					if oldValue != "" {
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
				}

				if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
					tmp = labelName + " : " + newValue
				}

				if field.TranKey == "operation_log.drug_configure_setting.dtp_ipType" {
					tmp = labelName + " : " + newValue
				}

			} else if log.Type == 2 {
				tmp = labelName + " : " + oldValue + " -> " + newValue
				if log.Module == "operation_log.module.notifications" {
					moduleName := locales.Tr(ctx, log.Module)
					context[0] = moduleName + "-" + secondaryMenuName
				}
				if log.Module == "operation_log.module.project_basic_information" {
					if oldValue == "1" {
						old := locales.Tr(ctx, "projectSettings.basicInformation.projectProperties.projectType.basicStudy")
						if newValue == "2" {
							tmp = labelName + " : " + old + " -> " + locales.Tr(ctx, "projectSettings.basicInformation.projectProperties.projectType.cohortStudy")
						} else if newValue == "3" {
							tmp = labelName + " : " + old + " -> " + locales.Tr(ctx, "projectSettings.basicInformation.projectProperties.projectType.reRandomizationStudy")
						}
					}
				}

				if field.TranKey == "operation_log.project_storehouse.onlyID" || field.TranKey == "operation_log.drug_configure.onlyID" ||
					field.TranKey == "operation_log.uploadMedicines.onlyID" ||
					//field.TranKey == "operation_log.random_list.onlyID" ||
					field.TranKey == "operation_log.simulateRandom.onlyID" {
					if oldValue != "" {
						// 去掉字符串开头的双引号
						if len(oldValue) >= 2 && oldValue[0] == '"' && oldValue[len(oldValue)-1] == '"' {
							oldValue = oldValue[1 : len(oldValue)-1]
						}
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						// 去掉字符串开头的双引号
						if len(newValue) >= 2 && newValue[0] == '"' && newValue[len(newValue)-1] == '"' {
							newValue = newValue[1 : len(newValue)-1]
						}
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
						//kk := tools.DecryptAes(aes)
						//if bytes.Equal(plaintext, kk) {
						//	fmt.Println("加密之前:", newValue)
						//	fmt.Println("解密之后:", string(kk))
						//	fmt.Println("加密之后:", encryptedData)
						//}
					}
				}

				if field.TranKey == "operation_log.drug_configure_setting.doseLevelID" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentID" {
					if oldValue != "" {
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
				}

				if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
					tmp = labelName + " : " + newValue
				}
				if field.TranKey == "operation_log.drug_configure_setting.dtp_ipType" {
					tmp = labelName + " : " + newValue
				}

			} else if log.Type == 3 {
				tmp = labelName + " : " + oldValue

				if field.TranKey == "operation_log.project_storehouse.onlyID" || field.TranKey == "operation_log.drug_configure.onlyID" ||
					field.TranKey == "operation_log.uploadMedicines.onlyID" ||
					//field.TranKey == "operation_log.random_list.onlyID" ||
					field.TranKey == "operation_log.simulateRandom.onlyID" {
					if oldValue != "" {
						// 去掉字符串开头的双引号
						if len(oldValue) >= 2 && oldValue[0] == '"' && oldValue[len(oldValue)-1] == '"' {
							oldValue = oldValue[1 : len(oldValue)-1]
						}
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						// 去掉字符串开头的双引号
						if len(newValue) >= 2 && newValue[0] == '"' && newValue[len(newValue)-1] == '"' {
							newValue = newValue[1 : len(newValue)-1]
						}
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
						//kk := tools.DecryptAes(aes)
						//if bytes.Equal(plaintext, kk) {
						//	fmt.Println("加密之前:", newValue)
						//	fmt.Println("解密之后:", string(kk))
						//	fmt.Println("加密之后:", encryptedData)
						//}
					}
				}

				if field.TranKey == "operation_log.drug_configure_setting.doseLevelID" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentID" {
					if oldValue != "" {
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
				}

				if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
					tmp = labelName + " : " + newValue
				}

				if field.TranKey == "operation_log.drug_configure_setting.dtp_ipType" {
					tmp = labelName + " : " + newValue
				}

			} else if log.Type == 4 {
				tmp = labelName
			} else if log.Type == 5 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 6 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 7 {
				tmp = labelName + " : " + oldValue + " -> " + newValue
			} else if log.Type == 8 {
				tmp = labelName + " : " + oldValue + " -> " + newValue
			} else if log.Type == 10 {
				tmp = labelName + " : " + newValue
				if log.Module == "operation_log.module.notifications" {
					moduleName := locales.Tr(ctx, log.Module)
					context[0] = moduleName + "-" + secondaryMenuName
				}
				if field.TranKey == "operation_log.project_storehouse.onlyID" || field.TranKey == "operation_log.drug_configure.onlyID" ||
					field.TranKey == "operation_log.uploadMedicines.onlyID" ||
					//field.TranKey == "operation_log.random_list.onlyID" ||
					field.TranKey == "operation_log.simulateRandom.onlyID" {
					if oldValue != "" {
						// 去掉字符串开头的双引号
						if len(oldValue) >= 2 && oldValue[0] == '"' && oldValue[len(oldValue)-1] == '"' {
							oldValue = oldValue[1 : len(oldValue)-1]
						}
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						// 去掉字符串开头的双引号
						if len(newValue) >= 2 && newValue[0] == '"' && newValue[len(newValue)-1] == '"' {
							newValue = newValue[1 : len(newValue)-1]
						}
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
						//kk := tools.DecryptAes(aes)
						//if bytes.Equal(plaintext, kk) {
						//	fmt.Println("加密之前:", newValue)
						//	fmt.Println("解密之后:", string(kk))
						//	fmt.Println("加密之后:", encryptedData)
						//}
					}
				}

				if field.TranKey == "operation_log.drug_configure_setting.doseLevelID" || field.TranKey == "operation_log.drug_configure_setting.visitJudgmentID" {
					if oldValue != "" {
						plaintext := []byte(oldValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
					if newValue != "" {
						plaintext := []byte(newValue)
						aes := tools.ShuffleString(plaintext)
						// Base64编码
						encryptedData := base64.StdEncoding.EncodeToString(aes)
						tmp = labelName + " : " + encryptedData
					}
				}
				if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
					tmp = labelName + " : " + newValue
				}
				if field.TranKey == "operation_log.drug_configure_setting.dtp_ipType" {
					tmp = labelName + " : " + newValue
				}
			} else if log.Type == 11 {
				tmp = labelName + " : " + newValue
				if log.Module == "operation_log.module.notifications" {
					moduleName := locales.Tr(ctx, log.Module)
					context[0] = moduleName + "-" + secondaryMenuName
				}
				if field.TranKey == "operation_log.drug_configure.visitDrugNameDispeningNumber" {
					tmp = labelName + " : " + newValue
				}
				if field.TranKey == "operation_log.drug_configure_setting.dtp_ipType" {
					tmp = labelName + " : " + newValue
				}
			} else if log.Type == 12 {
				if field.TranKey == "operation_log.projectUser.emailLanguage" {
					tmp = labelName + " : " + oldValue + " -> " + newValue
				} else {
					tmp = labelName + " : " + newValue
				}
			} else if log.Type == 13 {
				if field.TranKey == "operation_log.projectUser.status" {
					tmp = labelName + " : " + oldValue + " -> " + newValue
				} else {
					tmp = labelName + " : " + newValue
				}
			} else if log.Type == 14 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 15 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 16 {
				tmp = labelName + " : " + newValue
			} else if log.Type == 17 {
				tmp = labelName + " : " + newValue
			}

			if field.TranKey == "operation_log.project_storehouse.onlyID" || field.TranKey == "operation_log.drug_configure.onlyID" ||
				field.TranKey == "operation_log.uploadMedicines.onlyID" ||
				//field.TranKey == "operation_log.random_list.onlyID" ||
				field.TranKey == "operation_log.simulateRandom.onlyID" {

				context = append(context, tmp)

				for _, mark := range log.Mark {
					if mark.Label != "" {
						value := mark.Value
						if mark.Label == "operation_log.label.otherMedicines" ||
							mark.Label == "operation_log.label.uploadMedicine" ||
							mark.Label == "operation_log.label.medicine" ||
							mark.Label == "operation_log.project_storehouse.medicine_name" {
							if isBlindDrugMap[value] && isBlindedRole {
								value = tools.BlindData
							}
						} else {
							if mark.Blind && isBlind {
								value = tools.BlindData
							}
						}
						if log.Module != "operation_log.module.subjects" {
							context = append(context, locales.Tr(ctx, mark.Label)+" : "+value)
						}
					}
				}
			} else {
				context = append(context, tmp)
			}

		}
		userName := ""
		if log.User.Unicode == 0 {
			userName = log.User.Name
		} else {
			userName = fmt.Sprintf("%s(%d)", log.User.Name, log.User.Unicode)
		}
		//if len(context) == 1 {
		//	if context[0] == locales.Tr(ctx, log.Module) {
		//		if log.Module != "operation_log.module.configure_export" && log.Module != "operation_log.module.projectUser" {
		//			continue
		//		}
		//	}
		//	//if log.Module == "operation_log.module.drug_configure_setting" {
		//	//	continue
		//	//}
		//}

		if log.CloudType == 1 {
			userName = locales.Tr(ctx, "operation.projects.main.setting.function.admin.cloud")
			context = []string{}
			context = append(context, locales.Tr(ctx, "operation.projects.main.setting.function.admin.cloud.delete"))
		} else if log.CloudType == 2 {
			userName = locales.Tr(ctx, "operation.projects.main.setting.function.admin.cloud")
			context = []string{}
			context = append(context, locales.Tr(ctx, "operation.projects.main.setting.function.admin.cloud.disable"))
		}

		location := "Asia/Shanghai"
		if len(users) > 0 && users[0].Settings.Tz != "" {
			location = users[0].Settings.Tz
		}
		timeStr, err := tools.GetLocationUtc(location, int64(log.Time))
		if err != nil {
			panic(err)
		}

		//if len(context) == 2 && log.Type == 10 {
		//	num += 1
		//	continue
		//}

		RetOperationLogs = append(RetOperationLogs, models.RetOperationLog{
			ID:            log.ID,
			UserEmail:     log.User.Email,
			UserName:      userName,
			OperationType: locales.Tr(ctx, models.OperationLogMap["operation_type"].(bson.M)[convertor.ToString(log.Type)].(string)),
			Time:          log.Time,
			TimeStr:       timeStr,
			Context:       context,
		})

	}
	return map[string]interface{}{
		"items": RetOperationLogs,
		"total": count,
	}, nil
}

// updateBarcodeLabel 更新条形码标签项目日志
func insertBarcodeLabel(ctx *gin.Context, sctx mongo.SessionContext, oldLabel, newLabel models.BarcodeLabel, OID primitive.ObjectID, operType int) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	if operType != 3 { // 关联研究产品
		if oldLabel.CorrelationName != newLabel.CorrelationName {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.barcode_label",
				TranKey: "operation_log.barcode_label.correlation_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: oldLabel.CorrelationName,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: newLabel.CorrelationName,
				},
			})
		}

		// 关联研究产品数量变更
		if len(oldLabel.ProductIds) != len(newLabel.ProductIds) {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.barcode_label",
				TranKey: "operation_log.barcode_label.product_count",
				Old: models.OperationLogField{
					Type:  2,
					Value: len(oldLabel.ProductIds),
				},
				New: models.OperationLogField{
					Type:  2,
					Value: len(newLabel.ProductIds),
				},
			})
		}

		// 标签模板尺寸变更
		if oldLabel.Template.LabelSize.Width != newLabel.Template.LabelSize.Width ||
			oldLabel.Template.LabelSize.Height != newLabel.Template.LabelSize.Height {
			oldTemplateSize := fmt.Sprintf("%.1f×%.1fmm", oldLabel.Template.LabelSize.Width, oldLabel.Template.LabelSize.Height)
			newTemplateSize := fmt.Sprintf("%.1f×%.1fmm", newLabel.Template.LabelSize.Width, newLabel.Template.LabelSize.Height)
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.barcode_label",
				TranKey: "operation_log.barcode_label.label_size",
				Old: models.OperationLogField{
					Type:  2,
					Value: oldTemplateSize,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: newTemplateSize,
				},
			})
		}

		// 打印尺寸
		if oldLabel.Template.PrintSize.Width != newLabel.Template.PrintSize.Width ||
			oldLabel.Template.PrintSize.Height != newLabel.Template.PrintSize.Height {
			oldTemplateSize := fmt.Sprintf("%.1f×%.1fmm", oldLabel.Template.PrintSize.Width, oldLabel.Template.PrintSize.Height)
			newTemplateSize := fmt.Sprintf("%.1f×%.1fmm", newLabel.Template.PrintSize.Width, newLabel.Template.PrintSize.Height)
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.barcode_label",
				TranKey: "operation_log.barcode_label.template_size",
				Old: models.OperationLogField{
					Type:  2,
					Value: oldTemplateSize,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: newTemplateSize,
				},
			})
		}

		for _, field := range newLabel.Template.Fields {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.barcode_label",
				TranKey: "operation_log.barcode_label.base.text",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: field.Value,
				},
			})
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.barcode_label",
				TranKey: "operation_log.barcode_label.base.position",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: field.TextAlign,
				},
			})
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.barcode_label",
				TranKey: "operation_log.barcode_label.base.font",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: field.FontFamily + "/" + convertor.ToString(field.FontSize) + "/" + field.FontWeight + "/" + field.FontColor,
				},
			})
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.barcode_label",
				TranKey: "operation_log.barcode_label.base.background",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: field.BackgroundColor,
				},
			})
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.barcode_label",
				TranKey: "operation_log.barcode_label.base.height",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: field.Height,
				},
			})
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.barcode_label",
				TranKey: "operation_log.barcode_label.base.marginTop",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: field.MarginTop,
				},
			})
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.barcode_label",
				TranKey: "operation_log.barcode_label.base.marginLeft",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: field.MarginLeft,
				},
			})
		}
	}

	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.barcode_label",
		Value: newLabel.LabelNumber,
		Blind: false,
	})

	err := tools.SaveOperation(ctx, sctx, "operation_log.module.barcode_label", OID, operType, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func converOptionValue(ctx *gin.Context, value interface{}) string {
	result := ""
	if value != nil {
		result = value.(string)
		one := strings.Contains(result, "form.control.type.options.one")
		if one {
			// 使用 ReplaceAll 替换所有出现的子串
			result = strings.ReplaceAll(result, "form.control.type.options.one", locales.Tr(ctx, "form.control.type.options.one"))
		}
		two := strings.Contains(result, "form.control.type.options.two")
		if two {
			// 使用 ReplaceAll 替换所有出现的子串
			result = strings.ReplaceAll(result, "form.control.type.options.two", locales.Tr(ctx, "form.control.type.options.two"))
		}
		three := strings.Contains(result, "form.control.type.options.three")
		if three {
			// 使用 ReplaceAll 替换所有出现的子串
			result = strings.ReplaceAll(result, "form.control.type.options.three", locales.Tr(ctx, "form.control.type.options.three"))
		}
	}
	return result
}

func converDefaultValue(ctx *gin.Context, value interface{}) string {
	result := ""
	if value != nil {
		result = value.(string)
		one := strings.Contains(value.(string), "form.control.type.options.one")
		if one {
			// 使用 ReplaceAll 替换所有出现的子串
			result = strings.ReplaceAll(value.(string), "form.control.type.options.one", locales.Tr(ctx, "form.control.type.options.one"))
		}
		two := strings.Contains(value.(string), "form.control.type.options.two")
		if two {
			// 使用 ReplaceAll 替换所有出现的子串
			result = strings.ReplaceAll(value.(string), "form.control.type.options.two", locales.Tr(ctx, "form.control.type.options.two"))
		}
		three := strings.Contains(value.(string), "form.control.type.options.three")
		if three {
			// 使用 ReplaceAll 替换所有出现的子串
			result = strings.ReplaceAll(value.(string), "form.control.type.options.three", locales.Tr(ctx, "form.control.type.options.three"))
		}
	}
	return result
}

// 角色
func converRoles(ctx *gin.Context, value interface{}) string {
	var roles []string
	v, _ := value.(primitive.A)
	if len(v) > 0 {
		var projectRolePermission []models.ProjectRolePermission
		var s []primitive.ObjectID
		for i := 0; i < len(v); i++ {
			s = append(s, v[i].(primitive.ObjectID))
		}
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: bson.D{{Key: "_id", Value: bson.M{"$in": s}}}}},
		}
		cus, _ := tools.Database.Collection("project_role_permission").Aggregate(nil, pipeline)
		_ = cus.All(nil, &projectRolePermission)

		for j := 0; j < len(projectRolePermission); j++ {
			roles = append(roles, projectRolePermission[j].Name)
		}
	}
	return strings.Join(roles, ",")
}

// 内容配置-场景
func converContentConfiguration(ctx *gin.Context, configurationType int, value interface{}) string {
	v, _ := value.(primitive.A)
	var result []string
	if len(v) > 0 {
		if configurationType == 17 {
			for i := 0; i < len(v); i++ {
				if v[i].(string) == "group" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.group"))
				} else if v[i].(string) == "random_number" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.randomizationNumber"))
				} else if v[i].(string) == "projectNumber" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.projectNumber"))
				} else if v[i].(string) == "projectName" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.projectName"))
				} else if v[i].(string) == "siteNumber" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.siteNumber"))
				} else if v[i].(string) == "siteName" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.contentConfiguration.siteName"))
				}
			}
		} else if configurationType == 18 {
			for i := 0; i < len(v); i++ {
				if v[i].(string) == "dispensing.plan-title" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.Dispense"))
				} else if v[i].(string) == "dispensing.unscheduled-plan-title" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.unscheduledDispense"))
				} else if v[i].(string) == "dispensing.reissue-title" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.reDispense"))
				} else if v[i].(string) == "dispensing.replace-title" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.replaceIP"))
				} else if v[i].(string) == "dispensing.retrieval-title" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.retrieveIP"))
				} else if v[i].(string) == "dispensing.register-title" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.actuallyUsedIP"))
				} else if v[i].(string) == "dispensing.not-attend-title" {
					result = append(result, locales.Tr(ctx, "notifications.dispensing.scene.notAttend"))
				}
			}
		} else if configurationType == 19 {
			for i := 0; i < len(v); i++ {
				if v[i].(string) == "medicine.freeze.title" {
					result = append(result, locales.Tr(ctx, "notifications.isolation.scene.quarantine"))
				} else if v[i].(string) == "medicine.freeze.release" {
					result = append(result, locales.Tr(ctx, "notifications.isolation.scene.release"))
				}
			}
		} else if configurationType == 20 {
			for i := 0; i < len(v); i++ {
				if v[i].(string) == "order.approval.add-title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.apply"))
				} else if v[i].(string) == "order.approval.failed-title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.applicationApprovalFailed"))
				} else if v[i].(string) == "order.medicine_order_title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.createManualOrder"))
				} else if v[i].(string) == "order.cancel_title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.cancel"))
				} else if v[i].(string) == "order.no_automatic_success_title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.confirm"))
				} else if v[i].(string) == "order.close_title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.close"))
				} else if v[i].(string) == "order.send_title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.deliver"))
				} else if v[i].(string) == "order.receive_title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.receive"))
				} else if v[i].(string) == "order.end_title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.termination"))
				} else if v[i].(string) == "order.lost_title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.lost"))
				} else if v[i].(string) == "order.automatic_success_title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.createAutomaticOrder"))
				} else if v[i].(string) == "order.automatic_error_title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.creationFailedAutomaticOrder"))
				} else if v[i].(string) == "order.automatic_alarm_title" {
					result = append(result, locales.Tr(ctx, "notifications.order.scene.automaticOrderAlarm"))
				}
			}
		} else if configurationType == 22 {
			for i := 0; i < len(v); i++ {
				if v[i].(string) == "projectName" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.body.configuration.project.name"))
				} else if v[i].(string) == "projectNumber" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.body.configuration.project.no"))
				} else if v[i].(string) == "envName" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.body.configuration.project.environment"))
				} else if v[i].(string) == "siteName" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.body.configuration.site.name"))
				} else if v[i].(string) == "siteNumber" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.body.configuration.site.number"))
				}
			}
		} else if configurationType == 23 {
			for i := 0; i < len(v); i++ {
				if v[i].(string) == "notice.subject.add" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.subject.register"))
				} else if v[i].(string) == "notice.subject.random" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.subject.randomization"))
				} else if v[i].(string) == "notice.subject.signOut" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.subject.signOut"))
				} else if v[i].(string) == "notice.subject.replace" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.subject.replace"))
				} else if v[i].(string) == "notice.subject.update" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.subject.update"))
				} else if v[i].(string) == "notice.subject.dispensing" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.subject.dispensation"))
				} else if v[i].(string) == "notice.subject.alarm" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.subject.alert"))
				} else if v[i].(string) == "notice.subject.unblinding" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.emergency.unblinding"))
				} else if v[i].(string) == "notice.medicine.isolation" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.ip.quarantine"))
				} else if v[i].(string) == "otice.medicine.order" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.shipment.order"))
				} else if v[i].(string) == "notice.medicine.reminder" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.ip.expiration.reminder"))
				} else if v[i].(string) == "notice.medicine.alarm" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.inventory.alert"))
				} else if v[i].(string) == "notice.storehouse.alarm" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.depot.inventory.alert"))
				} else if v[i].(string) == "notice.order.timeout" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.late.shipment.alert"))
				} else if v[i].(string) == "notice.subject.alert.threshold" {
					result = append(result, locales.Tr(ctx, "notifications.notice.email.content.scene.subject.alert.threshold"))
				}
			}
		} else if configurationType == 24 {
			for i := 0; i < len(v); i++ {
				if v[i].(string) == "subject.screen.success" {
					result = append(result, locales.Tr(ctx, "notifications.subject.screen.success"))
				} else if v[i].(string) == "subject.screen.fail" {
					result = append(result, locales.Tr(ctx, "notifications.subject.screen.fail"))
				}
			}
		} else if configurationType == 25 {
			for i := 0; i < len(v); i++ {
				if v[i].(string) == "subject.update.form_factor" {
					result = append(result, locales.Tr(ctx, "notifications.subject.update.form.factor"))
				} else if v[i].(string) == "subject.update.screen" {
					result = append(result, locales.Tr(ctx, "notifications.subject.update.screen"))
				} else if v[i].(string) == "subject.update.stop" {
					result = append(result, locales.Tr(ctx, "notifications.subject.update.stop"))
				} else if v[i].(string) == "subject.update.finish" {
					result = append(result, locales.Tr(ctx, "notifications.subject.update.finish"))
				} else if v[i].(string) == "subject.update.shortname" {
					result = append(result, locales.Tr(ctx, "notifications.subject.update.shortname"))
				}
			}
		}
	}
	return strings.Join(result, ",")
}

func converCountry(ctx *gin.Context, value interface{}) string {
	lang := locales.Lang(ctx)

	country := ""
	city := ""
	state := ""
	v, _ := value.(primitive.A)
	if len(v) == 2 {
		pipeline := mongo.Pipeline{
			{{Key: "$unwind", Value: "$state"}},
			{{Key: "$unwind", Value: bson.M{"path": "$state.city", "preserveNullAndEmptyArrays": true}}},
			{{Key: "$match", Value: bson.M{"code": v[0].(string), "state.code": v[1].(string)}}},
		}
		var data []map[string]interface{}
		cursor, _ := tools.Database.Collection("country").Aggregate(nil, pipeline, nil)
		_ = cursor.All(nil, &data)
		if len(data) == 0 {
			pipeline = mongo.Pipeline{
				{{Key: "$unwind", Value: "$state"}},
				{{Key: "$unwind", Value: "$state.city"}},
				{{Key: "$match", Value: bson.M{"code": v[0], "state.city.code": v[1]}}},
			}
			cursor, _ = tools.Database.Collection("country").Aggregate(nil, pipeline, nil)
			_ = cursor.All(nil, &data)
			if data != nil {
				if lang == "zh" {
					country = data[0]["cn"].(string)
					city = data[0]["state"].(map[string]interface{})["city"].(map[string]interface{})["cn"].(string)
				} else if lang == "ko" {
					country = data[0]["ko"].(string)
					city = data[0]["state"].(map[string]interface{})["city"].(map[string]interface{})["ko"].(string)
				} else {
					country = data[0]["en"].(string)
					city = data[0]["state"].(map[string]interface{})["city"].(map[string]interface{})["en"].(string)
				}
			}
		} else {
			if lang == "zh" {
				country = data[0]["cn"].(string)
				city = data[0]["state"].(map[string]interface{})["cn"].(string)
			} else if lang == "ko" {
				country = data[0]["ko"].(string)
				city = data[0]["state"].(map[string]interface{})["ko"].(string)
			} else {
				country = data[0]["en"].(string)
				city = data[0]["state"].(map[string]interface{})["en"].(string)
			}
		}

		return country + "/" + city
	} else if len(v) == 3 {
		pipeline := mongo.Pipeline{
			{{Key: "$unwind", Value: "$state"}},
			{{Key: "$unwind", Value: "$state.city"}},
			{{Key: "$match", Value: bson.M{"code": v[0], "state.code": v[1], "state.city.code": v[2]}}},
		}
		var data []map[string]interface{}
		cursor, _ := tools.Database.Collection("country").Aggregate(nil, pipeline, nil)
		_ = cursor.All(nil, &data)
		if data != nil {
			if lang == "zh" {
				country = data[0]["cn"].(string)
				state = data[0]["state"].(map[string]interface{})["cn"].(string)
				city = data[0]["state"].(map[string]interface{})["city"].(map[string]interface{})["cn"].(string)
			} else if lang == "ko" {
				country = data[0]["ko"].(string)
				state = data[0]["state"].(map[string]interface{})["ko"].(string)
				city = data[0]["state"].(map[string]interface{})["city"].(map[string]interface{})["ko"].(string)
			} else {
				country = data[0]["en"].(string)
				state = data[0]["state"].(map[string]interface{})["en"].(string)
				city = data[0]["state"].(map[string]interface{})["city"].(map[string]interface{})["en"].(string)
			}
			return country + "/" + state + "/" + city
		}
	}
	return ""
}

// 新增未编号研究产品日志
func insertOtherMedicineLog(ctx *gin.Context, new models.AddMedicineOther, OID primitive.ObjectID, operType int, copyEnv string) error {
	// 保存项目日志

	var OperationLogFieldGroups []models.OperationLogFieldGroup

	if copyEnv != "" {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.env_name",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: copyEnv,
			},
		})
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.uploadMedicines.medicineName",
		Old: models.OperationLogField{
			Type:  7,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  7,
			Value: new.Name,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.uploadMedicines.expireDate",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.ExpirationDate,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.uploadMedicines.batchNumber",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.BatchNumber,
		},
	})
	if new.PackageCount > 0 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.singleCount",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.Count,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.packageCount",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.PackageCount,
			},
		})
	} else {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.count",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.Count,
			},
		})
	}
	var projectStorehouse models.ProjectStorehouse
	var storehouse models.Storehouse
	_ = tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": new.StorehouseID}).Decode(&projectStorehouse)
	_ = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": projectStorehouse.StorehouseID}).Decode(&storehouse)

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.uploadMedicines.storehouse",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: storehouse.Name,
		},
	})

	marks := []models.Mark{}
	err := tools.SaveOperation(ctx, nil, "operation_log.module.otherMedicines", OID, operType, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 编辑未编号研究产品日志
func UpdateOtherMedicineLog(ctx *gin.Context, newData models.UpdateMedicineOtherInfo, medicineOther models.UpdateMedicineOtherInfo, OID primitive.ObjectID, operType int) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.uploadMedicines.medicineName",
		Old: models.OperationLogField{
			Type:  7,
			Value: medicineOther.Name,
		},
		New: models.OperationLogField{
			Type:  7,
			Value: newData.Name,
		},
	})

	if newData.PackageCount > 0 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.singleCount",
			Old: models.OperationLogField{
				Type:  2,
				Value: medicineOther.Count,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newData.Count,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.packageCount",
			Old: models.OperationLogField{
				Type:  2,
				Value: medicineOther.PackageCount,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newData.PackageCount,
			},
		})
	} else {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.count",
			Old: models.OperationLogField{
				Type:  2,
				Value: medicineOther.Count,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newData.Count,
			},
		})
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.uploadMedicines.expireDate",
		Old: models.OperationLogField{
			Type:  2,
			Value: medicineOther.ExpirationDate,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newData.ExpirationDate,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.uploadMedicines.batchNumber",
		Old: models.OperationLogField{
			Type:  2,
			Value: medicineOther.BatchNumber,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newData.BatchNumber,
		},
	})
	var projectStorehouse models.ProjectStorehouse
	var storehouse models.Storehouse
	_ = tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": newData.StorehouseID}).Decode(&projectStorehouse)
	_ = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": projectStorehouse.StorehouseID}).Decode(&storehouse)

	var oldProjectStorehouse models.ProjectStorehouse
	var oldStorehouse models.Storehouse
	_ = tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": medicineOther.StorehouseID}).Decode(&oldProjectStorehouse)
	_ = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": oldProjectStorehouse.StorehouseID}).Decode(&oldStorehouse)

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.uploadMedicines.storehouse",
		Old: models.OperationLogField{
			Type:  2,
			Value: oldStorehouse.Name,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: storehouse.Name,
		},
	})

	// if (OperationLogFieldGroups != nil && len(OperationLogFieldGroups) > 0) && (operType == 1 || operType == 2 || operType == 10) {
	// 	newOperationLogFieldGroup := models.OperationLogFieldGroup{
	// 		Key:     "operation_log.medicinesList",
	// 		TranKey: "operation_log.uploadMedicines.onlyID",
	// 		Old: models.OperationLogField{
	// 			Type:  2,
	// 			Value: nil,
	// 		},
	// 		New: models.OperationLogField{
	// 			Type:  2,
	// 			Value: medicineOther.ID,
	// 		},
	// 	}
	// 	// 添加新的 OperationLogFieldGroup 到数组的开头
	// 	OperationLogFieldGroups = append([]models.OperationLogFieldGroup{newOperationLogFieldGroup}, OperationLogFieldGroups...)
	// }

	marks := []models.Mark{}
	err := tools.SaveOperation(ctx, nil, "operation_log.module.otherMedicines", OID, operType, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 随机类型， 区组， 分层日志写入
func insertRandomDesignLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, oldRandomDesign models.RandomDesignInfo, newRandomDesign models.RandomDesignInfo, OperID primitive.ObjectID, updateType string, project models.Project, copyEnv string) error {
	// TODO保存项目日志
	groupLabelShow := ""
	factorLabelShow := ""
	OperationLogFieldGroups := []models.OperationLogFieldGroup{}
	if types != 3 {
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design",
				TranKey: "operation_log.random_design.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}
		if oldRandomDesign.Type != newRandomDesign.Type && newRandomDesign.Type != 0 {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design",
				TranKey: "operation_log.random_design.type",
				Old: models.OperationLogField{
					Type:  6,
					Value: oldRandomDesign.Type,
				},
				New: models.OperationLogField{
					Type:  6,
					Value: newRandomDesign.Type,
				},
			})
		}
		for _, newGroup := range newRandomDesign.Groups {
			add := true
			for _, oldGroup := range oldRandomDesign.Groups {
				if newGroup.ID == oldGroup.ID {
					add = false
					haveSub := false
					if (oldGroup.SubGroup != nil && len(oldGroup.SubGroup) > 0) || (newGroup.SubGroup != nil && len(newGroup.SubGroup) > 0) {
						haveSub = true
					}
					if !haveSub { // 编辑的话 修改当前数据
						if newGroup.Name == oldGroup.Name {
							groupLabelShow = oldGroup.Name
						}
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.group_name",
							Old: models.OperationLogField{
								Type:  7,
								Value: oldGroup.Name,
							},
							New: models.OperationLogField{
								Type:  7,
								Value: newGroup.Name,
							},
						})

						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.group_code",
							Old: models.OperationLogField{
								Type:  7,
								Value: oldGroup.Code,
							},
							New: models.OperationLogField{
								Type:  7,
								Value: newGroup.Code,
							},
						})
					} else {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.group_code",
							Old: models.OperationLogField{
								Type:  7,
								Value: oldGroup.Code,
							},
							New: models.OperationLogField{
								Type:  7,
								Value: newGroup.Code,
							},
						})
						oldType := 7
						oldGroupName := oldGroup.Name
						if oldGroup.SubGroup != nil && len(oldGroup.SubGroup) > 0 {
							oldType = 13
							subNames := slice.Map(oldGroup.SubGroup, func(index int, item models.SubGroup) string {
								return item.Name
							})
							oldGroupName = fmt.Sprintf("%s%s%s", oldGroup.Name, "@@", strings.Join(subNames, "$$"))
						}
						newType := 7
						newGroupName := newGroup.Name
						if newGroup.SubGroup != nil && len(newGroup.SubGroup) > 0 {
							newType = 13
							subNames := slice.Map(newGroup.SubGroup, func(index int, item models.SubGroup) string {
								return item.Name
							})
							newGroupName = fmt.Sprintf("%s%s%s", newGroup.Name, "@@", strings.Join(subNames, "$$"))
						}
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.group_name",
							Old: models.OperationLogField{
								Type:  oldType,
								Value: oldGroupName,
							},
							New: models.OperationLogField{
								Type:  newType,
								Value: newGroupName,
							},
						})
					}
					if oldGroup.Status != newGroup.Status {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.status",
							Old: models.OperationLogField{
								Type:  6,
								Value: oldGroup.Status,
							},
							New: models.OperationLogField{
								Type:  6,
								Value: newGroup.Status,
							},
						})
					}
				}

			}
			if add {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.group_code",
					Old: models.OperationLogField{
						Type:  7,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  7,
						Value: newGroup.Code,
					},
				})
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.status",
					Old: models.OperationLogField{
						Type:  6,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  6,
						Value: newGroup.Status,
					},
				})
				haveSub := false
				if newGroup.SubGroup != nil && len(newGroup.SubGroup) > 0 {
					haveSub = true
				}
				if !haveSub {
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.group_name",
						Old: models.OperationLogField{
							Type:  7,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  7,
							Value: newGroup.Name,
						},
					})
				} else {
					newType := 7
					newGroupName := newGroup.Name
					if newGroup.SubGroup != nil && len(newGroup.SubGroup) > 0 {
						newType = 13
						subNames := slice.Map(newGroup.SubGroup, func(index int, item models.SubGroup) string {
							return item.Name
						})
						newGroupName = fmt.Sprintf("%s%s%s", newGroup.Name, "@@", strings.Join(subNames, "$$"))
					}
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.group_name",
						Old: models.OperationLogField{
							Type:  7,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  newType,
							Value: newGroupName,
						},
					})
				}
			}
		}
		for _, newFactor := range newRandomDesign.Factors {
			add := true
			for _, oldFactor := range oldRandomDesign.Factors {
				if newFactor.ID == oldFactor.ID {
					add = false
					if newFactor.Label == oldFactor.Label {
						factorLabelShow = oldFactor.Label
					}
					if newFactor.Name != oldFactor.Name {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.name",
							Old: models.OperationLogField{
								Type:  1,
								Value: oldFactor.Name,
							},
							New: models.OperationLogField{
								Type:  1,
								Value: newFactor.Name,
							},
						})
					}
					if newFactor.Number != oldFactor.Number {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.number",
							Old: models.OperationLogField{
								Type:  1,
								Value: oldFactor.Number,
							},
							New: models.OperationLogField{
								Type:  1,
								Value: newFactor.Number,
							},
						})
					}
					if newFactor.Label != oldFactor.Label {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.label",
							Old: models.OperationLogField{
								Type:  1,
								Value: oldFactor.Label,
							},
							New: models.OperationLogField{
								Type:  1,
								Value: newFactor.Label,
							},
						})
					}
					if newFactor.Type != oldFactor.Type && newFactor.IsCalc == false {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.type",
							Old: models.OperationLogField{
								Type:  6,
								Value: oldFactor.Type,
							},
							New: models.OperationLogField{
								Type:  6,
								Value: newFactor.Type,
							},
						})
					}

					if newFactor.Status != oldFactor.Status {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.status",
							Old: models.OperationLogField{
								Type:  6,
								Value: oldFactor.Status,
							},
							New: models.OperationLogField{
								Type:  6,
								Value: newFactor.Status,
							},
						})
					}
					if oldFactor.DateFormat == nil {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.form.format",
							Old: models.OperationLogField{
								Type:  2,
								Value: nil,
							},
							New: models.OperationLogField{
								Type:  2,
								Value: newFactor.DateFormat,
							},
						})
					}
					if oldFactor.Precision == nil {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.precision",
							Old: models.OperationLogField{
								Type:  1,
								Value: nil,
							},
							New: models.OperationLogField{
								Type:  1,
								Value: newFactor.Precision,
							},
						})
					} else {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.precision",
							Old: models.OperationLogField{
								Type:  1,
								Value: *oldFactor.Precision,
							},
							New: models.OperationLogField{
								Type:  1,
								Value: *newFactor.Precision,
							},
						})
					}
					oldLabel := []string{}
					newLabel := []string{}
					for _, oldOption := range oldFactor.Options {
						if oldOption.Formula != nil {
							oldLabel = append(oldLabel, oldOption.Label+" - "+*oldOption.Formula)
						} else {
							oldLabel = append(oldLabel, oldOption.Label)
						}
					}
					for _, newOption := range newFactor.Options {
						if newOption.Formula != nil {
							newLabel = append(newLabel, newOption.Label+" - "+*newOption.Formula)
						} else {
							newLabel = append(newLabel, newOption.Label)
						}
					}
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.factor.options_label_value",
						Old: models.OperationLogField{
							Type:  1,
							Value: strings.Join(oldLabel, "/"),
						},
						New: models.OperationLogField{
							Type:  1,
							Value: strings.Join(newLabel, "/"),
						},
					})
				}
			}
			if add {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.factor.name",
					Old: models.OperationLogField{
						Type:  1,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newFactor.Name,
					},
				})
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.factor.number",
					Old: models.OperationLogField{
						Type:  1,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newFactor.Number,
					},
				})
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.factor.label",
					Old: models.OperationLogField{
						Type:  1,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newFactor.Label,
					},
				})
				if newFactor.IsCalc == true {
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.factor.calcType",
						Old: models.OperationLogField{
							Type:  1,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  6,
							Value: newFactor.CalcType,
						},
					})
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.factor.formula",
						Old: models.OperationLogField{
							Type:  2,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  2,
							Value: newFactor.CustomFormulas,
						},
					})
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.factor.keepDecimal",
						Old: models.OperationLogField{
							Type:  1,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  1,
							Value: newFactor.Precision,
						},
					})
					if newFactor.Precision != nil && *newFactor.Precision == 0 {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.random_design",
							TranKey: "operation_log.random_design.factor.roundingMethod",
							Old: models.OperationLogField{
								Type:  6,
								Value: "",
							},
							New: models.OperationLogField{
								Type:  6,
								Value: newFactor.Round,
							},
						})
					}

				}
				if newFactor.IsCalc == false {
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.random_design",
						TranKey: "operation_log.random_design.factor.type",
						Old: models.OperationLogField{
							Type:  1,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  6,
							Value: newFactor.Type,
						},
					})
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.factor.status",
					Old: models.OperationLogField{
						Type:  1,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  6,
						Value: newFactor.Status,
					},
				})
				newLabel := []string{}
				for _, newOption := range newFactor.Options {
					if newOption.Formula != nil {
						newLabel = append(newLabel, newOption.Label+" - "+*newOption.Formula)
					} else {
						newLabel = append(newLabel, newOption.Label)
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design",
					TranKey: "operation_log.random_design.factor.options_label_value",
					Old: models.OperationLogField{
						Type:  1,
						Value: "",
					},
					New: models.OperationLogField{
						Type:  1,
						Value: strings.Join(newLabel, "/"),
					},
				})
			}
		}
		marks := []models.Mark{}
		if types == 2 {
			if updateType == "group" && groupLabelShow != "" {
				marks = append(marks, models.Mark{
					Label: "operation_log.random_design.group_name",
					Value: groupLabelShow,
					Blind: false,
				})
			}
			if updateType == "factor" && factorLabelShow != "" {
				marks = append(marks, models.Mark{
					Label: "operation_log.random_design.factor.label",
					Value: factorLabelShow,
					Blind: false,
				})
			}
		}
		if len(OperationLogFieldGroups) != 0 {
			err := tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", OID, types, OperationLogFieldGroups, marks, OperID)
			if err != nil {
				return errors.WithStack(err)
			}
			return nil
		}

	}
	return nil
}

// 随机列表，分层人数设置日志
func insertFactorNumberLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, old models.FactorsCombination, new models.FactorsCombination, OID primitive.ObjectID, historyOID primitive.ObjectID, randomListName string) error {
	OperationLogFieldGroups := []models.OperationLogFieldGroup{}
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.list",
		Value: randomListName,
		Blind: false,
	})

	oldFactor := ""
	for _, factor := range old.LayeredFactors {
		oldFactor = oldFactor + fmt.Sprintf("%s(%s),", factor.Label, factor.Text)
	}
	if len(oldFactor) > 0 {
		oldFactor = oldFactor[:len(oldFactor)-1]
	}

	newFactor := ""
	for _, factor := range new.LayeredFactors {
		newFactor = newFactor + fmt.Sprintf("%s(%s),", factor.Label, factor.Text)
	}
	if len(newFactor) > 0 {
		newFactor = newFactor[:len(newFactor)-1]
	}

	if types == 2 {
		marks = append(marks, models.Mark{
			Label: "operation_log.random_list.factor",
			Value: oldFactor,
			Blind: false,
		})
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.factor",
		Old: models.OperationLogField{
			Type:  1,
			Value: oldFactor,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newFactor,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.estimateNumber",
		Old: models.OperationLogField{
			Type:  1,
			Value: old.EstimateNumber,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: new.EstimateNumber,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.warnNumber",
		Old: models.OperationLogField{
			Type:  1,
			Value: old.WarnNumber,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: new.WarnNumber,
		},
	})

	err := tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", historyOID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 随机列表区组随机号激活失活
func insertBlockNumberActiveInactivateLog(ctx *gin.Context, sctx mongo.SessionContext, envCohortId primitive.ObjectID, statusType int, operationType int, randomListId primitive.ObjectID, randomListName string, block int, randomNumber string) error {
	OperationLogFieldGroups := []models.OperationLogFieldGroup{}
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.list",
		Value: randomListName,
		Blind: false,
	})

	if statusType == 1 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.randomNumber",
			Old: models.OperationLogField{
				Type:  1,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  1,
				Value: randomNumber,
			},
		})
	} else if statusType == 0 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.block",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: block,
			},
		})
	}
	t := 16
	if operationType == 1 {
		t = 17
	}

	err := tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", envCohortId, t, OperationLogFieldGroups, marks, randomListId)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 地区分层项目日志写入
func insertLayerRegionLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, oldAttribute models.AttributeInfo, newAttribute models.AttributeInfo, OID primitive.ObjectID) error {
	// TODO保存项目日志
	OperationLogFieldGroups := []models.OperationLogFieldGroup{}
	oldTypes := models.OperationLogField{
		Type:  2,
		Value: "",
	}
	newTypes := models.OperationLogField{
		Type:  2,
		Value: "",
	}
	if oldAttribute.IsRandom {
		oldTypes = models.OperationLogField{
			Type:  6,
			Value: 1,
		}
	}
	if oldAttribute.IsCountryRandom {
		oldTypes = models.OperationLogField{
			Type:  6,
			Value: 2,
		}
	}
	if oldAttribute.IsRegionRandom {
		oldTypes = models.OperationLogField{
			Type:  6,
			Value: 3,
		}
	}
	if newAttribute.IsRandom {
		newTypes = models.OperationLogField{
			Type:  6,
			Value: 1,
		}
	}
	if newAttribute.IsCountryRandom {
		newTypes = models.OperationLogField{
			Type:  6,
			Value: 2,
		}
	}
	if newAttribute.IsRegionRandom {
		newTypes = models.OperationLogField{
			Type:  6,
			Value: 3,
		}
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_design.factor.list",
		Old:     oldTypes,
		New:     newTypes,
	})

	marks := []models.Mark{}
	if len(OperationLogFieldGroups) != 0 {
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", envOID, types, OperationLogFieldGroups, marks, OID)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	}

	return nil
}

func insertLayerLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, oldAttribute models.AttributeInfo, newAttribute models.AttributeInfo, OID primitive.ObjectID) error {
	// TODO保存项目日志
	OperationLogFieldGroups := []models.OperationLogFieldGroup{}

	//if oldAttribute.IsRandom != newAttribute.IsRandom {
	//	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
	//		Key:     "operation_log.random_design",
	//		TranKey: "operation_log.random_list.isRandom",
	//		Old: models.OperationLogField{
	//			Type:  4,
	//			Value: oldAttribute.IsRandom,
	//		},
	//		New: models.OperationLogField{
	//			Type:  4,
	//			Value: newAttribute.IsRandom,
	//		},
	//	})
	//}
	//if oldAttribute.IsCountryRandom != newAttribute.IsCountryRandom {
	//	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
	//		Key:     "operation_log.random_design",
	//		TranKey: "operation_log.random_list.isCountryRandom",
	//		Old: models.OperationLogField{
	//			Type:  4,
	//			Value: oldAttribute.IsCountryRandom,
	//		},
	//		New: models.OperationLogField{
	//			Type:  4,
	//			Value: newAttribute.IsCountryRandom,
	//		},
	//	})
	//}
	//if oldAttribute.IsRegionRandom != newAttribute.IsRegionRandom {
	//	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
	//		Key:     "operation_log.random_design",
	//		TranKey: "operation_log.random_list.isRegionRandom",
	//		Old: models.OperationLogField{
	//			Type:  4,
	//			Value: oldAttribute.IsRegionRandom,
	//		},
	//		New: models.OperationLogField{
	//			Type:  4,
	//			Value: newAttribute.IsRegionRandom,
	//		},
	//	})
	//}
	oldTypes := 0
	newTypes := 0
	if oldAttribute.InstituteLayered {
		oldTypes = 1
	}
	if oldAttribute.CountryLayered {
		oldTypes = 2
	}
	if oldAttribute.RegionLayered {
		oldTypes = 3
	}
	if newAttribute.InstituteLayered {
		newTypes = 1
	}
	if newAttribute.CountryLayered {
		newTypes = 2
	}
	if newAttribute.RegionLayered {
		newTypes = 3
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_design.factor.layer",
		Old: models.OperationLogField{
			Type:  6,
			Value: oldTypes,
		},
		New: models.OperationLogField{
			Type:  6,
			Value: newTypes,
		},
	})

	marks := []models.Mark{}
	if len(OperationLogFieldGroups) != 0 {
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", envOID, types, OperationLogFieldGroups, marks, OID)
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	}

	return nil
}

// 供应计划详情 项目日志写入
func insertSupplyPlanMedicineLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, oldSupplyPlanMedicine models.MedicinePlanInfo, newSupplyPlanMedicine models.MedicinePlanInfo, OID primitive.ObjectID, supplyPlanID primitive.ObjectID, copyEnv string) error {
	// TODO保存项目日志
	var supplyPlan models.SupplyPlan
	err := tools.Database.Collection("supply_plan").FindOne(sctx, bson.M{"_id": supplyPlanID}).Decode(&supplyPlan)
	if err != nil {
		return errors.WithStack(err)
	}

	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {

		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.supply",
				TranKey: "operation_log.supply_detail.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  7,
					Value: copyEnv,
				},
			})
		}

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.name",
			Old: models.OperationLogField{
				Type:  7,
				Value: oldSupplyPlanMedicine.MedicineName,
			},
			New: models.OperationLogField{
				Type:  7,
				Value: newSupplyPlanMedicine.MedicineName,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.init_supply",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldSupplyPlanMedicine.InitSupply,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newSupplyPlanMedicine.InitSupply,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.warning",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldSupplyPlanMedicine.Warning,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newSupplyPlanMedicine.Warning,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.buffer",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldSupplyPlanMedicine.Buffer,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newSupplyPlanMedicine.Buffer,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.second_supply",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldSupplyPlanMedicine.SecondSupply,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newSupplyPlanMedicine.SecondSupply,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.dispensing_alert",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldSupplyPlanMedicine.DispensingAlarm,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newSupplyPlanMedicine.DispensingAlarm,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.un_distribution_date",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldSupplyPlanMedicine.UnDistributionDate,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newSupplyPlanMedicine.UnDistributionDate,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.un_provide_date",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldSupplyPlanMedicine.UnProvideDate,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newSupplyPlanMedicine.UnProvideDate,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.validity_reminder",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldSupplyPlanMedicine.ValidityReminder,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newSupplyPlanMedicine.ValidityReminder,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.auto_supply_size",
			Old: models.OperationLogField{
				Type:  5,
				Value: oldSupplyPlanMedicine.AutoSupplySize,
			},
			New: models.OperationLogField{
				Type:  5,
				Value: newSupplyPlanMedicine.AutoSupplySize,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.supply_mode_key",
			Old: models.OperationLogField{
				Type:  6,
				Value: oldSupplyPlanMedicine.SupplyMode,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: newSupplyPlanMedicine.SupplyMode,
			},
		})
		oldForecast := ""
		if oldSupplyPlanMedicine.ForecastMin != nil {
			oldForecast = convertor.ToString(oldSupplyPlanMedicine.ForecastMin) + "~" + convertor.ToString(oldSupplyPlanMedicine.ForecastMax)
		}
		newForecast := ""
		if newSupplyPlanMedicine.ForecastMin != nil {
			newForecast = convertor.ToString(newSupplyPlanMedicine.ForecastMin) + "~" + convertor.ToString(newSupplyPlanMedicine.ForecastMax)
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.supply",
			TranKey: "operation_log.supply_detail.forecastPeriod",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldForecast,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newForecast,
			},
		})
	}
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.supply",
		Value: supplyPlan.SupplyPlanInfo.Name,
		Blind: false,
	})
	if types == 1 { // 写入新增 编辑的 名称
		marks = append(marks, models.Mark{
			Label: "operation_log.label.medicine",
			Value: newSupplyPlanMedicine.MedicineName,
			Blind: true,
		})
	} else { // 写入删除前的名称
		marks = append(marks, models.Mark{
			Label: "operation_log.label.medicine",
			Value: oldSupplyPlanMedicine.MedicineName,
			Blind: true,
		})
	}
	err = tools.SaveOperation(ctx, sctx, "operation_log.module.supply_detail", envOID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 生成随机表 项目日志写入
func insertGenerateRandomListLog(ctx *gin.Context, sctx mongo.SessionContext, attribute models.Attribute, randomDesign models.RandomDesign, OID primitive.ObjectID, types int, newRandomDesignInfo models.RandomListConfigPt, OperID primitive.ObjectID, projectSiteStr string, copyEnv string) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if copyEnv != "" {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.env_name",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: copyEnv,
			},
		})
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.name",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newRandomDesignInfo.Name,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.site",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: projectSiteStr,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.initial_number",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newRandomDesignInfo.InitialValue,
		},
	})
	if newRandomDesignInfo.RandomNumberRule == 1 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.end_number",
			Old: models.OperationLogField{
				Type:  1,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  1,
				Value: newRandomDesignInfo.EndValue,
			},
		})
	}
	var weightRatio []string
	for _, group := range newRandomDesignInfo.Groups {
		weightRatio = append(weightRatio, convertor.ToString(group.Name)+"/"+convertor.ToString(group.Ratio))
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.weight_ratio",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  7,
			Value: strings.Join(weightRatio, ","),
		},
	})
	if randomDesign.Info.Type == 1 && newRandomDesignInfo.Blocks != nil && len(newRandomDesignInfo.Blocks) > 0 {
		var blocks []string
		for _, block := range newRandomDesignInfo.Blocks {
			blocks = append(blocks, convertor.ToString(block.BlockLength)+"/"+convertor.ToString(block.BlockNumber))
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.block_configuration",
			Old: models.OperationLogField{
				Type:  1,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  1,
				Value: strings.Join(blocks, ","),
			},
		})
	}
	if randomDesign.Info.Type == 2 && newRandomDesignInfo.Factors != nil && len(newRandomDesignInfo.Factors) > 0 {
		var factors []string
		for _, factor := range newRandomDesignInfo.Factors {
			factors = append(factors, convertor.ToString(factor.Label)+"/"+convertor.ToString(factor.Ratio))
		}
		if attribute.AttributeInfo.InstituteLayered || attribute.AttributeInfo.CountryLayered || attribute.AttributeInfo.RegionLayered {
			factors = append(factors, convertor.ToString(newRandomDesignInfo.Ratio))
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.factor_ratio",
			Old: models.OperationLogField{
				Type:  1,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  1,
				Value: strings.Join(factors, ","),
			},
		})
	}
	if randomDesign.Info.Type == 1 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design",
			TranKey: "operation_log.random_list.block_rule",
			Old: models.OperationLogField{
				Type:  1,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  6,
				Value: newRandomDesignInfo.BlocksRule,
			},
		})
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.random_number_rule",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  6,
			Value: newRandomDesignInfo.RandomNumberRule,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.number_length",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newRandomDesignInfo.NumberLength,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.seed",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newRandomDesignInfo.Seed,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.prefix",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newRandomDesignInfo.Prefix,
		},
	})
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.list",
		Value: newRandomDesignInfo.Name,
		Blind: false,
	})

	//if (OperationLogFieldGroups != nil && len(OperationLogFieldGroups) > 0) && (types == 1 || types == 2 || types == 10) {
	//	newOperationLogFieldGroup := models.OperationLogFieldGroup{
	//		Key:     "operation_log.random_design",
	//		TranKey: "operation_log.random_list.onlyID",
	//		Old: models.OperationLogField{
	//			Type:  2,
	//			Value: nil,
	//		},
	//		New: models.OperationLogField{
	//			Type:  2,
	//			Value: OID,
	//		},
	//	}
	//	// 添加新的 OperationLogFieldGroup 到数组的开头
	//	OperationLogFieldGroups = append([]models.OperationLogFieldGroup{newOperationLogFieldGroup}, OperationLogFieldGroups...)
	//}

	err := tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", OID, types, OperationLogFieldGroups, marks, OperID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// uploadRandomList
func insertUploadRandomListLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, oldRandomDesignInfo models.RandomList, newRandomDesignInfo models.RandomListConfigPt, OID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.name",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newRandomDesignInfo.Name,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.initial_number",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newRandomDesignInfo.InitialValue,
		},
	})

	var weightRatio []string
	for _, block := range newRandomDesignInfo.Groups {
		weightRatio = append(weightRatio, convertor.ToString(block.Name)+"/"+convertor.ToString(block.Ratio))
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.weight_ratio",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: strings.Join(weightRatio, ","),
		},
	})
	var blocks []string
	for _, block := range newRandomDesignInfo.Blocks {
		blocks = append(blocks, convertor.ToString(block.BlockLength)+"/"+convertor.ToString(block.BlockNumber))
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.block_configuration",
		Old: models.OperationLogField{
			Type:  7,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  7,
			Value: strings.Join(blocks, ","),
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.number_length",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newRandomDesignInfo.NumberLength,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.seed",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newRandomDesignInfo.Seed,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.random_design",
		TranKey: "operation_log.random_list.prefix",
		Old: models.OperationLogField{
			Type:  1,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newRandomDesignInfo.Prefix,
		},
	})
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.list",
		Value: newRandomDesignInfo.Name,
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.random_design", envOID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// uploadRandomList
func insertFormLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, oldField models.Field, newField models.Field, OID primitive.ObjectID, copyEnv string) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	if types != 3 {
		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design.form",
				TranKey: "operation_log.form.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design.form",
			TranKey: "operation_log.form.applicationType",
			Old: models.OperationLogField{
				Type:  6,
				Value: oldField.ApplicationType,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: newField.ApplicationType,
			},
		})
		if types == 1 || types == 10 {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design.form",
				TranKey: "operation_log.form.name",
				Old: models.OperationLogField{
					Type:  1,
					Value: oldField.Label,
				},
				New: models.OperationLogField{
					Type:  1,
					Value: newField.Label,
				},
			})
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design.form",
			TranKey: "operation_log.form.variable",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldField.Variable,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newField.Variable,
			},
		})
		// 公式计算 不显示
		if newField.ApplicationType != nil && *newField.ApplicationType != 2 {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design.form",
				TranKey: "operation_log.form.editable",
				Old: models.OperationLogField{
					Type:  4,
					Value: oldField.Modifiable,
				},
				New: models.OperationLogField{
					Type:  4,
					Value: newField.Modifiable,
				},
			})
		}

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design.form",
			TranKey: "operation_log.form.required",
			Old: models.OperationLogField{
				Type:  4,
				Value: oldField.Required,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: newField.Required,
			},
		})

		//if types == 2 {
		//	if newField.Type == "radio" || newField.Type == "checkbox" || newField.Type == "select" {
		//		oldOption := []string{}
		//		for _, option := range oldField.Options {
		//			oldOption = append(oldOption, option.Label)
		//		}
		//		newOption := []string{}
		//		for _, option := range newField.Options {
		//			newOption = append(newOption, option.Label)
		//		}
		//		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		//			Key:     "operation_log.random_design.form",
		//			TranKey: "operation_log.form.options",
		//			Old: models.OperationLogField{
		//				Type:  1,
		//				Value: strings.Join(oldOption, ","),
		//			},
		//			New: models.OperationLogField{
		//				Type:  1,
		//				Value: strings.Join(newOption, ","),
		//			},
		//		})
		//	}
		//}

		if types == 1 || types == 2 || types == 10 || types == 11 {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.random_design.form",
				TranKey: "operation_log.form.type",
				Old: models.OperationLogField{
					Type:  6,
					Value: oldField.Type,
				},
				New: models.OperationLogField{
					Type:  6,
					Value: newField.Type,
				},
			})

			if newField.Type == "input" || newField.Type == "textArea" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.format",
					Old: models.OperationLogField{
						Type:  1,
						Value: locales.Tr(ctx, "operation_log.form.length"),
					},
					New: models.OperationLogField{
						Type:  1,
						Value: locales.Tr(ctx, "operation_log.form.length"),
					},
				})

				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.variableFormat",
					Old: models.OperationLogField{
						Type:  1,
						Value: oldField.Length,
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newField.Length,
					},
				})

			} else if newField.Type == "inputNumber" {
				oldFormatTypeName := "form.control.type.format.numberLength"
				newFormatTypeName := "form.control.type.format.numberLength"
				if newField.FormatType == "decimalLength" {
					oldFormatTypeName = "form.control.type.format.decimalLength"
					newFormatTypeName = "form.control.type.format.decimalLength"
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.format",
					Old: models.OperationLogField{
						Type:  1,
						Value: oldFormatTypeName,
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newFormatTypeName,
					},
				})

				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.variableFormat",
					Old: models.OperationLogField{
						Type:  1,
						Value: oldField.Length,
					},
					New: models.OperationLogField{
						Type:  1,
						Value: newField.Length,
					},
				})

				max := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.max",
				}
				min := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.min",
				}
				if newField.Range != nil {
					max.New = models.OperationLogField{
						Type:  1,
						Value: newField.Range.Max,
					}
					min.New = models.OperationLogField{
						Type:  1,
						Value: newField.Range.Min,
					}
				} else {
					max.New = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
					min.New = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
				}
				if oldField.Range != nil {
					max.Old = models.OperationLogField{
						Type:  1,
						Value: oldField.Range.Max,
					}
					min.Old = models.OperationLogField{
						Type:  1,
						Value: oldField.Range.Min,
					}
				} else {
					max.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
					min.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, max)
				OperationLogFieldGroups = append(OperationLogFieldGroups, min)
			} else if newField.Type == "datePicker" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.format",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldField.DateFormat,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newField.DateFormat,
					},
				})

				max := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.max",
				}
				min := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.min",
				}
				if newField.DateRange != nil {
					maxStr := newField.DateRange.Max
					maxStrEn := newField.DateRange.Max
					if newField.DateRange.Max == "currentTime" {
						maxStr = locales.TrWithLang("zh", "operation_log.form.currentTime")
						maxStrEn = locales.TrWithLang("en", "operation_log.form.currentTime")
					}
					max.New = models.OperationLogField{
						Type:    30,
						Value:   maxStr,
						ENValue: maxStrEn,
					}
					min.New = models.OperationLogField{
						Type:  2,
						Value: newField.DateRange.Min,
					}
				} else {
					max.New = models.OperationLogField{
						Type:  2,
						Value: nil,
					}
					min.New = models.OperationLogField{
						Type:  2,
						Value: nil,
					}
				}
				if oldField.DateRange != nil {
					oldMaxStr := oldField.DateRange.Max
					oldMaxStrEn := oldField.DateRange.Max
					if oldField.DateRange.Max == "currentTime" {
						oldMaxStr = locales.TrWithLang("zh", "operation_log.form.currentTime")
						oldMaxStrEn = locales.TrWithLang("en", "operation_log.form.currentTime")
					}
					max.Old = models.OperationLogField{
						Type:    30,
						Value:   oldMaxStr,
						ENValue: oldMaxStrEn,
					}
					min.Old = models.OperationLogField{
						Type:  2,
						Value: oldField.DateRange.Min,
					}
				} else {
					max.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
					min.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, max)
				OperationLogFieldGroups = append(OperationLogFieldGroups, min)
			} else if newField.Type == "timePicker" {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.format",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldField.TimeFormat,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newField.TimeFormat,
					},
				})
				max := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.max",
				}
				min := models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.min",
					New: models.OperationLogField{
						Type:  2,
						Value: newField.DateRange.Min,
					},
				}
				if newField.DateRange != nil {
					maxStr := newField.DateRange.Max
					maxStrEn := newField.DateRange.Max
					if newField.DateRange.Max == "currentTime" {
						maxStr = locales.TrWithLang("zh", "operation_log.form.currentTime")
						maxStrEn = locales.TrWithLang("en", "operation_log.form.currentTime")
					}
					max.New = models.OperationLogField{
						Type:    30,
						Value:   maxStr,
						ENValue: maxStrEn,
					}
					min.New = models.OperationLogField{
						Type:  2,
						Value: newField.DateRange.Min,
					}
				} else {
					max.New = models.OperationLogField{
						Type:  2,
						Value: nil,
					}
					min.New = models.OperationLogField{
						Type:  2,
						Value: nil,
					}
				}
				if oldField.DateRange != nil {
					oldMaxStr := oldField.DateRange.Max
					oldMaxStrEn := oldField.DateRange.Max
					if oldField.DateRange.Max == "currentTime" {
						oldMaxStr = locales.TrWithLang("zh", "operation_log.form.currentTime")
						oldMaxStrEn = locales.TrWithLang("en", "operation_log.form.currentTime")
					}
					max.Old = models.OperationLogField{
						Type:    30,
						Value:   oldMaxStr,
						ENValue: oldMaxStrEn,
					}
					min.Old = models.OperationLogField{
						Type:  2,
						Value: oldField.DateRange.Min,
					}
				} else {
					max.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
					min.Old = models.OperationLogField{
						Type:  5,
						Value: nil,
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, max)
				OperationLogFieldGroups = append(OperationLogFieldGroups, min)
			}

			if newField.Type == "radio" || newField.Type == "checkbox" || newField.Type == "select" {
				oldOption := []string{}
				for _, option := range oldField.Options {
					oldOption = append(oldOption, option.Label)
				}
				newOption := []string{}
				for _, option := range newField.Options {
					newOption = append(newOption, option.Label)
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.random_design.form",
					TranKey: "operation_log.form.options",
					Old: models.OperationLogField{
						Type:  1,
						Value: strings.Join(oldOption, ","),
					},
					New: models.OperationLogField{
						Type:  1,
						Value: strings.Join(newOption, ","),
					},
				})
			}
		}
		if oldField.Status == nil {
			status := 1
			oldField.Status = &status
		}
		if newField.Status == nil {
			status := 1
			newField.Status = &status
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.random_design.form",
			TranKey: "operation_log.form.status",
			Old: models.OperationLogField{
				Type:  6,
				Value: oldField.Status,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: newField.Status,
			},
		})
	}
	marks := []models.Mark{}
	if types == 1 || types == 10 {
		marks = append(marks, models.Mark{
			Label: "operation_log.label.name",
			Value: newField.Label,
			Blind: false,
		})
	} else {
		marks = append(marks, models.Mark{
			Label: "operation_log.label.name",
			Value: oldField.Label,
			Blind: false,
		})
	}

	if types == 3 || len(OperationLogFieldGroups) != 0 {
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.form", envOID, types, OperationLogFieldGroups, marks, OID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func insertSimulateRandomFormLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, oldSimulateRandomInfo models.SimulateRandomInfo, newSimulateRandomInfo models.SimulateRandomInfo, randomDesign models.RandomDesign, OID primitive.ObjectID, oldRandomListName string, newRandomListName string, copyEnv string) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if copyEnv != "" {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.simulate_random",
			TranKey: "operation_log.simulateRandom.env_name",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: copyEnv,
			},
		})
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.simulate_random",
		TranKey: "operation_log.simulateRandom.name",
		Old: models.OperationLogField{
			Type:  1,
			Value: oldSimulateRandomInfo.Name,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newSimulateRandomInfo.Name,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.simulate_random",
		TranKey: "operation_log.simulateRandom.randomList",
		Old: models.OperationLogField{
			Type:  1,
			Value: oldRandomListName,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newRandomListName,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.simulate_random",
		TranKey: "operation_log.simulateRandom.siteQuantity",
		Old: models.OperationLogField{
			Type:  1,
			Value: oldSimulateRandomInfo.SiteQuantity,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newSimulateRandomInfo.SiteQuantity,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.simulate_random",
		TranKey: "operation_log.simulateRandom.countryQuantity",
		Old: models.OperationLogField{
			Type:  1,
			Value: oldSimulateRandomInfo.CountryQuantity,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newSimulateRandomInfo.CountryQuantity,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.simulate_random",
		TranKey: "operation_log.simulateRandom.regionQuantity",
		Old: models.OperationLogField{
			Type:  1,
			Value: oldSimulateRandomInfo.RegionQuantity,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newSimulateRandomInfo.RegionQuantity,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.simulate_random",
		TranKey: "operation_log.simulateRandom.RunQuantity",
		Old: models.OperationLogField{
			Type:  1,
			Value: oldSimulateRandomInfo.RunQuantity,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newSimulateRandomInfo.RunQuantity,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.simulate_random",
		TranKey: "operation_log.simulateRandom.SubjectQuantity",
		Old: models.OperationLogField{
			Type:  1,
			Value: oldSimulateRandomInfo.SubjectQuantity,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: newSimulateRandomInfo.SubjectQuantity,
		},
	})

	marks := []models.Mark{}
	if newSimulateRandomInfo.Name == oldSimulateRandomInfo.Name {
		if types == 1 {
			marks = append(marks, models.Mark{
				Label: "operation_log.label.simulate_random_name",
				Value: newSimulateRandomInfo.Name,
				Blind: false,
			})
		} else {
			marks = append(marks, models.Mark{
				Label: "operation_log.label.simulate_random_name",
				Value: oldSimulateRandomInfo.Name,
				Blind: false,
			})
		}
	}

	if (OperationLogFieldGroups != nil && len(OperationLogFieldGroups) > 0) && (types == 1 || types == 2 || types == 10) {
		newOperationLogFieldGroup := models.OperationLogFieldGroup{
			Key:     "operation_log.simulateRandom",
			TranKey: "operation_log.simulateRandom.onlyID",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: OID,
			},
		}
		// 添加新的 OperationLogFieldGroup 到数组的开头
		OperationLogFieldGroups = append([]models.OperationLogFieldGroup{newOperationLogFieldGroup}, OperationLogFieldGroups...)
	}
	if newSimulateRandomInfo.FactorRatio != nil && len(newSimulateRandomInfo.FactorRatio) > 0 {
		factors := randomDesign.Info.Factors
		for _, ratioGroup := range newSimulateRandomInfo.FactorRatio {
			for _, ratio := range ratioGroup {
			Loop:
				for _, factor := range factors {
					if ratio.FactorName == factor.Name {
						for _, option := range factor.Options {
							if ratio.OptionValue == option.Value {
								oldValue := ""
								if oldSimulateRandomInfo.FactorRatio != nil && len(oldSimulateRandomInfo.FactorRatio) > 0 {
									for _, oldRatioGroup := range oldSimulateRandomInfo.FactorRatio {
										for _, oldRatio := range oldRatioGroup {
											if oldRatio.FactorName == factor.Name && oldRatio.OptionValue == option.Value {
												ratioValue := "-"
												if oldRatio.Ratio != nil {
													ratioValue = fmt.Sprintf("%d", *oldRatio.Ratio)
												}
												oldValue = fmt.Sprintf("%s:%s/%s", factor.Label, option.Label, ratioValue)
												newValue := ""
												newRatioValue := "-"
												if ratio.Ratio != nil {
													newRatioValue = fmt.Sprintf("%d", *ratio.Ratio)
												}
												newValue = fmt.Sprintf("%s:%s/%s", factor.Label, option.Label, newRatioValue)
												OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
													Key:     "operation_log.simulate_random",
													TranKey: "operation_log.simulateRandom.FactorRatio",
													Old: models.OperationLogField{
														Type:  2,
														Value: oldValue,
													},
													New: models.OperationLogField{
														Type:  2,
														Value: newValue,
													},
												})
												break Loop
											}
										}
									}
								} else {
									newValue := ""
									newRatioValue := "-"
									if ratio.Ratio != nil {
										newRatioValue = fmt.Sprintf("%d", *ratio.Ratio)
									}
									newValue = fmt.Sprintf("%s:%s/%s", factor.Label, option.Label, newRatioValue)
									OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
										Key:     "operation_log.simulate_random",
										TranKey: "operation_log.simulateRandom.FactorRatio",
										Old: models.OperationLogField{
											Type:  2,
											Value: oldValue,
										},
										New: models.OperationLogField{
											Type:  2,
											Value: newValue,
										},
									})
									break Loop
								}
							}
						}
					}
				}
			}
		}
	}
	if types == 3 || len(OperationLogFieldGroups) != 0 {
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.simulate_random", envOID, types, OperationLogFieldGroups, marks, OID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

// 发送数据 项目日志写入
func insertPushLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, edcPushAll []models.EdcPush, operatorID primitive.ObjectID) error {

	var OperationLogFieldGroups []models.OperationLogFieldGroup
	for _, push := range edcPushAll {
		if push.SourceType == 1 { // 登记
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.registered",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		} else if push.SourceType == 2 { // 修改
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.update",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		} else if push.SourceType == 3 { // 随机
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.random",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		} else if push.SourceType == 4 { // 发药
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.dispense",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		} else if push.SourceType == 5 { // 访视外发药
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.out_visit_dispensing",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		} else if push.SourceType == 6 { // 药物替换
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.replace",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		} else if push.SourceType == 7 { // 药物补发
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.reissue",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		} else if push.SourceType == 8 { // 药物撤销
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.cancel",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		} else if push.SourceType == 9 { // 药物取回
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.retrieval",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		} else if push.SourceType == 10 { // 实际用药
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.realDispensing",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		} else if push.SourceType == 11 { // 受试者替换
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.subjectReplace",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		} else { // 未知
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.push",
				TranKey: "operation_log.push.unknown",
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
				New: models.OperationLogField{
					Type:  2,
					Value: push.Content.SubjectNo,
				},
			})
		}
	}

	marks := []models.Mark{}
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.push", OID, types, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 批次管理操作日志
func insertBatchManagementLog(ctx *gin.Context, OID primitive.ObjectID, batchInfo models.BatchInfo, operatorID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	updateBatch := batchInfo.UpdateBatchs[0]
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.updateBatch.batch",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: updateBatch.BatchNumber,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.updateBatch.expirationDate",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: updateBatch.ExpirationDate,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.updateBatch.name",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: updateBatch.Name,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.updateBatch.status",
		Old: models.OperationLogField{
			Type:  5,
			Value: nil,
		},
		New: models.OperationLogField{
			Type:  5,
			Value: batchInfo.Status,
		},
	})
	if updateBatch.Name == "-" {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.updateBatch.updateCount",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: batchInfo.UpdateCount,
			},
		})
	} else {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.updateBatch.updateCount",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: updateBatch.Count,
			},
		})
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.updateBatch.updateBatch",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: batchInfo.UpdateBatch,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.medicinesList",
		TranKey: "operation_log.updateBatch.updateExpirationDate",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: batchInfo.UpdateExpirationDate,
		},
	})

	marks := []models.Mark{}
	err := tools.SaveOperation(ctx, nil, "operation_log.module.updateBatch", OID, 5, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 人员管理日志——新增
func insertAddProjectUserLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, envUser models.ProjectEnvironmentUser, operatorID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	langZh := ""
	langEn := ""
	if envUser.EmailLanguage != nil {
		if *envUser.EmailLanguage == "zh" {
			langZh = "中文"
			langEn = "Chinese"
		} else if *envUser.EmailLanguage == "en" {
			langZh = "英文"
			langEn = "English"
		}
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.projectUser",
		TranKey: "operation_log.projectUser.emailLanguage",
		Old: models.OperationLogField{
			Type:    30,
			Value:   "",
			ENValue: "",
		},
		New: models.OperationLogField{
			Type:    30,
			Value:   langZh,
			ENValue: langEn,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.projectUser",
		TranKey: "operation_log.projectUser.email",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: envUser.Email,
		},
	})
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.projectUser",
		Value: envUser.Email,
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.projectUser", OID, types, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 人员管理日志——揭盲码
func insertUnblindingCodeProjectUserLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, count int, userNameEmail string, operatorID primitive.ObjectID) error {

	var OperationLogFieldGroups []models.OperationLogFieldGroup

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.projectUser",
		TranKey: "operation_log.projectUser.unblindingCode",
		Old: models.OperationLogField{
			Type:  1,
			Value: nil,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: count,
		},
	})

	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.projectUser",
		Value: userNameEmail,
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.projectUser_role", OID, types, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 人员管理日志——角色
func insertRolesProjectUserLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, oldEnvUser models.UserProjectEnvironment, newEnvUser models.ProjectEnvironmentUser, userNameEmail string, operatorID primitive.ObjectID) error {

	var OperationLogFieldGroups []models.OperationLogFieldGroup

	//分配新增
	addRolesIds := slice.Difference(newEnvUser.Roles, oldEnvUser.Roles)

	//取消角色
	cancelRolesIds := slice.Difference(oldEnvUser.Roles, newEnvUser.Roles)

	addRoles := ""
	if len(addRolesIds) > 0 {
		projectRoles := make([]models.ProjectRolePermission, 0)
		cursor, err := tools.Database.Collection("project_role_permission").Find(sctx, bson.M{"_id": bson.M{"$in": addRolesIds}})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(sctx, &projectRoles)
		if err != nil {
			return errors.WithStack(err)
		}
		roleNames := slice.Map(projectRoles, func(index int, item models.ProjectRolePermission) string {
			return item.Name
		})
		addRoles = strings.Join(roleNames, "、")
	}

	cancelRoles := ""
	if len(cancelRolesIds) > 0 {
		projectRoles := make([]models.ProjectRolePermission, 0)
		cursor, err := tools.Database.Collection("project_role_permission").Find(sctx, bson.M{"_id": bson.M{"$in": cancelRolesIds}})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(sctx, &projectRoles)
		if err != nil {
			return errors.WithStack(err)
		}
		roleNames := slice.Map(projectRoles, func(index int, item models.ProjectRolePermission) string {
			return item.Name
		})
		cancelRoles = strings.Join(roleNames, "、")
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.projectUser",
		TranKey: "operation_log.projectUser.addRoles",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: addRoles,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.projectUser",
		TranKey: "operation_log.projectUser.cancelRoles",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: cancelRoles,
		},
	})

	if len(addRolesIds) > 0 || len(cancelRolesIds) > 0 {
		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.projectUser",
			Value: userNameEmail,
			Blind: false,
		})
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.projectUser_role", OID, types, OperationLogFieldGroups, marks, operatorID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

// 人员管理日志——中心
func insertSitesProjectUserLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, oldSites []primitive.ObjectID, newSites []primitive.ObjectID, userEmail string, operatorID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	//新增
	addIds := slice.Difference(newSites, oldSites)

	//取消
	cancelIds := slice.Difference(oldSites, newSites)

	addSitesName := ""
	if len(addIds) > 0 {
		projectSites := make([]models.ProjectSite, 0)
		cursor, err := tools.Database.Collection("project_site").Find(sctx, bson.M{"_id": bson.M{"$in": addIds}})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(sctx, &projectSites)
		if err != nil {
			return errors.WithStack(err)
		}
		siteNames := slice.Map(projectSites, func(index int, item models.ProjectSite) string {
			return models.GetProjectSiteName(ctx, item)
		})
		addSitesName = strings.Join(siteNames, "、")

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.addSites",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: addSitesName,
			},
		})
	}

	cancelSitesName := ""
	if len(cancelIds) > 0 {
		projectSites := make([]models.ProjectSite, 0)
		cursor, err := tools.Database.Collection("project_site").Find(sctx, bson.M{"_id": bson.M{"$in": cancelIds}})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(sctx, &projectSites)
		if err != nil {
			return errors.WithStack(err)
		}
		siteNames := slice.Map(projectSites, func(index int, item models.ProjectSite) string {
			return models.GetProjectSiteName(ctx, item)
		})
		cancelSitesName = strings.Join(siteNames, "、")
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.cancelSites",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: cancelSitesName,
			},
		})
	}

	if len(addIds) > 0 || len(cancelIds) > 0 {
		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.projectUser",
			Value: userEmail,
			Blind: false,
		})
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.projectUser_site", OID, types, OperationLogFieldGroups, marks, operatorID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

// 人员管理日志——仓库
func insertDepotsProjectUserLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, oldDepots []primitive.ObjectID, newDepots []primitive.ObjectID, userEmail string, operatorID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	//新增
	addIds := slice.Difference(newDepots, oldDepots)

	//取消
	cancelIds := slice.Difference(oldDepots, newDepots)

	addDepotsName := ""
	if len(addIds) > 0 {
		newProjectStorehouses := make([]models.ProjectStorehouse, 0)
		cursor, err := tools.Database.Collection("project_storehouse").Find(sctx, bson.M{"_id": bson.M{"$in": addIds}})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(sctx, &newProjectStorehouses)
		if err != nil {
			return errors.WithStack(err)
		}
		storehouses := make([]models.Storehouse, 0)
		storehouseIds := slice.Map(newProjectStorehouses, func(index int, item models.ProjectStorehouse) primitive.ObjectID {
			return item.StorehouseID
		})
		cursor, err = tools.Database.Collection("storehouse").Find(sctx, bson.M{"_id": bson.M{"$in": storehouseIds}})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(sctx, &storehouses)
		if err != nil {
			return errors.WithStack(err)
		}
		depotName := slice.Map(storehouses, func(index int, item models.Storehouse) string {
			return item.Name
		})
		addDepotsName = strings.Join(depotName, "、")

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.addDepots",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: addDepotsName,
			},
		})

	}

	cancelDepotsName := ""
	if len(cancelIds) > 0 {
		newProjectStorehouses := make([]models.ProjectStorehouse, 0)
		cursor, err := tools.Database.Collection("project_storehouse").Find(sctx, bson.M{"_id": bson.M{"$in": cancelIds}})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(sctx, &newProjectStorehouses)
		if err != nil {
			return errors.WithStack(err)
		}
		storehouses := make([]models.Storehouse, 0)
		storehouseIds := slice.Map(newProjectStorehouses, func(index int, item models.ProjectStorehouse) primitive.ObjectID {
			return item.StorehouseID
		})
		cursor, err = tools.Database.Collection("storehouse").Find(sctx, bson.M{"_id": bson.M{"$in": storehouseIds}})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(sctx, &storehouses)
		if err != nil {
			return errors.WithStack(err)
		}
		depotName := slice.Map(storehouses, func(index int, item models.Storehouse) string {
			return item.Name
		})
		cancelDepotsName = strings.Join(depotName, "、")

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.cancelDepots",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: cancelDepotsName,
			},
		})
	}

	if len(addIds) > 0 || len(cancelIds) > 0 {
		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.label.projectUser",
			Value: userEmail,
			Blind: false,
		})
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.projectUser_depot", OID, types, OperationLogFieldGroups, marks, operatorID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

// 人员管理日志——App账号
func insertAppProjectUserLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, old bool, new bool, userEmail string, operatorID primitive.ObjectID) error {
	OperationLogFieldGroups := make([]models.OperationLogFieldGroup, 0)

	if old != new {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.App",
			Old: models.OperationLogField{
				Type:  4,
				Value: old,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new,
			},
		})
	}

	if OperationLogFieldGroups != nil && len(OperationLogFieldGroups) > 0 {
		marks := make([]models.Mark, 0)
		marks = append(marks, models.Mark{
			Label: "operation_log.label.projectUser",
			Value: userEmail,
			Blind: false,
		})
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.projectUser", OID, types, OperationLogFieldGroups, marks, operatorID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

// 人员管理日志——解绑/再次授权
func insertUnbindInviteProjectUserLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, old int, new int, userNameEmail string, operatorID primitive.ObjectID, emailLanguage string) error {

	var OperationLogFieldGroups []models.OperationLogFieldGroup

	if len(emailLanguage) > 0 {
		langZh := ""
		langEn := ""
		if emailLanguage == "zh" {
			langZh = "中文"
			langEn = "Chinese"
		} else if emailLanguage == "en" {
			langZh = "英文"
			langEn = "English"
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.emailLanguage",
			Old: models.OperationLogField{
				Type:    30,
				Value:   "",
				ENValue: "",
			},
			New: models.OperationLogField{
				Type:    30,
				Value:   langZh,
				ENValue: langEn,
			},
		})
	}

	if types == 7 || types == 8 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.status",
			Old: models.OperationLogField{
				Type:  6,
				Value: old,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new,
			},
		})
	}

	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.projectUser",
		Value: userNameEmail,
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.projectUser", OID, types, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 客户用户 再次邀请12/关闭13/设置14/取消15
func insertCustomerUserLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, old int, new int, userNameEmail string, operatorID primitive.ObjectID, emailLanguage string) error {

	var OperationLogFieldGroups []models.OperationLogFieldGroup

	if len(emailLanguage) > 0 {
		langZh := ""
		langEn := ""
		if emailLanguage == "zh" {
			langZh = "中文"
			langEn = "Chinese"
		} else if emailLanguage == "en" {
			langZh = "英文"
			langEn = "English"
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.emailLanguage",
			Old: models.OperationLogField{
				Type:    30,
				Value:   "",
				ENValue: "",
			},
			New: models.OperationLogField{
				Type:    30,
				Value:   langZh,
				ENValue: langEn,
			},
		})
	}
	if types == 13 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.status",
			Old: models.OperationLogField{
				Type:  6,
				Value: old,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new,
			},
		})
	}

	if types == 14 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.addRoles",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: "Customer-Admin",
			},
		})
	}

	if types == 15 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.cancelRoles",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: "Customer-Admin",
			},
		})
	}

	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.projectUser",
		Value: userNameEmail,
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.user", OID, types, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func projectTimeZoneLog(tz string) (string, string) {

	type TimeZoneInfo struct {
		ID       string `json:"id"`
		Name     string `json:"name"`
		Location string `json:"location"`
		Zh       string `json:"zh"`
		En       string `json:"en"`
	}

	// 你的 JSON 数据
	jsonData := `[
		{
			id: "Dateline Standard Time",
			name: "国际日期变更线标准时间",
			location: "Etc/GMT+12",
			zh: "国际日期变更线西",
			en: "International Date Line West"
		},
		{
			id: "UTC-11",
			name: "UTC-11",
			location: "Etc/GMT+11",
			zh: "协调世界时-11",
			en: "Coordinated Universal Time-11"
		},
		{
			id: "Aleutian Standard Time",
			name: "阿留申群岛标准时间",
			location: "America/Adak",
			zh: "阿留申群岛",
			en: "Aleutian Islands"
		},
		{
			id: "Hawaiian Standard Time",
			name: "夏威夷标准时间",
			location: "US/Hawaii",
			zh: "夏威夷",
			en: "Hawaii"
		},
		{
			id: "Marquesas Standard Time",
			name: "马克萨斯群岛标准时间",
			location: "Pacific/Marquesas",
			zh: "马克萨斯群岛",
			en: "Marquesas Islands"
		},
		{
			id: "Alaskan Standard Time",
			name: "阿拉斯加标准时间",
			location: "US/Alaska",
			zh: "阿拉斯加",
			en: "Alaska"
		},
		{
			id: "UTC-09",
			name: "UTC-09",
			location: "Etc/GMT+9",
			zh: "协调世界时-09",
			en: "Coordinated Universal Time-09"
		},
		{
			id: "Pacific Standard Time (Mexico)",
			name: "太平洋标准时间(墨西哥)",
			location: "America/Tijuana",
			zh: "下加利福尼亚州",
			en: "Baja California"
		},
		{
			id: "UTC-08",
			name: "UTC-08",
			location: "Etc/GMT+8",
			zh: "协调世界时-08",
			en: "Coordinated Universal Time-08"
		},
		{
			id: "Pacific Standard Time",
			name: "太平洋标准时间",
			location: "America/Los_Angeles",
			zh: "太平洋时间(美国和加拿大)",
			en: "Pacific Time (USA and Canada)"
		},
		{
			id: "US Mountain Standard Time",
			name: "美国山地标准时间",
			location: "America/Phoenix",
			zh: "亚利桑那",
			en: "Arizona"
		},
		{
			id: "Mountain Standard Time",
			name: "山地标准时间",
			location: "MST",
			zh: "山地标准时间",
			en: "Mountain Standard Time"
		},
		{
			id: "Mountain Time",
			name: "山地时间",
			location: "MST7MDT",
			zh: "山地时间(美国和加拿大)",
			en: "Mountain Time(USA and Canada)"
		},
		{
			id: "Mountain Standard Time (Mexico)",
			name: "山地标准时间(墨西哥)",
			location: "America/Chihuahua",
			zh: "拉巴斯、马扎特兰",
			en: "La Paz, Mazatlan"
		},
		{
			id: "Yukon Standard Time",
			name: "育空标准时间",
			location: "Canada/Yukon",
			zh: "育空",
			en: "Yukon"
		},
		{
			id: "Central America Standard Time",
			name: "中美洲标准时间",
			location: "America/Guatemala",
			zh: "中美洲",
			en: "Central America"
		},
		{
			id: "Central Standard Time",
			name: "中部标准时间",
			location: "America/Chicago",
			zh: "中部时间(美国和加拿大)",
			en: "Central Time (USA and Canada)"
		},
		{
			id: "Easter Island Standard Time",
			name: "复活节岛标准时间",
			location: "Chile/EasterIsland",
			zh: "复活节岛",
			en: "Easter Island"
		},
		{
			id: "Central Standard Time (Mexico)",
			name: "中部标准时间(墨西哥)",
			location: "America/Mexico_City",
			zh: "瓜达拉哈拉，墨西哥城，蒙特雷",
			en: "Guadalajara, Mexico City, Monterey"
		},
		{
			id: "Canada Central Standard Time",
			name: "加拿大中部标准时间",
			location: "America/Regina",
			zh: "萨斯喀彻温",
			en: "Saskatchewan"
		},
		{
			id: "Eastern Standard Time",
			name: "东部标准时间",
			location: "America/New_York",
			zh: "东部时间(美国和加拿大)",
			en: "Eastern Time (USA and Canada)"
		},
		{
			id: "Eastern Standard Time (Mexico)",
			name: "东部标准时间(墨西哥)",
			location: "America/Cancun",
			zh: "切图马尔",
			en: "Chetumal"
		},
		{
			id: "US Eastern Standard Time",
			name: "美国东部标准时间",
			location: "America/Indiana/Indianapolis",
			zh: "印地安那州(东部)",
			en: "Indiana (Eastern)"
		},
		{
			id: "Cuba Standard Time",
			name: "古巴标准时间",
			location: "Cuba",
			zh: "哈瓦那",
			en: "Havana "
		},
		{
			id: "SA Pacific Standard Time",
			name: "南美洲太平洋标准时间",
			location: "America/Bogota",
			zh: "波哥大，利马，基多，里奥布朗库",
			en: "Bogota, Lima, Quito, Rio Bronco"
		},
		{
			id: "Haiti Standard Time",
			name: "海地标准时间",
			location: "America/Port-au-Prince",
			zh: "海地",
			en: "Haiti"
		},
		{
			id: "Turks And Caicos Standard Time",
			name: "特克斯和凯科斯群岛标准时间",
			location: "America/Grand_Turk",
			zh: "特克斯和凯科斯群岛",
			en: "Turks and Caicos Islands"
		},
		{
			id: "SA Western Standard Time",
			name: "南美洲西部标准时间",
			location: "America/La_Paz",
			zh: "乔治敦，拉巴斯，马瑙斯，圣胡安",
			en: "Georgetown, La Paz, Manaus, San Juan"
		},
		{
			id: "Paraguay Standard Time",
			name: "巴拉圭标准时间",
			location: "America/Asuncion",
			zh: "亚松森",
			en: "Asuncion"
		},
		{
			id: "Venezuela Standard Time",
			name: "委内瑞拉标准时间",
			location: "America/Caracas",
			zh: "加拉加斯",
			en: "Caracas"
		},
		{
			id: "Pacific SA Standard Time",
			name: "太平洋南美洲标准时间",
			location: "America/Santiago",
			zh: "圣地亚哥",
			en: "Santiago"
		},
		{
			id: "Atlantic Standard Time",
			name: "大西洋标准时间",
			location: "America/Halifax",
			zh: "大西洋时间(加拿大)",
			en: "Atlantic Time (Canada)"
		},
		{
			id: "Central Brazilian Standard Time",
			name: "巴西中部标准时间",
			location: "America/Cuiaba",
			zh: "库亚巴",
			en: "Manaus"
		},
		{
			id: "Newfoundland Standard Time",
			name: "纽芬兰标准时间",
			location: "America/St_Johns",
			zh: "纽芬兰",
			en: "Newfoundland"
		},
		{
			id: "SA Eastern Standard Time",
			name: "南美洲东部标准时间",
			location: "America/Cayenne",
			zh: "卡宴，福塔雷萨",
			en: "Cayenne, Fortaleza"
		},
		{
			id: "Saint Pierre Standard Time",
			name: "圣皮埃尔标准时间",
			location: "America/Miquelon",
			zh: "圣皮埃尔和密克隆群岛",
			en: "Saint Pierre and Miquelon Islands"
		},
		{
			id: "E. South America Standard Time",
			name: "东部南美洲标准时间",
			location: "America/Sao_Paulo",
			zh: "巴西利亚",
			en: "Brasilia"
		},
		{
			id: "Argentina Standard Time",
			name: "阿根廷标准时间",
			location: "America/Buenos_Aires",
			zh: "布宜诺斯艾利斯",
			en: "Buenos Aires"
		},
		{
			id: "Bahia Standard Time",
			name: "巴伊亚标准时间",
			location: "America/Bahia",
			zh: "萨尔瓦多",
			en: "El Salvador"
		},
		{
			id: "Montevideo Standard Time",
			name: "蒙得维的亚标准时间",
			location: "America/Montevideo",
			zh: "蒙得维的亚",
			en: "Montevideo"
		},
		{
			id: "Magallanes Standard Time",
			name: "麦哲伦标准时间",
			location: "America/Punta_Arenas",
			zh: "蓬塔阿雷纳斯",
			en: "Punta Arenas"
		},
		{
			id: "Tocantins Standard Time",
			name: "托坎廷斯标准时间",
			location: "America/Araguaina",
			zh: "阿拉瓜伊纳",
			en: "Araguaina"
		},
		{
			id: "Greenland Standard Time",
			name: "格陵兰标准时间",
			location: "America/Godthab",
			zh: "格陵兰",
			en: "Greenland"
		},
		{
			id: "UTC-02",
			name: "UTC-02",
			location: "Etc/GMT+2",
			zh: "协调世界时-02",
			en: "Coordinated Universal Time-02"
		},
		{
			id: "Azores Standard Time",
			name: "亚速尔群岛标准时间",
			location: "Atlantic/Azores",
			zh: "亚速尔群岛",
			en: "Azores"
		},
		{
			id: "Cape Verde Standard Time",
			name: "佛得角标准时间",
			location: "Atlantic/Cape_Verde",
			zh: "佛得角群岛",
			en: "Cape Verde Is."
		},
		{
			id: "UTC",
			name: "协调世界时",
			location: "Etc/GMT",
			zh: "协调世界时",
			en: "Coordinated Universal Time"
		},
		{
			id: "Sao Tome Standard Time",
			name: "圣多美标准时",
			location: "Africa/Sao_Tome",
			zh: "圣多美",
			en: "Sao Tome"
		},
		{
			id: "Greenwich Standard Time",
			name: "格林威治标准时间",
			location: "Atlantic/Reykjavik",
			zh: "蒙罗维亚，雷克雅未克",
			en: "Monrovia, Reykjavik"
		},
		{
			id: "GMT Standard Time",
			name: "GMT 标准时间",
			location: "Europe/Dublin",
			zh: "都柏林，爱丁堡，里斯本，伦敦",
			en: "Greenwich Mean Time : Dublin, Edinburgh, Lisbon, London"
		},
		{
			id: "Morocco Standard Time",
			name: "摩洛哥标准时间",
			location: "Africa/Casablanca",
			zh: "卡萨布兰卡",
			en: "Casablanca"
		},
		{
			id: "W. Central Africa Standard Time",
			name: "中非西部标准时间",
			location: "Africa/Lagos",
			zh: "中非西部",
			en: "West Central Africa"
		},
		{
			id: "Romance Standard Time",
			name: "罗马标准时间",
			location: "Europe/Brussels",
			zh: "布鲁塞尔，哥本哈根，马德里，巴黎",
			en: "Brussels, Copenhagen, Madrid, Paris"
		},
		{
			id: "Central European Standard Time",
			name: "中欧的标准时间",
			location: "Europe/Sarajevo",
			zh: "萨拉热窝，斯科普里，华沙，萨格勒布",
			en: "Sarajevo, Skopje, Warsaw, Zagreb"
		},
		{
			id: "Central Europe Standard Time",
			name: "中欧标准时间",
			location: "Europe/Belgrade",
			zh: "贝尔格莱德，布拉迪斯拉发，布达佩斯，卢布尔雅那，布拉格",
			en: "Belgrade, Bratislava, Budapest, Ljubljana, Prague"
		},
		{
			id: "W. Europe Standard Time",
			name: "西欧标准时间",
			location: "Europe/Amsterdam",
			zh: "阿姆斯特丹，柏林，伯尔尼，罗马，斯德哥尔摩，维也纳",
			en: "Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna"
		},
		{
			id: "West Bank Standard Time",
			name: "西岸加沙标准时间",
			location: "Asia/Gaza",
			zh: "加沙，希伯伦",
			en: "Gaza, Hebron"
		},
		{
			id: "Kaliningrad Standard Time",
			name: "俄罗斯 TZ 1 标准时间",
			location: "Europe/Kaliningrad",
			zh: "加里宁格勒",
			en: "Kaliningrad"
		},
		{
			id: "South Africa Standard Time",
			name: "南非标准时间",
			location: "Africa/Harare",
			zh: "哈拉雷，比勒陀利亚",
			en: "Harare, Pretoria"
		},
		{
			id: "Sudan Standard Time",
			name: "苏丹标准时",
			location: "Africa/Khartoum",
			zh: "喀土穆",
			en: "Khartoum"
		},
		{
			id: "E. Europe Standard Time",
			name: "东欧标准时间",
			location: "Europe/Bucharest",
			zh: "基希讷乌",
			en: "Chisinau"
		},
		{
			id: "Egypt Standard Time",
			name: "埃及标准时间",
			location: "Africa/Cairo",
			zh: "开罗",
			en: "Cairo"
		},
		{
			id: "South Sudan Standard Time",
			name: "南苏丹标准时间",
			location: "Africa/Juba",
			zh: "朱巴",
			en: "Juba"
		},
		{
			id: "Namibia Standard Time",
			name: "纳米比亚标准时间",
			location: "Africa/Windhoek",
			zh: "温得和克",
			en: "Windhoek"
		},
		{
			id: "Libya Standard Time",
			name: "利比亚标准时间",
			location: "Africa/Tripoli",
			zh: "的黎波里",
			en: "Tripoli"
		},
		{
			id: "Israel Standard Time",
			name: "耶路撒冷标准时间",
			location: "Asia/Jerusalem",
			zh: "耶路撒冷",
			en: "Jerusalem"
		},
		{
			id: "Middle East Standard Time",
			name: "中东标准时间",
			location: "Asia/Beirut",
			zh: "贝鲁特",
			en: "Beirut"
		},
		{
			id: "FLE Standard Time",
			name: "FLE 标准时间",
			location: "Europe/Helsinki",
			zh: "赫尔辛基，基辅，里加，索非亚，塔林，维尔纽斯",
			en: "Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius"
		},
		{
			id: "GTB Standard Time",
			name: "GTB 标准时间",
			location: "Europe/Athens",
			zh: "雅典，布加勒斯特",
			en: "Athens, Bucharest, Istanbul"
		},
		{
			id: "Turkey Standard Time",
			name: "土耳其标准时间",
			location: "Europe/Istanbul",
			zh: "伊斯坦布尔",
			en: "Istanbul"
		},
		{
			id: "Volgograd Standard Time",
			name: "伏尔加格勒标准时间",
			location: "Europe/Volgograd",
			zh: "伏尔加格勒",
			en: "Volgograd"
		},
		{
			id: "E. Africa Standard Time",
			name: "东非标准时间",
			location: "Africa/Nairobi",
			zh: "内罗毕",
			en: "Nairobi"
		},
		{
			id: "Syria Standard Time",
			name: "叙利亚标准时间",
			location: "Asia/Damascus",
			zh: "大马士革",
			en: "Damascus"
		},
		{
			id: "Jordan Standard Time",
			name: "约旦标准时间",
			location: "Asia/Amman",
			zh: "安曼",
			en: "Amman"
		},
		{
			id: "Arabic Standard Time",
			name: "阿拉伯 (Arabic) 标准时间",
			location: "Asia/Baghdad",
			zh: "巴格达",
			en: "Baghdad"
		},
		{
			id: "Belarus Standard Time",
			name: "白俄罗斯标准时间",
			location: "Europe/Minsk",
			zh: "明斯克",
			en: "Minsk"
		},
		{
			id: "Arab Standard Time",
			name: "阿拉伯 (Arab) 标准时间",
			location: "Asia/Kuwait",
			zh: "科威特，利雅得",
			en: "Kuwait, Riyadh"
		},
		{
			id: "Russian Standard Time",
			name: "俄罗斯 TZ 2 标准时间",
			location: "Europe/Moscow",
			zh: "莫斯科，圣彼得堡",
			en: "Moscow, St. Petersburg"
		},
		{
			id: "Iran Standard Time",
			name: "伊朗标准时间",
			location: "Asia/Tehran",
			zh: "德黑兰",
			en: "Tehran"
		},
		{
			id: "Caucasus Standard Time",
			name: "高加索标准时间",
			location: "Asia/Yerevan",
			zh: "埃里温",
			en: "Yerevan"
		},
		{
			id: "Azerbaijan Standard Time",
			name: "阿塞拜疆标准时间",
			location: "Asia/Baku",
			zh: "巴库",
			en: "Baku"
		},
		{
			id: "Georgian Standard Time",
			name: "格鲁吉亚标准时间",
			location: "Asia/Tbilisi",
			zh: "第比利斯",
			en: "Tbilisi"
		},
		{
			id: "Saratov Standard Time",
			name: "萨拉托夫标准时间",
			location: "Europe/Saratov",
			zh: "萨拉托夫",
			en: "Saratov"
		},
		{
			id: "Mauritius Standard Time",
			name: "毛里求斯标准时间",
			location: "Indian/Mauritius",
			zh: "路易港",
			en: "Port Louis"
		},
		{
			id: "Arabian Standard Time",
			name: "阿拉伯半岛标准时间",
			location: "Asia/Dubai",
			zh: "阿布扎比，马斯喀特",
			en: "Abu Dhabi, Muscat"
		},
		{
			id: "Astrakhan Standard Time",
			name: "阿斯特拉罕标准时间",
			location: "Europe/Astrakhan",
			zh: "阿斯特拉罕，乌里扬诺夫斯克",
			en: "Astrakhan, Ulyanovsk"
		},
		{
			id: "Afghanistan Standard Time",
			name: "阿富汗标准时间",
			location: "Asia/Kabul",
			zh: "喀布尔",
			en: "Kabul"
		},
		{
			id: "Pakistan Standard Time",
			name: "巴基斯坦标准时间",
			location: "Asia/Karachi",
			zh: "伊斯兰堡，卡拉奇",
			en: "Islamabad, Karachi"
		},
		{
			id: "Ekaterinburg Standard Time",
			name: "俄罗斯 TZ 4 标准时间",
			location: "Asia/Yekaterinburg",
			zh: "叶卡捷琳堡",
			en: "Yekaterinburg"
		},
		{
			id: "West Asia Standard Time",
			name: "西亚标准时间",
			location: "Asia/Tashkent",
			zh: "阿什哈巴德，塔什干",
			en: "Ashgabat, Tashkent"
		},
		{
			id: "Qyzylorda Standard Time",
			name: "克孜洛尔达标准时间",
			location: "Asia/Qyzylorda",
			zh: "阿斯塔纳",
			en: "Qyzylorda"
		},
		{
			id: "Sri Lanka Standard Time",
			name: "斯里兰卡标准时间",
			location: "Asia/Colombo",
			zh: "斯里加亚渥登普拉",
			en: "Sri Jayawardenepura"
		},
		{
			id: "India Standard Time",
			name: "印度标准时间",
			location: "Asia/Kolkata",
			zh: "钦奈，加尔各答，孟买，新德里",
			en: "Chennai, Kolkata, Mumbai, New Delhi"
		},
		{
			id: "Nepal Standard Time",
			name: "尼泊尔标准时间",
			location: "Asia/Katmandu",
			zh: "加德满都",
			en: "Katmandu"
		},
		{
			id: "Central Asia Standard Time",
			name: "中亚标准时间",
			location: "Asia/Bishkek",
			zh: "比什凯克",
			en: "Bishkek"
		},
		{
			id: "Bangladesh Standard Time",
			name: "孟加拉国标准时间",
			location: "Asia/Dhaka",
			zh: "达卡",
			en: "Dhaka"
		},
		{
			id: "Omsk Standard Time",
			name: "鄂木斯克标准时间",
			location: "Asia/Omsk",
			zh: "鄂木斯克",
			en: "Omsk"
		},
		{
			id: "Myanmar Standard Time",
			name: "缅甸标准时间",
			location: "Asia/Rangoon",
			zh: "仰光",
			en: "Yangon (Rangoon)"
		},
		{
			id: "North Asia Standard Time",
			name: "俄罗斯 TZ 6 标准时间",
			location: "Asia/Krasnoyarsk",
			zh: "克拉斯诺亚尔斯克",
			en: "Krasnoyarsk"
		},
		{
			id: "Altai Standard Time",
			name: "阿尔泰标准时间",
			location: "Asia/Barnaul",
			zh: "巴尔瑙尔，戈尔诺-阿尔泰斯克",
			en: "Barnaul, Gorno Altysk"
		},
		{
			id: "Tomsk Standard Time",
			name: "托木斯克标准时间",
			location: "Asia/Tomsk",
			zh: "托木斯克",
			en: "Tomsk"
		},
		{
			id: "N. Central Asia Standard Time",
			name: "新西伯利亚标准时间",
			location: "Asia/Novosibirsk",
			zh: "新西伯利亚",
			en: "Novosibirsk"
		},
		{
			id: "SE Asia Standard Time",
			name: "东南亚标准时间",
			location: "Asia/Bangkok",
			zh: "曼谷，河内，雅加达",
			en: "Bangkok, Hanoi, Jakarta"
		},
		{
			id: "W. Mongolia Standard Time",
			name: "西蒙古标准时间",
			location: "Asia/Hovd",
			zh: "科布多",
			en: "Hovd"
		},
		{
			id: "Ulaanbaatar Standard Time",
			name: "乌兰巴托标准时间",
			location: "Asia/Ulaanbaatar",
			zh: "乌兰巴托",
			en: "Ulaanbaatar"
		},
		{
			id: "North Asia East Standard Time",
			name: "俄罗斯 TZ 7 标准时间",
			location: "Asia/Irkutsk",
			zh: "伊尔库茨克",
			en: "Irkutsk"
		},
		{
			id: "China Standard Time",
			name: "中国标准时间",
			location: "Asia/Shanghai",
			zh: "北京，重庆，香港，乌鲁木齐",
			en: "Beijing, Chongqing, Hong Kong, Urumqi"
		},
		{
			id: "Taipei Standard Time",
			name: "台北标准时间",
			location: "Asia/Taipei",
			zh: "台北",
			en: "Taipei"
		},
		{
			id: "Singapore Standard Time",
			name: "马来西亚半岛标准时间",
			location: "Asia/Singapore",
			zh: "吉隆坡，新加坡",
			en: "Kuala Lumpur, Singapore"
		},
		{
			id: "W. Australia Standard Time",
			name: "澳大利亚西部标准时间",
			location: "Australia/Perth",
			zh: "珀斯",
			en: "Perth"
		},
		{
			id: "Aus Central W. Standard Time",
			name: "澳大利亚中西部标准时间",
			location: "Australia/Eucla",
			zh: "尤克拉",
			en: "Eucla"
		},
		{
			id: "Tokyo Standard Time",
			name: "东京标准时间",
			location: "Asia/Tokyo",
			zh: "大阪，札幌，东京",
			en: "Osaka, Sapporo, Tokyo"
		},
		{
			id: "North Korea Standard Time",
			name: "朝鲜标准时间",
			location: "Asia/Pyongyang",
			zh: "平壤",
			en: "Pyongyang"
		},
		{
			id: "Transbaikal Standard Time",
			name: "外贝加尔标准时间",
			location: "Asia/Chita",
			zh: "赤塔市",
			en: "Chita"
		},
		{
			id: "Yakutsk Standard Time",
			name: "俄罗斯 TZ 8 标准时间",
			location: "Asia/Yakutsk",
			zh: "雅库茨克",
			en: "Yakutsk"
		},
		{
			id: "Korea Standard Time",
			name: "韩国标准时间",
			location: "Asia/Seoul",
			zh: "首尔",
			en: "Seoul"
		},
		{
			id: "AUS Central Standard Time",
			name: "澳大利亚中部标准时间",
			location: "Australia/Darwin",
			zh: "达尔文",
			en: "Darwin"
		},
		{
			id: "Cen. Australia Standard Time",
			name: "中部澳大利亚标准时间",
			location: "Australia/Adelaide",
			zh: "阿德莱德",
			en: "Adelaide"
		},
		{
			id: "West Pacific Standard Time",
			name: "太平洋西部标准时间",
			location: "Pacific/Guam",
			zh: "关岛，莫尔兹比港",
			en: "Guam, Port Moresby"
		},
		{
			id: "AUS Eastern Standard Time",
			name: "澳大利亚东部标准时间",
			location: "Australia/Sydney",
			zh: "堪培拉，墨尔本，悉尼",
			en: "Canberra, Melbourne, Sydney"
		},
		{
			id: "E. Australia Standard Time",
			name: "东部澳大利亚标准时间",
			location: "Australia/Brisbane",
			zh: "布里斯班",
			en: "Brisbane"
		},
		{
			id: "Vladivostok Standard Time",
			name: "俄罗斯 TZ 9 标准时间",
			location: "Asia/Vladivostok",
			zh: "符拉迪沃斯托克",
			en: "Vladivostok"
		},
		{
			id: "Tasmania Standard Time",
			name: "塔斯马尼亚岛标准时间",
			location: "Australia/Hobart",
			zh: "霍巴特",
			en: "Hobart"
		},
		{
			id: "Lord Howe Standard Time",
			name: "豪勋爵岛标准时间",
			location: "Australia/Lord_Howe",
			zh: "豪勋爵岛",
			en: "Lord Howe Island"
		},
		{
			id: "Russia Time Zone 10",
			name: "俄罗斯 TZ 10 标准时间",
			location: "Asia/Srednekolymsk",
			zh: "乔库尔达赫",
			en: "Chokurdah"
		},
		{
			id: "Bougainville Standard Time",
			name: "布干维尔岛标准时间",
			location: "Pacific/Bougainville",
			zh: "布干维尔岛",
			en: "Bougainville Island"
		},
		{
			id: "Central Pacific Standard Time",
			name: "太平洋中部标准时间",
			location: "Pacific/Guadalcanal",
			zh: "所罗门群岛，新喀里多尼亚",
			en: "Solomon Islands, New Caledonia"
		},
		{
			id: "Sakhalin Standard Time",
			name: "萨哈林标准时间",
			location: "Asia/Sakhalin",
			zh: "萨哈林",
			en: "Sakhalin"
		},
		{
			id: "Norfolk Standard Time",
			name: "诺福克岛标准时间",
			location: "Pacific/Norfolk",
			zh: "诺福克岛",
			en: "Norfolk Island"
		},
		{
			id: "Magadan Standard Time",
			name: "马加丹标准时间",
			location: "Asia/Magadan",
			zh: "马加丹",
			en: "Magadan"
		},
		{
			id: "UTC+12",
			name: "UTC+12",
			location: "Etc/GMT-12",
			zh: "协调世界时+12",
			en: "Coordinated Universal Time+12"
		},
		{
			id: "New Zealand Standard Time",
			name: "新西兰标准时间",
			location: "Pacific/Auckland",
			zh: "奥克兰，惠灵顿",
			en: "Auckland, Wellington"
		},
		{
			id: "Fiji Standard Time",
			name: "斐济标准时间",
			location: "Pacific/Fiji",
			zh: "斐济",
			en: "Fiji"
		},
		{
			id: "Russia Time Zone 11",
			name: "俄罗斯 TZ 11 标准时间",
			location: "Asia/Kamchatka",
			zh: "阿纳德尔，堪察加彼得罗巴甫洛夫斯克",
			en: "Anadel, Kamchatka Petropavlovsk"
		},
		{
			id: "Chatham Islands Standard Time",
			name: "查塔姆群岛标准时间",
			location: "Pacific/Chatham",
			zh: "查塔姆群岛",
			en: "Chatham Islands"
		},
		{
			id: "Tonga Standard Time",
			name: "汤加标准时间",
			location: "Pacific/Tongatapu",
			zh: "努库阿洛法",
			en: "Nukualofa"
		},
		{
			id: "UTC+13",
			name: "UTC+13",
			location: "Etc/GMT-13",
			zh: "协调世界时+13",
			en: "Coordinated Universal Time+13"
		},
		{
			id: "Samoa Standard Time",
			name: "萨摩亚群岛标准时间",
			location: "Pacific/Apia",
			zh: "萨摩亚群岛",
			en: "Samoa Islands"
		},
		{
			id: "Line Islands Standard Time",
			name: "莱恩群岛标准时间",
			location: "Pacific/Kiritimati",
			zh: "圣诞岛",
			en: "Kiritimati"
		}
	]` // 替换为你的完整 JSON

	// 替换所有的键名前加上双引号，并在键名后面加上冒号和双引号
	data := strings.NewReplacer(
		"id:", `"id":`,
		"name:", `"name":`,
		"location:", `"location":`,
		"zh:", `"zh":`,
		"en:", `"en":`,
	).Replace(jsonData)

	timeZones := make([]TimeZoneInfo, 0)
	if err := json.Unmarshal([]byte(data), &timeZones); err != nil {
		fmt.Println("Error parsing JSON:", err)
		return "", ""
	}

	chinese := ""
	english := ""
	if timeZones != nil && len(timeZones) > 0 {
		for _, zone := range timeZones {
			if zone.Location == tz {
				offsetString, _ := tools.GetUTCOffsetString(tz)
				chinese = "(" + offsetString + ") " + zone.Zh
				english = "(" + offsetString + ") " + zone.En
			}
		}
	}

	return chinese, english

}

// 项目设置
func insertProjectInfoLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, old models.Project, new models.Project, operatorID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	module := "operation_log.module.project"
	//项目基本信息

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.status",
		Old: models.OperationLogField{
			Type:  5,
			Value: []int{old.Status},
		},
		New: models.OperationLogField{
			Type:  5,
			Value: []int{new.Status},
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.sponsor",
		Old: models.OperationLogField{
			Type:  2,
			Value: old.Sponsor,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.Sponsor,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.name",
		Old: models.OperationLogField{
			Type:  2,
			Value: old.Name,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.Name,
		},
	})

	//OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
	//	Key:     "operation_log.project.properties",
	//	TranKey: "operation_log.project.type",
	//	Old: models.OperationLogField{
	//		Type:  2,
	//		Value: old.Type,
	//	},
	//	New: models.OperationLogField{
	//		Type:  2,
	//		Value: new.Type,
	//	},
	//})

	oldStartTime := ""
	newStartTime := ""
	if old.StartTime > 0 {
		oldStartTime = time.Unix(int64(old.StartTime), 0).UTC().Format("2006-01-02")
	}
	if new.StartTime > 0 {
		newStartTime = time.Unix(int64(new.StartTime), 0).UTC().Format("2006-01-02")
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.startTime",
		Old: models.OperationLogField{
			Type:  2,
			Value: oldStartTime,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newStartTime,
		},
	})
	oldEndTime := ""
	newEndTime := ""
	if old.EndTime > 0 {
		oldEndTime = time.Unix(int64(old.EndTime), 0).UTC().Format("2006-01-02")
	}
	if new.EndTime > 0 {
		newEndTime = time.Unix(int64(new.EndTime), 0).UTC().Format("2006-01-02")
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.endTime",
		Old: models.OperationLogField{
			Type:  2,
			Value: oldEndTime,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newEndTime,
		},
	})

	//OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
	//	Key:     "operation_log.project",
	//	TranKey: "operation_log.project.plannedCases",
	//	Old: models.OperationLogField{
	//		Type:  1,
	//		Value: old.PlannedCases,
	//	},
	//	New: models.OperationLogField{
	//		Type:  1,
	//		Value: new.PlannedCases,
	//	},
	//})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.phone",
		Old: models.OperationLogField{
			Type:  2,
			Value: old.Phone,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.Phone,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.descriptions",
		Old: models.OperationLogField{
			Type:  2,
			Value: old.Description,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.Description,
		},
	})
	if old.Status != new.Status || old.Sponsor != new.Sponsor || old.Name != new.Name || oldStartTime != newStartTime || old.EndTime != new.EndTime || old.Phone != new.Phone || old.Description != new.Description {
		module = "operation_log.module.project_information"
	}

	//业务功能
	//projectTimeZoneLog

	oldTimeZoneZh := ""
	oldTimeZoneEn := ""
	newTimeZoneZh := ""
	newTimeZoneEn := ""
	if old.Tz != "" {
		ch, en := projectTimeZoneLog(old.Tz)
		oldTimeZoneZh = ch
		oldTimeZoneEn = en
	} else if old.TimeZoneStr != "" {
		connector := ""
		if !strings.Contains(old.TimeZoneStr, "-") {
			connector = "+"
		}
		oldTimeZoneZh = "UTC" + connector + old.TimeZoneStr
		oldTimeZoneEn = "UTC" + connector + old.TimeZoneStr
	}
	if new.Tz != "" {
		ch, en := projectTimeZoneLog(new.Tz)
		newTimeZoneZh = ch
		newTimeZoneEn = en
	} else if new.TimeZoneStr != "" {
		connector := ""
		if !strings.Contains(new.TimeZoneStr, "-") {
			connector = "+"
		}
		newTimeZoneZh = "UTC" + connector + new.TimeZoneStr
		newTimeZoneEn = "UTC" + connector + new.TimeZoneStr
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.timeZone",
		Old: models.OperationLogField{
			Type:    30,
			Value:   oldTimeZoneZh,
			ENValue: oldTimeZoneEn,
		},
		New: models.OperationLogField{
			Type:    30,
			Value:   newTimeZoneZh,
			ENValue: newTimeZoneEn,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.orderCheck",
		Old: models.OperationLogField{
			Type:  5,
			Value: []int{old.OrderCheck},
		},
		New: models.OperationLogField{
			Type:  5,
			Value: []int{new.OrderCheck},
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.orderCheckDay",
		Old: models.OperationLogField{
			Type:  5,
			Value: old.ProjectInfo.OrderCheckDay,
		},
		New: models.OperationLogField{
			Type:  5,
			Value: new.ProjectInfo.OrderCheckDay,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.customTime",
		Old: models.OperationLogField{
			Type:  2,
			Value: old.ProjectInfo.OrderCheckTime,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.ProjectInfo.OrderCheckTime,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.orderConfirmation",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.OrderConfirmation),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.OrderConfirmation),
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.deIsolationApproval",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.DeIsolationApproval),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.DeIsolationApproval),
		},
	})

	//管理员
	oldAdministrators := ""
	newAdministrators := ""
	if len(old.Administrators) > 0 {
		var users []models.User
		cursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": old.Administrators}})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(nil, &users)
		if err != nil {
			return errors.WithStack(err)
		}
		userNames := slice.Map(users, func(index int, user models.User) string {
			return user.Name
		})
		oldAdministrators = strings.Join(userNames, "、")
	}
	if len(new.Administrators) > 0 {
		var users []models.User
		cursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": new.Administrators}})
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(nil, &users)
		if err != nil {
			return errors.WithStack(err)
		}
		userNames := slice.Map(users, func(index int, user models.User) string {
			return user.Name
		})
		newAdministrators = strings.Join(userNames, "、")
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.administrators",
		Old: models.OperationLogField{
			Type:  2,
			Value: oldAdministrators,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newAdministrators,
		},
	})

	if newTimeZoneZh != oldTimeZoneZh || newTimeZoneEn != oldTimeZoneEn || old.OrderCheck != new.OrderCheck || old.OrderConfirmation != new.OrderConfirmation || old.DeIsolationApproval != new.DeIsolationApproval || oldAdministrators != newAdministrators ||
		old.OrderCheckTime != new.OrderCheckTime || !reflect.DeepEqual(old.ProjectInfo.OrderCheckDay, new.ProjectInfo.OrderCheckDay) {
		module = "operation_log.module.project_function"
	}

	//外部对接
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.connectEdc",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.ConnectEdc),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.ConnectEdc),
		},
	})

	//EDC供应商
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.edcSupplier",
		Old: models.OperationLogField{
			Type:  1,
			Value: old.EdcSupplier,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: new.EdcSupplier,
		},
	})

	//URL
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.edcUrl",
		Old: models.OperationLogField{
			Type:  2,
			Value: old.EdcUrl,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.EdcUrl,
		},
	})

	//EDC映射
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.visitRandomization",
		Old: models.OperationLogField{
			Type:  2,
			Value: old.VisitRandomization,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.VisitRandomization,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.pushMode",
		Old: models.OperationLogField{
			Type:  5,
			Value: []int{old.PushMode},
		},
		New: models.OperationLogField{
			Type:  5,
			Value: []int{new.PushMode},
		},
	})

	// 推送规则
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.pushRules",
		Old: models.OperationLogField{
			Type:  5,
			Value: []int{old.PushRules},
		},
		New: models.OperationLogField{
			Type:  5,
			Value: []int{new.PushRules},
		},
	})

	// 推送场景 登记
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.registerPush",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.PushScenario.RegisterPush,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.PushScenario.RegisterPush,
		},
	})

	// 推送场景 受试者修改(随机前)
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.updateRandomFrontPush",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.PushScenario.UpdateRandomFrontPush,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.PushScenario.UpdateRandomFrontPush,
		},
	})

	// 推送场景 受试者修改(随机后)
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.updateRandomAfterPush",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.PushScenario.UpdateRandomAfterPush,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.PushScenario.UpdateRandomAfterPush,
		},
	})

	// 推送场景 随机
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.randomPush",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.PushScenario.RandomPush,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.PushScenario.RandomPush,
		},
	})

	// 推送场景 分层校验不一致，进行随机阻断
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.randomBlockPush",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.PushScenario.RandomBlockPush,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.PushScenario.RandomBlockPush,
		},
	})

	// 推送场景 表单校验不一致，进行随机阻断
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.formRandomBlockPush",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.PushScenario.FormRandomBlockPush,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.PushScenario.FormRandomBlockPush,
		},
	})

	if old.Type == 2 || old.Type == 3 {
		// 推送场景 cohort名称校验不一致，进行随机阻断
		tempTranKey := "operation_log.project.cohortRandomBlockPush"
		if old.Type == 3 {
			tempTranKey = "operation_log.project.stageRandomBlockPush"
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project",
			TranKey: tempTranKey,
			Old: models.OperationLogField{
				Type:  4,
				Value: old.PushScenario.CohortRandomBlockPush,
			},
			New: models.OperationLogField{
				Type:  4,
				Value: new.PushScenario.CohortRandomBlockPush,
			},
		})
	}

	// 推送场景 发药
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.dispensingPush",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.PushScenario.DispensingPush,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.PushScenario.DispensingPush,
		},
	})

	// 推送场景 筛选
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.screenPush",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.PushScenario.ScreenPush,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.PushScenario.ScreenPush,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.synchronizationMode",
		Old: models.OperationLogField{
			Type:  5,
			Value: []int{old.SynchronizationMode},
		},
		New: models.OperationLogField{
			Type:  5,
			Value: []int{new.SynchronizationMode},
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.connectLearning",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.ConnectLearning),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.ConnectLearning),
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.needLearning",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.NeedLearning),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.NeedLearning),
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.needLearningEnv",
		Old: models.OperationLogField{
			Type:  2,
			Value: old.NeedLearningEnv,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.NeedLearningEnv,
		},
	})
	if old.ConnectEdc != new.ConnectEdc || old.PushMode != new.PushMode ||
		old.SynchronizationMode != new.SynchronizationMode ||
		old.ConnectLearning != new.ConnectLearning ||
		old.NeedLearning != new.NeedLearning ||
		old.EdcSupplier != new.EdcSupplier ||
		old.EdcUrl != new.EdcUrl ||
		old.PushRules != new.PushRules ||
		old.PushScenario != new.PushScenario ||
		new.EdcSupplier == 2 {
		module = "operation_log.module.project_docking"
	}

	//自定义流程
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.unblindingControl",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.UnblindingControl),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.UnblindingControl),
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.unblindingSms",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.UnblindingSms),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.UnblindingSms),
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.unblindingProcess",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.UnblindingProcess),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.UnblindingProcess),
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.unblindingCode",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.UnblindingCode),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.UnblindingCode),
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.pvUnblinding",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.PvUnblindingType),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.PvUnblindingType),
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.pvUnblindingSms",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.PvUnblindingSms),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.PvUnblindingSms),
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.pvUnblindingProcess",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.PvUnblindingProcess),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.PvUnblindingProcess),
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.ipUnblinding",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.IpUnblindingType),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.IpUnblindingType),
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.ipUnblindingSms",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.IpUnblindingSms),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.IpUnblindingSms),
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.ipUnblindingProcess",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.IpUnblindingProcess),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.IpUnblindingProcess),
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.orderApprovalControl",
		Old: models.OperationLogField{
			Type:  4,
			Value: intToBool(old.OrderApprovalControl),
		},
		New: models.OperationLogField{
			Type:  4,
			Value: intToBool(new.OrderApprovalControl),
		},
	})
	if old.OrderApprovalControl != new.OrderApprovalControl ||
		old.UnblindingCode != new.UnblindingCode ||
		old.UnblindingProcess != new.UnblindingProcess ||
		old.UnblindingSms != new.UnblindingSms ||
		old.UnblindingControl != new.UnblindingControl ||
		old.PvUnblindingType != new.PvUnblindingType ||
		old.PvUnblindingProcess != new.PvUnblindingProcess ||
		old.PvUnblindingSms != new.PvUnblindingSms ||
		old.IpUnblindingType != new.IpUnblindingType ||
		old.IpUnblindingProcess != new.IpUnblindingProcess ||
		old.IpUnblindingSms != new.IpUnblindingSms {
		module = "operation_log.module.project_custom"
	}

	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.project",
		Value: old.ProjectInfo.Name + "[" + old.ProjectInfo.Number + "]",
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, module, OID, 2, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil

}

// 项目设置——项目环境（新增）
func insertAddProjectEnvLog(ctx *gin.Context, sctx mongo.SessionContext, types int, OID primitive.ObjectID, oldEnv models.Environment, newEnv models.Environment, operatorID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.envName",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newEnv.Name,
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.cohortStatus",
		Old: models.OperationLogField{
			Type:  6,
			Value: oldEnv.Status,
		},
		New: models.OperationLogField{
			Type:  6,
			Value: newEnv.Status,
		},
	})
	if (newEnv.AlertThresholds != nil && len(newEnv.AlertThresholds) > 0) || (oldEnv.AlertThresholds != nil && len(oldEnv.AlertThresholds) > 0) {
		oldAlertThresholdsBuilder := strings.Builder{}
		oldAlertThresholdsBuilderEn := strings.Builder{}
		newAlertThresholdsBuilder := strings.Builder{}
		newAlertThresholdsBuilderEn := strings.Builder{}

		if oldEnv.AlertThresholds != nil && len(oldEnv.AlertThresholds) > 0 {
			for i, threshold := range oldEnv.AlertThresholds {
				statusStr := ""
				statusStrEn := ""
				if threshold.Type == 1 {
					statusStr = locales.TrWithLang("zh", "subject.status.registered")
					statusStrEn = locales.TrWithLang("en", "subject.status.registered")
				} else if threshold.Type == 3 {
					statusStr = locales.TrWithLang("zh", "cohort.status.enrollment")
					statusStrEn = locales.TrWithLang("en", "cohort.status.enrollment")
				} else if threshold.Type == 7 {
					statusStr = locales.TrWithLang("zh", "subject.status.screen.success")
					statusStrEn = locales.TrWithLang("en", "subject.status.screen.success")
				}
				capacity := strconv.Itoa(threshold.Capacity)
				thresholds := ""
				if threshold.Thresholds != nil {
					thresholds = strconv.Itoa(*threshold.Thresholds)
				}
				oldAlertThresholdsBuilder.WriteString(statusStr)
				oldAlertThresholdsBuilder.WriteString("/")
				oldAlertThresholdsBuilder.WriteString(capacity)
				oldAlertThresholdsBuilder.WriteString("/")
				oldAlertThresholdsBuilder.WriteString(thresholds)
				if i < len(oldEnv.AlertThresholds)-1 {
					oldAlertThresholdsBuilder.WriteString("，")
				}

				oldAlertThresholdsBuilderEn.WriteString(statusStrEn)
				oldAlertThresholdsBuilderEn.WriteString("/")
				oldAlertThresholdsBuilderEn.WriteString(capacity)
				oldAlertThresholdsBuilderEn.WriteString("/")
				oldAlertThresholdsBuilderEn.WriteString(thresholds)
				if i < len(oldEnv.AlertThresholds)-1 {
					oldAlertThresholdsBuilderEn.WriteString(",")
				}
			}
		}
		if newEnv.AlertThresholds != nil && len(newEnv.AlertThresholds) > 0 {
			for i, threshold := range newEnv.AlertThresholds {
				statusStr := ""
				statusStrEn := ""
				if threshold.Type == 1 {
					statusStr = locales.TrWithLang("zh", "subject.status.registered")
					statusStrEn = locales.TrWithLang("en", "subject.status.registered")
				} else if threshold.Type == 3 {
					statusStr = locales.TrWithLang("zh", "cohort.status.enrollment")
					statusStrEn = locales.TrWithLang("en", "cohort.status.enrollment")
				} else if threshold.Type == 7 {
					statusStr = locales.TrWithLang("zh", "subject.status.screen.success")
					statusStrEn = locales.TrWithLang("en", "subject.status.screen.success")
				}
				capacity := strconv.Itoa(threshold.Capacity)
				thresholds := ""
				if threshold.Thresholds != nil {
					thresholds = strconv.Itoa(*threshold.Thresholds)
				}
				newAlertThresholdsBuilder.WriteString(statusStr)
				newAlertThresholdsBuilder.WriteString("/")
				newAlertThresholdsBuilder.WriteString(capacity)
				newAlertThresholdsBuilder.WriteString("/")
				newAlertThresholdsBuilder.WriteString(thresholds)
				if i < len(oldEnv.AlertThresholds)-1 {
					newAlertThresholdsBuilder.WriteString("，")
				}

				newAlertThresholdsBuilderEn.WriteString(statusStrEn)
				newAlertThresholdsBuilderEn.WriteString("/")
				newAlertThresholdsBuilderEn.WriteString(capacity)
				newAlertThresholdsBuilderEn.WriteString("/")
				newAlertThresholdsBuilderEn.WriteString(thresholds)
				if i < len(oldEnv.AlertThresholds)-1 {
					newAlertThresholdsBuilderEn.WriteString(",")
				}
			}
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project",
			TranKey: "operation_log.project.alertThresholds",
			Old: models.OperationLogField{
				Type:    30,
				Value:   oldAlertThresholdsBuilder.String(),
				ENValue: oldAlertThresholdsBuilderEn.String(),
			},
			New: models.OperationLogField{
				Type:    30,
				Value:   newAlertThresholdsBuilder.String(),
				ENValue: newAlertThresholdsBuilderEn.String(),
			},
		})
	}
	var project models.Project
	_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": OID}).Decode(&project)
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.project",
		Value: project.Name + "[" + project.Number + "]",
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.project_env", OID, types, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 项目设置——项目环境(复制)
func insertCopyProjectEnvLog(ctx *gin.Context, sctx mongo.SessionContext, types int, OID primitive.ObjectID, env string, newEnv string, operatorID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.envName",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: env,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.newEnvName",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newEnv,
		},
	})

	var project models.Project
	_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": OID}).Decode(&project)
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.project",
		Value: project.Name + "[" + project.Number + "]",
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.project_env", OID, types, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 项目设置——项目环境（锁定/解锁）
func insertLockProjectEnvLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, old bool, new bool, operatorID primitive.ObjectID, envName string) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.envName",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: envName,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.lockStatus",
		Old: models.OperationLogField{
			Type:  5,
			Value: []int{boolToInt(old)},
		},
		New: models.OperationLogField{
			Type:  5,
			Value: []int{boolToInt(new)},
		},
	})
	var project models.Project
	_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": OID}).Decode(&project)
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.project",
		Value: project.Name + "[" + project.Number + "]",
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.project_env", OID, 2, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 项目设置——项目环境（新增编辑cohort）
func insertUpdateProjectEnvCohort(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, operType int, projectType int, oldEnvName string, envName string, old models.Cohort, new models.Cohort, operatorID primitive.ObjectID, cohorts []models.Cohort) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.envName",
		Old: models.OperationLogField{
			Type:  2,
			Value: oldEnvName,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: envName,
		},
	})

	tranKey := "operation_log.project.cohort"
	if projectType == 3 || (projectType == 2 && new.Type == 1) {
		if projectType == 3 {
			tranKey = "operation_log.project.stage"
		}
		lastCohort := ""
		oldLastCohort := ""
		for _, cohort := range cohorts {
			if cohort.ID == new.LastID {
				lastCohort = cohort.Name
			}
			if cohort.ID == old.LastID {
				oldLastCohort = cohort.Name
			}
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project",
			TranKey: "operation_log.project.lastStage",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldLastCohort,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: lastCohort,
			},
		})
	}

	//名称
	oldCohortName := old.Name
	if old.Name == new.Name {
		oldCohortName = ""
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: tranKey,
		Old: models.OperationLogField{
			Type:  2,
			Value: oldCohortName,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.Name,
		},
	})
	if projectType == 2 && new.Type == 1 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project",
			TranKey: "operation_log.project.stage",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.ReRandomName,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.ReRandomName,
			},
		})
	}

	if (new.AlertThresholds != nil && len(new.AlertThresholds) > 0) || (old.AlertThresholds != nil && len(old.AlertThresholds) > 0) {
		oldAlertThresholdsBuilder := strings.Builder{}
		oldAlertThresholdsBuilderEn := strings.Builder{}
		newAlertThresholdsBuilder := strings.Builder{}
		newAlertThresholdsBuilderEn := strings.Builder{}

		if old.AlertThresholds != nil && len(old.AlertThresholds) > 0 {
			for i, threshold := range old.AlertThresholds {
				statusStr := ""
				statusStrEn := ""
				if threshold.Type == 1 {
					statusStr = locales.TrWithLang("zh", "subject.status.registered")
					statusStrEn = locales.TrWithLang("en", "subject.status.registered")
				} else if threshold.Type == 3 {
					statusStr = locales.TrWithLang("zh", "cohort.status.enrollment")
					statusStrEn = locales.TrWithLang("en", "cohort.status.enrollment")
				} else if threshold.Type == 7 {
					statusStr = locales.TrWithLang("zh", "subject.status.screen.success")
					statusStrEn = locales.TrWithLang("en", "subject.status.screen.success")
				}
				capacity := strconv.Itoa(threshold.Capacity)
				thresholds := ""
				if threshold.Thresholds != nil {
					thresholds = strconv.Itoa(*threshold.Thresholds)
				}
				oldAlertThresholdsBuilder.WriteString(statusStr)
				oldAlertThresholdsBuilder.WriteString("/")
				oldAlertThresholdsBuilder.WriteString(capacity)
				oldAlertThresholdsBuilder.WriteString("/")
				oldAlertThresholdsBuilder.WriteString(thresholds)
				if i < len(old.AlertThresholds)-1 {
					oldAlertThresholdsBuilder.WriteString("，")
				}

				oldAlertThresholdsBuilderEn.WriteString(statusStrEn)
				oldAlertThresholdsBuilderEn.WriteString("/")
				oldAlertThresholdsBuilderEn.WriteString(capacity)
				oldAlertThresholdsBuilderEn.WriteString("/")
				oldAlertThresholdsBuilderEn.WriteString(thresholds)
				if i < len(old.AlertThresholds)-1 {
					oldAlertThresholdsBuilderEn.WriteString(",")
				}
			}
		}
		if new.AlertThresholds != nil && len(new.AlertThresholds) > 0 {
			for i, threshold := range new.AlertThresholds {
				statusStr := ""
				statusStrEn := ""
				if threshold.Type == 1 {
					statusStr = locales.TrWithLang("zh", "subject.status.registered")
					statusStrEn = locales.TrWithLang("en", "subject.status.registered")
				} else if threshold.Type == 3 {
					statusStr = locales.TrWithLang("zh", "cohort.status.enrollment")
					statusStrEn = locales.TrWithLang("en", "cohort.status.enrollment")
				} else if threshold.Type == 7 {
					statusStr = locales.TrWithLang("zh", "subject.status.screen.success")
					statusStrEn = locales.TrWithLang("en", "subject.status.screen.success")
				}
				capacity := strconv.Itoa(threshold.Capacity)
				thresholds := ""
				if threshold.Thresholds != nil {
					thresholds = strconv.Itoa(*threshold.Thresholds)
				}
				newAlertThresholdsBuilder.WriteString(statusStr)
				newAlertThresholdsBuilder.WriteString("/")
				newAlertThresholdsBuilder.WriteString(capacity)
				newAlertThresholdsBuilder.WriteString("/")
				newAlertThresholdsBuilder.WriteString(thresholds)
				if i < len(old.AlertThresholds)-1 {
					newAlertThresholdsBuilder.WriteString("，")
				}

				newAlertThresholdsBuilderEn.WriteString(statusStrEn)
				newAlertThresholdsBuilderEn.WriteString("/")
				newAlertThresholdsBuilderEn.WriteString(capacity)
				newAlertThresholdsBuilderEn.WriteString("/")
				newAlertThresholdsBuilderEn.WriteString(thresholds)
				if i < len(old.AlertThresholds)-1 {
					newAlertThresholdsBuilderEn.WriteString(",")
				}
			}
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project",
			TranKey: "operation_log.project.alertThresholds",
			Old: models.OperationLogField{
				Type:    30,
				Value:   oldAlertThresholdsBuilder.String(),
				ENValue: oldAlertThresholdsBuilderEn.String(),
			},
			New: models.OperationLogField{
				Type:    30,
				Value:   newAlertThresholdsBuilder.String(),
				ENValue: newAlertThresholdsBuilderEn.String(),
			},
		})
	}

	var project models.Project
	_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": OID}).Decode(&project)
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.project",
		Value: project.Name + "[" + project.Number + "]",
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.project_env", OID, operType, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func insertUpdateSubjectsCohortStatus(ctx *gin.Context, sctx mongo.SessionContext, projectId primitive.ObjectID, operType int, old models.Cohort, newStatus int, operatorID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.cohortStatus",
		Old: models.OperationLogField{
			Type:  6,
			Value: old.Status,
		},
		New: models.OperationLogField{
			Type:  6,
			Value: newStatus,
		},
	})

	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.project",
		Value: old.Name,
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.subjects", old.ID, operType, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// 项目设置——项目环境（删除cohort）
func insertProjectEnvDelCohortLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, operatorID primitive.ObjectID, projectType int, envName string, cohortName string) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.envName",
		New: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		Old: models.OperationLogField{
			Type:  2,
			Value: envName,
		},
	})

	tranKey := "operation_log.project.cohort"
	if projectType == 3 {
		tranKey = "operation_log.project.stage"
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: tranKey,
		New: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		Old: models.OperationLogField{
			Type:  2,
			Value: cohortName,
		},
	})
	var project models.Project
	_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": OID}).Decode(&project)
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.project",
		Value: project.Name + "[" + project.Number + "]",
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.project_env", OID, 3, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func insertDrugPackageConfigureLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, old models.DrugPackageConfigure, new models.DrugPackageConfigure, operID primitive.ObjectID) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	//不发放天数
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.drug_configure",
		TranKey: "operation_log.drug_configure.isDispense",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.IsOpenUnProvideDate,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.IsOpenUnProvideDate,
		},
	})

	var oldUnProvideDate string
	var newUnProvideDate string
	for _, config := range old.UnProvideDateConfig {
		oldUnProvideDate = oldUnProvideDate + fmt.Sprintf("%s-%d,", config.Name, config.Number)
	}
	if len(old.UnProvideDateConfig) > 0 {
		oldUnProvideDate = oldUnProvideDate[:len(oldUnProvideDate)-1] + ";"
	}
	for _, config := range new.UnProvideDateConfig {
		newUnProvideDate = newUnProvideDate + fmt.Sprintf("%s-%d,", config.Name, config.Number)
	}
	if len(new.UnProvideDateConfig) > 0 {
		newUnProvideDate = newUnProvideDate[:len(newUnProvideDate)-1] + ";"
	}
	if oldUnProvideDate != newUnProvideDate {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.drug_configure",
			TranKey: "operation_log.drug_configure.notDispenseConfig",
			Old: models.OperationLogField{
				Type:  7,
				Value: oldUnProvideDate,
			},
			New: models.OperationLogField{
				Type:  7,
				Value: newUnProvideDate,
			},
		})
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.drug_configure",
		TranKey: "operation_log.drug_configure.isOpenPackage",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.IsOpen,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.IsOpen,
		},
	})
	var oldDrugPackageValues string
	var ENOldDrugPackageValues string
	var newDrugPackageValues string
	var EnNewDrugPackageValues string
	for _, mixedPackage := range old.MixedPackage {
		ZHIsMixed := ""
		ENIsMixed := ""
		if mixedPackage.IsMixed {
			ZHIsMixed = "是"
			ENIsMixed = "Yes"
		} else {
			ZHIsMixed = "否"
			ENIsMixed = "No"
		}
		for _, v := range mixedPackage.PackageConfig {
			oldDrugPackageValues = oldDrugPackageValues + fmt.Sprintf("%s-%d,", v.Name, v.Number)
			ENOldDrugPackageValues = ENOldDrugPackageValues + fmt.Sprintf("%s-%d,", v.Name, v.Number)
		}
		oldDrugPackageValues = oldDrugPackageValues + fmt.Sprintf("%s;", ZHIsMixed)
		ENOldDrugPackageValues = ENOldDrugPackageValues + fmt.Sprintf("%s;", ENIsMixed)
	}
	for _, mixedPackage := range new.MixedPackage {
		ZHIsMixed := ""
		ENIsMixed := ""
		if mixedPackage.IsMixed {
			ZHIsMixed = "是"
			ENIsMixed = "Yes"
		} else {
			ZHIsMixed = "否"
			ENIsMixed = "No"
		}

		for _, v := range mixedPackage.PackageConfig {
			newDrugPackageValues = newDrugPackageValues + fmt.Sprintf("%s-%d,", v.Name, v.Number)
			EnNewDrugPackageValues = EnNewDrugPackageValues + fmt.Sprintf("%s-%d,", v.Name, v.Number)
		}
		newDrugPackageValues = newDrugPackageValues + fmt.Sprintf("%s;", ZHIsMixed)
		EnNewDrugPackageValues = EnNewDrugPackageValues + fmt.Sprintf("%s;", ENIsMixed)
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.drug_configure",
		TranKey: "operation_log.drug_configure.packageConfigNew",
		Old: models.OperationLogField{
			Type:    30,
			Value:   oldDrugPackageValues,
			ENValue: ENOldDrugPackageValues,
		},
		New: models.OperationLogField{
			Type:    30,
			Value:   newDrugPackageValues,
			ENValue: EnNewDrugPackageValues,
		},
	})

	//研究中心订单申请
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.drug_configure",
		TranKey: "operation_log.drug_configure.isOpenApplication",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.IsOpenApplication,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.IsOpenApplication,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.drug_configure",
		TranKey: "operation_log.drug_configure.supplyRatio",
		Old: models.OperationLogField{
			Type:  4,
			Value: old.SupplyRatio,
		},
		New: models.OperationLogField{
			Type:  4,
			Value: new.SupplyRatio,
		},
	})

	var oldOrderApplicationCongfig string
	var newOrderApplicationCongfig string
	for _, config := range old.OrderApplicationConfig {
		for _, v := range config.DrugNames {
			oldOrderApplicationCongfig = oldOrderApplicationCongfig + fmt.Sprintf("%s-%d,", v.DrugName, v.Number)
		}
		oldOrderApplicationCongfig = oldOrderApplicationCongfig[:len(oldOrderApplicationCongfig)-1] + ";"
	}
	for _, mixedPackage := range new.OrderApplicationConfig {
		for _, v := range mixedPackage.DrugNames {
			newOrderApplicationCongfig = newOrderApplicationCongfig + fmt.Sprintf("%s-%d,", v.DrugName, v.Number)
		}
		newOrderApplicationCongfig = newOrderApplicationCongfig[:len(newOrderApplicationCongfig)-1] + ";"
	}

	if oldOrderApplicationCongfig != newOrderApplicationCongfig {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.drug_configure",
			TranKey: "operation_log.drug_configure.orderApplictionConfig",
			Old: models.OperationLogField{
				Type:  7,
				Value: oldOrderApplicationCongfig,
			},
			New: models.OperationLogField{
				Type:  7,
				Value: newOrderApplicationCongfig,
			},
		})
	}

	marks := []models.Mark{}
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.package_configure", OID, types, OperationLogFieldGroups, marks, operID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// 项目设置-项目权限
func insertAddProjectRoleLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, oldProjectRole models.ProjectRolePermission, newProjectRole models.ProjectRolePermission, operatorID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.roleName",
		Old: models.OperationLogField{
			Type:  2,
			Value: oldProjectRole.Name,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newProjectRole.Name,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.scope",
		Old: models.OperationLogField{
			Type:  2,
			Value: oldProjectRole.Scope,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newProjectRole.Scope,
		},
	})

	var oldStatus interface{}
	if types == 2 {
		oldStatus = []int{oldProjectRole.Status}
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.roleStatus",
		Old: models.OperationLogField{
			Type:  5,
			Value: oldStatus,
		},
		New: models.OperationLogField{
			Type:  5,
			Value: []int{newProjectRole.Status},
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.roleDescription",
		Old: models.OperationLogField{
			Type:  2,
			Value: oldProjectRole.Description,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newProjectRole.Description,
		},
	})

	//角色
	// cancelSelects := []string{}
	// newSelects := []string{}
	// cancelPer := ""
	// newPer := ""
	cancelSelects := slice.Difference(oldProjectRole.Permissions, newProjectRole.Permissions)
	newSelects := slice.Difference(newProjectRole.Permissions, oldProjectRole.Permissions)

	if len(cancelSelects) > 0 {
		//cancelPer = rolePermissionMenu(ctx, cancelSelects, reverseIndex, positionMap)
		types = 5
	}
	if len(newSelects) > 0 {
		//newPer = rolePermissionMenu(ctx, newSelects, reverseIndex, positionMap)
		types = 5
	}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.cancelRolePermissionSelect",
		Old: models.OperationLogField{
			Type:  5,
			Value: nil,
		},
		New: models.OperationLogField{
			Type:  5,
			Value: cancelSelects,
		},
	})

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project",
		TranKey: "operation_log.project.addRolePermissionSelect",
		Old: models.OperationLogField{
			Type:  5,
			Value: nil,
		},
		New: models.OperationLogField{
			Type:  5,
			Value: newSelects,
		},
	})

	var project models.Project
	_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": OID}).Decode(&project)
	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.label.project",
		Value: project.Name + "[" + project.Number + "]",
		Blind: false,
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.project_permission", OID, types, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func insertDrugConfigureLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, old models.DrugConfigureInfo, new models.DrugConfigureInfo, operID primitive.ObjectID, envOID primitive.ObjectID, cohortOID primitive.ObjectID, copyEnv string, visitCycleInfo models.VisitCycle) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {

		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  7,
					Value: copyEnv,
				},
			})
		}

		//组别
		oldGroup := old.Group
		if old.ParName != "" {
			oldGroup = old.ParName
		}
		if new.ParName != oldGroup {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.group",
				Old: models.OperationLogField{
					Type:  7,
					Value: oldGroup,
				},
				New: models.OperationLogField{
					Type:  7,
					Value: new.Group,
				},
			})
		}
		//子组别
		if new.SubName != "" {
			oldSubGroup := models.Group{
				Group:   old.Group,
				ParName: old.ParName,
				SubName: old.SubName,
			}
			newSubGroup := models.Group{
				Group:   new.Group,
				ParName: new.ParName,
				SubName: new.SubName,
			}
			if newSubGroup != oldSubGroup {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure",
					TranKey: "operation_log.drug_configure.subGroup",
					Old: models.OperationLogField{
						Type:  11,
						Value: oldSubGroup,
					},
					New: models.OperationLogField{
						Type:  11,
						Value: newSubGroup,
					},
				})
			}
		}
		//发放方式
		if new.OpenSetting != old.OpenSetting {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.open",
				Old: models.OperationLogField{
					Type:  6,
					Value: old.OpenSetting,
				},
				New: models.OperationLogField{
					Type:  6,
					Value: new.OpenSetting,
				},
			})
		}

		if new.IsFormula != old.IsFormula {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.formula",
				Old: models.OperationLogField{
					Type:  4,
					Value: old.IsFormula,
				},
				New: models.OperationLogField{
					Type:  4,
					Value: new.IsFormula,
				},
			})
		}

		if new.IsFormula && new.CustomerCalculation != old.CustomerCalculation {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.customerCalculation",
				Old: models.OperationLogField{
					Type:  2,
					Value: old.CustomerCalculation,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: new.CustomerCalculation,
				},
			})
		}

		if new.IsFormula && new.CustomerCalculationSpec != old.CustomerCalculationSpec {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.spec",
				Old: models.OperationLogField{
					Type:  2,
					Value: old.CustomerCalculationSpec,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: new.CustomerCalculationSpec,
				},
			})
		}

		if new.KeepDecimal != old.KeepDecimal {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.keepDecimal",
				Old: models.OperationLogField{
					Type:  1,
					Value: old.KeepDecimal,
				},
				New: models.OperationLogField{
					Type:  1,
					Value: new.KeepDecimal,
				},
			})
		}

		//研究产品配置
		if new.OpenSetting != 3 {
			var oldDrugValues string
			var newDrugValues string
			for _, v := range old.Values {
				// 兼容旧数据
				dispensingNumber := ""
				if v.CustomDispensingNumber != "" {
					dispensingNumber = v.CustomDispensingNumber
				} else {
					dispensingNumber = convertor.ToString(v.DispensingNumber)
				}

				oldDrugValues = oldDrugValues + fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s；", convertor.ToString(v.AutomaticRecode), convertor.ToString(v.AutomaticRecodeSpec), v.DrugName, dispensingNumber, v.DrugSpec, strconv.FormatBool(v.IsOther), v.Label)
			}
			for _, v := range new.Values {
				// 兼容旧数据
				dispensingNumber := ""
				if v.CustomDispensingNumber != "" {
					dispensingNumber = v.CustomDispensingNumber
				} else {
					dispensingNumber = convertor.ToString(v.DispensingNumber)
				}
				newDrugValues = newDrugValues + fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s；", convertor.ToString(v.AutomaticRecode), convertor.ToString(v.AutomaticRecodeSpec), v.DrugName, dispensingNumber, v.DrugSpec, strconv.FormatBool(v.IsOther), v.Label)
			}
			if newDrugValues != oldDrugValues {
				if len(new.Values) > 0 {
					for i, v := range new.Values {
						var oldValue models.DrugValue
						if len(old.Values) > i {
							oldValue = old.Values[i]
						}

						oDispensingNumber := ""
						if oldValue.CustomDispensingNumber != "" {
							oDispensingNumber = oldValue.CustomDispensingNumber
						} else {
							oDispensingNumber = convertor.ToString(oldValue.DispensingNumber)
						}

						vDispensingNumber := ""
						if v.CustomDispensingNumber != "" {
							vDispensingNumber = v.CustomDispensingNumber
						} else {
							vDispensingNumber = convertor.ToString(v.DispensingNumber)
						}

						olds := fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s；", convertor.ToString(oldValue.AutomaticRecode), convertor.ToString(oldValue.AutomaticRecodeSpec), oldValue.DrugName, oDispensingNumber, oldValue.DrugSpec, strconv.FormatBool(oldValue.IsOther), oldValue.Label)
						news := fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s；", convertor.ToString(v.AutomaticRecode), convertor.ToString(v.AutomaticRecodeSpec), v.DrugName, vDispensingNumber, v.DrugSpec, strconv.FormatBool(v.IsOther), v.Label)
						//研究产品名称/规格
						if v.DrugName != "" && olds != news {
							oldDrugNameSpec := ""
							if oldValue.DrugName != v.DrugName || oldValue.DrugSpec != v.DrugSpec {
								oldDrugNameSpec = oldValue.DrugName + "/" + oldValue.DrugSpec
							}
							OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
								Key:     "operation_log.drug_configure",
								TranKey: "operation_log.drug_configure.drugNameSpec",
								Old: models.OperationLogField{
									Type:  7,
									Value: oldDrugNameSpec,
								},
								New: models.OperationLogField{
									Type:  7,
									Value: v.DrugName + "/" + v.DrugSpec,
								},
							})
							//发放数量
							if vDispensingNumber != oDispensingNumber {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.dispensingNumber",
									Old: models.OperationLogField{
										Type:  1,
										Value: oldValue.CustomDispensingNumber,
									},
									New: models.OperationLogField{
										Type:  1,
										Value: v.CustomDispensingNumber,
									},
								})
							}
							if new.OpenSetting == 1 {
								//发放标签
								if v.Label != oldValue.Label {
									OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
										Key:     "operation_log.drug_configure",
										TranKey: "operation_log.drug_configure.drugLabel",
										Old: models.OperationLogField{
											Type:  1,
											Value: oldValue.Label,
										},
										New: models.OperationLogField{
											Type:  1,
											Value: v.Label,
										},
									})
								}
							}

							//未编号研究产品
							if v.IsOther != oldValue.IsOther {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.otherCheck",
									Old: models.OperationLogField{
										Type:  6,
										Value: oldValue.IsOther,
									},
									New: models.OperationLogField{
										Type:  6,
										Value: v.IsOther,
									},
								})
							}
							//自动赋值
							if v.AutomaticRecode != oldValue.AutomaticRecode {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.automatic_recode",
									Old: models.OperationLogField{
										Type:  4,
										Value: oldValue.AutomaticRecode,
									},
									New: models.OperationLogField{
										Type:  4,
										Value: v.AutomaticRecode,
									},
								})
							}
							//计算单位
							if v.AutomaticRecodeSpec != oldValue.AutomaticRecodeSpec {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.automatic_recode_spec",
									Old: models.OperationLogField{
										Type:  1,
										Value: oldValue.AutomaticRecodeSpec,
									},
									New: models.OperationLogField{
										Type:  1,
										Value: v.AutomaticRecodeSpec,
									},
								})
							}
						}

					}
				}
			}
		}

		//查询访视
		oldVisitCycleZh := make([]string, 0)
		oldVisitCycleEn := make([]string, 0)
		newVisitCycleZh := make([]string, 0)
		newVisitCycleEn := make([]string, 0)
		//var oldVisitCycle string
		//var newVisitCycle string
		var visitCycle models.VisitCycle
		match := bson.M{"env_id": envOID}
		if cohortOID != primitive.NilObjectID {
			match = bson.M{"env_id": envOID, "cohort_id": cohortOID}
		}
		tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visitCycle)
		if copyEnv != "" {
			visitCycle = visitCycleInfo
		}
		if visitCycle.Infos != nil {
			for _, v := range visitCycle.Infos {
				for _, oldV := range old.VisitCycles {
					if v.ID == oldV {
						oldVisitCycleZh = append(oldVisitCycleZh, v.Name)
						oldVisitCycleEn = append(oldVisitCycleEn, v.Name)
						//oldVisitCycle = oldVisitCycle + v.Name + ";"
					}
				}
				for _, newV := range new.VisitCycles {
					if v.ID == newV {
						newVisitCycleZh = append(newVisitCycleZh, v.Name)
						newVisitCycleEn = append(newVisitCycleEn, v.Name)
						//newVisitCycle = newVisitCycle + v.Name + ";"
					}
				}
			}
		}

		if visitCycle.SetInfo.IsOpen {
			for _, oldV := range old.VisitCycles {
				if visitCycle.SetInfo.Id == oldV {
					oldVisitCycleZh = append(oldVisitCycleZh, visitCycle.SetInfo.NameZh)
					oldVisitCycleEn = append(oldVisitCycleEn, visitCycle.SetInfo.NameEn)
					//oldVisitCycle = oldVisitCycle + v.Name + ";"
				}
			}
			for _, newV := range new.VisitCycles {
				if visitCycle.SetInfo.Id == newV {
					newVisitCycleZh = append(newVisitCycleZh, visitCycle.SetInfo.NameZh)
					newVisitCycleEn = append(newVisitCycleEn, visitCycle.SetInfo.NameEn)
				}
			}
		}

		//访视名称
		if strings.Join(newVisitCycleZh, ";") != strings.Join(oldVisitCycleZh, ";") ||
			strings.Join(newVisitCycleEn, ";") != strings.Join(oldVisitCycleEn, ";") {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure",
				TranKey: "operation_log.drug_configure.visitCycles",
				Old: models.OperationLogField{
					Type:    30,
					Value:   strings.Join(oldVisitCycleZh, ";"),
					ENValue: strings.Join(oldVisitCycleEn, ";"),
				},
				New: models.OperationLogField{
					Type:    30,
					Value:   strings.Join(newVisitCycleZh, ";"),
					ENValue: strings.Join(newVisitCycleEn, ";"),
				},
			})
		}
		//发放标签
		if new.OpenSetting == 1 || new.OpenSetting == 2 {
			if new.Label != old.Label {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure",
					TranKey: "operation_log.drug_configure.label",
					Old: models.OperationLogField{
						Type:  2,
						Value: old.Label,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: new.Label,
					},
				})
			}
		}

		//公式发放
		//研究产品名称打码显示
		if new.OpenSetting == 3 {
			//方式
			if new.CalculationType != old.CalculationType {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure",
					TranKey: "operation_log.drug_configure.calculationType",
					Old: models.OperationLogField{
						Type:  6,
						Value: old.CalculationType,
					},
					New: models.OperationLogField{
						Type:  6,
						Value: new.CalculationType,
					},
				})
			}
			//自定义公式 其他体表面积BSA
			if new.CalculationType == 4 {
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure",
					TranKey: "operation_log.drug_configure.customerCalculation",
					Old: models.OperationLogField{
						Type:  2,
						Value: old.CustomerCalculation,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: new.CustomerCalculation,
					},
				})
			}

			if len(new.Values) > 0 {
				for i, v := range new.Values {
					var oldValue models.DrugValue
					if len(old.Values) > i {
						oldValue = old.Values[i]
					}

					//范围/发放数量
					oldF := ""
					newF := ""
					for _, v := range oldValue.Formulas {
						oldF = oldF + v.Expression + "/" + strconv.Itoa(v.Value) + ";"
					}
					for _, v := range v.Formulas {
						newF = newF + v.Expression + "/" + strconv.Itoa(v.Value) + ";"
					}

					oldV := fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s", oldValue.DrugName, oldValue.DrugSpec, strconv.FormatBool(oldValue.IsOther), strconv.FormatBool(oldValue.IsOther), strconv.FormatBool(oldValue.CalculationInfo.KeepDecimal), convertor.ToString(oldValue.CalculationInfo.Precision), oldF)
					newV := fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s", v.DrugName, v.DrugSpec, strconv.FormatBool(v.IsOther), strconv.FormatBool(v.IsOther), strconv.FormatBool(v.CalculationInfo.KeepDecimal), convertor.ToString(v.CalculationInfo.Precision), newF)

					oldSpecifiaction := ""
					if new.CalculationType == 3 || new.CalculationType == 4 {
						if types == 2 {
							if oldValue.CalculationInfo.Specifications.Value != nil && oldValue.CalculationInfo.Specifications.Unit != nil {
								oldSpecifiaction = convertor.ToString(*oldValue.CalculationInfo.Specifications.Value) + convertor.ToString(*oldValue.CalculationInfo.Specifications.Unit)
							}
						}
						oldV = oldV + oldSpecifiaction + convertor.ToString(oldValue.UnitCalculationStandard)
						newV = newV + convertor.ToString(*v.CalculationInfo.Specifications.Value) + convertor.ToString(*v.CalculationInfo.Specifications.Unit) + convertor.ToString(v.UnitCalculationStandard)
					}

					//研究产品名称/规格
					if v.DrugName != "" && oldV != newV {
						oldDrugNameSpec := ""
						if oldValue.DrugName != v.DrugName || oldValue.DrugSpec != v.DrugSpec {
							oldDrugNameSpec = oldValue.DrugName + "/" + oldValue.DrugSpec
						}
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.drug_configure",
							TranKey: "operation_log.drug_configure.drugNameSpec",
							Old: models.OperationLogField{
								Type:  7,
								Value: oldDrugNameSpec,
							},
							New: models.OperationLogField{
								Type:  7,
								Value: v.DrugName + "/" + v.DrugSpec,
							},
						})

						//体重范围/发放数量
						if new.CalculationType == 2 || new.CalculationType == 3 || new.CalculationType == 4 {
							if newF != oldF {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.weightDispeningNumber",
									Old: models.OperationLogField{
										Type:  2,
										Value: oldF,
									},
									New: models.OperationLogField{
										Type:  2,
										Value: newF,
									},
								})
							}
							//体重计算比较
							if v.ComparisonSwitch != oldValue.ComparisonSwitch {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.comparisonSwitch",
									Old: models.OperationLogField{
										Type:  6,
										Value: oldValue.ComparisonSwitch,
									},
									New: models.OperationLogField{
										Type:  6,
										Value: v.ComparisonSwitch,
									},
								})
							}
							//比较条件
							if v.ComparisonType != oldValue.ComparisonType {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.comparisonType",
									Old: models.OperationLogField{
										Type:  6,
										Value: oldValue.ComparisonType,
									},
									New: models.OperationLogField{
										Type:  6,
										Value: v.ComparisonType,
									},
								})
							}
							if v.ComparisonRatio != oldValue.ComparisonRatio {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.comparisonRatio",
									Old: models.OperationLogField{
										Type:  2,
										Value: oldValue.ComparisonRatio,
									},
									New: models.OperationLogField{
										Type:  2,
										Value: v.ComparisonRatio,
									},
								})
							}
							if v.CurrentComparisonType != oldValue.CurrentComparisonType {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.currentComparisonType",
									Old: models.OperationLogField{
										Type:  6,
										Value: oldValue.CurrentComparisonType,
									},
									New: models.OperationLogField{
										Type:  6,
										Value: v.CurrentComparisonType,
									},
								})
							}
						}
						//年龄范围/发放数量
						if new.CalculationType == 1 {
							if newF != oldF {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.ageDispeningNumber",
									Old: models.OperationLogField{
										Type:  2,
										Value: oldF,
									},
									New: models.OperationLogField{
										Type:  2,
										Value: newF,
									},
								})
							}
						}
						//单位计算标准
						if new.CalculationType == 3 || new.CalculationType == 4 {
							OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
								Key:     "operation_log.drug_configure",
								TranKey: "operation_log.drug_configure.specifications",
								Old: models.OperationLogField{
									Type:  2,
									Value: oldSpecifiaction,
								},
								New: models.OperationLogField{
									Type:  2,
									Value: convertor.ToString(*v.CalculationInfo.Specifications.Value) + convertor.ToString(*v.CalculationInfo.Specifications.Unit),
								},
							})
							if v.UnitCalculationStandard != oldValue.UnitCalculationStandard {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.standard",
									Old: models.OperationLogField{
										Type:  2,
										Value: oldValue.UnitCalculationStandard,
									},
									New: models.OperationLogField{
										Type:  2,
										Value: v.UnitCalculationStandard,
									},
								})
							}
						}

						//未编号研究产品
						if v.IsOther != oldValue.IsOther {
							OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
								Key:     "operation_log.drug_configure",
								TranKey: "operation_log.drug_configure.otherCheck",
								Old: models.OperationLogField{
									Type:  6,
									Value: oldValue.IsOther,
								},
								New: models.OperationLogField{
									Type:  6,
									Value: v.IsOther,
								},
							})
						}
						//开放研究产品
						if v.IsOpen != oldValue.IsOpen {
							OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
								Key:     "operation_log.drug_configure",
								TranKey: "operation_log.drug_configure.openCheck",
								Old: models.OperationLogField{
									Type:  6,
									Value: oldValue.IsOpen,
								},
								New: models.OperationLogField{
									Type:  6,
									Value: v.IsOpen,
								},
							})
						}
						//保留小数位
						if v.CalculationInfo.KeepDecimal != oldValue.CalculationInfo.KeepDecimal {
							OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
								Key:     "operation_log.drug_configure",
								TranKey: "operation_log.drug_configure.keepDecimal",
								Old: models.OperationLogField{
									Type:  6,
									Value: oldValue.CalculationInfo.KeepDecimal,
								},
								New: models.OperationLogField{
									Type:  6,
									Value: v.CalculationInfo.KeepDecimal,
								},
							})
						}
						if v.CalculationInfo.KeepDecimal {
							//保留位数
							if v.CalculationInfo.Precision != oldValue.CalculationInfo.Precision {
								OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
									Key:     "operation_log.drug_configure",
									TranKey: "operation_log.drug_configure.precision",
									Old: models.OperationLogField{
										Type:  1,
										Value: oldValue.CalculationInfo.Precision,
									},
									New: models.OperationLogField{
										Type:  1,
										Value: v.CalculationInfo.Precision,
									},
								})
							}
						}
					}
				}
			}
		}

		//常规访视映射
		//oldRoutineVisitMappingList := old.RoutineVisitMappingList
		newRoutineVisitMappingList := new.RoutineVisitMappingList

		visitDrugNameDispeningNumberList := make([]string, 0)
		if newRoutineVisitMappingList != nil && len(newRoutineVisitMappingList) > 0 {
			for _, newRoutineVisitMapping := range newRoutineVisitMappingList {
				visitNameList := make([]string, 0)
				if visitCycle.Infos != nil {
					for _, v := range visitCycle.Infos {
						if newRoutineVisitMapping.VisitList != nil && len(newRoutineVisitMapping.VisitList) > 0 {
							for _, visitId := range newRoutineVisitMapping.VisitList {
								if v.ID == visitId {
									visitNameList = append(visitNameList, v.Name)
								}
							}
						}
					}
				}
				strings.Join(visitNameList, "、")
				if newRoutineVisitMapping.DrugList != nil && len(newRoutineVisitMapping.DrugList) > 0 {
					for _, drug := range newRoutineVisitMapping.DrugList {
						visitDrugNameDispeningNumberList = append(visitDrugNameDispeningNumberList, strings.Join(visitNameList, "、")+"/"+drug.DrugName+"/"+drug.CustomDispensingNumber)
					}
				}
			}
		}

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.drug_configure",
			TranKey: "operation_log.drug_configure.visitDrugNameDispeningNumber",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: strings.Join(visitDrugNameDispeningNumberList, "、"),
			},
		})

	}

	if (OperationLogFieldGroups != nil && len(OperationLogFieldGroups) > 0) && (types == 1 || types == 2 || types == 10) {
		newOperationLogFieldGroup := models.OperationLogFieldGroup{
			Key:     "operation_log.drug_configure",
			TranKey: "operation_log.drug_configure.onlyID",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.ID,
			},
		}
		// 添加新的 OperationLogFieldGroup 到数组的开头
		OperationLogFieldGroups = append([]models.OperationLogFieldGroup{newOperationLogFieldGroup}, OperationLogFieldGroups...)
	}
	marks := []models.Mark{}
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.drug_configure", OID, types, OperationLogFieldGroups, marks, operID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func rolePermissionMenu(ctx *gin.Context, permissionIds []string, reverseIndex map[string][]string, positionMap map[string]int) string {
	// change reverseIndex to map[string]string
	permission2Menu := make(map[string]string)
	for permission, menu := range reverseIndex {
		permission2Menu[permission] = strings.Join(menu, " > ")
	}
	menu := make(map[string][]string)
	for _, p := range permissionIds {
		if _, ok := permission2Menu[p]; ok {
			if _, ok := menu[permission2Menu[p]]; !ok {
				menu[permission2Menu[p]] = make([]string, 0)
			}
			menu[permission2Menu[p]] = append(menu[permission2Menu[p]], p)
		}
	}
	// change menus from map[string][]string to [][]string
	tmpM := make([][]string, len(positionMap))
	for _, permissions := range menu {
		if menuCascade, ok := reverseIndex[permissions[0]]; ok {
			lowestMenu := menuCascade[len(menuCascade)-1]
			index := positionMap[lowestMenu]
			tmpM[index] = permissions
		}
	}
	// compact the sparse array m to a dense array menu
	m := make([][]string, len(menu))
	mIndex := 0
	for _, item := range tmpM {
		if len(item) != 0 {
			m[mIndex] = item
			mIndex++
		}
	}
	rows := make([]string, 0)

	// 其他行
	for _, permissions := range m {
		// 层叠菜单
		menuCascade := make([]string, 0)
		for _, code := range reverseIndex[permissions[0]] {
			menuCascade = append(menuCascade, locales.Tr(ctx, code))
		}
		// 权限
		p := make([]string, 0)
		for _, permission := range permissions {
			p = append(p, locales.Tr(ctx, permission))
		}
		rows = append(rows, fmt.Sprintf("%s:%s;", strings.Join(menuCascade, "-"), strings.Join(p, "、")))
	}
	perStr := strings.Join(rows, "")
	return perStr
}

// int转bool
func intToBool(intValue int) bool {
	boolValue := false
	if intValue == 1 {
		boolValue = true
	}
	return boolValue
}

// bool转int
func boolToInt(boolValue bool) int {
	intValue := 0
	if boolValue {
		intValue = 1
	}
	return intValue
}

func insertVisitDragLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, oldVisit, newVisit models.VisitCycle, OID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	marks := []models.Mark{}
	oldVisitArr := []string{}
	for _, info := range oldVisit.ConfigInfo.Infos {
		oldVisitArr = append(oldVisitArr, info.Number)
	}
	newVisitArr := []string{}
	for _, info := range newVisit.ConfigInfo.Infos {
		newVisitArr = append(newVisitArr, info.Number)
	}
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.visitCycle",
		TranKey: "operation_log.visitCycle.sort",
		Old: models.OperationLogField{
			Type:  2,
			Value: strings.Join(oldVisitArr, ","),
		},
		New: models.OperationLogField{
			Type:  2,
			Value: strings.Join(newVisitArr, ","),
		},
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.visitCycle", envOID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func insertVisitTypeLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, oldVisit, newVisit models.VisitCycle, OID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	marks := []models.Mark{}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.visitCycle",
		TranKey: "operation_log.visitCycle.type",
		Old: models.OperationLogField{
			Type:  6,
			Value: oldVisit.ConfigInfo.VisitType,
		},
		New: models.OperationLogField{
			Type:  6,
			Value: newVisit.ConfigInfo.VisitType,
		},
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.visitCycle", envOID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func insertBaseCohortLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, oldVisit, newVisit models.VisitCycle, OID primitive.ObjectID, oldCohortName, newCohortName string) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	marks := []models.Mark{}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.visitCycle",
		TranKey: "operation_log.visitCycle.baseCohort",
		Old: models.OperationLogField{
			Type:  2,
			Value: oldCohortName,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: newCohortName,
		},
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.visitCycle", envOID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func insertVisitPushLog(ctx *gin.Context, sctx mongo.SessionContext, visitCycle models.VisitCycle, envOID primitive.ObjectID, OID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	marks := []models.Mark{}

	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.visitCycle",
		TranKey: "operation_log.visitCycle.sop",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  6,
			Value: "1",
		},
	})
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.visitCycle",
		TranKey: "operation_log.visitCycle.version",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: visitCycle.Version,
		},
	})
	err := tools.SaveOperation(ctx, sctx, "operation_log.module.visitCycle", envOID, 1, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// insertProjectLog
func insertProjectLog(ctx *gin.Context, sctx mongo.SessionContext, envOID primitive.ObjectID, types int, oldType string, newType string, OID primitive.ObjectID) error {
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	marks := []models.Mark{}
	if types != 3 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project.properties",
			TranKey: "operation_log.project.type",
			Old: models.OperationLogField{
				Type:  2,
				Value: oldType,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newType,
			},
		})
	}

	if types == 3 || len(OperationLogFieldGroups) != 0 {
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.project_basic_information", envOID, types, OperationLogFieldGroups, marks, OID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func insertDrugConfigureSettingLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, old models.DrugConfigureSetting, new models.DrugConfigureSetting, operID primitive.ObjectID, envOID primitive.ObjectID, cohortOID primitive.ObjectID, copyEnv string) error {
	// 保存项目日志
	OperationLogFieldGroups := make([]models.OperationLogFieldGroup, 0)
	if types != 3 {

		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}

		if new.DtpIpList != nil && len(new.DtpIpList) > 0 {
			dtpIpTypeListZh := make([]string, 0)
			dtpIpTypeListEn := make([]string, 0)
			for _, dtpIp := range new.DtpIpList {
				dtpIpType := dtpIp.IP + "/"
				if dtpIp.DtpTypeList != nil && len(dtpIp.DtpTypeList) > 0 {
					dtpTypeListZh := make([]string, 0)
					dtpTypeListEn := make([]string, 0)
					for _, dtpType := range dtpIp.DtpTypeList {
						dtpTypeZh := ""
						dtpTypeEn := ""
						if dtpType != 0 {
							if dtpType == 1 {
								dtpTypeZh = locales.TrWithLang("zh", "operation_log.drug_configure_setting.dtp_ipType_site")
								dtpTypeEn = locales.TrWithLang("en", "operation_log.drug_configure_setting.dtp_ipType_site")
							} else if dtpType == 2 {
								dtpTypeZh = locales.TrWithLang("zh", "operation_log.drug_configure_setting.dtp_ipType_siteSubject")
								dtpTypeEn = locales.TrWithLang("en", "operation_log.drug_configure_setting.dtp_ipType_siteSubject")
							} else if dtpType == 3 {
								dtpTypeZh = locales.TrWithLang("zh", "operation_log.drug_configure_setting.dtp_ipType_depotSubject")
								dtpTypeEn = locales.TrWithLang("en", "operation_log.drug_configure_setting.dtp_ipType_depotSubject")
							}
							dtpTypeListZh = append(dtpTypeListZh, dtpTypeZh)
							dtpTypeListEn = append(dtpTypeListEn, dtpTypeEn)
						}
					}
					dtpIpTypeListZh = append(dtpIpTypeListZh, dtpIpType+strings.Join(dtpTypeListZh, "、"))
					dtpIpTypeListEn = append(dtpIpTypeListEn, dtpIpType+strings.Join(dtpTypeListEn, "、"))
				}
			}

			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.dtp_ipType",
				Old: models.OperationLogField{
					Type:    30,
					Value:   nil,
					ENValue: nil,
				},
				New: models.OperationLogField{
					Type:    30,
					Value:   strings.Join(dtpIpTypeListZh, ","),
					ENValue: strings.Join(dtpIpTypeListEn, ","),
				},
			})
		}

		oldIsOpen := ""
		if old.IsOpen {
			oldIsOpen = locales.Tr(ctx, "drug.configure.setting.doseAdjustment.open")
		} else {
			oldIsOpen = locales.Tr(ctx, "drug.configure.setting.doseAdjustment.close")
		}
		newIsOpen := ""
		if old.IsOpen {
			newIsOpen = locales.Tr(ctx, "drug.configure.setting.doseAdjustment.open")
		} else {
			newIsOpen = locales.Tr(ctx, "drug.configure.setting.doseAdjustment.close")
		}

		//剂量调整开关
		if new.SelectType != old.SelectType {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.doseAdjustment",
				Old: models.OperationLogField{
					Type:  1,
					Value: oldIsOpen,
				},
				New: models.OperationLogField{
					Type:  1,
					Value: newIsOpen,
				},
			})
		}

		oldSelectTypeName := ""
		if old.SelectType == 1 {
			oldSelectTypeName = locales.Tr(ctx, "drug.configure.setting.dose.level")
		} else if old.SelectType == 2 {
			oldSelectTypeName = locales.Tr(ctx, "drug.configure.setting.dose.visit.judgment")
		}
		newSelectTypeName := ""
		if old.SelectType == 1 {
			newSelectTypeName = locales.Tr(ctx, "drug.configure.setting.dose.level")
		} else if old.SelectType == 2 {
			newSelectTypeName = locales.Tr(ctx, "drug.configure.setting.dose.visit.judgment")
		}

		//类型
		if new.SelectType != old.SelectType {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.selectType",
				Old: models.OperationLogField{
					Type:  1,
					Value: oldSelectTypeName,
				},
				New: models.OperationLogField{
					Type:  1,
					Value: newSelectTypeName,
				},
			})
		}
		oldDoseFormName := ""
		oldFieldName := ""
		oldOptionList := make([]models.Option, 0)
		if len(old.DoseFormId) != 0 {
			oldDoseForm, err := GetFormType(ctx, new.CustomerID.Hex(), new.EnvironmentID.Hex(), new.CohortID.Hex(), 3, old.DoseFormId)
			if err != nil {
				return errors.WithStack(err)
			}
			oldDoseFormName = oldDoseForm.Variable
			oldFieldName = oldDoseForm.Label
			oldOptionList = oldDoseForm.Options
		}
		newDoseFormName := ""
		newFieldName := ""
		newOptionList := make([]models.Option, 0)
		if len(new.DoseFormId) != 0 {
			newDoseForm, err := GetFormType(ctx, new.CustomerID.Hex(), new.EnvironmentID.Hex(), new.CohortID.Hex(), 3, new.DoseFormId)
			if err != nil {
				return errors.WithStack(err)
			}
			newDoseFormName = newDoseForm.Variable
			newFieldName = newDoseForm.Label
			newOptionList = newDoseForm.Options
		}
		//剂量表单
		if new.DoseFormId != old.DoseFormId {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.doseForm",
				Old: models.OperationLogField{
					Type:  2,
					Value: oldDoseFormName,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: newDoseFormName,
				},
			})
		}

		//首次访视启用初始剂量
		if new.IsFirstInitial != old.IsFirstInitial {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.isFirstInitial",
				Old: models.OperationLogField{
					Type:  4,
					Value: old.IsFirstInitial,
				},
				New: models.OperationLogField{
					Type:  4,
					Value: new.IsFirstInitial,
				},
			})
		}

		//允许受试者剂量下调的次数
		if new.IsDoseReduction != old.IsDoseReduction {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.isDoseReduction",
				Old: models.OperationLogField{
					Type:  4,
					Value: old.IsDoseReduction,
				},
				New: models.OperationLogField{
					Type:  4,
					Value: new.IsDoseReduction,
				},
			})
		}

		//次数
		if new.Frequency != old.Frequency {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.frequency",
				Old: models.OperationLogField{
					Type:  1,
					Value: old.Frequency,
				},
				New: models.OperationLogField{
					Type:  1,
					Value: new.Frequency,
				},
			})
		}

		//剂量水平集
		// 创建两个map，用于快速查找
		oldDoseLevelMap := make(map[primitive.ObjectID]models.DoseLevel)
		for _, obj := range old.DoseLevelList {
			oldDoseLevelMap[obj.ID] = obj
		}
		newDoseLevelMap := make(map[primitive.ObjectID]models.DoseLevel)
		for _, obj := range new.DoseLevelList {
			newDoseLevelMap[obj.ID] = obj
		}
		// 找出新增的元素
		for id, newObj := range newDoseLevelMap {
			if _, ok := oldDoseLevelMap[id]; !ok {
				//fmt.Printf("新增对象: %v\n", newObj)
				//剂量水平
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevel",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newObj.Name,
					},
				})
				//名称
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelName",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newObj.Name,
					},
				})
				//组别
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelGroup",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(newObj.Group, ", "),
					},
				})

				//发放剂量
				newDoseDistributionList := make([]string, 0)
				if newObj.DoseDistribution != nil && len(newObj.DoseDistribution) > 0 {
					for _, doseDistribution := range newObj.DoseDistribution {
						var jsonObject map[string]interface{}
						json.Unmarshal([]byte(doseDistribution), &jsonObject)
						fmt.Println(jsonObject)
						name := jsonObject["name"].(string)
						newDoseDistributionList = append(newDoseDistributionList, name)
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelDoseDistribution",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(newDoseDistributionList, ", "),
					},
				})
				//初始剂量
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelInitialDose",
					Old: models.OperationLogField{
						Type:  30,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:    30,
						Value:   FormatBoolZh(newObj.InitialDose),
						ENValue: strconv.FormatBool(newObj.InitialDose),
					},
				})
			}
		}
		// 找出删除的元素
		for id, oldObj := range oldDoseLevelMap {
			if _, ok := newDoseLevelMap[id]; !ok {
				fmt.Printf("删除对象: %v\n", oldObj)
				//ID
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelID",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldObj.ID.Hex(),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//剂量水平
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevel",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldObj.Name,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//名称
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelName",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldObj.Name,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//组别
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelGroup",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(oldObj.Group, ", "),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//发放剂量
				oldDoseDistributionList := make([]string, 0)
				if oldObj.DoseDistribution != nil && len(oldObj.DoseDistribution) > 0 {
					for _, doseDistribution := range oldObj.DoseDistribution {
						var jsonObject map[string]interface{}
						json.Unmarshal([]byte(doseDistribution), &jsonObject)
						fmt.Println(jsonObject)
						name := jsonObject["name"].(string)
						oldDoseDistributionList = append(oldDoseDistributionList, name)
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelDoseDistribution",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(oldDoseDistributionList, ", "),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//初始剂量
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.doseLevelInitialDose",
					Old: models.OperationLogField{
						Type:    30,
						Value:   FormatBoolZh(oldObj.InitialDose),
						ENValue: strconv.FormatBool(oldObj.InitialDose),
					},
					New: models.OperationLogField{
						Type:  30,
						Value: nil,
					},
				})
			}
		}
		// 找出修改的元素
		for id, newObj := range newDoseLevelMap {
			if oldObj, ok := oldDoseLevelMap[id]; ok {

				if newObj.Name != oldObj.Name || !reflect.DeepEqual(newObj.Group, oldObj.Group) ||
					!reflect.DeepEqual(newObj.DoseDistribution, oldObj.DoseDistribution) ||
					newObj.InitialDose != oldObj.InitialDose {
					//剂量水平
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.doseLevel",
						Old: models.OperationLogField{
							Type:  2,
							Value: nil,
						},
						New: models.OperationLogField{
							Type:  2,
							Value: newObj.Name,
						},
					})
				}

				if newObj.Name != oldObj.Name {
					//名称
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.doseLevelName",
						Old: models.OperationLogField{
							Type:  2,
							Value: oldObj.Name,
						},
						New: models.OperationLogField{
							Type:  2,
							Value: newObj.Name,
						},
					})
				}

				// 使用reflect.DeepEqual函数比较两个字符串数组
				if !reflect.DeepEqual(newObj.Group, oldObj.Group) {
					//组别
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.doseLevelGroup",
						Old: models.OperationLogField{
							Type:  2,
							Value: strings.Join(oldObj.Group, ", "),
						},
						New: models.OperationLogField{
							Type:  2,
							Value: strings.Join(newObj.Group, ", "),
						},
					})
				}
				if !reflect.DeepEqual(newObj.DoseDistribution, oldObj.DoseDistribution) {
					//发放剂量
					oldDoseDistributionList := make([]string, 0)
					if oldObj.DoseDistribution != nil && len(oldObj.DoseDistribution) > 0 {
						for _, doseDistribution := range oldObj.DoseDistribution {
							var jsonObject map[string]interface{}
							json.Unmarshal([]byte(doseDistribution), &jsonObject)
							fmt.Println(jsonObject)
							name := jsonObject["name"].(string)
							oldDoseDistributionList = append(oldDoseDistributionList, name)
						}
					}
					newDoseDistributionList := make([]string, 0)
					if newObj.DoseDistribution != nil && len(newObj.DoseDistribution) > 0 {
						for _, doseDistribution := range newObj.DoseDistribution {
							var jsonObject map[string]interface{}
							json.Unmarshal([]byte(doseDistribution), &jsonObject)
							fmt.Println(jsonObject)
							name := jsonObject["name"].(string)
							newDoseDistributionList = append(newDoseDistributionList, name)
						}
					}
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.doseLevelDoseDistribution",
						Old: models.OperationLogField{
							Type:  2,
							Value: strings.Join(oldDoseDistributionList, ", "),
						},
						New: models.OperationLogField{
							Type:  2,
							Value: strings.Join(newDoseDistributionList, ", "),
						},
					})
				}
				if newObj.InitialDose != oldObj.InitialDose {
					//初始剂量
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.doseLevelInitialDose",
						Old: models.OperationLogField{
							Type:    30,
							Value:   FormatBoolZh(oldObj.InitialDose),
							ENValue: strconv.FormatBool(oldObj.InitialDose),
						},
						New: models.OperationLogField{
							Type:    30,
							Value:   FormatBoolZh(newObj.InitialDose),
							ENValue: strconv.FormatBool(newObj.InitialDose),
						},
					})
				}

			}
		}

		//访视判断
		// 创建两个map，用于快速查找
		oldVisitJudgmentMap := make(map[primitive.ObjectID]models.VisitJudgment)
		for _, obj := range old.VisitJudgmentList {
			oldVisitJudgmentMap[obj.ID] = obj
		}
		newVisitJudgmentMap := make(map[primitive.ObjectID]models.VisitJudgment)
		for _, obj := range new.VisitJudgmentList {
			newVisitJudgmentMap[obj.ID] = obj
		}

		// 找出新增的元素
		for id, newObj := range newVisitJudgmentMap {
			if _, ok := oldVisitJudgmentMap[id]; !ok {
				fmt.Printf("新增对象: %v\n", newObj)

				newName := ""
				if newOptionList != nil && len(newOptionList) > 0 {
					for _, option := range newOptionList {
						if option.Value == newObj.Name {
							newName = newFieldName + " : " + option.Label
						}
					}
				}

				//访视判断
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgment",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newName,
					},
				})
				//名称
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentName",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newName,
					},
				})
				//组别
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentGroup",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(newObj.Group, ", "),
					},
				})
				//发放剂量
				newDoseDistributionList := make([]string, 0)
				if newObj.DoseDistribution != nil && len(newObj.DoseDistribution) > 0 {
					for _, doseDistribution := range newObj.DoseDistribution {
						var jsonObject map[string]interface{}
						json.Unmarshal([]byte(doseDistribution), &jsonObject)
						fmt.Println(jsonObject)
						name := jsonObject["name"].(string)
						newDoseDistributionList = append(newDoseDistributionList, name)
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentDoseDistribution",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: strings.Join(newDoseDistributionList, ", "),
					},
				})
				//后续访视继承
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentVisitInheritance",
					Old: models.OperationLogField{
						Type:  30,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:    30,
						Value:   FormatBoolZh(newObj.VisitInheritance),
						ENValue: strconv.FormatBool(newObj.VisitInheritance),
					},
				})
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentVisitInheritanceCount",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: newObj.VisitInheritanceCount,
					},
				})
			}
		}

		// 找出删除的元素
		for id, oldObj := range oldVisitJudgmentMap {
			if _, ok := newVisitJudgmentMap[id]; !ok {
				fmt.Printf("删除对象: %v\n", oldObj)

				oldName := ""
				if oldOptionList != nil && len(oldOptionList) > 0 {
					for _, option := range oldOptionList {
						if option.Value == oldObj.Name {
							oldName = oldFieldName + " : " + option.Label
						}
					}
				}

				//ID
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentID",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldObj.ID,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//访视判断
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgment",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldName,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//名称
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentName",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldName,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//组别
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentGroup",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(oldObj.Group, ", "),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//发放剂量
				oldDoseDistributionList := make([]string, 0)
				if oldObj.DoseDistribution != nil && len(oldObj.DoseDistribution) > 0 {
					for _, doseDistribution := range oldObj.DoseDistribution {
						var jsonObject map[string]interface{}
						json.Unmarshal([]byte(doseDistribution), &jsonObject)
						fmt.Println(jsonObject)
						name := jsonObject["name"].(string)
						oldDoseDistributionList = append(oldDoseDistributionList, name)
					}
				}
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentDoseDistribution",
					Old: models.OperationLogField{
						Type:  2,
						Value: strings.Join(oldDoseDistributionList, ", "),
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
				//后续访视继承
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentVisitInheritance",
					Old: models.OperationLogField{
						Type:    30,
						Value:   FormatBoolZh(oldObj.VisitInheritance),
						ENValue: strconv.FormatBool(oldObj.VisitInheritance),
					},
					New: models.OperationLogField{
						Type:  30,
						Value: nil,
					},
				})
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.drug_configure_setting",
					TranKey: "operation_log.drug_configure_setting.visitJudgmentVisitInheritanceCount",
					Old: models.OperationLogField{
						Type:  2,
						Value: oldObj.VisitInheritanceCount,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
				})
			}
		}

		// 找出修改的元素
		for id, newObj := range newVisitJudgmentMap {
			if oldObj, ok := oldVisitJudgmentMap[id]; ok {

				oldName := ""
				if oldOptionList != nil && len(oldOptionList) > 0 {
					for _, option := range oldOptionList {
						if option.Value == oldObj.Name {
							oldName = oldFieldName + " : " + option.Label
						}
					}
				}

				newName := ""
				if newOptionList != nil && len(newOptionList) > 0 {
					for _, option := range newOptionList {
						if option.Value == newObj.Name {
							newName = newFieldName + " : " + option.Label
						}
					}
				}

				if newObj.Name != oldObj.Name || !reflect.DeepEqual(newObj.Group, oldObj.Group) ||
					!reflect.DeepEqual(newObj.DoseDistribution, oldObj.DoseDistribution) ||
					newObj.VisitInheritance != oldObj.VisitInheritance {
					//访视判断
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.visitJudgment",
						Old: models.OperationLogField{
							Type:  2,
							Value: nil,
						},
						New: models.OperationLogField{
							Type:  2,
							Value: newName,
						},
					})
				}

				if newObj.Name != oldObj.Name {
					//名称
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.visitJudgmentName",
						Old: models.OperationLogField{
							Type:  2,
							Value: oldName,
						},
						New: models.OperationLogField{
							Type:  2,
							Value: newName,
						},
					})
				}

				// 使用reflect.DeepEqual函数比较两个字符串数组
				if !reflect.DeepEqual(newObj.Group, oldObj.Group) {
					//组别
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.visitJudgmentGroup",
						Old: models.OperationLogField{
							Type:  2,
							Value: strings.Join(oldObj.Group, ", "),
						},
						New: models.OperationLogField{
							Type:  2,
							Value: strings.Join(newObj.Group, ", "),
						},
					})
				}
				if !reflect.DeepEqual(newObj.DoseDistribution, oldObj.DoseDistribution) {
					//发放剂量
					oldDoseDistributionList := make([]string, 0)
					if oldObj.DoseDistribution != nil && len(oldObj.DoseDistribution) > 0 {
						for _, doseDistribution := range oldObj.DoseDistribution {
							var jsonObject map[string]interface{}
							json.Unmarshal([]byte(doseDistribution), &jsonObject)
							fmt.Println(jsonObject)
							name := jsonObject["name"].(string)
							oldDoseDistributionList = append(oldDoseDistributionList, name)
						}
					}
					newDoseDistributionList := make([]string, 0)
					if newObj.DoseDistribution != nil && len(newObj.DoseDistribution) > 0 {
						for _, doseDistribution := range newObj.DoseDistribution {
							var jsonObject map[string]interface{}
							json.Unmarshal([]byte(doseDistribution), &jsonObject)
							fmt.Println(jsonObject)
							name := jsonObject["name"].(string)
							newDoseDistributionList = append(newDoseDistributionList, name)
						}
					}
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.visitJudgmentDoseDistribution",
						Old: models.OperationLogField{
							Type:  2,
							Value: strings.Join(oldDoseDistributionList, ", "),
						},
						New: models.OperationLogField{
							Type:  2,
							Value: strings.Join(newDoseDistributionList, ", "),
						},
					})
				}
				if newObj.VisitInheritance != oldObj.VisitInheritance {
					//初始剂量
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.visitJudgmentVisitInheritance",
						Old: models.OperationLogField{
							Type:    30,
							Value:   FormatBoolZh(oldObj.VisitInheritance),
							ENValue: strconv.FormatBool(oldObj.VisitInheritance),
						},
						New: models.OperationLogField{
							Type:    30,
							Value:   FormatBoolZh(newObj.VisitInheritance),
							ENValue: strconv.FormatBool(newObj.VisitInheritance),
						},
					})

				}
				if oldObj.VisitInheritanceCount != newObj.VisitInheritanceCount {
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.drug_configure_setting",
						TranKey: "operation_log.drug_configure_setting.visitJudgmentVisitInheritanceCount",
						Old: models.OperationLogField{
							Type:  2,
							Value: oldObj.VisitInheritanceCount,
						},
						New: models.OperationLogField{
							Type:  2,
							Value: newObj.VisitInheritanceCount,
						},
					})
				}
			}
		}

		//  访视继承发放
		if len(old.GroupType) > len(new.GroupType) {
			oldLength := len(old.GroupType) - len(new.GroupType)
			for _ = range oldLength {
				new.GroupType = append(new.GroupType, models.GroupType{})
			}
		}
		if len(new.GroupType) > len(old.GroupType) {
			oldLength := len(new.GroupType) - len(old.GroupType)
			for _ = range oldLength {
				old.GroupType = append(old.GroupType, models.GroupType{})
			}
		}

		for i, oldValue := range old.GroupType {
			newValue := new.GroupType[i]
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.InheritanceGroup",
				Old: models.OperationLogField{
					Type:  2,
					Value: oldValue.Group,
					Mark:  1,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: newValue.Group,
					Mark:  1,
				},
			})
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.InheritanceRule",
				Old: models.OperationLogField{
					Type:  6,
					Value: oldValue.Type,
					Mark:  1,
				},
				New: models.OperationLogField{
					Type:  6,
					Value: newValue.Type,
					Mark:  1,
				},
			})
		}

	}
	marks := []models.Mark{}
	if copyEnv != "" && len(OperationLogFieldGroups) == 2 {
		return nil
	} else {
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.drug_configure_setting", OID, types, OperationLogFieldGroups, marks, operID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func FormatBoolZh(b bool) string {
	if b {
		return "是"
	}
	return "否"
}

func GetFormType(ctx *gin.Context, customerID string, envID string, cohortID string, applicationType int, id string) (models.Field, error) {
	var field models.Field
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	filter := bson.M{
		"customer_id": customerOID,
		"env_id":      envOID,
		//"fields":      bson.M{"$elemMatch": bson.M{"application_type": applicationType}},
	}

	if cohortID != "" {
		filter["cohort_id"] = cohortOID
	}
	var form models.Form
	if err := tools.Database.Collection("form").FindOne(nil, filter).Decode(&form); err != nil && err != mongo.ErrNoDocuments {
		return field, errors.WithStack(err)
	}
	opts := options.Find().SetProjection(bson.M{
		"info": 1,
	})
	type subjectResp struct {
		ID   primitive.ObjectID `json:"id" bson:"_id"`
		Info []models.Info      `json:"info"`
	}
	subjects := make([]*subjectResp, 0)
	cursor, err := tools.Database.Collection("subject").Find(nil, filter, opts)
	if err != nil {
		return field, errors.WithStack(err)
	}
	err = cursor.All(nil, &subjects)
	if err != nil {
		return field, errors.WithStack(err)
	}
	for i := range form.Fields {
		//状态默认值
		if form.Fields[i].Status == nil {
			status := 1
			form.Fields[i].Status = &status
		}
		// 应用类型 默认值
		if form.Fields[i].ApplicationType == nil {
			at := 1
			form.Fields[i].ApplicationType = &at
		}

		//判断是否有subject引用
		if form.Fields[i].Type == "select" || form.Fields[i].Type == "radio" || form.Fields[i].Type == "checkbox" || form.Fields[i].IsCalc == true {
			for _, subject := range subjects {
				for _, info := range subject.Info {
					if info.Name == form.Fields[i].Name {
						for j := range form.Fields[i].Options {
							if form.Fields[i].Type == "checkbox" && info.Value != nil {
								for _, v := range info.Value.(primitive.A) {
									if form.Fields[i].Options[j].Value == v {
										form.Fields[i].Options[j].Disable = true
									}
								}
							} else if form.Fields[i].Options[j].Value == info.Value {
								form.Fields[i].Options[j].Disable = true
							}
						}
					}
				}
			}
		}
		apt := *form.Fields[i].ApplicationType
		status := *form.Fields[i].Status
		if apt == applicationType && status != 2 {
			if id == form.Fields[i].ID.Hex() {
				field = form.Fields[i]
			}
		}
	}

	return field, nil
}

func InsertBarcodeLog(ctx *gin.Context, sctx mongo.SessionContext, types int, barcodeGroup models.BarcodeGroup, effectiveTime string, copyEnv string) error {
	OID := barcodeGroup.EnvironmentID
	if barcodeGroup.CohortID != primitive.NilObjectID {
		OID = barcodeGroup.CohortID
	}
	// 保存项目日志
	var operationLogFieldGroups []models.OperationLogFieldGroup

	if copyEnv != "" {
		operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.barcode_add",
			TranKey: "operation_log.addBarcode.env_name",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: copyEnv,
			},
		})
	}

	//查询仓库名称
	var projectStorehouse models.ProjectStorehouse
	err := tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": barcodeGroup.StorehouseID}).Decode(&projectStorehouse)
	if err != nil {
		return errors.WithStack(err)
	}
	var storehouse models.Storehouse
	_ = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": projectStorehouse.StorehouseID}).Decode(&storehouse)
	operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.barcode_add",
		TranKey: "operation_log.addBarcode.storehouse",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: storehouse.Name,
		},
	})

	operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.barcode_add",
		TranKey: "operation_log.addBarcode.expireDate",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: effectiveTime,
		},
	})
	operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.barcode_add",
		TranKey: "operation_log.addBarcode.batchNumber",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: barcodeGroup.BatchNumber,
		},
	})
	operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.barcode_add",
		TranKey: "operation_log.addBarcode.count",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: len(barcodeGroup.MedicineIDs),
		},
	})
	operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.barcode_add",
		TranKey: "operation_log.addBarcode.prefix",
		Old: models.OperationLogField{
			Type:  2,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  2,
			Value: barcodeGroup.Prefix,
		},
	})

	//研究产品条形码规则
	operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.barcode_add",
		TranKey: "operation_log.addBarcode.barcodeRule",
		Old: models.OperationLogField{
			Type:  6,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  6,
			Value: barcodeGroup.BarcodeRule,
		},
	})
	//包装是否生成
	operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.barcode_add",
		TranKey: "operation_log.addBarcode.isPackageBarcode",
		Old: models.OperationLogField{
			Type:  6,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  6,
			Value: barcodeGroup.IsPackageBarcode,
		},
	})
	//包装条形码生成规则
	operationLogFieldGroups = append(operationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.barcode_add",
		TranKey: "operation_log.addBarcode.packageRule",
		Old: models.OperationLogField{
			Type:  6,
			Value: "",
		},
		New: models.OperationLogField{
			Type:  6,
			Value: barcodeGroup.PackageRule,
		},
	})

	marks := []models.Mark{}
	// marks = append(marks,
	// 	models.Mark{
	// 		Label: "operation_log.label.barcode_add",
	// 		Value: storehouse.Name, // 原来 barcodeGroup.DrugName
	// 		Blind: false,
	// 	})

	err = tools.SaveOperation(ctx, sctx, "operation_log.module.barcode_add", OID, types, operationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func InsertMedicineLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, oldMedicine []models.CopyMedicine, newMedicine []models.CopyMedicine, copyEnv string) error {

	operationLogList := make([]models.OperationLog, 0)
	if oldMedicine == nil {
		if newMedicine != nil && len(newMedicine) > 0 {
			for _, medicine := range newMedicine {
				drugName := medicine.Name
				spec := medicine.Spec

				storehouseID := medicine.StorehouseID
				expirationDate := medicine.ExpirationDate
				batch := medicine.BatchNumber
				// 保存项目日志
				var OperationLogFieldGroups []models.OperationLogFieldGroup
				if types != 3 {

					if copyEnv != "" {
						OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
							Key:     "operation_log.medicinesList",
							TranKey: "operation_log.uploadMedicines.env_name",
							Old: models.OperationLogField{
								Type:  2,
								Value: "",
							},
							New: models.OperationLogField{
								Type:  2,
								Value: copyEnv,
							},
						})
					}

					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.medicinesList",
						TranKey: "operation_log.uploadMedicines.medicineName",
						Old: models.OperationLogField{
							Type:  7,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  7,
							Value: drugName,
						},
					})
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.medicinesList",
						TranKey: "operation_log.uploadMedicines.medicineSpec",
						Old: models.OperationLogField{
							Type:  2,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  2,
							Value: spec,
						},
					})
					//查询仓库名称
					var projectStorehouse models.ProjectStorehouse
					err := tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": storehouseID}).Decode(&projectStorehouse)
					if err != nil {
						return errors.WithStack(err)
					}
					var storehouse models.Storehouse
					_ = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": projectStorehouse.StorehouseID}).Decode(&storehouse)
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.medicinesList",
						TranKey: "operation_log.uploadMedicines.storehouse",
						Old: models.OperationLogField{
							Type:  2,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  2,
							Value: storehouse.Name,
						},
					})
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.medicinesList",
						TranKey: "operation_log.uploadMedicines.expireDate",
						Old: models.OperationLogField{
							Type:  2,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  2,
							Value: expirationDate,
						},
					})
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.medicinesList",
						TranKey: "operation_log.uploadMedicines.batchNumber",
						Old: models.OperationLogField{
							Type:  2,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  2,
							Value: batch,
						},
					})
					OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
						Key:     "operation_log.medicinesList",
						TranKey: "operation_log.uploadMedicines.count",
						Old: models.OperationLogField{
							Type:  2,
							Value: "",
						},
						New: models.OperationLogField{
							Type:  2,
							Value: strconv.Itoa(medicine.Count),
						},
					})
				}
				marks := make([]models.Mark, 0)
				u, _ := ctx.Get("user")
				user := u.(models.User)
				var operationLog models.OperationLog
				operationLog.ID = primitive.NewObjectID()
				operationLog.OID = OID
				operationLog.Operator = user.ID
				operationLog.Module = "operation_log.module.medicinesList_uploadMedicine"
				operationLog.Mark = marks
				operationLog.Time = time.Duration(time.Now().Unix())
				operationLog.Type = types
				operationLog.OperatorID = OID
				operationLog.Fields = OperationLogFieldGroups

				operationLogList = append(operationLogList, operationLog)
			}
		}
	}

	// marks = append(marks,
	// 	models.Mark{
	// 		Label: "operation_log.label.uploadMedicine",
	// 		Value: strings.Join(drugNames, "、"),
	// 		Blind: true,
	// 	})

	if operationLogList != nil && len(operationLogList) > 0 {
		var docs []interface{}
		for _, operationLog := range operationLogList {
			docs = append(docs, operationLog)
		}
		if _, err := tools.Database.Collection("operation_log").InsertMany(nil, docs); err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func InsertUploadMedicineLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, count int, fileName string, drugNamesInfo []models.DrugNameInfo) error {
	drugName := ""
	spec := ""

	for _, v := range drugNamesInfo {
		drugName = drugName + v.DrugName + ";"
		specs := "-"
		if v.Spec != "" {
			specs = v.Spec
		}
		spec = spec + specs + ";"
	}

	storehouseID, _ := primitive.ObjectIDFromHex(ctx.PostForm("storehouseId"))
	expirationDate := ctx.PostForm("expirationDate")
	batch := ctx.PostForm("batch")
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup
	if types != 3 {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.medicineName",
			Old: models.OperationLogField{
				Type:  7,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  7,
				Value: drugName,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.medicineSpec",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: spec,
			},
		})
		//查询仓库名称
		var projectStorehouse models.ProjectStorehouse
		err := tools.Database.Collection("project_storehouse").FindOne(nil, bson.M{"_id": storehouseID}).Decode(&projectStorehouse)
		if err != nil {
			return errors.WithStack(err)
		}
		var storehouse models.Storehouse
		_ = tools.Database.Collection("storehouse").FindOne(nil, bson.M{"_id": projectStorehouse.StorehouseID}).Decode(&storehouse)
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.storehouse",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: storehouse.Name,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.expireDate",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: expirationDate,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.batchNumber",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: batch,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.count",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: count,
			},
		})
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.medicinesList",
			TranKey: "operation_log.uploadMedicines.fileName",
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
			New: models.OperationLogField{
				Type:  2,
				Value: fileName,
			},
		})
	}
	marks := []models.Mark{}
	// marks = append(marks,
	// 	models.Mark{
	// 		Label: "operation_log.label.uploadMedicine",
	// 		Value: strings.Join(drugNames, "、"),
	// 		Blind: true,
	// 	})

	err := tools.SaveOperation(ctx, sctx, "operation_log.module.medicinesList_uploadMedicine", OID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func InsertExamineUpdateReleaseLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, toExamineFlowPathParameter models.ToExamineFlowPathParameter) error {
	// 保存项目日志
	var OperationLogFieldGroups []models.OperationLogFieldGroup

	module := ""
	if toExamineFlowPathParameter.Sign == 1 { // 审核
		module = "operation_log.module.examine"
		statusZh := ""
		statusEn := ""
		if toExamineFlowPathParameter.RadioValue == 1 {
			statusZh = "通过"
			statusEn = "Approved"
		} else if toExamineFlowPathParameter.RadioValue == 2 {
			statusZh = "拒绝"
			statusEn = "Reject"
		}

		var bf strings.Builder
		for _, tm := range toExamineFlowPathParameter.MedicineParameter {
			bf.WriteString(tm.Name + "/" + tm.ExpirationDate + "/" + tm.BatchNumber + "/" + strconv.Itoa(tm.Count) + "、")
		}

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.drug_examine",
			TranKey: "operation_log.drug_examine.nameDateNumberCount",
			New: models.OperationLogField{
				Type:  2,
				Value: bf.String(),
			},
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
		})

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.drug_examine",
			TranKey: "operation_log.drug_examine.examineConfig",
			New: models.OperationLogField{
				Type:    30,
				Value:   statusZh,
				ENValue: statusEn,
			},
			Old: models.OperationLogField{
				Type:    30,
				Value:   "",
				ENValue: "",
			},
		})

		// 只有拒绝才会有备注
		if toExamineFlowPathParameter.RadioValue == 2 {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_examine",
				TranKey: "operation_log.drug_examine.examineNotes",
				New: models.OperationLogField{
					Type:  2,
					Value: toExamineFlowPathParameter.RemarkValue,
				},
				Old: models.OperationLogField{
					Type:  2,
					Value: "",
				},
			})
		}

	} else if toExamineFlowPathParameter.Sign == 2 { // 修改
		module = "operation_log.module.update"
		var bf strings.Builder
		for _, tm := range toExamineFlowPathParameter.MedicineParameter {
			newName := ""
			if tm.NewName != "" {
				newName = tm.NewName
			} else {
				newName = tm.Name
			}
			bf.WriteString(tm.Name + "/" + tm.ExpirationDate + "/" + tm.BatchNumber + "/" + strconv.Itoa(tm.Count) + "->" + newName + "/" + tm.ExpirationDate + "/" + tm.BatchNumber + "/" + strconv.Itoa(tm.Count) + "、")
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.drug_update",
			TranKey: "operation_log.drug_update.nameDateNumberCount",
			New: models.OperationLogField{
				Type:  2,
				Value: bf.String(),
			},
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
		})
	} else { // 放行
		module = "operation_log.module.release"
		var bf strings.Builder
		for _, tm := range toExamineFlowPathParameter.MedicineParameter {
			bf.WriteString(tm.Name + "/" + tm.ExpirationDate + "/" + tm.BatchNumber + "/" + strconv.Itoa(tm.Count) + "、")
		}
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.drug_update",
			TranKey: "operation_log.drug_update.nameDateNumberCount",
			New: models.OperationLogField{
				Type:  2,
				Value: bf.String(),
			},
			Old: models.OperationLogField{
				Type:  2,
				Value: "",
			},
		})
	}

	marks := []models.Mark{}
	err := tools.SaveOperation(ctx, sctx, module, OID, types, OperationLogFieldGroups, marks, OID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// 项目设置-项目通知
func insertProjectNoticeLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, oldProjectNotice models.ProjectNotice, newProjectNotice models.ProjectNotice, operatorID primitive.ObjectID) error {

	var project models.Project
	_ = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": newProjectNotice.ProjectID}).Decode(&project)

	envName := ""
	env, a := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == newProjectNotice.EnvID
	})
	if a {
		envName = env.Name
	}

	OperationLogFieldGroups := make([]models.OperationLogFieldGroup, 0)
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_notice",
		TranKey: "operation_log.project_notice.envName",
		Old: models.OperationLogField{
			Type:  2,
			Value: nil,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: envName,
		},
	})

	oldNoticeRuleList := make([]models.NoticeRule, 0)
	if oldProjectNotice.NoticeRule != nil && len(oldProjectNotice.NoticeRule) > 0 {
		oldNoticeRuleList = oldProjectNotice.NoticeRule
	}

	newNoticeRuleList := make([]models.NoticeRule, 0)
	if newProjectNotice.NoticeRule != nil && len(newProjectNotice.NoticeRule) > 0 {
		newNoticeRuleList = newProjectNotice.NoticeRule
	}

	// 创建两个map，用于快速查找
	oldNoticeRuleMap := make(map[primitive.ObjectID]models.NoticeRule)
	for _, obj := range oldNoticeRuleList {
		oldNoticeRuleMap[obj.ID] = obj
	}
	newNoticeRuleMap := make(map[primitive.ObjectID]models.NoticeRule)
	for _, obj := range newNoticeRuleList {
		newNoticeRuleMap[obj.ID] = obj
	}

	addNoticeRuleZhList := make([]string, 0)
	addNoticeRuleEnList := make([]string, 0)
	// 找出新增的元素
	for id, newObj := range newNoticeRuleMap {
		if _, ok := oldNoticeRuleMap[id]; !ok {
			//fmt.Printf("新增对象: %v\n", newObj)
			addNoticeRuleZh := "最小随访窗口期前" + strconv.Itoa(newObj.Date) + "天，" + newObj.Time + "推送，选择" + newObj.Template
			addNoticeRuleZhList = append(addNoticeRuleZhList, addNoticeRuleZh)
			addNoticeRuleEn := "Minimum visit window period" + strconv.Itoa(newObj.Date) + "Day," + newObj.Time + "Push, Select" + newObj.Template
			addNoticeRuleEnList = append(addNoticeRuleEnList, addNoticeRuleEn)
		}
	}

	deleteNoticeRuleZhList := make([]string, 0)
	deleteNoticeRuleEnList := make([]string, 0)
	// 找出删除的元素
	for id, oldObj := range oldNoticeRuleMap {
		if _, ok := newNoticeRuleMap[id]; !ok {
			//fmt.Printf("删除对象: %v\n", oldObj)
			deleteNoticeRuleZh := "最小随访窗口期前" + strconv.Itoa(oldObj.Date) + "天，" + oldObj.Time + "推送，选择" + oldObj.Template
			deleteNoticeRuleZhList = append(deleteNoticeRuleZhList, deleteNoticeRuleZh)
			deleteNoticeRuleEn := "Minimum visit window period" + strconv.Itoa(oldObj.Date) + "Day," + oldObj.Time + "Push, Select" + oldObj.Template
			deleteNoticeRuleEnList = append(deleteNoticeRuleEnList, deleteNoticeRuleEn)
		}
	}

	updateNoticeRuleZhList := make([]string, 0)
	updateNoticeRuleEnList := make([]string, 0)
	// 找出修改的元素
	for id, newObj := range newNoticeRuleMap {
		if oldObj, ok := oldNoticeRuleMap[id]; ok {
			//fmt.Printf("修改对象: %v\n", oldObj)
			if newObj.Date != oldObj.Date || newObj.Time != oldObj.Time || newObj.Template != oldObj.Template {
				updateNoticeRuleZh := "最小随访窗口期前" + strconv.Itoa(newObj.Date) + "天，" + newObj.Time + "推送，选择" + newObj.Template
				updateNoticeRuleZhList = append(updateNoticeRuleZhList, updateNoticeRuleZh)
				updateNoticeRuleEn := "Minimum visit window period" + strconv.Itoa(newObj.Date) + "Day," + newObj.Time + "Push, Select" + newObj.Template
				updateNoticeRuleEnList = append(updateNoticeRuleEnList, updateNoticeRuleEn)
			}
		}
	}

	oldRoleUserList := make([]models.RoleUsers, 0)
	if oldProjectNotice.RoleUser != nil && len(oldProjectNotice.RoleUser) > 0 {
		oldRoleUserList = oldProjectNotice.RoleUser
	}

	newRoleUserList := make([]models.RoleUsers, 0)
	if newProjectNotice.RoleUser != nil && len(newProjectNotice.RoleUser) > 0 {
		newRoleUserList = newProjectNotice.RoleUser
	}

	// 创建两个map，用于快速查找
	oldRoleUserMap := make(map[primitive.ObjectID]models.RoleUsers)
	for _, obj := range oldRoleUserList {
		oldRoleUserMap[obj.RoleID] = obj
	}
	newRoleUserMap := make(map[primitive.ObjectID]models.RoleUsers)
	for _, obj := range newRoleUserList {
		newRoleUserMap[obj.RoleID] = obj
	}

	addRoleUserList := make([]string, 0)
	// 找出新增的元素
	for id, newObj := range newRoleUserMap {
		if _, ok := oldRoleUserMap[id]; !ok {
			//fmt.Printf("新增对象: %v\n", newObj)
			if newObj.Users != nil && len(newObj.Users) > 0 {
				var projectRolePermission models.ProjectRolePermission
				err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": newObj.RoleID}).Decode(&projectRolePermission)
				if err != nil {
					return errors.WithStack(err)
				}

				emailList := make([]string, 0)
				for _, userId := range newObj.Users {
					var user models.User
					err = tools.Database.Collection("user").FindOne(nil, bson.M{"_id": userId}).Decode(&user)
					if err != nil && err != mongo.ErrNoDocuments {
						return errors.WithStack(err)
					}
					emailList = append(emailList, user.Email)
				}
				addRoleUser := projectRolePermission.Name + "[" + strings.Join(emailList, ",") + "]"
				addRoleUserList = append(addRoleUserList, addRoleUser)
			}
		}
	}

	deleteRoleUserList := make([]string, 0)
	// 找出删除的元素
	for id, oldObj := range oldRoleUserMap {
		if _, ok := newRoleUserMap[id]; !ok {
			//fmt.Printf("删除对象: %v\n", oldObj)
			if oldObj.Users != nil && len(oldObj.Users) > 0 {
				var projectRolePermission models.ProjectRolePermission
				err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": oldObj.RoleID}).Decode(&projectRolePermission)
				if err != nil {
					return errors.WithStack(err)
				}

				emailList := make([]string, 0)
				for _, userId := range oldObj.Users {
					var user models.User
					err = tools.Database.Collection("user").FindOne(nil, bson.M{"_id": userId}).Decode(&user)
					if err != nil && err != mongo.ErrNoDocuments {
						return errors.WithStack(err)
					}
					emailList = append(emailList, user.Email)
				}
				deleteRoleUser := projectRolePermission.Name + "[" + strings.Join(emailList, ",") + "]"
				deleteRoleUserList = append(deleteRoleUserList, deleteRoleUser)
			}
		}
	}

	updateRoleUserList := make([]string, 0)
	// 找出修改的元素
	for id, newObj := range newRoleUserMap {
		if oldObj, ok := oldRoleUserMap[id]; ok {
			//fmt.Printf("修改对象: %v\n", oldObj)

			oldUserList := oldObj.Users
			newUserList := newObj.Users

			// 对数组进行排序
			sort.Slice(oldUserList, func(i, j int) bool {
				return oldUserList[i].Hex() < oldUserList[j].Hex()
			})

			sort.Slice(newUserList, func(i, j int) bool {
				return newUserList[i].Hex() < newUserList[j].Hex()
			})

			if !reflect.DeepEqual(oldUserList, newUserList) {
				if newObj.Users != nil && len(newObj.Users) > 0 {
					var projectRolePermission models.ProjectRolePermission
					err := tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": newObj.RoleID}).Decode(&projectRolePermission)
					if err != nil {
						return errors.WithStack(err)
					}

					emailList := make([]string, 0)
					for _, userId := range oldObj.Users {
						var user models.User
						err = tools.Database.Collection("user").FindOne(nil, bson.M{"_id": userId}).Decode(&user)
						if err != nil && err != mongo.ErrNoDocuments {
							return errors.WithStack(err)
						}
						emailList = append(emailList, user.Email)
					}
					updateRoleUser := projectRolePermission.Name + "[" + strings.Join(emailList, ",") + "]"
					updateRoleUserList = append(updateRoleUserList, updateRoleUser)
				}
			}
		}
	}

	if (len(addNoticeRuleZhList) > 0 && len(addNoticeRuleEnList) > 0) || len(addRoleUserList) > 0 {
		addOperationLogFieldGroups := OperationLogFieldGroups
		if len(addNoticeRuleZhList) > 0 && len(addNoticeRuleEnList) > 0 {
			addOperationLogFieldGroups = append(addOperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.project_notice",
				TranKey: "operation_log.project_notice.notice_rule",
				Old: models.OperationLogField{
					Type:    30,
					Value:   nil,
					ENValue: nil,
				},
				New: models.OperationLogField{
					Type:    30,
					Value:   strings.Join(addNoticeRuleZhList, ","),
					ENValue: strings.Join(addNoticeRuleEnList, ","),
				},
			})
		}
		if len(addRoleUserList) > 0 {
			addOperationLogFieldGroups = append(addOperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.project_notice",
				TranKey: "operation_log.project_notice.notice_targets",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: strings.Join(addRoleUserList, ","),
				},
			})
		}
		marks := []models.Mark{}
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.project_notice", OID, 1, addOperationLogFieldGroups, marks, operatorID)
		if err != nil {
			return errors.WithStack(err)
		}

	}

	if (len(deleteNoticeRuleZhList) > 0 && len(deleteNoticeRuleEnList) > 0) || len(deleteRoleUserList) > 0 {
		deleteOperationLogFieldGroups := OperationLogFieldGroups
		if len(deleteNoticeRuleZhList) > 0 && len(deleteNoticeRuleEnList) > 0 {
			deleteOperationLogFieldGroups = append(deleteOperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.project_notice",
				TranKey: "operation_log.project_notice.notice_rule",
				Old: models.OperationLogField{
					Type:    30,
					Value:   strings.Join(deleteNoticeRuleZhList, ","),
					ENValue: strings.Join(deleteNoticeRuleEnList, ","),
				},
				New: models.OperationLogField{
					Type:    30,
					Value:   nil,
					ENValue: nil,
				},
			})
		}
		if len(deleteRoleUserList) > 0 {
			deleteOperationLogFieldGroups = append(deleteOperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.project_notice",
				TranKey: "operation_log.project_notice.notice_targets",
				Old: models.OperationLogField{
					Type:  2,
					Value: strings.Join(deleteRoleUserList, ","),
				},
				New: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
			})
		}
		marks := []models.Mark{}
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.project_notice", OID, 3, deleteOperationLogFieldGroups, marks, operatorID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	if (len(updateNoticeRuleZhList) > 0 && len(updateNoticeRuleEnList) > 0) || len(updateRoleUserList) > 0 {
		updateOperationLogFieldGroups := OperationLogFieldGroups
		if len(updateNoticeRuleZhList) > 0 && len(updateNoticeRuleEnList) > 0 {
			updateOperationLogFieldGroups = append(updateOperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.project_notice",
				TranKey: "operation_log.project_notice.notice_rule",
				Old: models.OperationLogField{
					Type:    30,
					Value:   nil,
					ENValue: nil,
				},
				New: models.OperationLogField{
					Type:    30,
					Value:   strings.Join(updateNoticeRuleZhList, ","),
					ENValue: strings.Join(updateNoticeRuleEnList, ","),
				},
			})
		}
		if len(updateRoleUserList) > 0 {
			updateOperationLogFieldGroups = append(updateOperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.project_notice",
				TranKey: "operation_log.project_notice.notice_targets",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: strings.Join(updateRoleUserList, ","),
				},
			})
		}
		marks := []models.Mark{}
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.project_notice", OID, 2, updateOperationLogFieldGroups, marks, operatorID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func InsertStoreBatchGroupLog(ctx *gin.Context, sctx mongo.SessionContext, OID primitive.ObjectID, types int, old []models.DepotBatchGroupAlarm, new []models.DepotBatchGroupAlarm, operID primitive.ObjectID, envOID primitive.ObjectID, copyEnv string) error {
	// 保存项目日志
	OperationLogFieldGroups := make([]models.OperationLogFieldGroup, 0)
	if types != 3 {

		if copyEnv != "" {
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.drug_configure_setting",
				TranKey: "operation_log.drug_configure_setting.env_name",
				Old: models.OperationLogField{
					Type:  2,
					Value: nil,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: copyEnv,
				},
			})
		}

		IDs := slice.Map(old, func(index int, item models.DepotBatchGroupAlarm) primitive.ObjectID {
			return item.Depot
		})
		newIDs := slice.Map(new, func(index int, item models.DepotBatchGroupAlarm) primitive.ObjectID {
			return item.Depot
		})
		IDs = append(IDs, newIDs...)
		storehouseInfos, err := database.GetProjectStorehouseInfo(IDs)
		if err != nil {
			return err
		}
		mapStore := map[primitive.ObjectID]models.ProjectStorehouseInfo{}
		for _, info := range storehouseInfos {
			mapStore[info.ID] = info
		}
		// 旧库房对比

		depotLength := len(new) - len(old)
		if depotLength > 0 {
			for _ = range depotLength {
				old = append(old, models.DepotBatchGroupAlarm{})
			}
		}

		for _, alarm := range old {
			depotP, ok := slice.Find(new, func(index int, item models.DepotBatchGroupAlarm) bool {
				return item.Depot == alarm.Depot
			})

			depot := models.DepotBatchGroupAlarm{}
			//  库房
			if ok { // 新的数据有 判断是否更新
				depot = *depotP
			}

			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.module.updateBatch",
				TranKey: "operation_log.updateBatch.depot",
				Old: models.OperationLogField{
					Type:  2,
					Value: mapStore[alarm.Depot].Storehouse.Name,
					Mark:  1,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: mapStore[depot.Depot].Storehouse.Name,
					Mark:  1,
				},
			})

			tmpOperationLogFieldGroups := batchGroupSiteAndDepot(alarm.Info, depot.Info)
			OperationLogFieldGroups = append(OperationLogFieldGroups, tmpOperationLogFieldGroups...)
			//for i, groupAlarm := range alarm.Info {
			//	// 批次
			//
			//	newTmpGroup := models.BatchGroupAlarm{}
			//	if len(depot.Info) >= i+1 {
			//		newTmpGroup = depot.Info[i]
			//	}
			//
			//	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			//		Key:     "operation_log.updateBatch",
			//		TranKey: "operation_log.updateBatch.batch",
			//		Old: models.OperationLogField{
			//			Type:  2,
			//			Value: groupAlarm.Batch,
			//			Mark:  1,
			//		},
			//		New: models.OperationLogField{
			//			Type:  2,
			//			Value: newTmpGroup.Batch,
			//			Mark:  1,
			//		},
			//	})
			//	groupLength := len(newTmpGroup.Info) - len(groupAlarm.Info)
			//	if groupLength > 0 {
			//		for _ = range groupLength {
			//			groupAlarm.Info = append(groupAlarm.Info, models.GroupAlarm{})
			//		}
			//	}
			//
			//	for j, info := range groupAlarm.Info {
			//		tmpInfo := models.GroupAlarm{}
			//		if len(newTmpGroup.Info) >= j+1 {
			//			tmpInfo = newTmpGroup.Info[j]
			//		}
			//		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			//			Key:     "operation_log.updateBatch",
			//			TranKey: "operation_log.updateBatch.group",
			//			Old: models.OperationLogField{
			//				Type:  2,
			//				Value: info.Group,
			//				Mark:  1,
			//			},
			//			New: models.OperationLogField{
			//				Type:  2,
			//				Value: tmpInfo.Group,
			//				Mark:  1,
			//			},
			//		})
			//
			//		oldWarn := ""
			//		newWarn := ""
			//		if info.Warn != 0 {
			//			oldWarn = convertor.ToString(info.Warn)
			//		}
			//		if tmpInfo.Warn != 0 {
			//			newWarn = convertor.ToString(tmpInfo.Warn)
			//		}
			//
			//		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			//			Key:     "operation_log.updateBatch",
			//			TranKey: "operation_log.updateBatch.warn",
			//			Old: models.OperationLogField{
			//				Type:  2,
			//				Value: oldWarn,
			//				Mark:  1,
			//			},
			//			New: models.OperationLogField{
			//				Type:  2,
			//				Value: newWarn,
			//				Mark:  1,
			//			},
			//		})
			//
			//		oldCapacity := ""
			//		newCapacity := ""
			//		if info.Capacity != 0 {
			//			oldCapacity = convertor.ToString(info.Capacity)
			//		}
			//		if tmpInfo.Capacity != 0 {
			//			newCapacity = convertor.ToString(tmpInfo.Capacity)
			//		}
			//		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			//			Key:     "operation_log.updateBatch",
			//			TranKey: "operation_log.updateBatch.capacity",
			//			Old: models.OperationLogField{
			//				Type:  2,
			//				Value: oldCapacity,
			//				Mark:  1,
			//			},
			//			New: models.OperationLogField{
			//				Type:  2,
			//				Value: newCapacity,
			//				Mark:  1,
			//			},
			//		})
			//
			//	}
			//}
		}

	}
	marks := []models.Mark{}
	if copyEnv != "" && len(OperationLogFieldGroups) == 2 {
		return nil
	} else {
		err := tools.SaveOperation(ctx, sctx, "operation_log.module.updateBatch", OID, types, OperationLogFieldGroups, marks, operID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func batchGroupSiteAndDepot(old, new []models.BatchGroupAlarm) []models.OperationLogFieldGroup {
	batchLength := len(new) - len(old)
	if batchLength > 0 {
		for _ = range batchLength {
			old = append(old, models.BatchGroupAlarm{})
		}
	}
	OperationLogFieldGroups := []models.OperationLogFieldGroup{}
	for i, groupAlarm := range old {
		// 批次

		newTmpGroup := models.BatchGroupAlarm{}
		if len(new) >= i+1 {
			newTmpGroup = new[i]
		}

		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.updateBatch",
			TranKey: "operation_log.updateBatch.batchNo",
			Old: models.OperationLogField{
				Type:  2,
				Value: groupAlarm.Batch,
				Mark:  1,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: newTmpGroup.Batch,
				Mark:  1,
			},
		})
		groupLength := len(newTmpGroup.Info) - len(groupAlarm.Info)
		if groupLength > 0 {
			for _ = range groupLength {
				groupAlarm.Info = append(groupAlarm.Info, models.GroupAlarm{})
			}
		}

		for j, info := range groupAlarm.Info {
			tmpInfo := models.GroupAlarm{}
			if len(newTmpGroup.Info) >= j+1 {
				tmpInfo = newTmpGroup.Info[j]
			}
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.updateBatch",
				TranKey: "operation_log.updateBatch.group",
				Old: models.OperationLogField{
					Type:  2,
					Value: info.Group,
					Mark:  1,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: tmpInfo.Group,
					Mark:  1,
				},
			})

			oldWarn := ""
			newWarn := ""
			if info.Warn != 0 {
				oldWarn = convertor.ToString(info.Warn)
			}
			if tmpInfo.Warn != 0 {
				newWarn = convertor.ToString(tmpInfo.Warn)
			}

			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.updateBatch",
				TranKey: "operation_log.updateBatch.warn",
				Old: models.OperationLogField{
					Type:  2,
					Value: oldWarn,
					Mark:  1,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: newWarn,
					Mark:  1,
				},
			})

			oldCapacity := ""
			newCapacity := ""
			if info.Capacity != 0 {
				oldCapacity = convertor.ToString(info.Capacity)
			}
			if tmpInfo.Capacity != 0 {
				newCapacity = convertor.ToString(tmpInfo.Capacity)
			}
			OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
				Key:     "operation_log.updateBatch",
				TranKey: "operation_log.updateBatch.capacity",
				Old: models.OperationLogField{
					Type:  2,
					Value: oldCapacity,
					Mark:  1,
				},
				New: models.OperationLogField{
					Type:  2,
					Value: newCapacity,
					Mark:  1,
				},
			})

		}
	}
	return OperationLogFieldGroups
}

// insertMultiLanguageLog 多语言轨迹
func insertMultiLanguageLog(ctx *gin.Context, sctx mongo.SessionContext, types int, old models.ProjectMultiLanguage, new models.ProjectMultiLanguage, OID primitive.ObjectID, operatorID primitive.ObjectID) error {

	project := models.Project{}
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": new.ProjectID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	OperationLogFieldGroups := make([]models.OperationLogFieldGroup, 0)
	//项目
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_multi_language",
		TranKey: "operation_log.project_multi_language.projectName",
		Old: models.OperationLogField{
			Type:  2,
			Value: nil,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: project.ProjectInfo.Number,
		},
	})
	//语言
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_multi_language",
		TranKey: "operation_log.project_multi_language.language",
		Old: models.OperationLogField{
			Type:  2,
			Value: nil,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.Language,
		},
	})
	//启用状态
	if old.Status != new.Status {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_multi_language",
			TranKey: "operation_log.project_multi_language.status",
			Old: models.OperationLogField{
				Type:  1,
				Value: old.Status,
			},
			New: models.OperationLogField{
				Type:  1,
				Value: new.Status,
			},
		})
	}
	//启用系统库
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_multi_language",
		TranKey: "operation_log.project_multi_language.sharedSystemLibrary",
		Old: models.OperationLogField{
			Type:  1,
			Value: old.SharedSystemLibrary,
		},
		New: models.OperationLogField{
			Type:  1,
			Value: new.SharedSystemLibrary,
		},
	})

	if old.Language != new.Language || old.Status != new.Status || old.SharedSystemLibrary != new.SharedSystemLibrary {
		marks := []models.Mark{}
		err = tools.SaveOperation(ctx, sctx, "operation_log.module.project_multi_language", OID, types, OperationLogFieldGroups, marks, operatorID)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

// insertMultiLanguageTranslateLog 多语言翻译轨迹
func insertMultiLanguageTranslateLog(ctx *gin.Context, sctx mongo.SessionContext, types int, old models.ProjectMultiLanguageTranslateVo, new models.ProjectMultiLanguageTranslateVo, OID primitive.ObjectID, operatorID primitive.ObjectID) error {

	project := models.Project{}
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": new.ProjectID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	envName := ""
	cohortName := ""
	if new.EnvironmentID != primitive.NilObjectID {
		if project.Environments != nil && len(project.Environments) > 0 {
			for _, environment := range project.Environments {
				if environment.ID == new.EnvironmentID {
					envName = environment.Name
					if project.ProjectInfo.Type != 1 && new.CohortID != primitive.NilObjectID {
						if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
							for _, cohort := range environment.Cohorts {
								if cohort.ID == new.CohortID {
									cohortName = cohort.Name
								}
							}
						}
					}
				}
			}
		}
	}

	OperationLogFieldGroups := make([]models.OperationLogFieldGroup, 0)
	//项目
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_multi_language_translate",
		TranKey: "operation_log.project_multi_language_translate.projectName",
		Old: models.OperationLogField{
			Type:  2,
			Value: nil,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: project.ProjectInfo.Number,
		},
	})
	//语言库
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_multi_language_translate",
		TranKey: "operation_log.project_multi_language_translate.languageLibrary",
		Old: models.OperationLogField{
			Type:    30,
			Value:   nil,
			ENValue: nil,
		},
		New: models.OperationLogField{
			Type:    30,
			Value:   new.LanguageLibraryValueZh,
			ENValue: new.LanguageLibraryValueEn,
		},
	})
	//页面路径
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_multi_language_translate",
		TranKey: "operation_log.project_multi_language_translate.pagePath",
		Old: models.OperationLogField{
			Type:    30,
			Value:   nil,
			ENValue: nil,
		},
		New: models.OperationLogField{
			Type:    30,
			Value:   new.PagePathValueZh,
			ENValue: new.PagePathValueEn,
		},
	})
	if project.ProjectInfo.Type != 1 && envName != "" {
		//环境
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_multi_language_translate",
			TranKey: "operation_log.project_multi_language_translate.envName",
			Old: models.OperationLogField{
				Type:  2,
				Value: nil,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: envName,
			},
		})
		//群组、阶段
		if cohortName != "" {
			if project.ProjectInfo.Type == 2 {
				//群组
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.project_multi_language_translate",
					TranKey: "operation_log.project_multi_language_translate.cohortName",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: cohortName,
					},
				})
			} else if project.ProjectInfo.Type == 3 {
				//阶段
				OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
					Key:     "operation_log.project_multi_language_translate",
					TranKey: "operation_log.project_multi_language_translate.stageName",
					Old: models.OperationLogField{
						Type:  2,
						Value: nil,
					},
					New: models.OperationLogField{
						Type:  2,
						Value: cohortName,
					},
				})
			}
		}
	}
	//类型
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_multi_language_translate",
		TranKey: "operation_log.project_multi_language_translate.type",
		Old: models.OperationLogField{
			Type:    30,
			Value:   nil,
			ENValue: nil,
		},
		New: models.OperationLogField{
			Type:    30,
			Value:   new.TypeValueZh,
			ENValue: new.TypeValueEn,
		},
	})
	//Key
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_multi_language_translate",
		TranKey: "operation_log.project_multi_language_translate.key",
		Old: models.OperationLogField{
			Type:  2,
			Value: nil,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: new.Key,
		},
	})
	//名称
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_multi_language_translate",
		TranKey: "operation_log.project_multi_language_translate.name",
		Old: models.OperationLogField{
			Type:    30,
			Value:   nil,
			ENValue: nil,
		},
		New: models.OperationLogField{
			Type:    30,
			Value:   new.NameValueZh,
			ENValue: new.NameValueEn,
		},
	})
	//韩语、日语等
	if old.TranslateValue != new.TranslateValue {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.project_multi_language_translate",
			TranKey: "operation_log.project_multi_language_translate.label",
			Old: models.OperationLogField{
				Type:  2,
				Value: old.TranslateValue,
			},
			New: models.OperationLogField{
				Type:  2,
				Value: new.TranslateValue,
			},
		})
	}

	if old.TranslateValue != new.TranslateValue {
		marks := []models.Mark{}
		marks = append(marks, models.Mark{
			Label: "operation_log.module.project_multi_language_translate.language",
			Value: new.LanguageName,
			Blind: false,
		})
		err = tools.SaveOperation(ctx, sctx, "operation_log.module.project_multi_language_translate", OID, types, OperationLogFieldGroups, marks, operatorID)
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

// insertMultiLanguageBatchImportLog 批量导入多语言翻译轨迹
func insertMultiLanguageBatchImportLog(ctx *gin.Context, sctx mongo.SessionContext, types int, projectOID primitive.ObjectID, filename string, languageName string, OID primitive.ObjectID, operatorID primitive.ObjectID) error {
	project := models.Project{}
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}

	OperationLogFieldGroups := make([]models.OperationLogFieldGroup, 0)
	//项目
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_multi_language_batch_upload",
		TranKey: "operation_log.project_multi_language_batch_upload.projectName",
		Old: models.OperationLogField{
			Type:  2,
			Value: nil,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: project.ProjectInfo.Number,
		},
	})

	// 批量导入文件名称
	OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
		Key:     "operation_log.project_multi_language_batch_upload",
		TranKey: "operation_log.project_multi_language_batch_upload.filename",
		Old: models.OperationLogField{
			Type:  2,
			Value: nil,
		},
		New: models.OperationLogField{
			Type:  2,
			Value: filename,
		},
	})

	marks := []models.Mark{}
	marks = append(marks, models.Mark{
		Label: "operation_log.module.project_multi_language_batch_upload.language",
		Value: languageName,
		Blind: false,
	})
	err = tools.SaveOperation(ctx, sctx, "operation_log.module.project_multi_language_batch_upload", OID, types, OperationLogFieldGroups, marks, operatorID)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}
