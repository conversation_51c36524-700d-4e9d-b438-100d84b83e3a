package service

import (
	"clinflash-irt/database"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/task"
	"clinflash-irt/tools"
	"context"
	"fmt"
	"github.com/gin-gonic/gin/binding"
	"math"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/convertor"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DispensingService struct {
	medicineOrderService MedicineOrderService
}

func (s *DispensingService) GetVisit(ctx *gin.Context, customerID string, projectID string, envID string, cohortID string, subjectId string, status string, roleId string) (interface{}, error) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	subjectOID, _ := primitive.ObjectIDFromHex(subjectId)
	match := bson.M{
		"project_id":  projectOID,
		"env_id":      envOID,
		"customer_id": customerOID,
	}
	if cohortID != "" && !cohortOID.IsZero() {
		match = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	var attribute models.Attribute
	err := tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visitCycle)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	index := 0
	hasRandom := false
	for i, info := range visitCycle.Infos {
		if info.Random {
			index = i
			hasRandom = true
		}
	}
	var visits []models.VisitCycleInfo
	//随即前访视

	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	group := "N/A"
	if subject.Group == "" {
		if index != 0 { //0:index 访视前
			visits = visitCycle.Infos[0:index]
		} else {
			if !hasRandom { // 无随机访视
				visits = visitCycle.Infos[index:]
			}
		}
	}
	//随机 随机后访视
	if status == "3" || status == "6" {
		group = subject.Group
		visits = visitCycle.Infos[index:len(visitCycle.Infos)]

	}
	var visitID bson.A
	var data []map[string]interface{}

	for _, visit := range visits {
		visitID = append(visitID, visit.ID)
	}
	if len(visits) != 0 {
		lookup := bson.M{
			"from": "visit_cycle",
			"let": bson.M{
				"visit_cycles": "$configures.visit_cycles",
				"env_id":       "$env_id",
				"cohort_id":    "$cohort_id",
			},
			"pipeline": bson.A{
				bson.M{"$unwind": "$infos"},
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$infos.id", "$$visit_cycles"}}}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$$env_id"}}}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$cohort_id", "$$cohort_id"}}}},
			},
			"as": "visit",
		}
		blind := false
		isBlindedRole, err := tools.IsBlindedRole(roleId)
		if err != nil {
			return nil, err
		}
		if isBlindedRole {
			blind = true
		}
		project := bson.M{}
		project = bson.M{
			"_id":                                        0,
			"configures.id":                              1,
			"configures.label":                           1,
			"configures.customer_calculation":            1,
			"configures.open_setting":                    1,
			"configures.group":                           1,
			"configures.values.dispensing_number":        1,
			"configures.values.drug_spec":                1,
			"configures.values.drugname":                 1,
			"configures.values.custom_dispensing_number": 1,
			"configures.values.is_count":                 1,
			"configures.values.is_other":                 1,
			"configures.values.automatic_recode":         1,
			"configures.values.label":                    1,
			"visit_info":                                 "$visit.infos",
		}

		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$unwind", Value: "$configures"}},
			{{Key: "$unwind", Value: "$configures.visit_cycles"}},
			{{Key: "$match", Value: bson.M{"configures.group": group, "configures.visit_cycles": bson.M{"$in": visitID}}}},
			{{Key: "$lookup", Value: lookup}},
			{{Key: "$unwind", Value: "$visit"}},
			{{Key: "$project", Value: project}},
			{{Key: "$sort", Value: bson.D{{"visit_info.id", 1}}}},
		}
		cursor, err := tools.Database.Collection("drug_configure").Aggregate(nil, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &data)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		for index, value := range data {
			for i, item := range value["configures"].(map[string]interface{})["values"].(primitive.A) {
				name := item.(map[string]interface{})["drugname"].(string)
				encrypted, randomSalt := tools.Encrypt(name)
				// isOpenDrug, _ := tools.IsOpenDrug(envOID, cohortOID, name)
				// isOtherDrug, _ := tools.IsOtherDrug(envOID, name)
				// if !isOpenDrug && !isOtherDrug {
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, name)
				if isBlindedDrug && blind {
					data[index]["configures"].(map[string]interface{})["values"].(primitive.A)[i].(map[string]interface{})["drugname"] = tools.BlindData
				}
				data[index]["configures"].(map[string]interface{})["values"].(primitive.A)[i].(map[string]interface{})["salt"] = randomSalt
				data[index]["configures"].(map[string]interface{})["values"].(primitive.A)[i].(map[string]interface{})["saltName"] = encrypted

				// 把自定义数量转成selectOption
				custom_dispensing_number := item.(map[string]interface{})["custom_dispensing_number"]
				if custom_dispensing_number != nil && custom_dispensing_number.(string) != "" {
					data[index]["configures"].(map[string]interface{})["values"].(primitive.A)[i].(map[string]interface{})["custom_dispensing_number"] = customDispensingNumberToSelectOption(item.(map[string]interface{})["custom_dispensing_number"].(string))
				} else {
					data[index]["configures"].(map[string]interface{})["values"].(primitive.A)[i].(map[string]interface{})["custom_dispensing_number"] = []int32{item.(map[string]interface{})["dispensing_number"].(int32)}
				}

			}
			if blind {
				data[index]["configures"].(map[string]interface{})["group"] = tools.BlindData
				data[index]["visit_info"].(map[string]interface{})["group"] = tools.BlindData
			}
		}
	}
	return data, nil
}

func (s *DispensingService) AddDispensingVisit(ctx *gin.Context, data map[string]interface{}, sign int, edcUserName string) (models.RemoteSubjectDispensing, []models.ResDispensingInfo, error) {
	var remoteSubjectDispensing models.RemoteSubjectDispensing
	var resOrderInfo []models.ResDispensingInfo
	var subject models.Subject
	var dispensing models.Dispensing
	var formValue *models.FormValue
	var doseLevel models.DoseLevel
	var visitJudgment models.VisitJudgment
	// 查询访视周期信息
	projectOID, _ := primitive.ObjectIDFromHex(data["project_id"].(string))
	envOID, _ := primitive.ObjectIDFromHex(data["env_id"].(string))
	customerOID, _ := primitive.ObjectIDFromHex(data["customer_id"].(string))
	cohortOID := primitive.NilObjectID
	dispensingOID := primitive.NewObjectID()
	visitSign := true
	reissue := 0
	serialNumber := -1
	subjectOID, _ := primitive.ObjectIDFromHex(data["subject_id"].(string))
	visitOID, _ := primitive.ObjectIDFromHex(data["visit_id"].(string))
	//visitDrugOID := primitive.NilObjectID
	visitLabels := []map[string]interface{}{}
	if data["visit_labels"] != nil {
		for _, item := range data["visit_labels"].([]interface{}) {
			visitLabels = append(visitLabels, item.(map[string]interface{}))
		}
		//visitDrugOID, _ = primitive.ObjectIDFromHex(data["visit_label_id"].(string))
	}
	sendType := 0
	remark := ""
	if data["remark"] != nil {
		remark = data["remark"].(string)
	}
	if data["send_type"] != nil {
		sendType = int(data["send_type"].(float64))
	}
	var logisticsInfo models.LogisticsInfo
	var formulaInfo models.FormulaInfo
	otherMedicineCount := []primitive.ObjectID{}
	if data["logistics"] != nil {
		logisticsInfo.Logistics = data["logistics"].(string)
		if data["other"] != nil {
			logisticsInfo.Other = data["other"].(string)
		}
	}
	if data["number"] != nil {
		logisticsInfo.Number = data["number"].(string)
	}
	var medicineInfo []interface{}
	if data["open_setting"] != nil {
		medicineInfo = data["open_setting"].([]interface{})
	}

	var formulaMedicine []interface{}
	if data["formula_medicine"] != nil && len(data["formula_medicine"].([]interface{})) > 0 {
		formulaMedicine = data["formula_medicine"].([]interface{})
		formulaMedicine = slice.Filter(formulaMedicine, func(index int, item interface{}) bool {
			return item.(map[string]interface{})["count"] != nil && int(item.(map[string]interface{})["count"].(float64)) != 0
		})
	}

	if data["formula_age"] != nil {
		value := data["formula_age"].(string)
		value = value[:10]
		formulaInfo.Age = &value
	}
	if data["formula_height"] != nil {
		value, _ := convertor.ToFloat(data["formula_height"].(float64))
		formulaInfo.Height = &value
	}
	if data["formula_weight"] != nil {
		value, _ := convertor.ToFloat(data["formula_weight"].(float64))
		formulaInfo.Weight = &value
	}

	var customerFormulas []models.CustomerFormula
	if data["form"] != nil {
		for key, value := range data["form"].(map[string]interface{}) {
			if value != nil {
				customerFormulas = append(customerFormulas, models.CustomerFormula{
					Key:   key,
					Value: value.(float64),
				})
			}

		}
	}

	var useFormulas []string

	if data["useFormulas"] != nil {
		for _, value := range data["useFormulas"].([]interface{}) {
			useFormulas = append(useFormulas, value.(string))
		}
	}
	for _, item := range formulaMedicine {
		info := item.(map[string]interface{})["info"].(map[string]interface{})
		if item.(map[string]interface{})["count"] != nil {
			itemInfo := map[string]interface{}{
				"count":        item.(map[string]interface{})["count"],
				"is_other":     info["is_other"],
				"name":         info["name"],
				"salt":         info["salt"],
				"saltName":     info["saltName"],
				"spec":         info["spec"],
				"useFormula":   item.(map[string]interface{})["useFormula"],
				"formulaCount": item.(map[string]interface{})["formulaCount"],
				"open_setting": 3,
			}
			if item.(map[string]interface{})["dtp"] != nil {
				itemInfo["dtp"] = item.(map[string]interface{})["dtp"]
			}
			medicineInfo = append(medicineInfo, itemInfo)

			medicineName := info["name"].(string)
			if info["saltName"] != nil {
				medicineName = tools.Decrypt(info["saltName"].(string), info["salt"].(string))
			}
			weight := item.(map[string]interface{})["weight"].(float64)
			formulaInfo.FormulaWeight = append(formulaInfo.FormulaWeight, models.FormulaWeight{
				Weight: &weight,
				Name:   medicineName,
			})
		}

	}

	if data["cohort_id"] != nil {
		cohortOID, _ = primitive.ObjectIDFromHex(data["cohort_id"].(string))
	}

	visitCycles, visitCycle, visitInfo, err := getVisitInfos(envOID, cohortOID, visitOID)
	if err != nil {
		return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, errors.WithStack(err)
	}
	roleID := ""
	isBlindedRole := false
	isBlindedRoomRole := false
	if sign == 0 {
		roleID = data["role_id"].(string)
		var err error
		isBlindedRole, err = tools.IsBlindedRole(roleID)
		if err != nil {
			return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, errors.WithStack(err)
		}
		isBlindedRoomRole, err = tools.IsBlindedRoomRole(roleID)
		if err != nil {
			return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, errors.WithStack(err)
		}
	}

	match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortOID != primitive.NilObjectID {
		match = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}

	var dispensingMedicine []models.DispensingMedicine
	var otherDispensingMedicine []models.OtherDispensingMedicine
	var othersDispensingMedicine []models.OthersDispensingMedicine
	var room string
	projectFilter := bson.M{"_id": projectOID}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
	if err != nil {
		return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, errors.WithStack(err)
	}
	var form models.Form
	err = tools.Database.Collection("form").FindOne(nil, match).Decode(&form)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.RemoteSubjectDispensing{}, nil, err
	}

	var attribute models.Attribute
	attributeMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortOID != primitive.NilObjectID {
		attributeMatch = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	err = tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&attribute)
	if err != nil {
		return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, errors.WithStack(err)
	}

	subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

	var medicineNumber []string
	var histories []models.History
	user := models.User{}
	u, _ := ctx.Get("user")
	if u != nil {
		user = u.(models.User)
	}
	var userName string
	if project.ProjectInfo.ConnectEdc == 1 && project.ProjectInfo.PushMode == 1 && user.Name == "" { // EDC对接项目
		userName = edcUserName
		ctx.Set("edcUserName", edcUserName)
	} else {
		userName = user.Name
	}
	oldOpenProject := false
	oldOpenProjectRes, err := tools.Database.Collection("setting_config").CountDocuments(nil, bson.M{"key": "open-project", "data": project.ProjectInfo.Number})
	if err != nil {
		return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, errors.WithStack(err)
	}
	if oldOpenProjectRes == 1 && !attribute.AttributeInfo.Blind { // 如果当前cohort属性为盲态
		oldOpenProject = true
	}

	var env models.Environment
	envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
		return item.ID == envOID
	})
	env = *envP

	now := time.Duration(time.Now().Unix())
	zone, err := tools.GetTimeZone(projectOID)
	hours := time.Duration(zone)
	minutes := time.Duration((zone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute
	outSize := false
	siteOID := subject.ProjectSiteID  // 药物起运地
	storeOID := primitive.NilObjectID // 药物起运地
	if err != nil {
		return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, errors.WithStack(err)
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var doseInfo *models.DoseInfo
		medicineNumber = []string{}
		histories = []models.History{}
		dispensingMedicine = []models.DispensingMedicine{}
		otherDispensingMedicine = []models.OtherDispensingMedicine{}
		othersDispensingMedicine = []models.OthersDispensingMedicine{}
		resOrderInfo = []models.ResDispensingInfo{}

		err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询获取访视周期标签研究产品配置信息
		siteOID = subject.ProjectSiteID  // 药物起运地
		storeOID = primitive.NilObjectID // 药物起运地
		// DTP
		StoreOrderID := primitive.NewObjectID()
		SiteOrderID := primitive.NewObjectID()
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil && err != mongo.ErrNoDocuments {
			return [12]byte{}, errors.WithStack(err)
		}
		if len(projectSite.StoreHouseID) > 0 && projectSite.StoreHouseID[0] != primitive.NilObjectID {
			storeOID = projectSite.StoreHouseID[0] // 药物起运地
		}

		dispensingSort, err := getDispensingSort(sctx, subjectOID)

		nameBatch, lastDate, err := IsFirstDispensingIPInheritance(subject, attribute, dispensingSort, visitCycles, visitCycle)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		alarmCapacityInfoData, err := alarmCapacityInfo(sctx, project, envOID, projectSite, subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if data["dispensing_id"] != nil && data["dispensing_id"] != "" {
			dispensingOID, _ = primitive.ObjectIDFromHex(data["dispensing_id"].(string))
			visitSign = false
			// 校验是否已经发药了
			var checkDispensing models.Dispensing
			err = tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"_id": dispensingOID}).Decode(&checkDispensing)
			if err != nil {
				return models.RemoteSubjectDispensing{}, errors.WithStack(err)
			}
			if checkDispensing.Status == 2 {
				return models.RemoteSubjectDispensing{}, tools.BuildServerError(ctx, "subject_visit_dispensing")
			}
			if checkDispensing.Status == 3 {
				return models.RemoteSubjectDispensing{}, tools.BuildServerError(ctx, "subject_visit_dispensing_no_join")
			}
			// 补发 访视外等操作 再进行发药操作 序号 操作类型不变化
			visitSign = checkDispensing.VisitSign
			reissue = checkDispensing.Reissue
			serialNumber = checkDispensing.SerialNumber
			dispensing.Reasons = checkDispensing.Reasons
			dispensing.CancelMedicinesHistory = checkDispensing.CancelMedicinesHistory
			dispensing.OtherMedicinesHistory = checkDispensing.OtherMedicinesHistory
			dispensing.RealDispensingMedicines = checkDispensing.RealDispensingMedicines
			dispensing.RealOtherDispensingMedicines = checkDispensing.RealOtherDispensingMedicines
			dispensing.RealOthersDispensingMedicines = checkDispensing.RealOthersDispensingMedicines
			if len(checkDispensing.Reasons) == 0 {
				dispensing.Reasons = []models.Reason{}
			}
			if len(dispensing.CancelMedicinesHistory) == 0 {
				dispensing.CancelMedicinesHistory = []models.DispensingMedicine{}
			}
			if visitCycle.SetInfo.IsOpen && checkDispensing.VisitSign && checkDispensing.Reissue == 0 {
				outSize = true
			}
		} else {
			if visitCycle.SetInfo.IsOpen {
				outSize = true
			}
		}

		if len(visitLabels) == 0 && len(medicineInfo) == 0 && len(formulaMedicine) == 0 && outSize {
			return models.RemoteSubjectDispensing{}, tools.BuildServerError(ctx, "subject_medicine_label_select_outsize")

		}
		if len(visitLabels) == 0 && len(medicineInfo) == 0 && len(formulaMedicine) == 0 { // 开启访视外
			return models.RemoteSubjectDispensing{}, tools.BuildServerError(ctx, "subject_medicine_label_select")
		}

		// 校验 受试者是否已经揭盲 退出 替换
		status, err := checkSubjectStatus(ctx, sctx, subjectOID)
		if err != nil {
			return models.RemoteSubjectDispensing{}, err
		}
		if !status {
			return models.RemoteSubjectDispensing{}, tools.BuildServerError(ctx, "subject_status_no_dispensing")
		}

		// 校验发药是否按顺序
		checkVisit, err := checkVisitOrder(sctx, subjectOID, visitOID, visitSign)
		if err != nil {
			return models.RemoteSubjectDispensing{}, err
		}
		if !checkVisit {
			return models.RemoteSubjectDispensing{}, tools.BuildServerError(ctx, "subject_visit_dispensing_no_order")
		}

		// 校验通用订单是否存在待确认订单
		checkConfirm, err := checkConfirmOrder(sctx, subjectOID, 6)
		if err != nil {
			return models.RemoteSubjectDispensing{}, errors.WithStack(err)
		}
		if !checkConfirm {
			return models.RemoteSubjectDispensing{}, tools.BuildServerError(ctx, "subject_visit_dispensing_no_order_confrim")
		}

		if !visitInfo.DTP && sendType != 0 {
			return models.RemoteSubjectDispensing{}, tools.BuildServerError(ctx, "subject_visit_dispensing_no_order_dtp")
		}

		var instanceRepeatNo = "0"
		var blockRepeatNo = "0"
		if sign == 1 {
			// 和EDC对接过来的
			instanceRepeatNo, _ = data["instanceRepeatNo"].(string)
			blockRepeatNo, _ = data["blockRepeatNo"].(string)
		} else {
			// 查询访视下的发药数据
			var dispensingArray []models.Dispensing
			dsCursor, err := tools.Database.Collection("dispensing").Find(sctx, bson.M{"subject_id": subjectOID, "visit_info.visit_cycle_info_id": visitInfo.ID})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = dsCursor.All(nil, &dispensingArray)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			count := 0
			for _, dispensing := range dispensingArray {
				if (dispensing.Status == 3 && !dispensing.VisitSign) || len(dispensing.DispensingMedicines) > 0 || len(dispensing.OtherDispensingMedicines) > 0 || len(dispensing.RealDispensingMedicines) > 0 || len(dispensing.RealOtherDispensingMedicines) > 0 {
					count++
				}
			}
			blockRepeatNo = strconv.Itoa(count)
		}

		if !attribute.AttributeInfo.Random && attribute.AttributeInfo.Dispensing {
			var cohort models.Cohort

			//查询条件
			filter := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
			if subject.CohortID != primitive.NilObjectID {
				filter = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}

				cohortP, _ := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
					return item.ID == subject.CohortID
				})
				cohort = *cohortP
			}
			currentDispensingCount, err := tools.Database.Collection("dispensing").CountDocuments(sctx, bson.M{"subject_id": subject.ID, "status": bson.M{"$in": []int{2, 3}}})
			if err != nil {
				return 0, errors.WithStack(err)
			}
			count, err := tools.CountSubject(sctx, attribute, filter)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			needCapacity := false
			alertThresholdP := &models.AlertThreshold{}
			if project.ProjectInfo.Type == 1 {
				alertThresholdP, needCapacity = slice.Find(env.AlertThresholds, func(index int, item models.AlertThreshold) bool {
					return item.Type == 3
				})
			} else {
				alertThresholdP, needCapacity = slice.Find(cohort.AlertThresholds, func(index int, item models.AlertThreshold) bool {
					return item.Type == 3
				})
			}
			isStatus := false
			if project.ProjectInfo.Type == 1 {
				if env.Status != nil {
					isStatus = *env.Status == 5
				}
			} else {
				isStatus = cohort.Status == 5
			}
			if currentDispensingCount == 0 && (isStatus || (needCapacity && count >= alertThresholdP.Capacity)) {
				return nil, tools.BuildServerError(ctx, "subject.dispensing.enrollment.full")
			}
		}

		// 获取剂量信息
		doseTypeTmp := -1
		var lastDispensing models.Dispensing
		if data["dose_info"] != nil {
			key := data["dose_info"].(map[string]interface{})["key"].(string)
			value := data["dose_info"].(map[string]interface{})["value"].(string)
			lastDispensing, _, doseLevel, visitJudgment, doseTypeTmp, err = getCurrentDose(ctx, subject, value)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			formValue = &models.FormValue{Key: key, Value: value}
			if !doseLevel.ID.IsZero() {
				frequency := 0
				if doseTypeTmp == 2 {
					frequency++
				}
				doseInfoItem := models.DoseInfo{
					DoseLevelList: &doseLevel,
					Frequency:     frequency,
					Form:          formValue,
				}
				doseInfo = &doseInfoItem
				opts := &options.UpdateOptions{
					ArrayFilters: &options.ArrayFilters{
						Filters: bson.A{bson.M{"element._id": doseLevel.ID}},
					},
				}
				_, err = tools.Database.Collection("drug_configure_setting").UpdateOne(sctx, bson.M{"env_id": envOID, "cohort_id": cohortOID},
					bson.M{
						"$set": bson.M{
							"dose_level_list.$[element].is_used": true,
						},
					},
					opts)
				if err != nil {
					return nil, errors.WithStack(err)
				}

			}
			if !visitJudgment.ID.IsZero() {
				doseInfoItem := models.DoseInfo{
					VisitJudgmentList: &visitJudgment,
					Form:              formValue,
				}
				doseInfo = &doseInfoItem
				opts := &options.UpdateOptions{
					ArrayFilters: &options.ArrayFilters{
						Filters: bson.A{bson.M{"element._id": visitJudgment.ID}},
					},
				}
				_, err = tools.Database.Collection("drug_configure_setting").UpdateOne(sctx, bson.M{"env_id": envOID, "cohort_id": cohortOID},
					bson.M{
						"$set": bson.M{
							"visit_judgment_list.$[element].is_used": true,
						},
					},
					opts)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}

		err = updateForm(sctx, envOID, cohortOID, customerFormulas, formValue)
		if err != nil {
			return nil, err
		}
		allDrugMap, err := tools.AllDrugMap(envOID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 查询中的供应计划不发放日期
		expireDateKey, dispensingAlarmNumber, err := getExpireDateKey(subjectOID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		label := []string{}
		labelStr := ""

		var drugConfigure models.DispensingDrugConfigure
		err = tools.Database.Collection("drug_configure").FindOne(sctx, match).Decode(&drugConfigure)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		{
			/*    判断是否走申请流程，根据data["apply"] == true 判断
			根据是否超窗，attribute发药是否开启发药审批流程
			if 超窗 && 开启审批流程
				if 待审批状态
					返回前端 发放申请审批中，请等待。
				else
					将请求的data存入数据库
					创建审批流程
			else
				直接发药
			*/

			dispensingType := 1
			if dispensing.VisitSign == true {
				dispensingType = 2
			}
			process, err := CheckDispensingApprovalProcess(ctx, sctx, project, env, projectSite, dispensing, data, subject, attribute, visitCycle, dispensingType)
			if err != nil {
				return nil, err
			}
			if !process { // 返回申请成功
				if sign == 1 { // edc对接放回 待审批
					return nil, tools.BuildServerError(ctx, "dispensing.approval.pending")
				}
				return nil, nil
			}
		}

		for _, visitLabel := range visitLabels {
			configOID, _ := primitive.ObjectIDFromHex(visitLabel["id"].(string))
			configuresP, ok := slice.Find(drugConfigure.Configures, func(index int, item models.DispensingConfigureInfo) bool {
				return item.ID == configOID
			})
			if !ok {
				continue
			}
			configures := *configuresP
			if visitLabel["label"] != configures.Label { // 不是组合标签 则是单标签
				for _, value := range configures.Values {
					if value.Label == visitLabel["label"] {
						if visitLabel["count"] != nil {
							dispensingNumber, _ := convertor.ToInt(visitLabel["count"])
							value.DispensingNumber = int(dispensingNumber)
						} else {
							// 自定义访视外 读取另外配置
							if outSize {
								number, err := OutSizeDispensingNumber(value.DrugName, configures.RoutineVisitMappingList, visitOID)
								if err != nil {
									return nil, err
								}
								value.DispensingNumber = number
							}
						}
						if doseTypeTmp == 1 {
							value.DispensingNumber = updateLabelNameCounts(lastDispensing, value.DrugName)
						}

						if attribute.AttributeInfo.DtpRule == 1 && visitLabel["dtp"] == nil {
							return nil, tools.BuildServerError(ctx, "subject_medicine_dtp_error")
						}

						if visitLabel["dtp"] != nil {
							value.DTP = int(visitLabel["dtp"].(float64))
						}
						configures.Values = []models.DispensingDrugValue{value}
					}
				}
			} else {

				if attribute.AttributeInfo.DtpRule == 1 && visitLabel["dtp"] == nil {
					return nil, tools.BuildServerError(ctx, "subject_medicine_dtp_error")
				}
				if visitLabel["count"] != nil {
					for _, value := range configures.Values {
						dispensingNumber, _ := convertor.ToInt(visitLabel["count"])
						value.DispensingNumber = int(dispensingNumber)
						value.Label = visitLabel["label"].(string)
						if visitLabel["dtp"] != nil {
							value.DTP = int(visitLabel["dtp"].(float64))
						}
						configures.Values = []models.DispensingDrugValue{value}
					}
				} else {
					tmpValue := make([]models.DispensingDrugValue, 0)
					for _, value := range configures.Values {
						value.Label = visitLabel["label"].(string)
						if doseTypeTmp == 1 {
							value.DispensingNumber = updateLabelNameCounts(lastDispensing, value.DrugName)
						}

						// 自定义访视外 读取另外配置
						if outSize {
							number, err := OutSizeDispensingNumber(value.DrugName, configures.RoutineVisitMappingList, visitOID)
							if err != nil {
								return nil, err
							}
							value.DispensingNumber = number
						}
						if visitLabel["dtp"] != nil {
							value.DTP = int(visitLabel["dtp"].(float64))
						}
						if value.DispensingNumber != 0 {
							tmpValue = append(tmpValue, value)
						}
					}
					configures.Values = tmpValue

				}
			}

			// 分配房间号 康泰
			// 访视存在房间号  并且 受试者未分配房间号
			roomNumbers := []string{}
			if len(configures.RoomNumbers) != 0 {
				roomNumbers = configures.RoomNumbers

			}
			room = subject.RoomNumber
			if len(roomNumbers) > 0 {
				tmpRoom, err := AllocationRooms(sctx, subject, roomNumbers)
				if err != nil {
					return nil, err
				}
				if room == "" {
					room = tmpRoom
				}
			}

			// 循环研究产品配置的药物列表
			for _, values := range configures.Values {
				tmpSendType := sendType
				if attribute.AttributeInfo.DtpRule == 1 {
					tmpSendType = values.DTP
				}
				number := values.DispensingNumber
				if err != nil {
					return nil, err
				}
				date := time.Now().UTC().Add(duration).Format("2006-01-02")
				if expireDateKey[values.DrugName] != nil {
					date = expireDateKey[values.DrugName].(string)
				}
				//
				//
				//drugMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "info.name": values.(map[string]interface{})["drugname"],
				//	"info.expire_date": bson.M{"$gt": date},
				//}
				//count, _ := tools.Database.Collection("medicine_other_institute").CountDocuments(sctx, drugMatch)
				// 通过读配置判断是否是未编号药物配置

				if values.IsOther {
					// 未编号药物
					//otherDispensingMedicineSinge, othersDispensingMedicineSinge, err := getOthersMedicine(ctx, sctx, subject, date, number, values, &otherMedicineCount, now, sourceSend,, dispensingAlarmNumber, labelStr, now)
					otherDispensingMedicineSinge, othersDispensingMedicineSinge, err := getOthersMedicine(ctx, sctx, subject, date, number, values, &otherMedicineCount, tmpSendType, siteOID, storeOID, SiteOrderID, StoreOrderID, now, dispensingAlarmNumber, labelStr, 1, "", attribute, alarmCapacityInfoData, nameBatch, lastDate)
					if err != nil {
						return nil, err
					}
					if visitLabel["useFormula"] != nil && visitLabel["formulaCount"] != nil {
						useFormula := models.CustomerFormula{
							Key:   visitLabel["useFormula"].(string),
							Value: visitLabel["formulaCount"].(float64),
						}
						for i := range otherDispensingMedicineSinge {
							otherDispensingMedicineSinge[i].UseFormulas = &useFormula
						}
						for i := range othersDispensingMedicineSinge {
							othersDispensingMedicineSinge[i].UseFormulas = &useFormula
						}
					}
					if visitLabel["level"] != nil {
						for i := range otherDispensingMedicineSinge {
							otherDispensingMedicineSinge[i].DoseInfo = doseInfo
						}
						for i := range othersDispensingMedicineSinge {
							othersDispensingMedicineSinge[i].DoseInfo = doseInfo
						}
					}

					otherDispensingMedicine = slice.Concat(otherDispensingMedicine, otherDispensingMedicineSinge)
					othersDispensingMedicine = slice.Concat(othersDispensingMedicine, othersDispensingMedicineSinge)

				} else {
					// 研究产品名称在未编号研究产品中查找不到
					dispensingMedicineSinge, err := getMedicine(ctx, sctx, subject, date, number, values, attribute, userName, user, &medicineNumber, tmpSendType, siteOID, storeOID, SiteOrderID, StoreOrderID, &histories, 1, now, visitInfo.Name, visitSign, dispensingAlarmNumber, labelStr, allDrugMap, oldOpenProject, alarmCapacityInfoData, nameBatch, lastDate)
					if err != nil {
						return nil, err
					}

					if visitLabel["useFormula"] != nil && visitLabel["formulaCount"] != nil {
						useFormula := models.CustomerFormula{
							Key:   visitLabel["useFormula"].(string),
							Value: visitLabel["formulaCount"].(float64),
						}
						for i := range dispensingMedicineSinge {
							dispensingMedicineSinge[i].UseFormulas = &useFormula
						}
					}
					for i := range dispensingMedicineSinge {
						dispensingMedicineSinge[i].DoseInfo = doseInfo
					}
					for i := range dispensingMedicineSinge {
						dispensingMedicineSinge[i].OpenSetting = 1
					}

					dispensingMedicine = slice.Concat(dispensingMedicine, dispensingMedicineSinge)
				}

			}
		}
		// 开放药物 + 公式计算药物
		openDrug := models.OpenDrug{
			MedicineInfo:             medicineInfo,
			Subject:                  subject,
			ExpireDateKey:            expireDateKey,
			SendType:                 sendType,
			OtherMedicineCount:       &otherMedicineCount,
			OtherDispensingMedicine:  &otherDispensingMedicine,
			OthersDispensingMedicine: &othersDispensingMedicine,
			DispensingMedicine:       &dispensingMedicine,
			Attribute:                attribute,
			UserName:                 userName,
			User:                     user,
			MedicineNumber:           &medicineNumber,
			SiteOID:                  siteOID,
			StoreOID:                 storeOID,
			SiteOrderOID:             SiteOrderID,
			StoreOrderOID:            StoreOrderID,
			Histories:                &histories,
			Now:                      now,
			DispensingType:           1,
			VisitSign:                visitSign,
			VisitName:                visitInfo.Name,
			DispensingAlarmNumber:    dispensingAlarmNumber,
			DoseInfo:                 doseInfo,
			AllDrugMap:               allDrugMap,
			OldOpenProject:           oldOpenProject,
			AlarmCapacityInfoData:    alarmCapacityInfoData,
			LastDate:                 lastDate,
			NameBatch:                nameBatch,
		}
		err = getOpenDrug(ctx, sctx, openDrug)
		if err != nil {
			return nil, err
		}
		// 发药插入数据

		// 查询访视周期序列值
		var dispensingModel models.Dispensing

		reasons := []models.Reason{}
		reason := ""
		if data["reason"] != nil {
			reasons = append(reasons, models.Reason{
				ReasonType: 3,
				ReasonID:   primitive.NewObjectID(),
				ReasonTime: now,
				Reason:     data["reason"].(string),
			})
			reason = data["reason"].(string)
		}

		opts := &options.FindOneOptions{
			Sort: bson.D{{"serial_number", -1}},
		}
		err = tools.Database.Collection("dispensing").FindOne(nil, bson.M{"visit_info.visit_cycle_info_id": visitInfo.ID, "subject_id": subjectOID},
			opts).Decode(&dispensingModel)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if len(dispensingMedicine) == 0 {
			dispensingMedicine = []models.DispensingMedicine{}
		}

		if serialNumber == -1 {
			serialNumber = dispensingModel.SerialNumber + 1
		}
		dispensing.Reasons = append(dispensing.Reasons, reasons...)
		//dispensing.CancelMedicinesHistory = append(dispensing.CancelMedicinesHistory, dispensingMedicine...)
		//dispensing.OtherMedicinesHistory = append(dispensing.OtherMedicinesHistory, otherDispensingMedicine...)
		for _, other := range otherDispensingMedicine {
			item := models.OtherDispensingMedicineInfo{
				ID:              other.ID,
				MedicineOtherID: other.MedicineOtherID,
				Name:            other.Name,
				Count:           other.Count,
				Batch:           other.Batch,
				ExpireDate:      other.ExpireDate,
				Time:            other.Time,
				Type:            other.Type,
				Label:           other.Label,
				DTP:             other.DTP,
				OrderOID:        other.OrderOID,
				UseFormulas:     other.UseFormulas,
				DoseInfo:        other.DoseInfo,
			}
			dispensing.OtherMedicinesHistory = append(dispensing.OtherMedicinesHistory, item)
		}

		err = slice.SortByField(dispensingMedicine, "Number")
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = slice.SortByField(otherDispensingMedicine, "Name")
		if err != nil {
			return nil, errors.WithStack(err)
		}

		dispensing = models.Dispensing{
			ID:            dispensingOID,
			CustomerID:    customerOID,
			ProjectID:     projectOID,
			EnvironmentID: envOID,
			CohortID:      cohortOID,
			SubjectID:     subjectOID,
			VisitInfo: models.VisitInfo{
				VisitCycleInfoID: visitInfo.ID,
				Number:           visitInfo.Number,
				InstanceRepeatNo: instanceRepeatNo,
				BlockRepeatNo:    blockRepeatNo,
				Name:             visitInfo.Name,
				Random:           visitInfo.Random,
				Dispensing:       visitInfo.Dispensing,
			},
			DispensingMedicines:           dispensingMedicine,
			OtherDispensingMedicines:      otherDispensingMedicine,
			OthersDispensingMedicines:     othersDispensingMedicine,
			VisitSign:                     visitSign,
			Reissue:                       reissue,
			Status:                        2,
			DispensingTime:                now,
			Reasons:                       dispensing.Reasons,
			CancelMedicinesHistory:        dispensing.CancelMedicinesHistory,
			SerialNumber:                  serialNumber,
			ReplaceMedicines:              []models.ReplaceMedicines{},
			Labels:                        label,
			Remark:                        remark,
			OtherMedicinesHistory:         dispensing.OtherMedicinesHistory,
			FormulaInfo:                   formulaInfo,
			Form:                          customerFormulas,
			DoseInfo:                      doseInfo,
			RealDispensingMedicines:       dispensing.RealDispensingMedicines,
			RealOtherDispensingMedicines:  dispensing.RealOtherDispensingMedicines,
			RealOthersDispensingMedicines: dispensing.RealOthersDispensingMedicines,
		}

		// 新增订单 访视DTP
		if sendType != 0 {
			orderOID := SiteOrderID
			sourceSend := subject.ProjectSiteID
			if sendType == 2 {
				orderOID = StoreOrderID
				sourceSend = storeOID

			}
			tmpResOrderInfo, err := s.OrderSend(ctx, sctx, isBlindedRole, subject, dispensing, sendType, sourceSend, orderOID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			dispensing.Order = tmpResOrderInfo[0].Order
			resOrderInfo = append(resOrderInfo, tmpResOrderInfo...)
		} else { // 药物DTP
			_, siteOK := slice.Find(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
				return *item.DTP == 1
			})
			_, siteOtherOK := slice.Find(dispensing.OthersDispensingMedicines, func(index int, item models.OthersDispensingMedicine) bool {
				return *item.DTP == 1
			})
			if siteOK || siteOtherOK {
				tmpResOrderInfo, err := s.OrderSend(ctx, sctx, isBlindedRole, subject, dispensing, 1, siteOID, SiteOrderID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				resOrderInfo = append(resOrderInfo, tmpResOrderInfo...)
			}
			_, storeOK := slice.Find(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
				return *item.DTP == 2
			})
			_, storeOtherOK := slice.Find(dispensing.OthersDispensingMedicines, func(index int, item models.OthersDispensingMedicine) bool {
				return *item.DTP == 2
			})
			if storeOK || storeOtherOK {
				tmpResOrderInfo, err := s.OrderSend(ctx, sctx, isBlindedRole, subject, dispensing, 2, storeOID, StoreOrderID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				resOrderInfo = append(resOrderInfo, tmpResOrderInfo...)
			}
		}

		//添加app任务,判断是否是系统编码
		barcode_rule_filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
		if data["cohort_id"] != nil {
			barcode_rule_filter["cohort_id"] = cohortOID
		}
		var barcodeRule models.BarcodeRule
		err = tools.Database.Collection("barcode_rule").FindOne(nil, barcode_rule_filter).Decode(&barcodeRule)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		if barcodeRule.CodeRule == 1 && len(dispensingMedicine) > 0 { // 系统自动编码
			//新增工作任务
			workType := 4
			//创建发药任务
			permissions := []string{"operation.subject.medicine.dispensing"}
			if visitSign { //访视外发药
				workType = 9
				permissions = []string{"operation.subject.medicine.out-visit-dispensing"}
			}
			if reissue == 1 {
				workType = 5
				permissions = []string{"operation.subject.medicine.reissue"}
			}

			siteOrStoreIDs := []primitive.ObjectID{subject.ProjectSiteID}
			userIds, err := tools.GetPermissionUserIds(sctx, permissions, projectOID, envOID, siteOrStoreIDs...)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			workTaskId := primitive.NewObjectID()
			if len(userIds) > 0 {
				var medicineIds []primitive.ObjectID
				for _, v := range dispensingMedicine {
					medicineIds = append(medicineIds, v.MedicineID)
				}
				workTask := models.WorkTask{
					ID:            workTaskId,
					CustomerID:    customerOID,
					ProjectID:     projectOID,
					EnvironmentID: envOID,
					CohortID:      cohortOID,
					UserIDs:       userIds,
					// UserID:        user.ID,
					// RoleID:        roleOID,
					Info: models.WorkTaskInfo{
						WorkType:        workType,
						Status:          0,
						CreatedTime:     time.Duration(time.Now().Unix()),
						Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
						MedicineIDs:     medicineIds,
						MedicineOrderID: primitive.NilObjectID,
						DispensingID:    dispensingOID,
					},
				}
				_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				dispensing.WorkTaskId = workTaskId
			}
		}

		upsert := true
		updateOpts := &options.UpdateOptions{
			Upsert: &upsert,
		}
		_, err = tools.Database.Collection("dispensing").UpdateOne(sctx, bson.M{"_id": dispensingOID}, bson.M{"$set": dispensing}, updateOpts)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var emailMedicineNumber []string
		sortDispensingMedicines := sortTypeMedicineNumber(dispensingMedicine)
		for _, itemMedicine := range sortDispensingMedicines {
			emailMedicineNumber = append(emailMedicineNumber, itemMedicine.Number)
		}
		for _, item := range otherDispensingMedicine {
			if item.Batch == "" {
				item.Batch = "-"
			}
			if item.ExpireDate == "" {
				item.ExpireDate = "-"
			}
			emailMedicineNumber = append(emailMedicineNumber, fmt.Sprintf("%s/%d/%s/%s", item.Name, item.Count, item.Batch, item.ExpireDate))
		}
		title := "dispensing.plan-title"
		content := "dispensing.plan"
		if visitSign {
			title = "dispensing.unscheduled-plan-title"
			content = "dispensing.unscheduled-plan"
		}
		if dispensing.Reissue == 1 {
			title = "dispensing.reissue-title"

		}
		if sendType == 1 || sendType == 2 { //通用DTP模板
			content = content + "-logistics"
		}
		mails := make([]models.Mail, 0)
		subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
		subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)
		mails, err = mail(ctx, mails, dispensing, dispensing.DispensingTime, now, "", visitCycle, visitInfo, emailMedicineNumber, title, []string{}, subjectEmailReplaceTextZh, subjectEmailReplaceTextEn, attribute, remark, dispensing.Order, reason)
		if err != nil {
			return nil, err
		}

		// 批次警戒通知

		if attribute.AttributeInfo.IPInheritance && len(nameBatch) == 0 {
			err = noticeBatchAlarmMail(ctx, &mails, alarmCapacityInfoData, dispensing, project, projectSite)
			if err != nil {
				return nil, err
			}
		}
		if !attribute.AttributeInfo.Random && attribute.AttributeInfo.Dispensing {
			var env models.Environment
			envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
				return item.ID == dispensing.EnvironmentID
			})
			env = *envP
			var cohort models.Cohort
			cohortP, b := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
				return item.ID == dispensing.CohortID
			})
			if b {
				cohort = *cohortP
			}
			var projectSite models.ProjectSite
			err := tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			var noticeConfig models.NoticeConfig
			err = tools.Database.Collection("notice_config").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "key": "notice.basic.settings"}).Decode(&noticeConfig)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			html := "subject_limit_alert_zh_en.html"
			if noticeConfig.Manual != 0 {
				if noticeConfig.Manual == 1 {
					html = "subject_limit_alert_zh.html"
				} else if noticeConfig.Manual == 2 {
					html = "subject_limit_alert_en.html"
				} else if noticeConfig.Manual == 3 {
					html = "subject_limit_alert_zh_en.html"
				}
			} else {
				if locales.Lang(ctx) == "zh" {
					html = "subject_limit_alert_zh.html"
				} else if locales.Lang(ctx) == "en" {
					html = "subject_limit_alert_en.html"
				}
			}
			mails, err = alertThresholds(sctx, 3, project, env, cohort, attribute, subject, projectSite, ctx, html, mails)
		}

		ctx.Set("MAIL", mails)
		if len(mails) > 0 {
			var envs []models.MailEnv
			for _, m := range mails {
				envs = append(envs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     m.ID,
					CustomerID: dispensing.CustomerID,
					ProjectID:  dispensing.ProjectID,
					EnvID:      dispensing.EnvironmentID,
					CohortID:   dispensing.CohortID,
				})
			}
			ctx.Set("MAIL-ENV", envs)
		}

		// 更新访视通知

		_, err = tools.Database.Collection("visit_notice").UpdateMany(sctx,
			bson.M{"dispensing_id": dispensing.ID, "status": 0},
			bson.M{"$set": bson.M{"status": 3}})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//更新报表 公式字段
		if len(useFormulas) > 0 {
			_, err = tools.Database.Collection("customer_report_title").UpdateOne(nil,
				bson.M{"customer_id": dispensing.CustomerID, "env_id": dispensing.EnvironmentID, "project_id": dispensing.ProjectID, "cohort_id": dispensing.CohortID},
				bson.M{"$addToSet": bson.M{"title": bson.M{"$each": useFormulas}}},
				updateOpts,
			)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		//在随机项目判断发药完成后是否需要添加下一阶段的受试者   jira 6631 DTP发放不触发 等订单接收再触发
		if sendType == 0 && data["dispensing_id"] != nil && data["dispensing_id"] != "" { // 新增的访视外不添加受试者
			err = DispensingAddSubject(ctx, sctx, dispensing.ProjectID, dispensing.SubjectID, edcUserName)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		//更新入组状态
		if !attribute.AttributeInfo.Random && attribute.AttributeInfo.Dispensing {
			err = UpdateDispensingCohortStatus(sctx, attribute, dispensing, project)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		return nil, nil
	}
	err = tools.Transaction(callback)

	if err != nil {
		// 订单实时核查
		if project.ProjectInfo.OrderCheck == 2 {
			err := task.AlarmMedicineNew(2, envOID)
			if err != nil {
				return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, err
			}
		}
		return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, err
	} else {
		// 订单实时核查
		if project.ProjectInfo.OrderCheck == 2 {
			err := task.AlarmMedicineNew(2, envOID)
			if err != nil {
				return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, err
			}
		}
	}

	timeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, err
	}
	if timeZone == "" {
		zone, err := tools.GetTimeZone(projectOID)
		if err != nil {
			return models.RemoteSubjectDispensing{}, []models.ResDispensingInfo{}, err
		}
		timeZone = tools.FormatOffsetToZoneStringUtc(zone)
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(timeZone, "UTC", "", 1))
	intTimeZone, err := tools.ParseTimezoneOffset(timeZone)
	hours = time.Duration(intTimeZone)
	minutes = time.Duration((intTimeZone - float64(hours)) * 60)
	duration = hours*time.Hour + minutes*time.Minute

	remoteSubjectDispensing.SubjectNo = subject.Info[0].Value.(string)
	remoteSubjectDispensing.DispensingMedicines = dispensingMedicine
	remoteSubjectDispensing.OtherDispensingMedicines = otherDispensingMedicine
	remoteSubjectDispensing.DispensingTime = time.Unix(int64(now), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
	remoteSubjectDispensing.TimeZone = timeZone
	remoteSubjectDispensing.Timestamp = now
	if room != "" {
		remoteSubjectDispensing.Room = room
		if attribute.AttributeInfo.IsRandomNumber {
			remoteSubjectDispensing.Number = subject.RandomNumber
		}
	}

	// 发药轨迹 判断是否是EDC对接项目
	var edcFormValue models.FormValue
	if formValue != nil {
		edcFormValue = *formValue
	}
	history, err := getOtherMedicineHistory(dispensing, dispensing.OthersDispensingMedicines, siteOID, storeOID, "", user, userName, now)
	if err != nil {
		return models.RemoteSubjectDispensing{}, nil, err
	}
	histories = append(histories, history...)
	//if sendType != 0 {
	//
	//
	//	for _, medicine := range dispensing.OthersDispensingMedicines {
	//		history := models.History{
	//			Key:  "history.medicine.otherToBeConfirm",
	//			OID:  medicine.MedicineID,
	//			Data: bson.M{"name": medicine.Name, "batch": medicine.BatchNumber, "expireDate": medicine.ExpirationDate, "count": 1},
	//			Time: now,
	//			UID:  user.ID,
	//			User: userName,
	//		}
	//		histories = append(histories, history)
	//	}
	//} else {
	//	for _, medicine := range dispensing.OthersDispensingMedicines {
	//		history := models.History{
	//			Key:  "history.medicine.otherUse",
	//			OID:  medicine.MedicineID,
	//			Data: bson.M{"name": medicine.Name, "batch": medicine.BatchNumber, "expireDate": medicine.ExpirationDate, "count": 1},
	//			Time: now,
	//			UID:  user.ID,
	//			User: userName,
	//		}
	//		histories = append(histories, history)
	//
	//	}
	//
	//}
	key := "history.dispensing.dispensingCustomer-dispensing"
	if dispensing.VisitSign {
		key = "history.dispensing.dispensingCustomer-dispensingVisit"
		if outSize {
			key = "history.dispensing.dispensingCustomer-dispensingVisitCustomer"
		}
	}

	if dispensing.Reissue == 1 {
		key = "history.dispensing.dispensingCustomer-reissue"
	}
	err = addDispensingHistoryData(ctx, key, &histories, subjectReplaceText, subject, dispensing, formulaInfo,
		user, userName, now, sendType, customerFormulas, form, edcFormValue, logisticsInfo, data, attribute)
	if err != nil {
		return models.RemoteSubjectDispensing{}, nil, errors.WithStack(err)
	}

	// IRT请求 并且为盲法项目   数据权限处理
	if sign == 0 {
		if isBlindedRole {
			for i, medicine := range remoteSubjectDispensing.DispensingMedicines {
				// isOpenDrug, _ := tools.IsOpenDrug(envOID, cohortOID, medicine.Name)
				// isOtherDrug, _ := tools.IsOtherDrug(envOID, medicine.Name)
				// if !isOpenDrug && !isOtherDrug {
				// 	remoteSubjectDispensing.DispensingMedicines[i].Name = tools.BlindData
				// }
				isBlindedDrug, _ := tools.IsBlindedDrug(envOID, medicine.Name)
				if isBlindedDrug {
					remoteSubjectDispensing.DispensingMedicines[i].Name = tools.BlindData
				}
			}
		}
		if isBlindedRoomRole && remoteSubjectDispensing.Room != "" {
			remoteSubjectDispensing.Room = tools.BlindData
		}
	}

	//发放完成--更新app发放通知为完成状态
	if attribute.AttributeInfo.Dispensing && dispensing.Status == 2 && !dispensing.VisitSign {
		dispensingId, _ := data["dispensing_id"].(string)
		err = s.PatchAppDispenseTaskFinish(ctx, dispensingId)
		if err != nil {
			return remoteSubjectDispensing, nil, err
		}
	}

	// 推送给EDC
	sourceType := 4
	if dispensing.VisitSign { // 是否访视外发药
		if dispensing.Reissue == 1 { // 补发
			sourceType = 7
		} else { // 非补发
			sourceType = 5
		}
	}

	// 修改cohort入组状态

	if len(roleID) > 0 && attribute.AttributeInfo.Dispensing {
		err = PatchAppDispenseTask(ctx, subject.ID.Hex(), roleID)
		if err != nil {
			return remoteSubjectDispensing, nil, err
		}
	}

	// 推给EDC
	if tools.PushScenarioFilter(project.ConnectEdc, project.PushMode, project.EdcSupplier, project.PushScenario.DispensingPush) {
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectDispensingPush(logData, dispensingOID, sourceType, now)
	}
	//if project.ConnectEdc == 1 && project.PushMode == 2 && (project.PushTypeEdc == "" || project.PushTypeEdc == "OnlyDrug" || project.PushTypeEdc == "RandomAndDrug") {
	//	SubjectDispensingPush(ctx, dispensingOID, sourceType, now)
	//}

	return remoteSubjectDispensing, resOrderInfo, nil
}

func OutSizeDispensingNumber(name string, routineVisitMapping []models.RoutineVisitMapping, oid primitive.ObjectID) (int, error) {
	number := int64(0)
	var err error
	for _, item := range routineVisitMapping {
		_, ok := slice.Find(item.VisitList, func(index int, item primitive.ObjectID) bool {
			return item == oid
		})
		if ok {
			drugP, nameOK := slice.Find(item.DrugList, func(index int, item models.Drug) bool {
				return item.DrugName == name
			})
			if nameOK {
				drug := *drugP
				number, err = convertor.ToInt(drug.CustomDispensingNumber)
				return int(number), errors.WithStack(err)
			}
		}
	}
	return int(number), nil

}

// PatchAppDispenseTaskFinish 完成发药-修改app发药任务的完成状态
func (s *DispensingService) PatchAppDispenseTaskFinish(ctx *gin.Context, id string) error {
	OID, _ := primitive.ObjectIDFromHex(id)
	//判断app端是否有发药任务，如果有，关闭任务
	var workTask models.WorkTask
	err := tools.Database.Collection("work_task").FindOneAndUpdate(ctx, bson.M{"info.dispensing_id": OID, "info.work_type": 11, "info.status": 0, "deleted": bson.M{"$ne": true}}, bson.M{"$set": bson.M{"info.status": 1, "info.finish_time": time.Duration(time.Now().Unix())}}).Decode(&workTask)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	return nil
}

// PatchAppDispenseTaskVoided 不参加访视-删除已经产生的app发药任务
func (s *DispensingService) PatchAppDispenseTaskVoided(ctx *gin.Context, id string) error {
	OID, _ := primitive.ObjectIDFromHex(id)
	//判断app端是否有发药任务，如果有，关闭任务
	var workTask models.WorkTask
	err := tools.Database.Collection("work_task").FindOneAndUpdate(ctx, bson.M{"info.dispensing_id": OID, "info.work_type": 11, "info.status": 0, "deleted": bson.M{"$ne": true}}, bson.M{"$set": bson.M{"deleted": true}}).Decode(&workTask)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	return nil
}

func (s *DispensingService) GetAppDispensing(ctx *gin.Context, subjectID string, roleID string) (map[string]interface{}, error) {
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	result := make(map[string]interface{})
	// 查询该项目是否为盲法
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(ctx, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var attribute models.Attribute
	filter := bson.M{"customer_id": subject.CustomerID, "env_id": subject.EnvironmentID}
	if !subject.CohortID.IsZero() {
		filter = bson.M{"customer_id": subject.CustomerID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
	}

	err = tools.Database.Collection("attribute").FindOne(nil, filter).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 声明接受字段信息
	var fields []models.Field
	fields = append(fields, attribute.AttributeInfo.Field)

	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询是否有分层因素
	var randomLists []models.RandomList
	randomFilter := filter
	randomFilter["status"] = 1
	if !subject.ProjectSiteID.IsZero() {
		randomFilter["$or"] = bson.A{
			bson.M{"site_ids": subject.ProjectSiteID},
			bson.M{"site_ids": nil},
		}
	}
	cursor, err := tools.Database.Collection("random_list").Find(nil, randomFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomLists)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var subjectArray []models.Subject
	infos := make([]models.Info, 0)
	// 在随机
	firstPhase := false
	if project.ProjectInfo.Type == 3 {
		subjectNo := ""
		for _, info := range subject.Info {
			if info.Name == "shortname" {
				subjectNo = info.Value.(string)
			}
		}
		var environment models.Environment
		for _, env := range project.Environments {
			if env.ID == subject.EnvironmentID {
				environment = env
				break
			}
		}
		for idx, cho := range environment.Cohorts {
			var sbj models.Subject
			err := tools.Database.Collection("subject").FindOne(nil,
				bson.M{
					"cohort_id": cho.ID,
					"info": bson.M{
						"$elemMatch": bson.M{
							"name":  "shortname",
							"value": subjectNo,
						}},
					"deleted": bson.M{"$ne": true}}).Decode(&sbj)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, errors.WithStack(err)
			}
			if sbj.Info != nil && len(sbj.Info) > 0 {
				subjectArray = append(subjectArray, sbj)
				// 查询form表单/分层
				var fieldArray []models.Field
				fieldArray, err = GetFields(sbj, cho, project.ProjectInfo.Type, idx)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				fields = append(fields, fieldArray...)
				// 给form表单/分层塞值
				fieldsValue := GetFieldsValue(sbj, project.ProjectInfo.Type, idx)
				infos = append(infos, fieldsValue...)
			}
		}
		// 判断在随机是否是第一阶段
		if environment.Cohorts[0].ID == subject.CohortID {
			firstPhase = true
		}
	} else {
		subjectArray = append(subjectArray, subject)
		// 查询form表单/分层
		var fieldArray []models.Field
		fieldArray, err = GetFields(subject, models.Cohort{}, project.ProjectInfo.Type, 0)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		fields = append(fields, fieldArray...)
		//给form表单/分层塞值
		if subject.Info != nil && len(subject.Info) > 0 {
			fieldsValue := GetFieldsValue(subject, project.ProjectInfo.Type, 0)
			infos = append(infos, fieldsValue...)
		}
	}

	result["fields"] = fields
	result["fieldsValue"] = infos
	result["randomNumber"] = subject.RandomNumber
	result["firstPhase"] = firstPhase
	result["cohortId"] = subject.CohortID

	if !attribute.AttributeInfo.IsRandomNumber && subject.RandomNumber != "" {
		result["randomNumber"] = tools.BlindData
	}

	roleOID, _ := primitive.ObjectIDFromHex(roleID)
	var projectRolePermission models.ProjectRolePermission
	err = tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"project_id": subject.ProjectID, "_id": roleOID}).Decode(&projectRolePermission)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	_, err = tools.Database.Collection("project_role_permission").CountDocuments(nil, bson.M{"project_id": subject.ProjectID, "_id": roleOID, "permissions": "operation.subject.medicine.formula.update"})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	_, updateOk := slice.Find(projectRolePermission.Permissions, func(index int, item string) bool {
		return item == "operation.subject.medicine.formula.update"
	})

	_, visitSignOk := slice.Find(projectRolePermission.Permissions, func(index int, item string) bool {
		return item == "operation.subject.medicine.out-visit-dispensing"
	})

	_, reissueOk := slice.Find(projectRolePermission.Permissions, func(index int, item string) bool {
		return item == "operation.subject.medicine.reissue"
	})

	_, grantOk := slice.Find(projectRolePermission.Permissions, func(index int, item string) bool {
		return item == "operation.subject.medicine.dispensing"
	})

	_, grantDtpOk := slice.Find(projectRolePermission.Permissions, func(index int, item string) bool {
		return item == "operation.subject-dtp.medicine.dispensing"
	})

	// 筛选权限
	_, screenOk := slice.Find(projectRolePermission.Permissions, func(index int, item string) bool {
		return item == "operation.subject.screen"
	})
	_, screenDtpOk := slice.Find(projectRolePermission.Permissions, func(index int, item string) bool {
		return item == "operation.subject-dtp.screen"
	})
	//随机权限
	_, randomOk := slice.Find(projectRolePermission.Permissions, func(index int, item string) bool {
		return item == "operation.subject.random"
	})
	_, randomDtpOk := slice.Find(projectRolePermission.Permissions, func(index int, item string) bool {
		return item == "operation.subject-dtp.random"
	})

	var resAppDispensing []models.ResAppDispensing

	// TODO 7064

	var visitCycles []models.VisitCycle
	visitMatch := bson.M{
		"project_id":  subject.ProjectID,
		"env_id":      subject.EnvironmentID,
		"customer_id": subject.CustomerID,
	}

	cursor, err = tools.Database.Collection("visit_cycle").Find(nil, visitMatch)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &visitCycles)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	subjectMap := make(map[string]models.Subject)
	for _, value := range visitCycles {
		if !value.BaseCohort.IsZero() && value.CohortID != value.BaseCohort {
			subjectMap, err = task.GetBaseCohortSubjectMap(nil, value.BaseCohort, primitive.NilObjectID, "")

		}
	}

	for _, sa := range subjectArray {
		var dispensing []models.ResAppDispensing
		match := bson.M{"subject_id": sa.ID}
		cursor, err = tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$sort", Value: bson.M{"serial_number": 1}}},
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		isBlindedRole := false
		if roleID != "" {
			isBlindedRole, err = tools.IsBlindedRole(roleID)
			if err != nil {
				return nil, err
			}
		}

		IsBlindDrugMap, err := tools.IsBlindDrugMap(attribute.EnvironmentID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		visitCycleP, _ := slice.Find(visitCycles, func(index int, item models.VisitCycle) bool {
			return item.CohortID == sa.CohortID
		})
		visitCycle := *visitCycleP
		visitCycleInfoMap := make(map[string]models.VisitCycleInfo)
		for _, v := range visitCycle.Infos {
			visitCycleInfoMap[v.Number] = v
		}

		strTimeZone, err := tools.GetSiteTimeZone(sa.ProjectSiteID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if strTimeZone == "" {
			float, err := tools.GetTimeZone(sa.ProjectID)
			if err != nil {
				return nil, err
			}
			strTimeZone = tools.FormatOffsetToZoneStringUtc(float)
			//strTimeZone = fmt.Sprintf("UTC%+d", zone)
		}
		//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
		intTimeZone, err := tools.ParseTimezoneOffset(strTimeZone)

		lastTime := time.Duration(0)
		afterRandom := false
		interval := float64(0)

		canVisitSign := -1
		firstTime := time.Duration(0)
		randomTime := subject.RandomTime
		if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
			randomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
		}

		for i, resDispensing := range dispensing {
			dispensing[i].CanGrant = false
			// 实际发药时间
			if resDispensing.Status != 2 {
				dispensing[i].DispensingTime = 0
			}

			dispensing[i].DispensingMedicines = sortTypeMedicineNumber(dispensing[i].DispensingMedicines)

			//访视名称
			visitName := resDispensing.VisitInfo.Name
			if resDispensing.VisitSign { //计划外访视
				if resDispensing.Reissue == 1 { //补发
					visitName = resDispensing.VisitInfo.Name + "-" + locales.Tr(ctx, "export.dispensing.reissue")
				} else {
					visitName = resDispensing.VisitInfo.Name + "-" + locales.Tr(ctx, "export.dispensing.outVisit")

					if visitCycle.SetInfo.IsOpen {
						if ctx.GetHeader("Accept-Language") == "zh" {
							visitName = resDispensing.VisitInfo.Name + "-" + visitCycle.SetInfo.NameZh
						} else {
							visitName = resDispensing.VisitInfo.Name + "-" + visitCycle.SetInfo.NameEn
						}
					}
				}
			}
			dispensing[i].VisitInfo.Name = visitName
			if !resDispensing.VisitSign { //计划主访视
				dispensing[i].DTP = visitCycleInfoMap[resDispensing.VisitInfo.Number].DTPType
				// TODO 7064
				if firstTime == 0 && resDispensing.DispensingTime != 0 && visitCycleInfoMap[resDispensing.VisitInfo.Number].Interval != nil && randomTime == 0 {
					randomTime = time.Duration(time.Unix(int64(resDispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleInfoMap[resDispensing.VisitInfo.Number].Interval)).Unix())
					firstTime = resDispensing.DispensingTime
				}

				period := handlePeriod(afterRandom, visitCycle.VisitType, visitCycleInfoMap[resDispensing.VisitInfo.Number], randomTime, lastTime, resDispensing.DispensingTime, intTimeZone, &interval, attribute, sa.JoinTime)
				if resDispensing.Status == 2 { //已发药
					interval = 0
					lastTime = resDispensing.DispensingTime

					canVisitSign = i
				}
				if resDispensing.VisitInfo.Random {
					afterRandom = true
				}
				dispensing[i].ApplyTime = period.LineTime
				dispensing[i].WindowTimeMin = period.MinPeriod
				dispensing[i].WindowTimeMax = period.MaxPeriod
				dispensing[i].OutSize = period.OutSize
			}
			if resDispensing.VisitInfo.Random {
				afterRandom = true
			}

			for j, medicine := range resDispensing.DispensingMedicines {
				if isBlindedRole && IsBlindDrugMap[medicine.Name] {
					dispensing[i].DispensingMedicines[j].Name = tools.BlindData
					dispensing[i].DispensingMedicines[j].ExpirationDate = tools.BlindData
					dispensing[i].DispensingMedicines[j].BatchNumber = tools.BlindData
				}
			}
			for j, medicine := range resDispensing.OtherDispensingMedicines {
				if isBlindedRole && IsBlindDrugMap[medicine.Name] {
					dispensing[i].OtherDispensingMedicines[j].Name = tools.BlindData
					dispensing[i].OtherDispensingMedicines[j].Batch = tools.BlindData
					dispensing[i].OtherDispensingMedicines[j].ExpireDate = tools.BlindData
				}
			}
			if isBlindedRole {
				for j, medicine := range resDispensing.ReplaceMedicines {
					if IsBlindDrugMap[medicine.Name] {
						dispensing[i].ReplaceMedicines[j].Name = tools.BlindData
						dispensing[i].ReplaceMedicines[j].BatchNumber = tools.BlindData
						dispensing[i].ReplaceMedicines[j].ExpirationDate = tools.BlindData
					}
				}
				for j, medicine := range resDispensing.RealDispensingMedicines {
					if IsBlindDrugMap[medicine.Name] {
						dispensing[i].RealDispensingMedicines[j].Name = tools.BlindData
						dispensing[i].RealDispensingMedicines[j].BatchNumber = tools.BlindData
						dispensing[i].RealDispensingMedicines[j].ExpirationDate = tools.BlindData
					}
				}
				for j, medicine := range resDispensing.RealOtherDispensingMedicines {
					if IsBlindDrugMap[medicine.Name] {
						dispensing[i].RealOtherDispensingMedicines[j].Name = tools.BlindData
						dispensing[i].RealOtherDispensingMedicines[j].Batch = tools.BlindData
						dispensing[i].RealOtherDispensingMedicines[j].ExpireDate = tools.BlindData
					}
				}
				for j, medicine := range resDispensing.CancelMedicinesHistory {
					if IsBlindDrugMap[medicine.Name] {
						dispensing[i].CancelMedicinesHistory[j].Name = tools.BlindData
						dispensing[i].CancelMedicinesHistory[j].BatchNumber = tools.BlindData
						dispensing[i].CancelMedicinesHistory[j].ExpirationDate = tools.BlindData
					}
				}
			}
		}

		if canVisitSign != -1 && updateOk {
			dispensing[canVisitSign].FormulaOperation = true
		}
		if canVisitSign != -1 && visitSignOk {
			if project.Status == 0 {
				if project.Environments != nil && len(project.Environments) > 0 {
					for _, environment := range project.Environments {
						if sa.EnvironmentID == environment.ID {
							if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
								var cohorts []models.Cohort
								// 在随机不是第一阶段
								if project.ProjectInfo.Type == 3 && !firstPhase {
									cohorts = append(cohorts, environment.Cohorts[1])
								} else {
									cohorts = environment.Cohorts
								}

								for _, cohort := range cohorts {
									if sa.CohortID == cohort.ID {
										if cohort.Status == 2 || cohort.Status == 5 {
											dispensing[canVisitSign].CanVisitSign = true
										}
										if visitCycle.SetInfo.IsOpen {
											if ctx.GetHeader("Accept-Language") == "zh" {
												dispensing[canVisitSign].CanVisitSignStr = visitCycle.SetInfo.NameZh
											} else {
												dispensing[canVisitSign].CanVisitSignStr = visitCycle.SetInfo.NameEn

											}
										}
									}
								}
							} else {
								if *environment.Status == 2 || *environment.Status == 5 {
									dispensing[canVisitSign].CanVisitSign = true
								}
								if visitCycle.SetInfo.IsOpen {
									if ctx.GetHeader("Accept-Language") == "zh" {
										dispensing[canVisitSign].CanVisitSignStr = visitCycle.SetInfo.NameZh
									} else {
										dispensing[canVisitSign].CanVisitSignStr = visitCycle.SetInfo.NameEn

									}
								}
							}
						}
					}
				}
			}
		}
		if canVisitSign != -1 && reissueOk {
			if project.Status == 0 {
				if project.Environments != nil && len(project.Environments) > 0 {
					for _, environment := range project.Environments {
						if sa.EnvironmentID == environment.ID {
							if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
								var cohorts []models.Cohort
								// 在随机不是第一阶段
								if project.ProjectInfo.Type == 3 && !firstPhase {
									cohorts = append(cohorts, environment.Cohorts[1])
								} else {
									cohorts = environment.Cohorts
								}
								for _, cohort := range cohorts {
									if sa.CohortID == cohort.ID {
										if cohort.Status == 2 || cohort.Status == 5 {
											dispensing[canVisitSign].CanReissue = true
										}
									}
								}
							} else {
								if *environment.Status == 2 || *environment.Status == 5 {
									dispensing[canVisitSign].CanReissue = true
								}
							}
						}
					}
				}
			}
		}

		condition := bson.M{"customer_id": sa.CustomerID, "project_id": sa.ProjectID, "env_id": sa.EnvironmentID}
		if sa.CohortID != primitive.NilObjectID {
			condition = bson.M{"customer_id": sa.CustomerID, "project_id": sa.ProjectID, "env_id": sa.EnvironmentID, "cohort_id": sa.CohortID}
		}
		var visit models.VisitCycle
		err = tools.Database.Collection("visit_cycle").FindOne(nil, condition).Decode(&visit)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		visitCycleMap := map[string]models.VisitCycleInfo{}
		for _, info := range visit.Infos {
			visitCycleMap[info.Number] = info
		}

		//判断app受试者列表访视是否显示发放按钮 除了先随机再发放还未随机的不展示、dtp发放的，dtp订单确认后才展示发放入口
		if grantOk || grantDtpOk {
			orderIsConfirm := false
			if project.Status == 0 {
				if project.Environments != nil && len(project.Environments) > 0 {
					for _, environment := range project.Environments {
						if sa.EnvironmentID == environment.ID {
							if environment.Cohorts != nil && len(environment.Cohorts) > 0 {
								for _, cohort := range environment.Cohorts {
									if sa.CohortID == cohort.ID {
										if cohort.Status == 2 || cohort.Status == 5 {
											orderIsConfirm = true
										}
									}
								}
							} else {
								if *environment.Status == 2 || *environment.Status == 5 {
									orderIsConfirm = true
								}
							}
						}
					}
				}
			}

			if orderIsConfirm {
				var period models.Period
				firstPlanTime := time.Duration(0)
				randomTime := subject.RandomTime
				for i, appDispensing := range dispensing {
					if !appDispensing.VisitSign {
						if appDispensing.Status == 2 {
							interval = 0
							lastTime = appDispensing.DispensingTime
						}
						if appDispensing.VisitInfo.Random {
							afterRandom = true
						}
						// TODO 7064

						if subjectMap[subject.Info[0].Value.(string)].RandomTime != 0 {
							randomTime = subjectMap[subject.Info[0].Value.(string)].RandomTime
						}
						if firstPlanTime == 0 && appDispensing.DispensingTime != 0 && visitCycleInfoMap[appDispensing.VisitInfo.Number].Interval != nil && randomTime == 0 {
							randomTime = time.Duration(time.Unix(int64(appDispensing.DispensingTime), 0).Add(time.Hour * time.Duration(-24**visitCycleInfoMap[appDispensing.VisitInfo.Number].Interval)).Unix())
							firstPlanTime = appDispensing.DispensingTime
						}
						if appDispensing.ID == appDispensing.ID {
							period = handlePeriod(afterRandom, visit.VisitType, visitCycleMap[appDispensing.VisitInfo.Number], randomTime, lastTime, appDispensing.DispensingTime, intTimeZone, &interval, attribute, sa.JoinTime)
						}
					}

					dispensing[i].PlanLeftTime = ""
					if len(period.MinPeriod) != 0 {
						dispensing[i].PlanLeftTime = timestampConversion(period.MinPeriod)
					}
					dispensing[i].PlanRightTime = ""
					if len(period.MaxPeriod) != 0 {
						dispensing[i].PlanRightTime = timestampConversion(period.MaxPeriod)
					}

					if sa.RandomTime != 0 {
						//timeZone := time.Duration(intTimeZone)
						hours := time.Duration(intTimeZone)
						minutes := time.Duration((intTimeZone - float64(hours)) * 60)

						duration := hours*time.Hour + minutes*time.Minute
						formattedDate := time.Unix(int64(sa.RandomTime), 0).UTC().Add(duration).UTC().Format("2006.01.02")
						if dispensing[i].PlanLeftTime == "" {
							dispensing[i].PlanLeftTime = formattedDate
						}
						if dispensing[i].PlanRightTime == "" {
							dispensing[i].PlanRightTime = formattedDate
						}
					}

					if appDispensing.Status == 1 {
						firstRandomlyDispense := false
						for j, info := range visitCycle.Infos {
							//先随机后发药
							if info.Random == true && j == 0 {
								firstRandomlyDispense = true
							}
						}
						if appDispensing.VisitInfo.Dispensing {
							dispensing[i].CanGrant = true

						}

						if attribute.AttributeInfo.Random && attribute.AttributeInfo.Dispensing && sa.Status != 3 && firstRandomlyDispense {
							dispensing[i].CanGrant = false
						}
						if !orderIsConfirm {
							dispensing[i].CanGrant = false
						}
						if dispensing[i].CanGrant {
							break
						}

					} else if appDispensing.Status == 2 {
						condition := bson.M{"customer_id": sa.CustomerID, "project_id": sa.ProjectID, "env_id": sa.EnvironmentID, "subject_id": appDispensing.SubjectID, "dispensing_id": appDispensing.ID}
						if count, _ := tools.Database.Collection("medicine_order").CountDocuments(ctx, condition); count > 0 {
							var order models.MedicineOrder
							err = tools.Database.Collection("medicine_order").FindOne(ctx, condition).Decode(&order)
							if err != nil {
								return nil, errors.WithStack(err)
							}
							if order.Status == 6 {
								orderIsConfirm = false
								dispensing[i].CanVisitSign = false
								dispensing[i].CanReissue = false
							}
						}
					}
				}
			}
		}

		//编码规则
		rulefilter := bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID}
		if !subject.CohortID.IsZero() {
			rulefilter = bson.M{"customer_id": subject.CustomerID, "project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
		}
		var barcodeRule models.BarcodeRule
		err = tools.Database.Collection("barcode_rule").FindOne(ctx, rulefilter).Decode(&barcodeRule)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		result["codeRule"] = barcodeRule.CodeRule
		// 判断当前角色是否有扫码确认权限
		_, scanFlagOk := slice.Find(projectRolePermission.Permissions, func(index int, item string) bool {
			return "operation.subject.medicine.dispensing" == item ||
				"operation.subject.medicine.out-visit-dispensing" == item ||
				"operation.subject.medicine.reissue" == item
		})

		scanFlag := false
		if scanFlagOk {
			scanFlag = true
		}

		result["scanFlag"] = scanFlag

		// 判断是否展示筛选按钮
		screenBut := false
		if project.Status == 0 && attribute.AttributeInfo.IsScreen && (screenOk || screenDtpOk) && (subject.Status == 1) { // || subject.Status == 8
			screenBut = true
		}
		result["screenBut"] = screenBut

		// 判断是否展示随机按钮
		randomBut := false
		if attribute.AttributeInfo.IsScreen { // 开启筛选流程
			randomBut, err = RandomBut(project, attribute, visitCycle, subject, randomOk, randomDtpOk, 7)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else {
			randomBut, err = RandomBut(project, attribute, visitCycle, subject, randomOk, randomDtpOk, 1)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		result["randomBut"] = randomBut
		factorSign := false
		if randomLists != nil && len(randomLists) > 0 {
			factorSign = LayeredBl(attribute, visitCycle, randomLists[0].Design.Factors)
		}
		result["factorSign"] = factorSign

		for i, ds := range dispensing {
			if ds.DispensingMedicines != nil && len(ds.DispensingMedicines) > 0 {
				for j, medicine := range ds.DispensingMedicines {
					if medicine.BatchNumber == "" {
						dispensing[i].DispensingMedicines[j].BatchNumber = "-"
					}
					if medicine.ExpirationDate == "" {
						dispensing[i].DispensingMedicines[j].ExpirationDate = "-"
					}
				}
			}
			if ds.OtherDispensingMedicines != nil && len(ds.OtherDispensingMedicines) > 0 {
				for j, medicine := range ds.OtherDispensingMedicines {
					if medicine.Batch == "" {
						dispensing[i].OtherDispensingMedicines[j].Batch = "-"
					}
					if medicine.ExpireDate == "" {
						dispensing[i].OtherDispensingMedicines[j].ExpireDate = "-"
					}
				}
			}
			if ds.RealDispensingMedicines != nil && len(ds.RealDispensingMedicines) > 0 {
				for j, medicine := range ds.RealDispensingMedicines {
					if medicine.BatchNumber == "" {
						dispensing[i].RealDispensingMedicines[j].BatchNumber = "-"
					}
					if medicine.ExpirationDate == "" {
						dispensing[i].RealDispensingMedicines[j].ExpirationDate = "-"
					}
				}
			}
			if ds.ReplaceMedicines != nil && len(ds.ReplaceMedicines) > 0 {
				for j, medicine := range ds.ReplaceMedicines {
					if medicine.BatchNumber == "" {
						dispensing[i].ReplaceMedicines[j].BatchNumber = "-"
					}
					if medicine.ExpirationDate == "" {
						dispensing[i].ReplaceMedicines[j].ExpirationDate = "-"
					}
				}
			}
			if ds.CanRetrieval != nil && len(ds.CanRetrieval) > 0 {
				for j, medicine := range ds.CanRetrieval {
					if medicine.BatchNumber == "" {
						dispensing[i].CanRetrieval[j].BatchNumber = "-"
					}
					if medicine.ExpirationDate == "" {
						dispensing[i].CanRetrieval[j].ExpirationDate = "-"
					}
				}
			}
			if ds.OtherCanRetrieval != nil && len(ds.OtherCanRetrieval) > 0 {
				for j, medicine := range ds.OtherCanRetrieval {
					if medicine.Batch == "" {
						dispensing[i].OtherCanRetrieval[j].Batch = "-"
					}
					if medicine.ExpireDate == "" {
						dispensing[i].OtherCanRetrieval[j].ExpireDate = "-"
					}
				}
			}
			if ds.MedicineOrder != nil && len(ds.MedicineOrder) > 0 {
				for j, medicine := range ds.MedicineOrder {
					if medicine.OtherMedicines != nil && len(medicine.OtherMedicines) > 0 {
						for k, om := range medicine.OtherMedicines {
							if om.Batch == "" {
								dispensing[i].MedicineOrder[j].OtherMedicines[k].Batch = "-"
							}
							if om.ExpireDate == "" {
								dispensing[i].MedicineOrder[j].OtherMedicines[k].ExpireDate = "-"
							}
						}
					}
				}
			}
			if ds.RealOtherDispensingMedicines != nil && len(ds.RealOtherDispensingMedicines) > 0 {
				for j, medicine := range ds.RealOtherDispensingMedicines {
					if medicine.Batch == "" {
						dispensing[i].RealOtherDispensingMedicines[j].Batch = "-"
					}
					if medicine.ExpireDate == "" {
						dispensing[i].RealOtherDispensingMedicines[j].ExpireDate = "-"
					}
				}
			}
			if ds.CancelMedicinesHistory != nil && len(ds.CancelMedicinesHistory) > 0 {
				for j, medicine := range ds.CancelMedicinesHistory {
					if medicine.BatchNumber == "" {
						dispensing[i].CancelMedicinesHistory[j].BatchNumber = "-"
					}
					if medicine.ExpirationDate == "" {
						dispensing[i].CancelMedicinesHistory[j].ExpirationDate = "-"
					}
				}
			}
			if ds.OtherMedicinesHistory != nil && len(ds.OtherMedicinesHistory) > 0 {
				for j, medicine := range ds.OtherMedicinesHistory {
					if medicine.Batch == "" {
						dispensing[i].OtherMedicinesHistory[j].Batch = "-"
					}
					if medicine.ExpireDate == "" {
						dispensing[i].OtherMedicinesHistory[j].ExpireDate = "-"
					}
				}
			}
			if ds.ReplaceOtherMedicines != nil && len(ds.ReplaceOtherMedicines) > 0 {
				for j, medicine := range ds.ReplaceOtherMedicines {
					if medicine.Batch == "" {
						dispensing[i].ReplaceOtherMedicines[j].Batch = "-"
					}
					if medicine.ExpireDate == "" {
						dispensing[i].ReplaceOtherMedicines[j].ExpireDate = "-"
					}
				}
			}
			resAppDispensing = append(resAppDispensing, ds)
		}
	}
	result["dispensing"] = resAppDispensing
	result["randomTime"] = subject.RandomTime
	result["randomBl"] = attribute.AttributeInfo.Random
	result["dispensingBl"] = attribute.AttributeInfo.Dispensing
	result["isScreen"] = attribute.AttributeInfo.IsScreen
	result["subjectStatus"] = strconv.Itoa(subject.Status)
	return result, nil
}

func GetFields(subject models.Subject, cohort models.Cohort, projectType int, idx int) ([]models.Field, error) {

	var fields []models.Field
	// 条件
	filter := bson.M{"customer_id": subject.CustomerID, "env_id": subject.EnvironmentID}
	if !subject.CohortID.IsZero() {
		filter = bson.M{"customer_id": subject.CustomerID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}
	}

	// 查表单
	var form models.Form
	_ = tools.Database.Collection("form").FindOne(nil, filter).Decode(&form)
	if form.Fields != nil {
		for _, ff := range form.Fields {
			if ff.ApplicationType == nil || (ff.ApplicationType != nil && *ff.ApplicationType != 3) {
				fLabel := ff.Label
				fName := ff.Name
				if projectType == 3 {
					fLabel = ff.Label + "(" + cohort.Name + ")"
					fName = ff.Name + "" + fmt.Sprintf("%d", idx)
				}
				ff.Label = fLabel
				ff.Name = fName
				fields = append(fields, ff)
			}
		}
	}

	// 查询是否有分层因素
	var randomLists []models.RandomList
	randomFilter := filter
	randomFilter["status"] = 1
	if !subject.ProjectSiteID.IsZero() {
		randomFilter["$or"] = bson.A{
			bson.M{"site_ids": subject.ProjectSiteID},
			bson.M{"site_ids": nil},
		}
	}
	cursor, err := tools.Database.Collection("random_list").Find(nil, randomFilter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &randomLists)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	//TODO app端的分层显示
	randomFactors := getRandomFactor(randomLists)

	for _, factor := range randomFactors {
		fLabel := factor.Label
		fName := factor.Name
		if projectType == 3 {
			fLabel = factor.Label + "(" + cohort.Name + ")"
			fName = factor.Name + "" + fmt.Sprintf("%d", idx)
		}
		fields = append(fields, models.Field{
			Name:           fName,
			Label:          fLabel,
			Type:           factor.Type,
			Options:        factor.Options,
			Modifiable:     true, // 标记可修改
			Stratification: true, // 标记是分层因素
			Status:         factor.Status,
			IsCalc:         factor.IsCalc,
			CalcType:       factor.CalcType,
		})
	}
	return fields, nil
}

func GetFieldsValue(subject models.Subject, projectType int, idx int) []models.Info {
	infos := make([]models.Info, 0)
	for _, info := range subject.Info {
		//str := "" + fmt.Sprint(info.Value) // 发现float 转为字符串,后续使用会报错
		iName := info.Name
		if projectType == 3 && iName != "shortname" {
			iName = info.Name + "" + fmt.Sprintf("%d", idx)
		}
		info.Name = iName
		infos = append(infos, info)
		//if v, ok := info.Value.(int); ok {
		//	info.Value = strconv.Itoa(v)
		//}
	}
	// 补充实际分层
	if subject.ActualInfo != nil && len(subject.ActualInfo) > 0 {
		for _, info := range subject.ActualInfo {
			//str := "" + fmt.Sprint(info.Value) // 发现float 转为字符串,后续使用会报错
			iName := info.Name
			if projectType == 3 && iName != "shortname" {
				iName = info.Name + "" + fmt.Sprintf("%d", idx)
			}
			info.Name = iName + "_actual"
			infos = append(infos, info)
			//if v, ok := info.Value.(int); ok {
			//	info.Value = strconv.Itoa(v)
			//}
		}
	}

	return infos
}

// 随机按钮条件判断
func RandomBut(
	project models.Project,
	attribute models.Attribute,
	visitCycle models.VisitCycle,
	subject models.Subject,
	randomOk bool,
	randomDtpOk bool,
	subjectStatus int) (bool, error) {
	randomBut := false
	if attribute.AttributeInfo.Dispensing { // 发药
		var dispensingArray []models.DispensingHistory
		dispatchMatch := bson.M{
			"subject_id": subject.ID,
		}
		//dispatchMatch := bson.M{
		//	"project_id": subject.ProjectID,
		//	"env_id":     subject.ProjectID,
		//}
		//if subject.CohortID.IsZero(){
		//	dispatchMatch["cohort_id"] = subject.CohortID
		//}
		//dispatchMatch["subject_id"] = subject.ID
		dispensingAllCursor, _ := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
			{{"$match", dispatchMatch}},
			{{Key: "$lookup", Value: bson.M{"from": "history", "localField": "_id", "foreignField": "oid", "as": "history"}}},
			{{"$sort", bson.D{{"subject_id", 1}, {"serial_number", 1}}}},
		})
		err := dispensingAllCursor.All(nil, &dispensingArray)
		if err != nil {
			return randomBut, err
		}

		count := 0
		subjectDispensing := slice.Filter(dispensingArray, func(index int, item models.DispensingHistory) bool {
			return item.SubjectID == subject.ID
		})

		visitInfoArray := make([]models.VisitCycleInfo, 0)
		sign := false // 标记随机前发药是否完成
		hasRandom := false
		for _, visitCycleInfo := range visitCycle.Infos {
			if !visitCycleInfo.Random {
				visitInfoArray = append(visitInfoArray, visitCycleInfo)
			} else { // 碰到是随机的数据终止循环
				hasRandom = true
				break
			}
		}
		for _, dispensing := range subjectDispensing {
			if subject.ID == dispensing.SubjectID {
				if checkVisitCycle(visitInfoArray, dispensing.VisitInfo.VisitCycleInfoID) &&
					dispensing.VisitInfo.Dispensing &&
					dispensing.Status == 1 {
					count++
				}
			}
		}
		if count == 0 && hasRandom {
			sign = true
		}

		if project.Status == 0 &&
			attribute.AttributeInfo.Random &&
			(randomOk || randomDtpOk) &&
			subject.Status == subjectStatus && sign {
			randomBut = true
		}
	} else { // 不发药
		if project.Status == 0 &&
			attribute.AttributeInfo.Random &&
			(randomOk || randomDtpOk) &&
			subject.Status == subjectStatus {
			randomBut = true
		}
	}
	return randomBut, nil
}

func (s *DispensingService) GetDispensing(ctx *gin.Context, subjectID string, roleID string) (models.ResDispensingListInfo, error) {
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	hasBeConfirmOrder := false
	canReissue := false
	dispensing := make([]models.ResDispensing, 0)
	visitOID := primitive.NilObjectID
	visitOpen := ""
	// 查询该项目是否为盲法
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return models.ResDispensingListInfo{}, errors.WithStack(err)
	}

	// 查询项目
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}).Decode(&project)
	if err != nil {
		return models.ResDispensingListInfo{}, errors.WithStack(err)
	}

	// 查询中心
	var projectSite models.ProjectSite
	err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
	if err != nil {
		return models.ResDispensingListInfo{}, errors.WithStack(err)
	}

	strTimeZone, err := tools.GetSiteTimeZone(subject.ProjectSiteID)
	if err != nil {
		return models.ResDispensingListInfo{}, errors.WithStack(err)
	}
	if strTimeZone == "" {
		zone, err := tools.GetTimeZone(project.ID)
		if err != nil {
			return models.ResDispensingListInfo{}, err
		}
		//strTimeZone = fmt.Sprintf("UTC%+d", zone)
		strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	//intTimeZone, _ := strconv.ParseFloat(strings.Replace(strTimeZone, "UTC", "", 1), 64)
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)
	cohort, exist := models.GetCohort(project, subject.EnvironmentID.Hex(), subject.CohortID.Hex())
	if (project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.ProjectInfo.Number)) || (project.ProjectInfo.Type == 2 && exist && cohort.Type == 1) {
		var environment models.Environment
		for _, env := range project.Environments {
			if env.ID == subject.EnvironmentID {
				environment = env
				break
			}
		}

		// 受试者ids
		var ids []primitive.ObjectID
		// 受试者value
		var Value interface{}
		// 拿到受试者号
		for _, info := range subject.Info {
			if info.Name == "shortname" {
				Value = info.Value
			}
		}

		opts := &options.FindOptions{
			Sort: bson.D{{"_id", 1}},
		}
		subjectFilter := bson.M{
			"customer_id": subject.CustomerID,
			"project_id":  subject.ProjectID,
			"env_id":      subject.EnvironmentID,
			"info": bson.M{
				"$elemMatch": bson.M{
					"name":  "shortname",
					"value": Value,
				},
			},
			"$or": []bson.M{
				{"deleted": subject.Deleted},
				{"deleted": bson.M{"$exists": false}},
			},
		}
		subjectList := make([]models.Subject, 0)
		subjectCursor, err := tools.Database.Collection("subject").Find(nil, subjectFilter, opts)
		if err != nil {
			return models.ResDispensingListInfo{}, errors.WithStack(err)
		}
		err = subjectCursor.All(nil, &subjectList)
		if err != nil {
			return models.ResDispensingListInfo{}, errors.WithStack(err)
		}

		for _, sl := range subjectList {
			ids = append(ids, sl.ID)
		}
		for i, findSubject := range subjectList {
			var attribute models.Attribute
			var atRandomDispensing []models.ResDispensing
			hasBeConfirmOrderTmp := false
			canReissuetTmp := false
			visitOIDTmp := primitive.NilObjectID
			var visitCycle models.VisitCycle
			visitCycleMap := map[string]models.VisitCycleInfo{}
			visitCycleMap, atRandomDispensing, attribute, visitCycle, err = dispensingConfig(findSubject)
			atRandomDispensing, hasBeConfirmOrderTmp, canReissuetTmp, visitOIDTmp, err = dispensingsInfo(ctx, project, roleID, attribute, atRandomDispensing, visitCycle, findSubject, visitCycleMap, intTimeZone)
			if err != nil {
				return models.ResDispensingListInfo{}, errors.WithStack(err)
			}

			if i == 0 {
				hasBeConfirmOrder = hasBeConfirmOrderTmp
				canReissue = canReissuetTmp
				visitOID = visitOIDTmp
			}
			// 第二阶段未随机未发药 无审批中 ，允许上一阶段最后一个发药访视  访视外和补发
			if i == 1 {
				subjectIsRandom := findSubject.Status == 1 || findSubject.Status == 2 || findSubject.Status == 7 || findSubject.Status == 8
				_, ok := slice.Find(atRandomDispensing, func(index int, item models.ResDispensing) bool {
					return item.Status == 2 || item.Status == 3 //待审批状态
				})

				if !subjectIsRandom || ok {
					hasBeConfirmOrder = hasBeConfirmOrderTmp
					canReissue = canReissuetTmp
					visitOID = visitOIDTmp
				}
			}

			if visitCycle.SetInfo.IsOpen {
				if ctx.GetHeader("Accept-Language") == "en" {
					visitOpen = visitCycle.SetInfo.NameEn

				} else {
					visitOpen = visitCycle.SetInfo.NameZh
				}
			}
			for _, ard := range atRandomDispensing {
				//筛选cohort
				var cht models.Cohort
				for _, cohort := range environment.Cohorts {
					if cohort.ID == ard.CohortID {
						cht = cohort
						break
					}
				}
				ard.CohortName = cht.Name
				ard.ReRandomName = cht.ReRandomName
				ard.SubjectStatus = findSubject.Status
				dispensing = append(dispensing, ard)
			}
		}
	} else {
		visitCycleMap := map[string]models.VisitCycleInfo{}
		var attribute models.Attribute
		var visitCycle models.VisitCycle

		visitCycleMap, dispensing, attribute, visitCycle, err = dispensingConfig(subject)
		if visitCycle.SetInfo.IsOpen {
			if ctx.GetHeader("Accept-Language") == "en" {
				visitOpen = visitCycle.SetInfo.NameEn

			} else {
				visitOpen = visitCycle.SetInfo.NameZh
			}
		}

		if err != nil {
			return models.ResDispensingListInfo{}, errors.WithStack(err)
		}
		dispensing, hasBeConfirmOrder, canReissue, visitOID, err = dispensingsInfo(ctx, project, roleID, attribute, dispensing, visitCycle, subject, visitCycleMap, intTimeZone)
		if err != nil {
			return models.ResDispensingListInfo{}, errors.WithStack(err)
		}
	}
	result := make([]models.ResDispensing, 0)
	if dispensing != nil && len(dispensing) > 0 {
		for _, res := range dispensing {
			res.Tz = projectSite.Tz
			result = append(result, res)
		}
	}

	rep := models.ResDispensingListInfo{
		VisitOpen:        visitOpen,
		CanOutVisit:      hasBeConfirmOrder,
		VisitCycleInfoId: visitOID,
		ResDispensing:    result,
		CanReissue:       canReissue,
	}
	return rep, nil
}

func (s *DispensingService) GetNonBlindDispensing(ctx *gin.Context, subjectID string) ([]models.Dispensing, error) {
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	match := bson.M{"subject_id": subjectOID}
	opts := &options.FindOptions{
		Sort: bson.D{{"serial_number", 1}},
	}
	var dispensing []models.Dispensing
	cursor, err := tools.Database.Collection("dispensing").Find(nil, match, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensing)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return dispensing, nil
}

func (s *DispensingService) ReplaceDrug(ctx *gin.Context, data map[string]interface{}, edcUserName string) ([]models.ResDispensingInfo, error) {
	res, _, err := ReplaceDrugMethod(ctx, data, edcUserName, 0)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return res, nil
}

func (s *DispensingService) ReissueDispensing(ctx *gin.Context, data map[string]interface{}, sign int, edcUserName string) ([]models.ResDispensingInfo, bool, error) {
	var resOrderInfo []models.ResDispensingInfo
	ID := primitive.NilObjectID
	projectOID, _ := primitive.ObjectIDFromHex(data["project_id"].(string))
	envOID, _ := primitive.ObjectIDFromHex(data["env_id"].(string))
	customerOID, _ := primitive.ObjectIDFromHex(data["customer_id"].(string))
	//roleOID := primitive.NilObjectID

	user := models.User{}
	u, _ := ctx.Get("user")
	isBlindedRole := false

	if u != nil {
		user = u.(models.User)
		//roleOID, _ = primitive.ObjectIDFromHex(data["roleId"].(string))
		var err error
		isBlindedRole, err = tools.IsBlindedRole(data["roleId"].(string))
		if err != nil {
			return []models.ResDispensingInfo{}, false, errors.WithStack(err)
		}
	}

	cohortOID := primitive.NilObjectID
	if data["cohort_id"] != nil {
		cohortOID, _ = primitive.ObjectIDFromHex(data["cohort_id"].(string))
	}
	visitOID, _ := primitive.ObjectIDFromHex(data["visit_id"].(string))
	subjectOID, _ := primitive.ObjectIDFromHex(data["subject_id"].(string))

	// 补发DTP模式字段

	sendType := 0
	if data["send_type"] != nil {
		sendType = int(data["send_type"].(float64))
	}

	var logisticsInfo models.LogisticsInfo
	otherMedicineCount := []primitive.ObjectID{}

	if data["logistics"] != nil {
		logisticsInfo.Logistics = data["logistics"].(string)
		if data["other"] != nil {
			logisticsInfo.Other = data["other"].(string)
		}
		logisticsInfo.Number = data["number"].(string)
	}

	var project models.Project
	projectOpts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}, projectOpts).Decode(&project); err != nil {
		return nil, false, errors.WithStack(err)
	}
	var instanceRepeatNo = "0"
	var blockRepeatNo = "0"

	if sign == 1 { // 和EDC对接过来的
		if data["instanceRepeatNo"] != nil {
			instanceRepeatNo, _ = data["instanceRepeatNo"].(string)
		}
		if data["blockRepeatNo"] != nil {
			blockRepeatNo, _ = data["blockRepeatNo"].(string)
		}
	} else {
		// 查询访视下的发药数据
		var dispensingArray []models.Dispensing
		dsCursor, err := tools.Database.Collection("dispensing").Find(nil, bson.M{"subject_id": subjectOID, "visit_info.visit_cycle_info_id": visitOID})
		if err != nil {
			return nil, false, errors.WithStack(err)
		}
		err = dsCursor.All(nil, &dispensingArray)
		if err != nil {
			return nil, false, errors.WithStack(err)
		}
		count := 0
		for _, dispensing := range dispensingArray {
			if (dispensing.Status == 3 && !dispensing.VisitSign) || len(dispensing.DispensingMedicines) > 0 || len(dispensing.OtherDispensingMedicines) > 0 || len(dispensing.RealDispensingMedicines) > 0 || len(dispensing.RealOtherDispensingMedicines) > 0 {
				count++
			}
		}
		blockRepeatNo = strconv.Itoa(count)
	}
	var userName string
	if project.ProjectInfo.ConnectEdc == 1 && project.ProjectInfo.PushMode == 1 && user.Name == "" { // EDC对接项目
		userName = edcUserName
	} else {
		userName = user.Name
	}
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, false, errors.WithStack(err)

	}

	if project.ProjectInfo.Type == 3 && !tools.InRandomIsolation(project.Number) {
		//受试者ID是第一阶段的话 校验第一阶段是否全部发放完成
		envP, _ := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == envOID
		})
		env := *envP
		if env.Cohorts[0].ID == subject.CohortID {
			count, err := tools.Database.Collection("dispensing").CountDocuments(nil, bson.M{"subject_id": subjectOID, "status": 1})
			if err != nil {
				return nil, false, errors.WithStack(err)
			}
			if count == 0 {
				return nil, false, tools.BuildServerError(ctx, "subject_status_no_reissue")

			}
		}
	}

	var attribute models.Attribute
	attributeMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
	if cohortOID != primitive.NilObjectID {
		attributeMatch = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
	}
	err = tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&attribute)
	if err != nil {
		return nil, false, errors.WithStack(err)
	}

	subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

	medicineInfo := data["medicine_info"].([]interface{})
	remark := ""
	reason := ""
	if data["remark"] != nil {
		remark = data["remark"].(string)
	}
	if data["reason"] != nil {
		reason = data["reason"].(string)
	}
	var otherDispensingMedicine []models.OtherDispensingMedicine
	var othersDispensingMedicine []models.OthersDispensingMedicine
	var dispensingMedicine []models.DispensingMedicine
	var medicineNumber []string
	var histories []models.History
	var order models.MedicineOrder
	now := time.Duration(time.Now().Unix())

	oldOpenProject := false
	oldOpenProjectRes, err := tools.Database.Collection("setting_config").CountDocuments(nil, bson.M{"key": "open-project", "data": project.ProjectInfo.Number})
	if err != nil {
		return nil, false, errors.WithStack(err)
	}
	if oldOpenProjectRes == 1 && !attribute.AttributeInfo.Blind { // 如果当前cohort属性为盲态
		oldOpenProject = true
	}

	visitCycles, visitCycle, visitInfo, err := getVisitInfos(envOID, cohortOID, visitOID)
	if err != nil {
		return nil, false, errors.WithStack(err)
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 校验 受试者是否已经揭盲 退出 替换
		otherDispensingMedicine = []models.OtherDispensingMedicine{}
		othersDispensingMedicine = []models.OthersDispensingMedicine{}
		dispensingMedicine = []models.DispensingMedicine{}
		medicineNumber = []string{}
		histories = []models.History{}
		order = models.MedicineOrder{}
		status, err := checkSubjectStatus(ctx, sctx, subjectOID)

		siteOID := subject.ProjectSiteID  // 药物起运地
		storeOID := primitive.NilObjectID // 药物起运地
		// DTP
		StoreOrderID := primitive.NewObjectID()
		SiteOrderID := primitive.NewObjectID()
		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil && err != mongo.ErrNoDocuments {
			return [12]byte{}, errors.WithStack(err)
		}
		if len(projectSite.StoreHouseID) > 0 && projectSite.StoreHouseID[0] != primitive.NilObjectID {
			storeOID = projectSite.StoreHouseID[0] // 药物起运地
		}

		dispensingSort, err := getDispensingSort(sctx, subjectOID)

		nameBatch, lastDate, err := IsFirstDispensingIPInheritance(subject, attribute, dispensingSort, visitCycles, visitCycle)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		alarmCapacityInfoData, err := alarmCapacityInfo(sctx, project, envOID, projectSite, subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if err != nil {
			return nil, err
		}
		if !status {
			return nil, tools.BuildServerError(ctx, "subject_status_no_reissue")
		}

		if data["blockRepeatNo"] != nil && data["blockRepeatNo"] != "0" || (data["instanceRepeatNo"] != nil && data["instanceRepeatNo"] != "0") {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_reissue")
		}
		if len(medicineInfo) == 0 {
			return nil, tools.BuildServerError(ctx, "subject_medicine_label_select")
		}
		checkVisit, err := checkVisitOrder(sctx, subjectOID, visitOID, true)
		if err != nil {
			return nil, err
		}
		if !checkVisit {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_order")
		}

		// 校验通用订单是否存在待确认订单
		checkConfirm, err := checkConfirmOrder(sctx, subjectOID, 6)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if !checkConfirm {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_order_confrim")
		}

		if !visitInfo.DTP && sendType != 0 {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_order_dtp")
		}

		allDrugMap, err := tools.AllDrugMap(envOID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 根据供应计划 获取 各药物不发放日期
		expireDateKey, dispensingAlarmNumber, err := getExpireDateKey(subjectOID)
		if err != nil {
			return nil, err
		}

		// 开放药物
		openDrug := models.OpenDrug{
			MedicineInfo:             medicineInfo,
			VisitName:                visitInfo.Name,
			VisitSign:                true,
			Subject:                  subject,
			ExpireDateKey:            expireDateKey,
			SendType:                 sendType,
			OtherMedicineCount:       &otherMedicineCount,
			OtherDispensingMedicine:  &otherDispensingMedicine,
			OthersDispensingMedicine: &othersDispensingMedicine,
			DispensingMedicine:       &dispensingMedicine,
			Attribute:                attribute,
			UserName:                 userName,
			User:                     user,
			MedicineNumber:           &medicineNumber,
			SiteOID:                  siteOID,
			StoreOID:                 storeOID,
			SiteOrderOID:             StoreOrderID,
			StoreOrderOID:            StoreOrderID,
			Histories:                &histories,
			Now:                      now,
			DispensingType:           2,
			DispensingAlarmNumber:    dispensingAlarmNumber,
			AllDrugMap:               allDrugMap,
			OldOpenProject:           oldOpenProject,
			AlarmCapacityInfoData:    alarmCapacityInfoData,
			NameBatch:                nameBatch,
			LastDate:                 lastDate,
		}
		err = getOpenDrug(ctx, sctx, openDrug)
		if err != nil {
			return nil, err
		}

		// 受试者发药新增数据
		dispensingOID := primitive.NewObjectID()
		ID = dispensingOID
		// 查询访视周期序列值
		var dispensingModel models.Dispensing
		opts := &options.FindOneOptions{
			Sort: bson.D{{"serial_number", -1}},
		}
		err = tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"visit_info.visit_cycle_info_id": visitInfo.ID, "subject_id": subjectOID},
			opts).Decode(&dispensingModel)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		dispensing := models.Dispensing{
			ID:            dispensingOID,
			CustomerID:    customerOID,
			ProjectID:     projectOID,
			EnvironmentID: envOID,
			CohortID:      cohortOID,
			SubjectID:     subjectOID,
			VisitInfo: models.VisitInfo{
				VisitCycleInfoID: visitInfo.ID,
				Number:           visitInfo.Number,
				InstanceRepeatNo: instanceRepeatNo,
				BlockRepeatNo:    blockRepeatNo,
				Name:             visitInfo.Name,
				Random:           visitInfo.Random,
				Dispensing:       visitInfo.Dispensing,
			},
			DispensingMedicines:       dispensingMedicine,
			OtherDispensingMedicines:  otherDispensingMedicine,
			OthersDispensingMedicines: othersDispensingMedicine,
			Status:                    2,
			Reissue:                   1,
			DispensingTime:            now,
			Reasons:                   []models.Reason{},
			ReplaceMedicines:          []models.ReplaceMedicines{},
			CancelMedicinesHistory:    []models.DispensingMedicine{},
			SerialNumber:              dispensingModel.SerialNumber + 1,
			VisitSign:                 true,
			OtherMedicinesHistory:     []models.OtherDispensingMedicineInfo{},
		}
		for _, other := range otherDispensingMedicine {
			item := models.OtherDispensingMedicineInfo{
				ID:              other.ID,
				MedicineOtherID: other.MedicineOtherID,
				Name:            other.Name,
				Count:           other.Count,
				Batch:           other.Batch,
				ExpireDate:      other.ExpireDate,
				Time:            other.Time,
				Type:            other.Type,
				UseFormulas:     other.UseFormulas,
				DoseInfo:        other.DoseInfo,
			}
			dispensing.OtherMedicinesHistory = append(dispensing.OtherMedicinesHistory, item)
		}
		// 新增订单
		if sendType != 0 {
			orderOID := SiteOrderID
			sendOID := siteOID
			if sendType == 2 {
				orderOID = StoreOrderID
				sendOID = storeOID

			}
			tmpResOrderInfo, err := s.OrderSend(ctx, sctx, isBlindedRole, subject, dispensing, sendType, sendOID, orderOID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if len(tmpResOrderInfo) > 0 {
				dispensing.Order = tmpResOrderInfo[0].Order
			}
			resOrderInfo = append(resOrderInfo, tmpResOrderInfo...)
		} else { // 药物DTP
			_, siteOK := slice.Find(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
				return *item.DTP == 1
			})
			_, siteOtherOK := slice.Find(dispensing.OthersDispensingMedicines, func(index int, item models.OthersDispensingMedicine) bool {
				return *item.DTP == 1
			})
			if siteOK || siteOtherOK {
				tmpResOrderInfo, err := s.OrderSend(ctx, sctx, isBlindedRole, subject, dispensing, 1, siteOID, SiteOrderID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				resOrderInfo = append(resOrderInfo, tmpResOrderInfo...)
			}
			_, storeOK := slice.Find(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
				return *item.DTP == 2
			})
			_, storeOtherOK := slice.Find(dispensing.OthersDispensingMedicines, func(index int, item models.OthersDispensingMedicine) bool {
				return *item.DTP == 2
			})
			if storeOK || storeOtherOK {
				tmpResOrderInfo, err := s.OrderSend(ctx, sctx, isBlindedRole, subject, dispensing, 2, storeOID, StoreOrderID)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				resOrderInfo = append(resOrderInfo, tmpResOrderInfo...)
			}
		}

		//添加app任务,判断是否是系统编码
		barcode_rule_filter := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
		if data["cohort_id"] != nil {
			barcode_rule_filter["cohort_id"] = cohortOID
		}
		var barcodeRule models.BarcodeRule
		err = tools.Database.Collection("barcode_rule").FindOne(nil, barcode_rule_filter).Decode(&barcodeRule)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		if barcodeRule.CodeRule == 1 && len(dispensingMedicine) > 0 { // 系统自动编码
			permissions := []string{"operation.subject.medicine.reissue"}
			siteOrStoreIDs := []primitive.ObjectID{subject.ProjectSiteID}
			userIds, err := tools.GetPermissionUserIds(sctx, permissions, projectOID, envOID, siteOrStoreIDs...)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			//新增工作任务
			workTaskId := primitive.NewObjectID()
			if len(userIds) > 0 {
				dispensing.WorkTaskId = workTaskId
				var medicineIds []primitive.ObjectID
				for _, v := range dispensingMedicine {
					medicineIds = append(medicineIds, v.MedicineID)
				}
				workTask := models.WorkTask{
					ID:            workTaskId,
					CustomerID:    customerOID,
					ProjectID:     projectOID,
					EnvironmentID: envOID,
					CohortID:      cohortOID,
					UserIDs:       userIds,
					// UserID:        user.ID,
					// RoleID:        roleOID,
					Info: models.WorkTaskInfo{

						WorkType:        5,
						Status:          0,
						CreatedTime:     time.Duration(time.Now().Unix()),
						Deadline:        time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
						MedicineIDs:     medicineIds,
						MedicineOrderID: primitive.NilObjectID,
						DispensingID:    dispensingOID,
					},
				}
				_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}

		if _, err = tools.Database.Collection("dispensing").InsertOne(sctx, dispensing); err != nil {
			return nil, errors.WithStack(err)
		}
		var emailMedicine []string
		for _, item := range medicineNumber {
			emailMedicine = append(emailMedicine, item)
		}
		sort.Strings(emailMedicine)
		for _, item := range otherDispensingMedicine {
			if item.Batch == "" {
				item.Batch = "-"
			}
			if item.ExpireDate == "" {
				item.ExpireDate = "-"
			}
			emailMedicine = append(emailMedicine, item.Name+"/"+strconv.Itoa(item.Count)+"/"+item.Batch+"/"+item.ExpireDate)
			//otherMedicine = append(otherMedicine, item.Name+"/"+strconv.Itoa(item.Count)+"/"+item.Batch+"/"+item.ExpireDate)
		}
		mails := make([]models.Mail, 0)
		subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
		subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)
		mails, err = mail(ctx, mails, dispensing, dispensing.DispensingTime, now, "", visitCycle, visitInfo, emailMedicine, "dispensing.reissue-title", []string{}, subjectEmailReplaceTextZh, subjectEmailReplaceTextEn, attribute, remark, order.OrderNumber, reason)
		if err != nil {
			return nil, err
		}
		ctx.Set("MAIL", mails)
		if len(mails) > 0 {
			var envs []models.MailEnv
			for _, m := range mails {
				envs = append(envs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     m.ID,
					CustomerID: dispensing.CustomerID,
					ProjectID:  dispensing.ProjectID,
					EnvID:      dispensing.EnvironmentID,
					CohortID:   dispensing.CohortID,
				})
			}
			ctx.Set("MAIL-ENV", envs)
		}
		// 发药轨迹
		sort.Strings(medicineNumber)

		history, err := getOtherMedicineHistory(dispensing, dispensing.OthersDispensingMedicines, siteOID, storeOID, "", user, userName, now)
		if err != nil {
			return nil, err
		}
		histories = append(histories, history...)

		if sendType != 0 {

			//for _, medicine := range dispensing.OtherDispensingMedicines {
			//	history := models.History{
			//		Key:  "history.medicine.otherToBeConfirm",
			//		OID:  medicine.MedicineOtherID,
			//		Data: bson.M{"name": medicine.Name, "batch": medicine.Batch, "expireDate": medicine.ExpireDate, "count": medicine.Count},
			//		Time: now,
			//		UID:  user.ID,
			//		User: userName,
			//	}
			//	histories = append(histories, history)
			//}
			var medicinesInfo []map[string]interface{}
			err = slice.SortByField(dispensing.DispensingMedicines, "Number")
			if err != nil {
				return nil, err
			}
			for _, medicine := range dispensing.DispensingMedicines {
				medicinesInfo = append(medicinesInfo, map[string]interface{}{
					"number": medicine.Number,
					"name":   medicine.Name,
					"blind":  true,
				})
			}
			for _, medicine := range dispensing.OtherDispensingMedicines {
				medicinesInfo = append(medicinesInfo, map[string]interface{}{
					"number":     medicine.Count,
					"name":       medicine.Name,
					"batch":      medicine.Batch,
					"expireDate": medicine.ExpireDate,
					"blind":      false,
				})
			}
		} else {
			//for _, medicine := range dispensing.OtherDispensingMedicines {
			//	history := models.History{
			//		Key:  "history.medicine.otherUse",
			//		OID:  medicine.MedicineOtherID,
			//		Data: bson.M{"name": medicine.Name, "batch": medicine.Batch, "expireDate": medicine.ExpireDate, "count": medicine.Count},
			//		Time: now,
			//		UID:  user.ID,
			//		User: userName,
			//	}
			//	histories = append(histories, history)
			//}
		}
		err = addDispensingHistoryData(ctx, "history.dispensing.dispensingCustomer-reissue", &histories, subjectReplaceText, subject, dispensing, models.FormulaInfo{},
			user, userName, now, sendType, nil, models.Form{}, models.FormValue{}, logisticsInfo, data, attribute)
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		//订单实时核查
		return nil, false, err

	} else {
		//订单实时核查
		if project.ProjectInfo.OrderCheck == 2 {
			err = task.AlarmMedicineNew(2, envOID)
			if err != nil {
				return nil, false, errors.WithStack(err)
			}
		}
	}

	// 推给EDC
	if tools.PushScenarioFilter(project.ConnectEdc, project.PushMode, project.EdcSupplier, project.PushScenario.DispensingPush) {
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectDispensingPush(logData, ID, 7, now)
	}
	//if project.ConnectEdc == 1 && project.PushMode == 2 && (project.PushTypeEdc == "" || project.PushTypeEdc == "OnlyDrug" || project.PushTypeEdc == "RandomAndDrug") {
	//	SubjectDispensingPush(ctx, ID, 7, now)
	//}
	return resOrderInfo, len(dispensingMedicine) > 0, nil
}

func (s *DispensingService) Cancel(ctx *gin.Context, id string, data string) error {
	ID, _ := primitive.ObjectIDFromHex(id)
	now := time.Duration(time.Now().Unix())
	var project models.Project

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var dispensing models.Dispensing
		err := tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"_id": ID}).Decode(&dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// DTP 不允许撤销
		if count, _ := tools.Database.Collection("project").CountDocuments(sctx, bson.M{"_id": dispensing.ProjectID, "info.research_attribute": 1}); count == 1 {
			return nil, tools.BuildServerError(ctx, "subject_visit_cannot_cancel")
		}

		// 校验 受试者是否已经揭盲 退出 替换
		status, err := checkSubjectStatus(ctx, sctx, dispensing.SubjectID)
		if err != nil {
			return nil, err
		}
		if !status {
			return nil, tools.BuildServerError(ctx, "subject_status_no_cancel")
		}
		var subject models.Subject
		err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询项目
		projectFilter := bson.M{"_id": subject.ProjectID}
		err = tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 修改编号研究产品 置为可用
		var histories []models.History
		u, _ := ctx.Get("user")
		user := u.(models.User)
		medicinesNumber := []string{}
		var medicine bson.A
		if len(dispensing.DispensingMedicines) > 0 {
			for _, dispensingMedicine := range dispensing.DispensingMedicines {
				medicine = append(medicine, bson.M{"_id": dispensingMedicine.MedicineID})
				histories = append(histories, models.History{
					Key:  "history.medicine.cancel",
					OID:  dispensingMedicine.MedicineID,
					Data: bson.M{"subject": subject.Info[0].Value, "visit": dispensing.VisitInfo.Name},
					Time: now,
					UID:  user.ID,
					User: user.Name,
				})
				medicinesNumber = append(medicinesNumber, dispensingMedicine.Number)

			}
			update := bson.M{"$set": bson.M{"status": 1}}
			_, err = tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"$or": medicine}, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		// 修改未编号研究产品数量
		for _, dispensingMedicine := range dispensing.OtherDispensingMedicines {
			_, err = tools.Database.Collection("medicine_other_institute").UpdateOne(sctx, bson.M{"_id": dispensingMedicine.MedicineOtherID},
				bson.M{
					"$inc": bson.M{"info.count": dispensingMedicine.Count, "info.used_count": dispensingMedicine.Count * -1},
					"$set": bson.M{"edit": true}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			histories = append(histories, models.History{
				Key:  "history.medicine.otherUse",
				OID:  dispensingMedicine.MedicineOtherID,
				Data: bson.M{"name": dispensingMedicine.Name, "batch": dispensingMedicine.Batch, "expireDate": dispensingMedicine.ExpireDate, "count": dispensingMedicine.Count},
				Time: now,
				UID:  user.ID,
				User: user.Name,
			})
			medicinesNumber = append(medicinesNumber, dispensingMedicine.Name+"/"+strconv.Itoa(dispensingMedicine.Count)+"/"+dispensingMedicine.Batch+"/"+dispensingMedicine.ExpireDate)
		}

		// 更新发药记录
		updateStatus := 1
		if len(dispensing.RealDispensingMedicines) > 0 {
			updateStatus = 2
		}

		// 写入历史轨迹

		{
			// 撤销前写入历史  报表导出需要所有记录

			newDispensing := []models.DispensingMedicine{}
			//for _, item := range dispensing.DispensingMedicines {
			//	newDispensing = append(newDispensing, item)
			//}
			// jira 4734  撤销 取回保留原来数据
			dispensing.CancelMedicinesHistory = append(dispensing.CancelMedicinesHistory, dispensing.DispensingMedicines...)

			// 撤销操作写进操作历史 报表导出需要所有记录
			for _, item := range dispensing.DispensingMedicines {
				item.Time = now
				item.Type = 7
				newDispensing = append(newDispensing, item)
			}
			dispensing.CancelMedicinesHistory = append(dispensing.CancelMedicinesHistory, newDispensing...)

		}

		update := bson.M{
			"$set": bson.M{"dispensing_medicines": nil, "other_dispensing_medicines": nil, "dispensing_time": nil, "status": updateStatus},
		}
		reason := models.Reason{
			ReasonID:   primitive.NewObjectID(),
			ReasonType: 2,
			ReasonTime: now,
			Reason:     data,
		}
		{
			//for _, dispensingMedicine := range dispensing.OtherDispensingMedicines {
			//	item := models.OtherDispensingMedicineInfo{
			//		ID:              dispensingMedicine.ID,
			//		MedicineOtherID: dispensingMedicine.MedicineOtherID,
			//		Name:            dispensingMedicine.Name,
			//		Count:           dispensingMedicine.Count,
			//		Batch:           dispensingMedicine.ExpireDate,
			//		Time:            dispensingMedicine.Time,
			//		Type:            dispensingMedicine.Type,
			//	}
			//	dispensing.OtherMedicinesHistory = append(dispensing.OtherMedicinesHistory, item)
			//}
			//
			for _, dispensingMedicine := range dispensing.OtherDispensingMedicines {
				item := models.OtherDispensingMedicineInfo{
					ID:              dispensingMedicine.ID,
					MedicineOtherID: dispensingMedicine.MedicineOtherID,
					Name:            dispensingMedicine.Name,
					Count:           dispensingMedicine.Count,
					Batch:           dispensingMedicine.ExpireDate,
					Time:            now,
					Type:            7,
				}
				dispensing.OtherMedicinesHistory = append(dispensing.OtherMedicinesHistory, item)
			}
		}
		dispensing.Reasons = append(dispensing.Reasons, reason)
		if len(dispensing.RealDispensingMedicines) > 0 || len(dispensing.RealOtherDispensingMedicines) > 0 {
			update = bson.M{
				"$set": bson.M{
					"dispensing_medicines":       nil,
					"other_dispensing_medicines": nil,
					"reasons":                    dispensing.Reasons,
					"cancel_medicines_history":   dispensing.CancelMedicinesHistory,
					"other_medicines_history":    dispensing.OtherMedicinesHistory,
				},
			}
		} else {
			update = bson.M{
				"$set": bson.M{"dispensing_medicines": nil, "other_dispensing_medicines": nil, "dispensing_time": nil, "status": updateStatus,
					"reasons":                  dispensing.Reasons,
					"cancel_medicines_history": dispensing.CancelMedicinesHistory,
					"other_medicines_history":  dispensing.OtherMedicinesHistory,
				},
			}
		}

		_, err = tools.Database.Collection("dispensing").UpdateOne(sctx, bson.M{"_id": ID}, update)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 发药轨迹
		var attribute models.Attribute
		if dispensing.CohortID != primitive.NilObjectID {
			err = tools.Database.Collection("attribute").FindOne(sctx, bson.M{"env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID}).Decode(&attribute)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else {
			err = tools.Database.Collection("attribute").FindOne(sctx, bson.M{"env_id": dispensing.EnvironmentID}).Decode(&attribute)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

		history := models.History{
			Key:  "history.dispensing.cancel",
			OID:  ID,
			Data: map[string]interface{}{"label": subjectReplaceText, "subject": subject.Info[0].Value, "reason": data},
			Time: now,
			UID:  user.ID,
			User: user.Name,
		}
		histories = append(histories, history)
		ctx.Set("HISTORY", histories)
		visitCycle, visitInfo, err := getVisitInfo(dispensing.CustomerID, dispensing.ProjectID, dispensing.EnvironmentID, dispensing.CohortID, dispensing.VisitInfo.VisitCycleInfoID)

		mails := make([]models.Mail, 0)
		subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
		subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)
		mails, err = mail(ctx, mails, dispensing, dispensing.DispensingTime, now, "", visitCycle, visitInfo, medicinesNumber, "dispensing.retrieval-title", []string{}, subjectEmailReplaceTextZh, subjectEmailReplaceTextEn, attribute, "", "", "")
		ctx.Set("MAIL", mails)
		if len(mails) > 0 {
			var envs []models.MailEnv
			for _, m := range mails {
				envs = append(envs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     m.ID,
					CustomerID: dispensing.CustomerID,
					ProjectID:  dispensing.ProjectID,
					EnvID:      dispensing.EnvironmentID,
					CohortID:   dispensing.CohortID,
				})
			}
			ctx.Set("MAIL-ENV", envs)
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	// 推给EDC
	if tools.PushScenarioFilter(project.ConnectEdc, project.PushMode, project.EdcSupplier, project.PushScenario.DispensingPush) {
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectDispensingPush(logData, ID, 8, now)
	}
	//if project.ConnectEdc == 1 && project.PushMode == 2 && (project.PushTypeEdc == "" || project.PushTypeEdc == "OnlyDrug" || project.PushTypeEdc == "RandomAndDrug") {
	//	SubjectDispensingPush(ctx, ID, 8, now)
	//}
	return nil
}

func (s *DispensingService) ExportDispensing(ctx *gin.Context, customerID string, projectID string, envID string) error {
	// 选择中心导出
	// 不选择中心，根据用户分配的中心
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	envOID, _ := primitive.ObjectIDFromHex(envID)
	projectOID, _ := primitive.ObjectIDFromHex(projectID)

	projectSiteID := ctx.DefaultQuery("projectSiteId", "")
	cohortID := ctx.DefaultQuery("cohortId", "")
	roleId := ctx.Query("roleId")
	//room, _ := strconv.Atoi(ctx.DefaultQuery("room", "2"))

	var attribute models.Attribute
	attributeFilter := bson.M{"env_id": envOID}
	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		attributeFilter = bson.M{"env_id": envOID, "cohort_id": cohortOID}
	}
	err := tools.Database.Collection("attribute").FindOne(nil, attributeFilter).Decode(&attribute)
	if err != nil {
		return errors.WithStack(err)
	}

	subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

	match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "deleted": bson.M{"$ne": true}}
	var project models.Project
	tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if projectSiteID != "" {
		projectSiteOID, _ := primitive.ObjectIDFromHex(projectSiteID)
		match["project_site_id"] = projectSiteOID
	} else {
		// study 角色不过滤
		study, err := tools.RoleIsStudy(roleId)
		if err != nil {
			return errors.WithStack(err)
		}
		if !study {
			siteOID, err := tools.GetRoleSite(ctx, envID)
			if err != nil {
				return errors.WithStack(err)
			}
			if len(siteOID) > 0 {
				match["project_site_id"] = bson.M{"$in": siteOID}
			}
		}
	}

	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		match["cohort_id"] = cohortOID
	}

	var data []map[string]interface{}

	{

		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$unwind", Value: "$info"}},
			{{Key: "$match", Value: bson.M{"info.name": "shortname"}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "dispensing",
				"localField":   "_id",
				"foreignField": "subject_id",
				"as":           "dispensing",
			}}},
			{{Key: "$lookup", Value: bson.M{
				"from":         "project_site",
				"localField":   "project_site_id",
				"foreignField": "_id",
				"as":           "project_site",
			}}},
			{{Key: "$unwind", Value: "$dispensing"}},
			{{Key: "$unwind", Value: "$project_site"}},
			//{{Key: "$match", Value: bson.M{"dispensing.status": 2}}},
			{{Key: "$project", Value: bson.M{
				"_id":                      1,
				"country":                  bson.M{"$first": "$project_site.country"},
				"serial_number":            "$dispensing.serial_number",
				"site":                     "$project_site.number",
				"name":                     models.ProjectSiteNameLookUpBson(ctx),
				"timeZone":                 "$project_site.time_zone",
				"subject":                  "$info.value",
				"visit":                    "$dispensing.visit_info.name",
				"time":                     "$dispensing.dispensing_time",
				"visit_sign":               "$dispensing.visit_sign",
				"reissue":                  "$dispensing.reissue",
				"medicine":                 "$dispensing.dispensing_medicines",
				"realMedicine":             "$dispensing.real_dispensing_medicines",
				"replace_medicines":        "$dispensing.replace_medicines",
				"other_medicines":          "$dispensing.other_dispensing_medicines",
				"cancel_medicines_history": "$dispensing.cancel_medicines_history",
				"room":                     "$room_number",
				"medicines": bson.M{"$setUnion": bson.A{
					bson.M{"$ifNull": bson.A{"$dispensing.replace_medicines", bson.A{}}},
					bson.M{"$ifNull": bson.A{"$dispensing.dispensing_medicines", bson.A{}}},
					bson.M{"$ifNull": bson.A{"$dispensing.cancel_medicines_history", bson.A{}}},
				}},
			}}},
			{{Key: "$unwind", Value: bson.M{"path": "$medicines", "preserveNullAndEmptyArrays": true}}},
			{{Key: "$sort", Value: bson.D{{"site", 1}, {"_id", 1}, {"serial_number", 1}, {"medicines.time", 1}}}},
		}
		optTrue := true
		opt := &options.AggregateOptions{
			AllowDiskUse: &optTrue,
		}
		cursor, err := tools.Database.Collection("subject").Aggregate(nil, pipeline, opt)
		if err != nil {
			return errors.WithStack(err)
		}
		err = cursor.All(nil, &data)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	title := []interface{}{
		locales.Tr(ctx, "export.dispensing.siteNumber"),
		locales.Tr(ctx, "export.dispensing.siteName"),
		locales.Tr(ctx, "export.dispensing.room"),
		subjectReplaceText,
		locales.Tr(ctx, "export.dispensing.visit"),
		locales.Tr(ctx, "export.dispensing.time"),
		locales.Tr(ctx, "export.dispensing.type"),
		locales.Tr(ctx, "export.dispensing.medicine"),
		locales.Tr(ctx, "export.dispensing.replaceMedicine"),
	}
	countries := bson.M{}
	if attribute.AttributeInfo.CountryLayered {
		countries, err = database.GetCountries(ctx)
		title = append([]interface{}{locales.Tr(ctx, "common.country")}, title...)
	}

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return err
	}

	if !isBlindedRole || !attribute.AttributeInfo.Blind {
		title = append(title, locales.Tr(ctx, "export.dispensing.medicineName"))
	}

	title = append(title, locales.Tr(ctx, "export.dispensing.otherMedicineCount"))

	title = append(title, locales.Tr(ctx, "export.dispensing.realMedicine"))

	var projectInfo []map[string]interface{}
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": projectOID}}},
		{{Key: "$unwind", Value: "$envs"}},
		{{Key: "$match", Value: bson.M{"envs.id": envOID}}},
		{
			{
				Key: "$project",
				Value: bson.M{
					"_id":      0,
					"number":   "$info.number",
					"name":     "$info.name",
					"env":      "$envs.name",
					"timeZone": "$info.timeZoneStr",
				},
			},
		},
	}
	cursor, err := tools.Database.Collection("project").Aggregate(nil, pipeline)
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &projectInfo)
	if err != nil {
		return errors.WithStack(err)
	}

	var timeZone float64
	if projectInfo[0]["timeZone"] != nil {
		//timeZone = projectInfo[0]["timeZone"].(int32)
		timeZone, _ = strconv.ParseFloat(projectInfo[0]["timeZone"].(string), 64)
	} else {
		timeZone = float64(8)
	}
	hours := time.Duration(timeZone)
	minutes := time.Duration((timeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute

	roomShow, err := tools.IsBlindedRole(roleId)
	if err != nil {
		return errors.WithStack(err)
	}
	if !attribute.AttributeInfo.Blind {
		roomShow = false
	}
	var content [][]interface{}

	for _, item := range data {
		replace := true
		replaceIndex := 6
		if item["medicines"] != nil {
			var tmp []interface{}
			if attribute.AttributeInfo.CountryLayered {
				replaceIndex = 7
				if _, ok := item["country"]; ok {
					tmp = append(tmp, countries[item["country"].(string)])
				} else {
					tmp = append(tmp, "")
				}
			}

			tmp = append(tmp, item["site"].(string)) // 中心编号
			tmp = append(tmp, item["name"].(string)) // 中心名称
			if item["room"] != nil && !roomShow {
				tmp = append(tmp, item["room"].(string)) // 房间号
			} else {
				tmp = append(tmp, "") // 房间号
			}
			tmp = append(tmp, item["subject"].(string)) // 受试者

			// 访视名称-访视类型
			if item["visit_sign"].(bool) {
				if int(item["reissue"].(int32)) == 1.0 {
					tmp = append(tmp, item["visit"].(string)+"-"+locales.Tr(ctx, "export.dispensing.reissue"))
				} else {
					tmp = append(tmp, item["visit"].(string)+"-"+locales.Tr(ctx, "export.dispensing.outVisit"))
				}
			} else {
				tmp = append(tmp, item["visit"].(string))
			}

			// 操作时间
			if item["time"] != 0.0 {
				if _, ok := item["medicines"].(map[string]interface{})["time"]; ok {
					//
					times := item["medicines"].(map[string]interface{})["time"].(int64)
					if item["medicines"].(map[string]interface{})["type"] != nil && (int(item["medicines"].(map[string]interface{})["type"].(int32)) == 8.0 || int(item["medicines"].(map[string]interface{})["type"].(int32)) == 9.0) {
						if item["realMedicine"] != nil {
							for _, realMedicine := range item["realMedicine"].(primitive.A) {
								if realMedicine.(map[string]interface{})["real_medicine_id"] == item["medicines"].(map[string]interface{})["medicine_id"] &&
									realMedicine.(map[string]interface{})["medicine_id"] == item["medicines"].(map[string]interface{})["real_medicine_id"] || (realMedicine.(map[string]interface{})["real_medicine_id"] == item["medicines"].(map[string]interface{})["medicine_id"] &&
									item["medicines"].(map[string]interface{})["medicine_new_id"] != nil) {
									times = realMedicine.(map[string]interface{})["time"].(int64)
								}
							}
						}
					}

					if item["timeZone"] != nil && item["timeZone"] != "" {
						//siteTimeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
						siteTimeZone, _ := tools.ParseTimezoneOffset(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
						var dataTime string
						hour := time.Duration(siteTimeZone)
						minute := time.Duration((siteTimeZone - float64(hour)) * 60)
						durat := hour*time.Hour + minute*time.Minute
						dataTime = time.Unix(time.Duration(times).Nanoseconds(), 0).UTC().Add(durat).Format("2006-01-02 15:04:05")
						dataTime = dataTime + "(" + item["timeZone"].(string) + ")"
						tmp = append(tmp, dataTime)
					} else {
						var dataTime string
						dataTime = time.Unix(time.Duration(times).Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						strTimeZone := tools.FormatOffsetToZoneString(timeZone)
						dataTime = dataTime + "(" + strTimeZone + ")"
						tmp = append(tmp, dataTime)
					}

				} else {

					// 旧数据 无药物时间的 使用发药时间

					if item["timeZone"] != nil && item["timeZone"] != "" {
						//SiteTimeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
						SiteTimeZone, _ := tools.ParseTimezoneOffset(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
						var dataTime string
						hour := time.Duration(SiteTimeZone)
						minute := time.Duration((SiteTimeZone - float64(hour)) * 60)
						durati := hour*time.Hour + minute*time.Minute
						dataTime = time.Unix(item["time"].(int64), 0).UTC().Add(durati).Format("2006-01-02 15:04:05")
						dataTime = dataTime + "(" + item["timeZone"].(string) + ")"
						tmp = append(tmp, dataTime)
					} else {
						var dataTime string
						dataTime = time.Unix(time.Duration(item["time"].(int64)).Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
						strTimeZone := tools.FormatOffsetToZoneString(timeZone)
						dataTime = dataTime + "(" + strTimeZone + ")"
						tmp = append(tmp, dataTime)
					}

				}
			} else {
				tmp = append(tmp, "")
			}

			// 操作类型
			if project.ResearchAttribute == 0 {
				// 通用模式
				if _, ok := item["medicines"].(map[string]interface{})["type"]; ok {
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 1 {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.first"))
					}
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 2.0 {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.reissue"))
					}
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 3.0 {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.replace"))
					}
					// 记录取回数据
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 6.0 {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.register"))
					}
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 7.0 {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.cancel"))
					}

					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 0 {
						tmp = append(tmp, "")
					}
				} else { // 旧数据处理
					if item["visit_sign"].(bool) {
						if int(item["reissue"].(int32)) == 1.0 {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.reissue"))
						} else {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.first"))
						}
					} else {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.first"))
					}
				}
			} else {
				// DTP 模式
				if _, ok := item["medicines"].(map[string]interface{})["type"]; ok {
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 1 {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.visit_apply"))
					}
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 2.0 {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.reissue_apply"))
					}
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 3.0 {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.replace_apply"))
					}
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 5.0 {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.out_visit_apply"))
					}
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 8.0 {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.register"))
					}
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 9.0 {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.register"))
					}
					if int(item["medicines"].(map[string]interface{})["type"].(int32)) == 0 {
						tmp = append(tmp, "")
					}
				} else {
					if item["visit_sign"].(bool) {
						if int(item["reissue"].(int32)) == 1.0 {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.reissue_apply"))
						} else {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.out_visit_apply"))
						}
					} else {
						tmp = append(tmp, locales.Tr(ctx, "export.dispensing.visit_apply"))
					}
				}
			}

			tmp = append(tmp, item["medicines"].(map[string]interface{})["number"].(string)) // 研究产品编号

			// 已替换研究产品号
			oldNumber := ""
			if item["replace_medicines"] != nil {
				for _, replaceMedicine := range item["replace_medicines"].(primitive.A) {
					if replaceMedicine.(map[string]interface{})["new_number"].(string) == item["medicines"].(map[string]interface{})["number"].(string) {
						replace = false
						if _, ok := item["medicines"].(map[string]interface{})["type"]; ok {
							if int(item["medicines"].(map[string]interface{})["type"].(int32)) != 6.0 && int(item["medicines"].(map[string]interface{})["type"].(int32)) != 7.0 {
								tmp[replaceIndex] = locales.Tr(ctx, "export.dispensing.replace")
							}
						} else {
							tmp[replaceIndex] = locales.Tr(ctx, "export.dispensing.replace")
						}
						oldNumber = replaceMedicine.(map[string]interface{})["number"].(string)
					}
				}
			}
			tmp = append(tmp, oldNumber)

			// 研究产品名称
			if !isBlindedRole || !attribute.AttributeInfo.Blind {
				tmp = append(tmp, item["medicines"].(map[string]interface{})["name"].(string))
			}

			// 未编号研究产品数量
			tmp = append(tmp, "")

			// 实际使用产品
			var realMedicines string
			if item["realMedicine"] != nil {
				for _, realMedicine := range item["realMedicine"].(primitive.A) {
					if realMedicine.(map[string]interface{})["real_medicine_id"] == item["medicines"].(map[string]interface{})["medicine_id"] &&
						realMedicine.(map[string]interface{})["medicine_id"] == item["medicines"].(map[string]interface{})["real_medicine_id"] || (realMedicine.(map[string]interface{})["real_medicine_id"] == item["medicines"].(map[string]interface{})["medicine_id"] &&
						item["medicines"].(map[string]interface{})["medicine_new_id"] != nil) {
						realMedicines = realMedicines + realMedicine.(map[string]interface{})["number"].(string) + " "

					}
				}
			}
			tmp = append(tmp, realMedicines)

			content = append(content, tmp)
		}

		if item["other_medicines"] != nil && replace {
			for _, medicine := range item["other_medicines"].(primitive.A) {
				var tmp []interface{}
				if attribute.AttributeInfo.CountryLayered {
					if _, ok := item["country"]; ok {
						tmp = append(tmp, countries[item["country"].(string)])
					} else {
						tmp = append(tmp, "")
					}
				}
				tmp = append(tmp, item["site"].(string)) // 中心编号
				tmp = append(tmp, item["name"].(string)) // 中心名称
				if item["room"] != nil && !roomShow {
					tmp = append(tmp, item["room"].(string)) // 房间号
				} else {
					tmp = append(tmp, "") // 房间号
				}
				tmp = append(tmp, item["subject"].(string)) // 受试者

				// 访视名称-访视类型
				if item["visit_sign"].(bool) {
					if int(item["reissue"].(int32)) == 1.0 {
						tmp = append(tmp, item["visit"].(string)+"-"+locales.Tr(ctx, "export.dispensing.reissue"))
					} else {
						tmp = append(tmp, item["visit"].(string)+"-"+locales.Tr(ctx, "export.dispensing.outVisit"))
					}
				} else {
					tmp = append(tmp, item["visit"].(string))
				}

				// 操作时间
				if item["time"] != 0.0 {
					if _, ok := medicine.(map[string]interface{})["time"]; ok {
						if item["timeZone"] != nil && item["timeZone"] != "" {
							//siteTimeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
							siteTimeZone, _ := tools.ParseTimezoneOffset(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
							hour := time.Duration(siteTimeZone)
							minute := time.Duration((siteTimeZone - float64(hour)) * 60)
							durati := hour*time.Hour + minute*time.Minute
							var dataTime string
							dataTime = time.Unix(time.Duration(medicine.(map[string]interface{})["time"].(int64)).Nanoseconds(), 0).UTC().Add(durati).Format("2006-01-02 15:04:05")
							dataTime = dataTime + "(" + item["timeZone"].(string) + ")"
							tmp = append(tmp, dataTime)
						} else {
							var dataTime string
							dataTime = time.Unix(time.Duration(medicine.(map[string]interface{})["time"].(int64)).Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							strTimeZone := tools.FormatOffsetToZoneString(timeZone)
							dataTime = dataTime + "(" + strTimeZone + ")"
							tmp = append(tmp, dataTime)
						}
					} else {
						if item["timeZone"] != nil && item["timeZone"] != "" {
							//SiteTimeZone, _ := strconv.Atoi(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
							SiteTimeZone, _ := tools.ParseTimezoneOffset(strings.Replace(item["timeZone"].(string), "UTC", "", 1))
							hour := time.Duration(SiteTimeZone)
							minute := time.Duration((SiteTimeZone - float64(hour)) * 60)
							durati := hour*time.Hour + minute*time.Minute
							var dataTime string
							dataTime = time.Unix(item["time"].(int64), 0).UTC().Add(durati).Format("2006-01-02 15:04:05")
							dataTime = dataTime + "(" + item["timeZone"].(string) + ")"
							tmp = append(tmp, dataTime)
						} else {
							var dataTime string
							dataTime = time.Unix(time.Duration(item["time"].(int64)).Nanoseconds(), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
							strTimeZone := tools.FormatOffsetToZoneString(timeZone)
							dataTime = dataTime + "(" + strTimeZone + ")"
							tmp = append(tmp, dataTime)
						}
					}
				} else {
					tmp = append(tmp, "")
				}

				// 操作类型
				if project.ResearchAttribute == 0 {
					if _, ok := medicine.(map[string]interface{})["type"]; ok {
						if int(medicine.(map[string]interface{})["type"].(int32)) == 1.0 {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.first"))
						}
						if int(medicine.(map[string]interface{})["type"].(int32)) == 2.0 {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.reissue"))
						}
						if int(medicine.(map[string]interface{})["type"].(int32)) == 3.0 {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.replace"))
						}
					} else { // 旧数据处理
						if item["visit_sign"].(bool) {
							if int(item["reissue"].(int32)) == 1.0 {
								tmp = append(tmp, locales.Tr(ctx, "export.dispensing.reissue"))
							} else {
								tmp = append(tmp, locales.Tr(ctx, "export.dispensing.first"))
							}
						} else {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.first"))
						}
					}
				} else {
					if _, ok := medicine.(map[string]interface{})["type"]; ok {
						if int(medicine.(map[string]interface{})["type"].(int32)) == 1.0 {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.visit_apply"))
						}
						if int(medicine.(map[string]interface{})["type"].(int32)) == 2.0 {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.reissue_apply"))
						}
						if int(medicine.(map[string]interface{})["type"].(int32)) == 3.0 {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.replace_apply"))
						}
						if int(medicine.(map[string]interface{})["type"].(int32)) == 5.0 {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.out_visit_apply"))
						}
						if int(medicine.(map[string]interface{})["type"].(int32)) == 8.0 {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.register"))
						}
						if int(medicine.(map[string]interface{})["type"].(int32)) == 9.0 {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.register"))
						}

						if int(medicine.(map[string]interface{})["type"].(int32)) == 0 {
							tmp = append(tmp, "")
						}
					} else { // 旧数据处理
						if item["visit_sign"].(bool) {
							if int(item["reissue"].(int32)) == 1.0 {
								tmp = append(tmp, locales.Tr(ctx, "export.dispensing.reissue_apply"))
							} else {
								tmp = append(tmp, locales.Tr(ctx, "export.dispensing.out_visit_apply"))
							}
						} else {
							tmp = append(tmp, locales.Tr(ctx, "export.dispensing.visit_apply"))
						}
					}
				}

				tmp = append(tmp, "") // 研究产品编号

				tmp = append(tmp, "") // 已替换研究产品号

				// 研究产品名称
				if !isBlindedRole || !attribute.AttributeInfo.Blind {
					tmp = append(tmp, medicine.(map[string]interface{})["name"].(string))
				}

				// 未编号研究产品数量
				tmp = append(tmp, fmt.Sprintf("%d", medicine.(map[string]interface{})["count"].(int32)))

				content = append(content, tmp)
			}
		}
	}
	now := time.Now().UTC().Add(duration).Format("20060102")
	number := ""
	env := ""
	if len(projectInfo) > 0 {
		number = projectInfo[0]["number"].(string)
		env = projectInfo[0]["env"].(string)
	}
	fileName := fmt.Sprintf("%s[%s]-%s-%s.xlsx", number, env, locales.Tr(ctx, "dispense_list_download_name"), now)
	err = tools.ExportExcelStream(ctx, fileName, title, content)
	if err != nil {
		return err
	}
	return nil
}

func (s *DispensingService) UpdatePrintStatus(ctx *gin.Context, id string) error {
	OID, _ := primitive.ObjectIDFromHex(id)
	update := bson.M{
		"$set": bson.M{
			"print": 1,
		}}
	_, err := tools.Database.Collection("dispensing").UpdateOne(nil, bson.M{"_id": OID}, update)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *DispensingService) RetrievalDrug(ctx *gin.Context, id string, data map[string]interface{}) (interface{}, error) {
	res, err := RetrievalDrugMethod(ctx, id, data, 0)
	return res, err
}

func (s *DispensingService) InvalidDispensing(ctx *gin.Context, id string, roleId string, remark string) error {
	var dispensing models.Dispensing
	OID, _ := primitive.ObjectIDFromHex(id)
	// 校验 受试者是否已经揭盲 退出 替换
	now := time.Duration(time.Now().Unix())
	var subject models.Subject
	mails := make([]models.Mail, 0)

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		err := tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"_id": OID}).Decode(&dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		status, err := checkSubjectStatus(ctx, sctx, dispensing.SubjectID)
		if err != nil {
			return nil, err
		}
		if !status {
			return nil, tools.BuildServerError(ctx, "subject_status_no_join")
		}

		// 不参加校验
		if dispensing.Status == 2 {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing")
		}

		if dispensing.Status == 3 {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_join")
		}
		err = tools.Database.Collection("dispensing").FindOneAndUpdate(sctx, bson.M{"_id": OID}, bson.M{"$set": bson.M{
			"status":          3,
			"dispensing_time": now,
			"invalid_remark":  remark,
		}}).Decode(&dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//TODO 在随机项目判断发药完成后是否需要添加下一阶段的受试者
		err = DispensingAddSubject(ctx, sctx, dispensing.ProjectID, dispensing.SubjectID, "edc")
		if err != nil {
			return nil, errors.WithStack(err)
		}

		attribute, err := database.GetAttributeWithEnvCohortID(sctx, dispensing.EnvironmentID, dispensing.CohortID)

		subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
		subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)

		_, visitCycle, visitInfo, err := getVisitInfos(dispensing.EnvironmentID, dispensing.CohortID, dispensing.VisitInfo.VisitCycleInfoID)
		if err != nil {
			return nil, err
		}
		mails, err = mail(ctx, mails, dispensing, dispensing.DispensingTime, now, "", visitCycle, visitInfo, []string{}, "dispensing.not-attend-title", []string{}, subjectEmailReplaceTextZh, subjectEmailReplaceTextEn, attribute, remark, "", "")
		if err != nil {
			return nil, err
		}
		return nil, nil
	}

	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
	if err != nil {
		return errors.WithStack(err)
	}
	callbackNotice := func(sctx mongo.SessionContext) (interface{}, error) {
		err = task.UpdateNotice(sctx, 4, dispensing.EnvironmentID, dispensing.CohortID, subject.ProjectSiteID, subject.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err = tools.Transaction(callbackNotice)
	if err != nil {
		return errors.WithStack(err)
	}

	var histories []models.History
	user, err := tools.Me(ctx)
	if err != nil {
		return err
	}
	var attribute models.Attribute

	if dispensing.CohortID != primitive.NilObjectID {
		err := tools.Database.Collection("attribute").FindOne(nil, bson.M{"env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID}).Decode(&attribute)
		if err != nil {
			return errors.WithStack(err)
		}
	} else {
		err = tools.Database.Collection("attribute").FindOne(nil, bson.M{"env_id": dispensing.EnvironmentID}).Decode(&attribute)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

	//不参加访视--更新app发放通知的状态为已删除
	err = s.PatchAppDispenseTaskVoided(ctx, id)
	if err != nil {
		return errors.WithStack(err)
	}

	if len(roleId) > 0 && attribute.AttributeInfo.Dispensing {
		err = PatchAppDispenseTask(ctx, subject.ID.Hex(), roleId)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	histories = append(histories, models.History{
		Key:  "history.dispensing.invalid",
		OID:  OID,
		Data: map[string]interface{}{"label": subjectReplaceText, "subject": subject.Info[0].Value, "remark": remark},
		Time: now,
		UID:  user.ID,
		User: user.Name,
	})

	ctx.Set("HISTORY", histories)
	ctx.Set("MAIL", mails)
	return nil
}

func (s *DispensingService) RegisterDispensing(ctx *gin.Context, data *models.ParamRealDispensing) (interface{}, error) {

	OID := primitive.NilObjectID
	now := time.Duration(time.Now().Unix())
	var project models.Project
	updateRegister := false
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		subjectOID := data.SubjectID
		visitOID := data.VisitID
		number := data.Number
		u, _ := ctx.Get("user")
		user := u.(models.User)
		var dispensing models.Dispensing
		err := tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"_id": visitOID}).Decode(&dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		OID = dispensing.ID
		var subject models.Subject
		err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		expireDateKey, _, err := getExpireDateKey(dispensing.SubjectID)
		if err != nil {
			return nil, err
		}
		var attribute models.Attribute
		match := bson.M{"env_id": dispensing.EnvironmentID}
		if dispensing.CohortID != primitive.NilObjectID {
			match["cohort_id"] = dispensing.CohortID

		}
		err = tools.Database.Collection("attribute").FindOne(sctx, match).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

		var drugConfigure models.DrugConfigure
		err = tools.Database.Collection("drug_configure").FindOne(nil, match).Decode(&drugConfigure)
		if err != nil {
			return false, errors.WithStack(err)
		}

		medicineStatus := 1
		key := "history.medicine.sku-use-subject"
		beRegisterNumber := ""
		registerNumber := ""
		registerName := ""
		beRegisterName := ""
		var projectSite models.ProjectSite
		tools.Database.Collection("project").FindOne(sctx, bson.M{"_id": subject.ProjectID}).Decode(&project)
		err = tools.Database.Collection("project_site").FindOne(nil, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
		siteOID := subject.ProjectSiteID
		storeOID := primitive.NilObjectID
		order := models.MedicineOrder{}
		newOrder := models.MedicineOrder{}
		if data.RealNumber != nil {
			var beRegisterMedicine models.Medicine
			err = tools.Database.Collection("medicine").FindOne(sctx, bson.M{"_id": number}).Decode(&beRegisterMedicine)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			beRegisterName = beRegisterMedicine.Name
			realNumber := *data.RealNumber
			filterMedicine := bson.M{"number": realNumber, "status": bson.M{"$in": bson.A{1, 14}}}
			var medicine models.Medicine
			_, visitInfo, err := getVisitInfo(dispensing.CustomerID, dispensing.ProjectID, dispensing.EnvironmentID, dispensing.CohortID, dispensing.VisitInfo.VisitCycleInfoID)

			if err != nil {
				return nil, errors.WithStack(err)
			}
			// 判断药物是否在订单内
			count, err := tools.Database.Collection("medicine_order").CountDocuments(sctx, bson.M{"dispensing_id": dispensing.ID, "medicines": number})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if visitInfo.DTP && len(projectSite.StoreHouseID) > 0 && count > 0 {
				filterMedicine["$or"] = bson.A{bson.M{"site_id": subject.ProjectSiteID}, bson.M{"storehouse_id": projectSite.StoreHouseID[0]}}
			} else {
				meidicineP, ok := slice.Find(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
					return item.MedicineID == number
				})
				dispensingTime := dispensing.DispensingTime
				if ok {
					meidicineItem := *meidicineP
					dispensingTime = meidicineItem.Time
				}
				filterMedicine["site_id"] = getTransOldSiteID(subject, dispensingTime)
			}
			if project.ResearchAttribute == 1 {
				// DTP 模式下 查询是否关联仓库了， 修改查询仓库的药物数据
				if len(projectSite.StoreHouseID) == 0 {
					return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_site")
				}
				if projectSite.StoreHouseID[0] == primitive.NilObjectID {
					return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_site")
				}
				filterMedicine = bson.M{"storehouse_id": projectSite.StoreHouseID[0], "number": realNumber, "status": 1}
				//DTP 模式下 登记实际药物需判断 订单是否为待配送中后的状态（不等于订单申请状态）
				count, _ := tools.Database.Collection("medicine_order").CountDocuments(sctx, bson.M{"dispensing_id": dispensing.ID, "status": 7})
				if count == 1 {
					return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_order_status")
				}

			} else {
				// 判断 药物是否在待确认订单中 更新订单数据
				err = updateRegisterOrder(ctx, sctx, dispensing.ID, number, *data.Status, user, now)
				if err != nil {
					return nil, err
				}
			}
			err = tools.Database.Collection("medicine").FindOne(sctx, filterMedicine).Decode(&medicine)
			if err != nil && err != mongo.ErrNoDocuments {
				return nil, tools.BuildServerError(ctx, "subject_medicine_count_real")
			}
			if medicine.ID == primitive.NilObjectID {
				return nil, tools.BuildServerError(ctx, "subject_medicine_count_real")
			}

			// 保留实际发药ID
			realMedicineID := medicine.ID
			registerName = medicine.Name
			zone, err := tools.GetTimeZone(dispensing.ProjectID)
			if err != nil {
				return nil, err
			}
			date := time.Now().UTC().Add(time.Hour * time.Duration(zone)).Format("2006-01-02")
			if expireDateKey[medicine.Name] != nil {
				date = expireDateKey[medicine.Name].(string)
			}
			if date > medicine.ExpirationDate {
				return nil, tools.BuildServerError(ctx, "subject_medicine_count_real")
			}
			registerType := 8
			subjectID := primitive.NilObjectID
			isBlindedDrug, err := tools.IsBlindedDrug(dispensing.EnvironmentID, beRegisterMedicine.Name)

			// 旧数据 开放项目 + 盲态药物。 药物作废不绑定， 药物设为可用
			oldOpenProject, err := tools.Database.Collection("setting_config").CountDocuments(nil, bson.M{"key": "open-project", "data": project.ProjectInfo.Number})
			if oldOpenProject == 1 && attribute.AttributeInfo.Blind { // 如果当前cohort属性为盲态
				oldOpenProject = 0
			}
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if *data.Status == 1 {
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if isBlindedDrug && oldOpenProject == 0 {
					medicineStatus = 14
					key = "history.medicine.sku-freeze-subject"
					subjectID = dispensing.SubjectID
				}

			} else {
				registerType = 9
				medicineStatus = 6
				key = "history.medicine.sku-lost-subject"
				if isBlindedDrug && oldOpenProject == 0 {
					subjectID = dispensing.SubjectID
				}
			}

			//

			registerGroups := getRegisterGroups(drugConfigure, medicine.Name)

			// 更新发药数据
			filter := bson.M{"_id": visitOID}
			newDispensingMedicine := []models.DispensingMedicine{}
			newRealDispensingMedicine := dispensing.RealDispensingMedicines
			newCancelDispensingMedicine := dispensing.CancelMedicinesHistory
			insertMedicine := models.DispensingMedicine{
				MedicineID:     medicine.ID,
				Name:           medicine.Name,
				Number:         medicine.Number,
				ExpirationDate: medicine.ExpirationDate,
				BatchNumber:    medicine.BatchNumber,
				PackageNumber:  medicine.PackageNumber,
				Time:           now,
				Type:           4,
				RealMedicineID: number,
				RegisterGroup:  &registerGroups,
			}
			for _, dispensingMedicine := range dispensing.DispensingMedicines {
				if dispensingMedicine.MedicineID == number {
					newRealDispensingMedicine = append(newRealDispensingMedicine, insertMedicine)
					dispensingMedicine.Type = registerType
					dispensingMedicine.RealMedicineID = medicine.ID
					newCancelDispensingMedicine = append(newCancelDispensingMedicine, dispensingMedicine)
				} else {
					newDispensingMedicine = append(newDispensingMedicine, dispensingMedicine)
				}
			}
			update := bson.M{
				"$set": bson.M{
					"dispensing_medicines":      newDispensingMedicine,
					"real_dispensing_medicines": newRealDispensingMedicine,
					"cancel_medicines_history":  newCancelDispensingMedicine,
				},
			}
			_, err = tools.Database.Collection("dispensing").UpdateOne(sctx, filter, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			//更新相应的任务
			// 判断是否 取回全部编号研究产品
			if len(newDispensingMedicine) == 0 {
				//判断app端是否有发药任务，如果有，关闭任务

				determine := bson.M{
					"project_id":  dispensing.ProjectID,
					"env_id":      dispensing.EnvironmentID,
					"customer_id": dispensing.CustomerID,
					"subject_id":  dispensing.SubjectID,
					"cohort_id":   dispensing.CohortID,
				}
				dispensingList := make([]*models.Dispensing, 0)
				cursor, err := tools.Database.Collection("dispensing").Find(sctx, determine)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(nil, &dispensingList)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				//隐藏还未完成的app发药任务
				if dispensingList != nil && len(dispensingList) > 0 {
					for _, dis := range dispensingList {
						var workTask models.WorkTask
						err = tools.Database.Collection("work_task").FindOneAndUpdate(ctx, bson.M{"info.dispensing_id": dis.ID, "info.work_type": 11, "info.status": 0, "deleted": bson.M{"$ne": true}}, bson.M{"$set": bson.M{"deleted": true}}).Decode(&workTask)
						if err != nil && err != mongo.ErrNoDocuments {
							return nil, errors.WithStack(err)
						}
					}
				}
			} else {
				// 删除任务上对应取回的研究产品
				workTaskUpdate := bson.M{
					"$pull": bson.M{
						"info.medicine_ids": bson.M{"$in": []primitive.ObjectID{number}},
					},
				}
				_, err = tools.Database.Collection("work_task").UpdateOne(sctx, bson.M{"info.dispensing_id": OID, "info.work_type": 11, "info.status": 0, "deleted": false}, workTaskUpdate)
				if err != nil && err != mongo.ErrNoDocuments {
					return nil, errors.WithStack(err)
				}
			}
			var histories []models.History

			// 更新药物状态
			_, ok := slice.Find(dispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
				return item.MedicineID == number && item.DTP != nil && *item.DTP != 0
			})
			if ok { //
				// TODO 新增订单
				newOrder, err = RegisterDispensingAddOrder(sctx, medicine, dispensing, &histories, user)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			} else {
				_, err = tools.Database.Collection("medicine").UpdateOne(sctx, bson.M{"_id": medicine.ID}, bson.M{"$set": bson.M{"status": 5}})
				// 添加药物轨迹
				medicineHistory := models.History{
					Key:  "history.medicine.sku-used-subject",
					OID:  realMedicineID,
					Data: map[string]interface{}{"subject": subject.Info[0].Value, "visit": dispensing.VisitInfo.Name, "operation": 6},
					Time: now,
					UID:  user.ID,
					User: user.Name,
				}

				histories = append(histories, medicineHistory)
			}

			if err != nil {
				return nil, errors.WithStack(err)
			}
			// 登记的药物状态 更新为 可用、冻结、作废
			_ = tools.Database.Collection("medicine").FindOneAndUpdate(sctx, bson.M{"_id": number}, bson.M{"$set": bson.M{"status": medicineStatus, "subject_id": subjectID}}).Decode(&medicine)

			// 轨迹
			beRegisterNumber = medicine.Number
			registerNumber = realNumber
			u, _ := ctx.Get("user")
			user := u.(models.User)
			// 添加发药轨迹
			histroryData := bson.M{"systemMedicine": medicine.Number, "realMedicine": realNumber, "remark": data.Remark}
			dispensingKey := "history.dispensing.dispensingCustomer-register"
			dispensing.DispensingMedicines = []models.DispensingMedicine{}
			dispensing.OtherDispensingMedicines = []models.OtherDispensingMedicine{}
			dispensing.DoseInfo = nil

			dispensing.Order = newOrder.OrderNumber
			sendType := 0
			if !newOrder.ID.IsZero() {
				if newOrder.Type == 5 {
					sendType = 2
				} else {
					sendType = 1

				}
			}
			err = addDispensingHistoryData(ctx, dispensingKey, &histories, subjectReplaceText, subject, dispensing, models.FormulaInfo{},
				user, user.Name, now, sendType, nil, models.Form{}, models.FormValue{}, models.LogisticsInfo{}, histroryData, attribute)

			histories = append(histories, models.History{
				Key:  key,
				OID:  number,
				Time: now,
				Data: map[string]interface{}{"subject": subject.Info[0].Value, "visit": dispensing.VisitInfo.Name, "operation": 6},
				UID:  user.ID,
				User: user.Name,
			})

			ctx.Set("HISTORY", histories)
		} else { // 未编号登记
			from := "site"
			if data.From != nil {
				from = *data.From
			}
			beRegisterName = data.BeOther.Name

			name := *data.Name
			registerName = name
			batch := *data.Batch
			count := *data.Count
			expiration := *data.Expiration
			var newOtherDispensingMedicines []models.OtherDispensingMedicine
			////  原未编号数据
			var otherMedicine models.OtherDispensingMedicine
			beCount := 0
			updatePushID := []primitive.ObjectID{}
			beCountP := int64(count)
			var medicineOthers []models.OtherMedicine
			// 	登记的可用数量 -1  已用数量+1
			filter := bson.M{
				"env_id":          dispensing.EnvironmentID,
				"batch_number":    batch,
				"expiration_date": expiration,
				"name":            name,
				"status":          1,
			}
			sourceSend := subject.ProjectSiteID // 药物起运地
			dtpType := 0
			if from == "site" {
				filter["site_id"] = getTransOldSiteID(subject, dispensing.DispensingTime)

			} else {
				sourceSend, err = getBoundStore(ctx, sctx, subject)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				filter["storehouse_id"] = sourceSend
				dtpType = 2
				storeOID = sourceSend
			}
			cursor, err := tools.Database.Collection("medicine_others").Find(sctx, filter, &options.FindOptions{Sort: bson.M{"_id": 1}, Limit: &beCountP})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &medicineOthers)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if len(medicineOthers) != count {
				return nil, tools.BuildServerError(ctx, "subject_medicine_count_real")
			}
			beRegisterMedicine := []models.OthersDispensingMedicine{}
			registerMedicine := []models.OthersDispensingMedicine{}

			for _, medicine := range dispensing.OtherDispensingMedicines {
				if data.BeOther.Name == medicine.Name && medicine.Batch == data.BeOther.Batch && medicine.ExpireDate == data.BeOther.ExpirationDate && count > beCount {
					if medicine.Count > count {
						medicine.Count = medicine.Count - count
						newOtherDispensingMedicines = append(newOtherDispensingMedicines, medicine)
						tmpOthers := slice.Filter(dispensing.OthersDispensingMedicines, func(index int, item models.OthersDispensingMedicine) bool {
							return item.Time == medicine.Time
						})
						beRegisterMedicine = append(beRegisterMedicine, tmpOthers...)
						for i, item := range tmpOthers {
							if i == count {
								break
							}
							tmpOther := models.OthersDispensingMedicine{
								MedicineID:     medicineOthers[i].ID,
								Name:           medicineOthers[i].Name,
								ExpirationDate: medicineOthers[i].ExpirationDate,
								BatchNumber:    medicineOthers[i].BatchNumber,
								PackageNumber:  medicineOthers[i].PackageNumber,
								Time:           now,
								Type:           8,
								RealMedicineID: item.MedicineID,
							}
							dispensing.RealOthersDispensingMedicines = append(dispensing.RealOthersDispensingMedicines, tmpOther)
							tmpOther.DTP = &dtpType
							registerMedicine = append(registerMedicine, tmpOther)

							updatePushID = append(updatePushID, item.MedicineID)
						}
						beCount = count
						continue
					} else {
						beCount = beCount + medicine.Count
						tmpOthers := slice.Filter(dispensing.OthersDispensingMedicines, func(index int, item models.OthersDispensingMedicine) bool {
							return item.Time == medicine.Time && item.Name == medicine.Name && medicine.Batch == item.BatchNumber && medicine.ExpireDate == item.ExpirationDate
						})
						beRegisterMedicine = append(beRegisterMedicine, tmpOthers...)
						for i, item := range tmpOthers {
							if i == medicine.Count {
								break
							}
							tmpOther := models.OthersDispensingMedicine{
								MedicineID:     medicineOthers[i].ID,
								Name:           medicineOthers[i].Name,
								ExpirationDate: medicineOthers[i].ExpirationDate,
								BatchNumber:    medicineOthers[i].BatchNumber,
								PackageNumber:  medicineOthers[i].PackageNumber,
								Time:           now,
								Type:           8,
								RealMedicineID: item.MedicineID,
							}
							tmpOther.DTP = &dtpType
							dispensing.RealOthersDispensingMedicines = append(dispensing.RealOthersDispensingMedicines, tmpOther)
							registerMedicine = append(registerMedicine, tmpOther)

							updatePushID = append(updatePushID, item.MedicineID)

						}
						continue
					}
				}
				newOtherDispensingMedicines = append(newOtherDispensingMedicines, medicine)
			}

			if project.ResearchAttribute == 1 {
				// DTP 模式下 查询是否关联仓库了， 修改查询仓库的药物数据
				if len(projectSite.StoreHouseID) == 0 {
					return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_site")
				}
				if projectSite.StoreHouseID[0] == primitive.NilObjectID {
					return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_site")
				}
				//DTP 模式下 登记实际药物需判断 订单是否为待配送中后的状态（不等于订单申请状态）
				count, _ := tools.Database.Collection("medicine_order").CountDocuments(sctx, bson.M{"dispensing_id": dispensing.ID, "status": 7})
				if count == 1 {
					return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_order_status")
				}
			} else {
				// 判断 药物是否在待确认订单中 更新订单数据
				order, err = updateRegisterOtherMedicineOrder(ctx, sctx, dispensing.ID, updatePushID, count, *data.Status, user, now)
				if err != nil {
					return nil, err
				}
			}
			var histories []models.History

			//更新实际登记使用的
			if order.ID.IsZero() {
				updateIDs := slice.Map(medicineOthers, func(index int, item models.OtherMedicine) primitive.ObjectID {
					return item.ID
				})
				_, err = tools.Database.Collection("medicine_others").UpdateMany(sctx,
					bson.M{"_id": bson.M{"$in": updateIDs}},
					bson.M{"$set": bson.M{"status": 5}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
			} else {
				newOrder, err = RegisterDispensingOtherAddOrder(sctx, medicineOthers, dispensing, &histories, user, now)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			// 被登记药物状态更新为可用

			_, err = tools.Database.Collection("medicine_others").UpdateMany(sctx,
				bson.M{"_id": bson.M{"$in": updatePushID}},
				bson.M{"$set": bson.M{"status": 1}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			registerGroups := getRegisterGroups(drugConfigure, name)
			allCount := 0
			for _, medicine := range dispensing.OtherDispensingMedicines {
				if data.BeOther.Name == medicine.Name && medicine.Batch == data.BeOther.Batch && medicine.ExpireDate == data.BeOther.ExpirationDate {
					otherMedicine = medicine
					allCount = allCount + medicine.Count
				}
			}
			otherMedicine.Count = allCount

			info := models.OtherDispensingMedicineInfo{
				ID:            primitive.NewObjectID(),
				Name:          name,
				Count:         count,
				Batch:         batch,
				ExpireDate:    expiration,
				Time:          now,
				Type:          4,
				RegisterGroup: &registerGroups,
			}
			dispensing.RealOtherDispensingMedicines = append(dispensing.RealOtherDispensingMedicines, info) // 写入登记信息
			info.Type = 8
			info.BeInfo = &otherMedicine
			dispensing.OtherMedicinesHistory = append(dispensing.OtherMedicinesHistory, info) // 历史写入 方便报表输出
			dispensing.OthersDispensingMedicines = slice.Filter(dispensing.OthersDispensingMedicines, func(index int, item models.OthersDispensingMedicine) bool {
				_, ok := slice.Find(updatePushID, func(index int, it primitive.ObjectID) bool {
					return it == item.MedicineID
				})
				return !ok
			})
			var realDispensing models.Dispensing
			err = tools.Database.Collection("dispensing").FindOneAndUpdate(sctx,
				bson.M{"_id": dispensing.ID},
				bson.M{"$set": bson.M{
					"other_medicines_history":          dispensing.OtherMedicinesHistory,
					"other_dispensing_medicines":       newOtherDispensingMedicines,
					"others_dispensing_medicines":      dispensing.OthersDispensingMedicines,
					"real_other_dispensing_medicines":  dispensing.RealOtherDispensingMedicines,
					"real_others_dispensing_medicines": dispensing.RealOthersDispensingMedicines,
				}}).Decode(&realDispensing)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			u, _ := ctx.Get("user")
			user := u.(models.User)
			// 添加发药轨迹
			beRegisterNumber = data.BeOther.Name + "/" + strconv.Itoa(count) + "/" + data.BeOther.Batch + "/" + data.BeOther.ExpirationDate
			registerNumber = name + "/" + strconv.Itoa(count) + "/" + batch + "/" + expiration
			histroryData := bson.M{"systemMedicine": beRegisterNumber, "realMedicine": registerNumber, "remark": data.Remark}
			dispensingKey := "history.dispensing.dispensingCustomer-register"
			dispensing.DispensingMedicines = []models.DispensingMedicine{}
			dispensing.OtherDispensingMedicines = []models.OtherDispensingMedicine{}
			dispensing.DoseInfo = nil

			dispensing.Order = newOrder.OrderNumber
			sendType := 0
			if !newOrder.ID.IsZero() {
				if newOrder.Type == 5 {
					sendType = 2
				} else {
					sendType = 1

				}
			}

			err = addDispensingHistoryData(ctx, dispensingKey, &histories, subjectReplaceText, subject, dispensing, models.FormulaInfo{},
				user, user.Name, now, sendType, nil, models.Form{}, models.FormValue{}, models.LogisticsInfo{}, histroryData, attribute)

			historyKey := "history.medicine.otherUse"
			if !order.ID.IsZero() {
				historyKey = "history.medicine.confirmed"
			}
			history, err := getOtherMedicineHistory(dispensing, registerMedicine, siteOID, storeOID, historyKey, user, user.Name, now)
			if err != nil {
				return nil, err
			}

			history, err = getOtherMedicineHistory(dispensing, beRegisterMedicine, siteOID, storeOID, "history.medicine.otherCanUse", user, user.Name, now)
			if err != nil {
				return nil, err
			}
			histories = append(histories, history...)

			ctx.Set("HISTORY", histories)
		}
		visitCycle, visitInfo, err := getVisitInfo(dispensing.CustomerID, dispensing.ProjectID, dispensing.EnvironmentID, dispensing.CohortID, dispensing.VisitInfo.VisitCycleInfoID)

		mails := make([]models.Mail, 0)
		subjectEmailReplaceTextZh := GetEmailSubjectReplaceText("zh", attribute)
		subjectEmailReplaceTextEn := GetEmailSubjectReplaceText("en", attribute)
		mails, err = mail(ctx, mails, dispensing, dispensing.DispensingTime, now, registerNumber, visitCycle, visitInfo, []string{beRegisterNumber}, "dispensing.register-title", []string{}, subjectEmailReplaceTextZh, subjectEmailReplaceTextEn, attribute, data.Remark, "", "")
		if err != nil {
			return nil, err
		}
		ctx.Set("MAIL", mails)
		if len(mails) > 0 {
			var envs []models.MailEnv
			for _, m := range mails {
				envs = append(envs, models.MailEnv{
					ID:         primitive.NewObjectID(),
					MailID:     m.ID,
					CustomerID: dispensing.CustomerID,
					ProjectID:  dispensing.ProjectID,
					EnvID:      dispensing.EnvironmentID,
					CohortID:   dispensing.CohortID,
				})
			}
			ctx.Set("MAIL-ENV", envs)
		}

		// 变更组别
		updateRegister, err = checkRegisterChangeGroup(ctx, sctx, attribute, drugConfigure, subject, visitInfo.Number, beRegisterName, registerName)
		if err != nil {
			return nil, err
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return false, err
	}

	// 推给EDC
	if tools.PushScenarioFilter(project.ConnectEdc, project.PushMode, project.EdcSupplier, project.PushScenario.DispensingPush) {
		logData := tools.PrepareLogData(ctx)
		AsyncSubjectDispensingPush(logData, OID, 10, now)
	}
	//if project.ConnectEdc == 1 && project.PushMode == 2 && (project.PushTypeEdc == "" || project.PushTypeEdc == "OnlyDrug" || project.PushTypeEdc == "RandomAndDrug") {
	//	SubjectDispensingPush(ctx, OID, 10, now)
	//}
	return bson.M{"updateRegister": updateRegister}, nil
}

func (s *DispensingService) PostRecordRoomInfo(ctx *gin.Context, dispensingID string, data map[string]interface{}) error {
	dispensingOID, _ := primitive.ObjectIDFromHex(dispensingID)

	var subjectDispensing []map[string]interface{}
	cursor, err := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": dispensingOID}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "subject", // 关联的集合名称
			"let": bson.M{
				"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
			},
			"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
				bson.M{
					"$match": bson.M{
						"$expr": bson.M{
							"$and": bson.A{
								bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
								bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
							},
						},
					},
				},
			},
			"as": "subject", // 关联结果的字段名
		}}},
		{{Key: "$unwind", Value: "$subject"}},
	})
	if err != nil {
		return errors.WithStack(err)
	}

	err = cursor.All(nil, &subjectDispensing)
	if err != nil {
		return errors.WithStack(err)
	}
	subject := subjectDispensing[0]["subject"].(map[string]interface{})["_id"].(primitive.ObjectID)
	customerOID := subjectDispensing[0]["customer_id"].(primitive.ObjectID)
	projectOID := subjectDispensing[0]["project_id"].(primitive.ObjectID)
	envOID := subjectDispensing[0]["env_id"].(primitive.ObjectID)
	cohortOID := subjectDispensing[0]["cohort_id"].(primitive.ObjectID)
	projectSiteID := subjectDispensing[0]["subject"].(map[string]interface{})["project_site_id"].(primitive.ObjectID)

	randomNumber := ""
	if data["random_number"] != nil {
		randomNumber = data["random_number"].(string)
	}
	user, err := tools.Me(ctx)
	if err != nil {
		return err
	}
	DispensingRoomRecord := models.DispensingRoomRecord{
		ID:                 primitive.NewObjectID(),
		CustomerID:         customerOID,
		ProjectID:          projectOID,
		EnvironmentID:      envOID,
		CohortID:           cohortOID,
		ProjectSiteID:      projectSiteID,
		SubjectID:          subject,
		DispensingID:       dispensingOID,
		Subject:            data["subjectNo"].(string),
		Room:               data["room"].(string),
		RandomNumber:       randomNumber,
		DispensingMedicine: data["dispensingMedicines"].(string),
		DispensingTime:     data["dispensingTime"].(string),
		UserID:             user.ID,
		UserInfo:           user.UserInfo,
		Time:               time.Duration(time.Now().Unix()),
	}

	_, err = tools.Database.Collection("dispensing_room_record").InsertOne(nil, DispensingRoomRecord)
	if err != nil {
		return err
	}
	return nil
}

func (s *DispensingService) GetSubjectStatusAndRoom(ctx *gin.Context, subjectID string, roleID string) (interface{}, error) {
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)

	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var attribute models.Attribute
	match := bson.M{"env_id": subject.EnvironmentID}
	if subject.CohortID != primitive.NilObjectID {
		match["cohort_id"] = subject.CohortID
	}
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if !attribute.AttributeInfo.IsRandomNumber {
		subject.RandomNumber = ""
	}
	if attribute.AttributeInfo.Blind {
		isBlindedRole, err := tools.IsBlindedRole(roleID)
		if err != nil {
			return models.RemoteSubjectDispensing{}, errors.WithStack(err)
		}
		if isBlindedRole {
			subject.Group = tools.BlindData

		}
		isBlindedRoomRole, err := tools.IsBlindedRoomRole(roleID)
		if err != nil {
			return models.RemoteSubjectDispensing{}, errors.WithStack(err)
		}
		if isBlindedRoomRole && subject.RoomNumber != "" {
			subject.RoomNumber = tools.BlindData
		}

	}
	return subject, nil
}

func (s *DispensingService) GetDispensingRoomInfo(ctx *gin.Context, dispensingID string, roleId string) (interface{}, error) {
	dispensingOID, _ := primitive.ObjectIDFromHex(dispensingID)

	cursor, err := tools.Database.Collection("dispensing").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"_id": dispensingOID}}},
		{{Key: "$lookup", Value: bson.M{
			"from": "subject", // 关联的集合名称
			"let": bson.M{
				"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
			},
			"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
				bson.M{
					"$match": bson.M{
						"$expr": bson.M{
							"$and": bson.A{
								bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
								bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
							},
						},
					},
				},
			},
			"as": "subject", // 关联结果的字段名
		}}},
		{{Key: "$unwind", Value: "$subject"}},
		{{Key: "$project", Value: bson.M{
			"id":                         "$_id",
			"dispensing_time":            1,
			"env_id":                     1,
			"cohort_id":                  1,
			"dispensing_medicines":       "$dispensing_medicines.number",
			"other_dispensing_medicines": "$other_dispensing_medicines",
			"subject":                    bson.M{"$first": "$subject.info.value"},
			"number":                     "$subject.random_number",
			"room":                       "$subject.room_number",
			"group":                      "$subject.group",
			"project_site_id":            "$subject.project_site_id",
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var res []map[string]interface{}
	err = cursor.All(nil, &res)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(res) == 0 {
		return nil, tools.BuildCustomError("null")
	}
	data := res[0]
	response := map[string]interface{}{}
	var attribute models.Attribute
	match := bson.M{"env_id": data["env_id"]}
	if data["cohort_id"] != primitive.NilObjectID {
		match["cohort_id"] = data["cohort_id"]
	}
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": data["env_id"]}).Decode(&project)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	strTimeZone, err := tools.GetSiteTimeZone(data["project_site_id"].(primitive.ObjectID))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if strTimeZone == "" {
		zone, err := tools.GetTimeZone(project.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//strTimeZone = fmt.Sprintf("UTC%+d", zone)
		strTimeZone = tools.FormatOffsetToZoneStringUtc(zone)
	}
	//intTimeZone, _ := strconv.Atoi(strings.Replace(strTimeZone, "UTC", "", 1))
	intTimeZone, _ := tools.ParseTimezoneOffset(strTimeZone)
	hours := time.Duration(intTimeZone)
	minutes := time.Duration((intTimeZone - float64(hours)) * 60)
	duration := hours*time.Hour + minutes*time.Minute
	dataTime := time.Unix(data["dispensing_time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")

	var medicine []string

	if data["dispensing_medicines"] != nil {
		for _, item := range data["dispensing_medicines"].(primitive.A) {
			medicine = append(medicine, item.(string))
		}
	}
	if data["other_dispensing_medicines"] != nil {
		for _, item := range data["other_dispensing_medicines"].(primitive.A) {
			value := fmt.Sprintf("%s(%d)", item.(map[string]interface{})["name"], item.(map[string]interface{})["count"])
			medicine = append(medicine, value)
		}
	}

	response["subjectNo"] = data["subject"]
	response["number"] = data["number"]
	response["dispensingMedicines"] = strings.Join(medicine, " ")
	response["dispensingTime"] = dataTime + "(" + strTimeZone + ")"
	response["room"] = data["room"]
	if !attribute.AttributeInfo.IsRandomNumber {
		response["number"] = tools.BlindData
	}
	isBlindedRoomRole, err := tools.IsBlindedRoomRole(roleId)
	if err != nil {
		return nil, err
	}
	if attribute.AttributeInfo.Blind && isBlindedRoomRole {
		response["room"] = tools.BlindData
	}

	return response, nil
}

func (s *DispensingService) ExportRoomRecord(ctx *gin.Context, envID string, cohortID string, projectSiteID string, roleID string) error {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	match := bson.M{"env_id": envOID}
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"envs.id": envOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
	if cohortID != "" {
		match["cohort_id"] = cohortOID
	}
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil && err != mongo.ErrNoDocuments {
		return errors.WithStack(err)
	}
	if projectSiteID != "" {
		projectSiteOID, _ := primitive.ObjectIDFromHex(projectSiteID)
		match["project_site_id"] = projectSiteOID
	} else {
		// study 角色不过滤
		study, err := tools.RoleIsStudy(roleID)
		if err != nil {
			return errors.WithStack(err)
		}
		if !study {
			siteOID, err := tools.GetRoleSite(ctx, envID)
			if err != nil {
				return errors.WithStack(err)
			}
			if len(siteOID) > 0 {
				match["project_site_id"] = bson.M{"$in": siteOID}
			}
		}
	}
	var dispensingRoomRecords []map[string]interface{}
	cursor, err := tools.Database.Collection("dispensing_room_record").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{
			"from": "subject", // 关联的集合名称
			"let": bson.M{
				"subject_id": "$subject_id", // 将当前文档的 subject_id 赋值给变量 subject_id
			},
			"pipeline": bson.A{ // 使用 pipeline 添加过滤条件
				bson.M{
					"$match": bson.M{
						"$expr": bson.M{
							"$and": bson.A{
								bson.M{"$eq": bson.A{"$_id", "$$subject_id"}}, // 关联条件：_id 等于 subject_id
								bson.M{"$ne": bson.A{"$deleted", true}},       // 过滤条件：deleted 不为 true
							},
						},
					},
				},
			},
			"as": "subjects", // 关联结果的字段名
		}}},
		{{Key: "$lookup", Value: bson.M{"from": "project_site", "localField": "project_site_id", "foreignField": "_id", "as": "project_site"}}},
		{{"$unwind", "$project_site"}},
		{{Key: "$project", Value: bson.M{
			"subject":         1,
			"site_number":     "$project_site.number",
			"country":         bson.M{"$first": "$project_site.country"},
			"site_name":       models.ProjectSiteNameLookUpBson(ctx),
			"timeZone":        "$project_site.time_zone",
			"room":            1,
			"subject_room":    bson.M{"$first": "$subjects.room_number"},
			"dispensing_time": 1,
			"user":            "$user_info.email",
			"time":            1,
		}}},
		{{Key: "$sort", Value: bson.D{{Key: "site_number", Value: 1}, {"subject", 1}, {"time", 1}}}},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensingRoomRecords)
	if err != nil {
		return errors.WithStack(err)
	}
	roomShow, err := tools.IsBlindedRoomRole(roleID)
	if err != nil {
		return errors.WithStack(err)
	}
	if !attribute.AttributeInfo.Blind {
		roomShow = false
	}

	title := []interface{}{
		locales.Tr(ctx, "export.room.project"),
		locales.Tr(ctx, "site.number"),
		locales.Tr(ctx, "site.name"),
		locales.Tr(ctx, "subject.number"),
		locales.Tr(ctx, "export.dispensing.room"),
		locales.Tr(ctx, "export.room.history.room"),
		locales.Tr(ctx, "export.room.history.user"),
		locales.Tr(ctx, "export.room.history.time"),
	}

	countries := bson.M{}
	if attribute.AttributeInfo.CountryLayered {
		countries, err = database.GetCountries(ctx)
		title = []interface{}{
			locales.Tr(ctx, "export.room.project"),
			locales.Tr(ctx, "common.country"),
			locales.Tr(ctx, "site.number"),
			locales.Tr(ctx, "site.name"),
			locales.Tr(ctx, "subject.number"),
			locales.Tr(ctx, "export.dispensing.room"),
			locales.Tr(ctx, "export.room.history.room"),
			locales.Tr(ctx, "export.room.history.user"),
			locales.Tr(ctx, "export.room.history.time"),
		}
	}

	var content [][]interface{}
	zone, err := tools.GetTimeZone(project.ID)
	for _, record := range dispensingRoomRecords {
		var tmp []interface{}
		tmp = append(tmp, project.Number)
		if attribute.AttributeInfo.CountryLayered {
			if _, ok := record["country"]; ok {
				tmp = append(tmp, countries[record["country"].(string)])
			} else {
				tmp = append(tmp, "")
			}
		}
		tmp = append(tmp, record["site_number"])
		tmp = append(tmp, record["site_name"])
		tmp = append(tmp, record["subject"])
		if roomShow {
			tmp = append(tmp, "")
			tmp = append(tmp, "")
		} else {
			tmp = append(tmp, record["subject_room"])
			tmp = append(tmp, record["room"])
		}
		tmp = append(tmp, record["user"])

		if record["timeZone"] != nil && record["timeZone"] != "" {
			//timeZone, _ := strconv.Atoi(strings.Replace(record["timeZone"].(string), "UTC", "", 1))
			//timeZone, _ := strconv.ParseFloat(strings.Replace(record["timeZone"].(string), "UTC", "", 1), 64)
			timeZone, _ := tools.ParseTimezoneOffset(record["timeZone"].(string))
			hours := time.Duration(timeZone)
			minutes := time.Duration((timeZone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute
			var dataTime string
			dataTime = time.Unix(record["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
			dataTime = dataTime + "(" + record["timeZone"].(string) + ")"
			tmp = append(tmp, dataTime)
		} else {
			hours := time.Duration(zone)
			minutes := time.Duration((zone - float64(hours)) * 60)
			duration := hours*time.Hour + minutes*time.Minute
			var dataTime string
			dataTime = time.Unix(record["time"].(int64), 0).UTC().Add(duration).Format("2006-01-02 15:04:05")
			strTimeZone := tools.FormatOffsetToZoneStringUtc(zone)
			dataTime = dataTime + "(" + strTimeZone + ")"
			tmp = append(tmp, dataTime)
		}
		content = append(content, tmp)
	}
	fileName := fmt.Sprintf("%s_%s.xlsx", project.Number, "room_record")
	err = tools.ExportExcelStream(ctx, fileName, title, content)
	if err != nil {
		return err
	}
	return nil
}

func (s *DispensingService) UpdateDispensingVisitWithDTP(ctx *gin.Context, dispensingID string, visitID string, visitLabelIDs []interface{}, remark string, roleID string) ([]models.ResDispensingInfo, error) {
	ctx.Set("dtp", true)
	var resDispensingInfo []models.ResDispensingInfo
	var subject models.Subject
	var room string
	var attribute models.Attribute

	dispensingMedicine := []models.DispensingMedicine{}
	var otherDispensingMedicine []models.OtherDispensingMedicine
	var medicineNumber []string
	var checkDispensing models.Dispensing
	now := time.Duration(time.Now().Unix())

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		dispensingOID, _ := primitive.ObjectIDFromHex(dispensingID)
		err := tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"_id": dispensingOID}).Decode(&checkDispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if checkDispensing.Status == 2 {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing")
		}
		if checkDispensing.Status == 3 {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_join")
		}

		customerOID := checkDispensing.CustomerID
		projectOID := checkDispensing.ProjectID
		envOID := checkDispensing.EnvironmentID
		cohortOID := checkDispensing.CohortID
		subjectOID := checkDispensing.SubjectID
		visitOID, _ := primitive.ObjectIDFromHex(visitID)

		// 多个标签选择
		var visitLabelOIDs = bson.A{}
		for _, visitLabelID := range visitLabelIDs {
			visitLabelOID, _ := primitive.ObjectIDFromHex(visitLabelID.(string))
			visitLabelOIDs = append(visitLabelOIDs, visitLabelOID)
		}
		match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
		_, visitInfo, err := getVisitInfo(customerOID, projectOID, envOID, cohortOID, visitOID)

		projectFilter := bson.M{"_id": projectOID}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		attributeMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
		if cohortOID != primitive.NilObjectID {
			attributeMatch = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
		}
		err = tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 校验 受试者是否已经揭盲 退出 替换
		status, err := checkSubjectStatus(ctx, sctx, subjectOID)
		if err != nil {
			return nil, err
		}
		if !status {
			return nil, tools.BuildServerError(ctx, "subject_status_no_dispensing")
		}

		// 校验发药是否按顺序
		checkVisit, err := checkVisitOrder(sctx, subjectOID, visitOID, false)
		if err != nil {
			return nil, err
		}
		if !checkVisit {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_order")
		}

		//  DTP 模式 需判断上一个访视的发药的订单是否已经 待配送中后的状态 （非申请态）
		var dispensings []map[string]interface{}
		cursor, err := tools.Database.Collection("dispensing").Aggregate(sctx, mongo.Pipeline{
			{{"$match", bson.M{"subject_id": checkDispensing.SubjectID, "status": 2, "visit_sign": false}}},
			{{Key: "$lookup", Value: bson.M{
				"from": "medicine_order",
				"let": bson.M{
					"dispensing_id": "$_id",
					"order_number":  "$order",
				},
				"pipeline": bson.A{
					bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$dispensing_id", "$$dispensing_id"}}}},
					bson.M{"$match": bson.M{"$expr": bson.M{"$eq": bson.A{"$order_number", "$$order_number"}}}},
				},

				"as": "medicine_order",
			}}},
			{{"$unwind", "$medicine_order"}},
			{{"$sort", bson.D{{"serial_number", 1}}}},
			{{"$project", bson.M{
				"order_status": "$medicine_order.status",
			}}},
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		cursor.All(nil, &dispensings)
		if len(dispensings) > 0 {
			lastDispensingOrder := true
			for _, item := range dispensings {
				if int(item["order_status"].(int32)) == 7.0 {
					lastDispensingOrder = false
					break
				}
			}
			if !lastDispensingOrder {
				return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_order_status_last")
			}
		}

		err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		allDrugMap, err := tools.AllDrugMap(envOID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询中的供应计划
		expireDateKey, dispensingAlarmNumber, err := getExpireDateKey(subject.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 查询获取访视周期标签研究产品配置信息
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$unwind", Value: "$configures"}},
			{{Key: "$match", Value: bson.M{"configures.id": bson.M{"$in": visitLabelOIDs}}}},
		}
		drugCursor, err := tools.Database.Collection("drug_configure").Aggregate(sctx, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var drugData []models.UpdateDrugConfigure
		err = drugCursor.All(nil, &drugData)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 访视存在房间号  并且 受试者未分配房间号
		roomNumbers := []string{}
		room = subject.RoomNumber
		tmpRoom := "" // 初始化分配的房间号
		for _, drugDatum := range drugData {
			if len(drugDatum.Configures.RoomNumbers) > 0 {
				roomNumbers = drugDatum.Configures.RoomNumbers
			}
		}
		// 分配房间号处理
		if len(roomNumbers) > 0 {
			tmpRoom, err = AllocationRooms(sctx, subject, roomNumbers)
			if err != nil {
				return nil, err
			}

			if room == "" {
				room = tmpRoom
			}
		}

		// 查询中心关联的仓库
		var projectSite models.ProjectSite
		_ = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)

		if len(projectSite.StoreHouseID) == 0 || (len(projectSite.StoreHouseID) > 0 && projectSite.StoreHouseID[0] == primitive.NilObjectID) {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_site")
		}

		// 初始化 订单ID
		orderOID := primitive.NewObjectID()

		// 获取发药数据 并更新药物状态  可多标签
		var labels []string
		err = setDispensingMedicine(sctx, ctx, orderOID, drugData, expireDateKey, subject, &otherDispensingMedicine, &medicineNumber, &dispensingMedicine, attribute, &labels, projectSite.StoreHouseID[0], false, now, dispensingAlarmNumber, allDrugMap)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//// 开放药物
		//u, _ := ctx.Get("user")
		//user := u.(models.User)
		//userName := user.Name
		//openDrug := models.OpenDrug{
		//	MedicineInfo:            medicineInfo,
		//	Subject:                 subject,
		//	ExpireDateKey:           expireDateKey,
		//	SendType:                2,
		//	OtherMedicineCount:      &[]models.OtherMedicineCount{},
		//	OtherDispensingMedicine: &otherDispensingMedicine,
		//	DispensingMedicine:      &dispensingMedicine,
		//	Attribute:               attribute,
		//	UserName:                userName,
		//	User:                    user,
		//	MedicineNumber:          &medicineNumber,
		//	SourceSend:              projectSite.StoreHouseID[0],
		//	Histories:               &[]models.History{},
		//	Now:                     now,
		//}
		//err = getOpenDrug(ctx, sctx, openDrug)
		//if err != nil {
		//	return nil, errors.WithStack(err)
		//}
		instanceRepeatNo := "0"
		blockRepeatNo := "0"
		if checkDispensing.VisitInfo.InstanceRepeatNo != "" {
			instanceRepeatNo = checkDispensing.VisitInfo.InstanceRepeatNo
		}
		if checkDispensing.VisitInfo.BlockRepeatNo != "" {
			blockRepeatNo = checkDispensing.VisitInfo.InstanceRepeatNo
		}

		// 发药插入数据
		dispensing := models.Dispensing{
			ID:            dispensingOID,
			CustomerID:    customerOID,
			ProjectID:     projectOID,
			EnvironmentID: envOID,
			CohortID:      cohortOID,
			SubjectID:     subjectOID,
			VisitInfo: models.VisitInfo{
				VisitCycleInfoID: visitInfo.ID,
				Number:           visitInfo.Number,
				InstanceRepeatNo: instanceRepeatNo,
				BlockRepeatNo:    blockRepeatNo,
				Name:             visitInfo.Name,
				Random:           visitInfo.Random,
				Dispensing:       visitInfo.Dispensing,
			},
			DispensingMedicines:      dispensingMedicine,
			OtherDispensingMedicines: otherDispensingMedicine,
			VisitSign:                checkDispensing.VisitSign,
			Reissue:                  checkDispensing.Reissue,
			Status:                   2,
			DispensingTime:           now,
			Reasons:                  []models.Reason{},
			SerialNumber:             checkDispensing.SerialNumber,
			ReplaceMedicines:         []models.ReplaceMedicines{},
			Labels:                   labels,
			Remark:                   remark,
		}
		for _, other := range otherDispensingMedicine {
			item := models.OtherDispensingMedicineInfo{
				ID:              other.ID,
				MedicineOtherID: other.MedicineOtherID,
				Name:            other.Name,
				Count:           other.Count,
				Batch:           other.Batch,
				ExpireDate:      other.ExpireDate,
				Time:            other.Time,
				Type:            other.Type,
			}
			dispensing.OtherMedicinesHistory = append(dispensing.OtherMedicinesHistory, item)
		}
		// 生成订单, 并获取订单号
		orderNumber, err := generateOrder(sctx, ctx, orderOID, dispensing, projectSite.StoreHouseID[0], projectSite.ID, remark)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		dispensing.Order = orderNumber
		_, err = tools.Database.Collection("dispensing").UpdateOne(sctx, bson.M{"_id": dispensingOID}, bson.M{"$set": dispensing})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var emailMedicineNumber []string
		for _, item := range medicineNumber {
			emailMedicineNumber = append(emailMedicineNumber, item)
		}
		for _, item := range otherDispensingMedicine {
			emailMedicineNumber = append(emailMedicineNumber, fmt.Sprintf("%s/%d/%s/%s", item.Name, item.Count, item.Batch, item.ExpireDate))
		}
		err = mailWithDTP(ctx, attribute, subjectOID, envOID, visitInfo, emailMedicineNumber, "dispensing.apply-title", "dispensing.apply", []string{}, remark, orderNumber, "", now)
		if err != nil {
			return nil, err
		}

		// 整合响应数据

		blind := false

		isBlindedRole, err := tools.IsBlindedRole(roleID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if isBlindedRole {
			blind = true
		}

		for _, medicine := range dispensing.DispensingMedicines {
			// isOpenDrug, _ := tools.IsOpenDrug(subject.EnvironmentID, subject.CohortID, medicine.Name)
			// isOtherDrug, _ := tools.IsOtherDrug(subject.EnvironmentID, medicine.Name)
			// if blind && !isOpenDrug && !isOtherDrug {
			// 	medicine.Name = tools.BlindData
			// }
			isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
			if blind && isBlindedDrug {
				medicine.Name = tools.BlindData
			}
			resDispensingInfo = append(resDispensingInfo, models.ResDispensingInfo{
				Name:       medicine.Name,
				Number:     medicine.Number,
				Order:      orderNumber,
				ExpireDate: medicine.ExpirationDate,
			})
		}
		for _, medicine := range dispensing.OtherDispensingMedicines {
			isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
			if blind && isBlindedDrug {
				medicine.Name = tools.BlindData
			}
			countStr := strconv.Itoa(medicine.Count)
			count := medicine.Name + "(" + countStr + ")"
			resDispensingInfo = append(resDispensingInfo, models.ResDispensingInfo{
				Name:       medicine.Name,
				Number:     count,
				Order:      orderNumber,
				ExpireDate: medicine.ExpireDate,
			})
		}

		//发放完成--更新app发放通知为完成状态
		if attribute.AttributeInfo.Dispensing && dispensing.Status == 2 && !dispensing.VisitSign {
			err = s.PatchAppDispenseTaskFinish(ctx, dispensingID)
			if err != nil {
				return []models.ResDispensingInfo{}, err
			}
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return []models.ResDispensingInfo{}, err
	}

	// 发药轨迹
	setHistory(ctx, checkDispensing.ID, subject, attribute, otherDispensingMedicine, medicineNumber, room, "", dispensingMedicine, remark, now)

	sort.SliceStable(resDispensingInfo, func(i, j int) bool { return resDispensingInfo[i].Number < resDispensingInfo[j].Number })

	return resDispensingInfo, nil
}

// AddDispensingVisitWithDTP 访视外发药
func (s *DispensingService) AddDispensingVisitWithDTP(ctx *gin.Context, subjectID string, visitID string, visitLabelIDs []interface{}, reason string, remark string, roleID string) ([]models.ResDispensingInfo, error) {
	ctx.Set("dtp", true)

	dispensingOID := primitive.NewObjectID()
	var subject models.Subject
	var attribute models.Attribute
	var resDispensingInfo []models.ResDispensingInfo
	dispensingMedicine := []models.DispensingMedicine{}
	var otherDispensingMedicine []models.OtherDispensingMedicine
	var medicineNumber []string
	now := time.Duration(time.Now().Unix())
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
		err := tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		customerOID := subject.CustomerID
		projectOID := subject.ProjectID
		envOID := subject.EnvironmentID
		cohortOID := subject.CohortID
		visitOID, _ := primitive.ObjectIDFromHex(visitID)

		// 多个标签选择
		var visitLabelOIDs = bson.A{}
		for _, visitLabelID := range visitLabelIDs {
			visitLabelOID, _ := primitive.ObjectIDFromHex(visitLabelID.(string))
			visitLabelOIDs = append(visitLabelOIDs, visitLabelOID)
		}
		match := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
		_, visitInfo, err := getVisitInfo(customerOID, projectOID, envOID, cohortOID, visitOID)

		projectFilter := bson.M{"_id": projectOID}
		var project models.Project
		err = tools.Database.Collection("project").FindOne(nil, projectFilter).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		attributeMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID}
		if cohortOID != primitive.NilObjectID {
			attributeMatch = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
		}
		err = tools.Database.Collection("attribute").FindOne(nil, attributeMatch).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 校验 受试者是否已经揭盲 退出 替换
		status, err := checkSubjectStatus(ctx, sctx, subjectOID)
		if err != nil {
			return nil, err
		}
		if !status {
			return nil, tools.BuildServerError(ctx, "subject_status_no_dispensing")
		}

		// 校验发药是否按顺序 DTP模式下 访视外发药可以发之前的访视
		//checkVisit, err := checkVisitOrder(sctx, subjectOID, visitOID, false)
		//if err != nil {
		//	return nil, err
		//}
		//if !checkVisit {
		//	return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_order")
		//}

		err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		allDrugMap, err := tools.AllDrugMap(envOID)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询中的供应计划
		expireDateKey, dispensingAlarmNumber, err := getExpireDateKey(subjectOID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 查询获取访视周期标签研究产品配置信息
		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: match}},
			{{Key: "$unwind", Value: "$configures"}},
			{{Key: "$match", Value: bson.M{"configures.id": bson.M{"$in": visitLabelOIDs}}}},
		}
		drugCursor, err := tools.Database.Collection("drug_configure").Aggregate(sctx, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var drugData []models.UpdateDrugConfigure
		err = drugCursor.All(nil, &drugData)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询中心关联的仓库
		var projectSite models.ProjectSite
		_ = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)

		if len(projectSite.StoreHouseID) == 0 || (len(projectSite.StoreHouseID) > 0 && projectSite.StoreHouseID[0] == primitive.NilObjectID) {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_site")
		}

		// 初始化 订单ID
		orderOID := primitive.NewObjectID()

		// 获取发药数据 并更新药物状态  可多标签
		var labels []string
		err = setDispensingMedicine(sctx, ctx, orderOID, drugData, expireDateKey, subject, &otherDispensingMedicine, &medicineNumber, &dispensingMedicine, attribute, &labels, projectSite.StoreHouseID[0], true, now, dispensingAlarmNumber, allDrugMap)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 开放药物
		//u, _ := ctx.Get("user")
		//user := u.(models.User)
		//userName := user.Name
		//openDrug := models.OpenDrug{
		//	MedicineInfo:            medicineInfo,
		//	Subject:                 subject,
		//	ExpireDateKey:           expireDateKey,
		//	SendType:                2,
		//	OtherMedicineCount:      &[]models.OtherMedicineCount{},
		//	OtherDispensingMedicine: &otherDispensingMedicine,
		//	DispensingMedicine:      &dispensingMedicine,
		//	Attribute:               attribute,
		//	UserName:                userName,
		//	User:                    user,
		//	MedicineNumber:          &medicineNumber,
		//	SourceSend:              projectSite.StoreHouseID[0],
		//	Histories:               &[]models.History{},
		//	Now:                     now,
		//}
		//err = getOpenDrug(ctx, sctx, openDrug)
		//if err != nil {
		//	return nil, errors.WithStack(err)
		//}

		instanceRepeatNo := "0"
		blockRepeatNo := "0"

		var dispensingModel models.Dispensing
		opts := &options.FindOneOptions{
			Sort: bson.D{{"serial_number", -1}},
		}
		err = tools.Database.Collection("dispensing").FindOne(nil, bson.M{"visit_info.visit_cycle_info_id": visitInfo.ID, "subject_id": subjectOID},
			opts).Decode(&dispensingModel)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 发药插入数据
		dispensing := models.Dispensing{
			ID:            dispensingOID,
			CustomerID:    customerOID,
			ProjectID:     projectOID,
			EnvironmentID: envOID,
			CohortID:      cohortOID,
			SubjectID:     subjectOID,
			VisitInfo: models.VisitInfo{
				VisitCycleInfoID: visitInfo.ID,
				Number:           visitInfo.Number,
				InstanceRepeatNo: instanceRepeatNo,
				BlockRepeatNo:    blockRepeatNo,
				Name:             visitInfo.Name,
				Random:           visitInfo.Random,
				Dispensing:       visitInfo.Dispensing,
			},
			DispensingMedicines:      dispensingMedicine,
			OtherDispensingMedicines: otherDispensingMedicine,
			VisitSign:                true,
			Reissue:                  0,
			Status:                   2,
			DispensingTime:           now,
			Reasons:                  []models.Reason{},
			SerialNumber:             dispensingModel.SerialNumber + 1,
			ReplaceMedicines:         []models.ReplaceMedicines{},
			Labels:                   labels,
			Remark:                   remark,
		}
		for _, other := range otherDispensingMedicine {
			item := models.OtherDispensingMedicineInfo{
				ID:              other.ID,
				MedicineOtherID: other.MedicineOtherID,
				Name:            other.Name,
				Count:           other.Count,
				Batch:           other.Batch,
				ExpireDate:      other.ExpireDate,
				Time:            other.Time,
				Type:            other.Type,
			}
			dispensing.OtherMedicinesHistory = append(dispensing.OtherMedicinesHistory, item)
		}
		// 生成订单, 并获取订单号
		orderNumber, err := generateOrder(sctx, ctx, orderOID, dispensing, projectSite.StoreHouseID[0], projectSite.ID, reason)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		dispensing.Order = orderNumber
		_, err = tools.Database.Collection("dispensing").InsertOne(sctx, dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var emailMedicineNumber []string
		for _, item := range medicineNumber {
			emailMedicineNumber = append(emailMedicineNumber, item)
		}
		for _, item := range otherDispensingMedicine {
			emailMedicineNumber = append(emailMedicineNumber, fmt.Sprintf("%s/%d/%s/%s", item.Name, item.Count, item.Batch, item.ExpireDate))
		}
		err = mailWithDTP(ctx, attribute, subjectOID, envOID, visitInfo, emailMedicineNumber, "dispensing.unscheduled-apply-title", "dispensing.unscheduled-apply", []string{}, remark, orderNumber, reason, now)
		if err != nil {
			return nil, err
		}

		// 整合响应数据
		blind := false
		//if attribute.AttributeInfo.Blind {
		isBlindedRole, err := tools.IsBlindedRole(roleID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if isBlindedRole {
			blind = true
		}
		//}

		for _, medicine := range dispensing.DispensingMedicines {
			// isOpenDrug, _ := tools.IsOpenDrug(subject.EnvironmentID, subject.CohortID, medicine.Name)
			// isOtherDrug, _ := tools.IsOtherDrug(subject.EnvironmentID, medicine.Name)
			// if blind && !isOpenDrug && !isOtherDrug {
			// 	medicine.Name = tools.BlindData
			// }
			isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
			if blind && isBlindedDrug {
				medicine.Name = tools.BlindData
			}
			resDispensingInfo = append(resDispensingInfo, models.ResDispensingInfo{
				Name:       medicine.Name,
				Number:     medicine.Number,
				Order:      orderNumber,
				ExpireDate: medicine.ExpirationDate,
			})
		}
		for _, medicine := range dispensing.OtherDispensingMedicines {
			isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
			if blind && isBlindedDrug {
				medicine.Name = tools.BlindData
			}
			countStr := strconv.Itoa(medicine.Count)
			count := medicine.Name + "(" + countStr + ")"
			resDispensingInfo = append(resDispensingInfo, models.ResDispensingInfo{
				Name:       medicine.Name,
				Number:     count,
				Order:      orderNumber,
				ExpireDate: medicine.ExpireDate,
			})
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return []models.ResDispensingInfo{}, err
	}

	setHistory(ctx, dispensingOID, subject, attribute, otherDispensingMedicine, medicineNumber, "", reason, dispensingMedicine, remark, now)

	return resDispensingInfo, nil
}

func (s *DispensingService) ReissueDispensingWithDTP(ctx *gin.Context, data map[string]interface{}) ([]models.ResDispensingInfo, error) {
	ctx.Set("dtp", true)
	var resDispensingInfo []models.ResDispensingInfo
	remark := ""
	if data["remark"] != nil {
		remark = data["remark"].(string)
	}
	isBlindedRole, err := tools.IsBlindedRole(data["roleId"].(string))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	visitLabelOID, _ := primitive.ObjectIDFromHex(data["visit_label_id"].(string))

	visitOID, _ := primitive.ObjectIDFromHex(data["visit_id"].(string))
	subjectOID, _ := primitive.ObjectIDFromHex(data["subject_id"].(string))

	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	customerOID := subject.CustomerID
	projectOID := subject.ProjectID
	envOID := subject.EnvironmentID
	cohortOID := subject.CohortID

	var project models.Project
	projectOpts := &options.FindOneOptions{
		Projection: bson.M{"meta": 0},
	}
	if err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}, projectOpts).Decode(&project); err != nil {
		return nil, errors.WithStack(err)
	}
	var instanceRepeatNo = "0"
	var blockRepeatNo = "0"

	var otherDispensingMedicine []models.OtherDispensingMedicine
	var dispensingMedicine []models.DispensingMedicine
	var medicineNumber []string
	var otherMedicine []string

	//var openMedicines []interface{}
	//if data["medicine_info"] != nil {
	//	openMedicines = data["medicine_info"].([]interface{})
	//}

	dispensingOID := primitive.NewObjectID()
	now := time.Duration(time.Now().Unix())
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 校验 受试者是否已经揭盲 退出 替换
		status, err := checkSubjectStatus(ctx, sctx, subjectOID)
		if err != nil {
			return nil, err
		}
		if !status {
			return nil, tools.BuildServerError(ctx, "subject_status_no_reissue")
		}

		if data["blockRepeatNo"] != nil && data["blockRepeatNo"] != "0" || (data["instanceRepeatNo"] != nil && data["instanceRepeatNo"] != "0") {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_reissue")
		}
		// DTP模式下 可以补发之前的访视
		//checkVisit, err := checkVisitOrder(sctx, subjectOID, visitOID, true)
		//if err != nil {
		//	return nil, err
		//}
		//if !checkVisit {
		//	return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_order")
		//}

		// 查询中心关联的仓库
		var projectSite models.ProjectSite
		_ = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)

		if len(projectSite.StoreHouseID) == 0 || (len(projectSite.StoreHouseID) > 0 && projectSite.StoreHouseID[0] == primitive.NilObjectID) {
			return nil, tools.BuildServerError(ctx, "subject_visit_dispensing_no_site")
		}

		// 根据标签转成 对应的medicineInfo

		var dispensingSign models.Dispensing // 访视发药的数据
		tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"subject_id": subjectOID, "visit_info.visit_cycle_info_id": visitOID, "visit_sign": false}).Decode(&dispensingSign)
		var drugValue []models.DrugValue
		for _, medicine := range dispensingSign.DispensingMedicines {
			index := -1
			for i, value := range drugValue {
				if value.DrugName == medicine.Name {
					index = i
				}
			}
			if index == -1 {
				drugValue = append(drugValue, models.DrugValue{
					DrugName:         medicine.Name,
					DispensingNumber: 1,
					PkgSpec:          "",
					DrugSpec:         "",
				})
			} else {
				drugValue[index].DispensingNumber = drugValue[index].DispensingNumber + 1
			}
		}
		for _, medicine := range dispensingSign.OtherDispensingMedicines {
			index := -1
			for i, value := range drugValue {
				if value.DrugName == medicine.Name {
					index = i
				}
			}
			if index == -1 {
				drugValue = append(drugValue, models.DrugValue{
					DrugName:         medicine.Name,
					DispensingNumber: medicine.Count,
					PkgSpec:          "",
					DrugSpec:         "",
				})
			} else {
				drugValue[index].DispensingNumber = drugValue[index].DispensingNumber + medicine.Count
			}
		}

		// 查询获取访视周期标签研究产品配置信息

		pipeline := mongo.Pipeline{
			{{Key: "$match", Value: bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}}},
			{{Key: "$unwind", Value: "$configures"}},
			{{Key: "$match", Value: bson.M{"configures.id": visitLabelOID}}},
			{{Key: "$project", Value: bson.M{
				"values": "$configures.values",
			}}},
		}
		drugCursor, err := tools.Database.Collection("drug_configure").Aggregate(sctx, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var drugData []models.DrugConfigureInfo
		err = drugCursor.All(nil, &drugData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		prepareDrug, err := getReissueCount(ctx, drugData[0].Values, drugValue)
		if err != nil {
			return nil, tools.BuildServerError(ctx, "edc.drug.reissue.error")
		}
		var medicineInfo []interface{}
		for _, value := range prepareDrug {
			medicineInfo = append(medicineInfo, map[string]interface{}{
				"name":  value.DrugName,
				"spec":  value.DrugSpec,
				"count": float64(value.DispensingNumber),
			})
		}

		// 根据供应计划 获取 各药物不发放日期
		expireDateKey, dispensingAlarmNumber, err := getExpireDateKey(subjectOID)
		if err != nil {
			return nil, err
		}
		orderOID := primitive.NewObjectID()

		// 发药处理
		for _, medicine := range medicineInfo {
			zone, err := tools.GetTimeZone(projectOID)
			if err != nil {
				return nil, err
			}
			hour := time.Duration(zone)
			minute := time.Duration((zone - float64(hour)) * 60)
			duration := hour*time.Hour + minute*time.Minute
			date := time.Now().UTC().Add(duration).Format("2006-01-02")
			if expireDateKey[medicine.(map[string]interface{})["name"].(string)] != nil {
				date = expireDateKey[medicine.(map[string]interface{})["name"].(string)].(string)
			}
			drugMatch := bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID,
				"info.name": medicine.(map[string]interface{})["name"]}
			count, err := tools.Database.Collection("medicine_other_institute").CountDocuments(sctx, drugMatch)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			// 未编号研究产品
			if count > 0 {
				drugMatch = bson.M{"customer_id": customerOID, "project_id": projectOID, "env_id": envOID, "storehouse_id": projectSite.StoreHouseID[0],
					"info.name":        medicine.(map[string]interface{})["name"],
					"info.expire_date": bson.M{"$gt": date}}
				number := int(medicine.(map[string]interface{})["count"].(float64))
				opts := &options.FindOptions{
					Sort: bson.D{{"info.expire_date", 1}},
				}
				var medicineOtherInstitutes []models.MedicineOtherInstitute
				cursor, err := tools.Database.Collection("medicine_other_institute").Find(sctx, drugMatch, opts)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(sctx, &medicineOtherInstitutes)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				//// 未编号发放警戒
				//err = checkDispensingOtherAlarm(ctx, medicineOtherInstitutes, count, dispensingAlarmNumber[medicine.(map[string]interface{})["name"].(string)])
				//if err != nil {
				//	return nil, errors.WithStack(err)
				//}
				var medicineOtherInstitute models.MedicineOther
				useCount := 0
				needCount := number
				for _, institute := range medicineOtherInstitutes {
					if needCount == 0 {
						// 按批次 拿到足够的药物 退出循环
						break
					}
					if institute.Info.Count < needCount { // 一个批次不足 取药   减去这个批次剩下的药物
						if institute.Info.Count != 0 {
							update :=
								bson.M{
									"$set": bson.M{"edit": true},

									"$inc": bson.M{
										"info.count":       institute.Info.Count * -1,
										"info.apply_count": institute.Info.Count * 1,
									},
								}

							err := tools.Database.Collection("medicine_other_institute").FindOneAndUpdate(sctx, bson.M{"_id": institute.ID}, update).Decode(&medicineOtherInstitute)
							if err != nil {
								return nil, errors.WithStack(err)
							}
							var batchNumber []models.BatchCount

							batchNumber = append(batchNumber, models.BatchCount{Batch: institute.Info.Batch, Count: institute.Info.Count})
							useCount += institute.Info.Count
							needCount -= institute.Info.Count
							otherDispensingMedicine = append(otherDispensingMedicine, models.OtherDispensingMedicine{
								ID:              primitive.NewObjectID(),
								MedicineOtherID: institute.ID,
								Name:            institute.Info.Name,
								Count:           institute.Info.Count,
								Batch:           institute.Info.Batch,
								ExpireDate:      institute.Info.ExpireDate,
								Time:            now,
								Type:            2,
								BatchCount:      batchNumber,
							})

							// 插入未编号表新数据或者更新 申请数量
							if count, _ = tools.Database.Collection("medicine_other_institute").CountDocuments(sctx, bson.M{
								"subject_id": subject.ID,
								//"order_id":         orderOID,
								"info.name":        institute.Info.Name,
								"info.batch":       institute.Info.Batch,
								"info.expire_date": institute.Info.ExpireDate,
							}); count == 0 {
								tools.Database.Collection("medicine_other_institute").InsertOne(sctx, models.MedicineOtherInstitute{
									ID:            primitive.NewObjectID(),
									ProjectID:     subject.ProjectID,
									EnvironmentID: subject.EnvironmentID,
									CustomerID:    subject.CustomerID,
									InstituteID:   subject.ProjectSiteID,
									SubjectID:     subject.ID,
									Info: models.MedicineOtherInfo{
										ID:         institute.ID,
										Name:       institute.Info.Name,
										Batch:      institute.Info.Batch,
										ExpireDate: institute.Info.ExpireDate,
										ApplyCount: institute.Info.Count,
									},
								})
							} else {
								_, err := tools.Database.Collection("medicine_other_institute").UpdateOne(sctx, bson.M{
									"subject_id":       subject.ID,
									"info.name":        institute.Info.Name,
									"info.batch":       institute.Info.Batch,
									"info.expire_date": institute.Info.ExpireDate,
								},
									bson.M{
										"$set": bson.M{"edit": true},
										"$inc": bson.M{
											"info.apply_count": institute.Info.Count * 1,
										},
									})
								if err != nil {
									return nil, errors.WithStack(err)
								}
							}
							//otherMedicine = append(otherMedicine, fmt.Sprintf("%s(%d)", medicineOtherInstitute.Info.Name, institute.Info.Count*1))

						}
					} else { // 当前批次数量 足够取药  -所需药物数量
						update := bson.M{
							"$set": bson.M{"edit": true},
							"$inc": bson.M{
								"info.count":       needCount * -1,
								"info.apply_count": needCount * 1,
							},
						}
						err := tools.Database.Collection("medicine_other_institute").FindOneAndUpdate(sctx, bson.M{"_id": institute.ID}, update).Decode(&medicineOtherInstitute)
						if err != nil {
							return nil, errors.WithStack(err)
						}
						var batchNumber []models.BatchCount
						batchNumber = append(batchNumber, models.BatchCount{Batch: institute.Info.Batch, Count: needCount})
						otherDispensingMedicine = append(otherDispensingMedicine, models.OtherDispensingMedicine{
							ID:              primitive.NewObjectID(),
							MedicineOtherID: institute.ID,
							Name:            institute.Info.Name,
							Count:           needCount,
							Batch:           institute.Info.Batch,
							ExpireDate:      institute.Info.ExpireDate,
							Time:            now,
							Type:            2,
							BatchCount:      batchNumber,
						})

						// 插入未编号表新数据或者更新 申请数量
						if count, _ = tools.Database.Collection("medicine_other_institute").CountDocuments(sctx, bson.M{
							"subject_id":       subject.ID,
							"info.name":        institute.Info.Name,
							"info.batch":       institute.Info.Batch,
							"info.expire_date": institute.Info.ExpireDate,
						}); count == 0 {
							tools.Database.Collection("medicine_other_institute").InsertOne(sctx, models.MedicineOtherInstitute{
								ID:            primitive.NewObjectID(),
								ProjectID:     subject.ProjectID,
								EnvironmentID: subject.EnvironmentID,
								CustomerID:    subject.CustomerID,
								InstituteID:   subject.ProjectSiteID,
								SubjectID:     subject.ID,
								//OrderID:       orderOID,
								Info: models.MedicineOtherInfo{
									ID:         institute.ID,
									Name:       institute.Info.Name,
									Batch:      institute.Info.Batch,
									ExpireDate: institute.Info.ExpireDate,
									ApplyCount: needCount,
								},
							})
						} else {
							tools.Database.Collection("medicine_other_institute").UpdateOne(sctx, bson.M{
								"subject_id":       subject.ID,
								"info.name":        institute.Info.Name,
								"info.batch":       institute.Info.Batch,
								"info.expire_date": institute.Info.ExpireDate,
							},
								bson.M{
									"$set": bson.M{"edit": true},
									"$inc": bson.M{
										"info.apply_count": needCount * 1,
									},
								})
						}
						useCount += needCount
						needCount -= needCount
						//otherMedicine = append(otherMedicine, fmt.Sprintf("%s(%d)", medicineOtherInstitute.Info.Name, needCount))
					}

				}
				if useCount < number {
					return nil, tools.BuildServerError(ctx, "subject_medicine_count")
				}

			} else {
				// 已编号研究产品
				// 查询研究产品
				drugMatch = bson.M{
					"customer_id": customerOID,
					"project_id":  projectOID,
					"env_id":      envOID,
					"status":      1,
					"name":        medicine.(map[string]interface{})["name"],
					//"site_id":         subject.ProjectSiteID,
					"storehouse_id":   projectSite.StoreHouseID[0],
					"expiration_date": bson.M{"$gt": date},
					"spec":            medicine.(map[string]interface{})["spec"],
					"$or": bson.A{
						bson.M{"subject_id": bson.M{"$exists": 0}},
						bson.M{"subject_id": primitive.NilObjectID},
						bson.M{"subject_id": subjectOID},
					},
				}
				number64 := int64(medicine.(map[string]interface{})["count"].(float64))
				err = checkDispensingAlarm(ctx, sctx, medicine.(map[string]interface{})["name"].(string), drugMatch, number64, dispensingAlarmNumber)
				if err != nil {
					return nil, err
				}

				var medicines []models.Medicine
				opts := &options.FindOptions{
					Sort:  bson.D{{"subject_id", -1}, {"expiration_date", 1}, {"batch_number", 1}, {"serial_number", 1}, {"number", 1}}, // 优先获取受试者绑定的研究产品  subject_id
					Limit: &number64,
				}

				cursor, err := tools.Database.Collection("medicine").Find(sctx, drugMatch, opts)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = cursor.All(sctx, &medicines)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				if len(medicines) != int(medicine.(map[string]interface{})["count"].(float64)) {
					return nil, tools.BuildServerError(ctx, "subject_medicine_count")
				}
				// 更新研究产品状态
				update := bson.M{
					"$set": bson.M{
						"status":        13,
						"subject_id":    subject.ID,
						"dispensing_id": dispensingOID,
						"order_id":      orderOID,
					},
				}
				medicineOIDS := bson.A{}
				for _, item := range medicines {
					medicineOIDS = append(medicineOIDS, item.ID)
					medicineNumber = append(medicineNumber, item.Number) // mail参数
					// 写入dispensingMedicine
					dispensingMedicine = append(dispensingMedicine, models.DispensingMedicine{
						MedicineID:     item.ID,
						Name:           item.Name,
						Number:         item.Number,
						ShortCode:      item.ShortCode,
						ExpirationDate: item.ExpirationDate,
						BatchNumber:    item.BatchNumber,
						Time:           now,
						Type:           2,
					})
				}
				_, err = tools.Database.Collection("medicine").UpdateMany(sctx, bson.M{"_id": bson.M{"$in": medicineOIDS}}, update)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}

		// 受试者发药新增数据
		_, visitInfo, err := getVisitInfo(customerOID, projectOID, envOID, cohortOID, visitOID)
		if err != nil {
			return nil, err
		}
		// 开放药物
		var attribute models.Attribute

		attributeMatch := bson.M{"project_id": projectOID, "env_id": envOID}
		if cohortOID != primitive.NilObjectID {
			attributeMatch = bson.M{"project_id": projectOID, "env_id": envOID, "cohort_id": cohortOID}
		}
		err = tools.Database.Collection("attribute").FindOne(sctx, attributeMatch).Decode(&attribute)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		subjectReplaceText := GetSubjectReplaceText(ctx, attribute)

		u, _ := ctx.Get("user")
		user := u.(models.User)
		userName := user.Name
		//
		//openDrug := models.OpenDrug{
		//	MedicineInfo:            openMedicines,
		//	Subject:                 subject,
		//	ExpireDateKey:           expireDateKey,
		//	SendType:                2,
		//	OtherMedicineCount:      &[]models.OtherMedicineCount{},
		//	OtherDispensingMedicine: &otherDispensingMedicine,
		//	DispensingMedicine:      &dispensingMedicine,
		//	Attribute:               attribute,
		//	UserName:                userName,
		//	User:                    user,
		//	MedicineNumber:          &medicineNumber,
		//	SourceSend:              projectSite.StoreHouseID[0],
		//	Histories:               &[]models.History{},
		//	Now:                     now,
		//}
		//err = getOpenDrug(ctx, sctx, openDrug)
		//if err != nil {
		//	return nil, errors.WithStack(err)
		//}

		// 查询访视周期序列值
		var dispensingModel models.Dispensing
		opts := &options.FindOneOptions{
			Sort: bson.D{{"serial_number", -1}},
		}
		err = tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"visit_info.visit_cycle_info_id": visitInfo.ID, "subject_id": subjectOID},
			opts).Decode(&dispensingModel)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		dispensing := models.Dispensing{
			ID:            dispensingOID,
			CustomerID:    customerOID,
			ProjectID:     projectOID,
			EnvironmentID: envOID,
			CohortID:      cohortOID,
			SubjectID:     subjectOID,
			VisitInfo: models.VisitInfo{
				VisitCycleInfoID: visitInfo.ID,
				Number:           visitInfo.Number,
				InstanceRepeatNo: instanceRepeatNo,
				BlockRepeatNo:    blockRepeatNo,
				Name:             visitInfo.Name,
				Random:           visitInfo.Random,
				Dispensing:       visitInfo.Dispensing,
			},
			DispensingMedicines:      dispensingMedicine,
			OtherDispensingMedicines: otherDispensingMedicine,
			Status:                   2,
			Reissue:                  1,
			DispensingTime:           now,
			Reasons:                  []models.Reason{},
			ReplaceMedicines:         []models.ReplaceMedicines{},
			SerialNumber:             dispensingModel.SerialNumber + 1,
			VisitSign:                true,
			Remark:                   remark,
		}

		orderNumber, err := generateOrder(sctx, ctx, orderOID, dispensing, projectSite.StoreHouseID[0], projectSite.ID, remark)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		dispensing.Order = orderNumber

		if _, err = tools.Database.Collection("dispensing").InsertOne(sctx, dispensing); err != nil {
			return nil, errors.WithStack(err)
		}

		var emailMedicine []string
		for _, item := range medicineNumber {
			emailMedicine = append(emailMedicine, item)
		}
		for _, item := range dispensing.OtherDispensingMedicines {
			emailMedicine = append(emailMedicine, fmt.Sprintf("%s/%d/%s/%s", item.Name, item.Count, item.Batch, item.ExpireDate))
			otherMedicine = append(otherMedicine, fmt.Sprintf("%s/%d/%s/%s", item.Name, item.Count, item.Batch, item.ExpireDate))
		}

		err = mailWithDTP(ctx, attribute, subjectOID, envOID, visitInfo, emailMedicine, "dispensing.reissue-dtp-title", "dispensing.reissue-dtp", []string{}, remark, orderNumber, "", now)
		if err != nil {
			return nil, err
		}

		// 发药轨迹
		key := "history.dispensing.dtp-reissue"
		historyData := map[string]interface{}{"label": subjectReplaceText, "subject": subject.Info[0].Value, "medicine": medicineNumber, "remark": remark}
		if len(otherMedicine) > 0 {
			key = "history.dispensing.dtp-reissue-other"
			historyData = map[string]interface{}{"label": subjectReplaceText, "subject": subject.Info[0].Value, "medicine": otherMedicine, "remark": remark}

		}
		if len(medicineNumber) > 0 && len(otherMedicine) > 0 {
			key = "history.dispensing.dtp-reissue-with-other"
			historyData = map[string]interface{}{"label": subjectReplaceText, "subject": subject.Info[0].Value, "medicine": medicineNumber, "other_medicine": otherMedicine, "remark": remark}
		}

		var histories []models.History
		history := models.History{
			Key:  key,
			OID:  dispensingOID,
			Data: historyData,
			Time: now,
			UID:  user.ID,
			User: userName,
		}
		histories = append(histories, history)

		// 整合响应数据
		blind := false
		//if attribute.AttributeInfo.Blind {
		if isBlindedRole {
			blind = true
		}
		//}

		for _, medicine := range dispensing.DispensingMedicines {
			// isOpenDrug, _ := tools.IsOpenDrug(subject.EnvironmentID, subject.CohortID, medicine.Name)
			// isOtherDrug, _ := tools.IsOtherDrug(subject.EnvironmentID, medicine.Name)
			// if blind && !isOpenDrug && !isOtherDrug {
			// 	medicine.Name = tools.BlindData
			// }
			isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, medicine.Name)
			if blind && isBlindedDrug {
				medicine.Name = tools.BlindData
			}
			resDispensingInfo = append(resDispensingInfo, models.ResDispensingInfo{
				Name:       medicine.Name,
				Number:     medicine.Number,
				Order:      orderNumber,
				ExpireDate: medicine.ExpirationDate,
			})
			histories = append(histories, models.History{
				Key:  "history.medicine.apply",
				OID:  medicine.MedicineID,
				Time: now,
				UID:  user.ID,
				User: userName,
			})
		}
		for _, medicine := range dispensing.OtherDispensingMedicines {
			countStr := strconv.Itoa(medicine.Count)
			count := medicine.Name + "(" + countStr + ")"
			resDispensingInfo = append(resDispensingInfo, models.ResDispensingInfo{
				Name:       medicine.Name,
				Number:     count,
				Order:      orderNumber,
				ExpireDate: medicine.ExpireDate,
			})
		}
		ctx.Set("HISTORY", histories)

		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return nil, err
	}
	return resDispensingInfo, nil
}

// GetReissueMedicineName 通用模式获取补发的药物名称
func (s *DispensingService) GetReissueMedicineName(ctx *gin.Context, subjectID string, visitID string, roleID string) (models.RepMedicine, error) {
	var repMedicine models.RepMedicine
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	visitOID, _ := primitive.ObjectIDFromHex(visitID)
	var attribute models.Attribute
	var subject models.Subject
	var dispensing models.Dispensing
	var drugConfigure models.DrugConfigure
	var visitCycle models.VisitCycle
	tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	tools.Database.Collection("dispensing").FindOne(nil, bson.M{"subject_id": subjectOID, "visit_info.visit_cycle_info_id": visitOID, "visit_sign": false}).Decode(&dispensing)
	tools.Database.Collection("drug_configure").FindOne(nil, bson.M{"project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&drugConfigure)
	tools.Database.Collection("visit_cycle").FindOne(nil, bson.M{"project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&visitCycle)
	matchAttribute := bson.M{"env_id": subject.EnvironmentID}
	if subject.CohortID != primitive.NilObjectID {
		matchAttribute["cohort_id"] = subject.CohortID
	}
	tools.Database.Collection("attribute").FindOne(nil, matchAttribute).Decode(&attribute)
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return models.RepMedicine{}, errors.WithStack(err)
	}
	if subject.Group == "" {
		subject.Group = "N/A"
	}

	if subject.RegisterGroup != "" {
		subject.Group = subject.RegisterGroup
	}

	check := func(i int, drugConfigure models.DrugConfigureInfo) bool {
		visit := false
		for _, cycle := range drugConfigure.VisitCycles {
			if cycle == visitOID {
				visit = true
			}
		}
		return drugConfigure.Group == subject.Group && visit
	}
	cc := drugConfigure.Configures
	res := slice.Filter(cc, check)
	repMedicine.VisitName = dispensing.VisitInfo.Name
	group := map[string]int{}
	index := 0

	uniqueMedicine := map[string]bool{}
	isDose := false
	if dispensing.DoseInfo != nil {
		_, isDose = slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
			return item.ID == visitOID && item.DoseAdjustment
		})
	}

	infoLabels := []models.LabelValue{}
	infoNames := []models.LabelValue{}
	var drugConfigureSetting models.DrugConfigureSetting
	err = tools.Database.Collection("drug_configure_setting").FindOne(nil, bson.M{"project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&drugConfigureSetting)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.RepMedicine{}, errors.WithStack(err)
	}
	if isDose {

		if drugConfigureSetting.SelectType == 1 {
			doseP, ok := slice.Find(drugConfigureSetting.DoseLevelList, func(index int, item models.DoseLevel) bool {
				return dispensing.DoseInfo != nil && dispensing.DoseInfo.DoseLevelList != nil && dispensing.DoseInfo.DoseLevelList.ID == item.ID
			})
			if ok {
				doseData := *doseP

				infoLabels, infoNames, _, err = getVisitGroupLabel(ctx, visitOID, subject, doseData.DoseDistribution)
				if err != nil {
					return models.RepMedicine{}, errors.WithStack(err)
				}
			} else {
				return models.RepMedicine{DeleteDose: locales.Tr(ctx, "subject_visit_dispensing_no_reissue_dose")}, nil
			}

		} else if drugConfigureSetting.SelectType == 2 {
			doseP, ok := slice.Find(drugConfigureSetting.VisitJudgmentList, func(index int, item models.VisitJudgment) bool {
				return dispensing.DoseInfo != nil && dispensing.DoseInfo.VisitJudgmentList != nil && dispensing.DoseInfo.VisitJudgmentList.ID == item.ID
			})
			if ok {
				doseData := *doseP
				infoLabels, infoNames, _, err = getVisitGroupLabel(ctx, visitOID, subject, doseData.DoseDistribution)
				if err != nil {
					return models.RepMedicine{}, errors.WithStack(err)
				}
			} else {
				return models.RepMedicine{DeleteDose: locales.Tr(ctx, "subject_visit_dispensing_no_reissue_dose")}, nil

			}

		}

	}

	for _, info := range res {
		for _, value := range info.Values {
			if _, ok := group[value.DrugName+value.DrugSpec]; ok {
				if info.OpenSetting != 3 {
					if isDose {
						_, ok := slice.Find(infoLabels, func(index int, item models.LabelValue) bool {
							return (item.ID == info.ID && item.Label == info.Label) || (item.ID == info.ID && item.Label == value.Label)
						})
						_, nameOk := slice.Find(infoNames, func(index int, item models.LabelValue) bool {
							return item.ID == info.ID && item.Label == value.DrugName
						})
						if !ok && !nameOk {
							continue

						}
					}

					if repMedicine.SaltMedicines[group[value.DrugName+value.DrugSpec]].Max == nil {
						max := value.DispensingNumber
						repMedicine.SaltMedicines[group[value.DrugName+value.DrugSpec]].Max = &max
					} else if *repMedicine.SaltMedicines[group[value.DrugName+value.DrugSpec]].Max < value.DispensingNumber {
						max := value.DispensingNumber
						repMedicine.SaltMedicines[group[value.DrugName+value.DrugSpec]].Max = &max
					}
				} else {
					max := formulaMax(dispensing, value.DrugName, value.IsOther)
					repMedicine.SaltMedicines[group[value.DrugName+value.DrugSpec]].Max = &max
				}
				continue
			}
			var saltMedicine models.SaltMedicine
			saltMedicine.Key = index
			saltMedicine.Spec = value.DrugSpec
			saltMedicine.IsOther = value.IsOther
			if isBlindedRole {
				saltName, salt := tools.Encrypt(value.DrugName)
				// isOpenDrug, _ := tools.IsOpenDrug(subject.EnvironmentID, subject.CohortID, value.DrugName)
				// isOtherDrug, _ := tools.IsOtherDrug(subject.EnvironmentID, value.DrugName)
				//if !isOpenDrug && !isOtherDrug {
				isBlindedDrug, _ := tools.IsBlindedDrug(subject.EnvironmentID, value.DrugName)
				if isBlindedDrug {
					saltMedicine.Name = tools.BlindData
					saltMedicine.Salt = &salt
					saltMedicine.SaltName = &saltName
				} else {
					saltMedicine.Name = value.DrugName
				}
			} else {
				saltMedicine.Name = value.DrugName
			}
			if info.OpenSetting != 3 {
				max := value.DispensingNumber
				saltMedicine.Max = &max
			} else {
				max := formulaMax(dispensing, value.DrugName, value.IsOther)
				saltMedicine.Max = &max
			}
			if isDose && info.OpenSetting != 3 {
				_, ok := slice.Find(infoLabels, func(index int, item models.LabelValue) bool {
					return (item.ID == info.ID && item.Label == info.Label) || (item.ID == info.ID && item.Label == value.Label)
				})
				_, nameOk := slice.Find(infoNames, func(index int, item models.LabelValue) bool {
					return item.ID == info.ID && item.Label == value.DrugName
				})
				if !ok && !nameOk {
					continue

				}
			}
			if uniqueMedicine[value.DrugName+" "+value.DrugSpec] {
				continue
			}
			uniqueMedicine[value.DrugName+" "+value.DrugSpec] = true

			if attribute.AttributeInfo.DtpRule == 1 {
				dtp := getDTPInfo(drugConfigureSetting.DtpIpList, nil, value.DrugName)
				saltMedicine.DTP = &dtp
				saltMedicine.DTPS = &dtp
			}
			group[value.DrugName+value.DrugSpec] = index
			index++
			repMedicine.SaltMedicines = append(repMedicine.SaltMedicines, saltMedicine)
		}
	}

	return repMedicine, nil
}

// GetFormula 获取公式计算配置
func (s *DispensingService) GetFormula(ctx *gin.Context, envID, cohortID, subjectID, visitID, roleID, dose string) (models.RepFormula, error) {
	envOID, _ := primitive.ObjectIDFromHex(envID)
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	visitOID, _ := primitive.ObjectIDFromHex(visitID)
	cohortOID, _ := primitive.ObjectIDFromHex(cohortID)

	/*
		查询受试者组别
		根据组别 + 访视 查询研究产品配置



	*/
	var repFormula models.RepFormula
	var drugConfigure models.DrugConfigure
	var subject models.Subject
	var drugConfigureSetting models.DrugConfigureSetting

	tools.Database.Collection("drug_configure").FindOne(nil, bson.M{"env_id": envOID, "cohort_id": cohortOID}).Decode(&drugConfigure)
	tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	group := "N/A"
	err := tools.Database.Collection("drug_configure_setting").FindOne(nil, bson.M{"project_id": subject.ProjectID, "env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&drugConfigureSetting)
	if err != nil && err != mongo.ErrNoDocuments {
		return models.RepFormula{}, errors.WithStack(err)
	}
	// 获取上一次的身高体重年龄
	var dispensing models.Dispensing
	err = tools.Database.Collection("dispensing").FindOne(nil, bson.M{
		"subject_id":                     subjectOID,
		"visit_info.visit_cycle_info_id": visitOID,
		"visit_sign":                     false,
		"status":                         2,
	}, &options.FindOneOptions{
		Sort: bson.D{{"serial_number", -1}},
	}).Decode(&dispensing) //最新主访视的身高体重信息
	if err != nil && err != mongo.ErrNoDocuments {
		return models.RepFormula{}, err
	}

	if dispensing.FormulaInfo.Height != nil {
		repFormula.HeightValue = dispensing.FormulaInfo.Height
	}
	if dispensing.FormulaInfo.Weight != nil {
		repFormula.WeightValue = dispensing.FormulaInfo.Weight
	}
	if dispensing.FormulaInfo.Age != nil {
		repFormula.AgeValue = dispensing.FormulaInfo.Age
	}

	if subject.Group != "" {
		group = subject.Group
	}
	if subject.RegisterGroup != "" {
		group = subject.RegisterGroup
	}
	isBlindedRole, err := tools.IsBlindedRole(roleID)
	if err != nil {
		return models.RepFormula{}, errors.WithStack(err)
	}

	IsOpenDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return models.RepFormula{}, errors.WithStack(err)
	}

	_, _, doseLevel, visitJudgment, _, err := getCurrentDose(ctx, subject, dose)

	labelArr := []string{}
	if !doseLevel.ID.IsZero() {
		labelArr = doseLevel.DoseDistribution
	} else {
		labelArr = visitJudgment.DoseDistribution

	}
	_, _, info, err := getVisitGroupLabel(ctx, visitOID, subject, labelArr)

	for _, configure := range drugConfigure.Configures {
		if configure.Group != group {
			continue
		}
		if configure.OpenSetting != 3 {
			continue
		}
		_, ok := slice.Find(configure.VisitCycles, func(index int, item primitive.ObjectID) bool {
			return visitOID == item
		})
		infoItem := slice.Filter(info, func(index int, item models.LabelValue) bool {
			return item.ID == configure.ID
		})
		if len(infoItem) == 0 && dose != "" {
			continue
		}
		if ok {
			switch configure.CalculationType {
			case 1:
				repFormula.Age = true
			case 2:
				repFormula.Weight = true
			case 3:
				repFormula.Weight = true
				repFormula.Height = true
			case 4:
				if strings.Contains(configure.CustomerCalculation, "w") || strings.Contains(configure.CustomerCalculation, "W") {
					repFormula.Weight = true
				}
				if strings.Contains(configure.CustomerCalculation, "h") || strings.Contains(configure.CustomerCalculation, "h") {
					repFormula.Height = true
				}
			}
			for _, value := range configure.Values {
				_, valueOK := slice.Find(infoItem, func(index int, item models.LabelValue) bool {
					return item.Label == value.DrugName
				})
				if dose != "" && !valueOK {
					continue
				}
				dtp := getDTPInfo(drugConfigureSetting.DtpIpList, nil, value.DrugName)
				if isBlindedRole && IsOpenDrugMap[value.DrugName] {
					saltName, salt := tools.Encrypt(value.DrugName)
					repFormula.SaltMedicines = append(repFormula.SaltMedicines, models.SaltMedicine{
						Name:     tools.BlindData,
						IsOther:  value.IsOther,
						Salt:     &salt,
						SaltName: &saltName,
						Spec:     value.DrugSpec,
						DTP:      &dtp,
					})
				} else {
					repFormula.SaltMedicines = append(repFormula.SaltMedicines, models.SaltMedicine{
						Name:    value.DrugName,
						IsOther: value.IsOther,
						Spec:    value.DrugSpec,
						DTP:     &dtp,
					})
				}
			}
		}
	}
	return repFormula, nil
}

func (s *DispensingService) GetFormulaRes(ctx *gin.Context, info models.FormulaReq) (models.FormulaMedicineInfoRes, error) {
	var data models.FormulaMedicineInfoRes
	envOID := info.EnvironmentID
	subjectOID := info.SubjectID
	visitOID := info.VisitID
	cohortOID := info.CohortID
	age := info.Age
	height := info.Height
	weight := info.Weight

	var drugConfigure models.DrugConfigure
	var dispensings []models.Dispensing
	var subject models.Subject
	tools.Database.Collection("drug_configure").FindOne(nil, bson.M{"env_id": envOID, "cohort_id": cohortOID}).Decode(&drugConfigure)
	tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	cursor, err := tools.Database.Collection("dispensing").Find(nil,
		bson.M{"subject_id": subjectOID},
		&options.FindOptions{Sort: bson.D{{"serial_number", 1}}},
	)
	if err != nil {
		return data, errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensings)
	if err != nil {
		return data, errors.WithStack(err)
	}
	randomWeight := 0.0
	formulaWeight := []models.FormulaWeight{}
	lastWeight := 0.0
	for _, dispensing := range dispensings {
		if dispensing.VisitInfo.Random == true {
			if visitOID == dispensing.VisitInfo.VisitCycleInfoID { // 当前计算访视为随机访视  随机体重使用当前体重
				if weight != nil {
					randomWeight = *weight
				}
			} else {
				if dispensing.FormulaInfo.Weight != nil {
					randomWeight = *dispensing.FormulaInfo.Weight
				} else if weight != nil {
					// 随机访视没发药的话 不比较 则未自身
					randomWeight = *weight
				}
			}
		}

		if dispensing.VisitSign || dispensing.Status != 2 || visitOID == dispensing.VisitInfo.VisitCycleInfoID { // 访视外和未发药的跳过
			continue
		}

		if dispensing.VisitInfo.Random == true && dispensing.FormulaInfo.Weight != nil {
			randomWeight = *dispensing.FormulaInfo.Weight
		}

		if dispensing.FormulaInfo.FormulaWeight != nil {
			formulaWeight = dispensing.FormulaInfo.FormulaWeight
		}
		if dispensing.FormulaInfo.Weight != nil {
			lastWeight = *dispensing.FormulaInfo.Weight
		}

	}

	group := "N/A"
	if subject.Group != "" {
		group = subject.Group
	}
	if subject.RegisterGroup != "" {
		group = subject.RegisterGroup
	}
	customerFormulaStr := ""
	for _, configure := range drugConfigure.Configures {
		if configure.Group != group {
			continue
		}
		_, ok := slice.Find(configure.VisitCycles, func(index int, item primitive.ObjectID) bool {
			return visitOID == item
		})
		if configure.OpenSetting != 3 {
			if configure.IsFormula && ok {
				customerFormulaStr = configure.CustomerCalculation
				var customerFormulaMedicineRes models.CustomerFormulaMedicineRes

				customerFormulaMedicineRes.Unit = configure.CustomerCalculationSpec
				customerFormulaMedicineRes.ConfigID = configure.ID
				// 自定义公式、 查出具体函数、代入参数
				if customerFormulaStr != "" {
					formula, b := customerFormula(customerFormulaStr, info.Form)
					if b {
						if configure.KeepDecimal != nil {
							formula = tools.Round(formula, *configure.KeepDecimal)
						}
						customerFormulaMedicineRes.CustomerFormula = &formula
						customerFormulaMedicineRes.UseFormula = customerFormulaStr
					}
				}

				for _, value := range configure.Values {
					customerFormulaMedicineRes.Spec = append(customerFormulaMedicineRes.Spec, value.DrugSpec)
					if configure.OpenSetting == 1 {
						if configure.Label != "" {
							nameType := models.NameType{
								Name: configure.Label,
								ID:   configure.ID.Hex(),
								Type: 1,
							}
							if len(configure.Values) == 1 && configure.Values[0].AutomaticRecode && customerFormulaMedicineRes.CustomerFormula != nil {
								number := int(math.Ceil(*customerFormulaMedicineRes.CustomerFormula / *value.AutomaticRecodeSpec))
								if inDispensingCount(number, configure.Values[0].CustomDispensingNumber) {
									nameType.Number = &number
								} else {
									nameType.OutSize = true
								}
							}
							customerFormulaMedicineRes.Name = append(customerFormulaMedicineRes.Name, nameType)
						} else {
							if value.Label != "" {
								nameType := models.NameType{
									Name: value.Label,
									Type: 1,
									ID:   configure.ID.Hex(),
								}
								if value.AutomaticRecode && customerFormulaMedicineRes.CustomerFormula != nil {
									number := int(math.Ceil(*customerFormulaMedicineRes.CustomerFormula / *value.AutomaticRecodeSpec))
									if inDispensingCount(number, value.CustomDispensingNumber) {
										nameType.Number = &number
									} else {
										nameType.OutSize = true
									}
								}
								customerFormulaMedicineRes.Name = append(customerFormulaMedicineRes.Name, nameType)
							}
						}
					} else if configure.OpenSetting == 2 {
						_, ok := slice.Find(customerFormulaMedicineRes.Name, func(index int, item models.NameType) bool {
							return value.DrugName == item.Name && item.Type == 2
						})
						if !ok {
							nameType := models.NameType{
								Name: value.DrugName,
								Type: 2,
								ID:   configure.ID.Hex(),
							}
							if value.AutomaticRecode && customerFormulaMedicineRes.CustomerFormula != nil {
								number := int(math.Ceil(*customerFormulaMedicineRes.CustomerFormula / *value.AutomaticRecodeSpec))
								if inDispensingCount(number, value.CustomDispensingNumber) {
									nameType.Number = &number
								} else {
									nameType.OutSize = true
								}
							}
							customerFormulaMedicineRes.Name = append(customerFormulaMedicineRes.Name, nameType)

							if configure.Label != "" && len(configure.Values) == 1 && configure.Values[0].AutomaticRecode && customerFormulaMedicineRes.CustomerFormula != nil {
								nameType := models.NameType{
									Name: configure.Label,
									Type: 2,
									ID:   configure.ID.Hex(),
								}

								number := int(math.Ceil(*customerFormulaMedicineRes.CustomerFormula / *value.AutomaticRecodeSpec))
								if inDispensingCount(number, configure.Values[0].CustomDispensingNumber) {
									nameType.Number = &number
								} else {
									nameType.OutSize = true
								}
								customerFormulaMedicineRes.Name = append(customerFormulaMedicineRes.Name, nameType)

							}
						}
					}
				}
				data.CustomerFormulaMedicineRes = append(data.CustomerFormulaMedicineRes, customerFormulaMedicineRes)
			}
		} else {
			if ok {
				for _, value := range configure.Values {
					currentformulaWeight := 0.0
					if weight != nil {
						currentformulaWeight = *weight
					}
					use := false

					formulaWeightItem := 0.0
					formulaWeightP, ok := slice.Find(formulaWeight, func(index int, item models.FormulaWeight) bool {
						return value.DrugName == item.Name
					})
					if ok {
						formulaWeightValue := *formulaWeightP
						formulaWeightItem = *formulaWeightValue.Weight
					}
					if value.ComparisonSwitch && weight != nil {
						currentformulaWeight, _, use = useWeight(*weight, randomWeight, formulaWeightItem, lastWeight, value, configure)
					}
					bsaCount := 0.0
					number := 0

					bsaCount, number, err = tools.Formula(ctx, configure, value, age, &currentformulaWeight, height)
					if err != nil {
						return data, err
					}

					unit := ""
					outSize := false
					if (configure.CalculationType == 1 || configure.CalculationType == 2) && number == 0 {
						outSize = true
					}
					specificationsValue := 0.0
					if value.Specifications.Unit != nil {
						unit = *value.Specifications.Unit
					}
					if value.Specifications.Value != nil {
						specificationsValue = *value.Specifications.Value
					}
					formulaMedicine := models.FormulaMedicineRes{
						Number:                number,
						Count:                 bsaCount,
						ActualWeight:          currentformulaWeight,
						CurrentComparisonType: value.CurrentComparisonType,
						ComparisonType:        value.ComparisonType,
						ShowComparisonType:    use,
						Radio:                 value.ComparisonRatio,
						Unit:                  unit,
						Spec:                  value.DrugSpec,
						Specifications:        specificationsValue,
						CalculationType:       configure.CalculationType,
						IsOther:               value.IsOther,
						OutSize:               outSize,
						ComparisonSymbols:     value.ComparisonSymbols,
					}
					if configure.CalculationType == 1 {
						formula := "age"
						formulaMedicine.UseFormula = &formula
					}

					if configure.CalculationType == 2 {
						formula := "weight"
						formulaMedicine.UseFormula = &formula
					}

					if configure.CalculationType == 3 {
						formula := "sqrt(w*h/3600)"
						formulaMedicine.UseFormula = &formula
					}
					if configure.CalculationType == 4 {
						useFormula := configure.CustomerCalculation
						formulaMedicine.UseFormula = &useFormula
					}
					data.FormulaMedicineRes = append(data.FormulaMedicineRes, formulaMedicine)
				}
			}
		}
	}
	return data, nil
}

func (s *DispensingService) AliReplaceDrug(ctx *gin.Context, data map[string]interface{}, edcUserName string) ([]models.ReplaceCodeDTOS, error) {
	_, res, err := ReplaceDrugMethod(ctx, data, edcUserName, 1)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return res, nil
}

func (s *DispensingService) AliRetrievalDrug(ctx *gin.Context, id string, data map[string]interface{}) error {
	_, err := RetrievalDrugMethod(ctx, id, data, 1)
	return err
}

// GetLabelMedicine ...app发药页面获取对应标签、开放药物接口
func (s *DispensingService) GetLabelMedicine(ctx *gin.Context, subjectID, visitID, outSize string) (interface{}, error) {
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	visitOID, _ := primitive.ObjectIDFromHex(visitID)
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)

	}
	var drugConfig models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(nil, bson.M{
		"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID,
	}).Decode(&drugConfig)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	attribute, err := database.GetAttributeWithEnvCohortID(nil, subject.EnvironmentID, subject.CohortID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var drugConfigureSetting models.DrugConfigureSetting
	err = tools.Database.Collection("drug_configure_setting").FindOne(nil, bson.M{
		"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID,
	}).Decode(&drugConfigureSetting)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}
	if subject.Group == "" {
		subject.Group = "N/A"
	}

	if subject.RegisterGroup != "" {
		subject.Group = subject.RegisterGroup
	}

	filterConfig := slice.Filter(drugConfig.Configures, func(index int, item models.DrugConfigureInfo) bool {
		_, ok := slice.Find(item.VisitCycles, func(index int, item primitive.ObjectID) bool {
			return visitOID == item
		})
		return item.Group == subject.Group && ok
	})

	labelMedicineMiddles := make([]models.LabelMedicineMiddle, 0)
	medicineMiddles := make([]models.LabelMedicineMiddle, 0)

	for _, info := range filterConfig {
		formKey := make([]string, 0)
		if len(info.CustomerCalculation) > 0 {
			re := regexp.MustCompile(`\{([^}]+)\}`)
			matches := re.FindAllStringSubmatch(info.CustomerCalculation, -1)
			for _, match := range matches {
				formKey = append(formKey, match[1])
			}
		}
		if info.Label != "" {
			dtp := []int{}
			if attribute.AttributeInfo.DtpRule == 1 {
				dtp = getDTPInfo(drugConfigureSetting.DtpIpList, info.Values, "")
			}
			tmpLabel := models.LabelMedicineMiddle{
				ID:          info.ID,
				Label:       info.Label,
				OpenSetting: info.OpenSetting,
				Formkey:     formKey,
				DTP:         dtp,
			}
			if len(info.Values) == 1 {
				count := customDispensingNumberToSelectOption(info.Values[0].CustomDispensingNumber)
				if len(count) > 1 {
					specCount := models.SpecCount{
						Spec:  info.Values[0].DrugSpec,
						Count: customDispensingNumberToSelectOption(info.Values[0].CustomDispensingNumber),
					}
					tmpLabel.Info = specCount
				}
			}
			labelMedicineMiddles = append(labelMedicineMiddles, tmpLabel)
		}

		for _, value := range info.Values {
			if len(info.Values) == 1 && info.Label != "" {
				break
			}
			if value.Label != "" {
				dtp := []int{}
				if attribute.AttributeInfo.DtpRule == 1 {
					dtp = getDTPInfo(drugConfigureSetting.DtpIpList, nil, value.DrugName)
				}
				specCount := models.SpecCount{
					Spec:  value.DrugSpec,
					Count: customDispensingNumberToSelectOption(value.CustomDispensingNumber)}
				labelMedicineMiddles = append(labelMedicineMiddles, models.LabelMedicineMiddle{
					ID:      info.ID,
					Label:   value.Label,
					Info:    specCount,
					Formkey: formKey,
					DTP:     dtp,
				})
			}
		}

		if info.OpenSetting == 2 && info.Label == "" {
			for _, value := range info.Values {
				dtp := []int{}
				if attribute.AttributeInfo.DtpRule == 1 {
					dtp = getDTPInfo(drugConfigureSetting.DtpIpList, nil, value.DrugName)
				}
				saltName, salt := tools.Encrypt(value.DrugName)
				specCount := models.SpecCount{
					Spec:     value.DrugSpec,
					IsOther:  value.IsOther,
					Salt:     &salt,
					SaltName: &saltName,
					Count:    customDispensingNumberToSelectOption(value.CustomDispensingNumber),
				}
				medicineMiddles = append(medicineMiddles, models.LabelMedicineMiddle{
					ID:      info.ID,
					Label:   value.DrugName,
					Info:    specCount,
					Formkey: formKey,
					DTP:     dtp,
				})
			}

		}
	}

	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, bson.M{
		"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID,
	}).Decode(&visitCycle)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	filterOutSizeConfig := slice.Filter(drugConfig.Configures, func(index int, item models.DrugConfigureInfo) bool {
		_, ok := slice.Find(item.VisitCycles, func(index int, itemID primitive.ObjectID) bool {
			return itemID == visitCycle.SetInfo.Id
		})
		return item.Group == subject.Group && ok
	})

	visitOutSize := false
	if outSize == "1" && visitCycle.SetInfo.IsOpen {
		visitOutSize = true
	}

	if visitOutSize {
		_, _, labelMedicineMiddles, medicineMiddles = getOutSizeLabelMedicine(drugConfigureSetting.DtpIpList, filterOutSizeConfig, visitOID)
	}

	// 使用map来按照ID分组
	groupedLabelMedicineMiddles := make(map[primitive.ObjectID][]models.LabelMedicineMiddle)
	for _, labelMedicineMiddle := range labelMedicineMiddles {
		groupedLabelMedicineMiddles[labelMedicineMiddle.ID] = append(groupedLabelMedicineMiddles[labelMedicineMiddle.ID], labelMedicineMiddle)
	}

	labelMedicineResults := make([]models.LabelMedicineResult, 0)
	for _, objs := range groupedLabelMedicineMiddles {
		var labelMedicineResult models.LabelMedicineResult
		labelMedicineRes := make([]models.LabelMedicineRes, 0)
		for _, obj := range objs {
			var labelMedicine models.LabelMedicineRes
			labelMedicine.ID = obj.ID
			labelMedicine.Label = obj.Label
			labelMedicine.OpenSetting = obj.OpenSetting
			labelMedicine.Info = obj.Info
			if attribute.AttributeInfo.DtpRule == 1 {
				labelMedicine.DTP = obj.DTP
			}
			labelMedicine.Info = obj.Info
			labelMedicine.DTP = obj.DTP
			labelMedicineRes = append(labelMedicineRes, labelMedicine)
			if obj.Formkey != nil && len(obj.Formkey) > 0 {
				labelMedicineResult.Formkey = obj.Formkey
			}
		}
		labelMedicineResult.Label = labelMedicineRes
		labelMedicineResults = append(labelMedicineResults, labelMedicineResult)
	}

	// 使用map来按照ID分组
	groupedMedicineMiddles := make(map[primitive.ObjectID][]models.LabelMedicineMiddle)
	for _, medicineMiddle := range medicineMiddles {
		groupedMedicineMiddles[medicineMiddle.ID] = append(groupedMedicineMiddles[medicineMiddle.ID], medicineMiddle)
	}

	medicineResults := make([]models.LabelMedicineResult, 0)
	for _, objs := range groupedMedicineMiddles {
		var medicineResult models.LabelMedicineResult
		medicineRes := make([]models.LabelMedicineRes, 0)
		for _, obj := range objs {
			var medicine models.LabelMedicineRes
			medicine.ID = obj.ID
			medicine.Label = obj.Label
			medicine.Info = obj.Info
			medicine.DTP = obj.DTP
			medicineRes = append(medicineRes, medicine)
			if obj.Formkey != nil && len(obj.Formkey) > 0 {
				medicineResult.Formkey = obj.Formkey
			}
		}
		medicineResult.Label = medicineRes
		medicineResults = append(medicineResults, medicineResult)
	}

	GetAppLabelMedicineRes := models.GetAppLabelMedicineRes{
		Labels:    labelMedicineResults,
		Medicines: medicineResults,
	}
	return GetAppLabelMedicineRes, nil
}

func (s *DispensingService) GetWebLabelMedicines(ctx *gin.Context, subjectID, visitID, dispensingID string) (interface{}, error) {
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	visitOID, _ := primitive.ObjectIDFromHex(visitID)
	var subject models.Subject
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)

	}
	var drugConfig models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(nil, bson.M{
		"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID,
	}).Decode(&drugConfig)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, bson.M{
		"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID,
	}).Decode(&visitCycle)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	attribute, err := database.GetAttributeWithEnvCohortID(nil, subject.EnvironmentID, subject.CohortID)

	var drugConfigureSetting models.DrugConfigureSetting
	if attribute.AttributeInfo.DtpRule == 1 {
		err = tools.Database.Collection("drug_configure_setting").FindOne(nil, bson.M{
			"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID,
		}).Decode(&drugConfigureSetting)
		if err != nil && err != mongo.ErrNoDocuments {
			return nil, errors.WithStack(err)
		}
	}

	var dispensing models.Dispensing
	if dispensingID != "" {
		dispensingOID, _ := primitive.ObjectIDFromHex(dispensingID)
		err = tools.Database.Collection("dispensing").FindOne(nil, bson.M{
			"_id": dispensingOID,
		}).Decode(&dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	visitOutSize := false
	if (dispensingID == "" || (dispensing.VisitSign && dispensing.Reissue == 0)) && visitCycle.SetInfo.IsOpen {
		visitOutSize = true
	}

	visitInfoP, _ := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
		return item.ID == visitOID
	})
	visitInfo := *visitInfoP

	if subject.Group == "" {
		subject.Group = "N/A"
	}

	if subject.RegisterGroup != "" {
		subject.Group = subject.RegisterGroup
	}

	filterConfig := slice.Filter(drugConfig.Configures, func(index int, item models.DrugConfigureInfo) bool {
		_, ok := slice.Find(item.VisitCycles, func(index int, item primitive.ObjectID) bool {
			return visitOID == item
		})
		return item.Group == subject.Group && ok
	})
	filterOutSizeConfig := slice.Filter(drugConfig.Configures, func(index int, item models.DrugConfigureInfo) bool {
		_, ok := slice.Find(item.VisitCycles, func(index int, itemID primitive.ObjectID) bool {
			return itemID == visitCycle.SetInfo.Id
		})
		return item.Group == subject.Group && ok
	})
	labelMedicine := make([]models.WebLabelMedicineRes, 0)
	medicine := []models.WebMedicineRes{}
	index := -1
	openSettingindex := -1
	for _, info := range filterConfig {
		if info.Label != "" {
			labelMedicineItem := models.WebLabelMedicineRes{}
			itemValue := models.WebLabelMedicineValue{}
			if len(info.Values) == 1 {
				count := customDispensingNumberToSelectOption(info.Values[0].CustomDispensingNumber)
				if len(count) > 1 {
					itemValue.CustomDispensingNumber = count
					itemValue.Spec = info.Values[0].DrugSpec
					itemValue.AutomaticRecode = info.Values[0].AutomaticRecode
					itemValue.Label = info.Label
					labelMedicineItem.Values = &itemValue
				}
			}
			index++
			labelMedicineItem.ID = info.ID
			labelMedicineItem.Index = index
			labelMedicineItem.Name = info.Label
			labelMedicineItem.OpenSetting = info.OpenSetting
			labelMedicineItem.CustomerCalculation = info.CustomerCalculation
			labelMedicineItem.DTP = getDTPInfo(drugConfigureSetting.DtpIpList, info.Values, "")
			labelMedicineItem.DefaultShow = info.ShowAll
			labelMedicine = append(labelMedicine, labelMedicineItem)

		}

		for _, value := range info.Values {
			if value.Label != "" {
				if len(info.Values) == 1 && info.Label != "" { // 单药物  单标签 组合标签都有  优先组合标签
					continue
				}
				labelMedicineItem := models.WebLabelMedicineRes{}
				itemValue := models.WebLabelMedicineValue{}
				index++
				labelMedicineItem.ID = info.ID
				labelMedicineItem.Index = index
				labelMedicineItem.Name = value.Label
				labelMedicineItem.Values = nil
				count := customDispensingNumberToSelectOption(value.CustomDispensingNumber)
				if len(count) > 1 {
					itemValue.CustomDispensingNumber = customDispensingNumberToSelectOption(value.CustomDispensingNumber)
					itemValue.Spec = value.DrugSpec
					itemValue.AutomaticRecode = value.AutomaticRecode
					labelMedicineItem.Values = &itemValue
				}

				labelMedicineItem.CustomerCalculation = info.CustomerCalculation
				labelMedicineItem.DTP = getDTPInfo(drugConfigureSetting.DtpIpList, info.Values, value.DrugName)
				labelMedicineItem.DefaultShow = info.ShowAll

				labelMedicine = append(labelMedicine, labelMedicineItem)
			}
		}
		if info.OpenSetting == 2 && info.Label == "" {
			for _, value := range info.Values {
				openSettingindex++
				saltName, salt := tools.Encrypt(value.DrugName)
				itemMedicine := models.WebMedicineRes{}
				itemMedicine.ID = info.ID
				itemMedicine.Key = openSettingindex
				itemMedicine.Name = value.DrugName
				itemMedicine.Spec = value.DrugSpec
				itemMedicine.IsOther = value.IsOther
				itemMedicine.Salt = &salt
				itemMedicine.SaltName = &saltName
				itemMedicine.AutomaticRecode = value.AutomaticRecode
				itemMedicine.CustomDispensingNumber = customDispensingNumberToSelectOption(value.CustomDispensingNumber)
				itemMedicine.Max = value.DispensingNumber
				itemMedicine.CustomerCalculation = info.CustomerCalculation
				itemMedicine.DTP = getDTPInfo(drugConfigureSetting.DtpIpList, info.Values, value.DrugName)
				itemMedicine.DefaultShow = info.ShowAll
				medicine = append(medicine, itemMedicine)
			}

		}
	}

	if visitOutSize {
		labelMedicine, medicine, _, _ = getOutSizeLabelMedicine(drugConfigureSetting.DtpIpList, filterOutSizeConfig, visitOID)
	}

	GetLabelMedicineRes := models.GetWebLabelMedicineRes{
		VisitID:  visitOID,
		Name:     visitInfo.Name,
		Label:    labelMedicine,
		Medicine: medicine,
	}
	return GetLabelMedicineRes, nil
}

func getOutSizeLabelMedicine(dtpIpList []models.DtpIp, config []models.DrugConfigureInfo, oid primitive.ObjectID) ([]models.WebLabelMedicineRes, []models.WebMedicineRes, []models.LabelMedicineMiddle, []models.LabelMedicineMiddle) {
	index := -1
	openSettingindex := -1
	labelMedicine := make([]models.WebLabelMedicineRes, 0)
	medicine := make([]models.WebMedicineRes, 0)
	appLabelMedicine := make([]models.LabelMedicineMiddle, 0)
	appMedicine := make([]models.LabelMedicineMiddle, 0)

	for _, info := range config {
		formKey := make([]string, 0)
		if len(info.CustomerCalculation) > 0 {
			re := regexp.MustCompile(`\{([^}]+)\}`)
			matches := re.FindAllStringSubmatch(info.CustomerCalculation, -1)
			for _, match := range matches {
				formKey = append(formKey, match[1])
			}
		}

		routineVisitMappingList := slice.Filter(info.RoutineVisitMappingList, func(index int, item models.RoutineVisitMapping) bool {
			_, ok := slice.Find(item.VisitList, func(index int, item primitive.ObjectID) bool {
				return item == oid
			})
			return ok
		})

		if info.Label != "" {
			labelMedicineItem := models.WebLabelMedicineRes{}
			itemValue := models.WebLabelMedicineValue{}
			var drugValue models.Drug

			if len(routineVisitMappingList) > 0 {

				if len(info.Values) == 1 {
					for _, mapping := range routineVisitMappingList {
						DrugNameP, nameOK := slice.Find(mapping.DrugList, func(index int, item models.Drug) bool {
							return item.DrugName == info.Values[0].DrugName
						})
						if !nameOK {
							continue
						}
						drugValue = *DrugNameP

					}

					count := customDispensingNumberToSelectOption(drugValue.CustomDispensingNumber)
					if len(count) > 1 {
						itemValue.CustomDispensingNumber = count
						itemValue.Spec = info.Values[0].DrugSpec
						itemValue.AutomaticRecode = info.Values[0].AutomaticRecode
						itemValue.Label = info.Label
						labelMedicineItem.Values = &itemValue
					}
				}
				index++
				dtp := getDTPInfo(dtpIpList, info.Values, "")
				labelMedicineItem.ID = info.ID
				labelMedicineItem.Index = index
				labelMedicineItem.Name = info.Label
				labelMedicineItem.OpenSetting = info.OpenSetting
				labelMedicineItem.CustomerCalculation = info.CustomerCalculation
				labelMedicineItem.DTP = getDTPInfo(dtpIpList, info.Values, "")
				labelMedicineItem.DefaultShow = info.ShowAll
				labelMedicine = append(labelMedicine, labelMedicineItem)

				tmpLabel := models.LabelMedicineMiddle{
					ID:          info.ID,
					Label:       info.Label,
					OpenSetting: info.OpenSetting,
					Formkey:     formKey,
					DTP:         dtp,
				}
				if len(info.Values) == 1 {
					count := customDispensingNumberToSelectOption(drugValue.CustomDispensingNumber)
					if len(count) > 1 {
						specCount := models.SpecCount{
							Spec:  info.Values[0].DrugSpec,
							Count: customDispensingNumberToSelectOption(drugValue.CustomDispensingNumber),
						}
						tmpLabel.Info = specCount
					}
				}
				appLabelMedicine = append(appLabelMedicine, tmpLabel)
			}

		}
		for _, value := range info.Values {
			var drugValue models.Drug

			for _, mapping := range routineVisitMappingList {
				DrugNameP, nameOK := slice.Find(mapping.DrugList, func(index int, item models.Drug) bool {
					return item.DrugName == value.DrugName
				})
				if nameOK {
					drugValue = *DrugNameP
				}

			}
			if drugValue.ID.IsZero() {
				continue
			}
			if value.Label != "" {
				if len(info.Values) == 1 && info.Label != "" { // 单药物  单标签 组合标签都有  优先组合标签
					continue
				}
				labelMedicineItem := models.WebLabelMedicineRes{}
				itemValue := models.WebLabelMedicineValue{}
				index++
				labelMedicineItem.ID = info.ID
				labelMedicineItem.Index = index
				labelMedicineItem.Name = value.Label
				labelMedicineItem.Values = nil
				count := customDispensingNumberToSelectOption(drugValue.CustomDispensingNumber)
				if len(count) > 1 {
					itemValue.CustomDispensingNumber = customDispensingNumberToSelectOption(drugValue.CustomDispensingNumber)
					itemValue.Spec = value.DrugSpec
					itemValue.AutomaticRecode = value.AutomaticRecode
					labelMedicineItem.Values = &itemValue
				}

				labelMedicineItem.CustomerCalculation = info.CustomerCalculation
				dtp := getDTPInfo(dtpIpList, nil, value.DrugName)
				labelMedicineItem.DTP = dtp
				labelMedicineItem.DefaultShow = info.ShowAll
				labelMedicine = append(labelMedicine, labelMedicineItem)

				specCount := models.SpecCount{
					Spec:  value.DrugSpec,
					Count: customDispensingNumberToSelectOption(drugValue.CustomDispensingNumber)}
				appLabelMedicine = append(appLabelMedicine, models.LabelMedicineMiddle{
					ID:          info.ID,
					Label:       value.Label,
					Info:        specCount,
					Formkey:     formKey,
					DTP:         dtp,
					DefaultShow: info.ShowAll,
				})

			}
		}
		if info.OpenSetting == 2 && info.Label == "" {
			for _, value := range info.Values {
				var drugValue models.Drug
				for _, mapping := range routineVisitMappingList {
					DrugNameP, nameOK := slice.Find(mapping.DrugList, func(index int, item models.Drug) bool {
						return item.DrugName == value.DrugName
					})
					if !nameOK {
						continue
					}
					drugValue = *DrugNameP

				}
				if drugValue.ID.IsZero() {
					continue
				}
				openSettingindex++
				saltName, salt := tools.Encrypt(value.DrugName)
				itemMedicine := models.WebMedicineRes{}
				itemMedicine.ID = info.ID
				itemMedicine.Key = openSettingindex
				itemMedicine.Name = value.DrugName
				itemMedicine.Spec = value.DrugSpec
				itemMedicine.IsOther = value.IsOther
				itemMedicine.Salt = &salt
				itemMedicine.SaltName = &saltName
				itemMedicine.AutomaticRecode = value.AutomaticRecode
				itemMedicine.CustomDispensingNumber = customDispensingNumberToSelectOption(drugValue.CustomDispensingNumber)
				itemMedicine.Max = value.DispensingNumber
				itemMedicine.CustomerCalculation = info.CustomerCalculation
				dtp := getDTPInfo(dtpIpList, nil, value.DrugName)
				itemMedicine.DTP = dtp
				itemMedicine.DefaultShow = info.ShowAll

				medicine = append(medicine, itemMedicine)
				specCount := models.SpecCount{
					Spec:     value.DrugSpec,
					IsOther:  value.IsOther,
					Salt:     &salt,
					SaltName: &saltName,
					Count:    customDispensingNumberToSelectOption(drugValue.CustomDispensingNumber),
				}
				appMedicine = append(appMedicine, models.LabelMedicineMiddle{
					ID:          info.ID,
					Label:       value.DrugName,
					Info:        specCount,
					Formkey:     formKey,
					DTP:         dtp,
					DefaultShow: info.ShowAll,
				})
			}

		}
	}
	return labelMedicine, medicine, appLabelMedicine, appMedicine
}

//func getOutSizeLabelMedicine(){
//
//}

func (s *DispensingService) GetAppAddDispensingOperationDTP(ctx *gin.Context, subjectID, visitCycleID, roleID string) (interface{}, error) {
	getAppAddDispensingOperationDTPREQ := models.GetAppAddDispensingOperationDTPREQ{}
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	visitCycleOID, _ := primitive.ObjectIDFromHex(visitCycleID)
	roleOID, _ := primitive.ObjectIDFromHex(roleID)

	var subject models.Subject
	var visitCycle models.VisitCycle
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = tools.Database.Collection("visit_cycle").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&visitCycle)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	infoP, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
		return item.ID == visitCycleOID
	})

	attribute, err := database.GetAttributeWithEnvCohortID(nil, subject.EnvironmentID, subject.CohortID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if ok && attribute.AttributeInfo.DtpRule == 2 {
		info := *infoP
		getAppAddDispensingOperationDTPREQ.DTP = info.DTPType
	}
	total, err := tools.Database.Collection("project_role_permission").CountDocuments(nil, bson.M{"project_id": subject.ProjectID, "_id": roleOID, "permissions": "operation.subject.medicine.formula.update"})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if total > 0 {
		getAppAddDispensingOperationDTPREQ.Operation = true

	}
	return getAppAddDispensingOperationDTPREQ, nil
}

func (s *DispensingService) DispensingResume(ctx *gin.Context, dispensingID string, roleId string) (interface{}, error) {
	now := time.Duration(time.Now().Unix())
	user := models.User{}
	u, _ := ctx.Get("user")
	if u != nil {
		user = u.(models.User)
	}

	var project models.Project
	var dispensing models.Dispensing
	var subject models.Subject
	var attribute models.Attribute
	reqSubjectOID := primitive.NilObjectID
	dispensingOID, _ := primitive.ObjectIDFromHex(dispensingID)

	callback := func(sctx mongo.SessionContext) (interface{}, error) {

		err := tools.Database.Collection("dispensing").FindOneAndUpdate(sctx, bson.M{"_id": dispensingOID}, bson.M{"$set": bson.M{"status": 1, "dispensing_time": 0}}).Decode(&dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		reqSubjectOID = dispensing.SubjectID
		err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": dispensing.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": dispensing.SubjectID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		attribute, err = database.GetAttributeWithEnvCohortID(nil, dispensing.EnvironmentID, dispensing.CohortID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		subjectReplaceText := GetSubjectReplaceText(ctx, attribute)
		histories := []models.History{}
		histories = append(histories, models.History{
			Key:  "history.dispensing.resume",
			OID:  dispensingOID,
			Data: map[string]interface{}{"label": subjectReplaceText, "subject": subject.Info[0].Value},
			Time: now,
			UID:  user.ID,
			User: user.Name,
		})
		ctx.Set("HISTORY", histories)

		// TODO 再随机第一阶段 最后一个发药全部取回，则回到第一阶段
		if !dispensing.CohortID.IsZero() {
			env, cohort := database.GetEnvCohortInfo(project, dispensing.EnvironmentID, dispensing.CohortID)
			reRandom := models.IsCohortReRandom(project, dispensing.EnvironmentID.Hex(), cohort.Name)
			if (project.Type == 3 && !tools.InRandomIsolation(project.Number)) || (project.Type == 2 && reRandom) {
				// 获取第一阶段、第二阶段cohort
				curStage := ""
				nextStage := ""
				firstCohort := models.Cohort{}
				secondCohort := models.Cohort{}
				firstSubject := models.Subject{}
				secondSubject := models.Subject{}
				firstCohortP, ok := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
					return (item.Type == 1 || project.Type == 3) && item.LastID.IsZero()
				})
				if ok {
					firstCohort = *firstCohortP
					err = tools.Database.Collection("subject").FindOne(sctx, bson.M{
						"cohort_id": firstCohort.ID,
						"info": bson.M{
							"$elemMatch": bson.M{
								"name":  "shortname",
								"value": subject.Info[0].Value,
							},
						},
						"deleted": bson.M{"$ne": true},
					}).Decode(&firstSubject)
					if err != nil && err != mongo.ErrNoDocuments {
						return nil, errors.WithStack(err)
					}
				}

				secondCohortP, ok := slice.Find(env.Cohorts, func(index int, item models.Cohort) bool {
					return (item.Type == 1 || project.Type == 3) && !item.LastID.IsZero()
				})
				if ok {
					secondCohort = *secondCohortP
					err = tools.Database.Collection("subject").FindOne(sctx, bson.M{
						"cohort_id": secondCohort.ID,
						"info": bson.M{
							"$elemMatch": bson.M{
								"name":  "shortname",
								"value": subject.Info[0].Value,
							},
						},
						"deleted": bson.M{"$ne": true},
					}).Decode(&secondSubject)
					if err != nil && err != mongo.ErrNoDocuments {
						return nil, errors.WithStack(err)
					}
				}
				if firstSubject.CohortID == dispensing.CohortID {
					curStage = firstCohort.Name
					nextStage = secondCohort.Name
				} else if secondSubject.CohortID == dispensing.CohortID {
					curStage = secondCohort.Name
					nextStage = firstCohort.Name
				}

				if !firstSubject.ID.IsZero() && !secondSubject.ID.IsZero() {

					err = BackToFirstStage(ctx, sctx, firstSubject, secondSubject, curStage, nextStage, user)
					if err != nil {
						return nil, errors.WithStack(err)
					}
					reqSubjectOID = firstSubject.ID
				}
			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	callbackNotice := func(sctx mongo.SessionContext) (interface{}, error) {
		err = task.UpdateNotice(sctx, 4, dispensing.EnvironmentID, dispensing.CohortID, subject.ProjectSiteID, subject.ID)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err = tools.Transaction(callbackNotice)
	if err != nil {
		return nil, err
	}

	resDispensingList, err := GetDispensingList(ctx, subject.ID.Hex(), roleId)
	if err != nil {
		return nil, err
	}

	k := -1
	for i, resDispensing := range resDispensingList {
		if dispensingOID == resDispensing.ID {
			k = i
		}
		if k != -1 && k < i {
			//v2恢复发放、删除v3已经产生的app发放任务
			err = s.PatchAppDispenseTaskVoided(ctx, resDispensing.ID.Hex())
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
	}

	if len(roleId) > 0 && attribute.AttributeInfo.Dispensing {
		err = PatchAppDispenseTask(ctx, subject.ID.Hex(), roleId)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	return bson.M{"id": reqSubjectOID}, nil
}

func (s *DispensingService) DoseInfo(ctx *gin.Context, visitID string, subjectID string) (interface{}, error) {
	// 查询是否开启了剂量
	var doseInfo models.RepDoseInfo
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	visitOID, _ := primitive.ObjectIDFromHex(visitID)
	// 查询受试者信息
	var subject models.Subject
	var visitCycle models.VisitCycle
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if subject.Group == "" {
		subject.Group = "N/A"
	}

	if subject.RegisterGroup != "" {
		subject.Group = subject.RegisterGroup
	}

	count, err := tools.Database.Collection("drug_package_configure").CountDocuments(nil, bson.M{"env_id": subject.EnvironmentID, "is_open": true})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if count > 0 {
		doseInfo.IsPage = true
	}
	err = tools.Database.Collection("visit_cycle").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&visitCycle)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	visitP, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
		return item.ID == visitOID
	})
	if !ok {
		return nil, err
	}
	visit := *visitP
	if !visit.DoseAdjustment { //未开启剂量
		return nil, nil
	}

	var drugConfigureSetting models.DrugConfigureSetting
	err = tools.Database.Collection("drug_configure_setting").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&drugConfigureSetting)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	// 查询表单信息
	var form models.Form
	err = tools.Database.Collection("form").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&form)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	formItemP, ok := slice.Find(form.Fields, func(index int, item models.Field) bool {
		return item.ID.Hex() == drugConfigureSetting.DoseFormId && *item.Status == 1
	})
	if !ok {
		return nil, nil
	}

	// 查询表单信息
	var dispensings []models.ResDispensing
	cursor, err := tools.Database.Collection("dispensing").Find(nil, bson.M{"subject_id": subject.ID}, &options.FindOptions{
		Sort: bson.D{
			{"serial_number", 1},
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &dispensings)
	if err != nil {
		return nil, err
	}

	formItem := *formItemP

	//

	visitNumbers, visitInheritanceCount := DoseOpenCheck(subject, drugConfigureSetting, dispensings, 2)

	if drugConfigureSetting.SelectType == 1 {
		// 剂量调整
		// 查询是否配了初始剂量 || 获取当前剂量
		var dose models.DoseLevel
		for _, dispensing := range dispensings {
			if dispensing.DoseInfo != nil && dispensing.DoseInfo.DoseLevelList != nil {
				dose = *dispensing.DoseInfo.DoseLevelList
			}
		}

		// 查询是否可以用初始和当前剂量
		doseLevelList := slice.Filter(drugConfigureSetting.DoseLevelList, func(index int, item models.DoseLevel) bool {
			_, matchGroup := slice.Find(item.Group, func(index int, group string) bool {
				return group == subject.Group
			})
			return matchGroup
		})

		if len(doseLevelList) == 0 {
			return nil, nil
		}

		_, ok := slice.Find(doseLevelList, func(index int, item models.DoseLevel) bool {
			return item.InitialDose
		})

		// 查询是否可以下降一个水平选项 （次数用完 || 当前水平已经是最后一个）
		i := -1
		slice.ForEach(drugConfigureSetting.DoseLevelList, func(index int, item models.DoseLevel) {
			if item.Name == dose.Name {
				i = index
			}
		})

		frequency := 0
		slice.ForEach(dispensings, func(index int, item models.ResDispensing) {
			if item.DoseInfo != nil {
				frequency = frequency + item.DoseInfo.Frequency
			}
		})
		if i == len(drugConfigureSetting.DoseLevelList) || (drugConfigureSetting.Frequency != nil && frequency == *drugConfigureSetting.Frequency) {
			doseInfo.NoFrequency = true
		}

		//if !dose.ID.IsZero() || ok {
		//	for i, option := range formItem.Options {
		//		if option.Label == "form.control.type.options.one" {
		//			formItem.Options[i].Label = locales.Tr(ctx, option.Label)
		//		} else if option.Label == "form.control.type.options.two" {
		//			formItem.Options[i].Label = locales.Tr(ctx, option.Label)
		//			if dose.ID.IsZero() {
		//				formItem.Options[i].Disable = true
		//			}
		//		} else if option.Label == "form.control.type.options.three" {
		//			formItem.Options[i].Label = locales.Tr(ctx, option.Label)
		//			if doseInfo.NoFrequency {
		//				formItem.Options[i].Disable = true
		//			}
		//		}
		//	}
		//	doseInfo.Field = &formItem
		//}
		var inheritValue string
		if dose.ID.IsZero() {
			doseInfo.OnlyInit = true
			for i, option := range formItem.Options {
				if option.Label == "form.control.type.options.one" {
					formItem.Options[i].Label = locales.Tr(ctx, option.Label)
					if ok {
						inheritValue = formItem.Options[i].Value
					}
				} else if option.Label == "form.control.type.options.two" {
					formItem.Options[i].Label = locales.Tr(ctx, option.Label)
					formItem.Options[i].Disable = true

				} else if option.Label == "form.control.type.options.three" {
					formItem.Options[i].Label = locales.Tr(ctx, option.Label)
					formItem.Options[i].Disable = true

				}
			}
			doseInfo.Field = &formItem
		} else {
			for i, option := range formItem.Options {
				if option.Label == "form.control.type.options.one" {
					formItem.Options[i].Label = locales.Tr(ctx, option.Label)
				} else if option.Label == "form.control.type.options.two" {
					formItem.Options[i].Label = locales.Tr(ctx, option.Label)

				} else if option.Label == "form.control.type.options.three" {
					formItem.Options[i].Label = locales.Tr(ctx, option.Label)
					if doseInfo.NoFrequency {
						formItem.Options[i].Disable = true
					}

				}
			}
			doseInfo.Field = &formItem
		}
		doseInfo.IsLevel = true
		if inheritValue != "" {
			doseInfo.InheritValue = &inheritValue

		}
	} else { // 访视判断
		// 表单信息

		// 查询是否需要继承上次配置
		//visitJudgmentP, ok := slice.Find(drugConfigureSetting.VisitJudgmentList, func(index int, item models.VisitJudgment) bool {
		//	return item.VisitInheritance == true
		//})
		visitJudgmentListFilter := slice.Filter(drugConfigureSetting.VisitJudgmentList, func(index int, item models.VisitJudgment) bool {
			_, matchGroup := slice.Find(item.Group, func(index int, group string) bool {
				return group == subject.Group
			})
			return matchGroup
		})
		if len(visitJudgmentListFilter) == 0 { // 当前组别没有配置
			return nil, nil
		}
		doseInfo.Field = &formItem
		formItem.Options = slice.Filter(formItem.Options, func(index int, item models.Option) bool {
			_, optionOk := slice.Find(visitJudgmentListFilter, func(index int, option models.VisitJudgment) bool {
				return item.Value == option.Name
			})
			return optionOk
		})
		doseInfo.Field = &formItem

		dispensingFilter := slice.Filter(dispensings, func(index int, item models.ResDispensing) bool {
			return item.DoseInfo != nil && item.DoseInfo.VisitJudgmentList != nil && item.Status == 2
		})
		for i, option := range formItem.Options {
			if option.Label == "form.control.type.options.one" {
				formItem.Options[i].Label = locales.Tr(ctx, option.Label)
			} else if option.Label == "form.control.type.options.two" {
				formItem.Options[i].Label = locales.Tr(ctx, option.Label)

			} else if option.Label == "form.control.type.options.three" {
				formItem.Options[i].Label = locales.Tr(ctx, option.Label)
			}
		}
		visitJudgmentListP, inherit := slice.Find(visitJudgmentListFilter, func(index int, item models.VisitJudgment) bool {
			_, ok := slice.Find(dispensingFilter, func(index int, it models.ResDispensing) bool {
				return it.DoseInfo.VisitJudgmentList.ID == item.ID
			})
			return item.VisitInheritance && ok
		})

		if inherit {
			if visitInheritanceCount {
				if visitNumbers[visit.Number] {
					visitJudgmentList := *visitJudgmentListP
					doseInfo.InheritValue = &visitJudgmentList.Name
					// 匹配对应的标签、药物
					doseInfo.Label, doseInfo.Name, _, err = getVisitGroupLabel(ctx, visitOID, subject, visitJudgmentList.DoseDistribution)
					if err != nil {
						return nil, err
					}
				}
			} else {
				visitJudgmentList := *visitJudgmentListP
				doseInfo.InheritValue = &visitJudgmentList.Name
				// 匹配对应的标签、药物
				doseInfo.Label, doseInfo.Name, _, err = getVisitGroupLabel(ctx, visitOID, subject, visitJudgmentList.DoseDistribution)
				if err != nil {
					return nil, err
				}
			}
		}

	}
	return doseInfo, nil
}

func (s *DispensingService) DoseInfoRes(ctx *gin.Context, reqDose models.ReqDose) (interface{}, error) {
	// 查询是否开启了剂量
	// 查询受试者信息
	var subject models.Subject
	var visitCycle models.VisitCycle
	err := tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": reqDose.SubjectID}).Decode(&subject)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if subject.Group == "" {
		subject.Group = "N/A"
	}

	if subject.RegisterGroup != "" {
		subject.Group = subject.RegisterGroup
	}

	err = tools.Database.Collection("visit_cycle").FindOne(nil, bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&visitCycle)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	visitP, ok := slice.Find(visitCycle.Infos, func(index int, item models.VisitCycleInfo) bool {
		return item.ID == reqDose.VisitID
	})
	if !ok {
		return nil, nil
	}
	visit := *visitP
	if !visit.DoseAdjustment { //未开启剂量
		return nil, nil
	}
	var dose models.RepDoseInfo
	lastDispensing, lastDoseLevel, doseLevel, visitJudgment, doseType, err := getCurrentDose(ctx, subject, reqDose.Value)
	if err != nil {
		return nil, err
	}
	if !doseLevel.ID.IsZero() {
		dose.Label, dose.Name, _, err = getVisitGroupLabel(ctx, reqDose.VisitID, subject, doseLevel.DoseDistribution)

		if doseType == 1 {
			labels := []models.LabelValue{}
			name := []models.LabelValue{}
			for _, value := range dose.Label {
				labelOk := slice.Filter(lastDispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
					return item.DoseInfo != nil && item.DoseInfo.DoseLevelList != nil && item.Label == value.Label
				})
				_, medicineOk := slice.Find(lastDispensing.OtherDispensingMedicines, func(index int, item models.OtherDispensingMedicine) bool {
					return item.DoseInfo != nil && item.DoseInfo.DoseLevelList != nil && item.Label == value.Label
				})
				if len(labelOk) > 0 || medicineOk {
					//if len(labelOk) > 0 {
					//	value.Value = len(labelOk)
					//} else {
					//	other := *otherP
					//	value.Value = other.Count
					//}
					labels = append(labels, value)
				}
			}
			for _, value := range dose.Name {
				labelOk := slice.Filter(lastDispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
					return item.DoseInfo != nil && item.DoseInfo.DoseLevelList != nil && item.Name == value.Label
				})
				otherP, medicineOk := slice.Find(lastDispensing.OtherDispensingMedicines, func(index int, item models.OtherDispensingMedicine) bool {
					return item.DoseInfo != nil && item.DoseInfo.DoseLevelList != nil && item.Name == value.Label
				})
				if len(labelOk) > 0 || medicineOk {
					if len(labelOk) > 0 {
						value.Value = len(labelOk)
					} else {
						other := *otherP
						value.Value = other.Count
					}
					name = append(name, value)
				}
			}
			dose.Label = labels
			dose.Name = name
		}

		var levelTip models.LevelTip
		if !lastDoseLevel.ID.IsZero() {
			levelTip.Last = lastDoseLevel.Name
		}
		levelTip.Current = doseLevel.Name
		dose.LevelTip = &levelTip
	}
	if !visitJudgment.ID.IsZero() {
		dose.Label, dose.Name, _, err = getVisitGroupLabel(ctx, reqDose.VisitID, subject, visitJudgment.DoseDistribution)
	}

	return dose, nil
}

// 开启后续阶段访视
func (s *DispensingService) StartFollowUpVisits(ctx *gin.Context, data map[string]interface{}) error {
	projectId := data["projectId"].(string)
	projectOID, _ := primitive.ObjectIDFromHex(projectId)

	envId := data["envId"].(string)
	envOID, _ := primitive.ObjectIDFromHex(envId)
	cohortName := data["cohortName"].(string)

	shortname := data["shortname"].(string)
	settingChecked := data["settingChecked"].(bool)
	currentStage := data["currentStage"].(string)
	nextStage := data["nextStage"].(string)
	user := models.User{}
	u, _ := ctx.Get("user")
	if u != nil {
		user = u.(models.User)
	}

	// 查询项目
	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return errors.WithStack(err)
	}

	var env models.Environment
	for _, e := range project.Environments {
		if e.ID == envOID {
			env = e
		}
	}

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		stage1Id := env.Cohorts[0].ID
		stage2Id := env.Cohorts[1].ID
		is := models.IsCohortReRandom(project, envId, cohortName)
		if project.Type == 2 && is {
			stage1Id, _ = models.GetReRandomCohortFirstId(project, envId, cohortName)
			stage2Id, _ = models.GetReRandomCohortSecondId(project, envId, cohortName)
		}

		// 将未发放的药物信息状态更新为作废
		if settingChecked {
			subjectFile := bson.M{
				"cohort_id": stage1Id,
				"info": bson.M{
					"$elemMatch": bson.M{
						"name":  "shortname",
						"value": shortname,
					},
				},
				"deleted": bson.M{"$ne": true},
			}

			var subject models.Subject
			err := tools.Database.Collection("subject").FindOne(sctx, subjectFile).Decode(&subject)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			var dispensingUpdate []models.Dispensing
			cursor, err := tools.Database.Collection("dispensing").Find(sctx, bson.M{"subject_id": subject.ID, "status": 1})
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &dispensingUpdate)
			if err != nil {
				return nil, errors.WithStack(err)

			}
			update := bson.M{"$set": bson.M{"status": 3, "start_visit": 1}}
			_, err = tools.Database.Collection("dispensing").UpdateMany(sctx, bson.M{"subject_id": subject.ID, "status": 1}, update)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			//TODO 在随机项目判断发药完成后是否需要添加下一阶段的受试者
			err = DispensingAddSubject(ctx, sctx, subject.ProjectID, subject.ID, "")

			// 添加轨迹
			nextStageTest := "-"
			if nextStage != "" {
				nextStageTest = nextStage
			}
			attribute := models.Attribute{}
			_ = tools.Database.Collection("attribute").FindOne(sctx, bson.M{"env_id": subject.EnvironmentID, "cohort_id": subject.CohortID}).Decode(&attribute)
			subjectReplaceText := GetSubjectReplaceText(ctx, attribute)
			//subjectNo := ""
			//for _, info := range subject.Info {
			//	if info.Name == "shortname" {
			//		subjectNo = info.Value.(string)
			//	}
			//}
			histories := []interface{}{}
			history := models.History{
				Key: "history.subject.start-follow-up-visits",
				OID: subject.ID,
				//CustomTemps: temps,
				Data: map[string]interface{}{
					"label":        subjectReplaceText,
					"subjectNo":    shortname,
					"currentStage": currentStage,
					"nextStage":    nextStageTest,
				},
				Time: time.Duration(time.Now().Unix()),
				UID:  user.ID,
				User: user.Name,
			}

			histories = append(histories, history)
			historyData := bson.M{
				"label":   subjectReplaceText,
				"subject": subject.Info[0].Value,
			}
			for _, item := range dispensingUpdate {
				customTempOption := []models.CustomTempOption{}
				customTemps := []models.CustomTemp{}

				customTempOption = append(customTempOption, models.CustomTempOption{
					Index: 1,
					Key:   "history.dispensing.single.notAttendRemark",
					Data:  bson.M{"random": currentStage, "atRandom": nextStageTest},
				})
				customTemps = append(customTemps, models.CustomTemp{
					ParKey:              "data",
					ConnectingSymbolKey: "history.dispensing.single.comma",
					LastSymbolKey:       "history.dispensing.single.period",
					CustomTempOptions:   customTempOption,
				})
				histories = append(histories, models.History{
					Key:         "history.dispensing.dispensingCustomer-not-attend",
					OID:         item.ID,
					CustomTemps: customTemps,
					Data:        historyData,
					Time:        time.Duration(time.Now().Unix()),
					UID:         user.ID,
					User:        user.Name,
					Unicode:     user.Unicode,
				})
			}

			_, err = tools.Database.Collection("history").InsertMany(sctx, histories)
			if err != nil {
				return nil, errors.WithStack(err)
			}
		} else { // 移除第二阶段的受试者

			subjectFileOne := bson.M{
				"cohort_id": stage1Id,
				"info": bson.M{
					"$elemMatch": bson.M{
						"name":  "shortname",
						"value": shortname,
					},
				},
				"deleted": bson.M{"$ne": true},
			}
			var subject models.Subject
			err := tools.Database.Collection("subject").FindOne(sctx, subjectFileOne).Decode(&subject)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			if subject.Info != nil && len(subject.Info) > 0 {
				subjectFileTwo := bson.M{
					"cohort_id": stage2Id,
					"info": bson.M{
						"$elemMatch": bson.M{
							"name":  "shortname",
							"value": subject.Info[0].Value,
						},
					},
					"deleted": bson.M{"$ne": true},
				}
				var findSubject models.Subject
				err := tools.Database.Collection("subject").FindOne(sctx, subjectFileTwo).Decode(&findSubject)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				if findSubject.Info != nil && len(findSubject.Info) > 0 {
					// 查询当前要删除的受试者是否有进行发药
					count, _ := tools.Database.Collection("dispensing").CountDocuments(sctx, bson.M{"subject_id": findSubject.ID, "status": bson.M{"$ne": 1}})
					if count == 0 {
						err = BackToFirstStage(ctx, sctx, subject, findSubject, currentStage, nextStage, user)
						if err != nil {
							return nil, err
						}
					}
				}
			}
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *DispensingService) DispensingConfirmTable(ctx *gin.Context, req models.ReqDispensingConfirmTable) ([]models.ReqDispensingConfirmTableInfo, error) {
	var drugConfig models.DrugConfigure
	err := tools.Database.Collection("drug_configure").FindOne(nil, bson.M{"env_id": req.EnvironmentID, "cohort_id": req.CohortID}).Decode(&drugConfig)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	isBlindedRole, err := tools.IsBlindedRole(req.RoleID.Hex())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 选了水平继承访视 需要查出上一次
	var lastDispensing models.Dispensing
	//var lastDoseLevel models.DoseLevel
	//var doseLevel models.DoseLevel
	//var visitJudgment models.VisitJudgment
	var doseType int
	var subject models.Subject
	tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": req.SubjectID}).Decode(&subject)
	if req.LevelOption != "" {
		lastDispensing, _, _, _, doseType, err = getCurrentDose(ctx, subject, req.LevelOption)

	}

	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, bson.M{"env_id": req.EnvironmentID, "cohort_id": req.CohortID}).Decode(&visitCycle)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var dispensing models.Dispensing
	err = tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": req.DispensingID}).Decode(&dispensing)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, errors.WithStack(err)
	}

	visitOutSize := false
	if (req.DispensingID.IsZero() || dispensing.VisitSign) && visitCycle.SetInfo.IsOpen {
		visitOutSize = true
	}

	isBlindDrugMap, err := tools.IsBlindDrugMap(req.EnvironmentID)

	labelsInfo := make([]models.ReqDispensingConfirmTableInfo, 0)

	for _, info := range req.ReqDispensingConfirmTableInfo {
		DrugConfigureInfosP, ok := slice.Find(drugConfig.Configures, func(index int, item models.DrugConfigureInfo) bool {
			return info.ID == item.ID
		})
		if ok {
			DrugConfigureInfos := *DrugConfigureInfosP
			slice.SortByField(DrugConfigureInfos.Values, "DrugSpec")
			// 水平取实际用药数量
			if info.Level != "" {

				if info.Label == DrugConfigureInfos.Label {
					for _, value := range DrugConfigureInfos.Values {
						if isBlindedRole && isBlindDrugMap[value.DrugName] {
							info.IPName = tools.BlindData
						} else {
							info.IPName = value.DrugName
						}
						if doseType == 1 {
							count := updateLabelNameCounts(lastDispensing, value.DrugName)
							info.Count = &count
						} else {
							info.Count = &value.DispensingNumber
						}
						if visitOutSize {
							count, err := OutSizeDispensingNumber(value.DrugName, DrugConfigureInfos.RoutineVisitMappingList, req.VisitID)
							if err != nil {
								return nil, err
							}
							info.Count = &count
							if *info.Count == 0 {
								continue
							}
						}

						info.Spec = value.DrugSpec
						if len(labelsInfo) > 0 && labelsInfo[len(labelsInfo)-1].Label == info.Label && labelsInfo[len(labelsInfo)-1].IPName == info.IPName && labelsInfo[len(labelsInfo)-1].Spec == info.Spec {
							allCount := *labelsInfo[len(labelsInfo)-1].Count + *info.Count
							labelsInfo[len(labelsInfo)-1].Count = &allCount
						} else {
							labelsInfo = append(labelsInfo, info)
						}
					}
				} else {
					itemLabelP, ok := slice.Find(DrugConfigureInfos.Values, func(index int, item models.DrugValue) bool {
						return item.Label == info.Label
					})
					if ok {
						itemLabel := *itemLabelP
						if isBlindedRole && isBlindDrugMap[itemLabel.DrugName] {
							info.IPName = tools.BlindData
						} else {
							info.IPName = itemLabel.DrugName
						}
						if doseType == 1 {
							labelOk := slice.Filter(lastDispensing.DispensingMedicines, func(index int, item models.DispensingMedicine) bool {
								return itemLabel.DrugName == item.Name
							})
							otherP, medicineOk := slice.Find(lastDispensing.OtherDispensingMedicines, func(index int, item models.OtherDispensingMedicine) bool {
								return itemLabel.DrugName == item.Name
							})
							if len(labelOk) > 0 || medicineOk {
								if len(labelOk) > 0 {
									count := len(labelOk)
									info.Count = &count
								} else {
									other := *otherP
									info.Count = &other.Count
								}
							}
						} else {
							info.Count = &itemLabel.DispensingNumber
						}
						info.Spec = itemLabel.DrugSpec
						if len(labelsInfo) > 0 && labelsInfo[len(labelsInfo)-1].Label == info.Label && labelsInfo[len(labelsInfo)-1].IPName == info.IPName && labelsInfo[len(labelsInfo)-1].Spec == info.Spec {
							allCount := *labelsInfo[len(labelsInfo)-1].Count + *info.Count
							labelsInfo[len(labelsInfo)-1].Count = &allCount
						} else {
							labelsInfo = append(labelsInfo, info)
						}
					}
				}
			} else {

				if info.Label == DrugConfigureInfos.Label {
					count := info.Count
					for _, value := range DrugConfigureInfos.Values {
						if isBlindedRole && isBlindDrugMap[value.DrugName] {
							info.IPName = tools.BlindData
						} else {
							info.IPName = value.DrugName
						}
						if count == nil {
							info.Count = &value.DispensingNumber
							if visitOutSize {
								count, err := OutSizeDispensingNumber(value.DrugName, DrugConfigureInfos.RoutineVisitMappingList, req.VisitID)
								if err != nil {
									return nil, err
								}
								if count == 0 {
									continue
								}
								info.Count = &count
							}
						}
						info.Spec = value.DrugSpec
						if len(labelsInfo) > 0 && labelsInfo[len(labelsInfo)-1].Label == info.Label && labelsInfo[len(labelsInfo)-1].IPName == info.IPName && labelsInfo[len(labelsInfo)-1].Spec == info.Spec {
							allCount := *labelsInfo[len(labelsInfo)-1].Count + *info.Count
							labelsInfo[len(labelsInfo)-1].Count = &allCount
						} else {
							labelsInfo = append(labelsInfo, info)
						}
					}
				} else {
					itemLabelP, ok := slice.Find(DrugConfigureInfos.Values, func(index int, item models.DrugValue) bool {
						return item.Label == info.Label
					})
					if ok {
						itemLabel := *itemLabelP
						if isBlindedRole && isBlindDrugMap[itemLabel.DrugName] {
							info.IPName = tools.BlindData
						} else {
							info.IPName = itemLabel.DrugName
						}
						if info.Count == nil {
							info.Count = &itemLabel.DispensingNumber
							if visitOutSize {
								count, err := OutSizeDispensingNumber(itemLabel.DrugName, DrugConfigureInfos.RoutineVisitMappingList, req.VisitID)
								if err != nil {
									return nil, err
								}
								info.Count = &count
							}
						}

						info.Spec = itemLabel.DrugSpec
						if len(labelsInfo) > 0 && labelsInfo[len(labelsInfo)-1].Label == info.Label && labelsInfo[len(labelsInfo)-1].IPName == info.IPName && labelsInfo[len(labelsInfo)-1].Spec == info.Spec {
							allCount := *labelsInfo[len(labelsInfo)-1].Count + *info.Count
							labelsInfo[len(labelsInfo)-1].Count = &allCount
						} else {
							labelsInfo = append(labelsInfo, info)
						}
					}
				}

			}
		}

	}
	return labelsInfo, nil
}

func GetDispensingByIDs(ctx context.Context, idList []primitive.ObjectID) ([]models.Dispensing, error) {
	var data []models.Dispensing
	cursor, err := tools.Database.Collection("dispensing").Find(ctx, bson.M{"_id": bson.M{"$in": idList}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(ctx, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return data, nil
}

func (s *DispensingService) GetMedicineUnBlind(ctx *gin.Context, subjectID, ID string) (interface{}, error) {
	type ResStr struct {
		UnblindingApprovals []models.UrgentUnblindingApproval `json:"unblindingApprovals" ` //申请记录
		Number              string                            `json:"number" `              //申请记录
		Name                string                            `json:"name" `                //申请记录
		ApprovalUsers       []models.ApprovalUser             `json:"approvalUsers" `       //审批用户
		UnBlinding          *models.MedicineUnblind           `json:"unBlinding"`
	}

	var res ResStr
	OID, _ := primitive.ObjectIDFromHex(ID)
	subjectOID, _ := primitive.ObjectIDFromHex(subjectID)
	var medicine models.Medicine
	err := tools.Database.Collection("medicine").FindOne(nil, bson.M{"_id": OID}).Decode(&medicine)
	if err != nil {
		return res, errors.WithStack(err)
	}

	var subject models.Subject
	err = tools.Database.Collection("subject").FindOne(nil, bson.M{"_id": subjectOID}).Decode(&subject)
	if err != nil {
		return res, errors.WithStack(err)
	}

	// 查询该环境关联的所有用户
	userProjectEnvironmentList := make([]models.UserProjectEnvironment, 0)
	userProjectEnvironmentCursor, err := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"env_id": subject.EnvironmentID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userProjectEnvironmentCursor.All(nil, &userProjectEnvironmentList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var userIDs []primitive.ObjectID
	for _, upel := range userProjectEnvironmentList {
		userIDs = append(userIDs, upel.UserID)
	}
	// 查询user
	var userList []models.User
	userCursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIDs}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userCursor.All(nil, &userList)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 查询项目角色权限
	projectRolePermissionList := make([]models.ProjectRolePermission, 0)
	projectRolePermissionCursor, err := tools.Database.Collection("project_role_permission").Find(nil, bson.M{"project_id": subject.ProjectID, "name": bson.M{"$ne": "Project-Admin"}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = projectRolePermissionCursor.All(nil, &projectRolePermissionList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 查询用户关联的角色
	userSiteList := make([]models.UserSite, 0)
	userSiteCursor, err := tools.Database.Collection("user_site").Find(nil, bson.M{"env_id": subject.EnvironmentID})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = userSiteCursor.All(nil, &userSiteList)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	_, _, ipUnblindingApprovalUser := queryApprovalUser(userProjectEnvironmentList, userList, projectRolePermissionList, userSiteList, subject.ProjectSiteID)
	res.UnblindingApprovals = medicine.UnblindingApprovals
	res.ApprovalUsers = ipUnblindingApprovalUser
	res.Number = medicine.Number
	res.Name = medicine.Name
	res.UnBlinding = medicine.Unblinding
	return res, errors.WithStack(err)
}

func (s *DispensingService) IPUnblindingApplication(ctx *gin.Context) (interface{}, error) {
	//参数转化

	var req models.UnblindingMedicineReq
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	roleOID := req.RoleId
	subjectOID := req.SubjectId
	medicineOID := req.MedicineID
	dispensingOID := req.DispensingID

	//获取当前用户登录信息
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, err
	}
	//校验密码
	err = tools.PasswordDetection(ctx, me.Email, req.Password)
	if err != nil {
		return nil, tools.BuildServerError(ctx, "unblinding_password_error", tools.UnblindingPasswordError)
	}

	var now time.Time
	approvalNumber := ""
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		now = time.Now()
		var subject models.Subject
		err := tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		var medicine models.Medicine
		err = tools.Database.Collection("medicine").FindOne(sctx, bson.M{"_id": medicineOID}).Decode(&medicine)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		//配置校验

		var project models.Project
		err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": subject.ProjectID}).Decode(&project)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		envp, ok := slice.Find(project.Environments, func(index int, item models.Environment) bool {
			return item.ID == subject.EnvironmentID
		})
		if !ok {
			return nil, tools.BuildServerError(ctx, "common_configuration_error")
		}
		env := *envp

		_, ok = slice.Find(medicine.UnblindingApprovals, func(index int, item models.UrgentUnblindingApproval) bool {
			return item.Status == 0
		})
		if ok {
			return nil, tools.BuildServerError(ctx, "subject_urgentUnblindingApproval_ip_applicationed")
		}

		if project.UnblindingControl == 0 || project.IpUnblindingType == 0 {

			return IPUnblindingPassword(sctx, req, me)
			//return nil, tools.BuildServerError(ctx, "common_configuration_error")
		}

		if project.IpUnblindingSms != 1 && project.IpUnblindingProcess != 1 {
			return IPUnblindingPassword(sctx, req, me)

			//return nil, tools.BuildServerError(ctx, "common_configuration_error")
		}

		var projectSite models.ProjectSite
		err = tools.Database.Collection("project_site").FindOne(sctx, bson.M{"_id": subject.ProjectSiteID}).Decode(&projectSite)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		applicationTime := time.Duration(now.Unix())
		{
			// 审批确认
			//生成审批编号
			approvalNumber, err = GetApprovalNumber(ctx, subject.ProjectID)
			if err != nil {
				return nil, err
			}
			//创建一个揭盲审批为待审批状态
			approval := models.UrgentUnblindingApproval{
				Number:             approvalNumber,
				Status:             0,
				ApprovalType:       1,
				ApplicationTime:    applicationTime,
				ApplicationBy:      me.ID,
				ApplicationByEmail: me.Email,
				ApprovalTime:       0,
				ApprovalBy:         primitive.NilObjectID,
				ApprovalByEmail:    "",
				ReasonStr:          req.ReasonStr,
				Remark:             req.Remark,
				RejectReason:       "",
				Lang:               ctx.GetHeader("Accept-Language"),
			}
			//确认方式为流程或短信
			//短信

			// 审批权限关联用户
			permissions := []string{"operation.subject.unblinding-ip-approval"}
			var siteOrStoreIDs = []primitive.ObjectID{subject.ProjectSiteID}
			userIds, err := tools.GetPermissionUserIds(sctx, permissions, subject.ProjectID, subject.EnvironmentID, siteOrStoreIDs...)

			if project.ProjectInfo.IpUnblindingSms == 1 {
				type cloudOIdStruct struct {
					CloudId primitive.ObjectID `bson:"cloud_id"`
					ID      primitive.ObjectID `bson:"id"`
				}
				cloudOIds := []cloudOIdStruct{}
				//***********************结束****************************
				if len(userIds) > 0 {
					var users []models.User
					cursor, err := tools.Database.Collection("user").Find(nil, bson.M{"_id": bson.M{"$in": userIds}})
					if err != nil {
						return nil, errors.WithStack(err)
					}
					err = cursor.All(nil, &users)
					if err != nil {
						return nil, err
					}
					for _, puau := range users {
						cloudOIds = append(cloudOIds, cloudOIdStruct{ID: puau.ID, CloudId: puau.CloudId})
					}
				}

				// ***********************结束****************************

				cloudIds := slice.Map(cloudOIds, func(index int, item cloudOIdStruct) string {
					return item.CloudId.Hex()
				})

				users, err := tools.UserFetch(&models.UserFetchRequest{Ids: cloudIds}, locales.Lang(ctx))
				if err != nil {
					return nil, errors.WithStack(err)
				}
				usersIdMap := slice.GroupWith(users, func(t *models.UserData) string {
					return t.Id
				})
				cloudOIds = slice.Filter(cloudOIds, func(index int, item cloudOIdStruct) bool {
					u := usersIdMap[item.CloudId.Hex()]
					return u != nil
				})
				smsUsers := slice.Map(cloudOIds, func(index int, item cloudOIdStruct) models.SmsUser {
					cloudUser := usersIdMap[item.CloudId.Hex()][0]
					cloudOID, _ := primitive.ObjectIDFromHex(cloudUser.Id)
					smsUser := models.SmsUser{
						Phone:   cloudUser.Info.Mobile,
						UserId:  item.ID,
						CloudId: cloudOID,
						Name:    cloudUser.Info.Name,
						Email:   cloudUser.Info.Email,
					}
					return smsUser
				})
				phones := slice.Map(users, func(index int, item *models.UserData) string {
					i := item
					return i.Info.Mobile
				})
				phones = slice.Compact(phones)
				// 查询操作角色
				var projectRolePermission models.ProjectRolePermission
				err = tools.Database.Collection("project_role_permission").FindOne(nil, bson.M{"_id": roleOID}).Decode(&projectRolePermission)
				if err != nil {
					return nil, errors.WithStack(err)
				}

				if err != nil {
					return nil, errors.WithStack(err)
				}

				var dispensing models.Dispensing
				err = tools.Database.Collection("dispensing").FindOne(nil, bson.M{"_id": dispensingOID}).Decode(&dispensing)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				var visitCycle models.VisitCycle
				err = tools.Database.Collection("visit_cycle").FindOne(nil, bson.M{"env_id": dispensing.EnvironmentID, "cohort_id": dispensing.CohortID}).Decode(&visitCycle)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				//(揭盲审批-研究产品)项目：{$projectName}，中心：{$siteName}，申请人：${applicant}, 受试者号：{$subjectNumber}，随机号：{$randomNumber}，揭盲原因：{$reason}，审批编号：{$approvalNumber}。通过请回复{$agreeCode}，拒绝请回复：{$rejectCode}。
				//判断揭盲类型
				visitName := tools.GetDispensingTypeName("zh", dispensing, visitCycle)
				templateParam := struct {
					ProjectName    string `json:"projectName"`
					SiteName       string `json:"siteName"`
					Applicant      string `json:"applicant"`
					SubjectNumber  string `json:"subjectNumber"`
					Reason         string `json:"reason"`
					ApprovalNumber string `json:"approvalNumber"`
					AgreeCode      string `json:"agreeCode"`
					RejectCode     string `json:"rejectCode"`
					MedicineCOde   string `json:"medicineCode"`
					Visit          string `json:"visit"`
				}{
					ProjectName:    project.Number + "(" + env.Name + ")",
					SiteName:       models.GetProjectSiteName(ctx, projectSite),
					Applicant:      me.Name + "(" + projectRolePermission.Name + ")",
					SubjectNumber:  subject.Info[0].Value.(string),
					Reason:         req.ReasonStr,
					ApprovalNumber: approvalNumber,
					AgreeCode:      approvalNumber[8:] + "1",
					RejectCode:     approvalNumber[8:] + "2",
					MedicineCOde:   medicine.Number,
					Visit:          visitName,
				}
				paramJsonStr, err := convertor.ToJson(templateParam)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				err = tools.SendMedicineUnblindingApproval(paramJsonStr, phones)
				if err != nil {
					return nil, err
				}
				approval.Msg = paramJsonStr
				approval.SmsUsers = smsUsers
			}
			medicine.UnblindingApprovals = append(medicine.UnblindingApprovals, approval)
			subjectUpdate := bson.M{
				"$set": bson.M{"unblinding_approvals": medicine.UnblindingApprovals},
			}
			_, err = tools.Database.Collection("medicine").UpdateOne(sctx, bson.M{"_id": medicineOID}, subjectUpdate)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			// 添加轨迹
			customTempOption := []models.CustomTempOption{}
			customTemps := []models.CustomTemp{}

			customTempOption = append(customTempOption, models.CustomTempOption{
				Index: 1,
				Key:   "history.dispensing.single.unBlindMedicine",
				Data:  bson.M{"number": medicine.Number},
			})
			customTempOption = append(customTempOption, models.CustomTempOption{
				Index: 2,
				Key:   "history.dispensing.single.unBlindApprovalNumber",
				Data:  bson.M{"approvalNumber": approvalNumber},
			})
			customTempOption = append(customTempOption, models.CustomTempOption{
				Index:     3,
				Key:       "history.dispensing.single.unBlindStatus",
				TransData: "history.dispensing.single.unBlindApplication",
				TransType: models.KeyData,
			})
			customTemps = append(customTemps, models.CustomTemp{
				ParKey:              "data",
				ConnectingSymbolKey: "history.dispensing.single.comma",
				LastSymbolKey:       "history.dispensing.single.period",
				CustomTempOptions:   customTempOption,
			})
			history := models.History{
				OID:         dispensingOID,
				Time:        time.Duration(now.Unix()),
				UID:         me.ID,
				User:        me.Name,
				CustomTemps: customTemps,
				Key:         "history.dispensing.dispensingCustomer-unblinding-application",
				Data:        map[string]interface{}{"approvalNumber": approvalNumber, "number": medicine.Number},
			}
			if _, err := tools.Database.Collection("history").InsertOne(sctx, history); err != nil {
				return nil, errors.WithStack(err)
			}
			// 创建app任务

			if userIds != nil {
				// TODO 创建app任务
				//workTask := models.WorkTask{
				//	ID:            primitive.NewObjectID(),
				//	CustomerID:    subject.CustomerID,
				//	ProjectID:     subject.ProjectID,
				//	EnvironmentID: subject.EnvironmentID,
				//	CohortID:      subject.CohortID,
				//	UserIDs:       userIds,
				//	Info: models.WorkTaskInfo{
				//		WorkType:    16,
				//		Status:      0,
				//		CreatedTime: time.Duration(time.Now().Unix()),
				//		Deadline:    time.Duration(time.Now().AddDate(0, 0, 0).Unix()),
				//		MedicineIDs: []primitive.ObjectID{},
				//		//SubjectApproval: models.SubjectApproval{
				//		//	SubjectID: subject.ID,
				//		//	Number:    approvalNumber,
				//		//},
				//	},
				//}
				//workTask.Info.MedicineApproval = models.SubjectMedicineApproval{
				//	SubjectID: subject.ID,
				//	Number:    approvalNumber,
				//}
				//_, err = tools.Database.Collection("work_task").InsertOne(sctx, workTask)
				//if err != nil {
				//	return nil, errors.WithStack(err)
				//}
			}

			if project.ProjectInfo.IpUnblindingProcess == 1 {
				orderAddTaskId := primitive.NewObjectID()
				approvalTitle := "project.task.ip-unblinding.title"
				approvalType := 4
				orderAddTask := models.OrderAddTask{
					ID:            orderAddTaskId,
					CustomerID:    subject.CustomerID,
					ProjectID:     subject.ProjectID,
					EnvironmentID: subject.EnvironmentID,
					CohortID:      subject.CohortID,
					ProjectSiteID: subject.ProjectSiteID,
					//App:           orderInfo.App,
					ApprovalProcess: models.ApprovalProcess{
						Number:                  approvalNumber,
						Name:                    approvalTitle,
						Type:                    approvalType,
						Status:                  0,
						EstimatedCompletionTime: time.Duration(time.Now().AddDate(0, 0, 3).Unix()),
						ApplicationTime:         applicationTime,
						ApplicationBy:           me.ID,
						ApplicationByEmail:      me.Email,
						ApplicationRoleID:       roleOID,
					},
					UnblindingData: models.UnblindingDataInfo{
						SubjectID:           subject.ID,
						SubjectNumber:       subject.Info[0].Value.(string),
						SubjectRandomNumber: subject.RandomNumber,
						Number:              approval.Number,
						Status:              0, //审批状态 0提交申请 1已通过 2已拒绝
						ApprovalType:        1,
						ApplicationTime:     applicationTime,
						ApplicationBy:       me.ID,
						ApplicationByEmail:  me.Email,
						ApprovalTime:        0,
						ApprovalBy:          primitive.NilObjectID,
						ApprovalByEmail:     "",
						ReasonStr:           req.ReasonStr,
						Remark:              req.Remark,
						RejectReason:        "",
						MedicineNumber:      medicine.Number,
						MedicineID:          medicine.ID,
						DispensingID:        dispensingOID,
					},
				}
				if _, err := tools.Database.Collection("approval_process").InsertOne(sctx, orderAddTask); err != nil {
					return nil, errors.WithStack(err)
				}
			}

			//更新任务号
			approvalNumberSave := models.ApprovalNumber{
				ID:         primitive.NewObjectID(),
				CustomerID: subject.CustomerID,
				ProjectID:  subject.ProjectID,
				Number:     approvalNumber,
			}
			if _, err := tools.Database.Collection("approval_number").InsertOne(sctx, approvalNumberSave); err != nil {
				return nil, errors.WithStack(err)
			}
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return nil, err
	}
	type approvalResult struct {
		ApprovalNumber  string        `json:"approvalNumber"`
		ApplicationTime time.Duration `json:"applicationTime"`
	}
	result := approvalResult{
		ApprovalNumber:  approvalNumber,
		ApplicationTime: time.Duration(now.Unix()),
	}
	return result, nil
}

func (s *DispensingService) UnblindingApproval(ctx *gin.Context) (interface{}, error) {
	//参数转化
	req := struct {
		RoleId         string `json:"roleId"`
		SubjectId      string `json:"subjectId"`
		Agree          int    `json:"agree"`
		RejectReason   string `json:"rejectReason"`
		ApprovalNumber string `json:"approvalNumber"`
		MedicineID     string `json:"medicineId"`
		DispensingID   string `json:"dispensingId"`
	}{}
	err := ctx.ShouldBindBodyWith(&req, binding.JSON)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	roleOID, err := primitive.ObjectIDFromHex(req.RoleId)

	if err != nil {
		return nil, errors.WithStack(err)
	}
	subjectOID, err := primitive.ObjectIDFromHex(req.SubjectId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	dispensingOID, err := primitive.ObjectIDFromHex(req.DispensingID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	medicineOID, err := primitive.ObjectIDFromHex(req.MedicineID)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var now time.Time
	now = time.Now()
	callback := func(sctx mongo.SessionContext) (interface{}, error) {

		var subject models.Subject
		err = tools.Database.Collection("subject").FindOne(sctx, bson.M{"_id": subjectOID}).Decode(&subject)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		var dispensing models.Dispensing
		err = tools.Database.Collection("dispensing").FindOne(sctx, bson.M{"_id": dispensingOID}).Decode(&dispensing)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		_, err = UnbindingApproval(ctx, sctx, subject, medicineOID, dispensingOID, req.ApprovalNumber, req.Agree, req.RejectReason, roleOID, now)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return nil, nil
	}
	err = tools.Transaction(callback)
	if err != nil {
		return nil, err
	}
	type approvalResult struct {
		ApprovalNumber string        `json:"approvalNumber"`
		ApprovalTime   time.Duration `json:"approvalTime"`
	}
	result := approvalResult{
		ApprovalTime: time.Duration(now.Unix()),
	}
	return result, nil
}

func IPUnblindingPassword(sctx mongo.SessionContext, req models.UnblindingMedicineReq, me models.User) (interface{}, error) {
	now := time.Now()
	_, err := tools.Database.Collection("medicine").UpdateOne(sctx, bson.M{"_id": req.MedicineID},
		bson.M{"$set": bson.M{"unblinding": models.MedicineUnblind{
			Remark:        req.Remark,
			ReasonStr:     req.ReasonStr,
			OperationTime: time.Duration(now.Unix()),
			OperationBy:   me.ID,
			OperationMail: me.Email,
		}}})
	if err != nil {
		return nil, err
	}

	customTempOption := []models.CustomTempOption{}
	customTemps := []models.CustomTemp{}
	customTempOption = append(customTempOption, models.CustomTempOption{
		Index:     1,
		Key:       "history.dispensing.single.unBlindStatus",
		TransData: "history.dispensing.single.unBlindSuccess",
		TransType: models.KeyData,
	})
	customTemps = append(customTemps, models.CustomTemp{
		ParKey:              "data",
		ConnectingSymbolKey: "history.dispensing.single.comma",
		LastSymbolKey:       "history.dispensing.single.period",
		CustomTempOptions:   customTempOption,
	})
	history := models.History{
		OID:         req.DispensingID,
		Time:        time.Duration(now.Unix()),
		UID:         me.ID,
		User:        me.Name,
		CustomTemps: customTemps,
		Key:         "history.dispensing.dispensingCustomer-unblinding-application",
	}
	if _, err := tools.Database.Collection("history").InsertOne(sctx, history); err != nil {
		return nil, errors.WithStack(err)
	}

	return nil, nil
}

func (s *DispensingService) IPUnblindingApproval(ctx *gin.Context, id string, approvalNumber string, agree int, rejectReason string) (interface{}, error) {

}
