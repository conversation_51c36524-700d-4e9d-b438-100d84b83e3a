package service

import (
	"clinflash-irt/config"
	"clinflash-irt/convert"
	"clinflash-irt/data"
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/tools"
	"clinflash-irt/ws"
	"context"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin/binding"
	"github.com/wxnacy/wgo/arrays"
	"log"
	"net/url"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/algorithm"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type CustomerService struct {
}

// ListCustomerUsers 客户与用户的关系改成从cloud查询
func (s *CustomerService) ListCustomerUsers(ctx *gin.Context, customerID string, keyword string, start int, limit int) (map[string]interface{}, error) {
	var err error
	//调用cloud 获取用户列表
	resp, err := tools.UserFetchWithCustomer(&models.UserFetchWithCustomerRequest{
		CustomerId: customerID,
		Keyword:    keyword,
		Start:      int64(start),
		Limit:      int64(limit),
	}, locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	cloudUsers := resp.Users
	//先获取userIds查本地user表 主要为了给出roles字段   role字段过滤无效部分
	cloudIds := make([]string, len(cloudUsers))
	for i, user := range cloudUsers {
		cloudIds[i] = user.Id
	}
	users := make([]models.User, 0)
	cloudOIds := slice.Map(cloudIds, func(index int, item string) primitive.ObjectID {
		hex, _ := primitive.ObjectIDFromHex(item)
		return hex
	})
	cloudOIds = slice.Filter(cloudOIds, func(index int, item primitive.ObjectID) bool {
		return !item.IsZero()
	})

	cursor, err := tools.Database.Collection("user").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"cloud_id": bson.M{"$in": cloudOIds}}}},
		{{"$lookup", bson.M{
			"from": "role_permission",
			"let": bson.M{
				"roles": "$roles",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"template": 1, "status": 1}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$in": bson.A{"$_id", "$$roles"}}}},
			},
			"as": "role_permission",
		}}},
		{{"$project", bson.M{
			"_id":            1,
			"roles":          "$role_permission._id",
			"info.email":     1,
			"info.status":    1,
			"deleted":        1,
			"cloud_id":       1,
			"close_customer": 1,
		}}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var data []map[string]interface{}
	for _, cloudUser := range cloudUsers {
		//循环user下的客户id 判断是否为admin
		admin := false
	Loop:
		for _, customer := range cloudUser.Customers {
			if customer.Id == customerID {
				for _, a := range customer.Admin {
					if a == config.CLOUD_KEY {
						admin = true
						break Loop
					}
				}
			}
		}
		var user models.User
		for _, u := range users {
			//if u.Email == cloudUser.Info.Email {
			if u.CloudId.Hex() == cloudUser.Id {
				user = u
				break
			}
		}
		closeCustomer := make([]primitive.ObjectID, 0)
		if user.CloudId.Hex() == cloudUser.Id &&
			user.UserInfo.Status != int8(2) &&
			cloudUser.Status == int32(2) {
			//设置为已关闭状态
			if cloudUser.Customers != nil {
				customers := cloudUser.Customers
				for _, customer := range customers {
					if customer.Apps != nil {
						if stringInSlice(config.CLOUD_KEY, customer.Apps) {
							closed(ctx, user.ID.Hex(), customer.Id, 2)
						}
					}
				}
			}
		}
		if user.CloudId.Hex() == cloudUser.Id &&
			user.Deleted == false &&
			cloudUser.Deleted == true {
			//设置为已关闭状态
			if cloudUser.Customers != nil {
				customers := cloudUser.Customers
				for _, customer := range customers {
					if customer.Apps != nil {
						if stringInSlice(config.CLOUD_KEY, customer.Apps) {
							closed(ctx, user.ID.Hex(), customer.Id, 1)
						}
					}
				}
			}
		}

		//启用操作-由禁用变成启用
		if user.CloudId.Hex() == cloudUser.Id &&
			user.UserInfo.Status == int8(2) &&
			cloudUser.Status != int32(2) {
			if cloudUser.Customers != nil {
				customers := cloudUser.Customers
				for _, customer := range customers {
					if customer.Apps != nil {
						if stringInSlice(config.CLOUD_KEY, customer.Apps) {
							id, _ := primitive.ObjectIDFromHex(customer.Id)
							_, err = tools.Database.Collection("user").UpdateOne(nil,
								bson.M{"_id": user.ID},
								bson.M{"$pull": bson.M{"close_customer": id}, "$set": bson.M{"info.status": int8(cloudUser.Status)}})
							if err != nil {
								return nil, errors.WithStack(err)
							}
						}
					}
				}
			}
		}

		if user.CloseCustomer != nil && len(user.CloseCustomer) > 0 {
			closeCustomer = user.CloseCustomer
		}

		d := map[string]interface{}{
			"id":    user.ID,
			"admin": admin,
			"info": map[string]interface{}{
				"name":        cloudUser.Info.Name,
				"email":       cloudUser.Info.Email,
				"phone":       cloudUser.Info.Mobile,
				"company":     cloudUser.Info.Company,
				"description": cloudUser.Info.Description,
			},
			"closeCustomer": closeCustomer,
			"roles":         user.Roles,
			"status":        cloudUser.Status,
			"delete":        cloudUser.Deleted,
		}
		data = append(data, d)
	}
	return map[string]interface{}{"total": resp.Count, "items": data}, nil
}

// ListCustomerUsersExport 客户与用户的关系改成从cloud导出
func (s *CustomerService) ListCustomerUsersExport(ctx *gin.Context, customerID string, keyword string, now time.Time) (string, []byte, error) {
	var err error
	//调用cloud 获取用户列表
	resp, err := tools.UserFetchWithCustomer(&models.UserFetchWithCustomerRequest{
		CustomerId: customerID,
		//Keyword:    keyword,
		//Start:      int64(start),
		//Limit:      int64(limit),
	}, locales.Lang(ctx))
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	cloudUsers := resp.Users
	//先获取userIds查本地user表 主要为了给出roles字段   role字段过滤无效部分
	cloudIds := make([]string, len(cloudUsers))
	for i, user := range cloudUsers {
		cloudIds[i] = user.Id
	}
	users := make([]models.User, 0)
	cloudOIds := slice.Map(cloudIds, func(index int, item string) primitive.ObjectID {
		hex, _ := primitive.ObjectIDFromHex(item)
		return hex
	})
	cloudOIds = slice.Filter(cloudOIds, func(index int, item primitive.ObjectID) bool {
		return !item.IsZero()
	})

	cursor, err := tools.Database.Collection("user").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{"cloud_id": bson.M{"$in": cloudOIds}}}},
		{{"$lookup", bson.M{
			"from": "role_permission",
			"let": bson.M{
				"roles": "$roles",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"status": 1}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$in": bson.A{"$_id", "$$roles"}}}},
			},
			"as": "role_permission",
		}}},
		{{"$project", bson.M{
			"_id":            1,
			"roles":          "$role_permission._id",
			"info.email":     1,
			"close_customer": 1,
		}}},
	})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var bytes []byte

	//写入excel文件
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	streamWriter, _ := f.NewStreamWriter("Sheet1")
	_ = streamWriter.SetColWidth(1, 14, 20)

	//设置第一行title
	title := []interface{}{
		locales.Tr(ctx, "setting.user.list.No"),
		locales.Tr(ctx, "setting.user.list.email"),
		locales.Tr(ctx, "setting.user.list.fullName"),
		locales.Tr(ctx, "setting.user.list.telephone"),
		locales.Tr(ctx, "setting.user.list.company"),
		locales.Tr(ctx, "setting.user.list.description"),
		locales.Tr(ctx, "setting.user.list.role"),
	}
	t := make([]any, len(title))
	for i, item := range title {
		t[i] = excelize.Cell{Value: item}
	}
	_ = streamWriter.SetRow("A1", t)

	title2 := []any{
		locales.Tr(ctx, "setting.user.list.role.common"),
		locales.Tr(ctx, "setting.user.list.role.dtp"),
	}
	t2 := make([]any, len(title)+len(title2))
	for i, item := range title2 {
		t2[i+len(title)-1] = excelize.Cell{Value: item}
	}

	//处理订单详情合并
	title1Hcell, _ := excelize.CoordinatesToCellName(len(t), 1)
	title1Vcell, _ := excelize.CoordinatesToCellName(len(t2)-1, 1)
	_ = streamWriter.MergeCell(title1Hcell, title1Vcell)

	_ = streamWriter.SetRow("A2", t2)
	//处理订单字段一二行合并
	for i := 0; i < len(title)-1; i++ {
		hcell, _ := excelize.CoordinatesToCellName(i+1, 1)
		vcell, _ := excelize.CoordinatesToCellName(i+1, 2)
		_ = streamWriter.MergeCell(hcell, vcell)
	}

	var axisIndex = 2
	var i = 0
	for _, cloudUser := range cloudUsers {
		i++

		var user models.User
		for _, u := range users {
			if u.Email == cloudUser.Info.Email {
				user = u
				break
			}
		}
		r := []interface{}{i, cloudUser.Info.Email, cloudUser.Info.Name, cloudUser.Info.Mobile,
			cloudUser.Info.Company, cloudUser.Info.Description}
		rowStart, _ := excelize.CoordinatesToCellName(1, axisIndex+1)
		axisIndex++
		roles := user.Roles
		var common []string
		var dtp []string
		if len(roles) > 0 {
			var rolePermissions []models.RolePermission
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.D{{Key: "_id", Value: bson.M{"$in": roles}}}}},
			}
			cus, er := tools.Database.Collection("role_permission").Aggregate(nil, pipeline)
			if er != nil {
				return "", nil, errors.WithStack(er)
			}
			err = cus.All(nil, &rolePermissions)
			if err != nil {
				return "", nil, errors.WithStack(er)
			}

			for j := 0; j < len(rolePermissions); j++ {
				if rolePermissions[j].Template == 1 {
					common = append(common, rolePermissions[j].Name)
				} else if rolePermissions[j].Template == 2 {
					dtp = append(dtp, rolePermissions[j].Name)
				}
			}
		}
		rm := append(r, strings.Join(common, "/"))
		rm = append(rm, strings.Join(dtp, "/"))
		_ = streamWriter.SetRow(rowStart, rm)

	}
	_ = streamWriter.Flush()
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	bytes = buffer.Bytes()
	fileName := fmt.Sprintf("CustomerUserReport_%s.xlsx", now.Format("20060102150405"))

	return fileName, bytes, nil
}

// ListCustomerUsersExportWithIDs 客户与用户的关系改成从cloud导出  v2.13 增加通过id过滤
func (s *CustomerService) ListCustomerUsersExportWithIDs(ctx *gin.Context, customerID string, ids []string, now time.Time) (string, []byte, error) {
	var err error
	//调用cloud 获取用户列表
	resp, err := tools.UserFetchWithCustomer(&models.UserFetchWithCustomerRequest{
		CustomerId: customerID,
		//Keyword:    keyword,
		//Start:      int64(start),
		//Limit:      int64(limit),
	}, locales.Lang(ctx))
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	cloudUsers := resp.Users
	//先获取userIds查本地user表 主要为了给出roles字段   role字段过滤无效部分
	cloudIds := make([]string, len(cloudUsers))
	for i, user := range cloudUsers {
		cloudIds[i] = user.Id
	}
	users := make([]models.User, 0)
	cloudOIds := slice.Map(cloudIds, func(index int, item string) primitive.ObjectID {
		hex, _ := primitive.ObjectIDFromHex(item)
		return hex
	})
	cloudOIds = slice.Filter(cloudOIds, func(index int, item primitive.ObjectID) bool {
		return !item.IsZero()
	})

	oids := slice.Map(ids, func(index int, item string) primitive.ObjectID {
		hex, _ := primitive.ObjectIDFromHex(item)
		return hex
	})

	cursor, err := tools.Database.Collection("user").Aggregate(nil, mongo.Pipeline{
		{{"$match", bson.M{
			"cloud_id": bson.M{"$in": cloudOIds},
			"_id":      bson.M{"$in": oids},
		},
		}},
		{{"$lookup", bson.M{
			"from": "role_permission",
			"let": bson.M{
				"roles": "$roles",
			},
			"pipeline": bson.A{
				bson.M{"$match": bson.M{"status": 1}},
				bson.M{"$match": bson.M{"$expr": bson.M{"$in": bson.A{"$_id", "$$roles"}}}},
			},
			"as": "role_permission",
		}}},
		{{"$project", bson.M{
			"_id":            1,
			"roles":          "$role_permission._id",
			"info.email":     1,
			"close_customer": 1,
		}}},
	})
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	var bytes []byte

	//写入excel文件
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	streamWriter, _ := f.NewStreamWriter("Sheet1")
	_ = streamWriter.SetColWidth(1, 14, 20)

	//设置第一行title
	title := []interface{}{
		locales.Tr(ctx, "setting.user.list.No"),
		locales.Tr(ctx, "setting.user.list.email"),
		locales.Tr(ctx, "setting.user.list.fullName"),
		locales.Tr(ctx, "setting.user.list.telephone"),
		locales.Tr(ctx, "setting.user.list.company"),
		locales.Tr(ctx, "setting.user.list.description"),
		locales.Tr(ctx, "setting.user.list.role"),
	}
	t := make([]any, len(title))
	for i, item := range title {
		t[i] = excelize.Cell{Value: item}
	}
	_ = streamWriter.SetRow("A1", t)

	title2 := []any{
		locales.Tr(ctx, "setting.user.list.role.common"),
		locales.Tr(ctx, "setting.user.list.role.dtp"),
	}
	t2 := make([]any, len(title)+len(title2))
	for i, item := range title2 {
		t2[i+len(title)-1] = excelize.Cell{Value: item}
	}

	//处理订单详情合并
	title1Hcell, _ := excelize.CoordinatesToCellName(len(t), 1)
	title1Vcell, _ := excelize.CoordinatesToCellName(len(t2)-1, 1)
	_ = streamWriter.MergeCell(title1Hcell, title1Vcell)

	_ = streamWriter.SetRow("A2", t2)
	//处理订单字段一二行合并
	for i := 0; i < len(title)-1; i++ {
		hcell, _ := excelize.CoordinatesToCellName(i+1, 1)
		vcell, _ := excelize.CoordinatesToCellName(i+1, 2)
		_ = streamWriter.MergeCell(hcell, vcell)
	}

	var axisIndex = 2
	var i = 0
	for _, user := range users {
		i++

		var cloudUser models.UserData
		for _, cu := range cloudUsers {
			if cu.Info.Email == user.Email {
				cloudUser = *cu
				break
			}
		}
		r := []interface{}{i, cloudUser.Info.Email, cloudUser.Info.Name, cloudUser.Info.Mobile,
			cloudUser.Info.Company, cloudUser.Info.Description}
		rowStart, _ := excelize.CoordinatesToCellName(1, axisIndex+1)
		axisIndex++
		roles := user.Roles
		var common []string
		var dtp []string
		if len(roles) > 0 {
			var rolePermissions []models.RolePermission
			pipeline := mongo.Pipeline{
				{{Key: "$match", Value: bson.D{{Key: "_id", Value: bson.M{"$in": roles}}}}},
			}
			cus, er := tools.Database.Collection("role_permission").Aggregate(nil, pipeline)
			if er != nil {
				return "", nil, errors.WithStack(er)
			}
			err = cus.All(nil, &rolePermissions)
			if err != nil {
				return "", nil, errors.WithStack(er)
			}

			for j := 0; j < len(rolePermissions); j++ {
				if rolePermissions[j].Template == 1 {
					common = append(common, rolePermissions[j].Name)
				} else if rolePermissions[j].Template == 2 {
					dtp = append(dtp, rolePermissions[j].Name)
				}
			}
		}
		rm := append(r, strings.Join(common, "/"))
		rm = append(rm, strings.Join(dtp, "/"))
		_ = streamWriter.SetRow(rowStart, rm)

	}
	_ = streamWriter.Flush()
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	bytes = buffer.Bytes()
	fileName := fmt.Sprintf("CustomerUserReport_%s.xlsx", now.Format("20060102150405"))

	return fileName, bytes, nil
}

// ListSysAdminUsers 查询系统管理员
func (s *CustomerService) ListSysAdminUsers(ctx *gin.Context, keyword string, start int, limit int) (map[string]interface{}, error) {
	var err error
	var skip = int64(start)
	var page = int64(limit)
	var users []models.User
	filter := bson.M{"group": 1}
	if keyword != "" {
		drugs := bson.A{}
		drugs = append(drugs, bson.M{"info.email": bson.M{"$regex": keyword}})
		drugs = append(drugs, bson.M{"info.name": bson.M{"$regex": keyword}})
		drugs = append(drugs, bson.M{"info.phone": bson.M{"$regex": keyword}})
		drugs = append(drugs, bson.M{"info.company": bson.M{"$regex": keyword}})
		drugs = append(drugs, bson.M{"info.description": bson.M{"$regex": keyword}})
		//filter = bson.M{"group": 1, "info.name": bson.M{"$regex": keyword}}
		filter = bson.M{"group": 1, "$or": drugs}
	}
	opts := &options.FindOptions{
		Sort:  bson.D{{"time", -1}},
		Skip:  &skip,
		Limit: &page,
	}
	cursor, err := tools.Database.Collection("user").Find(nil, filter, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var data []map[string]interface{}
	for _, user := range users {
		closeCustomer := make([]primitive.ObjectID, 0)
		if user.CloseCustomer != nil && len(user.CloseCustomer) > 0 {
			closeCustomer = user.CloseCustomer
		}
		d := map[string]interface{}{
			"id":    user.ID,
			"admin": user.Admin,
			"info": map[string]interface{}{
				"name":        user.UserInfo.Name,
				"email":       user.UserInfo.Email,
				"phone":       user.UserInfo.Phone,
				"company":     user.UserInfo.Company,
				"description": user.UserInfo.Description,
			},
			"closeCustomer": closeCustomer,
			"roles":         user.Roles,
			"status":        user.UserInfo.Status,
			"delete":        user.Deleted,
		}
		data = append(data, d)
	}
	return map[string]interface{}{"total": len(users), "items": data}, nil
}

// ListSysAdminUsersExport 导出系统管理员
func (s *CustomerService) ListSysAdminUsersExport(ctx *gin.Context, now time.Time) (string, []byte, error) {
	var err error
	//var skip int64 = int64(start)
	//var page int64 = int64(limit)
	var users []models.User
	filter := bson.M{"group": 1}
	opts := &options.FindOptions{
		Sort: bson.D{{"time", -1}},
		//Skip:  &skip,
		//Limit: &page,
	}
	cursor, err := tools.Database.Collection("user").Find(nil, filter, opts)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	//设置第一行title
	title := []interface{}{
		locales.Tr(ctx, "setting.user.list.No"),
		locales.Tr(ctx, "setting.user.list.email"),
		locales.Tr(ctx, "setting.user.list.fullName"),
		locales.Tr(ctx, "setting.user.list.telephone"),
		locales.Tr(ctx, "setting.user.list.company"),
		locales.Tr(ctx, "setting.user.list.description"),
		locales.Tr(ctx, "setting.user.list.role"),
	}

	var i = 0
	content := make([][]interface{}, 0)
	for _, user := range users {
		i++
		contentItem := []interface{}{
			i,
			user.UserInfo.Email,
			user.UserInfo.Name,
			user.UserInfo.Phone,
			user.UserInfo.Company,
			user.UserInfo.Description,
			"Sys-Admin",
		}
		content = append(content, contentItem)
	}
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	tools.ExportSheet(f, "Sheet1", title, content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	fileName := fmt.Sprintf("SysAdminUserReport_%s.xlsx", now.Format("20060102150405"))

	return fileName, buffer.Bytes(), nil
}

// ListSysAdminUsersExportWithIDs 导出系统管理员  v2.13 增加通过id过滤
func (s *CustomerService) ListSysAdminUsersExportWithIDs(ctx *gin.Context, now time.Time, ids []string) (string, []byte, error) {
	var err error

	oids := slice.Map(ids, func(index int, item string) primitive.ObjectID {
		hex, _ := primitive.ObjectIDFromHex(item)
		return hex
	})

	var users []models.User
	filter := bson.M{
		"group": 1,
		"_id":   bson.M{"$in": oids},
	}
	opts := &options.FindOptions{
		Sort: bson.D{{"time", -1}},
		//Skip:  &skip,
		//Limit: &page,
	}
	cursor, err := tools.Database.Collection("user").Find(nil, filter, opts)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	//设置第一行title
	title := []interface{}{
		locales.Tr(ctx, "setting.user.list.No"),
		locales.Tr(ctx, "setting.user.list.email"),
		locales.Tr(ctx, "setting.user.list.fullName"),
		locales.Tr(ctx, "setting.user.list.telephone"),
		locales.Tr(ctx, "setting.user.list.company"),
		locales.Tr(ctx, "setting.user.list.description"),
		locales.Tr(ctx, "setting.user.list.role"),
	}

	var i = 0
	content := make([][]interface{}, 0)
	for _, user := range users {
		i++
		contentItem := []interface{}{
			i,
			user.UserInfo.Email,
			user.UserInfo.Name,
			user.UserInfo.Phone,
			user.UserInfo.Company,
			user.UserInfo.Description,
			"Sys-Admin",
		}
		content = append(content, contentItem)
	}
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	tools.ExportSheet(f, "Sheet1", title, content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return "", nil, errors.WithStack(err)
	}

	fileName := fmt.Sprintf("SysAdminUserReport_%s.xlsx", now.Format("20060102150405"))

	return fileName, buffer.Bytes(), nil
}

// ListRolesPermissionExport 导出设置角色权限
func (s *CustomerService) ListRolesPermissionExport(ctx *gin.Context, template int, keyword string, now time.Time) (string, []byte, error) {
	var permissions []models.RolePermission
	pipeline := mongo.Pipeline{
		//{{Key: "$match", Value: bson.M{"name": bson.M{"$regex": keyword}}}},
		{{Key: "$match", Value: bson.D{{Key: "template", Value: template}}}},
	}
	//if keyword != 0 {
	//	pipeline = append(pipeline, bson.D{{"$match", bson.M{"name": bson.M{"$regex": keyword}}}})
	//}
	cursor, err := tools.Database.Collection("role_permission").Aggregate(nil, pipeline)
	if err != nil {
		return "", nil, err
	}
	if err = cursor.All(ctx, &permissions); err != nil {
		return "", nil, err
	}

	//var s []models.RolePermission
	for i := 0; i < len(permissions); i++ {
		if permissions[i].Name == "Customer-Admin" || permissions[i].Name == "Sys-Admin" {
			var res []string
			if permissions[i].Permissions != nil {
				for _, perm := range permissions[i].Permissions {
					if perm != "operation.projects.main.setting.permission.export" {
						res = append(res, perm)
					}
				}
			}
			permissions[i].Permissions = res
		}
	}
	for _, permission := range permissions {
		var result models.RolePermission
		if permission.Name == "Customer-Admin" || permission.Name == "Sys-Admin" {
			//res := make([]string, 0)
			var res []string
			if permission.Permissions != nil {
				for _, perm := range permission.Permissions {
					if perm != "operation.projects.main.setting.permission.export" {
						res = append(res, perm)
					}
				}
			}
			result.Permissions = res
		}

	}

	positionMap, reverseIndex := reverseIndexPermissions(template)
	// change reverseIndex to map[string]string
	permission2Menu := make(map[string]string)
	for permission, menu := range reverseIndex {
		permission2Menu[permission] = strings.Join(menu, " > ")
	}
	role := make(map[string]map[string][]string) // role -> menu -> permissions
	for _, permission := range permissions {
		menu := make(map[string][]string)
		for _, p := range permission.Permissions {
			if _, ok := permission2Menu[p]; ok {
				if _, ok := menu[permission2Menu[p]]; !ok {
					menu[permission2Menu[p]] = make([]string, 0)
				}
				menu[permission2Menu[p]] = append(menu[permission2Menu[p]], p)
			}
		}
		temp := ""
		if permission.Template == 1 {
			temp = locales.Tr(ctx, "setting.user.list.role.common")
			//temp = "通用"
		} else {
			temp = locales.Tr(ctx, "setting.user.list.role.dtp")
			//temp = "DTP"
		}
		key := fmt.Sprintf("%s-%s", permission.Name, temp)
		role[key] = menu
	}

	r := make([]tools.Sheet[string], len(role))

	roleIndex := 0
	for roleName, menus := range role {
		// change menus from map[string][]string to [][]string
		tmpM := make([][]string, len(positionMap))
		for _, permissions := range menus {
			if menuCascade, ok := reverseIndex[permissions[0]]; ok {
				lowestMenu := menuCascade[len(menuCascade)-1]
				index := positionMap[lowestMenu]
				tmpM[index] = permissions
			}
		}
		// compact the sparse array m to a dense array menu
		m := make([][]string, len(menus))
		mIndex := 0
		for _, item := range tmpM {
			if len(item) != 0 {
				m[mIndex] = item
				mIndex++
			}
		}
		rows := make([][]string, 0)

		// 表头
		header := make([]string, 0)
		if locales.Lang(ctx) == "zh" {
			header = append(header, "角色")
			header = append(header, "菜单")
			header = append(header, "操作")
		} else {
			header = append(header, "Role")
			header = append(header, "Menu")
			header = append(header, "Operation")
		}
		rows = append(rows, header)

		// 第一行
		firstRow := make([]string, 0)
		// 角色名
		firstRow = append(firstRow, roleName)
		// 层叠菜单
		menuCascade := make([]string, 0)
		if len(m) == 0 || m[0] == nil || len(m[0]) == 0 || m[0][0] == "" {
			rows = append(rows, firstRow)
			r[roleIndex] = tools.Sheet[string]{Name: roleName, Rows: rows}
			roleIndex++
			continue
		}
		for _, code := range reverseIndex[m[0][0]] {
			menuCascade = append(menuCascade, locales.Tr(ctx, code))
		}
		firstRow = append(firstRow, strings.Join(menuCascade, "-"))
		// 权限
		p := make([]string, 0)
		for _, permission := range m[0] {
			p = append(p, locales.Tr(ctx, permission))
		}
		firstRow = append(firstRow, strings.Join(p, "、"))

		rows = append(rows, firstRow)

		// 其他行
		for _, permissions := range m[1:] {
			row := make([]string, 0)
			// 空串占位
			row = append(row, "")
			// 层叠菜单
			menuCascade := make([]string, 0)
			for _, code := range reverseIndex[permissions[0]] {
				menuCascade = append(menuCascade, locales.Tr(ctx, code))
			}
			row = append(row, strings.Join(menuCascade, "-"))
			// 权限
			p := make([]string, 0)
			for _, permission := range permissions {
				p = append(p, locales.Tr(ctx, permission))
			}
			row = append(row, strings.Join(p, "、"))
			rows = append(rows, row)
		}
		r[roleIndex] = tools.Sheet[string]{Name: roleName, Rows: rows}
		roleIndex++
	}

	// 按角色名排序
	algorithm.MergeSort(r, &comparator{})
	bytes, err := tools.ExcelBytes(r)
	if err != nil {
		panic(err)
	}
	filename := fmt.Sprintf("RolePermissionConfigurationReport_%s.xlsx", now.Format("20060102150405"))

	return filename, bytes, nil
}

// UserExport 设置-用户-导出
func (s *CustomerService) UserExport(ctx *gin.Context, sysAdmin bool, customerID string, keyword string) error {
	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}

	var (
		filename string
		content  []byte
	)

	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	loc, e := time.LoadLocation("Asia/Shanghai")
	if e != nil {
		return errors.WithStack(e)
	}
	if len(users) > 0 && users[0].Settings.Tz != "" {
		loc, e = time.LoadLocation(users[0].Settings.Tz)
		if e != nil {
			return errors.WithStack(e)
		}
	}

	now := time.Now().In(loc)

	filename, content, _ = s.ListCustomerUsersExport(ctx, customerID, keyword, now)
	if sysAdmin {
		filename, content, _ = s.ListSysAdminUsersExport(ctx, now)
	}
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(filename)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", content)

	return nil
}

// UserExportWithIDs 设置-用户-导出  v2.13 改为导出勾选的用户
func (s *CustomerService) UserExportWithIDs(ctx *gin.Context, sysAdmin bool, customerID string, ids []string) error {
	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}

	var (
		filename string
		content  []byte
	)

	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	loc, e := time.LoadLocation("Asia/Shanghai")
	if e != nil {
		return errors.WithStack(e)
	}
	if len(users) > 0 && users[0].Settings.Tz != "" {
		loc, e = time.LoadLocation(users[0].Settings.Tz)
		if e != nil {
			return errors.WithStack(e)
		}
	}

	now := time.Now().In(loc)

	if sysAdmin {
		filename, content, _ = s.ListSysAdminUsersExportWithIDs(ctx, now, ids)
	} else {
		filename, content, _ = s.ListCustomerUsersExportWithIDs(ctx, customerID, ids, now)
	}
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(filename)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", content)

	return nil
}

// RoleExport 设置-角色权限-导出
func (s *CustomerService) RoleExport(ctx *gin.Context, template int, keyword string) error {
	user, err := tools.Me(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	var (
		filename string
		content  []byte
	)

	users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))

	timezone := "08:00"
	offset := float64(8)
	if len(users) > 0 && users[0].Settings.Tz != "" {
		timezone, err = tools.GetLocationTimeZone(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
		offset, err = tools.GetLocationFloat(users[0].Settings.Tz)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	if err != nil {
		return errors.WithStack(err)
	}
	if offset > 0 {
		timezone = "+" + timezone
	}
	now := time.Now().In(time.FixedZone(fmt.Sprintf("UTC%s", timezone), int(offset*60*60)))

	filename, content, _ = s.ListRolesPermissionExport(ctx, template, keyword, now)
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(filename)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", content)

	return nil
}

// UpdateUserCustomerAdmin 调用crm接口设置和移除user在customer下的admin
func (s *CustomerService) UpdateUserCustomerAdmin(ctx *gin.Context, customerID string, userID string, userCustomer models.UserCustomer) error {
	var err error
	userOId, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return errors.WithStack(err)
	}
	var user models.User
	err = tools.Database.Collection("user").FindOne(nil, bson.M{"_id": userOId}).Decode(&user)
	if err != nil {
		return errors.WithStack(err)
	}
	customerOID, err := primitive.ObjectIDFromHex(customerID)
	if err != nil {
		return errors.WithStack(err)
	}
	userNameEmail := fmt.Sprintf("%s,%s", user.Name, user.Email)

	if userCustomer.Admin {
		if err = tools.AddCustomerAdmin(&models.CustomerAdminRequest{CustomerId: customerID, Email: user.Email}, locales.Lang(ctx)); err != nil {
			return err
		}
		//日志
		err = insertCustomerUserLog(ctx, nil, customerOID, 14, 0, 0, userNameEmail, userOId, "")
		if err != nil {
			return err
		}
	} else {
		if err = tools.RemoveCustomerAdmin(&models.CustomerAdminRequest{CustomerId: customerID, Email: user.Email}, locales.Lang(ctx)); err != nil {
			return errors.WithStack(err)
		}
		//日志
		err = insertCustomerUserLog(ctx, nil, customerOID, 15, 1, 0, userNameEmail, userOId, "")
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *CustomerService) ListCustomerUserProjects(ctx *gin.Context, customerID string, start int, limit int) (map[string]interface{}, error) {
	var err error
	me, err := tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	match := bson.M{"customer_id": customerOID, "user_id": me.ID, "roles.0": bson.M{"$exists": 1}}
	var total []map[string]interface{}
	cur, err := tools.Database.Collection("user_project_environment").Aggregate(nil, mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$group", Value: bson.M{"_id": "$project_id"}}},
		{{Key: "$count", Value: "total"}},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cur.All(nil, &total)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: match}},
		{{Key: "$lookup", Value: bson.M{"from": "project", "localField": "project_id", "foreignField": "_id", "as": "project"}}},
		{{Key: "$unwind", Value: "$project"}},
		{{Key: "$unwind", Value: "$project.envs"}},
		{{Key: "$match", Value: bson.M{"$expr": bson.M{"$eq": bson.A{"$env_id", "$project.envs.id"}}}}},
		{{Key: "$group", Value: bson.M{"_id": "$project_id", "project": bson.M{"$push": "$project"}}}},
		{{Key: "$project", Value: bson.M{
			"_id":            0,
			"id":             "$_id",
			"administrators": bson.M{"$first": "$project.administrators"},
			"status":         bson.M{"$first": "$project.status"},
			"info":           bson.M{"$first": "$project.info"},
			"envs":           "$project.envs"}}},
		{{Key: "$sort", Value: bson.D{{"info.number", 1}}}},
		{{Key: "$skip", Value: start}},
		{{Key: "$limit", Value: limit}},
	}

	var data []map[string]interface{}
	cursor, err := tools.Database.Collection("user_project_environment").Aggregate(nil, pipeline)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = cursor.All(nil, &data)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var count int32 = 0
	if len(total) > 0 {
		count = total[0]["total"].(int32)
	}
	return map[string]interface{}{"total": count, "items": data}, nil
}

func (s *CustomerService) ListCustomerProjects(ctx *gin.Context, customerID string, start int, limit int) (map[string]interface{}, error) {
	var err error
	var me models.User
	me, err = tools.Me(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	match := bson.M{"customer_id": customerOID}
	out, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{me.CloudId.Hex()}}, locales.Lang(ctx))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 2、根据Customers.admin == irt 是否存在判断是否为客户管理员
	adminCustomer := false
Loop:
	for _, customer := range out[0].Customers {
		if customer.Id == customerID {
			for _, item := range customer.Admin {
				if item == config.CLOUD_KEY {
					adminCustomer = true
					break Loop
				}
			}
		}
	}
	if !adminCustomer {
		match["administrators"] = me.ID
	}
	total, err := tools.Database.Collection("project").CountDocuments(nil, bson.M{"customer_id": customerOID})
	skip := int64(start)
	l := int64(limit)
	opts := &options.FindOptions{
		Sort:  bson.D{{"info.number", 1}},
		Skip:  &skip,
		Limit: &l,
	}
	cursor, err := tools.Database.Collection("project").Find(nil, match, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var data []models.Project
	if err := cursor.All(nil, &data); err != nil {
		return nil, errors.WithStack(err)
	}
	return map[string]interface{}{"total": total, "items": data}, nil
}

// AddCustomerUser 绑定用户与客户的关系的逻辑改成调用cloud
func (s *CustomerService) AddCustomerUser(ctx *gin.Context, customerID string, data models.UserInfo) error {
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var err error

		la := ctx.GetHeader("Accept-Language")
		lang := ctx.GetHeader("Accept-Language")
		if data.EmailLanguage != nil {
			lang = *data.EmailLanguage
		}
		ctx.Request.Header.Set("Accept-Language", lang)

		// 检查IRT中是否已存在该用户
		var user models.User
		//bind := false // 是否需要绑定用户与客户的关系
		invited := false
		userFetchResp, err := tools.UserFetch(&models.UserFetchRequest{Emails: []string{data.Email}}, locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		users := make([]*models.UserData, 0)
		userCollection := userFetchResp
		if len(userCollection) > 0 {
			for _, u := range userCollection {
				if u.Deleted == false {
					users = append(users, u)
				}
			}
		}
		if len(users) == 0 {
			_, err := tools.Database.Collection("user").UpdateOne(sctx, bson.M{"info.email": strings.ToLower(data.Email), "deleted": bson.M{"$ne": true}}, bson.M{"$set": bson.M{"deleted": true}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		if err := tools.Database.Collection("user").FindOne(sctx, bson.M{"info.email": strings.ToLower(data.Email), "deleted": bson.M{"$ne": true}}).Decode(&user); err != nil {
			// 用户不存在，调用Cloud创建用户
			var inviteResp *models.UserInviteResult
			var out *models.UserInviteResult
			if inviteResp, err = tools.UserInvite(&models.UserInviteRequest{Email: data.Email, CustomerId: customerID, Admin: false}, locales.Lang(ctx)); err == nil {
				out = inviteResp
				// IRT中创建用户
				uid, _ := primitive.ObjectIDFromHex(out.Uid)
				userData, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{out.Uid}}, locales.Lang(ctx))
				if err != nil {
					return nil, errors.WithStack(err)
				}
				cloudUser := userData[0]
				user = models.User{
					ID: uid,
					UserInfo: models.UserInfo{
						EmailLanguage: data.EmailLanguage,
						Email:         strings.ToLower(strings.TrimSpace(data.Email)),
						Unicode:       cloudUser.Number,
					}, // 大写转小写查询不到  插入小写的邮箱
					Roles:         []primitive.ObjectID{},
					CloseCustomer: []primitive.ObjectID{},
					CloudId:       uid,
				}
				if _, err = tools.Database.Collection("user").InsertOne(sctx, user); err != nil {
					return nil, errors.WithStack(err)
				}
				//bind = true
				invited = true
			} else {
				return nil, errors.WithStack(err)
			}
		} else {
			// 检查是否已存在该客户下
			if err != nil {
				return nil, err
			}
			userData := users[0]
			if userData.Status == int32(2) {
				ctx.Request.Header.Set("Accept-Language", la)
				return nil, tools.BuildServerError(ctx, "users.duplicated.cloud-customer")
			}
			customers := userData.Customers
			for _, customer := range customers {
				if customer.Id == customerID && slice.Contain(customer.Apps, config.CLOUD_KEY) {
					ctx.Request.Header.Set("Accept-Language", la)
					return nil, tools.BuildServerError(ctx, "users.duplicated.customer")
				}
			}
			//bind = true
		}
		if len(users) > 0 {
			userData := users[0]
			user, err = convert.CloudUserToUser(&user, userData)
		}
		ctx.Request.Header.Set("Accept-Language", lang)
		if !invited {
			//customers := make([]*pb.UserBindCustomer, 0)
			//customers = append(customers, &pb.UserBindCustomer{
			//	Id:    customerID,
			//	Admin: false,
			//})
			_, err := tools.UserInvite(&models.UserInviteRequest{
				Email:      user.Email,
				CustomerId: customerID,
				Admin:      false,
			}, locales.Lang(ctx))
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		var customerName string
		var customerNameEn string
		allCustomers, err := tools.ListAllCustomer(locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, customerData := range allCustomers {
			if customerData.Id == customerID {
				customerName = customerData.Name
				customerNameEn = customerData.NameEn
				break
			}
		}
		if customerName == "" {
			ctx.Request.Header.Set("Accept-Language", la)
			return nil, tools.BuildServerError(ctx, "customer.not.exist")
		}

		langList := make([]string, 0)
		langList = append(langList, lang)
		if lang == "en" && customerNameEn != "" {
			customerName = customerNameEn
		}

		var mails []models.Mail
		mails = append(mails, models.Mail{
			ID:      primitive.NewObjectID(),
			Subject: "user.notice.customer-title",
			HTML:    "user_notice_new.html",
			ContentData: map[string]interface{}{
				"Title":    locales.Tr(ctx, "user.notice.customer.bind.title"),
				"Activate": locales.Tr(ctx, "user.notice.return.login"),
				"Content": locales.Tr(ctx, "user.notice_customer", map[string]interface{}{
					"email":    user.Email,
					"customer": customerName,
				}),
				"Link": config.CLOUD,
			},
			To:           []string{user.Email},
			Lang:         ctx.GetHeader("Accept-Language"),
			LangList:     langList,
			Status:       0,
			CreatedTime:  time.Duration(time.Now().Unix()),
			ExpectedTime: time.Duration(time.Now().Unix()),
			SendTime:     time.Duration(time.Now().Unix()),
		})
		ctx.Set("MAIL", mails)
		type id struct {
			ID primitive.ObjectID `bson:"_id"`
		}
		idList := make([]id, 0)
		pipeline := mongo.Pipeline{
			{{"$match", bson.D{
				{"notice_type", 1},
				{"receive.user_id", bson.D{{"$ne", user.ID}}},
			}}},
			{{"$project", bson.D{
				{"_id", 1}, // 只选择 _id 字段
			}}},
		}
		cursor, err := tools.Database.Collection("message_center").Aggregate(sctx, pipeline)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(nil, &idList)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		for _, n := range idList {
			_, err = tools.Database.Collection("message_center").UpdateOne(sctx,
				bson.M{"_id": n.ID},
				bson.M{"$addToSet": bson.M{"receive": models.Receive{
					UserId: user.ID,
					Read:   false,
				}}})
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// AddCustomerBatchUserVerify 批量验证邮箱---绑定用户与客户的关系的逻辑改成调用cloud
func (s *CustomerService) AddCustomerBatchUserVerify(ctx *gin.Context, customerID string, data models.UserInfoList) ([]models.NotAvailableEmail, error) {

	isExistCustomer := false
	notAvailableEmailList := make([]models.NotAvailableEmail, 0)

	//la := ctx.GetHeader("Accept-Language")
	//lang := ctx.GetHeader("Accept-Language")
	//if data.EmailLanguage != nil {
	//	lang = *data.EmailLanguage
	//}
	//ctx.Request.Header.Set("Accept-Language", lang)

	var err error

	// 检查IRT中是否已存在该用户
	var user models.User
	//bind := false // 是否需要绑定用户与客户的关系
	var userFetchResp []*models.UserData
	userFetchResp, err = tools.UserFetch(&models.UserFetchRequest{Emails: data.EmailList}, locales.Lang(ctx))
	if err != nil {
		return notAvailableEmailList, errors.WithStack(err)
	}
	users := make([]*models.UserData, 0)
	if len(userFetchResp) > 0 {
		for _, u := range userFetchResp {
			users = append(users, u)
		}
	}

	allCustomers, err := tools.ListAllCustomer(locales.Lang(ctx))
	if err != nil {
		return notAvailableEmailList, errors.WithStack(err)
	}
	for _, customerData := range allCustomers {
		if customerData.Id == customerID {
			isExistCustomer = true
			break
		}
	}

	if !isExistCustomer {
		return notAvailableEmailList, tools.BuildServerError(ctx, "customer.not.exist")
	}

	notEmailList := make([]string, 0)
	if users != nil && len(users) > 0 {
		for _, userData := range users {
			if err := tools.Database.Collection("user").FindOne(ctx, bson.M{"info.email": strings.ToLower(userData.Info.Email)}).Decode(&user); err == nil {
				// 检查是否已存在该客户下
				if err != nil {
					return nil, err
				}
				if userData.Status == int32(2) {
					//Cloud中用户已禁用，无法添加。
					emailIndex := arrays.Contains(notEmailList, userData.Info.Email)
					if emailIndex == -1 {
						notEmailList = append(notEmailList, userData.Info.Email)

						var notAvailableEmail models.NotAvailableEmail
						notAvailableEmail.Email = userData.Info.Email
						notAvailableEmail.Reason = locales.Tr(ctx, "users.duplicated.cloud-disable")
						index := arrays.Contains(notAvailableEmailList, notAvailableEmail)
						if index == -1 {
							notAvailableEmailList = append(notAvailableEmailList, notAvailableEmail)
						}
						continue
					}
				}

				if !userData.Deleted {
					customers := userData.Customers
					for _, customer := range customers {
						if customer.Id == customerID && slice.Contain(customer.Apps, config.CLOUD_KEY) {
							//用户已存在
							emailIndex := arrays.Contains(notEmailList, userData.Info.Email)
							if emailIndex == -1 {
								notEmailList = append(notEmailList, userData.Info.Email)

								var notAvailableEmail models.NotAvailableEmail
								notAvailableEmail.Email = userData.Info.Email
								notAvailableEmail.Reason = locales.Tr(ctx, "users.duplicated.cloud-exist")
								index := arrays.Contains(notAvailableEmailList, notAvailableEmail)
								if index == -1 {
									notAvailableEmailList = append(notAvailableEmailList, notAvailableEmail)
								}
								continue
							}
						}
					}
				}
			}
		}
		for _, userData := range users {
			if err := tools.Database.Collection("user").FindOne(ctx, bson.M{"info.email": strings.ToLower(userData.Info.Email)}).Decode(&user); err == nil {
				// 检查是否已存在该客户下
				if err != nil {
					return nil, err
				}
				if userData.Deleted {
					//Cloud中用户已删除，无法添加。----用户已删除，请联系Cloud Admin确认。
					emailIndex := arrays.Contains(notEmailList, userData.Info.Email)
					if emailIndex == -1 {
						notEmailList = append(notEmailList, userData.Info.Email)

						var notAvailableEmail models.NotAvailableEmail
						notAvailableEmail.Email = userData.Info.Email
						notAvailableEmail.Reason = locales.Tr(ctx, "users.duplicated.cloud-delete")
						index := arrays.Contains(notAvailableEmailList, notAvailableEmail)
						if index == -1 {
							notAvailableEmailList = append(notAvailableEmailList, notAvailableEmail)
						}
						continue
					}
				}
			}
		}
	}

	return notAvailableEmailList, nil
}

// AddCustomerBatchUser 批量操作---绑定用户与客户的关系的逻辑改成调用cloud
func (s *CustomerService) AddCustomerBatchUser(ctx *gin.Context, customerID string, data models.UserInfoList) ([]primitive.ObjectID, error) {

	userIdList := make([]primitive.ObjectID, 0)

	lang := ctx.GetHeader("Accept-Language")
	if data.EmailLanguage != nil {
		lang = *data.EmailLanguage
	}
	ctx.Request.Header.Set("Accept-Language", lang)

	callback := func(sctx mongo.SessionContext) (interface{}, error) {

		var err error

		// 检查IRT中是否已存在该用户
		var user models.User
		//bind := false // 是否需要绑定用户与客户的关系
		var userFetchResp []*models.UserData
		userFetchResp, err = tools.UserFetch(&models.UserFetchRequest{Emails: data.EmailList}, locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		userList := make([]*models.UserData, 0)
		deleteUserList := make([]*models.UserData, 0)
		if len(userFetchResp) > 0 {
			for _, u := range userFetchResp {
				if u.Deleted == false {
					userList = append(userList, u)
				} else {
					deleteUserList = append(deleteUserList, u)
				}
			}
		}
		if deleteUserList != nil && len(deleteUserList) > 0 {
			for _, deleteUser := range deleteUserList {
				_, err := tools.Database.Collection("user").UpdateOne(sctx, bson.M{"info.email": strings.ToLower(deleteUser.Info.Email), "deleted": bson.M{"$ne": true}}, bson.M{"$set": bson.M{"deleted": true}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}

		mails := make([]models.Mail, 0)
		for _, email := range data.EmailList {
			invited := false
			if err := tools.Database.Collection("user").FindOne(sctx, bson.M{"info.email": strings.ToLower(email), "deleted": bson.M{"$ne": true}}).Decode(&user); err != nil {
				// 用户不存在，调用Cloud创建用户
				var out *models.UserInviteResult
				if out, err = tools.UserInvite(&models.UserInviteRequest{Email: email, CustomerId: customerID, Admin: false}, locales.Lang(ctx)); err == nil {
					// IRT中创建用户
					uid, _ := primitive.ObjectIDFromHex(out.Uid)
					userData, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{out.Uid}}, locales.Lang(ctx))
					if err != nil {
						return nil, errors.WithStack(err)
					}
					cloudUser := userData[0]
					user = models.User{
						ID: uid,
						UserInfo: models.UserInfo{
							EmailLanguage: data.EmailLanguage,
							Email:         strings.ToLower(strings.TrimSpace(email)),
							Unicode:       cloudUser.Number,
						}, // 大写转小写查询不到  插入小写的邮箱
						Roles:         []primitive.ObjectID{},
						CloseCustomer: []primitive.ObjectID{},
						CloudId:       uid,
					}
					if _, err = tools.Database.Collection("user").InsertOne(sctx, user); err != nil {
						return nil, errors.WithStack(err)
					}
					//bind = true
					invited = true
				} else {
					return nil, errors.WithStack(err)
				}
			}

			ctx.Request.Header.Set("Accept-Language", lang)
			if !invited {
				//customers := make([]*pb.UserBindCustomer, 0)
				//customers = append(customers, &pb.UserBindCustomer{
				//	Id:    customerID,
				//	Admin: false,
				//})
				_, err := tools.UserInvite(&models.UserInviteRequest{
					Email:      user.Email,
					CustomerId: customerID,
					Admin:      false,
				}, locales.Lang(ctx))
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}

			var customerName string
			var customerNameEn string
			allCustomers, err := tools.ListAllCustomer(locales.Lang(ctx))
			if err != nil {
				return nil, errors.WithStack(err)
			}
			for _, customerData := range allCustomers {
				if customerData.Id == customerID {
					customerName = customerData.Name
					customerNameEn = customerData.NameEn
					break
				}
			}

			langList := make([]string, 0)
			langIndex := arrays.ContainsString(langList, lang)
			if langIndex == -1 {
				langList = append(langList, lang)
			}
			if lang == "en" && customerNameEn != "" {
				customerName = customerNameEn
			}

			mails = append(mails, models.Mail{
				ID:      primitive.NewObjectID(),
				Subject: "user.notice.customer-title",
				HTML:    "user_notice_new.html",
				ContentData: map[string]interface{}{
					"Title":    locales.Tr(ctx, "user.notice.customer.bind.title"),
					"Activate": locales.Tr(ctx, "user.notice.return.login"),
					"Content": locales.Tr(ctx, "user.notice_customer", map[string]interface{}{
						"email":    user.Email,
						"customer": customerName,
					}),
					"Link": config.CLOUD,
				},
				To:           []string{user.Email},
				Lang:         ctx.GetHeader("Accept-Language"),
				LangList:     langList,
				Status:       0,
				CreatedTime:  time.Duration(time.Now().Unix()),
				ExpectedTime: time.Duration(time.Now().Unix()),
				SendTime:     time.Duration(time.Now().Unix()),
			})

			userIdIndex := arrays.Contains(userIdList, user.ID)
			if userIdIndex == -1 {
				userIdList = append(userIdList, user.ID)
			}

			type id struct {
				ID primitive.ObjectID `bson:"_id"`
			}
			idList := make([]id, 0)
			pipeline := mongo.Pipeline{
				{{"$match", bson.D{
					{"notice_type", 1},
					{"receive.user_id", bson.D{{"$ne", user.ID}}},
				}}},
				{{"$project", bson.D{
					{"_id", 1}, // 只选择 _id 字段
				}}},
			}
			cursor, err := tools.Database.Collection("message_center").Aggregate(sctx, pipeline)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			err = cursor.All(nil, &idList)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			for _, n := range idList {
				_, err = tools.Database.Collection("message_center").UpdateOne(sctx,
					bson.M{"_id": n.ID},
					bson.M{"$addToSet": bson.M{"receive": models.Receive{
						UserId: user.ID,
						Read:   false,
					}}})
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}
		ctx.Set("MAIL", mails)

		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return userIdList, errors.WithStack(err)
	}

	return userIdList, nil
}

func (s *CustomerService) SetUsersRoles(ctx *gin.Context, userRole models.UserRole) error {

	if userRole.UserIdList != nil && len(userRole.UserIdList) > 0 && userRole.RoleIdList != nil && len(userRole.RoleIdList) > 0 {
		for _, userId := range userRole.UserIdList {
			filter := bson.M{"_id": userId}
			update := bson.M{"$addToSet": bson.M{"roles": bson.M{"$each": userRole.RoleIdList}}}
			_, err := tools.Database.Collection("user").UpdateOne(nil, filter, update)
			if err != nil {
				return errors.WithStack(err)
			}
		}
	}

	return nil
}

func (s *CustomerService) AddCustomerProject(ctx *gin.Context, customerID string, projectInfo models.ProjectInfo) error {
	var err error
	customerOID, err := primitive.ObjectIDFromHex(customerID)
	if err != nil {
		return errors.WithStack(err)
	}
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		if count, _ := tools.Database.Collection("project").CountDocuments(sctx, bson.M{"info.number": projectInfo.Number}); count > 0 {
			return nil, tools.BuildServerError(ctx, "projects.duplicated.numbers")
		}
		projectInfo.TimeZoneStr = "8"
		projectInfo.Room = true
		projectOID := primitive.NewObjectID()
		project := models.Project{
			ID:             projectOID,
			CustomerID:     customerOID,
			ProjectInfo:    projectInfo,
			Environments:   []models.Environment{},
			Administrators: []primitive.ObjectID{},
			Status:         0, //项目进行中
			Meta: models.Meta{
				CreatedAt: time.Duration(time.Now().Unix()),
			},
		}
		if _, err = tools.Database.Collection("project").InsertOne(sctx, project); err != nil {
			return nil, errors.WithStack(err)
		}
		//初始化 拷贝系统角色权限绑定到项目
		template := 1
		if projectInfo.ResearchAttribute == 1 {
			template = 2
		}
		rolePermissions := make([]models.RolePermission, 0)
		cursor, err := tools.Database.Collection("role_permission").Find(sctx, bson.M{"template": template})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = cursor.All(sctx, &rolePermissions)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		roleWithTypes := slice.Map(rolePermissions, func(index int, item models.RolePermission) models.RolePermissionWithType {
			typeRoleP, ok := slice.Find(data.RolePools, func(index int, pool models.RolePool) bool {
				return item.Name == pool.Name
			})
			if ok {
				typeRole := *typeRoleP
				return models.RolePermissionWithType{
					ID:          item.ID,
					Type:        typeRole.Type,
					Name:        item.Name,
					Scope:       item.Scope,
					Description: item.Description,
					Template:    item.Template,
					Status:      item.Status,
					Permissions: item.Permissions,
				}
			}
			return models.RolePermissionWithType{}
		})
		roleWithTypes = slice.Filter(roleWithTypes, func(index int, item models.RolePermissionWithType) bool {
			return !item.ID.IsZero() && !slice.Contain([]int{4, 5}, item.Type)
		})
		projectRolePermissions := slice.Map(roleWithTypes, func(index int, item models.RolePermissionWithType) interface{} {
			return models.ProjectRolePermission{
				ID:          primitive.NewObjectID(),
				RoleId:      item.ID,
				CustomerID:  customerOID,
				ProjectID:   projectOID,
				Name:        item.Name,
				Scope:       item.Scope,
				Description: item.Description,
				Template:    item.Template,
				Status:      item.Status,
				Permissions: item.Permissions,
			}
		})

		_, err = tools.Database.Collection("project_role_permission").InsertMany(sctx, projectRolePermissions)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return nil, nil
	}
	err = tools.Transaction(callback)

	if err != nil {
		return err
	}
	return nil
}

func (s *CustomerService) CloseCustomerUser(ctx *gin.Context) error {
	customerID := ctx.Query("customerId")
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	userID := ctx.Query("userId")
	userOID, _ := primitive.ObjectIDFromHex(userID)
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		var user models.User
		err := tools.Database.Collection("user").FindOneAndUpdate(sctx, bson.M{"_id": userOID}, bson.M{"$addToSet": bson.M{"close_customer": customerOID}}).Decode(&user)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		_, err = tools.Database.Collection("user_project_environment").UpdateMany(sctx,
			bson.M{"user_id": userOID, "customer_id": customerOID},
			bson.M{"$set": bson.M{"unbind": true}})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//取消客户管理员
		//TODO 先判断是不是客户管理员
		_ = tools.RemoveCustomerAdmin(&models.CustomerAdminRequest{CustomerId: customerID, Email: user.Email}, locales.Lang(ctx))
		//取消该客户下的所有项目管理员
		_, err = tools.Database.Collection("project").UpdateMany(sctx, bson.M{"customer_id": customerOID}, bson.M{"$pull": bson.M{"administrators": userOID}})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//日志
		userNameEmail := fmt.Sprintf("%s,%s", user.Name, user.Email)
		err = insertCustomerUserLog(ctx, sctx, customerOID, 13, 1, 0, userNameEmail, userOID, "")
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}

	//websocket通知
	go func() {
		wsResult := models.WsResult{
			CustomerId: customerOID,
			UserId:     userOID,
			Text:       "closeUser",
		}

		jsonData, err := json.Marshal(wsResult)
		if err != nil {
			log.Fatalf("JSON marshaling failed: %s", err)
		}
		jsonString := string(jsonData)
		ws.Notify(userID, jsonString)
	}()

	return nil
}

func (s *CustomerService) BatchCloseCustomerUser(ctx *gin.Context) error {
	type BodyData struct {
		Ids []string `json:"ids"`
	}
	var bodyData BodyData
	_ = ctx.ShouldBindBodyWith(&bodyData, binding.JSON)
	userIDs := bodyData.Ids
	userOIDs := make([]primitive.ObjectID, 0, len(userIDs))
	// 遍历转换每个ID
	for _, id := range userIDs {
		oid, _ := primitive.ObjectIDFromHex(id)
		userOIDs = append(userOIDs, oid)
	}

	customerID := ctx.Query("customerId")
	customerOID, _ := primitive.ObjectIDFromHex(customerID)

	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		// 1. 批量更新用户集合，添加close_customer字段
		_, err := tools.Database.Collection("user").UpdateMany(
			sctx,
			bson.M{"_id": bson.M{"$in": userOIDs}},
			bson.M{"$addToSet": bson.M{"close_customer": customerOID}},
		)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 2. 批量更新user_project_environment集合
		_, err = tools.Database.Collection("user_project_environment").UpdateMany(
			sctx,
			bson.M{
				"user_id":     bson.M{"$in": userOIDs},
				"customer_id": customerOID,
			},
			bson.M{"$set": bson.M{"unbind": true}},
		)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 3. 查询所有用户信息用于后续操作
		cursor, err := tools.Database.Collection("user").Find(
			sctx,
			bson.M{"_id": bson.M{"$in": userOIDs}},
		)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		defer cursor.Close(sctx)

		var users []models.User
		if err = cursor.All(sctx, &users); err != nil {
			return nil, errors.WithStack(err)
		}

		// 4. 为每个用户执行取消管理员和日志记录操作
		for _, user := range users {
			// 取消客户管理员
			_ = tools.RemoveCustomerAdmin(&models.CustomerAdminRequest{
				CustomerId: customerID,
				Email:      user.Email,
			}, locales.Lang(ctx))

			// 5. 取消该客户下的所有项目管理员
			_, err = tools.Database.Collection("project").UpdateMany(
				sctx,
				bson.M{"customer_id": customerOID},
				bson.M{"$pull": bson.M{"administrators": user.ID}},
			)
			if err != nil {
				return nil, errors.WithStack(err)
			}

			// 6. 记录日志
			userNameEmail := fmt.Sprintf("%s,%s", user.Name, user.Email)
			err = insertCustomerUserLog(
				ctx, sctx, customerOID, 13, 1, 0, userNameEmail, user.ID, "",
			)
			if err != nil {
				return nil, err
			}
		}
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

func (s *CustomerService) InviteAgainCustomerUser(ctx *gin.Context) error {
	customerID := ctx.Query("customerId")
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	userID := ctx.Query("userId")
	userOID, _ := primitive.ObjectIDFromHex(userID)
	emailLanguage := ctx.Query("emailLanguage")
	callback := func(sctx mongo.SessionContext) (interface{}, error) {
		//获取本地user
		var user models.User
		err := tools.Database.Collection("user").FindOne(sctx, bson.M{"_id": userOID}).Decode(&user)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if len(emailLanguage) != 0 {
			ctx.Request.Header.Set("Accept-Language", emailLanguage)
		}

		// 检查用户是否已激活
		users, err := tools.UserFetch(&models.UserFetchRequest{Ids: []string{user.CloudId.Hex()}}, locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		cloudUser := users[0]
		if cloudUser.Status == 0 {
			_, err = tools.UserInvite(&models.UserInviteRequest{Email: cloudUser.Info.Email, CustomerId: customerID, Admin: false}, locales.Lang(ctx))
			if err != nil {
				return nil, errors.WithStack(err)
			}
		}
		var customerName string
		var customerNameEN string
		allCustomers, err := tools.ListAllCustomer(locales.Lang(ctx))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, customerData := range allCustomers {
			if customerData.Id == customerID {
				customerName = customerData.Name
				customerNameEN = customerData.NameEn
				break
			}
		}
		if customerName == "" {
			return nil, tools.BuildServerError(ctx, "customer.not.exist")
		}

		_, err = tools.Database.Collection("user").UpdateOne(sctx,
			bson.M{"_id": userOID},
			bson.M{"$pull": bson.M{"close_customer": customerOID}})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		//日志
		userNameEmail := fmt.Sprintf("%s,%s", user.Name, user.Email)
		err = insertCustomerUserLog(ctx, sctx, customerOID, 12, 1, 0, userNameEmail, userOID, emailLanguage)
		if err != nil {
			return nil, err
		}

		if ctx.GetHeader("Accept-Language") == "en" && customerNameEN != "" {
			customerName = customerNameEN
		}
		var mails []models.Mail
		mails = append(mails, models.Mail{
			ID:      primitive.NewObjectID(),
			Subject: "user.notice.customer-title",
			HTML:    "user_notice_new.html",
			ContentData: map[string]interface{}{
				"Title":    locales.Tr(ctx, "user.notice.customer.bind.title"),
				"Activate": locales.Tr(ctx, "user.notice.return.login"),
				"Content": locales.Tr(ctx, "user.notice_customer", map[string]interface{}{
					"email":    user.Email,
					"customer": customerName,
				}),
				"Link": config.CLOUD,
			},
			To:           []string{user.Email},
			Lang:         ctx.GetHeader("Accept-Language"),
			Status:       0,
			CreatedTime:  time.Duration(time.Now().Unix()),
			ExpectedTime: time.Duration(time.Now().Unix()),
			SendTime:     time.Duration(time.Now().Unix()),
		})
		ctx.Set("MAIL", mails)
		return nil, nil
	}
	err := tools.Transaction(callback)
	if err != nil {
		return err
	}
	return nil
}

// 设置为已关闭状态
func closed(ctx context.Context, userId string, customerID string, cloudType int) {
	customerOID, _ := primitive.ObjectIDFromHex(customerID)
	userOID, _ := primitive.ObjectIDFromHex(userId)

	var us models.User
	if cloudType == 1 {
		_ = tools.Database.Collection("user").FindOneAndUpdate(nil, bson.M{"_id": userOID}, bson.M{"$addToSet": bson.M{"close_customer": customerOID},
			"$set": bson.M{"deleted": true}}).Decode(&us)
	} else if cloudType == 2 {
		_ = tools.Database.Collection("user").FindOneAndUpdate(nil, bson.M{"_id": userOID}, bson.M{"$addToSet": bson.M{"close_customer": customerOID},
			"$set": bson.M{"info.status": int8(2)}}).Decode(&us)
	}

	_, _ = tools.Database.Collection("user_project_environment").UpdateMany(nil,
		bson.M{"user_id": userOID, "customer_id": customerOID},
		bson.M{"$set": bson.M{"unbind": true}})

	//取消客户管理员
	//TODO 先判断是不是客户管理员
	_ = tools.RemoveCustomerAdmin(&models.CustomerAdminRequest{CustomerId: customerID, Email: us.Email}, locales.Lang(ctx))
	//取消该客户下的所有项目管理员
	_, _ = tools.Database.Collection("project").UpdateMany(nil, bson.M{"customer_id": customerOID}, bson.M{"$pull": bson.M{"administrators": userOID}})

	var userProjectEnvironments []models.UserProjectEnvironment
	opt := &options.FindOptions{
		Sort: bson.D{{"_id", -1}},
	}
	//filter := bson.M{"env_id": eid, "project_id": oid, "user_id": user.ID}
	cursor, _ := tools.Database.Collection("user_project_environment").Find(nil, bson.M{"user_id": userOID, "customer_id": customerOID}, opt)
	cursor.All(nil, &userProjectEnvironments)
	for _, userProjectEnvironment := range userProjectEnvironments {
		_ = updatetUnbindInviteProjectUserLog(userProjectEnvironment.EnvID, 7, 1, 0, us.Email, userProjectEnvironment.ID, cloudType, userOID)
	}

}

func stringInSlice(str string, list []string) bool {
	for _, v := range list {
		if v == str {
			return true
		}
	}
	return false
}

// 人员管理日志——解绑/再次授权
func updatetUnbindInviteProjectUserLog(OID primitive.ObjectID, types int, old int, new int, userNameEmail string, operatorID primitive.ObjectID, cloudType int, userOID primitive.ObjectID) error {

	OperationLogFieldGroups := make([]models.OperationLogFieldGroup, 0)

	if old != new {
		OperationLogFieldGroups = append(OperationLogFieldGroups, models.OperationLogFieldGroup{
			Key:     "operation_log.projectUser",
			TranKey: "operation_log.projectUser.status",
			Old: models.OperationLogField{
				Type:  6,
				Value: old,
			},
			New: models.OperationLogField{
				Type:  6,
				Value: new,
			},
		})
	}

	if OperationLogFieldGroups != nil && len(OperationLogFieldGroups) > 0 {
		marks := make([]models.Mark, 0)
		marks = append(marks, models.Mark{
			Label: "operation_log.label.projectUser",
			Value: userNameEmail,
			Blind: false,
		})
		err := SaveOperation("operation_log.module.projectUser", OID, types, OperationLogFieldGroups, marks, operatorID, cloudType, userOID)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func SaveOperation(module string, OID primitive.ObjectID, types int, OperationLogFieldGroups []models.OperationLogFieldGroup, mark []models.Mark, operatorID primitive.ObjectID, cloudType int, userOID primitive.ObjectID) error {
	var operationLog models.OperationLog
	operationLog.ID = primitive.NewObjectID()
	operationLog.OID = OID
	operationLog.Operator = userOID
	operationLog.Module = module
	operationLog.Mark = mark
	operationLog.Time = time.Duration(time.Now().Unix())
	operationLog.Type = types
	operationLog.OperatorID = operatorID
	operationLog.Fields = OperationLogFieldGroups
	operationLog.CloudType = cloudType
	_, err := tools.Database.Collection("operation_log").InsertOne(nil, operationLog)
	return err
}
