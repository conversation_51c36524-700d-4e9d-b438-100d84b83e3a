package service

import (
	"clinflash-irt/locales"
	"clinflash-irt/models"
	"clinflash-irt/multilingual"
	"clinflash-irt/tools"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func ExportConfigureReportPdf(ctx *gin.Context, projectID string, envID string, cohortIds []string, roleId string, now time.Time) (models.ConfigureReport, error) {

	var pdfData models.ConfigureReport

	// 获取翻译映射
	transMap := configureReportTransMap(ctx)

	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	var project models.Project
	err := tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return pdfData, err
	}

	user, err := tools.Me(ctx)
	if err != nil {
		return pdfData, err
	}
	timeZone, err := tools.GetTimeZone(projectOID)
	if err != nil {
		return pdfData, err
	}
	hour := time.Duration(timeZone)
	minute := time.Duration((timeZone - float64(hour)) * 60)
	duration := hour*time.Hour + minute*time.Minute
	date := time.Now().UTC().Add(duration).Format("2006-01-02 15:04:05")

	envName := ""
	cohortNameList := make([]string, 0)
	if project.Environments != nil && len(project.Environments) > 0 {
		for _, environment := range project.Environments {
			if environment.ID == envOID {
				envName = environment.Name
				if environment.Cohorts != nil && len(environment.Cohorts) > 0 && cohortIds != nil && len(cohortIds) > 0 {
					for _, cohortId := range cohortIds {
						for _, cohort := range environment.Cohorts {
							if cohort.ID.Hex() == cohortId {
								cohortNameList = append(cohortNameList, models.GetCohortReRandomName(cohort))
							}
						}
					}
				}
			}
		}
	}

	userName := ""
	if user.Unicode == 0 {
		userName = user.Name
	} else {
		userName = fmt.Sprintf("%s(%d)", user.Name, user.Unicode)
	}

	pdfData = models.ConfigureReport{
		Sponsor:                          project.Sponsor,
		ProjectNumber:                    project.Number,
		ConfigureReport:                  transMap["export.random_config.export"],
		Env:                              envName,
		GenerationTime:                   transMap["export.random_config.createDate"],
		CreateDate:                       date,
		Generator:                        transMap["export.random_config.createBy"],
		CreateBy:                         userName,
		Directory:                        transMap["export.random_config.directory"],
		Summary:                          transMap["export.random_config.summary"],
		SummaryDetails:                   transMap["export.random_config.report"],
		BasicInformation:                 transMap["configureReport.basicInformation"],
		ProjectTimeZoneLabel:             transMap["configureReport.basicInformation.projectTimeZone"],
		ProjectTypeLabel:                 transMap["configureReport.basicInformation.projectType"],
		ProjectOrderCheckLabel:           transMap["configureReport.basicInformation.projectOrderCheck"],
		ProjectOrderConfirmationLabel:    transMap["configureReport.basicInformation.projectOrderConfirmation"],
		ProjectDeIsolationApprovalLabel:  transMap["configureReport.basicInformation.projectDeIsolationApproval"],
		ProjectUnblindingControlLabel:    transMap["configureReport.basicInformation.projectUnblindingControl"],
		ProjectOrderApprovalControlLabel: transMap["configureReport.basicInformation.projectOrderApprovalControl"],
		ProjectNoticeLabel:               transMap["configureReport.basicInformation.projectNotice"],
		ProjectConnectEdcLabel:           transMap["configureReport.basicInformation.projectConnectEdc"],
		ProjectPushModeLabel:             transMap["configureReport.basicInformation.projectPushMode"],
		ProjectSynchronizationModeLabel:  transMap["configureReport.basicInformation.projectSynchronizationMode"],
		GeneralSituation:                 transMap["configureReport.generalSituation"],
		AllOfThem:                        transMap["configureReport.allOfThem"],
		Name:                             transMap["configureReport.name"],
		CohortNameList:                   cohortNameList,
	}
	//项目时区
	t, _ := tools.GetProjectLocationUtc(project.ProjectInfo.TimeZoneStr, project.ProjectInfo.Tz, "")
	pdfData.ProjectTimeZone = t
	//if project.ProjectInfo.TimeZone == 0 {
	//	pdfData.ProjectTimeZone = "UTC"
	//} else if project.ProjectInfo.TimeZone > 0 {
	//	pdfData.ProjectTimeZone = "UTC+" + strconv.Itoa(project.ProjectInfo.TimeZone)
	//} else if project.ProjectInfo.TimeZone < 0 {
	//	pdfData.ProjectTimeZone = "UTC" + strconv.Itoa(project.ProjectInfo.TimeZone)
	//}
	//项目类型
	if project.ProjectInfo.Type == 1 {
		pdfData.ProjectType = transMap["configureReport.basicInformation.projectType.basicStudy"]
	} else if project.ProjectInfo.Type == 2 {
		pdfData.ProjectType = transMap["configureReport.basicInformation.projectType.cohortStudy"]
	} else if project.ProjectInfo.Type == 3 {
		pdfData.ProjectType = transMap["configureReport.basicInformation.projectType.reRandomizationStudy"]
	}

	//中心研究产品库存核查
	if project.ProjectInfo.OrderCheck == 1 {
		pdfData.ProjectOrderCheck = transMap["configureReport.basicInformation.projectOrderCheck.timing"]
	} else if project.ProjectInfo.OrderCheck == 2 {
		pdfData.ProjectOrderCheck = transMap["configureReport.basicInformation.projectOrderCheck.realTime"]
	} else if project.ProjectInfo.OrderCheck == 3 {
		pdfData.ProjectOrderCheck = transMap["configureReport.basicInformation.projectOrderCheck.notApplicable"]
	}

	//中心回收订单确认
	if project.ProjectInfo.OrderConfirmation == 1 {
		pdfData.ProjectOrderConfirmation = transMap["configureReport.basicInformation.projectOrderConfirmation.open"]
	} else if project.ProjectInfo.OrderConfirmation == 0 {
		pdfData.ProjectOrderConfirmation = transMap["configureReport.basicInformation.projectOrderConfirmation.close"]
	}

	//解隔离审批
	if project.ProjectInfo.DeIsolationApproval == 1 {
		pdfData.ProjectDeIsolationApproval = transMap["configureReport.basicInformation.projectDeIsolationApproval.open"]
	} else if project.ProjectInfo.DeIsolationApproval == 0 {
		pdfData.ProjectDeIsolationApproval = transMap["configureReport.basicInformation.projectDeIsolationApproval.close"]
	}

	unblindingList := make([]string, 0)
	//揭盲控制
	if project.ProjectInfo.UnblindingControl == 1 {
		//紧急揭盲
		if project.ProjectInfo.UnblindingType == 1 {
			//确认方式 短信
			if project.ProjectInfo.UnblindingSms == 1 {
				str := transMap["configureReport.basicInformation.projectUnblindingControl.unblinding"] + "-" + transMap["configureReport.basicInformation.projectUnblindingControl.unblinding.sms"]
				unblindingList = append(unblindingList, str)
			}
			//确认方式 流程操作
			if project.ProjectInfo.UnblindingProcess == 1 {
				str := transMap["configureReport.basicInformation.projectUnblindingControl.unblinding"] + "-" + transMap["configureReport.basicInformation.projectUnblindingControl.unblinding.process"]
				unblindingList = append(unblindingList, str)
			}
			//揭盲码
			if project.ProjectInfo.UnblindingCode == 1 {
				str := transMap["configureReport.basicInformation.projectUnblindingControl.unblinding"] + "-" + transMap["configureReport.basicInformation.projectUnblindingControl.unblinding.code"]
				unblindingList = append(unblindingList, str)
			}
		}
		//PV揭盲
		if project.ProjectInfo.PvUnblindingType == 1 {
			//确认方式 短信
			if project.ProjectInfo.PvUnblindingSms == 1 {
				str := transMap["configureReport.basicInformation.projectUnblindingControl.pvUnblinding"] + "-" + transMap["configureReport.basicInformation.projectUnblindingControl.pvUnblinding.sms"]
				unblindingList = append(unblindingList, str)
			}
			//确认方式 流程操作
			if project.ProjectInfo.PvUnblindingProcess == 1 {
				str := transMap["configureReport.basicInformation.projectUnblindingControl.pvUnblinding"] + "-" + transMap["configureReport.basicInformation.projectUnblindingControl.pvUnblinding.process"]
				unblindingList = append(unblindingList, str)
			}
		}
		// 研究产品揭盲
		if project.ProjectInfo.IpUnblindingType == 1 {
			//确认方式 短信
			if project.ProjectInfo.IpUnblindingSms == 1 {
				str := transMap["configureReport.basicInformation.projectUnblindingControl.IpUnblinding"] + "-" + transMap["configureReport.basicInformation.projectUnblindingControl.IpUnblinding.sms"]
				unblindingList = append(unblindingList, str)
			}
			//确认方式 流程操作
			if project.ProjectInfo.IpUnblindingProcess == 1 {
				str := transMap["configureReport.basicInformation.projectUnblindingControl.IpUnblinding"] + "-" + transMap["configureReport.basicInformation.projectUnblindingControl.IpUnblinding.process"]
				unblindingList = append(unblindingList, str)
			}
		}
		if unblindingList != nil && len(unblindingList) > 0 {
			unblindingStr := strings.Join(unblindingList, ", ")
			pdfData.ProjectUnblindingControl = unblindingStr
		} else {
			pdfData.ProjectUnblindingControl = transMap["configureReport.basicInformation.projectUnblindingControl.false"]
		}
	} else if project.ProjectInfo.UnblindingControl == 0 {
		pdfData.ProjectUnblindingControl = transMap["configureReport.basicInformation.projectUnblindingControl.false"]
	}

	//研究中心订单申请
	if project.ProjectInfo.OrderApprovalSms == 0 && project.ProjectInfo.OrderApprovalProcess == 0 {
		pdfData.ProjectOrderApprovalControl = transMap["configureReport.basicInformation.projectOrderApprovalControl.false"]
	} else {
		if project.ProjectInfo.OrderApprovalSms == 1 {
			pdfData.ProjectOrderApprovalControl = transMap["configureReport.basicInformation.projectOrderApprovalControl.sms"]
		}
		if project.ProjectInfo.OrderApprovalProcess == 1 {
			pdfData.ProjectOrderApprovalControl = transMap["configureReport.basicInformation.projectOrderApprovalControl.process"]
		}
	}

	//项目通知
	if count, _ := tools.Database.Collection("project_notice").CountDocuments(nil, bson.M{"project_id": projectOID, "env_id": envOID}); count > 0 {
		var projectNotice models.ProjectNotice
		err = tools.Database.Collection("project_notice").FindOne(nil, bson.M{"project_id": projectOID, "env_id": envOID}).Decode(&projectNotice)
		if err != nil {
			return pdfData, err
		}
		if projectNotice.Open {
			pdfData.ProjectNotice = transMap["configureReport.basicInformation.projectNotice.open"]
		} else {
			pdfData.ProjectNotice = transMap["configureReport.basicInformation.projectNotice.close"]
		}

	} else {
		pdfData.ProjectNotice = transMap["configureReport.basicInformation.projectNotice.close"]
	}

	//对接EDC ---- 如对接，显示EDC供应商值，否则
	if project.ProjectInfo.ConnectEdc == 1 {
		if project.ProjectInfo.EdcSupplier == 1 {
			pdfData.ProjectConnectEdc = "Clinflash  EDC"
		} else if project.ProjectInfo.EdcSupplier == 2 {
			pdfData.ProjectConnectEdc = "Medidata Rave  EDC"
		}
		//数据推送方式
		if project.ProjectInfo.PushMode == 1 {
			pdfData.ProjectPushMode = transMap["configureReport.basicInformation.projectPushMode.real"]
			//同步方式
			if project.ProjectInfo.SynchronizationMode == 1 {
				pdfData.ProjectSynchronizationMode = transMap["configureReport.basicInformation.projectSynchronizationMode.screen"]
			} else if project.ProjectInfo.SynchronizationMode == 2 {
				pdfData.ProjectSynchronizationMode = transMap["configureReport.basicInformation.projectSynchronizationMode.random"]
			}
		} else if project.ProjectInfo.PushMode == 2 {
			pdfData.ProjectPushMode = transMap["configureReport.basicInformation.projectPushMode.active"]
			pdfData.ProjectSynchronizationMode = "-"
		}
	} else if project.ProjectInfo.ConnectEdc == 0 || project.ProjectInfo.ConnectEdc == 2 {
		pdfData.ProjectConnectEdc = "-"
		pdfData.ProjectPushMode = "-"
		pdfData.ProjectSynchronizationMode = "-"
	}

	//是否群组、再随机项目
	if project.ProjectInfo.Type == 1 {
		pdfData.IsGroupStage = false
	} else {
		pdfData.IsGroupStage = true
		if project.ProjectInfo.Type == 2 {
			pdfData.CohortOrStage = transMap["configureReport.cohort"]
		} else if project.ProjectInfo.Type == 3 {
			pdfData.CohortOrStage = transMap["configureReport.stage"]
		}
	}

	// 创建一个空的map[string]interface{}切片
	cohortDatas := make([]models.ConfigureDetail, 0)
	if cohortIds != nil && len(cohortIds) > 0 {
		for index, cohortId := range cohortIds {
			cohortData, err := configureReportHtmlData(ctx, projectID, envID, cohortId, roleId, now, transMap)
			if err != nil {
				return pdfData, errors.WithStack(err)
			}
			cohortData.Index = strconv.Itoa(4 + index)
			cohortDatas = append(cohortDatas, cohortData)
		}
	} else {
		cohortData, err := configureReportHtmlData(ctx, projectID, envID, "", roleId, now, transMap)
		if err != nil {
			return pdfData, errors.WithStack(err)
		}
		cohortData.Index = "3"
		cohortDatas = append(cohortDatas, cohortData)
	}
	pdfData.ConfigureDetailList = cohortDatas

	return pdfData, nil
}

func configureReportHtmlData(ctx *gin.Context, projectID string, envID string, cohortID string, roleId string, now time.Time, transMap map[string]string) (models.ConfigureDetail, error) {
	var configureDetail models.ConfigureDetail

	// 获取配置数据
	projectOID, _ := primitive.ObjectIDFromHex(projectID)
	envOID, _ := primitive.ObjectIDFromHex(envID)

	isBlindedRole, err := tools.IsBlindedRole(roleId)
	//isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	//if err != nil {
	//	return configureDetail, err
	//}

	match := bson.M{
		"project_id": projectOID,
		"env_id":     envOID,
	}
	if cohortID != "" {
		cohortOID, _ := primitive.ObjectIDFromHex(cohortID)
		match["cohort_id"] = cohortOID
	}

	//项目详情
	var project models.Project
	err = tools.Database.Collection("project").FindOne(nil, bson.M{"_id": projectOID}).Decode(&project)
	if err != nil {
		return configureDetail, err
	}
	if cohortID != "" {
		for _, environment := range project.Environments {
			if environment.ID == envOID {
				for _, cohort := range environment.Cohorts {
					if cohort.ID.Hex() == cohortID {
						configureDetail.Title = models.GetCohortReRandomName(cohort)
					}
				}
			}
		}
	} else {
		configureDetail.Title = transMap["configureReport.projectDetails"]
	}

	//属性配置
	var attribute models.Attribute
	err = tools.Database.Collection("attribute").FindOne(nil, match).Decode(&attribute)
	if err != nil {
		return configureDetail, err
	}
	var attributeConfigureInfo models.AttributeConfigureInfo
	attributeConfigureInfo.Title = transMap["configureReport.projectDetails.attributeConfigure"]
	//系统配置
	var systemConfigureInfo models.SystemConfigureInfo
	systemConfigureInfo.Title = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration"]
	systemConfigureInfo.RandomizeLabel = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize"]
	systemConfigureInfo.DisplayRandomizationIDLabel = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID"]
	systemConfigureInfo.DisplayRandomSequenceNumberLabel = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber"]
	systemConfigureInfo.RandomSequenceNumberPrefixLabel = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberPrefix"]
	systemConfigureInfo.RandomSequenceNumberDigitLabel = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberDigit"]
	systemConfigureInfo.RandomSequenceNumberStartLabel = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberStart"]
	systemConfigureInfo.TreatmentDesignLabel = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign"]
	systemConfigureInfo.DTPRuleLabel = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule"]
	systemConfigureInfo.RandomizationSupplyCheckLabel = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck"]
	systemConfigureInfo.BlindDesignLabel = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign"]
	systemConfigureInfo.SubjectScreeningProcessLabel = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess"]
	if attribute.AttributeInfo.Random {
		systemConfigureInfo.Randomize = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize.random"]
		if attribute.AttributeInfo.IsRandomNumber {
			systemConfigureInfo.DisplayRandomizationID = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID.show"]
		} else {
			systemConfigureInfo.DisplayRandomizationID = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID.notShow"]
		}
		if attribute.AttributeInfo.IsRandomSequenceNumber {
			systemConfigureInfo.DisplayRandomSequenceNumberShowFlag = true
			systemConfigureInfo.DisplayRandomSequenceNumber = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber.show"]
			systemConfigureInfo.RandomSequenceNumberPrefix = attribute.AttributeInfo.RandomSequenceNumberPrefix
			systemConfigureInfo.RandomSequenceNumberDigit = strconv.Itoa(attribute.AttributeInfo.RandomSequenceNumberDigit)
			systemConfigureInfo.RandomSequenceNumberStart = strconv.Itoa(attribute.AttributeInfo.RandomSequenceNumberStart)
		} else {
			systemConfigureInfo.DisplayRandomSequenceNumber = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber.notShow"]
		}

	} else {
		systemConfigureInfo.Randomize = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize.non-randomized"]
		systemConfigureInfo.DisplayRandomizationID = "-"
		systemConfigureInfo.DisplayRandomSequenceNumber = "-"
	}
	if attribute.AttributeInfo.Dispensing {
		systemConfigureInfo.TreatmentDesign = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign.dispensing.yes"]
		if attribute.AttributeInfo.RandomControl {
			if attribute.AttributeInfo.RandomControlRule == 1 {
				systemConfigureInfo.RandomizationSupplyCheck = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule1"]
			} else if attribute.AttributeInfo.RandomControlRule == 2 {
				systemConfigureInfo.RandomizationSupplyCheck = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule2"]
			} else if attribute.AttributeInfo.RandomControlRule == 3 {
				systemConfigureInfo.RandomizationSupplyCheck =
					transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3"] +
						transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3.least"] +
						strconv.Itoa(attribute.AttributeInfo.RandomControlGroup) +
						transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3.groups"]
			}
		} else {
			systemConfigureInfo.RandomizationSupplyCheck = "-"
		}
	} else {
		systemConfigureInfo.TreatmentDesign = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign.dispensing.no"]
		systemConfigureInfo.RandomizationSupplyCheck = "-"
	}

	if attribute.AttributeInfo.DtpRule == 1 {
		systemConfigureInfo.DTPRule = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.ip"]
	} else if attribute.AttributeInfo.DtpRule == 2 {
		systemConfigureInfo.DTPRule = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.visitFlow"]
	} else if attribute.AttributeInfo.DtpRule == 3 {
		systemConfigureInfo.DTPRule = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.notApplicable"]
	} else {
		systemConfigureInfo.DTPRule = "-"
	}

	if attribute.AttributeInfo.Blind {
		systemConfigureInfo.BlindDesign = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign.blind"]
	} else {
		systemConfigureInfo.BlindDesign = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign.open"]
	}
	if attribute.AttributeInfo.IsScreen {
		systemConfigureInfo.SubjectScreeningProcess = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess.open"]
	} else {
		systemConfigureInfo.SubjectScreeningProcess = transMap["configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess.close"]
	}
	attributeConfigureInfo.SystemConfigure = systemConfigureInfo
	//受试者号规则
	var subjectIDRulesInfo models.SubjectIDRulesInfo
	subjectIDRulesInfo.Title = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules"]
	subjectIDRulesInfo.SubjectNumberInputRuleLabel = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule"]
	subjectIDRulesInfo.SubjectPrefixLabel = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix"]
	subjectIDRulesInfo.SubjectIDPrefixLabel = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectIDPrefix"]
	subjectIDRulesInfo.ReplacementTextForSubjectIDLabel = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.replacementTextForSubjectID"]
	subjectIDRulesInfo.ReplacementTextEnForSubjectIDLabel = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.replacementTextEnForSubjectID"]
	subjectIDRulesInfo.SubjectIDDigitLabel = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectIDDigit"]
	subjectIDRulesInfo.SubjectReplacementLabel = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement"]
	subjectIDRulesInfo.TakeCare = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.takeCare"]
	if attribute.AttributeInfo.SubjectNumberRule == 0 || attribute.AttributeInfo.SubjectNumberRule == 1 {
		subjectIDRulesInfo.SubjectNumberInputRule = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule1"]
	} else if attribute.AttributeInfo.SubjectNumberRule == 2 {
		subjectIDRulesInfo.SubjectNumberInputRule = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule2"]
	} else if attribute.AttributeInfo.SubjectNumberRule == 3 {
		subjectIDRulesInfo.SubjectNumberInputRule = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule3"]
	}
	if attribute.AttributeInfo.Prefix {
		subjectIDRulesInfo.SubjectPrefix = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix.have"]
	} else {
		subjectIDRulesInfo.SubjectPrefix = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix.nothing"]
	}
	if len(attribute.AttributeInfo.PrefixExpression) > 0 {
		subjectIDRulesInfo.SubjectIDPrefix = attribute.AttributeInfo.PrefixExpression
	}
	if len(attribute.AttributeInfo.SubjectReplaceText) > 0 {
		subjectIDRulesInfo.ReplacementTextForSubjectID = attribute.AttributeInfo.SubjectReplaceText
	}
	if len(attribute.AttributeInfo.SubjectReplaceTextEn) > 0 {
		subjectIDRulesInfo.ReplacementTextEnForSubjectID = attribute.AttributeInfo.SubjectReplaceTextEn
	}
	subjectIDRulesInfo.SubjectIDDigit = strconv.Itoa(attribute.AttributeInfo.Digit)
	if attribute.AttributeInfo.AllowReplace {
		subjectIDRulesInfo.SubjectReplacement = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement.open"]
	} else {
		subjectIDRulesInfo.SubjectReplacement = transMap["configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement.close"]
	}
	attributeConfigureInfo.SubjectIDRules = subjectIDRulesInfo
	//其他规则
	var otherRulesInfo models.OtherRulesInfo
	otherRulesInfo.Title = transMap["configureReport.projectDetails.attributeConfigure.otherRules"]
	otherRulesInfo.StopUnblindedSubjectsLabel = transMap["configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects"]
	otherRulesInfo.QuarantinedIPCountingRuleLabel = transMap["configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule"]
	otherRulesInfo.TransportAccordingPackagingLabel = transMap["configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging"]
	otherRulesInfo.Deactivate = transMap["configureReport.projectDetails.attributeConfigure.otherRules.deactivate"]
	otherRulesInfo.Quarantine = transMap["configureReport.projectDetails.attributeConfigure.otherRules.quarantine"]
	otherRulesInfo.Packing = transMap["configureReport.projectDetails.attributeConfigure.otherRules.packing"]
	if attribute.AttributeInfo.UnBlindingRestrictions {
		otherRulesInfo.StopUnblindedSubjects = transMap["configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects.true"]
	} else {
		otherRulesInfo.StopUnblindedSubjects = transMap["configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects.false"]
	}
	if attribute.AttributeInfo.IsFreeze {
		otherRulesInfo.QuarantinedIPCountingRule = transMap["configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule.true"]
	} else {
		otherRulesInfo.QuarantinedIPCountingRule = transMap["configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule.false"]
	}
	if count, _ := tools.Database.Collection("drug_package_configure").CountDocuments(nil, bson.M{"project_id": projectOID, "env_id": envOID}); count > 0 {
		var drugPackageConfigure models.DrugPackageConfigure
		err = tools.Database.Collection("drug_package_configure").FindOne(nil, bson.M{"project_id": projectOID, "env_id": envOID}).Decode(&drugPackageConfigure)
		if err != nil {
			return configureDetail, err
		}
		if drugPackageConfigure.IsOpen {
			otherRulesInfo.TransportAccordingPackaging = transMap["configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.true"]
		} else {
			otherRulesInfo.TransportAccordingPackaging = transMap["configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.false"]
		}
	} else {
		otherRulesInfo.TransportAccordingPackaging = transMap["configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.false"]
	}
	attributeConfigureInfo.OtherRules = otherRulesInfo
	configureDetail.AttributeConfigure = attributeConfigureInfo

	var randomDesign models.RandomDesign
	err = tools.Database.Collection("random_design").FindOne(nil, match).Decode(&randomDesign)
	if err != nil && err != mongo.ErrNoDocuments {
		return configureDetail, err
	}

	//随机配置
	var randomConfigureInfo models.RandomConfigureInfo
	randomConfigureInfo.Title = transMap["configureReport.projectDetails.randomConfigure"]
	var randomInfo models.RandomInfo
	randomInfo.Title = transMap["configureReport.projectDetails.randomConfigure.randomDesign"]
	randomInfo.RandomTypeLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.randomType"]
	randomInfo.GroupLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.group"]
	randomInfo.RegionFactorLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.regionFactor"]
	randomInfo.FactorOptionLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.factorOption"]

	//randomInfo.FactorLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.factor"] + strconv.Itoa(len(factorInfoList)+1)
	randomInfo.FieldNumberLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.factor.fieldNumber"]
	randomInfo.FieldNameLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.factor.fieldName"]
	randomInfo.VariableLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.factor.variable"]
	randomInfo.ControlTypeLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.factor.controlType"]
	randomInfo.OptionLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.factor.option"]
	randomInfo.StatusLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.factor.status"]

	//随机类型
	if randomDesign.Info.Type == 1 {
		randomInfo.RandomType = transMap["configureReport.projectDetails.randomConfigure.randomDesign.randomType.region"]
	} else if randomDesign.Info.Type == 2 {
		randomInfo.RandomType = transMap["configureReport.projectDetails.randomConfigure.randomDesign.randomType.min"]
	}
	//组别名称
	//randomGroupList := make([]string, 0)
	//if randomDesign.Info.Groups != nil && len(randomDesign.Info.Groups) > 0 {
	//	for _, group := range randomDesign.Info.Groups {
	//		if group.SubGroup != nil && len(group.SubGroup) > 0 {
	//			for _, sub := range group.SubGroup {
	//				randomGroupList = append(randomGroupList, group.Name+"("+sub.Name+")")
	//			}
	//		} else {
	//			randomGroupList = append(randomGroupList, group.Name)
	//		}
	//	}
	//} else {
	//	randomGroupList = append(randomGroupList, "-")
	//}
	groups := models.RandomDesignToGroupSub(randomDesign, isBlindedRole, attribute)
	randomInfo.Group = groups
	//地区分层
	if attribute.AttributeInfo.CountryLayered {
		randomInfo.RegionFactor = transMap["configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.country"]
	} else if attribute.AttributeInfo.InstituteLayered {
		randomInfo.RegionFactor = transMap["configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.site"]
	} else if attribute.AttributeInfo.RegionLayered {
		regionList := make([]models.Region, 0)
		cursor, err := tools.Database.Collection("region").Find(nil, bson.M{"project_id": projectOID, "env_id": envOID, "deleted": false})
		if err != nil {
			return configureDetail, err
		}
		err = cursor.All(nil, &regionList)
		if err != nil {
			return configureDetail, err
		}
		if regionList != nil && len(regionList) > 0 {
			regionNameList := make([]string, 0)
			for _, region := range regionList {
				regionNameList = append(regionNameList, region.Name)
			}
			randomInfo.RegionFactor = transMap["configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.region"] +
				"(" + strings.Join(regionNameList, ",") + ")"
		} else {
			randomInfo.RegionFactor = transMap["configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.region"]
		}
	} else {
		randomInfo.RegionFactor = ""
	}

	//分层因素
	factorInfoList := make([]models.FactorInfo, 0)
	var stratificationCalculationInfo models.StratificationCalculationInfo
	stratificationCalculationInfo.Title = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation"]
	stratificationCalculationInfo.FieldNumberLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.fieldNumber"]
	stratificationCalculationInfo.FormulaTypeLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType"]
	stratificationCalculationInfo.CustomFormulaLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.customFormula"]
	stratificationCalculationInfo.RetainDecimalsLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.retainDecimals"]
	stratificationCalculationInfo.LayeredNameLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.layeredName"]
	stratificationCalculationInfo.LayeredOptionLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.layeredOption"]
	stratificationCalculationInfo.StatusLabel = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status"]
	fieldList := make([]models.StratificationCalculationField, 0)

	if randomDesign.Info.Factors != nil && len(randomDesign.Info.Factors) > 0 {
		for _, factor := range randomDesign.Info.Factors {
			if factor.IsCalc {
				var field models.StratificationCalculationField
				field.FieldNumber = factor.Number
				if *factor.CalcType == 0 {
					field.FormulaType = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType.age"]
				} else if *factor.CalcType == 1 {
					field.FormulaType = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType.bmi"]
				}
				field.CustomFormula = factor.CustomFormulas
				if factor.Precision != nil {
					field.RetainDecimals = strconv.Itoa(*factor.Precision)
				}
				field.LayeredName = factor.Label
				optionList := make([]string, 0)
				if factor.Options != nil && len(factor.Options) > 0 {
					for _, option := range factor.Options {
						optionList = append(optionList, *option.Formula+"-"+option.Label)
					}
				}
				field.LayeredOption = optionList

				if factor.Status != nil {
					if *factor.Status == 1 {
						field.Status = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.valid"]
					} else if *factor.Status == 2 {
						field.Status = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.invalid"]
					}
				} else {
					field.Status = transMap["configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.valid"]
				}
				fieldList = append(fieldList, field)

			} else {
				//分层因素
				var factorInfo models.FactorInfo
				factorInfo.FieldNumber = factor.Number
				factorInfo.FieldName = factor.Label
				factorInfo.Variable = factor.Name
				factorInfo.ControlType = translateTypeString(ctx, factor.Type, transMap)
				optionList := make([]string, 0)
				if factor.Options != nil && len(factor.Options) > 0 {
					for _, option := range factor.Options {
						optionList = append(optionList, option.Label)
					}
				}
				factorInfo.Option = optionList
				if factor.Status != nil {
					if *factor.Status == 1 {
						factorInfo.Status = transMap["configureReport.projectDetails.randomConfigure.randomDesign.factor.status.valid"]
					} else if *factor.Status == 2 {
						factorInfo.Status = transMap["configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"]
					}
				} else {
					factorInfo.Status = transMap["configureReport.projectDetails.randomConfigure.randomDesign.factor.status.valid"]
				}
				factorInfoList = append(factorInfoList, factorInfo)
			}
		}
	}

	if attribute.AttributeInfo.Random {
		if len(randomInfo.RandomType) > 0 || (randomInfo.Group != nil && len(randomInfo.Group) > 0) || len(randomInfo.RegionFactor) > 0 || (factorInfoList != nil && len(factorInfoList) > 0) ||
			(fieldList != nil && len(fieldList) > 0) {
			if len(randomInfo.RandomType) > 0 || (randomInfo.Group != nil && len(randomInfo.Group) > 0) || len(randomInfo.RegionFactor) > 0 || (factorInfoList != nil && len(factorInfoList) > 0) {
				randomConfigureInfo.Level = ".2"
				randomInfo.Level = ".1"
				//if (bimFieldList != nil && len(bimFieldList) > 0) || (ageFieldList != nil && len(ageFieldList) > 0) {
				if fieldList != nil && len(fieldList) > 0 {
					stratificationCalculationInfo.Level = ".2"
					//if bimFieldList != nil && len(bimFieldList) > 0 {
					//	bmiInfo.Level = "1."
					//	if ageFieldList != nil && len(ageFieldList) > 0 {
					//		ageInfo.Level = "2."
					//	}
					//} else {
					//	bmiInfo.Level = ""
					//	if ageFieldList != nil && len(ageFieldList) > 0 {
					//		ageInfo.Level = "1."
					//	}
					//}
				} else {
					stratificationCalculationInfo.Level = ""
					//bmiInfo.Level = ""
					//ageInfo.Level = ""
				}
			} else {
				randomInfo.Level = ""
				//if (bimFieldList != nil && len(bimFieldList) > 0) || (ageFieldList != nil && len(ageFieldList) > 0) {
				if fieldList != nil && len(fieldList) > 0 {
					randomConfigureInfo.Level = ".2"
					stratificationCalculationInfo.Level = ".1"
					//if bimFieldList != nil && len(bimFieldList) > 0 {
					//	bmiInfo.Level = "1."
					//	if ageFieldList != nil && len(ageFieldList) > 0 {
					//		ageInfo.Level = "2."
					//	}
					//} else {
					//	bmiInfo.Level = ""
					//	if ageFieldList != nil && len(ageFieldList) > 0 {
					//		ageInfo.Level = "1."
					//	}
					//}
				} else {
					stratificationCalculationInfo.Level = ""
					//bmiInfo.Level = ""
					//ageInfo.Level = ""
				}
			}
		} else {
			randomConfigureInfo.Level = ""
		}
	} else {
		randomConfigureInfo.Level = ""
	}

	randomInfo.FactorList = factorInfoList
	if factorInfoList != nil && len(factorInfoList) > 0 {
		//randomInfo.Length = strconv.Itoa(len(factorInfoList) + 1)
		var length = 1
		for _, factorInfo := range factorInfoList {
			if len(factorInfo.Option) > 0 {
				length += len(factorInfo.Option)
			} else {
				length += 1
			}
		}
		randomInfo.Length = strconv.Itoa(length)
	} else {
		randomInfo.Length = "2"
	}
	randomConfigureInfo.RandomDesign = randomInfo

	stratificationCalculationInfo.FieldList = fieldList
	//bmiInfo.FieldList = bimFieldList
	//stratificationCalculationInfo.Bmi = bmiInfo
	//ageInfo.FieldList = ageFieldList
	//stratificationCalculationInfo.Age = ageInfo
	randomConfigureInfo.StratificationCalculation = stratificationCalculationInfo

	configureDetail.RandomConfigure = randomConfigureInfo

	//表单配置
	var form models.Form
	err = tools.Database.Collection("form").FindOne(nil, match).Decode(&form)
	if err != nil && err != mongo.ErrNoDocuments {
		return configureDetail, err
	}
	var formConfigureInfo models.FormConfigureInfo
	formConfigureInfo.Title = transMap["configureReport.projectDetails.formConfigure"]
	//受试者登记
	var subjectRegistrationInfo models.SubjectRegistrationInfo
	subjectRegistrationInfo.Title = transMap["configureReport.projectDetails.formConfigure.subjectRegistration"]
	subjectRegistrationInfo.FieldNameLabel = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.fieldName"]
	subjectRegistrationInfo.IsEditableLabel = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.isEditable"]
	subjectRegistrationInfo.RequiredLabel = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.required"]
	subjectRegistrationInfo.VariableIdLabel = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.variableId"]
	subjectRegistrationInfo.ControlTypeLabel = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.controlType"]
	subjectRegistrationInfo.OptionLabel = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.option"]
	subjectRegistrationInfo.FormatTypeLabel = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.formatType"]
	subjectRegistrationInfo.VariableFormatLabel = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.variableFormat"]
	subjectRegistrationInfo.VariableRangeLabel = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.variableRange"]
	subjectRegistrationInfo.StatusLabel = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.status"]
	registrationFieldList := make([]models.RegistrationField, 0)

	//公式计算
	var customFormulaInfo models.CustomFormulaInfo
	customFormulaInfo.Title = transMap["configureReport.projectDetails.formConfigure.customFormula"]
	customFormulaInfo.FieldNameLabel = transMap["configureReport.projectDetails.formConfigure.customFormula.fieldName"]
	customFormulaInfo.RequiredLabel = transMap["configureReport.projectDetails.formConfigure.customFormula.required"]
	customFormulaInfo.VariableIdLabel = transMap["configureReport.projectDetails.formConfigure.customFormula.variableId"]
	customFormulaInfo.ControlTypeLabel = transMap["configureReport.projectDetails.formConfigure.customFormula.controlType"]
	customFormulaInfo.FormatTypeLabel = transMap["configureReport.projectDetails.formConfigure.customFormula.formatType"]
	customFormulaInfo.VariableFormatLabel = transMap["configureReport.projectDetails.formConfigure.customFormula.variableFormat"]
	customFormulaInfo.VariableRangeLabel = transMap["configureReport.projectDetails.formConfigure.customFormula.variableRange"]
	customFormulaInfo.StatusLabel = transMap["configureReport.projectDetails.formConfigure.customFormula.status"]
	formulaFieldList := make([]models.FormulaField, 0)

	//剂量调整
	var doseAdjustmentInfo models.DoseAdjustmentInfo
	doseAdjustmentInfo.Title = transMap["configureReport.projectDetails.formConfigure.doseAdjustment"]
	doseAdjustmentInfo.FieldNameLabel = transMap["configureReport.projectDetails.formConfigure.doseAdjustment.fieldName"]
	doseAdjustmentInfo.RequiredLabel = transMap["configureReport.projectDetails.formConfigure.doseAdjustment.required"]
	doseAdjustmentInfo.VariableIdLabel = transMap["configureReport.projectDetails.formConfigure.doseAdjustment.variableId"]
	doseAdjustmentInfo.ControlTypeLabel = transMap["configureReport.projectDetails.formConfigure.doseAdjustment.controlType"]
	doseAdjustmentInfo.OptionLabel = transMap["configureReport.projectDetails.formConfigure.doseAdjustment.option"]
	doseAdjustmentInfo.StatusLabel = transMap["configureReport.projectDetails.formConfigure.doseAdjustment.status"]
	doseFieldList := make([]models.DoseField, 0)

	//分层计算
	var layeredCalculationInfo models.LayeredCalculationInfo
	layeredCalculationInfo.Title = transMap["configureReport.projectDetails.formConfigure.layeredCalculation"]
	layeredCalculationInfo.FieldNameLabel = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.fieldName"]
	layeredCalculationInfo.IsEditableLabel = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.isEditable"]
	layeredCalculationInfo.RequiredLabel = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.required"]
	layeredCalculationInfo.VariableIdLabel = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.variableId"]
	layeredCalculationInfo.ControlTypeLabel = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.controlType"]
	layeredCalculationInfo.OptionLabel = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.option"]
	layeredCalculationInfo.FormatTypeLabel = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.formatType"]
	layeredCalculationInfo.VariableFormatLabel = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.variableFormat"]
	layeredCalculationInfo.VariableRangeLabel = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.variableRange"]
	layeredCalculationInfo.StatusLabel = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.status"]
	layeredFieldList := make([]models.LayeredField, 0)

	if form.Fields != nil && len(form.Fields) > 0 {
		for _, field := range form.Fields {
			if field.ApplicationType == nil || (field.ApplicationType != nil && *field.ApplicationType == 1) {
				//受试者登记
				var registrationField models.RegistrationField
				registrationField.FieldName = field.Label
				if field.Modifiable {
					registrationField.IsEditable = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.isEditable.true"]
				} else {
					registrationField.IsEditable = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.isEditable.false"]
				}
				if field.Required {
					registrationField.Required = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.required.true"]
				} else {
					registrationField.Required = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.required.false"]
				}
				registrationField.VariableId = field.Variable
				registrationField.ControlType = translateTypeString(ctx, field.Type, transMap)
				optionList := make([]string, 0)
				if field.Options != nil && len(field.Options) > 0 {
					for _, option := range field.Options {
						optionList = append(optionList, option.Label)
					}
				}
				registrationField.Option = optionList
				if len(field.FormatType) > 0 {
					registrationField.FormatType = translateTypeString(ctx, field.FormatType, transMap)
				} else if len(field.DateFormat) > 0 {
					registrationField.FormatType = translateTypeString(ctx, field.DateFormat, transMap)
				} else if len(field.TimeFormat) > 0 {
					registrationField.FormatType = translateTypeString(ctx, field.TimeFormat, transMap)
				}
				if field.Length != nil {
					if field.FormatType == "decimalLength" {
						//小数
						// 将 float64 转换为字符串，动态确定小数点后的位数
						stringLength := strconv.FormatFloat(*field.Length, 'f', -1, 64)
						// 如果有小数点
						if strings.Contains(stringLength, ".") {
							parts := strings.Split(stringLength, ".")
							// 去除小数部分末尾的0
							parts[1] = strings.TrimRight(parts[1], "0")
							// 如果小数部分末尾是空，则去除小数点
							if parts[1] == "" {
								stringLength = parts[0]
							} else {
								stringLength = strings.Join(parts, ".")
							}
						}
						registrationField.VariableFormat = stringLength
					} else if field.FormatType == "numberLength" {
						//整数
						registrationField.VariableFormat = strconv.Itoa(int(*field.Length))
					} else if field.FormatType == "characterLength" {
						// 文本
						registrationField.VariableFormat = strconv.Itoa(int(*field.Length))
					}
				}
				if field.Range != nil {
					min := ""
					if field.Range.Min != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMin := strconv.FormatFloat(*field.Range.Min, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(min, ".") {
								parts := strings.Split(min, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									min = parts[0]
								} else {
									min = strings.Join(parts, ".")
								}
							}
							min = stringMin
						} else if field.FormatType == "numberLength" {
							//整数
							min = strconv.Itoa(int(*field.Range.Min))
						}
					}
					max := ""
					if field.Range.Max != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMax := strconv.FormatFloat(*field.Range.Max, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(max, ".") {
								parts := strings.Split(max, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									max = parts[0]
								} else {
									max = strings.Join(parts, ".")
								}
							}
							max = stringMax
						} else if field.FormatType == "numberLength" {
							//整数
							max = strconv.Itoa(int(*field.Range.Max))
						}
					}
					registrationField.VariableRange = min + "—" + max
				}

				if field.DateRange != nil {
					maxDate := field.DateRange.Max
					if maxDate == "currentTime" {
						maxDate = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.currentTime"]
					}
					registrationField.VariableRange = field.DateRange.Min + "—" + maxDate
				}

				if field.Status != nil {
					if *field.Status == 1 {
						registrationField.Status = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.status.valid"]
					} else if *field.Status == 2 {
						registrationField.Status = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.status.invalid"]
					}
				} else {
					registrationField.Status = transMap["configureReport.projectDetails.formConfigure.subjectRegistration.status.valid"]
				}
				registrationFieldList = append(registrationFieldList, registrationField)

			} else if *field.ApplicationType == 2 {
				//公式计算
				var formulaField models.FormulaField
				formulaField.FieldName = field.Label
				if field.Required {
					formulaField.Required = transMap["configureReport.projectDetails.formConfigure.customFormula.required.true"]
				} else {
					formulaField.Required = transMap["configureReport.projectDetails.formConfigure.customFormula.required.false"]
				}
				formulaField.VariableId = field.Variable
				formulaField.ControlType = translateTypeString(ctx, field.Type, transMap)
				if len(field.FormatType) > 0 {
					formulaField.FormatType = translateTypeString(ctx, field.FormatType, transMap)
				} else if len(field.DateFormat) > 0 {
					formulaField.FormatType = translateTypeString(ctx, field.DateFormat, transMap)
				} else if len(field.TimeFormat) > 0 {
					formulaField.FormatType = translateTypeString(ctx, field.TimeFormat, transMap)
				}
				if field.Length != nil {
					if field.FormatType == "decimalLength" {
						//小数
						// 将 float64 转换为字符串，动态确定小数点后的位数
						stringLength := strconv.FormatFloat(*field.Length, 'f', -1, 64)
						// 如果有小数点
						if strings.Contains(stringLength, ".") {
							parts := strings.Split(stringLength, ".")
							// 去除小数部分末尾的0
							parts[1] = strings.TrimRight(parts[1], "0")
							// 如果小数部分末尾是空，则去除小数点
							if parts[1] == "" {
								stringLength = parts[0]
							} else {
								stringLength = strings.Join(parts, ".")
							}
						}
						formulaField.VariableFormat = stringLength
					} else if field.FormatType == "numberLength" {
						//整数
						formulaField.VariableFormat = strconv.Itoa(int(*field.Length))
					}
				}
				if field.Range != nil {
					min := ""
					if field.Range.Min != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMin := strconv.FormatFloat(*field.Range.Min, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(stringMin, ".") {
								parts := strings.Split(stringMin, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									stringMin = parts[0]
								} else {
									stringMin = strings.Join(parts, ".")
								}
							}
							min = stringMin
						} else if field.FormatType == "numberLength" {
							//整数
							min = strconv.Itoa(int(*field.Range.Min))
						}
					}
					max := ""
					if field.Range.Max != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMax := strconv.FormatFloat(*field.Range.Max, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(max, ".") {
								parts := strings.Split(max, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									max = parts[0]
								} else {
									max = strings.Join(parts, ".")
								}
							}
							max = stringMax
						} else if field.FormatType == "numberLength" {
							//整数
							max = strconv.Itoa(int(*field.Range.Max))
						}
					}
					formulaField.VariableRange = min + "—" + max
				}
				if field.Status != nil {
					if *field.Status == 1 {
						formulaField.Status = transMap["configureReport.projectDetails.formConfigure.customFormula.status.valid"]
					} else if *field.Status == 2 {
						formulaField.Status = transMap["configureReport.projectDetails.formConfigure.customFormula.status.invalid"]
					}
				} else {
					formulaField.Status = transMap["configureReport.projectDetails.formConfigure.customFormula.status.valid"]
				}
				formulaFieldList = append(formulaFieldList, formulaField)

			} else if *field.ApplicationType == 3 {
				//剂量调整
				var doseField models.DoseField
				doseField.FieldName = field.Label
				if field.Required {
					doseField.Required = transMap["configureReport.projectDetails.formConfigure.doseAdjustment.required.true"]
				} else {
					doseField.Required = transMap["configureReport.projectDetails.formConfigure.doseAdjustment.required.false"]
				}
				doseField.VariableId = field.Variable
				doseField.ControlType = translateTypeString(ctx, field.Type, transMap)
				optionList := make([]string, 0)
				if field.Options != nil && len(field.Options) > 0 {
					for _, option := range field.Options {
						optionLabel := option.Label
						if option.Label == "form.control.type.options.one" {
							optionLabel = transMap["form.control.type.options.one"]
						} else if option.Label == "form.control.type.options.two" {
							optionLabel = transMap["form.control.type.options.two"]
						} else if option.Label == "form.control.type.options.three" {
							optionLabel = transMap["form.control.type.options.three"]
						} else {
						}
						optionList = append(optionList, optionLabel)
					}
				}
				doseField.Option = optionList
				if field.Status != nil {
					if *field.Status == 1 {
						doseField.Status = transMap["configureReport.projectDetails.formConfigure.doseAdjustment.status.valid"]
					} else if *field.Status == 2 {
						doseField.Status = transMap["configureReport.projectDetails.formConfigure.doseAdjustment.status.invalid"]
					}
				} else {
					doseField.Status = transMap["configureReport.projectDetails.formConfigure.doseAdjustment.status.valid"]
				}
				doseFieldList = append(doseFieldList, doseField)
			} else if *field.ApplicationType == 4 {
				//分层计算
				var layeredField models.LayeredField
				layeredField.FieldName = field.Label
				if field.Modifiable {
					layeredField.IsEditable = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.isEditable.true"]
				} else {
					layeredField.IsEditable = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.isEditable.false"]
				}
				if field.Required {
					layeredField.Required = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.required.true"]
				} else {
					layeredField.Required = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.required.false"]
				}
				layeredField.VariableId = field.Variable
				layeredField.ControlType = translateTypeString(ctx, field.Type, transMap)
				optionList := make([]string, 0)
				if field.Options != nil && len(field.Options) > 0 {
					for _, option := range field.Options {
						optionList = append(optionList, option.Label)
					}
				}
				layeredField.Option = optionList
				if len(field.FormatType) > 0 {
					layeredField.FormatType = translateTypeString(ctx, field.FormatType, transMap)
				} else if len(field.DateFormat) > 0 {
					layeredField.FormatType = translateTypeString(ctx, field.DateFormat, transMap)
				} else if len(field.TimeFormat) > 0 {
					layeredField.FormatType = translateTypeString(ctx, field.TimeFormat, transMap)
				}
				if field.Length != nil {
					if field.FormatType == "decimalLength" {
						//小数
						// 将 float64 转换为字符串，动态确定小数点后的位数
						stringLength := strconv.FormatFloat(*field.Length, 'f', -1, 64)
						// 如果有小数点
						if strings.Contains(stringLength, ".") {
							parts := strings.Split(stringLength, ".")
							// 去除小数部分末尾的0
							parts[1] = strings.TrimRight(parts[1], "0")
							// 如果小数部分末尾是空，则去除小数点
							if parts[1] == "" {
								stringLength = parts[0]
							} else {
								stringLength = strings.Join(parts, ".")
							}
						}
						layeredField.VariableFormat = stringLength
					} else if field.FormatType == "numberLength" {
						//整数
						layeredField.VariableFormat = strconv.Itoa(int(*field.Length))
					}
				}
				if field.Range != nil {
					min := ""
					if field.Range.Min != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMin := strconv.FormatFloat(*field.Range.Min, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(min, ".") {
								parts := strings.Split(min, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									min = parts[0]
								} else {
									min = strings.Join(parts, ".")
								}
							}
							min = stringMin
						} else if field.FormatType == "numberLength" {
							//整数
							min = strconv.Itoa(int(*field.Range.Min))
						}
					}
					max := ""
					if field.Range.Max != nil {
						if field.FormatType == "decimalLength" {
							//小数
							// 将 float64 转换为字符串，动态确定小数点后的位数
							stringMax := strconv.FormatFloat(*field.Range.Max, 'f', -1, 64)
							// 如果有小数点
							if strings.Contains(max, ".") {
								parts := strings.Split(max, ".")
								// 去除小数部分末尾的0
								parts[1] = strings.TrimRight(parts[1], "0")
								// 如果小数部分末尾是空，则去除小数点
								if parts[1] == "" {
									max = parts[0]
								} else {
									max = strings.Join(parts, ".")
								}
							}
							max = stringMax
						} else if field.FormatType == "numberLength" {
							//整数
							max = strconv.Itoa(int(*field.Range.Max))
						}
					}
					layeredField.VariableRange = min + "—" + max
				}

				if field.DateRange != nil {
					maxDate := field.DateRange.Max
					if maxDate == "currentTime" {
						maxDate = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.currentTime"]
					}
					layeredField.VariableRange = field.DateRange.Min + "—" + maxDate
				}

				if field.Status != nil {
					if *field.Status == 1 {
						layeredField.Status = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.status.valid"]
					} else if *field.Status == 2 {
						layeredField.Status = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.status.invalid"]
					}
				} else {
					layeredField.Status = transMap["configureReport.projectDetails.formConfigure.layeredCalculation.status.valid"]
				}
				layeredFieldList = append(layeredFieldList, layeredField)
			}
		}
	}

	if len(randomConfigureInfo.Level) > 0 {
		//随机
		if (registrationFieldList != nil && len(registrationFieldList) > 0) || (formulaFieldList != nil && len(formulaFieldList) > 0) || (doseFieldList != nil && len(doseFieldList) > 0) || (layeredFieldList != nil && len(layeredFieldList) > 0) {
			formConfigureInfo.Level = ".3"
			if registrationFieldList != nil && len(registrationFieldList) > 0 {
				//受试者登记
				subjectRegistrationInfo.Level = "1."
				if formulaFieldList != nil && len(formulaFieldList) > 0 {
					//公式计算
					customFormulaInfo.Level = "2."
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "3."
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "4."
						} else {
							layeredCalculationInfo.Level = ""
						}
					} else {
						doseAdjustmentInfo.Level = ""
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "3."
						} else {
							layeredCalculationInfo.Level = ""
						}
					}
				} else {
					customFormulaInfo.Level = ""
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "2."
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "3."
						} else {
							layeredCalculationInfo.Level = ""
						}
					} else {
						doseAdjustmentInfo.Level = ""
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "2."
						} else {
							layeredCalculationInfo.Level = ""
						}
					}
				}
			} else {
				subjectRegistrationInfo.Level = ""
				if formulaFieldList != nil && len(formulaFieldList) > 0 {
					//公式计算
					customFormulaInfo.Level = "1."
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "2."
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "3."
						} else {
							layeredCalculationInfo.Level = ""
						}
					} else {
						doseAdjustmentInfo.Level = ""
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "2."
						} else {
							layeredCalculationInfo.Level = ""
						}
					}
				} else {
					customFormulaInfo.Level = ""
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "1."
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "2."
						} else {
							layeredCalculationInfo.Level = ""
						}
					} else {
						doseAdjustmentInfo.Level = ""
						if layeredFieldList != nil && len(layeredFieldList) > 0 {
							//分层计算
							layeredCalculationInfo.Level = "1."
						} else {
							layeredCalculationInfo.Level = ""
						}
					}
				}
			}

		} else {
			formConfigureInfo.Level = ""
		}

	} else {
		//不随机
		if (registrationFieldList != nil && len(registrationFieldList) > 0) || (formulaFieldList != nil && len(formulaFieldList) > 0) || (doseFieldList != nil && len(doseFieldList) > 0) {
			formConfigureInfo.Level = ".2"
			if registrationFieldList != nil && len(registrationFieldList) > 0 {
				//受试者登记
				subjectRegistrationInfo.Level = "1."
				if formulaFieldList != nil && len(formulaFieldList) > 0 {
					//公式计算
					customFormulaInfo.Level = "2."
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "3."
					} else {
						doseAdjustmentInfo.Level = ""
					}
				} else {
					customFormulaInfo.Level = ""
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "2."
					} else {
						doseAdjustmentInfo.Level = ""
					}
				}
			} else {
				subjectRegistrationInfo.Level = ""
				if formulaFieldList != nil && len(formulaFieldList) > 0 {
					//公式计算
					customFormulaInfo.Level = "1."
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "2."
					} else {
						doseAdjustmentInfo.Level = ""
					}
				} else {
					customFormulaInfo.Level = ""
					if doseFieldList != nil && len(doseFieldList) > 0 {
						//剂量调整
						doseAdjustmentInfo.Level = "1."
					} else {
						doseAdjustmentInfo.Level = ""
					}
				}
			}

		} else {
			formConfigureInfo.Level = ""
		}
	}

	subjectRegistrationInfo.FieldList = registrationFieldList
	formConfigureInfo.SubjectRegistration = subjectRegistrationInfo

	customFormulaInfo.FieldList = formulaFieldList
	formConfigureInfo.CustomFormula = customFormulaInfo

	doseAdjustmentInfo.FieldList = doseFieldList
	formConfigureInfo.DoseAdjustment = doseAdjustmentInfo

	layeredCalculationInfo.FieldList = layeredFieldList
	formConfigureInfo.LayeredCalculation = layeredCalculationInfo

	configureDetail.FormConfigure = formConfigureInfo

	//研究产品管理
	var visitCycle models.VisitCycle
	err = tools.Database.Collection("visit_cycle").FindOne(nil, match).Decode(&visitCycle)
	if err != nil && err != mongo.ErrNoDocuments {
		return configureDetail, err
	}
	var ipManagementInfo models.IpManagementInfo
	ipManagementInfo.Title = transMap["configureReport.projectDetails.ipManagement"]

	//访视管理
	var visitManagementInfo models.VisitManagementInfo
	visitManagementInfo.Title = transMap["configureReport.projectDetails.ipManagement.visitManagement"]
	visitManagementInfo.CycleVersionLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.cycleVersion"]
	visitManagementInfo.VisitOffsetTypeLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.visitOffsetType"]
	visitManagementInfo.CycleVersion = visitCycle.Version
	if visitCycle.ConfigInfo.VisitType == 0 {
		visitManagementInfo.VisitOffsetType = "baseline"
	} else if visitCycle.ConfigInfo.VisitType == 1 {
		visitManagementInfo.VisitOffsetType = "lastdate"
	}
	visitManagementInfo.VisitNumberLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.visitNumber"]
	visitManagementInfo.VisitNameLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.visitName"]
	visitManagementInfo.GroupLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.group"]
	visitManagementInfo.IntervalDurationLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.intervalDuration"]
	visitManagementInfo.WindowLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.window"]
	visitManagementInfo.IsDispenseLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.isDispense"]
	visitManagementInfo.IsRandomizeLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.isRandomize"]
	visitManagementInfo.IsDTPLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.isDTP"]
	visitManagementInfo.IsSubjectReplaceLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.isSubjectReplace"]
	visitManagementInfo.IsDoseAdjustmentLabel = transMap["configureReport.projectDetails.ipManagement.visitManagement.isDoseAdjustment"]
	visitDetailsList := make([]models.VisitDetails, 0)
	if visitCycle.Infos != nil && len(visitCycle.Infos) > 0 {
		for _, info := range visitCycle.Infos {
			var visitDetails models.VisitDetails
			visitDetails.VisitNumber = info.Number
			visitDetails.VisitName = info.Name
			groupList := make([]string, 0)
			if info.Group != nil && len(info.Group) > 0 {
				for _, group := range info.Group {
					if isBlindedRole && attribute.AttributeInfo.Blind {
						groupList = append(groupList, models.BlindData)
					} else {
						groupList = append(groupList, group.(string))
					}
				}
			}
			visitDetails.Group = strings.Join(groupList, ",")
			if info.Interval != nil {
				visitDetails.IntervalDuration = strconv.Itoa(*info.Interval)
			} else {
				visitDetails.IntervalDuration = "-"
			}
			min := ""
			if info.PeriodMin != nil {
				min = strconv.Itoa(int(*info.PeriodMin))
			}
			max := ""
			if info.PeriodMax != nil {
				max = strconv.Itoa(int(*info.PeriodMax))
			}
			visitDetails.Window = min + "~" + max
			if info.Dispensing {
				visitDetails.IsDispense = "√"
			} else {
				visitDetails.IsDispense = "×"
			}
			if info.Random {
				visitDetails.IsRandomize = "√"
			} else {
				visitDetails.IsRandomize = "×"
			}
			if info.DTP {
				visitDetails.IsDTP = "√"
			} else {
				visitDetails.IsDTP = "×"
			}
			if info.Replace {
				visitDetails.IsSubjectReplace = "√"
			} else {
				visitDetails.IsSubjectReplace = "×"
			}
			if info.DoseAdjustment {
				visitDetails.IsDoseAdjustment = "√"
			} else {
				visitDetails.IsDoseAdjustment = "×"
			}
			visitDetailsList = append(visitDetailsList, visitDetails)
		}
	}

	//研究产品配置
	var drugConfigure models.DrugConfigure
	err = tools.Database.Collection("drug_configure").FindOne(nil, match).Decode(&drugConfigure)
	if err != nil && err != mongo.ErrNoDocuments {
		return configureDetail, errors.WithStack(err)
	}

	// 研究产品配置-设置
	var drugConfigureSetting models.DrugConfigureSetting
	err = tools.Database.Collection("drug_configure_setting").FindOne(nil, match).Decode(&drugConfigureSetting)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return configureDetail, errors.WithStack(err)
	}

	var treatmentDesignInfo models.TreatmentDesignInfo
	treatmentDesignInfo.Title = transMap["configureReport.projectDetails.ipManagement.treatmentDesign"]

	isBlindDrugMap, err := tools.IsBlindDrugMap(envOID)
	if err != nil {
		return configureDetail, errors.WithStack(err)
	}

	isOtherDrugMap, err := tools.IsOtherDrugMap(envOID)
	if err != nil {
		return configureDetail, errors.WithStack(err)
	}

	//DTP研究产品
	var dtpIpInfo models.DTPIpInfo
	dtpIpInfo.Title = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp"]
	dtpIpInfo.IpLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.ip"]
	dtpIpInfo.DTPModeLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.dtpMode"]
	dtpIpInfo.TakeCare = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.takeCare"]
	dtpIpFieldList := make([]models.DTPIpField, 0)

	//按标签/开放配置
	var labelOpenInfo models.LabelOpenInfo
	labelOpenInfo.Title = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen"]
	labelOpenInfo.GroupLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.group"]
	labelOpenInfo.VisitNameLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.visitName"]
	labelOpenInfo.IpNameLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.IpName"]
	labelOpenInfo.DispensationQuantityLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.dispensationQuantity"]
	labelOpenInfo.CustomFormulaLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.customFormula"]
	labelOpenInfo.CombinedDispensationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.combinedDispensation"]
	labelOpenInfo.IpSpecificationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.ipSpecification"]
	labelOpenInfo.SpecificationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.specification"]
	labelOpenInfo.AutomaticAssignmentLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.automaticAssignment"]
	labelOpenInfo.CalculationUnitLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.calculationUnit"]
	labelOpenInfo.TakeCare = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.takeCare"]
	labelOpenFieldList := make([]models.LabelOpenField, 0)

	//按公式计算
	var formulaConfigInfo models.FormulaConfigInfo
	formulaConfigInfo.Title = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig"]

	//年龄
	var formulaAgeInfo models.FormulaAgeInfo
	formulaAgeInfo.Title = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge"]
	formulaAgeInfo.GroupLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.group"]
	formulaAgeInfo.VisitNameLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.visitName"]
	formulaAgeInfo.IpNameLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.IpName"]
	formulaAgeInfo.IpSpecificationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.ipSpecification"]
	formulaAgeInfo.AgeRangeLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.ageRange"]
	formulaAgeInfo.DispensationQuantityLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.dispensationQuantity"]
	formulaAgeInfo.IsOpenIpLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.isOpenIp"]
	formulaAgeInfo.KeepDecimalPlacesLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.keepDecimalPlaces"]
	formulaAgeInfo.TakeCare = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.takeCare"]
	formulaAgeFieldList := make([]models.FormulaAgeField, 0)

	//体重
	var formulaWeightInfo models.FormulaWeightInfo
	formulaWeightInfo.Title = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight"]
	formulaWeightInfo.GroupLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.group"]
	formulaWeightInfo.VisitNameLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.visitName"]
	formulaWeightInfo.IpNameLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.IpName"]
	formulaWeightInfo.IpSpecificationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.ipSpecification"]
	formulaWeightInfo.WeightRangeLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.weightRange"]
	formulaWeightInfo.DispensationQuantityLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.dispensationQuantity"]
	formulaWeightInfo.IsOpenIpLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.isOpenIp"]
	formulaWeightInfo.KeepDecimalPlacesLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.keepDecimalPlaces"]
	formulaWeightInfo.WeightComparisonCalculationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.weightComparisonCalculation"]
	formulaWeightInfo.ComparedWithLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.comparedWith"]
	formulaWeightInfo.ChangeLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.change"]
	formulaWeightInfo.CalculationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.calculation"]
	formulaWeightInfo.TakeCare = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.takeCare"]
	formulaWeightFieldList := make([]models.FormulaWeightField, 0)

	//简易体表面积BSA
	var formulaSimpleBSAInfo models.FormulaSimpleBSAInfo
	formulaSimpleBSAInfo.Title = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA"]
	formulaSimpleBSAInfo.GroupLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.group"]
	formulaSimpleBSAInfo.VisitNameLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.visitName"]
	formulaSimpleBSAInfo.IpNameLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.IpName"]
	formulaSimpleBSAInfo.IpSpecificationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.ipSpecification"]
	formulaSimpleBSAInfo.UnitCapacityLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.unitCapacity"]
	formulaSimpleBSAInfo.UnitCalculationStandardLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.unitCalculationStandard"]
	formulaSimpleBSAInfo.IsOpenIpLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.isOpenIp"]
	formulaSimpleBSAInfo.WeightComparisonCalculationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.weightComparisonCalculation"]
	formulaSimpleBSAInfo.ComparedWithLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.comparedWith"]
	formulaSimpleBSAInfo.ChangeLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.change"]
	formulaSimpleBSAInfo.CalculationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.calculation"]
	formulaSimpleBSAInfo.TakeCare = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.takeCare"]
	formulaSimpleBSAFieldList := make([]models.FormulaSimpleBSAField, 0)

	//其他体表面积(按自定义公式)
	var formulaOtherBSAInfo models.FormulaOtherBSAInfo
	formulaOtherBSAInfo.Title = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA"]
	formulaOtherBSAInfo.GroupLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.group"]
	formulaOtherBSAInfo.VisitNameLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.visitName"]
	formulaOtherBSAInfo.IpNameLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.IpName"]
	formulaOtherBSAInfo.IpSpecificationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.ipSpecification"]
	formulaOtherBSAInfo.UnitCapacityLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.unitCapacity"]
	formulaOtherBSAInfo.UnitCalculationStandardLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.unitCalculationStandard"]
	formulaOtherBSAInfo.IsOpenIpLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.isOpenIp"]
	formulaOtherBSAInfo.WeightComparisonCalculationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.weightComparisonCalculation"]
	formulaOtherBSAInfo.ComparedWithLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.comparedWith"]
	formulaOtherBSAInfo.ChangeLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.change"]
	formulaOtherBSAInfo.CalculationLabel = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.calculation"]
	formulaOtherBSAFieldList := make([]models.FormulaOtherBSAField, 0)

	// DTP研究产品
	if attribute.AttributeInfo.DtpRule == 1 && drugConfigureSetting.DtpIpList != nil && len(drugConfigureSetting.DtpIpList) > 0 {
		dtpTypeMap := map[int]string{
			1: transMap["configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.site"],
			2: transMap["configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.siteSubject"],
			3: transMap["configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.depotSubject"],
		}

		for _, dtpIp := range drugConfigureSetting.DtpIpList {
			var dtpIpField models.DTPIpField
			dtpIpField.IpName = dtpIp.IP

			if isBlindedRole && isBlindDrugMap[dtpIp.IP] {
				dtpIpField.IpName = tools.BlindData
			} else {
				name := dtpIp.IP
				if isOtherDrugMap[dtpIp.IP] {
					name = dtpIp.IP
					dtpIpField.IsOtherDrug = true
				}
				dtpIpField.IpName = name
			}

			var dtpTypeList []string
			for _, dtpType := range dtpIp.DtpTypeList {
				dtpTypeList = append(dtpTypeList, dtpTypeMap[dtpType])
			}
			dtpIpField.DTPTypeList = dtpTypeList

			dtpIpFieldList = append(dtpIpFieldList, dtpIpField)
		}
	}

	if drugConfigure.Configures != nil && len(drugConfigure.Configures) > 0 {
		for _, configure := range drugConfigure.Configures {

			group := ""
			if len(configure.Group) > 0 {
				if isBlindedRole && attribute.AttributeInfo.Blind {
					group = models.BlindData
				} else {
					group = configure.Group
				}
			}
			visitNameList := make([]string, 0)
			if configure.VisitCycles != nil && len(configure.VisitCycles) > 0 {
				for _, cycle := range configure.VisitCycles {
					if visitCycle.Infos != nil && len(visitCycle.Infos) > 0 {
						for _, info := range visitCycle.Infos {
							if cycle == info.ID {
								visitNameList = append(visitNameList, info.Name)
							}
						}
					}
					if &visitCycle.SetInfo != nil && visitCycle.SetInfo.IsOpen {
						if cycle == visitCycle.SetInfo.Id {
							lang := ctx.Request.Header.Get("Accept-Language")
							if lang == "zh" {
								visitNameList = append(visitNameList, visitCycle.SetInfo.NameZh)
							} else if lang == "en" {
								visitNameList = append(visitNameList, visitCycle.SetInfo.NameEn)
							}
						}
					}
				}
			}

			if configure.OpenSetting == 1 || configure.OpenSetting == 2 {
				//按标签/开放配置
				if configure.Values != nil && len(configure.Values) > 0 {
					for _, value := range configure.Values {
						var labelOpenField models.LabelOpenField
						labelOpenField.Group = group
						labelOpenField.VisitName = strings.Join(visitNameList, ",")
						if isBlindedRole && isBlindDrugMap[value.DrugName] {
							labelOpenField.IpName = tools.BlindData
							labelOpenField.DispensationQuantity = tools.BlindData
							labelOpenField.IpSpecification = tools.BlindData

						} else {
							name := value.DrugName
							if value.IsOther {
								name = value.DrugName
								labelOpenField.IsOtherDrug = true
							}
							labelOpenField.IpName = name
							if len(value.CustomDispensingNumber) > 0 {
								labelOpenField.DispensationQuantity = value.CustomDispensingNumber
							} else {
								labelOpenField.DispensationQuantity = strconv.Itoa(value.DispensingNumber)
							}
							labelOpenField.IpSpecification = value.DrugSpec

						}
						if configure.IsFormula {
							labelOpenField.CustomFormula = configure.CustomerCalculation
							labelOpenField.Specification = configure.CustomerCalculationSpec
						}
						labelList := []string{}

						if value.Label != "" {
							labelList = append(labelList, value.Label)
						}
						if configure.Label != "" {
							labelList = append(labelList, configure.Label)
						}

						labelOpenField.CombinedDispensation = strings.Join(labelList, ",")

						if value.AutomaticRecode {
							labelOpenField.AutomaticAssignment = "√"
							labelOpenField.CalculationUnit = strconv.Itoa(int(*value.AutomaticRecodeSpec))
						} else {
							labelOpenField.AutomaticAssignment = "×"
						}
						labelOpenFieldList = append(labelOpenFieldList, labelOpenField)
					}
				}

			} else if configure.OpenSetting == 3 {
				//按公式计算
				if configure.CalculationType == 1 {
					//年龄
					if configure.Values != nil && len(configure.Values) > 0 {
						for _, value := range configure.Values {
							var formulaAgeField models.FormulaAgeField

							formulaAgeField.Group = group
							formulaAgeField.VisitName = strings.Join(visitNameList, ",")
							if isBlindedRole && isBlindDrugMap[value.DrugName] {
								formulaAgeField.IpName = tools.BlindData
								formulaAgeField.IpSpecification = tools.BlindData

							} else {
								name := value.DrugName
								if value.IsOther {
									name = value.DrugName
									formulaAgeField.IsOtherDrug = true
								}
								formulaAgeField.IpName = name
								formulaAgeField.IpSpecification = value.DrugSpec

							}
							formulaExpressionList := make([]string, 0)
							formulaValueList := make([]string, 0)
							if value.Formulas != nil && len(value.Formulas) > 0 {
								for _, formula := range value.Formulas {
									formulaExpressionList = append(formulaExpressionList, formula.Expression)
									formulaValueList = append(formulaValueList, strconv.Itoa(formula.Value))
								}
							}
							formulaAgeField.AgeRange = formulaExpressionList
							formulaAgeField.DispensationQuantity = formulaValueList
							if value.IsOpen {
								formulaAgeField.IsOpenIp = "√"
							} else {
								formulaAgeField.IsOpenIp = "×"
							}
							if value.CalculationInfo.KeepDecimal {
								if value.CalculationInfo.Precision != nil {
									formulaAgeField.KeepDecimalPlaces = strconv.Itoa(int(*value.CalculationInfo.Precision))
								}
							}

							formulaAgeFieldList = append(formulaAgeFieldList, formulaAgeField)
						}
					}

				} else if configure.CalculationType == 2 {
					//体重
					if configure.Values != nil && len(configure.Values) > 0 {
						for _, value := range configure.Values {
							var formulaWeightField models.FormulaWeightField
							formulaWeightField.Group = group
							formulaWeightField.VisitName = strings.Join(visitNameList, ",")
							if isBlindedRole && isBlindDrugMap[value.DrugName] {
								formulaWeightField.IpName = tools.BlindData
								formulaWeightField.IpSpecification = tools.BlindData

							} else {
								name := value.DrugName
								if value.IsOther {
									name = value.DrugName
									formulaWeightField.IsOtherDrug = true
								}
								formulaWeightField.IpName = name
								formulaWeightField.IpSpecification = value.DrugSpec
							}
							formulaExpressionList := make([]string, 0)
							formulaValueList := make([]string, 0)
							if value.Formulas != nil && len(value.Formulas) > 0 {
								for _, formula := range value.Formulas {
									formulaExpressionList = append(formulaExpressionList, formula.Expression)
									formulaValueList = append(formulaValueList, strconv.Itoa(formula.Value))
								}
							}
							formulaWeightField.WeightRange = formulaExpressionList
							formulaWeightField.DispensationQuantity = formulaValueList
							if value.IsOpen {
								formulaWeightField.IsOpenIp = "√"
							} else {
								formulaWeightField.IsOpenIp = "×"
							}
							if value.CalculationInfo.KeepDecimal {
								if value.CalculationInfo.Precision != nil {
									formulaWeightField.KeepDecimalPlaces = strconv.Itoa(int(*value.CalculationInfo.Precision))
								}
							}

							if value.CalculationInfo.ComparisonSwitch {
								if value.CalculationInfo.ComparisonType != nil {
									if *value.CalculationInfo.ComparisonType == 1 {
										formulaWeightField.ComparedWith = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation"]
									} else if *value.CalculationInfo.ComparisonType == 2 {
										formulaWeightField.ComparedWith = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual"]
									} else if *value.CalculationInfo.ComparisonType == 3 {
										formulaWeightField.ComparedWith = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random"]
									}
								}
								change := ""
								if value.CalculationInfo.ComparisonSymbols == 0 {
									change += ">"
								} else if value.CalculationInfo.ComparisonSymbols == 1 {
									change += ">="
								} else if value.CalculationInfo.ComparisonSymbols == 2 {
									change += "<="
								} else if value.CalculationInfo.ComparisonSymbols == 3 {
									change += "<"
								}
								if value.CalculationInfo.ComparisonRatio != nil {
									change = change + strconv.Itoa(int(*value.CalculationInfo.ComparisonRatio)) + "%"
								}
								formulaWeightField.Change = change
								if value.CalculationInfo.CurrentComparisonType != nil {
									if *value.CalculationInfo.CurrentComparisonType == 1 {
										formulaWeightField.Calculation = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation"]
									} else if *value.CalculationInfo.CurrentComparisonType == 2 {
										formulaWeightField.Calculation = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual"]
									} else if *value.CalculationInfo.CurrentComparisonType == 3 {
										formulaWeightField.Calculation = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random"]
									}
								}
							}

							formulaWeightFieldList = append(formulaWeightFieldList, formulaWeightField)
						}
					}

				} else if configure.CalculationType == 3 {
					//简易体表面积BSA
					if configure.Values != nil && len(configure.Values) > 0 {
						for _, value := range configure.Values {
							var formulaSimpleBSAField models.FormulaSimpleBSAField
							formulaSimpleBSAField.Group = group
							formulaSimpleBSAField.VisitName = strings.Join(visitNameList, ",")
							if isBlindedRole && isBlindDrugMap[value.DrugName] {
								formulaSimpleBSAField.IpName = tools.BlindData
								formulaSimpleBSAField.IpSpecification = tools.BlindData

							} else {
								name := value.DrugName
								if value.IsOther {
									name = value.DrugName
									formulaSimpleBSAField.IsOtherDrug = true
								}
								formulaSimpleBSAField.IpName = name
								formulaSimpleBSAField.IpSpecification = value.DrugSpec

							}
							formulaExpressionList := make([]string, 0)
							if value.Formulas != nil && len(value.Formulas) > 0 {
								for _, formula := range value.Formulas {
									formulaExpressionList = append(formulaExpressionList, formula.Expression)
								}
							}
							unit := ""
							if value.CalculationInfo.Specifications.Value != nil {
								unit += strconv.FormatFloat(*value.CalculationInfo.Specifications.Value, 'f', -1, 64)
							}
							if value.CalculationInfo.Specifications.Unit != nil {
								unit += *value.CalculationInfo.Specifications.Unit
							}
							formulaSimpleBSAField.UnitCapacity = unit
							if value.CalculationInfo.UnitCalculationStandard != nil {
								formulaSimpleBSAField.UnitCalculationStandard = strconv.Itoa(int(*value.CalculationInfo.UnitCalculationStandard))
							}
							if value.IsOpen {
								formulaSimpleBSAField.IsOpenIp = "√"
							} else {
								formulaSimpleBSAField.IsOpenIp = "×"
							}

							if value.CalculationInfo.ComparisonSwitch {
								if value.CalculationInfo.ComparisonType != nil {
									if *value.CalculationInfo.ComparisonType == 1 {
										formulaSimpleBSAField.ComparedWith = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation"]
									} else if *value.CalculationInfo.ComparisonType == 2 {
										formulaSimpleBSAField.ComparedWith = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual"]
									} else if *value.CalculationInfo.ComparisonType == 3 {
										formulaSimpleBSAField.ComparedWith = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random"]
									}
								}
								change := ""
								if value.CalculationInfo.ComparisonSymbols == 0 {
									change += ">"
								} else if value.CalculationInfo.ComparisonSymbols == 1 {
									change += ">="
								} else if value.CalculationInfo.ComparisonSymbols == 2 {
									change += "<="
								} else if value.CalculationInfo.ComparisonSymbols == 3 {
									change += "<"
								}
								if value.CalculationInfo.ComparisonRatio != nil {
									change = change + strconv.Itoa(int(*value.CalculationInfo.ComparisonRatio)) + "%"
								}
								formulaSimpleBSAField.Change = change
								if value.CalculationInfo.CurrentComparisonType != nil {
									if *value.CalculationInfo.CurrentComparisonType == 1 {
										formulaSimpleBSAField.Calculation = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation"]
									} else if *value.CalculationInfo.CurrentComparisonType == 2 {
										formulaSimpleBSAField.Calculation = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual"]
									} else if *value.CalculationInfo.CurrentComparisonType == 3 {
										formulaSimpleBSAField.Calculation = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random"]
									}
								}
							}

							formulaSimpleBSAFieldList = append(formulaSimpleBSAFieldList, formulaSimpleBSAField)
						}
					}

				} else if configure.CalculationType == 4 {
					//其他体表面积(按自定义公式)
					formulaOtherBSAInfo.TakeCare = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.takeCare1"] +
						configure.CustomerCalculation + ";" +
						transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.takeCare2"]
					if configure.Values != nil && len(configure.Values) > 0 {
						for _, value := range configure.Values {
							var formulaOtherBSAField models.FormulaOtherBSAField
							formulaOtherBSAField.Group = group
							formulaOtherBSAField.VisitName = strings.Join(visitNameList, ",")
							if isBlindedRole && isBlindDrugMap[value.DrugName] {
								formulaOtherBSAField.IpName = tools.BlindData
								formulaOtherBSAField.IpSpecification = tools.BlindData

							} else {
								name := value.DrugName
								if value.IsOther {
									name = value.DrugName
									formulaOtherBSAField.IsOtherDrug = true
								}
								formulaOtherBSAField.IpName = name
								formulaOtherBSAField.IpSpecification = value.DrugSpec

							}
							formulaExpressionList := make([]string, 0)
							if value.Formulas != nil && len(value.Formulas) > 0 {
								for _, formula := range value.Formulas {
									formulaExpressionList = append(formulaExpressionList, formula.Expression)
								}
							}
							unit := ""
							if value.CalculationInfo.Specifications.Value != nil {
								// 将 float64 转换为 string，保留所有小数位，不补零
								unit += strconv.FormatFloat(*value.CalculationInfo.Specifications.Value, 'f', -1, 64)
							}
							if value.CalculationInfo.Specifications.Unit != nil {
								unit += *value.CalculationInfo.Specifications.Unit
							}
							formulaOtherBSAField.UnitCapacity = unit
							if value.CalculationInfo.UnitCalculationStandard != nil {
								formulaOtherBSAField.UnitCalculationStandard = strconv.Itoa(int(*value.CalculationInfo.UnitCalculationStandard))
							}
							if value.IsOpen {
								formulaOtherBSAField.IsOpenIp = "√"
							} else {
								formulaOtherBSAField.IsOpenIp = "×"
							}

							if value.CalculationInfo.ComparisonSwitch {
								if value.CalculationInfo.ComparisonType != nil {
									if *value.CalculationInfo.ComparisonType == 1 {
										formulaOtherBSAField.ComparedWith = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation"]
									} else if *value.CalculationInfo.ComparisonType == 2 {
										formulaOtherBSAField.ComparedWith = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual"]
									} else if *value.CalculationInfo.ComparisonType == 3 {
										formulaOtherBSAField.ComparedWith = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random"]
									}
								}
								change := ""
								if value.CalculationInfo.ComparisonSymbols == 0 {
									change += ">"
								} else if value.CalculationInfo.ComparisonSymbols == 1 {
									change += ">="
								} else if value.CalculationInfo.ComparisonSymbols == 2 {
									change += "<="
								} else if value.CalculationInfo.ComparisonSymbols == 3 {
									change += "<"
								}
								if value.CalculationInfo.ComparisonRatio != nil {
									change = change + strconv.Itoa(int(*value.CalculationInfo.ComparisonRatio)) + "%"
								}
								formulaOtherBSAField.Change = change
								if value.CalculationInfo.CurrentComparisonType != nil {
									if *value.CalculationInfo.CurrentComparisonType == 1 {
										formulaOtherBSAField.Calculation = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation"]
									} else if *value.CalculationInfo.CurrentComparisonType == 2 {
										formulaOtherBSAField.Calculation = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual"]
									} else if *value.CalculationInfo.CurrentComparisonType == 3 {
										formulaOtherBSAField.Calculation = transMap["configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random"]
									}
								}
							}

							formulaOtherBSAFieldList = append(formulaOtherBSAFieldList, formulaOtherBSAField)
						}
					}

				}

			}
		}
	}

	dtpIpInfo.DTPIpFieldList = dtpIpFieldList

	labelOpenInfo.LabelOpenFieldList = labelOpenFieldList

	formulaAgeInfo.FormulaAgeFieldList = formulaAgeFieldList
	formulaConfigInfo.FormulaAge = formulaAgeInfo

	formulaWeightInfo.FormulaWeightFieldList = formulaWeightFieldList
	formulaConfigInfo.FormulaWeight = formulaWeightInfo

	formulaSimpleBSAInfo.FormulaSimpleBSAFieldList = formulaSimpleBSAFieldList
	formulaConfigInfo.FormulaSimpleBSA = formulaSimpleBSAInfo

	formulaOtherBSAInfo.FormulaOtherBSAFieldList = formulaOtherBSAFieldList
	formulaConfigInfo.FormulaOtherBSA = formulaOtherBSAInfo

	treatmentDesignInfo.FormulaConfig = formulaConfigInfo

	if len(randomConfigureInfo.Level) > 0 {
		//随机
		if len(formConfigureInfo.Level) > 0 {
			//表单
			if attribute.AttributeInfo.Dispensing {
				//发药
				if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
					(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) ||
					(labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
					(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
					(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
					(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
					(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
					ipManagementInfo.Level = ".4"

					if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
						(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) {
						visitManagementInfo.Level = ".1"
						if (dtpIpFieldList != nil && len(dtpIpFieldList) > 0) ||
							(labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
							(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
							(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
							(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
							(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
							treatmentDesignInfo.Level = ".2"

							if dtpIpFieldList != nil && len(dtpIpFieldList) > 0 {
								dtpIpInfo.Level = "1."
								if labelOpenFieldList != nil && len(labelOpenFieldList) > 0 {
									labelOpenInfo.Level = "2."
									if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
										(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
										(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
										(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
										formulaConfigInfo.Level = "3."
									}
								} else {
									labelOpenInfo.Level = ""
									if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
										(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
										(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
										(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
										formulaConfigInfo.Level = "2."
									}
								}
							} else {
								dtpIpInfo.Level = ""
								if labelOpenFieldList != nil && len(labelOpenFieldList) > 0 {
									labelOpenInfo.Level = "1."
									if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
										(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
										(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
										(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
										formulaConfigInfo.Level = "2."
									}
								} else {
									labelOpenInfo.Level = ""
									if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
										(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
										(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
										(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
										formulaConfigInfo.Level = "1."
									}
								}
							}

						} else {
							treatmentDesignInfo.Level = ""
						}
					} else {
						visitManagementInfo.Level = ""
						treatmentDesignInfo.Level = ""
					}
				} else {
					ipManagementInfo.Level = ""
				}

			} else {
				//不发药
				ipManagementInfo.Level = ""
			}

		} else {
			//无表单
			if attribute.AttributeInfo.Dispensing {
				//发药
				if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
					(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) ||
					(labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
					(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
					(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
					(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
					(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
					ipManagementInfo.Level = ".3"

					if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
						(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) {
						visitManagementInfo.Level = ".1"
						if (labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
							(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
							(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
							(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
							(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
							treatmentDesignInfo.Level = ".2"

							if labelOpenFieldList != nil && len(labelOpenFieldList) > 0 {
								labelOpenInfo.Level = "1."
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "2."
								}
							} else {
								labelOpenInfo.Level = ""
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "1."
								}
							}
						}
					} else {
						visitManagementInfo.Level = ""
						treatmentDesignInfo.Level = ""
					}
				} else {
					ipManagementInfo.Level = ""
				}
			} else {
				//不发药
				ipManagementInfo.Level = ""
			}
		}

	} else {
		//不随机
		if len(formConfigureInfo.Level) > 0 {
			//表单
			if attribute.AttributeInfo.Dispensing {
				//发药
				if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
					(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) ||
					(labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
					(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
					(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
					(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
					(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
					ipManagementInfo.Level = ".3"

					if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
						(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) {
						visitManagementInfo.Level = ".1"
						if (labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
							(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
							(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
							(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
							(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
							treatmentDesignInfo.Level = ".2"

							if labelOpenFieldList != nil && len(labelOpenFieldList) > 0 {
								labelOpenInfo.Level = "1."
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "2."
								}
							} else {
								labelOpenInfo.Level = ""
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "1."
								}
							}
						}
					} else {
						visitManagementInfo.Level = ""
						treatmentDesignInfo.Level = ""
					}
				} else {
					ipManagementInfo.Level = ""
				}

			} else {
				//不发药
				ipManagementInfo.Level = ""
			}

		} else {
			if attribute.AttributeInfo.Dispensing {
				//发药
				if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
					(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) ||
					(labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
					(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
					(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
					(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
					(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
					ipManagementInfo.Level = ".2"

					if len(visitManagementInfo.CycleVersion) > 0 || len(visitManagementInfo.VisitOffsetType) > 0 ||
						(visitManagementInfo.VisitList != nil && len(visitManagementInfo.VisitList) > 0) {
						visitManagementInfo.Level = ".1"
						if (labelOpenFieldList != nil && len(labelOpenFieldList) > 0) ||
							(formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
							(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
							(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
							(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
							treatmentDesignInfo.Level = ".2"

							if labelOpenFieldList != nil && len(labelOpenFieldList) > 0 {
								labelOpenInfo.Level = "1."
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "2."
								}
							} else {
								labelOpenInfo.Level = ""
								if (formulaAgeFieldList != nil && len(formulaAgeFieldList) > 0) ||
									(formulaWeightFieldList != nil && len(formulaWeightFieldList) > 0) ||
									(formulaSimpleBSAFieldList != nil && len(formulaSimpleBSAFieldList) > 0) ||
									(formulaOtherBSAFieldList != nil && len(formulaOtherBSAFieldList) > 0) {
									formulaConfigInfo.Level = "1."
								}
							}

						}
					} else {
						visitManagementInfo.Level = ""
						treatmentDesignInfo.Level = ""
					}
				} else {
					ipManagementInfo.Level = ""
				}
			} else {
				//不发药
				ipManagementInfo.Level = ""
			}
		}

	}

	treatmentDesignInfo.DTPIp = dtpIpInfo
	treatmentDesignInfo.FormulaConfig = formulaConfigInfo
	visitManagementInfo.VisitList = visitDetailsList
	ipManagementInfo.VisitManagement = visitManagementInfo
	treatmentDesignInfo.LabelOpen = labelOpenInfo
	ipManagementInfo.TreatmentDesign = treatmentDesignInfo
	configureDetail.IpManagement = ipManagementInfo

	return configureDetail, nil

}
func translateTypeString(ctx *gin.Context, str string, transMap map[string]string) string {
	var result = ""
	if len(str) > 0 {
		if str == "input" {
			//输入框
			result = transMap["form.control.type.input"]
		} else if str == "inputNumber" {
			//数字输入框
			result = transMap["form.control.type.inputNumber"]
		} else if str == "textArea" {
			//多行文本框
			result = transMap["form.control.type.textArea"]
		} else if str == "checkbox" {
			//复选框
			result = transMap["form.control.type.checkbox"]
		} else if str == "radio" {
			//单选框
			result = transMap["form.control.type.radio"]
		} else if str == "switch" {
			//开关
			result = transMap["form.control.type.switch"]
		} else if str == "datePicker" {
			//日期选择框
			result = transMap["form.control.type.date"]
		} else if str == "timePicker" {
			//时间选择框
			result = transMap["form.control.type.dateTime"]
		} else if str == "select" {
			//下拉框
			result = transMap["form.control.type.select"]
		} else if str == "characterLength" {
			//字符长度
			result = transMap["form.control.type.format.characterLength"]
		} else if str == "numberLength" {
			//数字长度
			result = transMap["form.control.type.format.inputNumber.numberLength"]
		} else if str == "decimalLength" {
			//小数（整数+小数点+小数）
			result = transMap["form.control.type.format.inputNumber.decimalLength"]
		} else if str == "checkbox" {
			//多选框
			result = transMap["form.control.type.format.checkbox"]
		} else if slice.Contain([]string{"YYYY", "YYYY-MM", "MM-YYYY", "MMM-YYYY", "YYYY-MM-DD", "DD-MM-YYYY",
			"MM-DD-YYYY", "DD-MMM-YYYY", "MMM-DD-YYYY"}, str) {
			//日期选择框
			result = str
		} else if slice.Contain([]string{"HH:mm:ss", "HH:mm", "YYYY-MM-DD HH:mm", "YYYY-MM-DD HH:mm:ss",
			"DD-MM-YYYY HH:mm", "DD-MM-YYYY HH:mm:ss", "MM-DD-YYYY HH:mm", "MM-DD-YYYY HH:mm:ss", "DD-MMM-YYYY HH:mm",
			"DD-MMM-YYYY HH:mm:ss", "MMM-DD-YYYY HH:mm", "MMM-DD-YYYY HH:mm:ss"}, str) {
			//时间选择框
			result = str
		}
	}
	return result
}

func configureReportTransMap(ctx context.Context) map[string]string {
	transKeys := []interface{}{
		// 导出配置报告相关
		locales.TrStash(ctx, "export.random_config.export"),
		locales.TrStash(ctx, "export.random_config.createDate"),
		locales.TrStash(ctx, "export.random_config.createBy"),
		locales.TrStash(ctx, "export.random_config.directory"),
		locales.TrStash(ctx, "export.random_config.summary"),
		locales.TrStash(ctx, "export.random_config.report"),

		// 基本信息相关
		locales.TrStash(ctx, "configureReport.basicInformation"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectTimeZone"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectType"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectOrderCheck"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectOrderConfirmation"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectDeIsolationApproval"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectOrderApprovalControl"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectNotice"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectConnectEdc"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectPushMode"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectSynchronizationMode"),

		// 通用信息相关
		locales.TrStash(ctx, "configureReport.generalSituation"),
		locales.TrStash(ctx, "configureReport.allOfThem"),
		locales.TrStash(ctx, "configureReport.name"),
		locales.TrStash(ctx, "configureReport.cohort"),
		locales.TrStash(ctx, "configureReport.stage"),

		// 项目类型相关
		locales.TrStash(ctx, "configureReport.basicInformation.projectType.basicStudy"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectType.cohortStudy"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectType.reRandomizationStudy"),

		// 中心研究产品库存核查相关
		locales.TrStash(ctx, "configureReport.basicInformation.projectOrderCheck.timing"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectOrderCheck.realTime"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectOrderCheck.notApplicable"),

		// 中心回收订单确认相关
		locales.TrStash(ctx, "configureReport.basicInformation.projectOrderConfirmation.open"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectOrderConfirmation.close"),

		// 解隔离审批相关
		locales.TrStash(ctx, "configureReport.basicInformation.projectDeIsolationApproval.open"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectDeIsolationApproval.close"),

		// 揭盲控制相关
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl.unblinding"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl.unblinding.sms"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl.unblinding.process"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl.unblinding.code"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl.pvUnblinding"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl.pvUnblinding.sms"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl.pvUnblinding.process"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl.IpUnblinding"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl.IpUnblinding.sms"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl.IpUnblinding.process"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectUnblindingControl.false"),

		// 研究中心订单申请相关
		locales.TrStash(ctx, "configureReport.basicInformation.projectOrderApprovalControl.false"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectOrderApprovalControl.sms"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectOrderApprovalControl.process"),

		// 项目通知相关
		locales.TrStash(ctx, "configureReport.basicInformation.projectNotice.open"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectNotice.close"),

		// 对接EDC相关
		locales.TrStash(ctx, "configureReport.basicInformation.projectPushMode.real"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectSynchronizationMode.screen"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectSynchronizationMode.random"),
		locales.TrStash(ctx, "configureReport.basicInformation.projectPushMode.active"),

		// 项目详情相关
		locales.TrStash(ctx, "configureReport.projectDetails"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberPrefix"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberDigit"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomSequenceNumberStart"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize.random"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID.show"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomizationID.notShow"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber.show"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.displayRandomSequenceNumber.notShow"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomize.non-randomized"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign.dispensing.yes"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.treatmentDesign.dispensing.no"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule1"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule2"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3.least"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.randomizationSupplyCheck.randomControlRule3.groups"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.ip"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.visitFlow"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.dtpRule.notApplicable"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign.blind"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.blindDesign.open"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess.open"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.systemConfiguration.subjectScreeningProcess.close"),

		// 受试者号规则相关
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectIDPrefix"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.replacementTextForSubjectID"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.replacementTextEnForSubjectID"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectIDDigit"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.takeCare"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule1"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule2"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectNumberInputRule.rule3"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix.have"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectPrefix.nothing"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement.open"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.subjectIDRules.subjectReplacement.close"),

		// 其他规则相关
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.deactivate"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.quarantine"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.packing"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects.true"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.stopUnblindedSubjects.false"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule.true"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.quarantinedIPCountingRule.false"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.true"),
		locales.TrStash(ctx, "configureReport.projectDetails.attributeConfigure.otherRules.transportAccordingPackaging.false"),

		// 随机配置相关
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.randomType"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.group"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.regionFactor"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factorOption"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.fieldNumber"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.fieldName"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.variable"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.controlType"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.option"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.randomType.region"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.randomType.min"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.country"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.site"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.regionFactor.region"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.fieldNumber"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.customFormula"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.retainDecimals"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.layeredName"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.layeredOption"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType.age"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.formulaType.bmi"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.valid"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.stratificationCalculation.status.invalid"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.valid"),
		locales.TrStash(ctx, "configureReport.projectDetails.randomConfigure.randomDesign.factor.status.invalid"),

		// 表单配置相关
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.fieldName"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.isEditable"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.required"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.variableId"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.controlType"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.option"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.formatType"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.variableFormat"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.variableRange"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.status"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.isEditable.true"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.isEditable.false"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.required.true"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.required.false"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.currentTime"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.status.valid"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.subjectRegistration.status.invalid"),

		// 公式计算相关
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.fieldName"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.required"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.variableId"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.controlType"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.formatType"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.variableFormat"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.variableRange"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.status"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.required.true"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.required.false"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.status.valid"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.customFormula.status.invalid"),

		// 剂量调整相关
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.fieldName"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.required"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.variableId"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.controlType"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.option"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.status"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.required.true"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.required.false"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.status.valid"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.doseAdjustment.status.invalid"),

		// 分层计算相关
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.fieldName"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.isEditable"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.required"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.variableId"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.controlType"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.option"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.formatType"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.variableFormat"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.variableRange"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.status"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.isEditable.true"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.isEditable.false"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.required.true"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.required.false"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.currentTime"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.status.valid"),
		locales.TrStash(ctx, "configureReport.projectDetails.formConfigure.layeredCalculation.status.invalid"),

		// IP管理相关
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.cycleVersion"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.visitOffsetType"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.visitNumber"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.visitName"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.group"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.intervalDuration"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.window"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.isDispense"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.isRandomize"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.isDTP"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.isSubjectReplace"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.visitManagement.isDoseAdjustment"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.ip"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.dtpMode"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.takeCare"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.site"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.siteSubject"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.dtpIp.send.depotSubject"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.group"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.visitName"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.IpName"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.dispensationQuantity"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.customFormula"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.combinedDispensation"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.ipSpecification"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.specification"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.automaticAssignment"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.calculationUnit"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.labelOpen.takeCare"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.group"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.visitName"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.IpName"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.ipSpecification"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.ageRange"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.dispensationQuantity"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.isOpenIp"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.keepDecimalPlaces"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaAge.takeCare"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.group"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.visitName"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.IpName"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.ipSpecification"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.weightRange"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.dispensationQuantity"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.isOpenIp"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.keepDecimalPlaces"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.weightComparisonCalculation"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.comparedWith"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.change"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.calculation"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaWeight.takeCare"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.group"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.visitName"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.IpName"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.ipSpecification"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.unitCapacity"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.unitCalculationStandard"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.isOpenIp"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.weightComparisonCalculation"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.comparedWith"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.change"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.calculation"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaSimpleBSA.takeCare"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.group"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.visitName"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.IpName"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.ipSpecification"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.unitCapacity"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.unitCalculationStandard"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.isOpenIp"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.weightComparisonCalculation"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.comparedWith"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.change"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.calculation"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.takeCare1"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.formulaOtherBSA.takeCare2"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.calculation"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.actual"),
		locales.TrStash(ctx, "configureReport.projectDetails.ipManagement.treatmentDesign.formulaConfig.random"),

		// 表单控件类型相关
		locales.TrStash(ctx, "form.control.type.input"),
		locales.TrStash(ctx, "form.control.type.inputNumber"),
		locales.TrStash(ctx, "form.control.type.textArea"),
		locales.TrStash(ctx, "form.control.type.checkbox"),
		locales.TrStash(ctx, "form.control.type.radio"),
		locales.TrStash(ctx, "form.control.type.switch"),
		locales.TrStash(ctx, "form.control.type.date"),
		locales.TrStash(ctx, "form.control.type.dateTime"),
		locales.TrStash(ctx, "form.control.type.select"),
		locales.TrStash(ctx, "form.control.type.format.characterLength"),
		locales.TrStash(ctx, "form.control.type.format.inputNumber.numberLength"),
		locales.TrStash(ctx, "form.control.type.format.inputNumber.decimalLength"),
		locales.TrStash(ctx, "form.control.type.format.checkbox"),
		locales.TrStash(ctx, "form.control.type.options.one"),
		locales.TrStash(ctx, "form.control.type.options.two"),
		locales.TrStash(ctx, "form.control.type.options.three"),
	}
	return multilingual.TrBatchMap(ctx, transKeys)
}
