// protoc --go_out=. --go-grpc_out=. protos/*.proto
// protoc-go-inject-tag -input ./pb/*.pb.go    // 这条命令在上面那条之后执行

syntax = "proto3";

option go_package = "./pb";

import "google/protobuf/empty.proto";

service Hospital {
  // 查询所有医院信息
  rpc ListAllHospitals(google.protobuf.Empty) returns (HospitalList);
  // 分页查找
  rpc ListHospitals(ListHospitalsRequest) returns (HospitalPage);
  // 模糊搜索医院的名称信息
  rpc SearchHospitalNames(SearchHospitalNamesRequest) returns (SearchHospitalNamesResult);
  // 模糊搜索医生信息
  rpc SearchDoctors(SearchDoctorsRequest) returns (SearchDoctorsResult);
  // 根据多个医院名称精确搜索
  rpc MultiSearchHospital(ListHospitalsRequest) returns(HospitalPage);
}

message  HospitalList {
  repeated HospitalData data = 1;  // @gotags: json:"data"
}


message  HospitalPage {
  int64 count = 1;  // @gotags: json:"count"
  repeated HospitalData items = 2;  // @gotags: json:"items"
}

message ListHospitalsRequest {
  int64 start = 1;
  int64 limit = 2;
  string hospitalName = 3;
  bool withDisabled = 4;
}

message SearchHospitalNamesRequest {
  string hospitalName = 1;
}

message SearchHospitalNamesResult {
  repeated HospitalNameData result = 1;  // @gotags: json:"result"
}

message HospitalNameData {
  repeated string hospitalNames = 1;  // @gotags: json:"hospitalNames"
  string hospitalNameEN = 2;  // @gotags: json:"hospitalNameEN"
}

message SearchDoctorsRequest {
  string hospitalName = 1;
  string doctorName = 2;
}

message SearchDoctorsResult {
  repeated DoctorData result = 1;  // @gotags: json:"result"
}

message HospitalData {
  string id = 1;  // @gotags: json:"id"
  repeated string  hospitalNames = 2;  // @gotags: json:"hospitalNames"
  string hospitalNameEN = 3;  // @gotags: json:"hospitalNameEN"
  string country = 4;  // @gotags: json:"country"
  string province = 5;  // @gotags: json:"province"
  string city = 6;  // @gotags: json:"city"
  string timezone = 7;  // @gotags: json:"timezone"
  repeated DepartmentData departments = 8;  // @gotags: json:"departments"
  repeated string countryCode = 9;  // @gotags: json:"countryCode"
  string postcode = 10;  // @gotags: json:"postcode"
  string address = 11;  // @gotags: json:"address"
  bool disabled = 12;
  bool isBeiAnSite = 13;  // @gotags: json:"isBeiAnSite"
  string type = 14;  // @gotags: json:"type"
  string tz = 15;  // @gotags: json:"tz"
}

message DepartmentData {
  string departmentName = 1;  // @gotags: json:"departmentName"
  repeated DoctorData doctors = 2;  // @gotags: json:"doctors"
  string id = 3;  // @gotags: json:"id"
}

message DoctorData {
  string doctorName = 1;  // @gotags: json:"doctorName"
  string departmentName = 2;  // 一些接口的返回值会把科室名称加上，这里就不加 @gotags 了
  string email = 3;  // @gotags: json:"email"
  string mobile = 4;  // @gotags: json:"mobile"
  string id = 5;  // @gotags: json:"id"
  string departmentId = 6;  // 一些接口的返回值会把科室id加上，这里就不加 @gotags 了
}