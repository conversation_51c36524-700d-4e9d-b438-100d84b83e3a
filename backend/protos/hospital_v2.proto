// protoc --go_out=. --go-grpc_out=. protos/*.proto
// protoc-go-inject-tag -input ./pb/*.pb.go    // 这条命令在上面那条之后执行

syntax = "proto3";

option go_package = "./pb";

service HospitalV2 {
  // 查询所有医院信息
  rpc ListAllHospitalsV2(ListAllHospitalsV2Request) returns (HospitalV2List);
  // 分页查找
  rpc ListHospitalsV2(ListHospitalsV2Request) returns (HospitalV2Page);
  // 模糊搜索医院的名称信息
  rpc SearchHospitalV2Names(SearchHospitalV2NamesRequest) returns (SearchHospitalV2NamesResult);
  //  // 模糊搜索医生信息
  rpc SearchDoctorsV2(SearchDoctorsV2Request) returns (SearchDoctorsV2Result);
}

message  HospitalV2List {
  repeated HospitalV2Data data = 1;  // @gotags: json:"data"
}


message  HospitalV2Page {
  int64 count = 1;  // @gotags: json:"count"
  repeated HospitalV2Data items = 2;  // @gotags: json:"items"
}

message ListAllHospitalsV2Request {
  bool combineNames = 1;
}

message ListHospitalsV2Request {
  int64 start = 1;
  int64 limit = 2;
  string hospitalName = 3;
  bool withDisabled = 4;
  bool combineNames = 5;
}

message SearchHospitalV2NamesRequest {
  string hospitalName = 1;
  bool combineNames = 2;
}

message SearchDoctorsV2Request {
  string hospitalName = 1;
  string doctorName = 2;
}

message SearchHospitalV2NamesResult {
  repeated HospitalV2NameData result = 1;  // @gotags: json:"result"
}

message HospitalV2NameData {
  repeated MultiLangName  hospitalNames = 1;  // @gotags: json:"hospitalNames"
  repeated MultiLangName  hospitalAliases = 2;  // @gotags: json:"hospitalAliases"
}

message SearchDoctorsV2Result {
  repeated DoctorV2Data result = 1;  // @gotags: json:"result"
}

message HospitalV2Data {
  string id = 1;  // @gotags: json:"id"
  repeated MultiLangName  hospitalNames = 2;  // @gotags: json:"hospitalNames"
  repeated MultiLangName  hospitalAliases = 3;  // @gotags: json:"hospitalAliases"
  repeated HospitalV2Address hospitalAddresses = 4;  // @gotags: json:"hospitalAddresses"
  repeated DepartmentV2Data departments = 5;  // @gotags: json:"departments"
  bool disabled = 6;  // 其他系统调用不需要显示该字段，仅对dmp 管理员使用
  bool isBeiAnSite = 7;  // @gotags: json:"isBeiAnSite"
  string type = 8;  // @gotags: json:"type"
}

message MultiLangName {
  string cn = 1;  // @gotags: json:"cn"
  string en = 2;  // @gotags: json:"en"
}

message HospitalV2Address {
  string timezone = 1;  // @gotags: json:"timezone"
  repeated string countryCode = 2;  // @gotags: json:"countryCode"
  string countryCn = 3;  // @gotags: json:"countryCn"
  string countryEn = 4;  // @gotags: json:"countryEn"
  string provinceCn = 5;  // @gotags: json:"provinceCn"
  string provinceEn = 6;  // @gotags: json:"provinceEn"
  string cityCn = 7;  // @gotags: json:"cityCn"
  string cityEn = 8;  // @gotags: json:"cityEn"
  string addressCn = 9;  // @gotags: json:"addressCn"
  string addressEn = 10;  // @gotags: json:"addressEn"
  string postcode = 11;  // @gotags: json:"postcode"
  string tz = 12;  // @gotags: json:"tz"
}

message DepartmentV2Data {
  string id = 1;  // @gotags: json:"id"
  repeated MultiLangName departmentNames = 2;  // @gotags: json:"departmentNames"
  repeated DoctorV2Data doctors = 3;  // @gotags: json:"doctors"
}

message DoctorV2Data {
  string id = 1;  // @gotags: json:"id"
  repeated MultiLangName doctorNames = 2;  // @gotags: json:"doctorNames"
  string departmentId = 3;  // 一些接口的返回值会把科室id加上，一些不需要，这里就不加 @gotags 了
  repeated MultiLangName departmentNames = 4;  // 一些接口的返回值会把科室名称加上，一些不需要，这里就不加 @gotags 了
  string doctorEmail = 5;  // @gotags: json:"doctorEmail"
  string doctorMobile = 6;  // @gotags: json:"doctorMobile"
}